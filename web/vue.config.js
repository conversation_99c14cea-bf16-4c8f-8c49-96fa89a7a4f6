const webpack = require('webpack');
const WebpackJmodulePlugin = require('@jmodule/webpack-jmodule-plugin');
const opn = require('opn');
const path = require('path');
const openInEditor = require('launch-editor-middleware');
const CompressionPlugin = require('compression-webpack-plugin');
// const fs = require('fs');

const args = process.argv;
const prodProxyTable = require('./.proxy.prod');
const devProxyTable = require('./.proxy.dev');

const isProd = !!(args || []).find(o => o.startsWith('prod'));
const proxy = isProd || args.includes('--proxy-prod') ? prodProxyTable : devProxyTable;
const devMode = process.env.NODE_ENV === 'development';
const isModulesMode = process.argv.includes('--modules'); // 这个值一直为false
const isSdk = process.argv.includes('--sdk');
const platformServer = 'http://devops.gj.local';// 这一块不要考虑,因为行云是平台底座。不可能在作为子系统了。
const host = 'local.devops.gj.local';
const { modulesEntry, targetDirectory, subDirectory } = WebpackJmodulePlugin.getModules();
const sdkDir = '/sdk/';

function resolve(dir) {
    return path.join(process.cwd(), dir);
}
const jmodulePlugin = new WebpackJmodulePlugin({
    mode: isModulesMode ? 'modules' : undefined,
    externalAlias: isModulesMode ? {
        vue: '$node_modules.vue',
        vuex: '$node_modules.vuex',
        '@/router': '$platform.router',
        '@/store': '$platform.store',
    } : {},
    customEntry: true,
    devConfig: devMode ? {
        modulesConfig: {
            jacpRoot: {
                name: '行云',
                topRooter: 'myzone',
            },
        },
        currentServer: 'http://local.jd.com:8080',
        platformServer,
        platformProxyTable: {},
        platformLocalPort: 8088,
        onAllServerReady: () => {
            opn('http://local.jd.com:8088/deployRoot/apps');
        },
    } : undefined,
});


module.exports = {
    productionSourceMap: false,
    css: isSdk ? { extract: false } : undefined,
    outputDir: !isModulesMode ? resolve(`./dist${isSdk ? sdkDir : ''}`) : resolve(`./dist/${isSdk ? sdkDir : subDirectory}`),
    filenameHashing: !isSdk,
    runtimeCompiler: true, // TODO: 明明不需要，可以改代码
    publicPath: isModulesMode && devMode ? 'http://local.jd.com:8080/' : undefined, // hmr 获取更新需要正确的域名
    devServer: {
        clientLogLevel: 'debug',
        host,
        port: isModulesMode ? 8080 : 8084,
        contentBase: path.join(__dirname, 'public'),
        contentBasePublicPath: '/static',
        proxy,
        // 用https启动
        // https: {
        //     key: fs.readFileSync('./public/key.pem'),
        //     cert: fs.readFileSync('./public/cert.pem'),
        // },
        disableHostCheck: true,
        headers: {
            'Access-Control-Allow-Origin': 'http://local.jd.com:8088',
            'Access-Control-Allow-Credentials': true,
        },
        overlay: {
            warnings: true,
            errors: true,
        },
        before(app) {
            // [「开发提效」从页面直接打开代码文件](https://mp.weixin.qq.com/s/LB7Fv_yjqVNjNTCmcDiMGg)
            app.use('/__open-in-editor', openInEditor('code'));
        },
    },
    configureWebpack: {
        plugins: [
            jmodulePlugin,
            new webpack.ProvidePlugin({
                'window.Quill': resolve('./node_modules/quill/dist/quill.min.js'),
            }),
            // 静态资源同时输出一份.gz的静态文件，需要结合nginx的配置来使用
            // nginx需要使用 ngx_http_gzip_static_module 模块，尝试使用静态压缩，如果有则返回 .gz 的预压缩文件，否则尝试动态压缩
            // nginx配置方式：https://blog.csdn.net/fxss5201/article/details/106535475
            // 目前jone需要登陆门神系统来调整nginx配置方式
            new CompressionPlugin({
                exclude: /.map$/,
            }),
        ],
        externals: isModulesMode ? [
            { '@jmodule/client': 'JModule' },
        ] : [],
    },
    pages: isModulesMode ? {
        [targetDirectory]: {
            entry: modulesEntry,
        },
    } : undefined,
    chainWebpack: (config) => {
        if (isModulesMode || isSdk) {
            config.plugins
                .delete('html')
                .delete('preload')
                .delete('prefetch');
        } else {
            config.plugin('html')
                .tap((options) => {
                    options[0].template = './index.html';
                    return options;
                });
        }
        config.optimization.splitChunks({
            maxInitialRequests: 5, // 默认3
            maxAsyncRequests: 6, // 默认5
            cacheGroups: {
                common: {
                    name: 'app', // 打包后的文件名
                    chunks: 'initial', //
                    minChunks: 2,
                    maxInitialRequests: 5,
                    minSize: 0,
                    priority: 1,
                    reuseExistingChunk: true,
                },
                vendors: {
                    name: 'chunk-vendors',
                    test: /[\\/]node_modules[\\/]/,
                    chunks: 'initial',
                    priority: 2,
                    reuseExistingChunk: true,
                    enforce: true,
                },
                'element-ui': {
                    name: 'chunk-element-ui',
                    test: /[\\/]node_modules[\\/]element-ui[\\/]/,
                    chunks: 'initial',
                    priority: 3,
                    reuseExistingChunk: true,
                },
                runtimeChunk: {
                    name: 'manifest', // runtimeChunk ，作用是将包含chunks映射关系的list单独从app.js里提取出来，因为每一个chunk的id基本都是基于内容hash出来的，所以你每次改动都会影响它，如果不把它提取出来的话，等于app.js每次都会改变，缓存就失效了
                },
            },
        });
    },
};
