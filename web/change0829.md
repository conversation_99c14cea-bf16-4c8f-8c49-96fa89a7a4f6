## 此次升级对模块基本兼容（如果有非期望方式使用X_WEBPACK_SERVER的模块。可能需要一些调整） 

## 升级 client
1. jmodule.js 增加了 --proxy-prod 参数 (为了更好地支持其它模块的开发模式联调)
2. 升级 @jmodule/client@next
3. exports.js 调整分发给模块的http的实现(获取模块地址的方式调整及cli的编译输出调整)
4. main.js 调整jRouter 的引用，移除jModulePlugin的注册
5. router.js 调整jrouter的引用
6. jrouter 的实现（由于 client 已经移除router）
7. combineModules.js 
    7.1> 移除 addRoutes 的hook，在afterInit的hook中实现 (由于 client 本身移除了模块 routes 的注册, 同时移除了addRoutes的hook)
    7.2> 配置JModule, 移除 load 函数的 applyStyle 参数
8. appModule.js 修正原先的路由跳转的bug，并更新 resource api：appendStyle => applyStyle

## 以上调整主要基于 client 的以下不兼容变动：
1. 移除了 jrouter => 复制原实现到本地，并调整其它代码相关引用
2. 移除了 JModulePlugin 以及 routes/store 的注册（与vue解藕）
3. 调整了 resource 的实现，appendStyle => applyStyle 的 api 更新
4. hook 调整：移除了 addRoutes 的hook, 用 afterInit hook 代替（afterInit 需要同时承担原有的 routes/store 的职责）
