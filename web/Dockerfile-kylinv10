# FROM nginx:latest
# COPY static dist/
# COPY dist /usr/share/nginx/html/dist
# COPY dist /usr/share/nginx/html/
# ADD  static /usr/share/nginx/html/dist/

# FROM hub.jdcloud.com/devops/base/nginx:latest
FROM hub.jdcloud.com/devops/devops-nginx222-kylinv10-amd64:sec1.3-nginx
COPY dist /usr/share/nginx/html/dist
RUN mkdir /usr/share/nginx/html/dist/static -p 
COPY static /usr/share/nginx/html/dist/static
RUN chmod -R 755 /usr/share/nginx/html/dist

