# 行云前端项目介绍
- 启动方式是基于`vue-cli`启动。
- 修改配置参考`vue.config.js`文件。
- 接口代理修改`.proxy.dev.js`文件。

## 安装

``` bash
# 安装依赖
npm i

# 本地研发环境启动
npm run dev
访问端口8084

# 线上打包
npm run build

```
### 环境准备
1. 安装 [nodejs ](https://nodejs.org/en/) （自带 npm推荐使用v6.14.8）Node推荐使用v14.13.0
2. 安装项目编译工具 [@jmodule/cli](https://jmodule.jd.com/)
3. 配置 host: local.jd.com    127.0.0.1

### 技术资料
1. 框架 [Vue](https://cn.vuejs.org/v2/guide/)
2. 路由管理 [Vue-router](https://router.vuejs.org/zh/)
3. 数据中心 [Vuex](https://vuex.vuejs.org/zh/guide/)
4. CSS预编译 [Less](http://lesscss.org/)
5. [ES6](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference)

### 项目结构


## 如果安装失败则考虑-安装内部包
```
npm i xx(某一个内部包) --@jmodule:registry=http://artifactory.jd.com/api/npm/npm/

# 安装项目编译工具
npm i @jmodule/cli -g --registry=http://artifactory.jd.com/api/npm/npm/
```
## 平台提供的可引用对象有
```
{
    $platform: {
        store,
        utils,
        router,
        http,
        event,
        models: {
            dialog: Dialog,
            app: App,
        },
    },
    $node_modules: {
        vue: Vue,
        moment,
        axios,
        asyncValidator,
    },
}

# in use
import axios from '$node_modules.axios';

```