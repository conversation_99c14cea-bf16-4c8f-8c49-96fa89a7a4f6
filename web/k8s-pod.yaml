apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: jacp-web-ingress
  annotations:
      kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - host: jacp-web.dev.jd.com
    http:
      paths:
      - backend:
          serviceName: jacp-web
          servicePort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: jacp-web
spec:
  ports:
  - port: 80
    name: http
  type: LoadBalancer
  selector:
    app: jacp-web
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jacp-web
spec:
  selector:
    matchLabels:
      app: jacp-web
  template:
    metadata:
      labels:
        app: jacp-web
    spec:
      containers:
      - name: jacp-web
        image: test-registry-kuberun.jdcloud.com/turbo/jacp-web
        ports:
          - containerPort: 80 
      nodeSelector:
        kubernetes.io/hostname: master02.jd.local
