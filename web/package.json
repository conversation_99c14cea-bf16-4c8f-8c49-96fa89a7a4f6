{"name": "jacp", "version": "1.0.0", "private": true, "description": "A Vue.js project", "author": "linguanghui3 <<EMAIL>>", "scripts": {"build": "vue-cli-service build", "dev": "vue-cli-service serve src/main.js", "report": "npx vue-cli-service build --report", "gitpush": "git add . && git commit -m '提交代码' && git push"}, "dependencies": {"@babel/polyfill": "^7.0.0", "@jacpbiz/menu-business": "0.0.27", "@jacpbiz/sidebar": "1.0.1", "@jmodule/http": "^0.0.9", "@jmodule/jeep-ui": "0.0.4-11", "@jonefe/form": "^0.2.3", "@popperjs/core": "^2.6.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "ansi-to-html": "^0.6.4", "async-validator": "1.8.0", "axios": "0.16.2", "axios-cache-adapter": "2.0.0", "better-bem": "^2.0.1", "blob-util": "^2.0.2", "bowser": "^1.7.3", "clipboard": "^2.0.4", "echarts": "^5.0.2", "element-resize-detector": "^1.2.1", "element-ui": "^2.13.2", "fscreen": "^1.0.2", "highcharts": "^6.0.2", "js-cookie": "^3.0.1", "jsonp": "^0.2.1", "lodash": "^4.17.10", "mavon-editor": "^2.9.1", "moment": "^2.18.1", "naming-style": "^1.0.1", "normalize.css": "^7.0.0", "perfect-scrollbar": "^1.5.0", "qs": "^6.9.3", "quill": "^1.3.7", "quill-image-drop-and-paste": "^1.0.4", "quill-image-resize-module": "^3.0.0", "sanitize-html": "^2.3.0", "sockjs-client": "^1.1.4", "sortablejs": "^1.13.0", "textarea-caret": "^3.1.0", "vue": "2.6.11", "vue-i18n": "^8.11.2", "vue-router": "^3.6.5", "vuedraggable": "^2.24.1", "vuex": "^2.3.1", "xss": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.14.5", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0", "@babel/plugin-proposal-private-methods": "^7.6.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@jmodule/cli": "^1.4.2", "@jmodule/client": "4.4.3-0", "@jmodule/webpack-jmodule-plugin": "^0.2.2", "@vue/babel-preset-app": "^3.6.0", "@vue/cli-plugin-babel": "^4.5.12", "@vue/cli-service": "^4.5.12", "autoprefixer": "^9.8.6", "babel-eslint": "^10.0.1", "babel-plugin-jsx-v-model": "^2.0.3", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^4.0.1", "chokidar": "^3.4.1", "compression-webpack-plugin": "^6.1.1", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^13.1.0", "eslint-import-resolver-webpack": "^0.11.1", "eslint-loader": "^2.1.2", "eslint-plugin-html": "^5.0.3", "eslint-plugin-import": "^2.17.2", "eslint-plugin-std": "0.0.8", "eslint-plugin-vue": "^5.2.2", "eventsource-polyfill": "^0.9.6", "launch-editor-middleware": "^2.2.1", "monaco-editor": "^0.22.3", "opn": "^6.0.0", "prettier": "^1.12.0", "vue-loader": "^14.2.4", "vue-template-compiler": "2.6.11", "webpack": "^4.44.1", "webpack-hot-middleware": "^2.18.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}}