# syntax = docker/dockerfile:experimental
FROM hub.ark.jd.com/liuxiaowen7/node:12-jonefe  as build
USER root
WORKDIR /app/

COPY package.json .
COPY package-lock.json .
COPY .npmrc .
COPY .proxy.prod.js .
COPY .proxy.dev.js .

RUN jonefe cache clean --force
RUN npm install --farce
RUN jonefe i --farce
RUN npm cache verify

COPY .env .
COPY vue.config.js .

COPY src src 
# COPY static static 

COPY public public 
COPY babel.config.js .
COPY .eslintignore .
COPY .eslintrc.js .

COPY index.html .

ENV NODE_OPTIONS=--max-old-space-size=6192

RUN npm run build

FROM hub.jdcloud.com/devops/base/nginx:latest.sec
COPY --from=build /app/dist /usr/share/nginx/html/dist
RUN mkdir /usr/share/nginx/html/dist/static -p 
COPY static /usr/share/nginx/html/dist/static
RUN chmod -R 755 /usr/share/nginx/html/dist
