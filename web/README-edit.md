## 修改记录

```
layout.myzone.vue

const { widgets } = this;
// 动态输出的widget
const leftNode = widgets.filter(w => w.type === 1);
const rightNode = widgets.filter(w => w.type === 2);

我的任务
{ leftNode.map(o => h(o.component)) }

动态消息
公告栏
帮助中心
{ rightNode.length ? <div class="myzone-main-columes fl1">{ rightNode.map(o => h(o.component)) }</div> : null }


// 是基本布局
demand/exports
// 是基本布局
teamspace/components/workspaceWidgets

对应  动态输出的widget


注意关键字 【$workspace】
```

## 加载 团队空间，需求管理

```
combineModules.js
 modulesBootstrap.keys().forEach((item) => {
        if (item.includes('jacpRoot')) { // 忽略模块形式启动的root
            return;
        }
        if (item.includes('teamspace')) { // 忽略团队空间
            return;
        }
        if (item.includes('demand')) { // 忽略需求管理
            return;
        }
```

## 隐藏侧边栏

```
combineModules.js

这里面区分了 是本地的还是 子系统。
判断条件是 isLocalModule；

export async function registerRemoteModules() {
    const allModules = await AppModule.getModuleList({ status: 'active' });
    const jmodules = allModules
        .map(item => item.register)
        .filter(item => !isLocalModule(item) && item.url);
    console.log({ allModules, jmodules });
    console.warn('=====');
    JModule.registerModules(jmodules)
        .then(modules => modules.forEach((module) => {
            if (module.type === 'iframe') {
                initIframeModule(module);
            } else {
                module.load('preload');
            }
        }));
}



menuService.js

export async function loadMenu(path = '') {
    let result = await http.get(`v1/setting/personal/menus/self${path || ''}?type=1`)
        .catch(() => []);
    console.log('result,==', result);
    result = result.filter(item => item.url !== 'teamspace');
    console.log('result,==', result);
    return creatRouteMenu(result, { menuScope: 'hideChildren' });
}
```