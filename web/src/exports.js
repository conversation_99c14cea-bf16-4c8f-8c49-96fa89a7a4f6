import Vue from 'vue';
import Vuex from 'vuex';
import moment from 'moment';
import Element from 'element-ui';
import asyncValidator from 'async-validator';
import { DepResolver, JModule } from '@jmodule/client';
import * as jeepUi from '@jmodule/jeep-ui';
import {
    // JHttp,
    HttpStack,
    HttpUtils,
    axios,
} from '@jmodule/http';
import router from './router';
import store from './store';
import common from './common';
import { event } from './plugins/event';
import { i18n } from '@/locale';
import { getDemandStatusColor } from '@/modules/demand/components/utils.demandStatus';
import * as domUtils from '@/plugins/utils.dom';
import * as formUtils from '@/plugins/utils.form';
import * as fileUtils from '@/plugins/utils.file';
import { moduleHttpFactory } from '@/plugins/moduleHttpFactory';
import './defineGlobal';

Vue.prototype.common = common;
// 启动测试
const platformExports = {
    $platform: {
        store,
        router,
        http: new DepResolver(moduleHttpFactory, { cacheBy: 'projectName' }), // 推荐使用，模块所有请求将遵循行云接口规范
        HttpUtils,
        HttpStack,
        event,
        i18n,
        jacpUtils: {
            ...fileUtils,
            ...domUtils,
            ...formUtils,
            getDemandStatusColor,
        },
    },
    $node_modules: {
        vue: Vue,
        vuex: Vuex,
        moment,
        elementUi: Element,
        // 原始的 axios, 每一次引入都分发一个拷贝的 axios，避免相互影响
        axios: new DepResolver(() => Object.create(axios)),
        asyncValidator,
        jeepUi,
    },
};
JModule.export(platformExports);
