import { merge } from 'axios/lib/utils';
import {
    HttpPlugins, HttpStack, DebounceHttp,
} from '@jmodule/http';


/**
 * 为不同的 module 分发不同的 JHttp 类
 * 作用是为每一个 http 引用增加 origin
 * 为兼容旧项目，对第三方模块默认启用平台的数据处理方式，可以通过 disableResponseResolver 方式禁用
 *
 * @param  {Object} config 可以获取由编译工具注入的相关参数
 * @param  {String} config.server 模块项目指定的编译服务器地址
 * @param  {String} config.moduleKey 引入这个对象的模块，对于项目内的公用组件，参数为空
 * @param  {String} config.projectName 项目名字
 * @return {ModuleHttp}
 */
export function moduleHttpFactory() {
    const { server } = JModule.import('$module.meta');
    const defautConf = {
        baseURL: '',
        withCredentials: true,
    };
    class ModuleHttp extends DebounceHttp {
        constructor(conf) {
            const mergedConfig = merge(defautConf, conf);
            const ins = super(mergedConfig);
            ins.setInterceptors('request', [(reqConfig) => {
                if (!reqConfig.url.match(/^\w+:\/\//)) { // 不带域名
                    const baseURL = (reqConfig.baseURL || '').replace(/\/$/, '');
                    const url = reqConfig.url.replace(/^\/+/, ''); // 移除头部 '/'
                    /* eslint-disable */
                  reqConfig.url = new URL(`${baseURL}/${url}`, server).href;
              }
              return reqConfig;
          }, error => Promise.reject(error)]);
          return ins;
      }
      static create(conf) {
          return new ModuleHttp(conf);
      }
  }
  return ModuleHttp;
}
export default function install (Vue) {
    HttpStack.init(Vue);
    DebounceHttp.use(HttpPlugins);
};