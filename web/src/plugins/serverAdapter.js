function fileAdapter(target, file) {
    if (file && file instanceof Object) {
        return target === 'local' ? {
            url: file.uri,
            name: file.attachmentName,
            id: file.id,
            readOnly: file.readOnly,
        } : {
            uri: file.url,
            attachmentName: file.name,
            id: file.id,
            readOnly: file.readOnly,
        };
    }
    return file;
}
function fileListAdapter(target, fileList) {
    return (fileList || []).map(file => fileAdapter(target, file));
}

export default {
    file: fileAdapter,
    fileList: fileListAdapter,
};
