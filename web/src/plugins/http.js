import JHttp from '$platform.http';
import LocalHttpPlugin from './http-plugins';
import responseResolver from './http-response-resolver';
import refreshToken from './refreshToken';

// 网关的接口都默认带一个header
const gatewayHeaders = { headers: { appId: 'arch' } };
JHttp.use(LocalHttpPlugin);
const apiServer = process.env.VUE_APP_API || '';
export const childHost = '';
const baseURL = `${apiServer}/devops-api/arch/api`;
const ajax = new JHttp({
    ...gatewayHeaders,
    baseURL,
});
console.log('-主-baseURL-', baseURL);

const childHttp = new JHttp({
    // ...gatewayHeaders,
    baseURL: '/devops-api/space-demand/api',
});

const rootHttp = new JHttp({
    baseURL: '/',
});

// 暂时没有用到
const jacpModuleHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/jacp-module/rest/',
});

// 暂时没有用到
const jacpMdcHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/module_data/api/v1',
});
const jacpLicenseHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/license/',
});

const jacpSecurityHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/arch/api/v1',
    // 'Content-Type': 'application/json',
});
const jacpToolsHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/',
});
const jacpArchHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/arch/api/v1/',
    // 'Content-Type': 'application/json',
});
const jacpPowerjobHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/scheduler-job/api/v1/',
});
// 因为要走统一网关，api-gateway这种二级域名形式放弃使用，在root/index.js文件内对baseURL进行重置
const apiGateway = new JHttp({
    // baseURL: '/api-gateway/',
    baseURL: '/devops-api',
    // 'Content-Type': 'application/json',
});
const uusGetWay = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/uaos/api/v1/',
});
// 应该是没有用到
const jacpJdrHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/jacp-jdr/',
    // 'Content-Type': 'application/json',
});
// 代码库相关， 在团队空间内，主框架没有调用
const devopsHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-compo/',
    // 'Content-Type': 'application/json',
});
const demandHttp = new JHttp({
    // ...gatewayHeaders,
    baseURL: '/devops-api/demand/api',
});
const spaceHttp = new JHttp({
    // ...gatewayHeaders,
    baseURL: '/devops-api/space/api',
});
// 没有用到
const parentHttp = new JHttp({
    ...gatewayHeaders,
    // eslint-disable-next-line no-restricted-globals
    baseURL: `${location.origin}/arch/api`,
    // 'Content-Type': 'application/json',
});
const uaosHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/uaos/api/',
});
// 有个pmp的接口需要单独处理
const pmpHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api',
    // 'Content-Type': 'application/json',
});

// security调用rest的接口单独处理
const secrityRestHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/devops-api/arch/api',
    // 'Content-Type': 'application/json',
});

// 用于直接调用主框架的接口 ，特殊处理
const archHttp = new JHttp({
    ...gatewayHeaders,
    baseURL: '/',
    // 'Content-Type': 'application/json',
});

// 请求头拦截
const requestHandler = (request) => {
    const {
        method, headers = {}, params = {}, data = {}, url,
    } = request;
    console.log('request', request);
    console.log('headers', headers);
    console.log('params', params);
    console.log('data', data);
    // 判断用户信息是否是最新的
    if (refreshToken.compareUser()) {
        refreshToken.checkUser();
    }
    // 判断是否包含完整的登录认证信息
    if (!refreshToken.checkAuthorization()) {
        // 跳转登录页
        refreshToken.redirectToLoginPage();
    }
    // 是否需要重新刷新认证
    refreshToken.refreshAuthorization();
    headers.Authorization = localStorage.getItem('Authorization');
    if (headers.appId) {
        if (method === 'get') {
            if (params.appCode) {
                console.error('当前请求路径存在appCode, url: ' + url);
            } else {
                params.appCode = headers.appId;
            }
        }
        if (method === 'post') {
            if (data.appCode) {
                console.error('当前请求体data存在appCode, url: ' + url);
            } else {
                data.appCode = headers.appId;
            }
        }
        if (method === 'delete') {
            if (params.appCode) {
                console.error('当前请求体data存在appCode, url: ' + url);
            } else {
                params.appCode = headers.appId;
            }
        }
        if (method === 'put') {
            if (data.appCode) {
                console.error('当前请求体data存在appCode, url: ' + url);
            } else {
                data.appCode = headers.appId;
            }
        }
        if (method === 'patch') {
            if (data.appCode) {
                console.error('当前请求体data存在appCode, url: ' + url);
            } else {
                data.appCode = headers.appId;
            }
        }
        delete headers.appId;
    }
    console.warn('-----');
    return {
        ...request,
        headers,
        data,
        params,
    };
};
[
    ajax, jacpSecurityHttp, jacpToolsHttp, jacpLicenseHttp, jacpJdrHttp,
    apiGateway, devopsHttp, childHttp, uaosHttp, demandHttp, spaceHttp, pmpHttp, secrityRestHttp,
    archHttp, jacpPowerjobHttp, jacpArchHttp,
].forEach((ins) => {
    ins.setInterceptors('request', [
        requestHandler,
    ]);
});
[
    ajax, jacpSecurityHttp, jacpToolsHttp, jacpLicenseHttp, jacpJdrHttp,
    apiGateway, devopsHttp, childHttp, uaosHttp, demandHttp, spaceHttp, pmpHttp, secrityRestHttp,
    archHttp,
].forEach((ins) => {
    ins.setInterceptors('response', [
        responseResolver.responseHandle,
        responseResolver.responseErrorHandle,
    ]);
});


export {
    JHttp,
    rootHttp,
    jacpModuleHttp,
    jacpSecurityHttp,
    jacpToolsHttp,
    jacpLicenseHttp,
    jacpJdrHttp,
    jacpMdcHttp,
    apiGateway,
    devopsHttp,
    parentHttp,
    childHttp,
    jacpArchHttp,
    jacpPowerjobHttp,
    uaosHttp,
    demandHttp,
    spaceHttp,
    pmpHttp,
    uusGetWay,
    secrityRestHttp,
    archHttp,
};
export default ajax;
