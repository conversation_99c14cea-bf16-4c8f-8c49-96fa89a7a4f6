import router from '$platform.router';
import { getSpaceId } from '@/plugins/utils';

function appendAxiosRequestData(params = {}) {
    return {
        requestHook(config) {
            if (['post', 'put', 'patch', 'delete'].includes(config.method)) {
                return {
                    data: Object.assign(params, config.data || {}),
                };
            }
            return {
                params: Object.assign(params, config.params || {}),
            };
        },
    };
}
function spaceIdPlugin(JHttp) {
    JHttp.plugin('useSpaceId', (spaceData = {}) => {
        const key = spaceData.spaceKey || router.currentRoute.params.spaceKey;
        const id = spaceData.spaceId || getSpaceId(key);
        return appendAxiosRequestData({ spaceId: id });
    });
}

/* 带分页的请求 */
function pagerPlugin(JHttp) {
    JHttp.plugin('useJacpPager', (pager = {}) => ({
        ...appendAxiosRequestData({ ...pager }),
        responseHook(response = {}) {
            const { data, code } = response.data || {};
            if (code === 200 && data && data.total) {
                // eslint-disable-next-line no-param-reassign
                pager.total = data.total;
            }
            return response;
        },
    }));
}

export default {
    install(JHttp) {
        spaceIdPlugin(JHttp);
        pagerPlugin(JHttp);
    },
};
