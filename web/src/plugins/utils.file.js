import { imageTodownload } from '@/plugins/processPicture';

export function getBase64(url) {
    return new Promise((resolve) => {
        // 通过构造函数来创建的 img 实例，在赋予 src 值后就会立刻下载图片，相比 createElement() 创建 <img> 省去了 append()，也就避免了文档冗余和污染
        const Img = new Image();
        let dataURL = '';
        // 必须放在设置src
        Img.setAttribute('crossOrigin', 'anonymous');
        Img.src = url;
        Img.onload = () => { // 要先确保图片完整获取到，这是个异步事件
            const canvas = document.createElement('canvas'); // 创建canvas元素
            const { width } = Img; // 确保canvas的尺寸和图片一样
            const { height } = Img;
            canvas.width = width;
            canvas.height = height;
            canvas.getContext('2d').drawImage(Img, 0, 0, width, height); // 将图片绘制到canvas中
            dataURL = canvas.toDataURL('image/png'); // 转换图片为dataURL
            resolve(dataURL);
        };
    });
}
export async function downloadFile({
    filename, url, name,
}) {
    imageTodownload(url, filename, name);
    // const a = document.createElement('a');
    // if (content) {
    //     const file = new Blob([content], { type: 'text/plain' });
    //     a.href = URL.createObjectURL(file);
    // } else if (url.startsWith('http')) {
    //     // a.href = url;
    //     a.href = new URL(url).pathname;
    // } else {
    //     a.href = url;
    // }
    // a.href = imageAddAuthorization(a.href);
    // a.target = '_blank';
    // a.style.display = 'none';
    // // href 所指向的地址，必须与当前网站同源，否则无效
    // // 如需直接下载的时候，可以将url做如下处理，由nginx做转发获取文件，就没有跨域问题了
    // /* let { url } = file;
    //   try {
    //       url = new URL(file.url).pathname;
    //   } catch (err) {} */
    // a.download = filename || name;
    // a.dispatchEvent(new MouseEvent('click', {}));
}
