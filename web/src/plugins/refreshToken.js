import axios from 'axios';
import store from '$platform.store';

// 模块配置项，包含刷新Token，所需要的参数和URL
const config = {
    refreshTokenKey: 'refreshToken',
    tenantIdKey: 'tenantId',
    refreshInterval: 2000,
    refreshUrl: '/devops/sso/refresh',
};

// 设置一个标识，2s内只触发一次
let isPending = false;

/**
 * @param {Function} fn - 真正执行的函数
 * @param {Number} wait - 定时器等待时间
 * @returns 返回执行的函数
 */
function debounce(fn, wait) {
    let timerId;
    // eslint-disable-next-line func-names
    return function (...args) {
        clearTimeout(timerId); // 清除上一个计时器
        if (!isPending) {
            isPending = true;
            timerId = setTimeout(() => {
                isPending = false;
                fn.apply(this, args); // 执行真正的函数
            }, wait);
        }
    };
}

/**
 * 切换用户的信息
 */
function checkUser() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const tenantId = localStorage.getItem('tenantId');
    if (store) {
        store.commit('user_update', userInfo);
        store.commit('tenantId_update', tenantId);
    }
}

function compareUser() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo'));
    const tenant = localStorage.getItem('tenantId');
    if (!userInfo || !tenant) {
        return false;
    }
    const { erp, tenantId } = store.state.user;
    return userInfo.erp !== erp || tenant !== tenantId;
}

/**
 * 重定向到登录页面
 */
function redirectToLoginPage() {
    // const { href, origin } = window.location;
    // const url = `${origin}/sso/login?ReturnUrl=${href}`;
    // window.location.href = url;
}

/**
 * @param {Erro} err - 错误对象
 * @returns {Promise} 返回一个Promise对象，包含错误信息和状态
 */
function handleErrorResponse(err) {
    if (err.response && err.response.status === 401) {
        // Token 失效，跳转到登录页面
        redirectToLoginPage();
    }
    return Promise.resolve({
        success: false,
        error: err,
    });
}

function createCookie(name, value, minute, path = '/') {
    const date = new Date();
    date.setTime(date.getTime() + (minute * 1000));
    const expires = `; expires=${date.toUTCString()}`;
    document.cookie = `${name}=${value}${expires}; path=${path}`;
}

/**
 * 刷新Token的函数
 * @param {string} refreshToken - 刷新Token的令牌
 * @param {string} [tenantId] - 租户ID, 默认值为null
 * @returns {Promise} 返回一个Promise对象，包含刷新Token后的结果和状态
 */
async function refreshTokenFun(refreshTokenData, tenantId = null) {
    try {
        const res = await axios.get(config.refreshUrl, {
            params: {
                refreshToken: refreshTokenData,
                tenantId,
            },
        });
        const {
            refreshToken: newRefreshToken, token, tokenHead, expiresIn,
        } = res?.data?.data;
        const oauthData = `${tokenHead}${token}`;
        const expiresInTime = new Date().getTime() + expiresIn * 1000;
        createCookie('ARCH-TOKEN', token, expiresIn);
        localStorage.setItem(config.refreshTokenKey, newRefreshToken);
        localStorage.setItem('Authorization', oauthData);
        localStorage.setItem('expiresIn', expiresInTime);
        return {
            success: true,
            data: {
                refreshToken: newRefreshToken,
                token,
                tokenHead,
            },
        };
    } catch (err) {
        return handleErrorResponse(err);
    }
}

/**
 * 自动刷新token的函数
 * @returns {Promise} 返回一个Promise 对象，包含刷新 Token 后的结果和状态
 */
async function autoRefreshToken() {
    const refreshToken = localStorage.getItem(config.refreshTokenKey);
    const tenantId = localStorage.getItem(config.tenantIdKey);

    if (refreshToken) {
        try {
            const result = await refreshTokenFun(refreshToken, tenantId);
            return result;
        } catch (err) {
            return handleErrorResponse(err);
        }
    }
    return false;
}

/**
 * 初始化函数：调用该函数来启动自动刷新 Token 的功能
 */
function init() {
    const debouncedRefreshToken = debounce(autoRefreshToken, config.refreshInterval);
    debouncedRefreshToken();
}

/**
 * 检查登录信息是否完整
 * @returns {Boolean}
 */
function checkAuthorization() {
    return true;
    // // 过期时间
    // const expiresIn = localStorage.getItem('expiresIn');
    // // 登录认证信息
    // const Authorization = localStorage.getItem('Authorization');
    // // 重新认证信息
    // const refreshTokenData = localStorage.getItem('refreshToken');
    // // 租户Id
    // const tenantId = localStorage.getItem('tenantId');
    // return expiresIn && Authorization && refreshTokenData && tenantId;
}

/**
 * 重新刷新认证信息
 */
function refreshAuthorization() {
    const expiresIn = localStorage.getItem('expiresIn');
    // 判断如果过期时间小于等于10分钟，重新刷新Authorization
    if (expiresIn && (expiresIn - new Date().getTime()) <= 10 * 60 * 1000) {
        init();
    }
}

export default {
    init,
    redirectToLoginPage,
    checkAuthorization,
    refreshAuthorization,
    checkUser,
    compareUser,
};
