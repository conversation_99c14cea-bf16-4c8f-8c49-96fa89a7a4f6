// 筛选器里，时间参数的defaultTime要设置一个时间范围，第二个值需要设置为'23:59:59'，涵盖当天的24小时

export const fixupDateDefaultTime = (fieldIns = {}) => {
    if (fieldIns.type === 'daterange' || fieldIns.type === 'date') {
        const originFn = fieldIns.getFragmentProps;
        fieldIns.getFragmentProps = function getFragmentProps() {
            const oldProps = originFn.call(fieldIns);
            return {
                ...oldProps,
                defaultTime: ['00:00:00', '23:59:59'],
            };
        };
    }
};
