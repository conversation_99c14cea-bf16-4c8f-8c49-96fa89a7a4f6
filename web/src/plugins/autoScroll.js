/**
 * auto scroll when dragging an item in scrollable container
 */

/* eslint-disable no-use-before-define */

import { scrollBy } from './utils.dom';

let isScrolling = false;

export function autoScroll({
    evt,
    scrollEl,
    scrollSensitivity,
    scrollStep,
}) {
    const {
        clientX: x,
        clientY: y,
    } = evt;

    const {
        left,
        right,
        top,
        bottom,
        width,
        height,
    } = scrollEl.getBoundingClientRect();

    const {
        scrollWidth,
        scrollHeight,
        // scrollLeft: scrollPosX,
        // scrollTop: scrollPoxY,
    } = scrollEl;

    const canScrollX = scrollWidth > width;
    const canScrollY = scrollHeight > height;

    const isLeft = Math.abs(left - x) <= scrollSensitivity;
    const isRight = Math.abs(right - x) <= scrollSensitivity;
    const isTop = Math.abs(top - y) <= scrollSensitivity;
    const isBottom = Math.abs(bottom - y) <= scrollSensitivity;

    // horizontal scroll
    if (canScrollX && (isLeft || isRight)) {
        if (!isScrolling) {
            isScrolling = true;
            doScroll(scrollEl, isRight ? scrollStep : -scrollStep, 0);
        }
        return;
    }

    // vertical scroll
    if (canScrollY && (isTop || isBottom)) {
        if (!isScrolling) {
            isScrolling = true;
            doScroll(scrollEl, 0, isBottom ? scrollStep : -scrollStep);
        }
        return;
    }

    isScrolling = false;
}

export function clearAutoScroll() {
    isScrolling = false;
}

function doScroll(el, x, y) {
    if (!isScrolling) {
        return;
    }

    scrollBy(el, x, y);
    window.requestAnimationFrame(doScroll.bind(this, el, x, y));
}
