import styleVariables from '@/theme/var.less';

// 使用首字母或者第一个字获取对应的色值
export const getColorByFirstChar = (str) => {
    const colorList = ['#0067B2', '#0067B2', '#01C0DD', '#0ED396', '#78C913', '#F84E54', '#FF8D00', '#FFC700', '#FF8D00', '#28A59A'];
    return colorList[str.charCodeAt(0) % 10];
};

export default styleVariables;
const {
    primaryColor,
    fontColor,
    fontSecendColor,
    fontThirdColor,
    errorColor,
    requiredColor,
    warningColor,
} = styleVariables;

export const fontColorMap = {
    primary: primaryColor,
    default: fontColor,
    secend: fontSecendColor,
    disable: fontThirdColor,
    error: errorColor,
    warning: warningColor,
    required: requiredColor,
};
