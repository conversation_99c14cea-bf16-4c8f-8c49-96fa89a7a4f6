// Serversent Event
import { MessageBox } from 'element-ui';
import axios from 'axios';

let hasBindWindow = false;
let showDialog = false;
let timeOutObj = null;
let source;

const sse = {
    init: (timestamp) => {
        const waitTime = 30000;

        function retry() {
            timeOutObj = setTimeout(() => {
                sse.init(timestamp);
            }, waitTime);
        }

        function isOutdated(localTimestamp, remoteTimestamp) {
            if (!localTimestamp || !remoteTimestamp) {
                return false;
            }
            return localTimestamp < remoteTimestamp;
        }

        function setOnClose() {
            axios.get(`/devops-api/arch/api/v1/sse/version/${timestamp}/close`);
            source.close();
        }

        function setOnMessage(data) {
            if (showDialog) {
                return;
            }
            if (isOutdated(timestamp, data.data)) {
                showDialog = true;
                MessageBox.confirm('检测到有新的版本，是否刷新页面？', '提示', {
                    confirmButtonText: '刷新',
                    cancelButtonText: '知道了，稍后刷新',
                }).then(() => {
                    setOnClose();
                    window.location.reload();
                }).catch(() => {
                    showDialog = false;
                    timeOutObj = setTimeout(() => {
                        setOnMessage(data);
                    }, waitTime);
                });
            }
        }

        function setOnError() {
            if (source.readyState !== 2) {
                setOnClose();
                // 重新连接
                if (document.visibilityState !== 'hidden') {
                    retry();
                }
            }
        }

        function changeTab() {
            // 清除正在重试的任务
            if (timeOutObj) {
                clearTimeout(timeOutObj);
            }
            if (document.visibilityState === 'hidden') {
                setOnClose();
                return;
            }
            if (source.readyState === 2) {
                sse.init(timestamp);
            }
        }

        function bindWindow() {
            if (!hasBindWindow) {
                hasBindWindow = true;
                // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
                window.onbeforeunload = setOnClose;
                // 浏览器窗口可视状态变化
                window.addEventListener('visibilitychange', changeTab, true);
            }
        }

        if (typeof (EventSource) !== 'undefined') {
            source = new EventSource(`/arch/api/v1/sse/version/${timestamp}`, { withCredentials: true });

            source.onerror = setOnError;
            source.onmessage = setOnMessage;

            bindWindow();
        }
    },
};

export default sse;
