export default class LazyLoader {
  #loading = false;

  constructor({
      originData = [], loadedData = [],
      pageSize = 100,
      currentPage = 0,
  }) {
      this.pageSize = pageSize;
      this.currentPage = currentPage;
      this.originData = originData;
      this.loadedData = loadedData;
  }

  get hasMore() {
      return this.currentPage * this.pageSize < this.originData.length;
  }

  get pageCount() {
      return Math.ceil(this.originData.length / this.pageSize);
  }

  load() {
      if (this.#loading) {
          return;
      }
      if (this.hasMore) {
          this.#loading = true;
          const newItem = this.originData.slice(this.currentPage * this.pageSize, this.pageSize * (this.currentPage + 1));
          if (newItem.length) {
              this.loadedData = [...this.loadedData, ...newItem];
              this.currentPage += 1;
          }
          this.#loading = false;
      }
  }

  destroy() {
      this.originData = [];
      this.loadedData = [];
      this.currentPage = 0;
  }
}
