/* const enumHandler = {
    get(obj, prop) {
        // 默认行为是返回属性值， prop通常是一个整数
        if (prop in obj) {
            return obj[prop];
        }
        // 通过 value 或者 code 来取
        // let result = '';
        const map = {};
        const labelMap = {};
        obj.reduce((resultMap, item) => {
            // 取valueMap的话，取完整item
            map[item.value || item.code] = item;
            if (item.label) {
                labelMap[item.value || item.code] = item.label;
            }
            return resultMap;
        }, map);
        if (prop in map) {
            return map[prop];
        }
        if (prop === 'allValues') {
            return Object.keys(map);
        }
        if (prop === 'labelMap') {
            return labelMap;
        }
        return undefined;
    },
};
 */
import isPlainObject from 'lodash/isPlainObject';

export class Enum {
    constructor(data, freeze = false) {
        const cache = {};
        if (Array.isArray(data)) {
            data.forEach((item) => {
                // 可以使用value或者id互取
                cache[item.code] = item;
                cache[item.value] = item;
            });
        }

        if (isPlainObject(data)) {
            Object.keys(data).reduce((targetCache, key) => {
                // eslint-disable-next-line no-param-reassign
                targetCache[data[key]] = key;
                return targetCache;
            }, cache);
        }
        const handler = {
            get(obj, prop) {
                if (prop in cache) {
                    return cache[prop];
                }
                if (prop in data) {
                    return data[prop];
                }
                if (prop === 'allValues') {
                    return data.map(o => o.value);
                }
                if (Array.isArray(data) && prop === 'kvMap') {
                    return data.reduce((result, item) => {
                        result[item.value] = item.code;
                        return result;
                    }, {});
                }
                if (prop === 'list') {
                    return data;
                }
                return undefined;
            },
            set(...args) {
                if (freeze) {
                    throw new Error('Cannot add/update properties on an Enum instance after it is defined');
                } else {
                    return Reflect.set(...args);
                }
            },
        };
        // Freeze the target object to prevent modifications
        return new Proxy(data, handler);
    }
}


export default function createEnum(target = []) {
    return new Enum(target);
}
export const getEnumByProp = targetEnum => (status, key) => {
    const statusObject = targetEnum[status] || {};
    if (key in statusObject) {
        return statusObject[key];
    }
    return statusObject;
};
