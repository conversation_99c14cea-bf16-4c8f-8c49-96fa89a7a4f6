import elementResizeDetectorMaker from 'element-resize-detector';

let supportsPassive = false;
try {
    const opts = Object.defineProperty({}, 'passive', {
        // eslint-disable-next-line getter-return
        get() {
            supportsPassive = true;
        },
    });
    window.addEventListener('testSupportsPassive', null, opts);
// eslint-disable-next-line no-empty
} catch (e) {}

export function closest(el, selector) {
    const matchesSelector = el.matches
      || el.webkitMatchesSelector
      || el.mozMatchesSelector || el.msMatchesSelector;
    while (el) {
        if (typeof selector === 'function') {
            if (selector(el)) return el;
        } else if (matchesSelector.call(el, selector)) {
            break;
        }
        el = el.parentElement;
    }
    return el;
}
export function isAnyPartOfElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    const windowHeight = (window.innerHeight || document.documentElement.clientHeight);
    const windowWidth = (window.innerWidth || document.documentElement.clientWidth);
    // http://stackoverflow.com/questions/325933/determine-whether-two-date-ranges-overlap
    const vertInView = (rect.top <= windowHeight) && ((rect.top + rect.height) >= 0);
    const horInView = (rect.left <= windowWidth) && ((rect.left + rect.width) >= 0);
    return (vertInView && horInView);
}
export function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    return (
        rect.top >= 0
      && rect.left >= 0
      && rect.bottom <= (window.innerHeight
          || document.documentElement.clientHeight)
      && parseInt(rect.right, 10) <= (window.innerWidth
          || document.documentElement.clientWidth)
    );
}
export function scrollElementIntoViewport(el) {
    if (el && el.scrollIntoView) {
        el.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
}
export function onElementScrollToBottom(options) {
    const {
        el, distance = 0, once = false, callback = null,
        passive = true,
    } = options;

    /** 滚动事件触发标识，保证触底区间内只回调一次 */
    let execute = false;

    /** 滚动事件 */
    function onScroll() {
        // 滚动的高度
        const { scrollTop } = el;
        // console.log('scroll', scrollTop);

        /** 滚动条高度 */
        const { scrollHeight } = el;
        if (scrollHeight - scrollTop - distance <= el.clientHeight) {
            if (execute) {
                return;
            }
            execute = !execute;
            if (typeof callback === 'function') callback();
            if (once) el.removeEventListener('scroll', onScroll);
        } else {
            execute = false;
        }
    }
    el.addEventListener('scroll', onScroll, supportsPassive ? { passive } : false);
}
export function focusFirstErrorItem() {
    setTimeout(() => {
        const firstErrorItem = document.getElementsByClassName('is-error')[0];
        const firstItemContent = firstErrorItem.querySelector('.el-form-item__content');
        if (firstItemContent) {
            firstItemContent.tabIndex = '0';
            firstItemContent.focus();
        }
    }, 1);
}

export function copyToClipBoard(txt) {
    const input = document.createElement('input');
    document.body.appendChild(input);
    input.setAttribute('value', txt);
    input.select();
    const result = document.execCommand('copy');
    document.body.removeChild(input);
    return result;
}

export function getWidth(elem, withMargin = false) {
    let width = elem
        && typeof elem.getBoundingClientRect === 'function'
        && elem.getBoundingClientRect().width;
    if (width) {
        width = +width.toFixed(6);
    }
    if (withMargin) {
        const computedStyle = window.getComputedStyle(elem, null);
        const margin = parseInt(computedStyle.marginLeft, 10) + parseInt(computedStyle.marginRight, 10);
        width += margin;
    }
    return width || 0;
}

export function setStyle(elem, styleProperty, value) {
    if (elem && typeof elem.style === 'object') {
        elem.style[styleProperty] = value;
    }
}

export function scrollBy(el, x, y) {
    el.scrollLeft += x;
    el.scrollTop += y;
}
export const isDOM = typeof HTMLElement === 'object'
    ? obj => obj instanceof HTMLElement
    : obj => obj && typeof obj === 'object' && obj.nodeType === 1 && typeof obj.nodeName === 'string';


export function addStyle(styleText) {
    const styleNode = document.createElement('style');
    styleNode.type = 'text/css';
    styleNode.textContent = styleText;
    document.head.appendChild(styleNode);
    return styleNode;
}

export const erd = elementResizeDetectorMaker({
    strategy: 'scroll', // <- For ultra performance.
});

export default {
    supportsPassive,
};
