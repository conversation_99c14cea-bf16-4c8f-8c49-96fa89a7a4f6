// key是icon名字
const filetypeIconMap = {
    code: ['html', 'htm', 'md', 'js', 'python', 'java', 'css', 'vue'],
    doc: ['docx', 'doc'],
    xls: ['xlsx', 'xls', 'numbers'],
    image: ['jpeg', 'jpg', 'png', 'gif', 'bmp'],
    pdf: ['pdf'],
    ppt: ['ppt', 'pptx', 'pptm', 'key'],
    txt: ['txt'],
    zip: ['rar', 'zip'],
};
export const filetypeIconMapTransfer = {};
Object.keys(filetypeIconMap).forEach((key) => {
    const filetypeList = filetypeIconMap[key];
    filetypeList.forEach((f) => {
        filetypeIconMapTransfer[f] = key;
    });
});
export function getFileType(filename) {
    const type = filename.split('.').pop();
    return type;
}
export function getFileTypeIconName(filename) {
    const type = getFileType(filename).toLowerCase();
    // console.log(type);
    if (!type) {
        return 'other';
    }
    if (type in filetypeIconMap) {
        return type;
    }
    if (type in filetypeIconMapTransfer) {
        return filetypeIconMapTransfer[type];
    }
    return 'other';
}
export default {
    // filetypeIconMap,
    // filetypeIconMapTransfer,
    getFileType,
    getFileTypeIconName,
};
