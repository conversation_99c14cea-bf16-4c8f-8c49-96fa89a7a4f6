/* eslint-disable */
// From http://blog.51cto.com/tonyqus/1131887
// 过滤html
export function filterPasteText(str) {
  str = str.replace(/\r\n|\n|\r/ig, '');
  //remove html body form
  str = str.replace(/<\/?(html|body|form)(?=[\s\/>])[^>]*>/ig, '');
  //remove doctype
  str = str.replace(/<(!DOCTYPE)(\n|.)*?>/ig, '');
  //remove xml tags
  str = str.replace(/<(\/?(\?xml(:\w+)?|xml|\w+:\w+)(?=[\s\/>]))[^>]*>/gi, '');
  //remove head
  str = str.replace(/<head[^>]*>(\n|.)*?<\/head>/ig, '');
  //remove <xxx />
  str = str.replace(/<(script|style|link|title|meta|textarea|option|select|iframe|hr)(\n|.)*?\/>/ig, '');
  //remove empty span
  str = str.replace(/<span[^>]*?><\/span>/ig, '');
  //remove <xxx>...</xxx>
  str = str.replace(/<(head|script|style|textarea|button|select|option|iframe)[^>]*>(\n|.)*?<\/\1>/ig, '');
  //remove table and <a> tag, <img> tag,<input> tag (this can help filter unclosed tag)
  str = str.replace(/<\/?(a|table|tr|td|tbody|thead|th|img|input|iframe|div)[^>]*>/ig, '');
  //remove bad attributes
  /* do {
      let len = str.length;
      str = str.replace(/(<[a-z][^>]*\s)(?:id|name|language|type|class|on\w+|\w+:\w+)=(?:"[^"]*"|\w+)\s?/gi, "$1");
  } while (len != str.length); */

  return str;
}
export function isWordDocument(strValue) {
  var re = new RegExp(/(class=\"?Mso|style=\"[^\"]*\bmso\-|w:WordDocument)/ig);
  return re.test(strValue);
}
// 过滤Word xml
export function filterPasteWord(str) {
  str = str.replace(/\r\n|\n|\r/ig, '');
  str = str.replace(/^\s*(&nbsp;)+/ig, '');
  str = str.replace(/(&nbsp;|<br[^>]*>)+\s*$/ig, '');
  str = str.replace(/<!--[\s\S]*?-->/ig, '');
  str = str.replace(/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|xml|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi, '');
  str = str.replace(/<p [^>]*class="?MsoHeading"?[^>]*>(.*?)<\/p>/gi, "<strong>$1</strong>");
  str = str.replace(/(lang)\s*=\s*([\'\"]?)[\w-]+\2/ig, '');
  str = str.replace(/(<[a-z][^>]*)\sstyle="([^"]*)"/gi, function (str, tag, style) {
      var n = [],
          i = 0,
          s = style.trim().replace(/&quot;/gi, "'").split(";");
      for (var i = 0; i < s.length; i++) {
          v = s[i];
          var name, value,
              parts = v.split(":");

          if (parts.length == 2) {
              name = parts[0].toLowerCase();
              value = parts[1].toLowerCase();
              // Translate certain MS Office styles into their CSS equivalents
              switch (name) {
                  case "mso-padding-alt":
                  case "mso-padding-top-alt":
                  case "mso-padding-right-alt":
                  case "mso-padding-bottom-alt":
                  case "mso-padding-left-alt":
                  case "mso-margin-alt":
                  case "mso-margin-top-alt":
                  case "mso-margin-right-alt":
                  case "mso-margin-bottom-alt":
                  case "mso-margin-left-alt":
                  case "mso-table-layout-alt":
                  case "mso-height":
                  case "mso-width":
                  case "mso-vertical-align-alt":
                      n[i++] = name.replace(/^mso-|-alt$/g, '') + ":" + ensureUnits(value);
                      continue;
                  case "horiz-align":
                      n[i++] = "text-align:" + value;
                      continue;
                  case "vert-align":
                      n[i++] = "vertical-align:" + value;
                      continue;
                  case "font-color":
                  case "mso-foreground":
                      n[i++] = "color:" + value;
                      continue;
                  case "mso-background":
                  case "mso-highlight":
                      n[i++] = "background:" + value;
                      continue;
                  case "mso-default-height":
                      n[i++] = "min-height:" + ensureUnits(value);
                      continue;
                  case "mso-default-width":
                      n[i++] = "min-;" + ensureUnits(value);
                      continue;
                  case "mso-padding-between-alt":
                      n[i++] = "border-collapse:separate;border-spacing:" + ensureUnits(value);
                      continue;
                  case "text-line-through":
                      if ((value == "single") || (value == "double")) {
                          n[i++] = "text-decoration:line-through";
                      }
                      continue;
                  case "mso-zero-height":
                      if (value == "yes") {
                          n[i++] = "display:none";
                      }
                      continue;
              }
              if (/^(mso|column|font-emph|lang|layout|line-break|list-image|nav|panose|punct|row|ruby|sep|size|src|tab-|table-border|text-(?:align|decor|indent|trans)|top-bar|version|vnd|word-break)/.test(name)) {
                  continue;
              }
              n[i++] = name + ":" + parts[1];        // Lower-case name, but keep value case
          }
      }
      if (i > 0) {
          return tag + ' style="' + n.join(';') + '"';
      }
      return tag;
  });
  return str;
}
