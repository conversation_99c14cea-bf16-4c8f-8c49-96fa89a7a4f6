import isFunction from 'lodash/isFunction';
import { HttpStack, HttpUtils } from '@jmodule/http';
// import refreshToken from './refreshToken';
import { Message } from 'element-ui';
import router from '$platform.router';

function throwError(response) {
    const error = new Error((response.data || {}).message);
    // 与ErrorHandle中的错误对象保持一致
    error.response = response;
    error.config = response.config;

    // 对此接口禁用错误堆栈信息
    if (!response.config.disableErrorStack) {
        HttpStack.pushError(error);
    }

    return error;
}

function responseHandle(response) {
    const { config } = response;
    if (config.responseHook && isFunction(config.responseHook)) {
        const changedResponse = config.responseHook(response);
        if (changedResponse) {
            Object.assign(response, changedResponse);
        }
    }

    /* 纯文本类型数据处理 */
    if (response.config.responseType === 'text') {
        return response.data;
    }

    if (response.config.responseType === 'blob') {
        return response;
    }

    /* 以下是json类型数据的处理 */
    if (response.data && response.data.status) {
        /* eslint-disable */
        response.data.code = response.data.status;
    }

    // Success
    if (response.data && response.data.code === 200) {
        return HttpUtils.transformNull(response.data.data);
    }

    // 没有Authorization
    // if (response.data && response.data.code === 401) {
    //     refreshToken.redirectToLoginPage();
    // }

    // Redirect
    if (response.data && (response.data.code === 304)) {
        console.log('304-old=^_^=前端重定向到登陆页面');
        // ReturnUrl
        const oldurl = response.data.data;
        if (oldurl) {
            const base = oldurl.split('=');
            if (base[1]) {
                const newUrl = new URL(base[1]);
                window.location.href = `${base[0]}=${newUrl.origin}`;
            } else {
                window.location.href = oldurl;
            }
        }
        console.warn('url=', oldurl)
        throw new Error('Url Redirect');
    }


    throw throwError(response);
}

function responseErrorHandle(error) {
    const response = error.response || {};
    if ([502, 503, 504].includes(response.status)) {
        Message.error('ServerError');
        throw new Error('ServerError');
    }
    if ([402, 412].includes(response.status) && router.history.pending.path !== '/login') {
        window.location.href = `${window.location.origin}/login?statusCode=${response.status}`;
        // throw new Error(error);
    }
    if (response.status === 406) {
        //     console.log('=^_^=前端重定向到登陆页面');
        const { href } = window.location;
        const url = response.headers?.location;
        window.location.href = `${url}?statusCode=${response.status}`;
        throw new Error(error);
    }
    console.log(response, 'responseresponse');
    if (response.status === 426) {
        //     console.log('=^_^=前端重定向到登陆页面');
        const { href } = window.location;
        const url = response.headers?.location;
        window.location.href = `${url}?returnUrl=${href}`;
        throw new Error(error);
    }
    // 在@jmodule/http里已经pushError过了，这里只需要抛一下错误就行了
    // if (response.status >= 400 && response.status <= 501) {
    if (response.status === 401) {
        const { href, origin} = window.location;
        const url = `${origin}/sso/login`;
        console.log('=^_^=前端重定向到登陆页面');
        window.location.href = url;
        throw new Error(error);
    }
    // }
    throw new Error(error);
}

export default {
    responseHandle,
    responseErrorHandle,
};
