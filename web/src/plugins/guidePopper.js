import merge from 'lodash/merge';
import { createPopper } from '@popperjs/core';
import './guide.less';
// FIXME: 临时方案，时间比较紧，待完善

// const showEvents = ['mouseenter', 'focus'];
// const hideEvents = ['mouseleave', 'blur'];
/* showEvents.forEach((event) => {
    button.addEventListener(event, show);
});

hideEvents.forEach((event) => {
    button.addEventListener(event, hide);
}); */
const DefaultConfig = {
    modifiers: [
        {
            name: 'offset',
            options: {
                offset: [0, 8],
            },
        },
    ],
};
export class GuidePopper {
    constructor(button, tooltip, config) {
        this.button = button;
        this.tooltip = tooltip;

        const mergedConfig = config ? merge({}, DefaultConfig, config) : DefaultConfig;
        this.config = mergedConfig;
    }

    create() {
        const { button, tooltip } = this;
        this.popperInstance = createPopper(button, tooltip, this.config);
    }

    destroy() {
        const { popperInstance } = this;
        if (popperInstance) {
            popperInstance.destroy();
            this.popperInstance = null;
        }
    }

    show() {
        this.tooltip.setAttribute('data-show', '');
        this.create();
    }

    hide() {
        this.tooltip.removeAttribute('data-show');
        this.destroy();
    }

    update() {
        if (this.popperInstance) {
            this.popperInstance.update();
        }
    }
}

// 先简单处理，以后有需要的时候做功能扩充
export default class Guide {
    constructor({
        key,
    } = {}) {
        this.localStorageKey = key;
        if (key) {
            this.visible = !localStorage.getItem(key);
        }
        // this.button = button;
        // this.tooltip = tooltip;
    }

    // TODO: 这里需要处理一下tooltip的基本模版
    initPopper(reference, tooltipEl, popperConfig) {
        if (!reference || !tooltipEl) {
            return undefined;
        }
        // this.button = reference;
        // this.tooltip = tooltipEl;
        this.popper = new GuidePopper(reference, tooltipEl, popperConfig);
        if (this.visible) {
            this.popper.show();
        }
        return this.popper;
    }

    hideGuide(remove = true) {
        if (!this.popper) {
            return;
        }
        if (remove) {
            localStorage.setItem(this.localStorageKey, 1);
            this.visible = false;
        }
        this.popper.hide();
    }
}
