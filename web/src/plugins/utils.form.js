import AsyncValidator from 'async-validator';

/* 表单校验 */
let validator;
let lastRule;
export function validate(value, rule) {
    if (!rule || !rule.length) {
        return Promise.resolve(value);
    }

    validator = lastRule === rule ? validator : new AsyncValidator({ value: rule });
    lastRule = rule;
    return new Promise((resolve, reject) => {
        validator.validate({ value }, (err) => {
            if (!err || !err.length) {
                resolve(value);
            } else {
                reject(err.map(item => item.message));
            }
        });
    });
}

export function multiValidate(values, rules) {
    const props = Object.keys(rules);
    const promises = props
        .map(prop => validate(values[prop], rules[prop]));
    return Promise.all(promises);
}

/**
 * 通用的非空校验
 * @param {Array} rule
 * @param {*} value
 * @param {Function} callback
 */
export function formRequiredValidator(rule, value, callback) {
    if (typeof value === 'undefined' || value === null) {
        callback(new Error(rule.message));
    }
    if (value instanceof Array && value.length === 0) {
        callback(new Error(rule.message));
        return;
    }
    if (typeof value === 'string' && value.trim() === '') {
        callback(new Error(rule.message));
        return;
    }
    if (typeof value !== 'object' && `${value}`.length === 0) {
        callback(new Error(rule.message));
        return;
    }
    callback();
}
export function textRuleCreator(min, max, required, requiredMsg) {
    const countRule = {
        trigger: 'change',
    };
    const res = [countRule];
    if (required) {
        res.push({
            required: true,
            message: requiredMsg,
            trigger: 'change',
        });
    }
    if (max) {
        countRule.max = max;
        countRule.message = `限${max}字`;
    }
    if (min !== undefined && min !== null) {
        countRule.min = min;
        countRule.message = `不得少于${min}字`;
    }
    if (max && min !== undefined && min !== null) {
        countRule.message = `长度在${min} - ${max}个字符`;
    }
    return res;
}
