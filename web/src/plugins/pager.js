// TODO: 这个是从度量拷过来的的。抓狂。很多互通的东西怎么整合？
export default class Pager {
    #total = 0;

    constructor({
        pageSize = 10,
        currentPage = 1,
        total = 0,
    } = {}) {
        this.pageSize = Number(pageSize);
        this.currentPage = Number(currentPage);
        this.#total = Number(total);
    }

    get hasMorePages() {
        return this.total > (this.currentPage + 1) * this.pageSize;
    }

    get pageCount() {
        return Math.floor((this.total) / this.pageSize);
    }

    set total(val) {
        this.#total = val;
    }

    get total() {
        return this.#total;
    }

    next() {
        this.currentPage += 1;
    }

    prev() {
        if (this.currentPage < 1) {
            return;
        }
        this.currentPage -= 1;
    }
}
