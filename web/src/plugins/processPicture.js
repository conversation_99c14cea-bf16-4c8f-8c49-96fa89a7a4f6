import axios from 'axios';

/**
 * @param {String} headImage 图片链接
 * @returns 加登录信息的图片链接
 */
const imageAddAuthorization = (headImage) => {
    let Authorization = localStorage.getItem('Authorization');
    if (!headImage || !Authorization) {
        return headImage;
    }
    let imageUrl = headImage;
    if (typeof (Authorization) !== 'string') {
        return headImage;
    }
    Authorization = Authorization.replace('Bearer ', '');
    if (!headImage.startsWith('http')) {
        imageUrl = `${window.location.origin}${imageUrl}`;
    }
    if (imageUrl.includes('?')) {
        imageUrl = `${imageUrl}&token=${Authorization}`;
    } else {
        imageUrl = `${imageUrl}?token=${Authorization}`;
    }
    return imageUrl;
};

const imageToBolb = (headImage) => {
    if (!headImage) {
        return headImage;
    }
    headImage = headImage.replace(/\/devops-api\/storage\/[^/]+\//, '');
    return axios({
        method: 'post',
        url: '/devops-api/arch/api/v1/files/load',
        data: { filePath: headImage },
        headers: {
            Authorization: localStorage.getItem('Authorization'),
        },
    }).then(res => `/devops-api/storage${res.data.data}`);
};

const imageToPreview = (headImage) => {
    if (!headImage) {
        return headImage;
    }
    headImage = headImage.replace(/\/devops-api\/storage\/[^/]+\//, '');
    return axios({
        method: 'post',
        url: '/devops-api/arch/api/v1/files/load',
        data: { filePath: headImage },
        headers: {
            Authorization: localStorage.getItem('Authorization'),
        },
    }).then((res) => {
        axios({
            method: 'get',
            url: `/devops-api/storage${res.data.data}`,
            responseType: 'blob',
        }).then(data => new Blob([data.data]));
    });
};

const imageTodownload = (headImage, filename, name) => {
    if (!headImage) {
        return headImage;
    }
    headImage = headImage.replace(/\/devops-api\/storage\/[^/]+\//, '');
    return axios({
        method: 'post',
        url: '/devops-api/arch/api/v1/files/load',
        data: { filePath: headImage },
        headers: {
            Authorization: localStorage.getItem('Authorization'),
        },
    }).then((res) => {
        axios({
            method: 'get',
            url: `/devops-api/storage${res.data.data}`,
            responseType: 'blob',
        }).then((data) => {
            const blob = new Blob([data.data]);
            const a = document.createElement('a');
            a.target = '_blank';
            a.style.display = 'none';
            a.href = URL.createObjectURL(blob);
            a.download = filename || name;
            a.dispatchEvent(new MouseEvent('click', {}));
        });
    });
};

const userImage = headImage => headImage;

export {
    imageAddAuthorization,
    imageToBolb,
    imageTodownload,
    imageToPreview,
    userImage,
};
