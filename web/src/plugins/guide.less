
.jacp-guide-popper-tooltip {
    background: var(--color--primary);
    color: #fff;
    border-radius: var(--radius--medium);
    display: none;
    min-width: 150px;
    border: 1px solid #ebeef5;
    padding: var(--gutter--medium);
    z-index: 2000;
    line-height: 1;
    text-align: justify;
    font-size: var(--font-size--content);
    transition: box-shadow 0.2s;
    &:hover{
      box-shadow:  0 4px 16px 0 rgba(48,49,51,0.1);
    }
 
    word-break: break-all;
    &__button{
      &.el-button--text{
        color: #fff;
      }
      &.el-button--primary{
        background-color: #fff;
        color: var(--color--primary);
        border-radius: 14px;
      }
    }
  }

.jacp-guide-popper-tooltip[data-show] {
    display: block;
  }

  .jacp-guide-popper-tooltip__arrow,
  .jacp-guide-popper-tooltip__arrow::before {
    position: absolute;
    width: 8px;
    height: 8px;
    z-index: -1;
  }

  .jacp-guide-popper-tooltip__arrow::before {
    content: '';
    transform: rotate(45deg);
    // background: #fff;
    background: var(--color--primary);
  }

  .jacp-guide-popper-tooltip[data-popper-placement^='top'] > .jacp-guide-popper-tooltip__arrow {
    bottom: -4px;
  }

  .jacp-guide-popper-tooltip[data-popper-placement^='bottom'] > .jacp-guide-popper-tooltip__arrow {
    top: -4px;
  }

  .jacp-guide-popper-tooltip[data-popper-placement^='left'] > .jacp-guide-popper-tooltip__arrow{
    right: -4px;
  }

  .jacp-guide-popper-tooltip[data-popper-placement^='right'] > .jacp-guide-popper-tooltip__arrow {
    left: -4px;
  }