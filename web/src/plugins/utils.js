
import {
    cloneDeep, each, isArray,
} from 'lodash';
import router from '$platform.router';
import store from '$platform.store';

export * from './utils.filetype';
export * from './utils.dom';
export * from './utils.file';
export * from './utils.form';

export function openLink({ route, isPopup }) {
    if (isPopup) {
        const resolved = router.resolve(route);
        window.open(resolved.href, '_blank');
    } else {
        router.push(route, () => {});
    }
}

export function replaceUrl(params = {}, name = router.currentRoute.name, withQuery = false) {
    router.replace({
        name,
        params: Object.assign(router.currentRoute.params, params),
        query: withQuery ? router.currentRoute.query : {},
    });
}
export function replaceRouteQuery({ query = {}, params = {}, name }) {
    router.replace({
        ...router.currentRoute,
        name: name || router.currentRoute.name,
        params: { ...router.currentRoute.params, ...params },
        query: { ...router.currentRoute.query, ...query },
    });
}
export function getSpaceId(spaceKey) {
    const { keyMap, keyMapPlus } = store.state.chilldteamspace;
    const { id } = keyMap[spaceKey] || keyMapPlus[spaceKey] || {};
    if (id) {
        return id;
    }
    return (keyMapPlus[spaceKey] || {}).id;
}
export function promisefy(fn) {
    return (...args) => {
        const res = fn.apply(this, args);
        return res && res.then ? res : Promise.resolve(res);
    };
}
export function clearRepeatInArray(arr, id = 'id', ignoreCase) {
    const map = {};
    const res = [];
    (arr || []).forEach((item) => {
        let idValue = item[id];
        if (ignoreCase && typeof idValue === 'string') {
            idValue = idValue.toLowerCase();
        }
        if (!map[idValue]) {
            map[idValue] = true;
            res.push(item);
        }
    });
    return res;
}
export function toNumber(data) {
    return data ? +data : data;
}
export function equalIgnoreCase(a, b) {
    if (typeof a === 'string' && typeof b === 'string') {
        return a.toLowerCase() === b.toLowerCase();
    }
    return a === b;
}
export function isCurrentUser(user = {}) {
    return equalIgnoreCase(user.erp, store.state.user.erp);
}
export function isCurrentUserExist(targetArray = []) {
    return !!targetArray.find(user => equalIgnoreCase(user.erp, store.state.user.erp));
}
export function getSpaceIdFromRouter() {
    return toNumber(getSpaceId(router.currentRoute.params.spaceKey));
}

/* 根据config 处理data, 并返回处理结果 */
export function dataFormatter(config = {}, { key, value }) {
    let mapValue = config[key]; // mapValue = { targetKey, handle }
    if (!mapValue) {
        return { key, value };
    }

    const configType = typeof mapValue;
    if (configType === 'function') {
        mapValue = {
            handle: mapValue,
        };
    }
    if (configType === 'string') {
        mapValue = {
            targetKey: mapValue,
        };
    }
    return {
        key: mapValue.targetKey || key,
        value: mapValue.handle ? mapValue.handle(value) : value,
    };
}

/* 根据config进行sourceData转换 */
export function jsonAdapter(config = {}, sourceData = {}, needflattenObject = false) {
    const transformedData = {};
    const clearData = {};
    Object.keys(sourceData).forEach((originKey) => {
        if (!config[originKey]) {
            return;
        }
        const { key, value } = dataFormatter(config, {
            key: originKey,
            value: sourceData[originKey],
        });
        transformedData[key] = value;
        if (key !== originKey && !config[originKey].doNotRemoveOriginKey) { // 移除原始值
            clearData[originKey] = undefined;
        }
    });
    if (needflattenObject) {
        // eslint-disable-next-line no-use-before-define
        return flattenObject(Object.assign({}, sourceData, transformedData, clearData), false);
    }
    return Object.assign({}, sourceData, transformedData, clearData);
}

export const isObject = obj => Object.prototype.toString.call(obj) === '[object Object]';
export function removeUndefined(val) {
    const newVal = {};
    Object.keys(val).forEach((key) => {
        if (val[key] !== undefined) {
            newVal[key] = val[key];
        }
    });
    return newVal;
}

export function getSpaceInfo(spaceKey) {
    return store.state.chilldteamspace.keyMap[spaceKey];
}
export function initProperties(instance, defaultValues = [], intialValue = {}) {
    if (!instance || !Array.isArray(defaultValues)) {
        throw new Error('initProperties 函数参数错误');
    }
    defaultValues.forEach((item) => {
        const [property, defaultValue] = Array.isArray(item) ? item : [item];
        // toLocal 里面通常返回了所有的字段。所以即使 b[a] === undefined, a in b 也等于 true
        if (typeof intialValue[property] !== 'undefined') {
            instance[property] = intialValue[property];
            return;
        }
        // 已经设置过了初始值的就不用设了？否则像空字符串或者0会被替换成undefined
        if (property in instance) {
            return;
        }
        // 深度copy一下初始值，否则用的都是同一个引用地址了
        instance[property] = instance[property] ?? cloneDeep(defaultValue);
    });
}
export const findOptionExistsByGroup = (projectGroup = [], fn = () => false) => {
    const target = projectGroup.reduce((flatOptions = [], { options = [] } = {}) => [...flatOptions, ...options], []);
    return target.find(fn);
};

let unique = 0;

export const uuid = (prefix) => {
    const time = Date.now();
    const random = Math.floor(Math.random() * 100000);

    unique += 1;

    return `${prefix}_${random}${unique}${String(time)}`;
};

// 暂时留下
const userFilterCache = new WeakMap();
export const userFilter = (users = []) => {
    if (!userFilterCache.has(users)) {
        userFilterCache.set(users, (keyword) => {
            if (!keyword) {
                return Promise.resolve(users);
            }
            return Promise.resolve(users
                .filter(member => member.name.includes(keyword)
            || member.erp.toLowerCase().includes(keyword.toLowerCase())));
        });
    }
    return userFilterCache.get(users);
};
export function flattenObject(obj, needDelimiter = true, delimiter = '.') {
    const delim = delimiter || '.';
    const nobj = {};

    each(obj, (val, key) => {
        // ensure is JSON key-value map, not array
        if (isObject(val) && !isArray(val)) {
            // union the returned result by concat all keys
            const strip = flattenObject(val, delim);
            each(strip, (v, k) => {
                if (needDelimiter) {
                    nobj[key + delim + k] = v;
                } else {
                    nobj[k] = v;
                }
            });
        } else {
            nobj[key] = val;
        }
    });
    return nobj;
}
// 字符串截取 包含对中文处理
export const byteSlice = (str, n) => {
    let len = 0;
    let result = '';
    Array.from(str).some((char, index) => {
        if (/[\u4e00-\u9fa5]/.test(str[index])) { // 判断为中文  长度为三字节（可根据实际需求更换长度，将所加长度更改即可）
            len += 2;
        } else { // 其余则长度为一字节
            len += 1;
        }
        if (len < n) { // 当长度大于传入的截取长度时，退出循环
            result += char; // 将每个长度范围内的字节加入到新的字符串中
            return false;
        }
        return true;
    });
    return result; // 返回截取好的字符串
};
export const calcLength = text => text.replace(/[\u4e00-\u9fa5]/ig, 'aa').length;
export function pipe(...args) {
    // console.log('pipe init---->', args);
    return x => args.reduce((acc, cur) => {
        // console.log('pipe:::start', cur);
        if (!(acc instanceof Promise)) {
            acc = Promise.resolve(acc);
        }
        const result = acc.then(cur);
        // console.log('pipe:::result in loop', result);
        return result;
    }, x);
}
export const utils = {
    byteSlice,
    calcLength,
    // router utils
    getSpaceId,
    getSpaceIdFromRouter,
    getSpaceInfo,
    replaceUrl,
    // store utils
    isCurrentUser,
    isCurrentUserExist,
};
export default {
    install(Vue) {
        // TODO: remove
        const VueAlias = Vue;
        VueAlias.prototype.$utils = utils;
    },
};
