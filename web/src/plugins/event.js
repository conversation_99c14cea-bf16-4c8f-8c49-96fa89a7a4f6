import Vue from 'vue';

export const event = new Vue();
const eventCache = new WeakMap();
// const initedEventCache = new WeakMap();
const eventFnCache = new WeakMap();
window.document.addEventListener(
    'click',
    (e) => {
        event.$emit('document.click.capture', e);
    },
    true,
);
window.document.addEventListener(
    'click',
    (e) => {
        event.$emit('document.click', e);
    },
    false,
);

export default {
    install(Vue2) {
        const VueAlias = Vue2;
        VueAlias.prototype.$initEvent = function initEvent() {
            // if (initedEventCache.get(this)) {
            //     return initedEventCache.get(this);
            // }

            // // 初始化事件名集合
            // const sets = new Set();
            // eventCache.set(this, sets);

            // // 分配空间存储事件及处理函数
            // const eventFnMap = {};
            // eventFnCache.set(this, eventFnMap);

            // // 单例
            // const componentEvent = {
            //     $on(name, handle) {
            //         sets.add(name);
            //         eventFnMap[name] = eventFnMap[name] || new Set();
            //         eventFnMap[name].add(handle);
            //         event.$on(name, handle);
            //         return this;
            //     },
            //     $off: event.$off,
            // };
            // initedEventCache.set(this, componentEvent);
            // return componentEvent;
            return event;
        };
        VueAlias.mixin({
            beforeDestroy() {
                (eventCache.get(this) || []).forEach((eventName) => {
                    const fnMap = eventFnCache.get(this) || {};
                    (fnMap[eventName] || []).forEach((eventHandle) => {
                        event.$off(eventName, eventHandle);
                    });
                });
            },
        });
        VueAlias.event = event;
    },
};
