/* eslint-disable no-use-before-define */
import { i18n } from './locale/index';

const timestamp2Str = (ts) => {
    if (ts < 10000) {
        return 'N/A';
    }
    try {
        if (ts) {
            const time = new Date(ts);
            const y = time.getFullYear();
            const M = time.getMonth() + 1;
            const d = time.getDate();
            const h = time.getHours();
            const m = time.getMinutes();
            const s = time.getSeconds();
            return `${y}-${addZero(M)}-${addZero(d)} ${addZero(h)}:${addZero(m)}:${addZero(s)}`;
        }
        return '';
    } catch (e) {
        return 'N/A';
    }
};

// 公共函数，涉及到 i18n ，放进 common.js 报错，暂时先放在这里吧
const translateInstanceStatus = (status) => {
    switch (status) {
    case 1: return i18n.t('jacp.message.waitingDispatch');
    case 2: return i18n.t('jacp.message.waitingWorkerReceive');
    case 3: return i18n.t('jacp.message.running');
    case 4: return i18n.t('jacp.message.failed');
    case 5: return i18n.t('jacp.message.success');
    case 9: return i18n.t('jacp.message.canceled');
    case 10: return i18n.t('jacp.message.stopped');
    default: return 'unknown';
    }
};

const translateWfInstanceStatus = (status) => {
    switch (status) {
    case 1: return i18n.t('jacp.message.wfWaiting');
    case 2: return i18n.t('jacp.message.running');
    case 3: return i18n.t('jacp.message.failed');
    case 4: return i18n.t('jacp.message.success');
    case 10: return i18n.t('jacp.message.stopped');
    default: return 'unknown';
    }
};

// 更换语言
const switchLanguage = (cmd) => {
    i18n.locale = cmd;
    // 存储到LangStorage
    window.localStorage.setItem('oms_lang', cmd);
};

function addZero(m) {
    return m < 10 ? `0${m}` : m;
}

export default {
    timestamp2Str,
    translateInstanceStatus,
    translateWfInstanceStatus,
    switchLanguage,
};
