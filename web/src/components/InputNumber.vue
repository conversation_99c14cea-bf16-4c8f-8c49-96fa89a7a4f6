<template>
    <div
        @dragstart.prevent
        :class="[
            'el-input-number',
            inputNumberSize ? 'el-input-number--' + inputNumberSize : '',
            { 'is-disabled': inputNumberDisabled },
            { 'is-without-controls': !controls },
            { 'is-controls-right': controlsAtRight }
        ]"
    >
        <span
            class="el-input-number__decrease"
            role="button"
            v-if="controls"
            v-repeat-click="decrease"
            :class="{'is-disabled': minDisabled}"
            @keydown.enter="decrease"
        >
            <i :class="`el-icon-${controlsAtRight ? 'arrow-down' : 'minus'}`" />
        </span>
        <span
            class="el-input-number__increase"
            role="button"
            v-if="controls"
            v-repeat-click="increase"
            :class="{'is-disabled': maxDisabled}"
            @keydown.enter="increase"
        >
            <i :class="`el-icon-${controlsAtRight ? 'arrow-up' : 'plus'}`" />
        </span>
        <el-input
            ref="input"
            type="number"
            :value="currentInputValue"
            :disabled="inputNumberDisabled"
            :size="inputNumberSize"
            :max="max"
            :min="min"
            :name="name"
            :label="label"
            @keydown.up.native.prevent="increase"
            @keydown.down.native.prevent="decrease"
            @blur="handleBlur"
            @focus="handleFocus"
            @change="handleInputChange"
            @input="handleInput"
        />
    </div>
</template>
<script>
import Focus from 'element-ui/src/mixins/focus';
import RepeatClick from 'element-ui/src/directives/repeat-click';

export default {
    name: 'JacpInputNumber',
    mixins: [Focus('input')],
    inject: {
        elForm: {
            default: '',
        },
        elFormItem: {
            default: '',
        },
    },
    directives: {
        repeatClick: RepeatClick,
    },
    props: {
        step: {
            type: Number,
            default: 1,
        },
        max: {
            type: Number,
            default: Infinity,
        },
        min: {
            type: Number,
            default: -Infinity,
        },
        value: {
            type: [Number, String],
            default: 0,
        },
        disabled: Boolean,
        size: String,
        controls: {
            type: Boolean,
            default: true,
        },
        controlsPosition: {
            type: String,
            default: '',
        },
        name: String,
        label: String,
        precision: {
            type: Number,
            validator(val) {
                return val >= 0 && val === parseInt(val, 10);
            },
        },
    },
    data() {
        return {
            currentValue: 0,
        };
    },
    watch: {
        value: {
            immediate: true,
            handler(value) {
                let newVal = value === undefined ? value : Number(value);
                if (newVal !== undefined) {
                    if (Number.isNaN(newVal)) {
                        return;
                    }
                    if (this.precision !== undefined) {
                        newVal = this.toPrecision(newVal, this.precision);
                    }
                }
                if (newVal >= this.max) {
                    newVal = this.max;
                }
                if (newVal <= this.min) {
                    newVal = this.min;
                }
                this.currentValue = newVal;
                this.$emit('input', newVal);
            },
        },
    },
    computed: {
        minDisabled() {
            return this.decrease_computed(this.value, this.step) < this.min;
        },
        maxDisabled() {
            return this.increase_computed(this.value, this.step) > this.max;
        },
        numPrecision() {
            const {
                value, step, getPrecision, precision,
            } = this;
            const stepPrecision = getPrecision(step);
            if (precision !== undefined) {
                return precision;
            }
            return Math.max(getPrecision(value), stepPrecision);
        },
        controlsAtRight() {
            return this.controlsPosition === 'right';
        },
        elFormItemSize() {
            return (this.elFormItem || {}).elFormItemSize;
        },
        inputNumberSize() {
            return this.size || this.elFormItemSize || (this.$ELEMENT || {}).size;
        },
        inputNumberDisabled() {
            return this.disabled || (this.elForm || {}).disabled;
        },
        currentInputValue() {
            const { currentValue } = this;
            if (typeof currentValue === 'number' && this.precision !== undefined) {
                return currentValue.toFixed(this.precision);
            }
            return currentValue;
        },
    },
    methods: {
        toPrecision(num, precision) {
            let tempPrecision = precision;
            if (precision === undefined) {
                tempPrecision = this.numPrecision;
            }
            return parseFloat(parseFloat(Number(num).toFixed(tempPrecision)));
        },
        getPrecision(value) {
            if (value === undefined) {
                return 0;
            }
            const valueString = value.toString();
            const dotPosition = valueString.indexOf('.');
            let precision = 0;
            if (dotPosition !== -1) {
                precision = valueString.length - dotPosition - 1;
            }
            return precision;
        },
        increase_computed(val, step) {
            if (typeof val !== 'number' && val !== undefined) {
                return this.currentValue;
            }

            const precisionFactor = 10 ** this.numPrecision;
            return this.toPrecision(((precisionFactor * val) + (precisionFactor * step))
                    / precisionFactor);
        },
        decrease_computed(val, step) {
            if (typeof val !== 'number' && val !== undefined) {
                return this.currentValue;
            }

            const precisionFactor = 10 ** this.numPrecision;

            return this.toPrecision(((precisionFactor * val) - (precisionFactor * step))
                    / precisionFactor);
        },
        increase() {
            if (this.inputNumberDisabled || this.maxDisabled) {
                return;
            }
            const value = this.value || 0;
            const newVal = this.increase_computed(value, this.step);
            this.setCurrentValue(newVal);
        },
        decrease() {
            if (this.inputNumberDisabled || this.minDisabled) {
                return;
            }
            const value = this.value || 0;
            const newVal = this.decrease_computed(value, this.step);
            this.setCurrentValue(newVal);
        },
        handleBlur(event) {
            this.$emit('blur', event);
            this.currentValue = this.currentInputValue;
        },
        handleFocus(event) {
            this.$emit('focus', event);
        },
        setCurrentValue(val) {
            let newVal = val;
            const oldVal = this.currentValue;
            if (typeof newVal === 'number' && this.precision !== undefined) {
                newVal = this.toPrecision(newVal, this.precision);
            }
            if (newVal >= this.max) {
                newVal = this.max;
            }
            if (newVal <= this.min) {
                newVal = this.min;
            }
            if (oldVal === newVal) {
                this.currentValue = newVal;
                return;
            }
            this.$emit('input', newVal);
            this.$emit('change', newVal, oldVal);
            this.currentValue = newVal;
        },
        handleInputChange(value) {
            const newVal = value === '' ? undefined : Number(value);
            if (!Number.isNaN(newVal) || value === '') {
                this.setCurrentValue(newVal);
            }
        },
        handleInput(value) {
            let newVal = value === '' ? undefined : Number(value);
            if (typeof newVal === 'number' && this.precision !== undefined) {
                const m = 10 ** this.precision;
                newVal = Math.floor(newVal * m) / m;
            }
            if (!Number.isNaN(newVal) || value === '') {
                this.setCurrentValue(newVal);
            }
        },
    },
    mounted() {
        const innerInput = this.$refs.input.$refs.input;
        innerInput.setAttribute('role', 'spinbutton');
        innerInput.setAttribute('aria-valuemax', this.max);
        innerInput.setAttribute('aria-valuemin', this.min);
        innerInput.setAttribute('aria-valuenow', this.currentValue);
        innerInput.setAttribute('aria-disabled', this.inputNumberDisabled);
    },
    updated() {
        if (!this.$refs || !this.$refs.input) {
            return;
        }
        const innerInput = this.$refs.input.$refs.input;
        innerInput.setAttribute('aria-valuenow', this.currentValue);
    },
};
</script>

<style lang="less">
    input[type="number"] {
        -moz-appearance:textfield;
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        margin: 0;
    }
    .el-input-number .el-input__inner {
        -webkit-appearance: textfield;
    }
</style>
