<template>
    <div class="org-selector">
        <div
            v-if="!editable"
        >
            <el-tooltip
                :content="value.orgTierName"
                v-if="value.orgTierName"
            >
                <span class="org-selector__content">{{ value.orgTierName | orgNameFilter }}</span>
            </el-tooltip>
            <span
                v-else
                class="org-selector__content-non"
            >
                暂无
            </span>
        </div>
        <el-select
            ref="selectIns"
            class="org-selector__select"
            v-model="selectLabelValue"
            :disabled="disabled"
            v-else
        >
            <el-option
                class="org-selector__select__option"
                :value="selectLabelValue"
            >
                <el-tree
                    ref="treeIns"
                    class="org-selector__select__tree"
                    :data="data"
                    :props="defaultProps"
                    :check-strictly="true"
                    node-key="id"
                    :highlight-current="true"
                    empty-text="没有部门数据"
                    :default-expanded-keys="defaultExpandedKeys"
                    :load="loadData"
                    @node-click="handleNodeClick"
                    lazy
                />
            </el-option>
            <div class="org-selector__select__buttons">
                <jacp-button @click="cancel">
                    取消
                </jacp-button>
                <jacp-button
                    type="primary"
                    @click="confirm"
                >
                    确定
                </jacp-button>
            </div>
        </el-select>
    </div>
</template>

<script type="text/javascript">
import OrgModel from '@/models/org';
import FormValidate from '@/mixins/mixin.form.validate';

export default {
    name: 'JacpOrgsSelectorTree',
    mixins: [FormValidate],
    props: {
        value: {
            type: Object,
            default: () => ({ }),
        },
        data: {
            type: Array,
            default: () => ([]),
        },
        level: {
            type: Number,
            default: -1,
        },
        editable: {
            type: Boolean,
            default: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            innerValue: { orgTierCode: '', orgTierName: '' },
            orgMap: {},
            defaultProps: {
                label: 'name',
                children: 'children',
            },
            defaultExpandedKeys: [],
            selectLabelValue: '',
        };
    },
    watch: {
        data: {
            handler() {
                if (this.value && this.value.orgTierCode) {
                    this.setNodeHighlight(this.value.orgTierCode.split('/').pop());
                }
            },
            immediate: true,
        },
        'value.orgTierName': {
            handler() {
                if (this.value && this.value.orgTierName) {
                    this.selectLabelValue = this.value.orgTierName.split('-').pop();
                }
            },
            immediate: true,
        },
    },
    filters: {
        orgNameFilter(orgTierName) {
            if (orgTierName) {
                return orgTierName.split('-').pop();
            }
            return '暂无';
        },
    },
    methods: {
        handleNodeClick(org) {
            const orgTierCode = `/${this.getFullPath('id', org.id).join('/')}`;
            const orgTierName = this.getFullPath('name', org.id).join('-');
            Object.assign(this.innerValue, { orgTierCode, orgTierName });
        },
        loadData(node, resolve) {
            const org = node.data;
            const data = [];
            if (org && org.id) {
                OrgModel.loadChildOrgs(org.id).then((orgs) => {
                    orgs.forEach((item) => {
                        data.push({
                            id: item.id,
                            name: item.name,
                            children: [],
                        });
                        this.orgMap[item.id] = Object.assign({}, item, { $_parentId: org.id });
                    });
                    resolve(data);
                });
            } else {
                OrgModel.getDeptByLevel({ level: this.level }).then((orgs) => {
                    const item = {
                        children: [],
                        id: orgs.id,
                        name: orgs.name,
                    };
                    data.push(item);
                    this.orgMap[item.id] = item;
                    resolve(data);
                });
            }
            resolve(data);
        },
        cancel() {
            this.$refs.selectIns.visible = false;
            this.innerValue = { ...this.value };
            if (this.innerValue.orgTierCode) {
                this.selectLabelValue = this.innerValue.orgTierName.split('-').pop();
                this.setNodeHighlight(this.innerValue.orgTierCode.split('/').pop());
            } else {
                this.selectLabelValue = '';
                this.setNodeHighlight(undefined);
            }
            this.$_triggerValidateChangeEvent();
        },
        confirm() {
            this.$refs.selectIns.visible = false;
            if (this.innerValue.orgTierCode === this.value.orgTierCode) {
                this.$_triggerValidateChangeEvent();
                return;
            }
            this.selectLabelValue = this.innerValue.orgTierName.split('-').pop();
            this.$emit('input', { ...this.innerValue });
            this.$emit('change', { ...this.innerValue });
            this.$_triggerValidateChangeEvent();
        },
        setNodeHighlight(key) {
            const { treeIns } = this.$refs;
            if (treeIns) {
                this.$nextTick(() => {
                    this.$refs.treeIns.setCurrentKey(key);
                });
            }
        },
        getFullPath(paramKey, orgCode) {
            const res = [];
            if (!orgCode || !this.orgMap[orgCode]) {
                return res;
            }
            res.unshift(this.orgMap[orgCode][paramKey]);

            const tempOrgMap = this.orgMap;
            (function eachParent(code) {
                const item = tempOrgMap[code];
                if (item.$_parentId) {
                    res.unshift(tempOrgMap[item.$_parentId][paramKey]);
                    eachParent(item.$_parentId);
                }
            }(orgCode));
            return res;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.org-selector{
    &__content {
        font-size: 12px;
        color: @fontColor;
    }
    &__content-non {
        font-size: 12px;
        color: @fontThirdColor;
    }
    &__select{
        width: 100%;
        &__option {
            height: auto;
            padding: 0;
        }
        &__tree {
            max-height: 196px;
            min-height: 32px;
            overflow-x: hidden;
            overflow-y: auto;
        }
        &__buttons{
            text-align: right;
            padding: 10px 10px 5px;
            border-top: @borderStyle;
        }
    }
}
</style>
