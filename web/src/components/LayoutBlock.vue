<template>
    <div
        class="layout-block-root"
        :class="[{
            'layout-block-root__fullscreen': fullScreenState,
        }]"
    >
        <div
            class="layout-block-title"
            v-if="title !== undefined"
        >
            <span class="layout-block-title__text">{{ title }}</span>
            <el-tooltip
                v-if="tipContent"
                class="item layout-block-title__tooltip"
                effect="dark"
                :content="tipContent"
                placement="bottom"
            >
                <i
                    style="font-size: 14px;"
                    class="jacp-icon-help"
                />
            </el-tooltip>
            <span
                v-if="subTitle"
                class="layout-block-title__subtext"
            >{{ subTitle }}</span>
            <div class="layout-block-title__extra">
                <slot name="extra" />
            </div>
            <!-- <i
                class="layout-block-title__fullscreen-icon"
                :title="fullScreenState ? '退出全屏' : '全屏模式'"
                :class="[fullScreenState ? 'jacp-icon-fullscreen-exit' : 'jacp-icon-fullscreen']"
                v-show="!parentFullScreenState && !displayFullscreen"
                @click="toggleFullscreen()"
            /> -->
        </div>
        <div :style="{minHeight: `${minHeight}vh`, height: `${innerHeight}vh`}">
            <slot />
        </div>
    </div>
</template>
<script>
import fscreen from 'fscreen';

export default {
    name: 'LayoutBlock',
    props: {
        title: String,
        subTitle: String,
        tipContent: String,
        minHeight: [Number, String],
        height: [Number, String],
        displayFullscreen: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            fullScreenState: false,
            parentFullScreenState: false,
            innerHeight: 0,
        };
    },
    created() {
        this.innerHeight = this.height;
        fscreen.addEventListener('fullscreenchange', this.setFullscreenState, false);
    },
    beforeDestroy() {
        fscreen.removeEventListener('fullscreenerror', this.setFullscreenState);
    },
    methods: {
        setFullscreenState() {
            this.fullScreenState = fscreen.fullscreenElement
                && fscreen.fullscreenElement.fullScreenComponent === this.$el;
            this.parentFullScreenState = !!fscreen.fullscreenElement && !this.fullScreenState;
            this.innerHeight = this.fullScreenState ? 85 : this.height;
        },
        toggleFullscreen() {
            if (!this.fullScreenState) {
                document.body.fullScreenComponent = this.$el;
                fscreen.requestFullscreen(document.body);
            } else {
                fscreen.exitFullscreen();
                document.body.fullScreenComponent = undefined;
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.layout-block-root{
    background-color: #fff;
    font-size: 12px;
    padding: 24px;
    overflow: auto;

    &:hover{
        & .layout-block-title__fullscreen-icon{
            display: block;
        }
    }

    &__fullscreen{
        position: fixed;
        top: 0;
        left: 0;
        z-index: 100;
        width: 100vw;
        height: 100vh;
    }

    .layout-block-title{
        margin-bottom: 2vh;
        &__extra{
            font-size: 12px;
            cursor: pointer;
        }
        &__text{
            font-size: 16px;
            color: @fontColor;
            letter-spacing: 0;
        }
        &__tooltip{
            color: @fontThirdColor;
            font-size: 16px;
        }
        &__subtext{
            margin-left: 10px;
            color: @fontThirdColor;
        }
        &__fullscreen-icon {
            display: none;
            float: right;
            cursor: pointer;
            color: @secendColor;
            position: relative;
            right: -8px;
            top: -8px;
        }
    }
}
</style>
