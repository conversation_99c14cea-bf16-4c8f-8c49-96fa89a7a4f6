<template>
    <el-dropdown
        class="org-selector"
        trigger="click"
        ref="dropdownIns"
        placement="bottom-start"
        :disabled="disabled"
    >
        <span class="org-selector__value el-dropdown-link">
            <slot :data="orgMap[value]">
                <div class="org-selector__value__outer">
                    <div class="org-selector__value__parentsname">{{ valueName.parents }}</div>
                    <div class="org-selector__value__name">{{ valueName.current }}</div>
                    <div v-show="!valueName.current">请选择</div>
                </div>
            </slot>
            <i class="el-icon-caret-bottom el-icon--right" v-if="!disabled"/>
        </span>
        <el-dropdown-menu
            slot="dropdown"
            class="org-selector__dropdown"
        >
            <el-tree
                class="org-selector__dropdown__tree"
                ref="treeIns"
                :props="defaultProps"
                :check-strictly="true"
                node-key="id"
                :highlight-current="true"
                empty-text="没有部门数据"
                :load="loadData"
                @node-click="handleNodeClick"
                lazy
            />
            <!-- :data="data" -->
            <div class="org-selector__dropdown__buttons">
                <jacp-button @click="cancel">
                    取消
                </jacp-button>
                <jacp-button
                    type="primary"
                    @click="confirm"
                >
                    确定
                </jacp-button>
            </div>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script type="text/javascript">
import OrgModel from '@/models/org';

function getParentsName(org) {
    const res = [];
    (function eachParent(item) {
        if (item.$_parent) {
            res.unshift(item.$_parent.name);
            eachParent(item.$_parent);
        }
    }(org));
    return res;
}

export default {
    name: 'JacpOrgsSelectorTreeAbs',
    props: {
        value: {
            type: String,
        },
        data: {
            // 在原本的demandsfilter里就一直没用到.但如果传入的话似乎也有做处理
            type: Array,
            default: () => ([]),
        },
        orgLevel: {
            // 对应部门级别。和rootOrgId需要存在一个优先级关系。
            type: Number,
            default: 0,
        },
        editable: {
            type: Boolean,
            default: true,
        },
        erp: {
            type: String,
            default: '',
        },
        rootOrgId: {
            type: String,
            default: '',
        },
        rootOrgName: {
            type: String,
            default: '',
        },
        defaultOrgId: {
            type: String,
            default: '',
        },
        defaultOrgName: {
            type: String,
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            innerValue: this.value,
            orgMap: {},
            defaultProps: {
                label: 'name',
                children: 'children',
            },
        };
    },
    watch: {
        // data: {
        //     handler(n) {
        //         this.orgMap = Object.assign({}, this.orgMap, createIndex(n));
        //         this.setNodeHighlight(this.value);
        //         const item = {
        //             id: store.state.user.orgCode,
        //             name: store.state.user.orgName,
        //             children: [],
        //         };
        //         this.orgMap[item.id] = item;
        //         if (item.id !== this.value) {
        //             this.getCurrentValueNode(this.value);
        //         }
        //     },
        //     immediate: true,
        // },
        value: {
            immediate: true,
            handler(n) {
                // value被emit的input事件携带的当前orgId修改。
                // 用于保存orgId
                this.innerValue = n;
                this.getCurrentValueNode(this.value);
                this.setNodeHighlight(this.value);
            },
        },
        defaultOrgId: {
            immediate: true,
            handler() {
                if (this.defaultOrgId) {
                    this.$emit('input', this.defaultOrgId);
                }
            },
        },
    },
    methods: {
        handleNodeClick(org) {
            this.innerValue = org.id;
            this.$emit('select', org);
        },
        loadData(node, resolve) {
            const org = node.data;
            const data = [];
            if (org && org.id) {
                OrgModel.loadChildOrgs(org.id).then((orgs) => {
                    orgs.forEach((item) => {
                        data.push({
                            id: item.id,
                            name: item.name,
                            children: [],
                        });
                        this.orgMap[item.id] = item;
                    });
                    resolve(data);
                });
            } else if (this.rootOrgId) {
                // 当prop有rootOrgId时采用它
                OrgModel.getDeptByOrgCode(this.rootOrgId).then((orgs) => {
                    const item = {
                        children: [],
                        id: orgs.id,
                        name: orgs.name,
                        level: orgs.level,
                    };
                    data.push(item);
                    this.orgMap[item.id] = item;
                    resolve(data);
                });
            } else {
                // 没有rootOrgId时采用orgLevel，这是原来的逻辑。
                // 京东集团level=0，体系level=1，这里的值是大于关系。
                OrgModel.getDeptByLevel({ level: this.orgLevel }).then((orgs) => {
                    const item = {
                        children: [],
                        id: orgs.id,
                        name: orgs.name,
                        level: orgs.level,
                    };
                    data.push(item);
                    this.orgMap[item.id] = item;
                    resolve(data);
                });
            }
            // resolve(data);
        },
        cancel() {
            this.$refs.dropdownIns.hide();
            this.innerValue = this.value;
            this.setNodeHighlight(this.value);
        },
        confirm() {
            this.$refs.dropdownIns.hide();
            this.$emit('input', this.innerValue);
            this.$emit('change', this.orgMap[this.innerValue]);
        },
        setNodeHighlight(key) {
            this.$nextTick(() => {
                // 非官方暴露的接口，后期升级elements可能会带来一些问题
                this.$refs.treeIns.store.setCurrentNodeKey(key);
            });
        },
        getCurrentValueNode(key) {
            if (key === undefined || key === '') {
                return;
            }
            const org = this.orgMap[key];
            // 根据value获取该org，如果orgMap里有则获取到，return;
            if (org) {
                return;
            }
            // 如果Map里没有的话，需要重新请求，并添加到orgMap，同时调用getNodeTreeData。
            OrgModel.getDeptByOrgCode(key).then((orgs) => {
                this.orgMap = Object.assign({}, this.orgMap, { [orgs.id]: orgs });
            });
        },
    },
    computed: {
        valueName() {
            const org = this.orgMap[this.value];
            return org ? {
                parents: getParentsName(org).join('-'),
                current: org.name,
            } : {};
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.org-selector{
    &__value{
        cursor: pointer;
        display: flex;
        align-items: center;
        text-align: right;

        &__parentsname{
            color: #666;
            font-size: 12px;
        }
        &__name{
            line-height: 1.6;
        }
        &__outer{
            margin-right: 10px;
        }
    }
    &__dropdown{
        overflow: auto;
        padding: 5px 0;
        min-width: 220px;

        & .el-tree{
            border: none;
        }
        & .el-tree-node__content{
            padding-right: 10px;
        }
        &__tree{
            overflow: auto;
            max-height: 70vh;
            padding: 0 0 5px 0;
        }
        &__buttons{
            text-align: right;
            padding: 10px 10px 5px;
            border-top: @borderStyle;
        }
    }
}
</style>
