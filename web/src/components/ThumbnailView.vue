<template>
    <div class="jacp-thumbnail-view">
        <template v-if="hasPreview">
            <div
                class="img_item_box"
                v-for="(item, index) in picAttchList"
                :key="item.id"
                @click="openPicViewer(item)"
            >
                <img
                    class="img_item"
                    :src="item.url"
                    :indexNum="index"
                >
                <p>{{ item.name }}</p>
            </div>
        </template>
        <transition name="fade">
            <div
                class="jacp-showbox"
                @click.stop="closePicViewer"
                v-show="isShowThumbnail"
            >
                <span
                    class="jacp-showbox__pre"
                    @click.stop="showPrePic"
                >
                    <i class="el-icon-arrow-left" />
                </span>
                <span
                    class="jacp-showbox__next"
                    @click.stop="showNextPic"
                >
                    <i class="el-icon-arrow-right" />
                </span>
                <img
                    :src="picAttchList[indexNum].url"
                    v-if="picAttchList[indexNum]"
                >
            </div>
        </transition>
    </div>
</template>

<script type="text/javascript">
import findIndex from 'lodash/findIndex';

export default {
    name: 'JacpThumbnailView',
    props: {
        // 附件列表
        attachments: {
            type: Array,
            default: () => [],
        },
        hasPreview: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            validPicType: ['.jpg', '.png', '.bmp', '.gif'], // 有效图片类型,
            indexNum: 0, // 图片预览下标
            isShowThumbnail: false,
        };
    },
    methods: {
        // 打开图片预览界面
        openPicViewer(item = {}) {
            const index = findIndex(this.picAttchList, o => o.id === item.id);
            if (index === -1) {
                return;
            }
            this.indexNum = index;
            this.$nextTick(() => {
                this.isShowThumbnail = true;
            });
        },
        // 关闭图片预览界面
        closePicViewer(event) {
            const { target } = event;
            if (target.nodeName !== 'SPAN') {
                // 清空当前图片
                this.isShowThumbnail = false;
            }
        },
        // 上一个
        showPrePic() {
            this.indexNum = this.indexNum > 0 ? this.indexNum - 1 : this.picAttchList.length - 1;
        },
        // 下一个
        showNextPic() {
            this.indexNum = this.indexNum < this.picAttchList.length - 1 ? this.indexNum + 1 : 0;
        },
    },
    computed: {
        // 过滤图片类型数据
        picAttchList() {
            return this.attachments.filter((item) => {
                const fileType = item.name.substr(item.name.lastIndexOf('.'));
                return this.validPicType.includes(fileType);
            });
        },
    },
};
</script>

<style lang="less">
.jacp-thumbnail-view{
    background-color: gray;
    & .jacp-showbox{
        position: fixed;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.8);
        z-index: 1000;
        left:0;
        top:0;
        & img{
            position: absolute;
            left:50%;
            top:50%;
            transform: translate(-50%, -50%);
            border-radius: 5px;
            max-width:90%;
            max-height: 98%;
        }
        & span{
            color: white;
            position: absolute;
            width: 40px;
            height: 100px;
            font-size: 32px;
            top: 40%;
            font-family: '宋体';
            display: inline-block;
            background-color: rgba(0, 0, 0, .1);
            line-height: 100px;
            text-align: center;
            font-weight: 900;
            cursor: pointer;
            z-index: 10;
            &:hover{
                background-color: rgba(0, 0, 0, .5);
            }
        }
        &__pre{
            left: 20%;
            border-radius: 50% 0 0 50%;
        }
        &__next{
            right: 20%;
            border-radius: 0 50% 50% 0;
        }
    }
    & .img_item_box{
        float: left;
        position: relative;
        font-size: 0;
        & img{
            width: 120px;
            height: 100px;
            margin: 5px;
            border-radius: 5px;
        }
        & p{
            width: 120px;
            margin: 0 auto;
            text-align: center;
            font-size: 12px;
            font-size: 12px;
            height: 15px;
            line-height: 14px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow-x: hidden;
        }
    }
    & .clearfloat{
        zoom: 1;
        &:after{
            display: block;
            clear: both;
            content: "";
            visibility: hidden;
            height: 0;
        }
    }
}
</style>
