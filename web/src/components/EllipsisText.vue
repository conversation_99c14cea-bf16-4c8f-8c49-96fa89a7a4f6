<template>
    <el-tooltip
        :disabled="isShowTooltip"
        :content="content"
        placement="top"
    >
        <slot slot="content">{{ content }}</slot>
        <span
            class="ellipsis-text"
            ref="text"
            @mouseover="onMouseOver"
            :style="{ 'max-width': `${maxWidth}px` }"
        ><slot>{{ content }}</slot></span>
    </el-tooltip>
</template>

<script>
export default {
    name: 'EllipsisText',
    props: {
        content: {
            type: String,
            default: '',
        },
        maxWidth: {
            type: [Number, String],
            default: 68,
        },
    },
    data() {
        return {
            isShowTooltip: true,
        };
    },
    methods: {
        onMouseOver() {
            const contentWidth = this.$refs.text.offsetWidth;
            if (contentWidth >= +this.maxWidth) {
                this.isShowTooltip = false;
            } else {
                this.isShowTooltip = true;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.ellipsis-text {
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
</style>
