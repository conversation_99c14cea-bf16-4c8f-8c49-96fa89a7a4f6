<template>
    <span ref="number">{{ start }}</span>
</template>

<script>
export default {
    name: 'NumberGrow',
    props: {
        value: {
            type: Number,
            default: 0,
        },
        start: {
            type: Number,
            default: 0,
        },
        time: {
            type: Number,
            default: 1,
        },
    },
    mounted() {
        this.numberGrow(this.$refs.number);
    },
    methods: {
        numberGrow(ele) {
            clearInterval(this.interval);
            if (!ele) {
                return;
            }
            const eleCopy = ele;
            let step = Math.floor((this.value * 10) / (this.time * 1000));
            if (step < 1) {
                step = 1;
            }
            let current = -1;
            let startTemp = this.start;
            this.interval = setInterval(() => {
                startTemp += step;
                if (startTemp > this.value) {
                    clearInterval(this.interval);
                    startTemp = this.value;
                    this.interval = null;
                }
                if (current === startTemp) {
                    clearInterval(this.interval);
                    return;
                }
                current = startTemp;
                eleCopy.innerHTML = this.formatNumber(current);
            }, 10);
        },
        formatNumber(num) {
            const x = num.toString().split('.');
            const x1 = x[0].replace(/([\d\\.])(?=(?:[\d]{3}[+]?)+$)/g, '$1,');
            let x2 = '';
            if (x.length === 2) {
                x2 = `.${x[1]}`;
            }
            return x1 + x2;
        },
    },
    watch: {
        value: {
            immediate: true,
            handler: function refresh() {
                this.numberGrow(this.$refs.number);
            },
        },
    },
};
</script>
