<template>
    <div class="url-link">
        <ul v-if="value.length > 0">
            <li
                v-for="(item, index) in value"
                :key="item[option.url]"
                :class="{'editable': enableEdit}"
            >
                <a
                    :href="item[option.path]"
                    target="_blank"
                >{{ item[option.name] }}</a>
                <i
                    v-show="!item.readOnly"
                    class="el-icon-circle-check"
                />
                <i
                    v-show="enableEdit && !item.readOnly"
                    class="el-icon-close"
                    @click="doDelete(index)"
                />
            </li>
        </ul>
        <div v-if="value.length == 0 && !enableEdit">
            暂无链接
        </div>
        <div
            class="url-link__add"
            v-show="showAdd"
        >
            <el-form
                label-width="80px"
                :model="formData"
                :inline="true"
                ref="linkInfo"
                :rules="rules"
            >
                <el-form-item
                    label="链接名称"
                    prop="name"
                >
                    <el-input v-model="formData.name" />
                </el-form-item>
                <el-form-item
                    label="链接地址"
                    prop="path"
                >
                    <el-input v-model="formData.path" />
                </el-form-item>
            </el-form>
            <div class="url-link__nodata">
                <a @click="doSave">确定</a><a @click="showAdd = false">取消</a>
            </div>
        </div>
        <div v-show="!showAdd && enableEdit">
            <a @click="showAdd = true">+添加链接 </a>
        </div>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'UrlLink',
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        keys: {
            type: Object,
            default: () => ({}),
        },
        addLinks: {
            type: Function,
        },
        deleteLinks: {
            type: Function,
        },
        enableEdit: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            showAdd: false,
            option: {
                id: this.keys.id || 'id', // 链接地址
                path: this.keys.path || 'path', // 链接地址
                name: this.keys.name || 'name', // 链接描述
            },
            formData: {
                path: '',
                name: '',
            },
            rules: {
                name: [{
                    required: true,
                    message: '链接名称不能为空',
                    trigger: 'blur',
                }],
                path: [{
                    required: true,
                    message: '链接地址不能为空',
                    trigger: 'blur',
                }],
            },
        };
    },
    methods: {
        doSave() {
            this.$refs.linkInfo.validate().then((valid) => {
                if (valid) {
                    const linkInto = {};
                    linkInto[this.keys.path || 'path'] = this.formData.path;
                    linkInto[this.keys.name || 'name'] = this.formData.name;
                    this.$refs.linkInfo.resetFields();
                    // 实时保存时调用对应添加的函数逻辑
                    if (this.addLinks) {
                        this.addLinks(linkInto).then((data) => {
                            this.value.push(data);
                        });
                    } else {
                        this.value.push(linkInto);
                    }
                } else {
                    throw new Error(valid);
                }
            });
        },
        doDelete(index) {
            this.$confirm(`确定移除链接「${this.value[index][this.keys.name || 'name']}」？`).then(() => {
                const linkId = this.value[index][this.keys.id || 'id'];
                // 实时保存时调用对应删除的函数逻辑
                if (this.deleteLinks) {
                    this.deleteLinks(linkId).then(() => {
                        this.value.splice(index, 1);
                    });
                } else {
                    this.value.splice(index, 1);
                }
            });
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.url-link{
    font-size: 12px;
    & ul{
        margin-top: 8px;
        list-style: none;
        font-size: 12px;
        padding: 0;
        font-size: 0;
        & li{
            font-size: 12px;
            height: 25px;
            line-height: 25px;
            padding-left: 5px;
            border-radius: 2px;
            &:hover{
                background-color: #f5f7fa;
            }
            & i.el-icon-circle-check{
                font-size: 14px;
                color: #67c23a;
                float: right;
                margin-right: 5px;
                margin-top: 5px;
            }
            & i.el-icon-close{
                display: none;
                font-size: 14px;
                float: right;
                margin-right: 5px;
                margin-top: 5px;
            }
            &.editable{
                &:hover{
                    & i.el-icon-circle-check{
                        display: none;
                    }
                    & i.el-icon-close{
                        display: block;
                    }
                }
            }
        }
    }
    &__add{
        margin-top: 5px;
        background-color: #f5f7fa;
        padding-top: 5px;
        & a{
            margin: 0 10px;
        }
    }
    &__form{
        display: flex;
        padding: 5px;
        & span{
            width: 100px;
            padding: 0 5px;
        }
    }
    &__name{
        color: @fadeFontColor;
        width: 200px;
    }
    & .hide {
        display: none;
    }
    & a{
        color: @primaryColor;
        cursor: pointer;
    }
    &__nodata{
        color: @remarkColor;
    }
}
</style>
