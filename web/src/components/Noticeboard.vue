<template>
    <div
        class="noticeboard__wrapper"
        :aria-label="label"
        :aria-valuetext="value"
    >
        <label
            :class="['noticeboard__label', {
                required,
            }]"
            :style="{
                fontSize: `${labelSize}px`,
            }"
        >{{ label }}
            <slot name="labelIcon" />
        </label>
        <span class="noticeboard__value">
            <span
                :style="{
                    fontSize: `${size}px`,
                }"
            >{{ value || noDataText }}</span>
            <slot name="unit" />
        </span>
    </div>
</template>
<script>

/* 弱化标题、突出数字的控件 */
export default {
    name: 'JacpNoticeboard',
    props: {
        label: String,
        labelSize: {
            type: [String, Number],
            default: '12',
        },
        value: [String, Number],
        // separator: Boolean,
        size: {
            type: [String, Number],
            default: '20',
        },
        noDataText: {
            type: String,
            default: '无数据',
        },
        required: Boolean,
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.noticeboard{
    &__wrapper{
        display: inline-flex;
        flex-direction: column;
        width: fit-content;
        margin-right: 8px;
        align-items: flex-start;
        height: 46px;
    }
    &__label{
        color: @remarkColor;
        white-space: nowrap;
        font-size: 12px;
        margin-bottom: 8px;
        line-height: 1.15;
        &.required{
            &::after{
                content: '*';
                color: @requiredColor;
                margin-left: 4px;
            }
        }
    }
    &__value{
        color: @fontColor;
        margin-right: 8px;
        font-weight: 400;
        line-height: 1.5;
    }
}
</style>
