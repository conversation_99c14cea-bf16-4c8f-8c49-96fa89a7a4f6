<template>
    <el-menu-item
        :index="menuItem.id"
        :disabled="menuItem.disabled"
        v-if="visible"
    >
        <el-submenu
            :index="menuItem.id"
            v-if="menuItem.hasChildren"
        >
            <template slot="title">
                <span
                    slot="title"
                ><slot
                    name="title"
                    :menuItem="menuItem"
                >{{ menuItem.name }}</slot></span>
            </template>
            <JacpNestedMenuItem
                v-for="menuItemChild in menuItem.children"
                :menu-item="menuItemChild"
                :key="menuItemChild.id"
                :scope="scope"
            >
                <template #title="{menuItem: scopedMenuItem}">
                    <slot
                        name="title"
                        :menuItem="scopedMenuItem"
                    >
                        {{ scopedMenuItem.name }}
                    </slot>
                </template>
            </JacpNestedMenuItem>
        </el-submenu>
        <span
            slot="title"
            v-else
        ><slot
            name="title"
            :menuItem="menuItem"
        >{{ menuItem.name }}</slot></span>
    </el-menu-item>
</template>

<script>
import {
    MenuItem,
} from '@jacpbiz/menu-business';

export default {
    name: 'JacpNestedMenuItem',
    props: {
        menuItem: {
            type: MenuItem,
            required: true,
        },
        scope: { type: String },
    },
    computed: {
        visible() {
            const { scopes = [] } = this.menuItem;
            return !(this.scope && scopes.length && !scopes.includes(this.scope));
        },
    },
};
</script>
