<template>
    <el-dropdown
        class="org-selector"
        trigger="click"
        ref="dropdownIns"
    >
        <span class="org-selector__value el-dropdown-link">
            <slot :data="orgMap[value]">
                <div class="org-selector__value__outer">
                    <div class="org-selector__value__parentsname">{{ valueName.parents }}</div>
                    <div class="org-selector__value__name">{{ valueName.current }}</div>
                    <div v-show="!valueName.current">请选择</div>
                </div>
            </slot>
            <i class="el-icon-caret-bottom el-icon--right" />
        </span>
        <el-dropdown-menu
            slot="dropdown"
            class="org-selector__dropdown"
        >
            <el-tree
                class="org-selector__dropdown__tree"
                ref="treeIns"
                :data="data"
                :props="defaultProps"
                :check-strictly="true"
                node-key="id"
                :highlight-current="true"
                empty-text="没有部门数据"
                :default-expanded-keys="defaultExpandedKeys"
                :load="loadData"
                @node-click="handleNodeClick"
                lazy
            />
            <div class="org-selector__dropdown__buttons">
                <jacp-button @click="cancel">
                    取消
                </jacp-button>
                <jacp-button
                    type="primary"
                    @click="confirm"
                >
                    确定
                </jacp-button>
            </div>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script type="text/javascript">
import OrgModel from '@/models/org';
import store from '$platform.store';

function createIndex(data = []) {
    const res = {};
    function eachCurrent(parent) {
        (parent.children || []).forEach((item) => {
            if (!res[item.id]) {
                res[item.id] = Object.assign(item, {
                    $_parent: parent,
                });
            }
            eachCurrent(item);
        });
    }
    data.forEach((item) => {
        res[item.id] = item;
        eachCurrent(item);
    });
    return res;
}
function getParentsName(org) {
    const res = [];
    (function eachParent(item) {
        if (item.$_parent) {
            res.unshift(item.$_parent.name);
            eachParent(item.$_parent);
        }
    }(org));
    return res;
}

export default {
    name: 'JacpOrgsSelector',
    props: {
        value: {
            type: String,
        },
        data: {
            type: Array,
            default: () => ([]),
        },
        level: {
            type: Number,
            default: 0,
        },
        erp: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            innerValue: this.value,
            orgMap: {},
            defaultProps: {
                label: 'name',
                children: 'children',
            },
            defaultExpandedKeys: [],
        };
    },
    watch: {
        data: {
            handler(n) {
                this.orgMap = Object.assign({}, this.orgMap, createIndex(n));
                this.setNodeHighlight(this.value);
                const item = {
                    id: store.state.user.orgCode,
                    name: store.state.user.orgName,
                    children: [],
                };
                this.orgMap[item.id] = item;
                if (item.id !== this.value) {
                    this.getCurrentValueNode(this.value);
                }
            },
            immediate: true,
        },
        value(n) {
            this.innerValue = n;
            this.getCurrentValueNode(this.value);
            this.setNodeHighlight(this.value);
        },
    },
    methods: {
        handleNodeClick(org) {
            this.innerValue = org.id;
            this.$emit('select', org);
        },
        loadData(node, resolve) {
            const org = node.data;
            const data = [];
            if (org && org.id) {
                OrgModel.loadChildOrgs(org.id).then((orgs) => {
                    orgs.forEach((item) => {
                        data.push({
                            id: item.id,
                            name: item.name,
                            children: [],
                        });
                        this.orgMap[item.id] = item;
                    });
                    resolve(data);
                });
            } else {
                OrgModel.getDeptByLevel({ level: this.level }).then((orgs) => {
                    const item = {
                        children: [],
                        id: orgs.id,
                        name: orgs.name,
                        level: orgs.level,
                    };
                    data.push(item);
                    this.orgMap[item.id] = item;
                    resolve(data);
                });
            }
            resolve(data);
        },
        getNodeTreeData() {
            OrgModel.getDeptByLevel({ level: this.level, erp: this.erp }).then((orgs) => {
                const item = {
                    children: [],
                    id: orgs.id,
                    name: orgs.name,
                };
                if (this.orgMap[item.id] === undefined) {
                    this.orgMap[item.id] = item;
                    this.data.splice(0, this.data.length, item);
                }
            });
        },
        cancel() {
            this.$refs.dropdownIns.hide();
            this.innerValue = this.value;
            this.setNodeHighlight(this.value);
        },
        confirm() {
            this.$refs.dropdownIns.hide();
            this.$emit('input', this.innerValue);
            this.$emit('change', this.orgMap[this.innerValue]);
        },
        setNodeHighlight(key) {
            this.$nextTick(() => {
                // 非官方暴露的接口，后期升级elements可能会带来一些问题
                this.$refs.treeIns.store.setCurrentNodeKey(key);
            });
        },
        getCurrentValueNode(key) {
            if (key === undefined || key === '') {
                return;
            }
            const org = this.orgMap[key];
            if (org) {
                return;
            }
            OrgModel.getDeptByOrgCode(key).then((orgs) => {
                this.orgMap = Object.assign({}, this.orgMap, { [orgs.id]: orgs });
            });
            this.getNodeTreeData();
        },
    },
    computed: {
        valueName() {
            const org = this.orgMap[this.value];
            return org ? {
                parents: getParentsName(org).join('-'),
                current: org.name,
            } : {};
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.org-selector{
    &__value{
        cursor: pointer;
        display: flex;
        align-items: center;
        text-align: right;

        &__parentsname{
            color: #666;
            font-size: 12px;
        }
        &__name{
            line-height: 1.6;
        }
        &__outer{
            margin-right: 10px;
        }
    }
    &__dropdown{
        overflow: auto;
        padding: 5px 0;
        min-width: 220px;

        & .el-tree{
            border: none;
        }
        & .el-tree-node__content{
            padding-right: 10px;
        }
        &__tree{
            overflow: auto;
            max-height: 70vh;
            padding: 0 0 5px 0;
        }
        &__buttons{
            text-align: right;
            padding: 10px 10px 5px;
            border-top: @borderStyle;
        }
    }
}
</style>
