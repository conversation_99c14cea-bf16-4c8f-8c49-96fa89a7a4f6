<template>
    <el-select
        class="jacp-input-orgs"
        style="width: 100%"
        ref="selectDom"
        :disabled="disabled"
        v-model="reqDeptList"
        value-key="id"
        filterable
        :multiple="multiple"
        :multiple-limit="maxCount"
        :clearable="clearable"
        remote
        :placeholder="placeholder"
        :remote-method="loadOrglist"
        @focus="clearReqList"
        @remove-tag="clearReqList"
        v-on="inputListeners"
    >
        <el-option
            v-for="item in reqList"
            :key="item.id"
            :label="item.fullName"
            :value="item"
        />
    </el-select>
</template>

<script>
// import AdminOption from '@/models/adminOption';
import Demand from '@/models/demands';
import { promisefy } from '@/plugins/utils';

export default {
    name: 'JacpInputOrgs',
    props: {
        value: {
            // type: Array,
            // default: () => [],
            type: Array,
            default: () => [],
        },
        maxCount: {
            type: Number,
            default: Infinity,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: <PERSON>olean,
            default: false,
        },
        loadOrgs: {
            type: Function,
            // default: AdminOption.getOrgList,
            default: Demand.formatOrgList,
        },
        placeholder: {
            type: String,
            default: '请输入搜索关键词',
        },
        clearable: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            reqDeptList: [],
            reqList: [],
            loadOrgsFn: promisefy(this.loadOrgs).bind(this),
        };
    },
    methods: {
        clearReqList() {
            this.reqList = [];
        },
        loadOrglist(orgInfo) {
            this.clearReqList();
            if (orgInfo) {
                this.loadOrgsFn({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    this.reqList = data;
                });
            } else {
                // 清空原部门信息
                this.clearReqList();
            }
        },
    },
    computed: {
        inputListeners() {
            const vm = this;
            return Object.assign({},
                this.$listeners,
                {
                    change(event) {
                        if (typeof event === 'string') {
                            vm.$emit('input', []);
                            return;
                        }
                        if (Array.isArray(event)) {
                            vm.$emit('input', event);
                        } else {
                            vm.clearReqList();
                            vm.$emit('input', [event]);
                        }
                    },
                });
        },
    },
    watch: {
        multiple: {
            handler() {
                if (this.multiple) {
                    this.reqDeptList = [];
                } else {
                    this.reqDeptList = {};
                }
            },
        },
        value: {
            immediate: true,
            deep: true,
            handler() {
                // value始终为数组。reqDeptList单选时为对象，多选时为对象数组。
                // 暂时仅用作单选。
                if (Array.isArray(this.value)) {
                    if (this.multiple) {
                        this.reqDeptList = this.value;
                    } else {
                        this.reqDeptList = this.value[0];
                    }
                } else if (this.multiple) {
                    if (this.value === '') {
                        this.reqDeptList = [];
                    } else {
                        this.reqDeptList = [this.value];
                    }
                } else if (this.value === '') {
                    this.reqDeptList = {};
                } else {
                    this.reqDeptList = this.value[0];
                }
                if (Array.isArray(this.reqDeptList)) {
                    (this.reqDeptList || []).forEach((item = {}) => {
                        if (!this.reqList.some(org => org.id === item.id)) {
                            this.reqList.push(item);
                        }
                    });
                } else if (!(this.reqList || []).some(org => org.id === this.reqDeptList?.id)) {
                    this.reqList.push(this.reqDeptList || {});
                }
            },
        },
    },
};
</script>

<style>

</style>
