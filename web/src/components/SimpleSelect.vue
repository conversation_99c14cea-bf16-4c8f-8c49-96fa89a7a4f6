<template>
    <jacp-form-select
        v-bind="$attrs"
        v-on="$listeners"
        :confirm="false"
        :simple="true"
        :auto-width="true"
    />
</template>
<script>
// 这是一个小的select，不支持删除，没有编辑提示和编辑确认
// [!important] 没有校验功能，慎用
export default {
    name: 'JacpSimpleSelect',
    inheritAttrs: false,
};
</script>
<style lang="less">
.jacp-simple-select{
    .el-input.is-disabled{
        .el-input__inner{
            background-color: transparent;
            color: #999;
        }
        .el-input__suffix{
            display: none;
        }
    }
}
</style>
