<template>
    <div :class="['layout-card-root']">
        <div
            class="layout-card-title"
            v-if="title"
        >
            <span class="layout-card-title__text">{{ title }}</span>
            <div class="layout-card-title__extra">
                <slot name="extra" />
            </div>
        </div>
        <div
            class="layout-card-content"
            :style="{minHeight: `${minHeight}px`, height: `${height}px`}"
        >
            <slot />
        </div>
    </div>
</template>
<script>
export default {
    name: 'LayoutCard',
    props: {
        title: String,
        span: {
            default: 2,
            type: Number,
        },
        minHeight: [Number, String],
        height: [Number, String],
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.layout-card-root{
  margin-top: 32px;
  width: 100%;
  .layout-card-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    &__extra{
      font-size: 12px;
      //color: @fontSecendColor;
      cursor: pointer;
      & a[href]{
        color: @fontSecendColor;
        &:hover{
          color: @primaryColor;
        }
      }
      & .el-input__inner{
        font-size: 12px;
        color: @fontSecendColor;
      }
      & i{
        color: @fontDisabledColor;
      }
      &:hover i,&:hover .el-input__inner{
        color: @primaryColor;
      }
    }
    &__text{
      font-size: 18px;
      color: @fontColor;
      letter-spacing: 0;
      text-align: justify;
      line-height: 40px; // element-ui的全局设置成了small以后，为了保留原来的布局，这里恢复成40px
    }
  }
}
.layout-card-content{
  background-color: #fff;
  font-size: 12px;
  // padding: 16px 24px 16px 24px;
  overflow: auto;
}
</style>
