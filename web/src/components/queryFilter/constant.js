// TODO: 这个应该是后端返回
export const FILTER_TYPE = [
    { value: 'EQ', label: '=' },
    { value: 'NE', label: '!=' },
    { value: 'GT', label: '>' },
    { value: 'GTE', label: '>=' },
    { value: 'LT', label: '<' },
    { value: 'LTE', label: '<=' },
    { value: 'CI', label: '区间' },
    { value: 'ISNULL', label: '为空' },
    { value: 'ISNOTNULL', label: '不为空' },
    { value: 'CONTAIN', label: '包含' },
    { value: 'EXCLUDE', label: '不包含' },
];
export const FILTER_TYPE_CATE = Object.freeze({
    oNumber: [
        { value: 'EQ', label: '=' },
        { value: 'NE', label: '!=' },
        { value: 'GT', label: '>' },
        { value: 'GTE', label: '>=' },
        { value: 'LT', label: '<' },
        { value: 'LTE', label: '<=' },
        { value: 'ISNULL', label: '为空' },
        { value: 'ISNOTNULL', label: '不为空' },
    ],
    oDate: [
        { value: 'CI', label: '区间' },
        { value: 'ISNULL', label: '为空' },
        { value: 'ISNOTNULL', label: '不为空' },
    ],
    oText: [
        { value: 'EQ', label: '=' },
        { value: 'NE', label: '!=' },
        { value: 'CONTAIN', label: '包含' },
        { value: 'EXCLUDE', label: '不包含' },
        { value: 'ISNULL', label: '为空' },
        { value: 'ISNOTNULL', label: '不为空' },
    ],
    oSelect: [
        { value: 'CONTAIN', label: '包含' },
        { value: 'EXCLUDE', label: '不包含' },
        { value: 'ISNULL', label: '为空' },
        { value: 'ISNOTNULL', label: '不为空' },
    ],
    oSelectReqired: [
        { value: 'CONTAIN', label: '包含' },
        { value: 'EXCLUDE', label: '不包含' },
    ],
});
