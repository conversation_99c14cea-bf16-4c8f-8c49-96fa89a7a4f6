import { SelectOption } from '@jmodule/jeep-ui';
// 用在复合高级查询里常用的工具函数
export const transformOptionFromId = arr => arr.map(o => new SelectOption({
    value: o.id,
    label: o.name,
}));
export const transformOptionFromCode = arr => arr.map(o => new SelectOption({
    value: o.code,
    label: o.name,
}));
export const transformOptionFromGroup = arr => arr.map(o => new SelectOption({
    value: o.groupId,
    label: o.groupName,
}));
export function bindQueryFilterChangedHandler(field = {}, outerFieldsEvents = {}) {
    const vm = this;
    if (field.getListener) {
        const events = outerFieldsEvents[field.name];
        const listener = Object.keys(events).reduce((lis, eventName) => {
            const superEvent = field.getListener(eventName);
            // eslint-disable-next-line func-names
            lis[eventName] = function (arg) {
                const fieldInstance = this;
                const customizedEvent = events[eventName];
                if (superEvent) {
                    superEvent.call(fieldInstance, arg);
                }
                customizedEvent.call(vm, arg);
                // console.log(`${fieldInstance.name} changed`); // 通知外围更新，同时把field信息传递过去
            };
            return lis;
        }, {});
        field.listeners = listener;
    }
}
export const setQueryFilterOption = queryOptionMap => (filter = {}, field = {}) => {
    const defer = queryOptionMap[filter.column];
    defer().then((options) => {
        field.options = options;
        // console.log(`设置${filter.column}的选项`, { field, options });
    });
};
