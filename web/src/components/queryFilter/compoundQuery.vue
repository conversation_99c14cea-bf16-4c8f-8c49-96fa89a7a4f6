<template>
    <div
        :class="scopedClass.mod({ compound: true }).cn"
    >
        <div
            :class="scopedClass.el('header').cn"
        >
            <div
                :class="scopedClass.el('header').el('leftwrap').cn"
            >
                <!-- FIXME: 诡异的布局方式，需要结合着下面动态布局fix一下 -->
                <slot name="prefix" />

                <!-- 封装了筛选器tabs和dialog的 -->
                <jacp-editable-tabs
                    ref="filter"
                    :query-manager="queryManager"
                    :conditions-creator="conditionsCreator"
                    @query="handleQueryChange($event)"
                    @createQueryFilter="() => handleFormVisibilityChange(false)"
                    @openForm="() => handleFormVisibilityChange(true)"
                    @updateQueryFilter="handleQueryUpdate"
                />
                <el-row
                    type="flex"
                    align="middle"
                    style="flex-shrink: 0;margin-left: -4px"
                >
                    <el-divider direction="vertical" />
                    <el-input
                        :placeholder="inputPlaceholder"
                        prefix-icon="el-icon-search"
                        :class="[scopedClass.el('header').el('input').cn, 'j-input--background']"
                        :clearable="true"
                        v-model="keyword"
                        @change="(val) => setTempFilterValue('keyword', val)"
                        @keydown.enter.native="({target}) => {
                            setTempFilterValue('keyword', target.value).then(() => {
                                tempQuery();
                            });
                        }"
                    />
                    <jacp-text
                        class="j-mgl16 j-whitespace-nowwrap"
                        icon="jacp-icon-a-ic-toolbar-fliter"
                        type="secend"
                        active
                        @click.native="() => handleFormVisibilityChange(!showForm)"
                    >
                        高级筛选
                    </jacp-text>
                </el-row>
            </div>
        </div>
        <div
            :class="scopedClass.el('content').cn"
        >
            <!-- 统一使用一个组件来build表单就可以了。外侧通过监听事件来完成字段的option处理什么的 -->
            <jacp-query-builder
                v-if="queryBuilder"
                v-bind="$attrs"
                :meta-filter-type-config="metaFilterTypeConfig"
                :query-builder="queryBuilder"
                :class="scopedClass.el('content').el('form').mod({
                    show: showForm,
                    dark: dark,
                }).cn"
                :schema="schema"
                ref="queryBuilder"
            >
                <query-actions
                    ref="queryActions"
                    :show-save="showSave"
                    :show-form.sync="showForm"
                    @tempQuery="tempQuery"
                    @saveQueryFilter="saveQueryFilter"
                    @saveAsQueryFilter="saveAsQueryFilter"
                    @resetForm="() => {
                        resetForm();
                        tempQuery();
                    }"
                />
            </jacp-query-builder>
        </div>
    </div>
</template>
<script>
import cloneDeep from 'lodash/cloneDeep';
import isFunction from 'lodash/isFunction';
import { CompoundQuery, QUERY_OWNER_TYPE } from './model/query';
import QueryActions from '@/components/queryFilter/form/queryActions';
import { CompoundQueryManager as QueryManager } from './model/queryManager';
import QueryBuilder from './model/queryBuilder.js';

const formMap = new Map();
export const setForm = (key, comp = {}) => formMap.set(key, comp);

const createFilters = metaFilterTypeConfig => conditions => Object.keys(conditions).map(column => ({
    column,
    value: conditions[column],
    type: ((metaFilterTypeConfig[column] || {}).filterType)[0]?.value || '=',
}));
export default {
    name: 'JacpCompoundQuery',
    inheritAttrs: false,
    props: {
        filterPersonalKey: {
            type: String,
            required: true,
        },
        initialConditions: {
            type: Object,
            default: () => ({}),
        },
        onQueryChange: { type: Function },
        onInitialSchema: { type: Function },
        // 给表单区域加一个灰底
        dark: { type: Boolean, default: false },
        metaFilterTypeConfig: { type: Object, default: () => ({}) },
        inputPlaceholder: { type: String, default: '输入标题或编号' },
        formVisible: { type: Boolean, default: false },
    },
    components: {
        QueryActions,
    },
    data() {
        const vm = this;
        const conditionsCreator = () => {
            const { initialConditions } = vm;
            return createFilters(this.metaFilterTypeConfig)(initialConditions);
        };
        return {
            scopedClass: vm.$scopedClass('jacp-common-filter'),
            showForm: this.formVisible,
            keyword: '',
            conditionsCreator,
            // 管理筛选器
            queryManager: undefined,
            // 管理筛选器表单
            queryBuilder: undefined,
            schema: undefined,
            defaultBuilderEvents: {
                onAddFilter: filter => vm.$emit('add', { filter, schema: this.schema }),
                onRemoveFilter: filter => vm.$emit('remove', { filter, schema: this.schema }),
                onMountFilter: filter => vm.$emit('mount', { filter, schema: this.schema }),
            },
        };
    },
    watch: {
        filterPersonalKey: {
            immediate: true,
            handler: 'initManager',
        },
        // showForm: {
        //     hanlder(val) {
        //         this.$emit('update:formVisible', val);
        //     },
        // },
    },
    computed: {
        query() {
            return this.queryBuilder ? this.queryBuilder.query : new CompoundQuery();
        },
        productAvailable() {
            return !!this.$store.getters['app/getAppByCode']('productUi');
        },
        showSave() {
            return this.query.type === QUERY_OWNER_TYPE.PERSONAL;
        },
    },
    methods: {
        initManager() {
            this.queryManager = new QueryManager(this.filterPersonalKey);
            this.queryManager.getQuerySchemaInstance().then(({ schema = {} } = {}) => {
                this.schema = isFunction(this.onInitialSchema) ? this.onInitialSchema(schema) : schema;
                if (!this.productAvailable) {
                    this.schema.fields = this.schema.fields.filter(item => item.name !== 'productId');
                }
            });
        },
        initFormBuilder(query = {}) {
            this.queryBuilder = new QueryBuilder(query, this.defaultBuilderEvents);
            const filter = this.queryBuilder.find('keyword');
            if (filter) {
                this.setKeyword(filter.value);
            }
            if (this.showForm) {
                this.$nextTick(() => {
                    this.resetForm();
                    this.queryBuilder.mountFilters();
                });
            }
        },
        handleQueryUpdate(query) {
            Object.assign(this.queryBuilder.query, query);
        },
        handleQueryChange(query = {}) {
            this.$emit('input', cloneDeep(query));
            this.initFormBuilder(query);
            // 主要用于在filterchange后，执行查询之前的时候，允许对filter.value做一些处理，
            if (isFunction(this.onQueryChange)) {
                Promise.resolve(this.onQueryChange(this.queryBuilder.query))
                    .then(() => {
                        // this.handleConditionChange(query);
                        this.tempQuery();
                    });
            } else {
                // this.handleConditionChange(query);
                this.tempQuery();
            }
        },
        handleFormVisibilityChange(visible) {
            this.showForm = visible;
            this.$emit('update:formVisible', visible);
            if (!this.queryBuilder.isMounted) {
                this.queryBuilder.mountFilters();
            }
            this.$emit('visible-change', visible);
        },
        saveQueryFilter() {
            const conditions = this.$refs.queryBuilder.getQueryFilters();
            // query.value：这个是筛选器保存过的value，持久化数据
            // conditions：是表单的当前的值，会发生变化
            this.$refs.filter.saveQueryFilter(conditions);
        },
        saveAsQueryFilter() {
            const conditions = this.$refs.queryBuilder.getQueryFilters();
            this.$refs.filter.saveAsQueryFilter(conditions);
        },
        tempQuery() {
            this.$emit('query', this.getConditions());
        },
        resetForm() {
            if (!this.$refs.queryBuilder) return;
            this.setKeyword((this.queryBuilder.find('keyword') || {}).value);
            this.$refs.queryBuilder.resetForm();
            this.$emit('reset', this.queryBuilder);
        },

        // Public methods
        setKeyword(val) {
            this.keyword = val;
        },
        // 由外向内：使用setFieldValue来设置内部字段value，例如：关联词变更
        // FIXME: 感觉有点复杂，链路有点长，是否由更好的方案？
        // 由内向外：在每个字段挂载到页面的时候，给field注册事件，借用设置field.listeners来监听内部value发生变化
        setTempFilterValue(key, val) {
            if (!this.queryBuilder || !this.$refs.queryBuilder) {
                console.debug('试图在复合高级筛选器(QueryBuilder)未完成初始化前设置字段值可能不会生效，异常字段信息:', key, val);
            }
            // 这里应该只变更显示的值，不应该变更query里原始的值
            if (this.$refs.queryBuilder) {
                const result = this.$refs.queryBuilder.setFieldValue(key, val);
                return Promise.resolve(result);
            }
            return Promise.resolve();
        },
        getConditions() {
            const conditions = this.$refs.queryBuilder.getQueryFilters();
            return { filters: cloneDeep(conditions) };
        },
    },
};
</script>
