import findIndex from 'lodash/findIndex';
import pick from 'lodash/pick';
import { CompoundQuery } from './query';

const avaliableEvents = [
    'onAddFilter',
    'onRemoveFilter',
    'onMountFilter',
];
export default class QueryBuilder {
  #eventHandlers = {};

  // onAddFilter/onRemoveFilter/onMountedFilter
  #isMounted = false;

  constructor(query = {}, config = {}) {
      this.query = new CompoundQuery(query);
      const events = pick(config, avaliableEvents);
      Object.keys(events).forEach(eventName => this.defineEventHanlder(eventName, events[eventName]));
  }

  get isMounted() {
      return this.#isMounted;
  }

  mountFilters() {
      if (Array.isArray(this.query.value)) {
          this.query.value.forEach(filter => this.runEvent('onMountFilter', filter));
      } else {
          this.query.value = [];
      }
      this.#isMounted = true;
  }

  defineEventHanlder(eventName, handle) {
      let handlers = this.#eventHandlers[eventName];
      if (!Array.isArray(handlers)) {
          handlers = [];
          this.#eventHandlers[eventName] = handlers;
      }
      handlers.push(handle);
  }

  runEvent(eventName, ...args) {
      const handlers = this.#eventHandlers[eventName] || [];
      handlers.forEach(handle => handle && handle.call(this, ...args));
  }

  addFilter(filter, opration = 'and') {
      if (opration in this.query) {
          this.query[opration](filter);
      } else {
          console.debug(`[复合高级筛选器]试图增加${opration}类型的运算关系无法成功， 仅支持 “AND”、“OR”类型的运算`);
      }
      this.runEvent('onAddFilter', filter);
  }

  removeFilter(filter = {}) {
      const { value } = this.query;
      const index = findIndex(value, f => f.column === filter.column);
      if (index > -1) {
          value.splice(index, 1);
      }
      this.runEvent('onRemoveFilter', filter);
  }

  find(key) {
      return this.query.find(key);
  }
}
