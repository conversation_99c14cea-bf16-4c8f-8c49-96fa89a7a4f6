export const QUERY_OWNER_TYPE = {
    SYSTEM: 1, // 固定的筛选器
    PERSONAL: 2, // 用户自定义的筛选器
};
export class OR {
    constructor(filters = []) {
        this.or = filters;
    }

    and(filter) {
        this.or.push(filter);
    }
}
// 键值对的查询条件：TODO: 以后会干掉
export default class Query {
    constructor({
        id, name,
        type = QUERY_OWNER_TYPE.SYSTEM, // 1: 系统的，2:自定义的
        erp = '',
        value = {},
    } = {}) {
        this.id = id;
        this.name = name;
        this.erp = erp;
        this.value = value;
        this.type = type;
    }
}
// 复合关系查询条件(compound query)
export class CompoundQuery {
    constructor({
        id, name,
        type = QUERY_OWNER_TYPE.SYSTEM, // 1: 系统的，2:自定义的
        erp = '',
        value = [],
    } = {}) {
        this.id = id;
        this.name = name;
        this.erp = erp;
        this.type = type;
        this.value = Array.isArray(value) ? value : [];
    }

    and(filter = {}) {
        this.value.push(filter);
    }

    or(filter) {
        const filters = Array.isArray(filter) ? filter : [filter];
        this.value.push(new OR(filters));
    }

    find(key) {
        return this.value.find(filter => filter.column === key);
    }
}
// const query = new CompoundQuery({
//     filters: [{ column: 'a', type: 'NOT', value: 123 }],
// });
// query.or([{ column: 'bor', type: 'NULL', value: 123 }]);
// console.log(query.filters);
