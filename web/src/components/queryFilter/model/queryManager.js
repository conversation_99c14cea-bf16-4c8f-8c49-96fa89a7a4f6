import http, { jacpSecurityHttp } from '@/plugins/http';
import DynamicForm from '@/models/dynamicForm';

// 由 QueryManagement 来维护所有筛选器的状态
export default class QueryManager {
    #personalKey = '';

    constructor(
        personalKey = '',
    ) {
        this.#personalKey = personalKey;
        this.allFilters = [];
        this.defaultId = undefined;
        this.fixedIds = [];
    }

    get personalKey() {
        return this.#personalKey;
    }

    // 获取所有的筛选器
    async getQueryFilter() {
        return http.get(`v1/setting/searcher/${this.personalKey}/all`).then((data) => {
            this.allFilters = data;
        });
    }

    // 获取默认筛选器和固定在顶部的筛选器
    async getFixedFilter() {
        return http.get(`v1/setting/searcher/${this.personalKey}/config`).then(({
            defaultId = undefined,
            fixedIds = [],
        } = {}) => {
            this.defaultId = defaultId || fixedIds[0];
            this.fixedIds = fixedIds;
        });
    }

    // 设置默认筛选器和固定在顶部的筛选器
    setFixedFilter({ defaultId, fixedIds = [] }) {
        return http.put(`v1/setting/searcher/${this.personalKey}/config`, {
            defaultId,
            fixedIds,
        }).then(() => {
            this.defaultId = defaultId || fixedIds[0];
            this.fixedIds = fixedIds;
        });
    }

    // 更新筛选器
    updateQueryFilter(filter) {
        return http.put(`v1/setting/searcher/${this.personalKey}`, filter).then(() => {
            const target = this.allFilters.find(item => item.id === filter.id);
            if (target) {
                Object.assign(target, filter);
            }
            return target;
        });
    }

    // 新增筛选器，param 包括 name, isDefault, value (筛选条件)
    saveQueryFilter(param) {
        return http.post(`v1/setting/searcher/${this.personalKey}`, param);
    }

    deleteQueryFilter(id) {
        return http.delete(`v1/setting/searcher/${this.personalKey}/${id}`).then(() => {
            const targetIndex = this.allFilters.findIndex(item => item.id === id);
            this.allFilters.splice(targetIndex, 1);
        });
    }

    getQuerySchema() {
        return jacpSecurityHttp.get(`setting/form/schema/query/name/${this.personalKey}`, {
            headers: { appId: 'teamspace' },
        });
    }

    // TODO: 与上面合二为一
    getQuerySchemaInstance() {
        return jacpSecurityHttp.get(`setting/form/schema/query/name/${this.personalKey}`, {
            headers: { appId: 'archDemand' },
        }).then((schema = {}) => new DynamicForm(DynamicForm.toLocal(schema)));
    }
}
// TODO: 兼容一下老的筛选器结构，以后统一为复合筛选器的时候就不需要query.value的这个结构了
export class CompoundQueryManager extends QueryManager {}
