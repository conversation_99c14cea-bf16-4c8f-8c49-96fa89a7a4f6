<template>
    <div class="jacp-editable-tabs-render-root">
        <el-tabs
            ref="queryFilterTabs"
            class="jacp-tabs--background"
            v-model="activeTab"
            :before-leave="beforeLeave"
            @tab-click="handleQuery"
        >
            <el-tab-pane
                v-for="filter in fixedFilters"
                :key="filter.id"
                :name="filter.id+''"
            >
                <ellipsis-text
                    slot="label"
                    :max-width="maxTabWidth"
                    :content="filter.name"
                />
            </el-tab-pane>
            <el-tab-pane
                key="more"
                name="more"
            >
                <template slot="label">
                    <el-dropdown
                        ref="queryFilterDropdown"
                        class="jacp-editable-tabs-render__dropdown"
                        placement="bottom"
                        @command="handleCommand"
                        trigger="click"
                    >
                        <div style="position: relative;padding-right:24px;">
                            <ellipsis-text
                                :content="activeMore || '更多'"
                                :max-width="maxTabWidth"
                            />
                            <jacp-icon
                                class="font-family jacp-editable-tabs-render__dropdown-icon"
                                name="el-icon-caret-bottom"
                            />
                        </div>

                        <el-dropdown-menu
                            class="jacp-editable-tabs-render__dropdown-menu"
                            slot="dropdown"
                        >
                            <!-- 系统预置的筛选器 -->
                            <el-dropdown-item
                                class="jacp-editable-tabs-render__dropdown-item"
                                v-for="(filter, index) in presetedFilters"
                                :key="filter.id"
                                :command="filter.id"
                            >
                                <ellipsis-text
                                    :content="filter.name"
                                    :max-width="120"
                                />
                                <span
                                    v-if="filter.id === defaultId"
                                    class="jacp-editable-tabs-render__tag"
                                >默认</span>
                                <el-dropdown
                                    class="jacp-editable-tabs-render__dropdown-more"
                                    trigger="click"
                                >
                                    <span
                                        class="el-dropdown-link"
                                        @click.stop
                                    >
                                        <i
                                            class="jacp-icon-more"
                                            style="color: #C0C4CC"
                                        />
                                    </span>
                                    <el-dropdown-menu
                                        slot="dropdown"
                                        :append-to-body="true"
                                    >
                                        <el-dropdown-item
                                            @click.native="setAsDefault(filter)"
                                        >
                                            设为默认
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="isFixable(filter)"
                                            @click.native="fixFilter(filter, 'add')"
                                        >
                                            置顶筛选器
                                        </el-dropdown-item>
                                        <!-- “全部”筛选器不可从顶部取消 -->
                                        <el-dropdown-item
                                            v-if="!isFixable(filter) && index !== 0"
                                            @click.native="fixFilter(filter, 'remove')"
                                        >
                                            从顶部取消
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </el-dropdown-item>
                            <el-divider
                                class="jacp-editable-tabs-render__divider"
                                v-if="presetedFilters.length && customFilters.length"
                            />
                            <!-- 自定义的筛选器 -->
                            <span
                                v-if="customFilters.length"
                                class="jacp-editable-tabs-render__dropdown-text"
                            >保存的筛选器</span>
                            <el-dropdown-item
                                class="jacp-editable-tabs-render__dropdown-item"
                                v-for="filter in customFilters"
                                :key="filter.id"
                                :command="filter.id"
                            >
                                <ellipsis-text
                                    :content="filter.name"
                                    :max-width="filter.id === defaultId ? 100 : 144"
                                />
                                <span
                                    v-if="filter.id === defaultId"
                                    class="jacp-editable-tabs-render__tag"
                                >默认</span>
                                <el-dropdown
                                    class="jacp-editable-tabs-render__dropdown-more"
                                    trigger="click"
                                    placement="bottom-end"
                                >
                                    <span
                                        class="el-dropdown-link"
                                        @click.stop
                                    >
                                        <i
                                            class="jacp-icon-more"
                                            style="color: #C0C4CC"
                                        />
                                    </span>
                                    <el-dropdown-menu
                                        slot="dropdown"
                                        :append-to-body="true"
                                    >
                                        <el-dropdown-item
                                            v-if="filter.id !== defaultId"
                                            @click.native="setAsDefault(filter)"
                                        >
                                            设为默认
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="isFixable(filter)"
                                            @click.native="fixFilter(filter, 'add')"
                                        >
                                            置顶筛选器
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="!isFixable(filter)"
                                            @click.native="fixFilter(filter, 'remove')"
                                        >
                                            从顶部取消
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            @click.native="renameFilter(filter)"
                                        >
                                            重命名
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            @click.native="deleteFilter(filter)"
                                        >
                                            <span style="color: #F55445">删除</span>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </el-dropdown-item>
                            <!-- <el-divider
                                class="jacp-editable-tabs-render__divider"
                                v-if="customFilters.length"
                            /> -->
                            <el-button
                                class="jacp-editable-tabs-render__dropdown-button"
                                type="text"
                                @click="createQueryFilter"
                            >
                                新增筛选器
                            </el-button>
                        </el-dropdown-menu>
                    </el-dropdown>
                </template>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import debounce from 'lodash/debounce';
// import elementResizeDetectorMaker from 'element-resize-detector';
import { getWidth, setStyle } from '@/plugins/utils.dom';

/* const erd = elementResizeDetectorMaker({
    strategy: 'scroll', // <- For ultra performance.
}); */

export default {
    name: 'JacpEditableTabsRender',
    props: {
        filters: {
            type: Array,
        },
        fixedIds: {
            type: Array,
            default: () => [],
        },
        defaultId: {
            type: Number,
        },
        maxTabWidth: {
            type: Number,
            default: 88,
        },
        boundsContainerSelector: {
            type: String,
        },
    },
    data() {
        return {
            activeTab: '',
            activeMore: '',
            activeMoreId: undefined,
            fixedIdsVisible: [...this.fixedIds],
        };
    },
    computed: {
        fixedFilters({ fixedIds, filters }) {
            return filters.filter(item => fixedIds.includes(item.id)).sort(
                (a, b) => fixedIds.indexOf(a.id) - fixedIds.indexOf(b.id),
            );
        },
        presetedFilters() {
            return this.filters.filter(item => item.type === 1);
        },
        customFilters() {
            return this.filters.filter(item => item.type === 2);
        },
    },
    mounted() {
        this.initContainer();
        this.handleResize();
        window.addEventListener('resize', debounce(this.handleResize.bind(this), 200));
    },
    beforeDestroy() {
        // erd.removeAllListeners(this.container);
        this.container = null;
    },
    methods: {
        initContainer() {
            let container = this.boundsContainerSelector && document.querySelector(this.boundsContainerSelector);
            if (!container) {
                container = this.$el.parentNode;
            }
            this.container = container;
            // erd.listenTo(this.container, this.handleResize);
        },
        setCurrentTab(id = this.defaultId, emitQuery = true) {
            const ids = this.fixedIdsVisible.length ? this.fixedIdsVisible : this.fixedIds;
            const activeFilter = (this.filters || []).find(item => item.id === id) || {};
            // 保存下当前使用筛选器 id，在 handleResize 中使用
            this.activeId = activeFilter.id;
            if (ids.indexOf(id) !== -1) {
                this.activeTab = `${id}`;
                this.activeMoreId = undefined;
                this.activeMore = '';
            } else {
                this.activeTab = 'more';
                this.activeMoreId = activeFilter.id;
                this.activeMore = activeFilter.name;
                // this.resetActiveBarWidth();
            }
            if (emitQuery) {
                this.$emit('query', activeFilter);
            }
        },
        setCurrentTabByName(name) {
            const activeFilter = (this.filters || []).find(item => item.name === name) || {};
            if (this.fixedIdsVisible.indexOf(activeFilter.id) !== -1) {
                this.activeTab = `${activeFilter.id}`;
                this.activeMoreId = undefined;
                this.activeMore = '';
            } else {
                this.activeTab = 'more';
                this.activeMoreId = activeFilter.id;
                this.activeMore = activeFilter.name;
                this.resetActiveBarWidth();
            }
            this.$emit('query', activeFilter);
        },
        beforeLeave(activeName) {
            if (activeName === 'more' && this.activeMore === '') {
                return false;
            }
            return true;
        },
        handleQuery(tab) {
            if (tab.name !== 'more') {
                this.setCurrentTab(+tab.name);
            }
        },
        handleCommand(id) {
            this.setCurrentTab(id);
            if (this.activeTab === 'more') {
                this.resetActiveBarWidth();
            }
        },
        resetActiveBarWidth() {
            setTimeout(() => {
                this.handleResize();
            }, 1000);
        },
        handleResize() {
            if (!this.activeId) {
                return;
            }

            const tabs = this.$el.querySelectorAll('.el-tabs__item');
            [].slice.call(tabs).forEach((el) => {
                setStyle(el, 'display', 'inline-block');
            });

            const containerSize = getWidth(this.container);
            const tabSizes = Array.from(tabs).map(tabEl => getWidth(tabEl, true));
            let currentSumWidth = tabSizes[tabSizes.length - 1];
            let lastVisibleIndex = -1;

            tabSizes.slice(0, tabSizes.length - 1).forEach((size) => {
                currentSumWidth += size;

                if (currentSumWidth <= containerSize) {
                    lastVisibleIndex += 1;
                }
            });

            this.fixedIdsVisible = this.fixedIds.slice(0, lastVisibleIndex + 1);

            this.setCurrentTab(this.activeId, false);

            [].slice.call(tabs, lastVisibleIndex + 1, tabSizes.length - 1).forEach((el) => {
                el.classList.add('tab--hidden');
                setStyle(el, 'display', 'none');
            });
        },
        isFixable(filter) {
            return !this.fixedIdsVisible.includes(filter.id);
        },
        fixFilter(filter, action) {
            this.$emit('fixFilter', [filter.id, action]);
            const activeId = this.activeTab === 'more'
                ? +this.activeMoreId
                : +this.activeTab;
            this.setCurrentTab(activeId, false);
        },
        setAsDefault(filter) {
            this.$emit('setAsDefault', filter.id);
        },
        renameFilter(filter) {
            this.$emit('renameFilter', filter);
        },
        deleteFilter(filter) {
            if (this.defaultId === filter.id) {
                this.$message({
                    message: '不可删除默认筛选器',
                    type: 'warning',
                });
                return;
            }
            this.$emit('deleteQueryFilter', filter);
        },
        createQueryFilter() {
            this.$refs.queryFilterDropdown.visible = false;
            this.$emit('createQueryFilter');
        },
    },
    watch: {
        filters: {
            handler() {
                if (this.activeTab === 'more') {
                    this.activeMore = (this.filters.find(item => item.id === this.activeMoreId) || {}).name;
                }
            },
        },
        fixedFilters: {
            handler(v) {
                if (v.length) {
                    this.$nextTick(() => {
                        this.$refs.queryFilterTabs.$nextTick(() => {
                            this.handleResize();
                        });
                    });
                }
            },
        },
        defaultId: {
            handler(newVal, oldVal) {
                if (newVal && !oldVal) {
                    this.setCurrentTab();
                }
            },
        },
    },
};
</script>

<style lang="less">
.jacp-editable-tabs-render {
    &-root {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        background-color: #fff;
        height: 48px;
        min-height: 48px;
        transition: all 0.3s;
        .jacp-icon__background {
            margin-left: 0 !important;
        }
    }
    &__dropdown {
        // height: 48px;
        &.el-dropdown {
            color: inherit;
        }
        &-menu{
            max-height: 330px;
            padding-bottom: 0;
            // overflow-y: overlay;
            overflow-y: auto;
            &::-webkit-scrollbar {
                width: 5px;
            }
            &::-webkit-scrollbar-track {
                border-radius: 4px;
                background-color: #fff;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 4px;
                background-color: #dddee0;
            }
        }
        .el-dropdown-menu i {
            margin-right: 0;
        }
    }
    &__dropdown-item.el-dropdown-menu__item {
        display: flex;
        align-items: center;
        width: 200px;
        padding-right: 36px;
        height: 34px;
        font-size: 14px;
    }
    &__dropdown-text {
        display: inline-flex;
        align-items: center;
        font-size: 12px;
        color: #8F939A;
        height: 34px;
        padding: 0 12px;
    }
    &__dropdown-button {
        font-weight: 400;
        font-size: 14px;
        position: sticky;
        bottom: 0px;
        width: 100%;
        // border-top: var(--border--hr);
        // background: #fff;
        line-height: 20px;
        padding-top: 6px;
        padding-bottom: 6px;
        &,&:hover{
            background: #fff;
            border-top: var(--border--hr);
        }
    }
    &__dropdown-more {
        // visibility: hidden;
        position: absolute;
        right: 8px;
        .el-dropdown-menu {
            width: intrinsic;           /* Safari/WebKit uses a non-standard name */
            width: -moz-max-content;    /* Firefox/Gecko */
            width: -webkit-max-content; /* Chrome */
            width: max-content;
        }
    }
    &__dropdown-icon {
        color: var(--color--arrow-base);
        transition: transform 0.2s;
        position: absolute;
        right: 4px;
        top: 8px;
    }
    &__divider.el-divider--horizontal{
        margin: 8px 0;
    }
    &__tag {
        font-size: 12px;
        width: 40px;
        height: 20px;
        line-height: 20px;
        margin-left: 8px;
        padding: 0 8px;
        color: #2695F1;
        background: #D4EAFC;
    }
    & .el-select-dropdown__item.selected {
        font-weight: 600;
    }
}
</style>
