<template>
    <div class="editable-tabs">
        <slot />
        <!-- 筛选器 tabs -->
        <jacp-editable-tabs-render
            ref="queryFilter"
            :filters="queryManager.allFilters"
            :default-id="queryManager.defaultId"
            :fixed-ids="queryManager.fixedIds"
            @query="setCurrentFilter($event)"
            @fixFilter="fixFilter($event)"
            @setAsDefault="setAsDefault($event)"
            @renameFilter="renameFilter($event)"
            @deleteQueryFilter="deleteQueryFilter($event)"
            @createQueryFilter="createQueryFilter"
        />
        <slot name="extra" />
        <el-dialog
            custom-class="j-dialog list-filter-message-box"
            top="0"
            :title="dialogTitle"
            :close-on-click-modal="false"
            :visible.sync="dialogVisible"
        >
            <filter-msg-box
                ref="filterMsgBox"
                v-model="dialogFormData"
                :filter-names="queryManager.allFilters.map(filter => filter.name)"
            />
            <span
                class="dialog-footer"
                slot="footer"
            >
                <el-button @click="onCancle">取 消</el-button>
                <jacp-button
                    type="primary"
                    :on-click="onConfirm"
                >{{ confirmButtonText }}</jacp-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import isFunction from 'lodash/isFunction';
import QueryManager from '@/components/queryFilter/model/queryManager';
import FilterMsgBox from '@/modules/card/components/filterMsgBox';

const saveAsTitle = '保存筛选器';
const createTitle = '新增筛选器';
const saveAsBtnText = '确定';
const createBtnText = '确定，去编辑';

// TODO: 逐步分离成一个管理tabs的通用组件，去掉筛选器的业务属性
export default {
    name: 'JacpEditableTabs',
    props: {
        conditionsCreator: {
            type: Function,
        },
        queryManager: { type: QueryManager, required: true },
    },
    components: {
        FilterMsgBox,
    },
    data() {
        return {
            currentQueryFilterId: undefined,
            dialogFormData: {}, // 重命名/另存为 dialog 内表单保存的值
            dialogVisible: false,
            dialogTitle: '保存筛选器',
            confirmButtonText: '确定',
        };
    },
    mounted() {
        this.initQueryManager();
    },
    computed: {
        currentFilter() {
            return this.queryManager.allFilters.find(filter => filter.id === this.currentQueryFilterId);
        },
    },
    watch: {
        queryManager() {
            this.initQueryManager();
        },
    },
    methods: {
        async initQueryManager() {
            await this.loadFilters();
            this.currentQueryFilterId = this.queryManager.defaultId;
        },
        async loadFilters() {
            await this.queryManager.getQueryFilter();
            await this.queryManager.getFixedFilter();
        },
        setCurrentFilter(filter) {
            this.currentQueryFilterId = filter.id;
            this.$emit('query', this.currentFilter);
        },
        fixFilter([filterId, action]) {
            const { fixedIds } = this.queryManager;
            if (action === 'add') {
                // 添加到“全部”筛选器的后面
                if (fixedIds.includes(filterId)) {
                    fixedIds.splice(fixedIds.indexOf(filterId), 1);
                }
                fixedIds.splice(1, 0, filterId);
            } else if (action === 'remove') {
                fixedIds.splice(fixedIds.indexOf(filterId), 1);
            }
            this.queryManager.setFixedFilter({
                fixedIds,
                defaultId: this.queryManager.defaultId,
            });
        },
        setAsDefault(id) {
            this.queryManager.setFixedFilter({
                defaultId: id,
                fixedIds: this.queryManager.fixedIds,
            });
        },
        renameFilter(filter) {
            // this.dialogFormData = {
            //     name: filter.name,
            // };
            // this.dialogTitle = '筛选器重命名';
            // this.dialogVisible = true;
            this.$prompt('请输入保存的筛选器名称', '筛选器重命名', {
                customClass: 'list-filter-message-box',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                closeOnClickModal: false,
                inputPattern: /\S/,
                inputValue: filter.name,
                inputValidator: this.inputValidator.bind(this, filter),
                inputErrorMessage: '名称不能为空!',
            }).then(({ value }) => {
                this.queryManager.updateQueryFilter({
                    ...filter,
                    name: value,
                }).then(() => {
                    this.$message.success('保存成功!');
                });
            });
        },
        deleteQueryFilter(filter) {
            const { id } = filter;
            const { fixedIds } = this.queryManager;
            // 兼容筛选器历史数据结构
            this.$confirm(`确定要删除该"${filter.name || filter.jsonValue.name}"筛选器吗?`, '', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                // 该筛选器是否是固定到顶部的
                let fixFilterPromise;
                const deleteFilterPromise = this.queryManager.deleteQueryFilter(id);
                if (fixedIds.includes(id)) {
                    fixedIds.splice(fixedIds.indexOf(id), 1);
                    fixFilterPromise = this.queryManager.setFixedFilter({
                        fixedIds,
                        defaultId: this.defaultId,
                    });
                }
                Promise.all([deleteFilterPromise, fixFilterPromise]).then(() => {
                    // 如果删除了当前正在使用的筛选器，则使用默认筛选器刷新列表
                    if (id === this.currentQueryFilterId) {
                        this.currentQueryFilterId = this.queryManager.defaultId;
                        this.$refs.queryFilter.setCurrentTab();
                    }
                    this.$message.success('删除成功！');
                });
            });
        },
        createQueryFilter() {
            // 有creator的直接创建conditions并打开弹窗
            if (isFunction(this.conditionsCreator)) {
                const conditions = this.conditionsCreator() || {};
                this.saveAsQueryFilter(conditions, true);
            }
            this.$emit('createQueryFilter');
        },
        // 以下几个方法是，从 QueryActions 触发，并在 view.task.table 里调用的
        saveQueryFilter(conditions) {
            this.queryManager.updateQueryFilter({
                ...this.currentFilter,
                value: conditions,
            }).then((query) => {
                this.$message.success('保存成功！');
                this.$emit('updateQueryFilter', query);
            });
        },
        saveAsQueryFilter(conditions, create = false) {
            this.dialogTitle = create ? createTitle : saveAsTitle;
            this.confirmButtonText = create ? createBtnText : saveAsBtnText;
            this.dialogVisible = true;
            this.conditionsInSaveAs = conditions; // 暂时保存下另存为的筛选条件
        },

        onCancle() {
            this.dialogVisible = false;
            this.dialogFormData = {};
            this.$nextTick(() => {
                this.$refs.filterMsgBox.clearValidate();
            });
        },
        async onConfirm() {
            const valid = await this.$refs.filterMsgBox.$refs.filterMsgForm.validate();
            if (valid) {
                return this.queryManager.saveQueryFilter({
                    name: this.dialogFormData.name,
                    isDefault: this.dialogFormData.isDefault,
                    value: this.conditionsInSaveAs,
                }).then(async (resp) => {
                    this.onCancle();
                    await this.loadAndSwitchTab(resp);
                    this.$emit('openForm');
                    this.$message.success('保存成功！');
                });
            }
            return Promise.reject();
        },
        // 新增筛选器后切换到新的 tab 下
        async loadAndSwitchTab(newFilter) {
            await this.loadFilters();
            this.$refs.queryFilter.setCurrentTab(newFilter.id);
        },
        inputValidator(filter, value) {
            if (this.queryManager.allFilters.find(item => item.name === value && item.id !== filter.id)) {
                return '当前名称已占用，请重新输入筛选器名称';
            }
            return true;
        },
    },
};
</script>

<style lang="less">
.editable-tabs {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    height: 48px;
    min-height: 48px;
    overflow: hidden;
    .el-dialog__wrapper {
        display: grid;
        place-content: center;
    }
    .el-dialog {
        margin: 0;
        width: 464px;

        .el-form-item {
            margin: 0;
            margin-bottom: 18px;
        }
        .el-form-item:last-child {
            margin-bottom: 0;
        }
    }
}
</style>
