<template>
    <div
        class="jacp-query-builder"
        v-if="queryBuilder && Array.isArray(queryBuilder.query.value)"
    >
        <div class="jacp-query-builder__wrapper">
            <el-form :model="model">
                <local-query-item
                    v-for="filter in queryBuilder.query.value"
                    v-show="filter.column in fieldsMap"
                    :key="filter.column"
                    :meta-field="fieldsMap[filter.column]"
                    :meta-filter-type="(metaFilterTypeConfig[filter.column] || {}).filterType"
                    :ui-apdator="schema.uiApdator"
                    v-bind="filter"
                    ref="queryItems"
                    :value-key="fieldsMap[filter.column] && getItemValueKey(fieldsMap[filter.column], filter)"
                />
            </el-form>
            <!-- FIXME: 样式分散在别处了 -->
            <el-row class="query-conditions-form__bottom">
                <el-popover
                    trigger="click"
                >
                    <jacp-query-conditions-selector
                        :init-check-list="checkedFields"
                        ref="queryConditionsSelectorEl"
                        :fields="schema.fields"
                        :disabled-field="disabledFields"
                        :categories="categories"
                        :without-keyword="false"
                        @checked="handleCheckedChange"
                    />
                    <jacp-button
                        class="query-edit-button"
                        size="mini"
                        slot="reference"
                        plain
                    >
                        编辑筛选条件<i class="el-icon-caret-bottom" />
                    </jacp-button>
                </el-popover>
                <slot />
            </el-row>
        </div>
    </div>
</template>

<script>

import LocalQueryItem from './queryItem.vue';
import QueryBuilder from '../model/queryBuilder';

export default {
    name: 'JacpQueryBuilder',
    props: {
        queryBuilder: { type: QueryBuilder, required: true },
        schema: { type: Object, required: true },
        // query: { type: Object, required: true },
        // 一定会存在并且不能被移除的条件字段
        disabledFields: { type: Array },
        categories: { type: Array, default: () => [] },
        metaFilterTypeConfig: { type: Object, default: () => ({}) },
        // eslint-disable-next-line no-unused-vars
        getItemValueKey: { type: Function, default: (field, filter) => undefined },
    },
    // 提供一个方式允许访问到所有filter的value
    provide() {
        return {
            dynamicForm: {
                getReaciveModel: () => this.model,
            },
        };
    },
    components: {
        LocalQueryItem,
    },
    computed: {
        fieldsMap() {
            if (!this.schema.fields) {
                return [];
            }
            return this.schema.fields.reduce((res, field) => Object.assign(res, { [field.name]: field }), {});
        },
        checkedFields() {
            const { query = {} } = this.queryBuilder;
            return Array.isArray(query.value) ? query.value.map(filter => filter.column) : [];
        },
        model() {
            if (!this.queryBuilder.query || !Array.isArray(this.queryBuilder.query.value)) {
                return {};
            }
            return this.queryBuilder.query.value
                .reduce((model, filter) => Object.assign(
                    model,
                    { [filter.column]: filter.value },
                ), {});
        },
    },
    methods: {
        getQueryFilters() {
            if (!this.$refs.queryItems) {
                console.error('No query items');
                return [];
            }
            return this.$refs.queryItems
                .filter(item => item.filter.column)
                .map(item => item.filter);
        },
        // TODO:
        resetForm() {
            if (!Array.isArray(this.$refs.queryItems)) {
                console.error('No query items be reset');
                return;
            }

            this.$refs.queryItems.forEach(item => item.reset());
        },
        handleCheckedChange(field = {}, checked) {
            if (checked) {
                this.queryBuilder.addFilter({
                    column: field.name,
                    value: field.value,
                    type: undefined,
                });
            } else {
                this.queryBuilder.removeFilter({
                    column: field.name,
                });
            }
        },
        // FIXME: 提供一个在外侧临时修改显示数据，又不影响元数据的方法
        setFieldValue(key, val) {
            const result = this.$refs.queryItems.some((vm) => {
                if (vm.filter.column === key) {
                    // console.log('找到啦。可以设置临时value了', val);
                    vm.compValue.filterValue = val;
                    return true;
                }
                return false;
            });
            return result;
        },

    },
};
</script>

<style lang="less">
.jacp-query-builder {
    // margin-top: var(--gutter--medium);
    .el-form{
        display: flex;
        align-content: flex-start;
        flex-wrap: wrap;
    }
    .jacp-query-builder-item {
        width: 33%;
    }
    // 覆盖一些自定义组件的样式
   & [name=orgnazitionId] {
        width:fit-content;
        // max-width: 180px;
        border: var(--border--form);
        height: 32px;
        border-radius: 4px;
        padding-left: var(--gutter--medium);
        // padding-top: 6px;
        display: block;
        line-height: 32px;
        height: 32px;
        font-size: 14px;
        &>.org-selector__value{
            margin-right: var(--gutter--medium);
        }
   }
}
</style>
