<template>
    <el-form-item
        class="jacp-query-builder-item"
    >
        <div
            slot="label"
            class="jacp-query-builder-item-label"
        >
            {{ metaField.label }}
        </div>
        <el-select
            v-model="compValue.filterType"
            :placeholder="$t('jacp.selectPlaceHolder')"
            class="jacp-query-builder-item-filter-type"
        >
            <el-option
                v-for="ft in metaFilterType"
                :key="ft.value"
                :label="ft.label"
                :value="ft.value"
            />
        </el-select>
        <el-tooltip
            :disabled="!metaField.title"
            :content="metaField.title"
        >
            <div
                slot="content"
                v-html="metaField.title"
            />
            <component
                v-if="metaField.getFragment && metaField.type !== 'enum'"
                class="jacp-query-builder-item-input-value"
                :is="metaField.getFragment(uiApdator.fragments)"
                ref="fieldEl"
                v-model="compValue.filterValue"
                v-bind="{
                    ...metaField.getFragmentProps(),
                    ...{...metaField.nativeListeners},
                    ...itemProps}"
                v-on="{...metaField.listeners}"
                @change="handleChange"
                :disabled="inputDisabled || metaField.disabled"
            />
            <!-- 为了根据valueKey来取value -->
            <el-select
                v-if="metaField.type == 'enum'"
                class="jacp-query-builder-item-input-value"
                ref="fieldEl"
                v-model="compValue.filterValue"
                v-bind="{
                    ...metaField.getFragmentProps(),
                    ...{...metaField.nativeListeners},
                    ...itemProps}"
                v-on="{...metaField.listeners}"
                :disabled="inputDisabled || metaField.disabled"
            >
                <el-option
                    v-for="(option, index) in metaField.options"
                    :key="index"
                    :value="valueKey ? option[valueKey] : option.value"
                    :label="option.label"
                />
            </el-select>
        </el-tooltip>
    </el-form-item>
</template>

<script>
import isFunction from 'lodash/isFunction';
import isPlainObject from 'lodash/isPlainObject';
import isEqual from 'lodash/isEqual';
import isUndefined from 'lodash/isUndefined';
import jeepUi from '@/models/jacpJeepUi';
import DemandRelatedProjectsSelector from '@/modules/demand/components/demandRelatedProjects';
import { DATE_SHORTCATS } from '@/utils/date';

const { UiApdator } = jeepUi;
const dateRangeShortcuts = ['today', 'recentAWeek', 'recentAMonth'];
export default {
    name: 'JacpQueryItem',
    props: {
        value: { type: [Number, String, Array, Object] },
        type: { type: String },
        metaField: { type: Object, default: () => ({}) },
        metaFilterType: { type: Array, default: () => ([]) },
        uiApdator: { type: UiApdator },
        valueKey: { type: String },
    },
    components: {
        DemandRelatedProjectsSelector,
    },
    data() {
        return {
            compValue: {
                filterType: this.type,
                filterValue: undefined,
            },
            filterTypeDefaultValue: {
                text: '=',
                enum: 'CONTAIN',
                date: 'CI',
                select_org: 'CONTAIN',
                select_user: 'CONTAIN',
                integer: '=',
            },
        };
    },
    methods: {
        getId() {
            if (this.metaField.type === 'enum' && this.valueKey) {
                if (Array.isArray(this.compValue.filterValue)) {
                    return this.compValue.filterValue.map((item) => {
                        if (isPlainObject(item)) {
                            return item[this.valueKey];
                        }
                        return item;
                    });
                }
            }
            if (this.valueKey && isPlainObject(this.compValue.filterValue)) {
                return this.compValue.filterValue[this.valueKey];
            }
            // 其他字段均直接返回value
            return this.compValue.filterValue;
        },
        reset() {
            if (!isEqual(this.compValue.filterValue, this.value)) {
                const field = this.metaField;

                if ('change' in field.listeners) {
                    field.listeners.change.call(field, this.value);
                }
            }
            this.setValue();
            this.setType();
        },
        // FIXME: 判断一下类型 ： 拷贝一层，一面影响原来的数据
        setValue() {
            this.compValue.filterValue = !isUndefined(this.value) ? JSON.parse(JSON.stringify(this.value)) : this.value;
        },
        setType() {
            this.compValue.filterType = this.type || this.defaultFilterType;
        },
        // FIXME: 直接通过v-on绑定的事件，在部门树和产品树第一次emit的change不执行？
        handleChange(val) {
            if (this.metaField.listeners && isFunction(this.metaField.listeners.change)) {
                this.metaField.listeners.change(val);
            }
        },
    },
    computed: {
        inputDisabled() {
            return ['ISNULL', 'ISNOTNULL'].some(item => item === this.compValue.filterType);
        },
        filter() {
            if (this.inputDisabled) {
                return {
                    column: this.metaField.name,
                    type: this.compValue.filterType,
                };
            }
            return {
                column: this.metaField.name,
                type: this.compValue.filterType,
                value: this.getId(),
            };
        },
        defaultFilterType() {
            return (this.metaFilterType[0]).value
            || this.filterTypeDefaultValue[this.metaField.type]
            || '=';
        },
        itemProps() {
            const { type } = this.metaField;

            const props = {};
            switch (true) {
            case type.includes('date'):
                props['value-format'] = 'timestamp';
                props['default-time'] = ['00:00:00', '23:59:59']; // 调整时间段的默认值
                props.pickerOptions = {
                    shortcuts: dateRangeShortcuts
                        .filter(text => text in DATE_SHORTCATS)
                        .map(text => ({
                            text: this.$t(`jacp.datePicker.shortcuts.${text}`),
                            onClick(picker) {
                                picker.$emit('pick', DATE_SHORTCATS[text]());
                            },
                        })),
                };
                break;
            default: break;
            }
            return props;
        },
    },
    // FIXME:
    watch: {
        value: {
            immediate: true,
            handler: 'setValue',
        },
        type: {
            immediate: true,
            handler: 'setType',
        },
    },
};
</script>

<style lang="less">
@--type-input-width: 100px;
.jacp-query-builder-item {
    display: flex;
    .el-form-item__label{
        font-size: var(--font-size--content);
        font-weight: var(--font-weight-default);
        color: var(--color--base--content);
    }
    .el-form-item__content {
        display: flex;
        width: 100%;
        height: fit-content;
        .jacp-query-builder-item-filter-type .el-input {
            width: var(@--type-input-width);
            .el-input__inner {
                width: var(@--type-input-width);
            }
        }
    }
    .jacp-query-builder-item-label {
        width: 140px;
    }
    .jacp-query-builder-item-input-value {
        margin-left: 4px;
        width: 100%;
        & .el-select__tags{
            overflow: auto;
        }
        .org-selector__value{
            height: 32px;
        }
    }
}
</style>
