<template>
    <div class="query-actions">
        <el-divider
            class="j-mgl16 j-mgr16"
            direction="vertical"
        />
        <jacp-button
            @click="query"
            type="primary"
            size="mini"
            icon="el-icon-search"
        >
            查询
        </jacp-button>
        <jacp-button
            icon="el-icon-refresh"
            size="mini"
            plain
            @click="resetForm"
        >
            重置
        </jacp-button>
        <jacp-button
            v-if="showSave"
            size="mini"
            plain
            @click="saveQueryFilter"
        >
            保存
        </jacp-button>
        <jacp-button
            @click="saveAsQueryFilter"
            size="mini"
            plain
        >
            另存为
        </jacp-button>
        <jacp-button
            icon="el-icon-arrow-up"
            size="mini"
            plain
            @click.native="() => {
                $emit('update:show-form', false)
            }"
        >
            收起
        </jacp-button>
    </div>
</template>

<script>
export default {
    name: 'QueryActions',
    props: {
        value: String,
        showSave: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            keyword: '',
        };
    },
    methods: {
        updateKeyword() {
            this.$emit('input', this.keyword);
        },
        query() {
            // this.$emit('input', this.keyword);
            this.$emit('tempQuery');
        },
        handleEnter() {
            this.updateKeyword();
            this.query();
        },
        saveAsQueryFilter() {
            this.$emit('saveAsQueryFilter');
        },
        saveQueryFilter() {
            this.$emit('saveQueryFilter');
        },
        resetForm() {
            this.$emit('resetForm');
        },
    },
    watch: {
        value: {
            handler(v) {
                if (v !== this.keyword) {
                    this.keyword = v;
                }
            },
        },
    },
};
</script>

<style lang="less">
.query-actions {
    display: flex;
    align-items: center;
    .el-input {
        width: 180px;
        & .el-input__inner {
            height: 28px;
        }
    }
    .el-button+.el-button {
        margin-left: 16px;
    }
    .jacp-icon-down {
        transform: rotate(180deg);
        transition: transform 0.3s;
        color: #50B7FF;
    }
    &-show-form .jacp-icon-down {
        transform: rotate(0);
    }
    &__btn {
        font-weight: 400;
        transition: all 0.3s;
        border-radius: 4px;
        padding-top: 7px;
        padding-bottom: 7px;
    }
}
</style>
