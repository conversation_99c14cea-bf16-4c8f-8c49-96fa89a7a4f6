<template>
    <div class="filter-selector-container">
        <el-scrollbar
            wrap-style="max-height: 300px;"
            v-if="!categories.length"
        >
            <el-checkbox-group
                v-model="checkList"
                @change="handleChange"
            >
                <el-checkbox
                    v-for="(field, key) in fieldsWithoutKeyword"
                    :key="key"
                    :label="field.name"
                    :disabled="isDisabled(field)"
                    @change="(value) => handleItemChange(field, value)"
                >
                    {{ field.label }}
                </el-checkbox>
            </el-checkbox-group>
        </el-scrollbar>

        <ul
            v-else
            class="filter-selector-category"
            style="display: flex;flex-wrap: nowrap;"
        >
            <li
                class="filter-selector-categoryitem"
                v-for="(cate, index) in categories"
                :key="`${cate.value}-${index}`"
            >
                <jacp-text
                    type="disable"
                    class="filter-selector-categoryitem__label"
                    size="12"
                >
                    {{ cate.label || cate.value }}
                </jacp-text>
                <el-checkbox-group
                    v-model="checkList"
                    @change="handleChange"
                >
                    <el-checkbox
                        v-for="fieldName in cate.data.filter(item => item in fieldsObject)"
                        :key="`${cate.value}-${fieldName}`"
                        :label="fieldsObject[fieldName].name"
                        :disabled="isDisabled(fieldsObject[fieldName])"
                        @change="(value) => handleItemChange(fieldsObject[fieldName], value)"
                    >
                        {{ fieldsObject[fieldName].label }}
                    </el-checkbox>
                </el-checkbox-group>
            </li>
        </ul>
    </div>
</template>
<script>
import { pick } from 'lodash';

export default {
    name: 'JacpQueryConditionsSelector',
    props: {
        fields: { type: Array, default: () => ([]) },
        initCheckList: { type: Array, default: () => ([]) },
        disabledField: { type: Array, default: () => ([]) },
        // 分类显示field
        categories: { type: Array, default: () => ([]) },
        withoutKeyword: { type: Boolean, default: true },
    },
    data() {
        return { checkList: [] };
    },
    methods: {
        remove(name) {
            this.checkList = this.checkList.filter(n => !n.includes(name));
        },
        isDisabled(field = {}) {
            return this.disabledField.includes(field.name);
        },
        handleItemChange(item, value) {
            this.$emit('checked', item, value);
        },
        handleChange() {
            this.$emit('demand-change', this.computedChecked, this.checkList.slice());
        },
        setInitCheckList(checkList) {
            // used for parent
            this.checkList = checkList;
        },
    },
    computed: {
        fieldsWithoutKeyword() {
            // 关键字搜索单独提出来了
            return this.withoutKeyword ? this.fields.filter(field => field.name !== 'keyword') : this.fields;
        },
        fieldsObject() {
            const result = {};
            (this.fieldsWithoutKeyword || []).forEach((field) => {
                result[field.name] = field;
            });
            return result;
        },
        computedChecked() {
            const { fieldsObject, checkList } = this;
            return Object.values(pick(fieldsObject, checkList));
        },
    },
    watch: {
        computedChecked() {
            this.$emit('change', this.computedChecked, this.checkList);
        },
        initCheckList: {
            immediate: true,
            handler() {
                this.checkList = this.initCheckList;
            },
        },
    },
};
</script>
<style lang="less" scoped>
.filter-selector-container{
    max-width: 60vw;
    overflow-x: auto;
}
.filter-selector-container .el-checkbox{
    // width: calc(30% - 24px);
    width: 120px;
    margin-bottom: 8px;
    display: block;
    margin-right: 0;
    font-weight: 400;
}
.filter-selector-categoryitem{
    list-style-type: none;
    padding: 0 8px;
    &+&{
        margin-left: 16px;
    }
    &__label{
        line-height: 28px;
        margin-bottom: 8px;
        width: 100%;
    }
}
</style>
