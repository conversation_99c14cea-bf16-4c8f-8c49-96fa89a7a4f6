<template>
    <div
        :class="scopedClass.mod({ compound: false }).cn"
    >
        <div
            :class="scopedClass.el('header').cn"
        >
            <div
                :class="scopedClass.el('header').el('leftwrap').cn"
            >
                <slot name="prefix" />
                <!-- 封装了筛选器tabs和dialog的 -->
                <jacp-editable-tabs
                    ref="filter"
                    :query-manager="queryManager"
                    :conditions-creator="conditionsCreator"
                    @query="handleQueryChange($event)"
                    @createQueryFilter="showForm = false"
                    @openForm="showForm = true"
                />
                <el-row
                    type="flex"
                    align="middle"
                    style="flex-shrink: 0;margin-left: -4px"
                >
                    <el-divider direction="vertical" />
                    <el-input
                        :placeholder="inputPlaceholder"
                        :class="[scopedClass.el('header').el('input').cn, 'j-input--background']"
                        :clearable="true"
                        prefix-icon="el-icon-search"
                        v-model="keyword"
                        @keydown.enter.native="tempQuery"
                    />
                    <jacp-text
                        icon="jacp-icon-a-ic-toolbar-fliter"
                        class="j-mgl16 j-whitespace-nowwrap"
                        type="secend"
                        active
                        @click.native="() => { showForm = !showForm}"
                    >
                        高级筛选
                    </jacp-text>
                </el-row>
            </div>
            <slot name="suffix" />
        </div>
        <div
            :class="scopedClass.el('content').cn"
        >
            <!-- TODO: 应该支持动态传入一个query-form的实现，所有query-form的表单有固定的入参和出参，可借用component：is来切换其他的表单实现 -->
            <component
                :is="form"
                ref="queryForm"
                :class="scopedClass.el('content').el('form').mod({
                    show: showForm,
                    dark: dark,
                }).cn"
                :filter="query"
                :show-form="showForm"
                :query-params="filterPersonalKey"
            >
                <query-actions
                    ref="queryActions"
                    :show-save="showSave"
                    :show-form.sync="showForm"
                    @tempQuery="tempQuery"
                    @saveQueryFilter="saveQueryFilter"
                    @saveAsQueryFilter="saveAsQueryFilter"
                    @resetForm="() => {
                        resetForm();
                        tempQuery();
                    }"
                />
            </component>
        </div>
    </div>
</template>
<script>
import merge from 'lodash/merge';
import cloneDeep from 'lodash/cloneDeep';
import isFunction from 'lodash/isFunction';
import omit from 'lodash/omit';
import QueryActions from '@/components/queryFilter/form/queryActions';
import CardQueryForm from '@/modules/card/components/queryConditionsForm';
import Query, { QUERY_OWNER_TYPE } from './model/query';
import QueryManager from './model/queryManager';
import TaskQueryForm from '@/modules/task/components/taskQueryForm';

const formMap = new Map();
export const setForm = (key, comp = {}) => formMap.set(key, comp);
setForm('card', CardQueryForm);
setForm('task', TaskQueryForm);


export default {
    name: 'JacpCommonQueryFilter',
    props: {
        filterPersonalKey: {
            type: String,
            default: 'demand-query-personal',
        },
        // FIXME: 初始值以demand的举例，其实是为了兼容以前用到的地方。。
        initialConditions: {
            type: Object,
            default: () => ({
                keyword: '',
                processors: [],
                status: [],
                demanders: [],
            }),
        },
        // 接收一个外部动态传入的参数，并且在保存的时候会存入筛选器的value
        extraConditions: { type: Object, default: () => ({}) },
        extraConditionsCreator: { type: Function },
        onQueryChange: { type: Function },
        dark: { type: Boolean, default: false },
        inputPlaceholder: { type: String, default: '输入标题或编号' },
    },
    components: {
        // DemandQueryForm,
        QueryActions,
    },
    data() {
        // 合并基本条件和附加条件
        const initialMergedConditions = merge(
            this.initialConditions,
            this.extraConditionsCreator ? this.extraConditionsCreator() : {},
        );
        const conditionsCreator = () => initialMergedConditions;
        return {
            scopedClass: this.$scopedClass('jacp-common-filter'),
            showForm: false,
            query: new Query(),
            keyword: '',
            conditionsCreator,
            queryManager: undefined,
        };
    },
    watch: {
        filterPersonalKey: {
            immediate: true,
            handler() {
                this.queryManager = new QueryManager(this.filterPersonalKey);
            },
        },
    },
    computed: {
        form() {
            const { filterPersonalKey } = this;
            if (formMap.has(filterPersonalKey)) {
                return formMap.get(filterPersonalKey);
            }
            let comp = {};
            Array.from(formMap.keys()).some((key) => {
                if (filterPersonalKey.includes(key)) {
                    comp = formMap.get(key);
                    return true;
                }
                return false;
            });
            return comp;
        },
        showSave() {
            return this.query.type === QUERY_OWNER_TYPE.PERSONAL;
        },
        conditions() {
            const { keyword, extraConditions } = this;
            return {
                ...this.query.value,
                ...extraConditions,
                keyword,
            };
        },
        conditionsOverrideByForm() {
            if (!this.$refs.queryForm) {
                return this.conditions;
            }
            const formData = omit(this.$refs.queryForm.formData, Object.keys(this.extraConditions));
            return {
                ...this.conditions,
                ...formData,
                keyword: this.keyword,
            };
        },
    },
    methods: {
        handleQueryChange(query = {}) {
            this.query = cloneDeep(query);
            this.resetForm();
            this.$emit('input', this.query);
            // 主要用于在filterchange后，执行查询之前的时候，允许对filter.value做一些处理，
            if (isFunction(this.onQueryChange)) {
                Promise.resolve(this.onQueryChange(query.value))
                    .then(() => {
                        this.handleConditionChange(query.value);
                        this.tempQuery();
                    });
            } else {
                this.handleConditionChange(query.value);
                this.tempQuery();
            }
        },
        handleConditionChange(conditions = {}) {
            this.keyword = conditions.keyword;
        },
        saveQueryFilter() {
            // TODO: 这个值和advanceQueryConditions有啥区别？
            // advanceQueryConditions：这个是筛选器保存过的value，不会发生变化，
            // queryForm.data：是表单的值，会发生变化
            this.$refs.filter.saveQueryFilter(this.conditionsOverrideByForm);
        },
        saveAsQueryFilter() {
            this.$refs.filter.saveAsQueryFilter(this.conditionsOverrideByForm);
        },
        tempQuery() {
            this.$emit('query', cloneDeep(this.conditionsOverrideByForm));
        },
        resetForm() {
            if (!this.$refs.queryForm) {
                return;
            }
            this.$refs.queryForm.data = cloneDeep(this.query.value);
            this.keyword = this.query.keyword;
            // this.tempQuery();
        },
    },
};
</script>
