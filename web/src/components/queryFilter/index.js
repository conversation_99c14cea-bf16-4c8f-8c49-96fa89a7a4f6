import JacpEditableTabs from './tabs/editableTabs';
import JacpEditableTabsRender from './tabs/editableTabsRender';
import JacpQueryBuilder from './form/queryBuilder';
import JacpQueryConditionsSelector from './form/queryConditionsSelector';
// import queryItem from './form/queryItem';
// 复合高级查询
import CompoundQuery from './compoundQuery';
// 简单高级查询
import CommonQueryFilter from './commonQueryFilter';
// TODO: 需要注册到全局么？
import QueryActions from './form/queryActions';
import './queryFilter.less';

// export { CommonQueryFilter };
export default [
    // 管理筛选器
    JacpEditableTabs,
    JacpEditableTabsRender,
    // 筛选器的查询表单
    JacpQueryBuilder,
    // JacpQueryItem,
    JacpQueryConditionsSelector,
    CompoundQuery,
    CommonQueryFilter,
    QueryActions,
];
