@import '~@/theme/var';

.jacp-common-filter{
    margin: 0;
    display: flex;
    flex-direction: column;
    .el-divider--vertical{
        margin: 0 var(--gutter--medium);
    }
    &__header{
        position: relative;
        height: var(--height--base);
        width: 100%;
        padding: 0 var(--gutter--large);
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        &__leftwrap{
            align-items: center;
            display: inline-flex;
            overflow: hidden;
        }
      
        &__input{
            max-width: var(--input-width--base);
            min-width: 80px;
        }
    }
    &__content{
        width: 100%;
        // border-top: var(--border--hr);
        background-color: #fff;
        &__form{
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.2, 0, 0.8, 1);
            will-change: transform;
            &--show{
                // 又不需要maxheight了？
                max-height: 1550px;
                overflow-y: scroll;
                overflow: overlay;
            }
            &--dark{
                border: 0;
                // 表单的背景色和圆角需要包裹一层，展开收起的时候作为一个整体
                .jacp-query-builder__wrapper{
                    margin: 0 var(--gutter--medium);
                    padding: var(--gutter--medium) 0;
                    background-color: var(--color--base--table-header);
                    border-radius: var(--radius--medium);
                }
            }
        }
    }
}

// TODO:需要以后把这个表单的布局统一起来，后面button组会统一巨左，目前只通过不同的间距控制居左位置
.jacp-common-filter--compound{
    .query-conditions-form__bottom{
        padding-left: 136px;
    }
}