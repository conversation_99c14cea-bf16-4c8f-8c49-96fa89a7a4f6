<template>
    <div class="jacp-input-multiselector-root">
        <el-autocomplete
            class="jacp-input-multiselector__input"
            v-model="text"
            :fetch-suggestions="filter"
            placeholder="请输入内容"
            @select="add"
        >
            <template slot-scope="{ item }">
                <div class="name">
                    {{ item.name }}
                </div>
            </template>
        </el-autocomplete>
        <div class="jacp-input-multiselector__value">
            <jacp-inline-list
                :data="value"
                :name-alias="nameAlias"
                @change="input(value)"
            />
        </div>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'JacpInputMultiSelector',
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        nameAlias: {
            type: String,
            default: 'name',
        },
        filter: {
            type: Function,
        },
        itemRender: {
            type: Function,
        },
    },
    data() {
        return {
            text: '',
        };
    },
    methods: {
        input(value) {
            this.$emit('input', value);
        },
        add(item) {
            if (this.value.indexOf(item) < 0) {
                this.value.push(item);
                this.text = '';
                this.input(this.value);
            }
        },
    },
};
</script>
