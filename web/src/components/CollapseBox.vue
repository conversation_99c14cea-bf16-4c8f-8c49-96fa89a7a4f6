<template>
    <div
        class="collapse-box__wrapper"
        :class="{
            'is-overflow': isOverflow,
        }"
        v-show="isOverflow !== null"
    >
        <!-- TODO: 这块还有问题，hidden一下的话就会闪一下，计算style的地方还需要再调整一下 -->
        <jacp-arrow
            :arrow-offset="arrowOffset"
            :visible-arrow="visibleArrow"
            class="collapse-box__arrow"
        />
        <div
            v-loading="loading"
            ref="viewContentEl"
            :style="styles"
            :class="['collapse-box__value', {
                'collapse-box__value--collapsed': this.isCollapsed === true,
            }]"
        >
            <span
                v-if="innerValue"
                v-html="$xss(innerValue)"
            />
            <span
                v-else
                class="j-nodata"
            >{{ noDataText }}</span>
        </div>
        <transition name="fade">
            <div
                class="collapse-box__buttons--unfold cancel-focus"
                v-show="isOverflow && isCollapsed === true"
                @mousedown.stop="toggleStatus"
            >
                <span>{{ unfoldText }}</span>
            </div>
        </transition>
        <div
            class="collapse-box__buttons--fold cancel-focus"
            v-show="isOverflow && isCollapsed === false"
            @mousedown.stop="toggleStatus"
        >
            <el-button
                round
                plain
                type="primary"
                size="mini"
            >
                {{ foldText }}
            </el-button>
        </div>
    </div>
</template>
<script>
import JacpArrow from './Arrow';

export default {
    // 一个展示内容的容器，可以展开收起，可以设置展开收起的文本，设置高度啥的
    name: 'JacpCollapseBox',
    components: { JacpArrow },
    props: {
        value: [String, Number],
        rows: [String, Number], // 设置rows或者高度，同时出现的时候以maxHeight为准
        maxHeight: {
            type: [String, Number],
            default: 150,
        },
        minHeight: {
            type: Number,
            default: 72,
        },
        unfoldText: {
            type: String,
            default: '展开',
        },
        foldText: {
            type: String,
            default: '收起',
        },
        // 是否显示箭头
        visibleArrow: Boolean,
        // 箭头距离左边的位置
        arrowOffset: [String],
        noDataText: {
            type: String,
            default: '无数据',
        },
    },
    data() {
        return {
            currentValue: '',
            // isOverflow和收缩状态的初始值都设置为null，计算后再赋值，才能保证初始状态不跳
            isOverflow: null,
            isCollapsed: null,
            observer: null,
        };
    },
    created() {
        setTimeout(() => {
            // 一开始渲染的时候不会触发MutationObserver的callback，只有发生变化了才会，所以每次创建的时候都给currentValue发生一下变化
            this.currentValue = this.value;
        }, 0);
    },
    mounted() {
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach(this.checkOverflow);
        });
        this.observer.observe(this.$refs.viewContentEl, {
            // attributes: true,
            childList: true,
            subtree: true,
        });
        // this.innerValue = convert.toHtml(this.$xss(this.innerValue));
    },
    beforeDestroy() {
        this.observer.disconnect();
    },
    computed: {
        loading() {
            return this.isOverflow === null;
        },
        styles() {
            const { maxHeight } = this;
            return this.isCollapsed ? {
                maxHeight: `${maxHeight}px`,
            } : {};
        },
        status() {
            return this.isCollapsed ? 'fold' : 'unfold';
        },
        innerValue() {
            const { currentValue } = this;
            return currentValue && currentValue.trim().replace(/\r?\n/g, '<br />');
        },
    },
    methods: {
        toggleStatus() {
            this.isCollapsed = !this.isCollapsed;
        },
        checkOverflow() {
            this.isCollapsed = true;
            // this.isOverflow = false;
            // console.log('checkOverflow');
            // 用MO的时候，每轮event loop都是非常快的，所以没必要每轮loop都Render UI，
            // 而是差不多16ms的时候再Render，所以直接获取高度是不行的，需要等ui渲染完了才能取到
            setTimeout(() => {
                if (!this.$refs.viewContentEl) {
                    return;
                }
                this.getStyles();
            }, 50);
        },
        getStyles() {
            const { scrollHeight } = this.$refs.viewContentEl;
            this.isOverflow = +scrollHeight > Number.parseInt(this.maxHeight, 10);
        },
    },
    watch: {
        value(val) {
            if (this.currentValue === this.value) {
                return;
            }
            this.currentValue = val;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.collapse-box{
    &__wrapper{
        min-width: 200px;
        position: relative;
        padding: 16px 16px 0 16px;
        background-color: #F5F5F5;
        border-radius: @formBorderRadius;
        width: 100%;
        &:not(.is-overflow){
            padding-bottom: 16px;
        }
    }
    &__value{
        line-height: 1.5;
        position: relative;
        overflow: hidden;
        overflow-x: auto;
        min-height: 72px;
        transition: height .3s ease;
        width: 100%;
    }
    &__buttons:empty{
        display: none;
    }
    &__buttons--unfold,
    &__buttons--unfold::before{
        // transition: background .5s;
        position: absolute;
        left: 0;
        width: 100%;
        cursor: pointer;
    }
    &__buttons--unfold{
        // z-index: 1;
        color: @primaryColor;
        font-size: 12px;
        bottom: -1px;
        // left: 0;
        padding: 16px;
        background:  #F5F5F5;
        &::before{
            content: '';
            display: block;
            height: 38px;
            top: -38px;
            background: linear-gradient(0deg, #F5F5F5 , rgba(245,245,245,0));
        }
    }
    &__buttons--fold{
        z-index: 1;
        font-size: 12px;
        position: sticky;
        bottom: 62px;
        padding-bottom: 16px;
        .el-button{
            font-size: inherit;
        }
    }
}

</style>
