<template>
    <div
        class="pmp-person"
        :style="`flex-direction: ${direction || 'row'}`">
        <div class="pmp-person-header">
            <img
                v-if="headImage"
                class="pmp-person-img"
                :src="headImage">
            <div v-else class="pmp-person-img">{{ defaultHeadImage }}</div>
            <a
                :class="{'disabled': disabled}"
                :href="disabled ? '#' : `timline://chat/?topin=${erp}`"
                class="pmp-person-link"
            />
        </div>
        <div style="line-height: 1.5;padding: 0 6px;">
            <span v-if="showName">{{ name }}</span>
            <span v-if="showErp">({{ erp }})</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PersonInfo',
    props: {
        disabled: {
            type: Boolean,
            default: false,
        },
        showName: {
            type: Boolean,
            default: true,
        },
        showErp: {
            type: <PERSON>olean,
            default: true,
        },
        direction: {
            type: String,
            default: 'row',
        },
        name: {
            type: String,
            default: '',
        },
        erp: {
            type: String,
            default: '',
        },
        headImage: {
            type: String,
            default: '',
        },
    },
    computed: {
        defaultHeadImage() {
            const chart = this.name || this.erp || '无';
            return chart.substr(0, 1);
        },
    },
};
</script>

<style lang="less">
.pmp-person{
    display: flex;
    align-items: center;
    &-header{
        width: 24px;
        height: 24px;
        position: relative;
        // &:not(.disabled):hover .pmp-person-link{
        //     visibility: visible;
        //     opacity: 1;
        // }
    }
    // &-link{
    //     opacity: 0;
    //     visibility: hidden;
    //     background: url('~@/assets/icons/<EMAIL>') no-repeat;
    //     background-size: cover;
    //     content: ' ';
    //     display: block;
    //     width: 100%;
    //     height: 100%;
    //     position: absolute;
    //     top: 0;
    //     transition: opacity .3s ease;
    // }
    &-img{
        width: 24px;
        height: 24px;
        line-height: 24px;
        border-radius: 50%;
        font-size: 14px;
        font-weight: 999;
        text-align: center;
        color: #fff;
        background-color: #409eff;
    }
    span{
        font-size: 12px;
        line-height: 1;
        color: #666;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
}
</style>
