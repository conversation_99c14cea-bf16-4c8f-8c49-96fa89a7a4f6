<template>
    <div class="jacp-dropdown">
        <el-dropdown
            ref="dropdownIns"
            trigger="click"
            v-show="currentName"
            :placement="menuAlign"
        >
            <span>
                <span
                    class="jacp-dropdown__value"
                    @click="$emit('clickName', $event)"
                >{{ currentName }}</span>
                <i
                    class="el-icon-arrow-down el-icon--right"
                    @click="$emit('clickIcon')"
                />
            </span>
            <el-dropdown-menu slot="dropdown">
                <div
                    class="jacp-dropdown__nodata j-nodata"
                    v-show="!data.length"
                >
                    {{ noDataText }}
                </div>
                <div
                    class="jacp-dropdown__search-outer"
                    v-show="enableSearch"
                >
                    <comp-search
                        class="jacp-dropdown__search"
                        v-model="search"
                        placeholder="快速筛选"
                    />
                </div>
                <div class="jacp-dropdown__item-outer">
                    <template v-if="mode==='group'">
                        <div
                            class="jacp-dropdown__group"
                            v-for="group in filterData"
                            :key="group.id"
                        >
                            <div class="jacp-dropdown__groupname">
                                {{ group.name }}
                            </div>
                            <li
                                class="jacp-dropdown__item"
                                v-for="item in group.childs"
                                v-show="filter(item)"
                                @click="click(item)"
                                :key="item.id"
                            >
                                {{ item.name }}
                            </li>
                        </div>
                    </template>
                    <template v-else>
                        <li
                            class="jacp-dropdown__item"
                            v-show="filter(item)"
                            v-for="item in filterData"
                            @click="click(item)"
                            :key="item.id"
                        >
                            <slot :item="item">
                                {{ item.name }}
                            </slot>
                        </li>
                    </template>
                </div>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>

<script type="text/javascript">
import CompSearch from './Search';

function mergeChildren(arr = []) {
    return arr.reduce((sum, { children = [] }) => sum.concat(children), []);
}

export default {
    name: 'JacpDropdown',
    components: {
        CompSearch,
    },
    props: {
        data: {
            default: () => [],
            type: Array,
        },
        value: [String, Number],
        mode: String, // mode === 'group' | other
        menuAlign: String,
        enableSearch: Boolean,
        noDataText: {
            type: String,
            default: '无数据',
        },
        idAlias: {
            type: String,
            default: 'id',
        },
        filter: {
            type: Function,
            default: () => true,
        },
    },
    data() {
        return {
            current: undefined,
            search: '',
        };
    },
    watch: {
        value: {
            handler(n) {
                this.updateCurrent(n);
            },
            immediate: true,
        },
        data: {
            handler() {
                this.updateCurrent(this.value);
            },
            immediate: true,
        },
        enableSearch(n) {
            if (!n) {
                this.search = '';
            }
        },
    },
    computed: {
        currentName() {
            return this.current && this.current.name ? this.current.name : '请选择';
        },
        filterData() {
            if (this.mode === 'group') {
                return this.data.filter((item) => {
                    const childs = (item.children || []).filter(
                        child => child.name.includes(this.search),
                    );
                    Object.assign(item, { childs });
                    return childs.length > 0;
                });
            }
            return this.data.filter(item => item.name.includes(this.search));
        },
    },
    methods: {
        updateCurrent(id) {
            if (this.current === id) {
                return;
            }
            const data = this.mode === 'group' ? mergeChildren(this.data) : this.data;
            this.current = data.filter(item => item[this.idAlias] === id)[0] || {};
        },
        click(item) {
            this.$refs.dropdownIns.hide();
            this.$emit('select', item);
            if (this.current !== item) {
                this.current = item;
                this.$emit('input', item[this.idAlias]);
                this.$emit('change', item);
            }
        },
    },
};
</script>

<style lang="less">
.jacp-dropdown{
    display: inline-block;
    height: 32px;
    line-height: 32px;
    cursor: pointer;

    &__item {
        list-style: none;
        line-height: 36px;
        padding: 0 10px;
        margin: 0;
        cursor: pointer;

        &:hover{
            background-color: #e5e9f2;
            color: #475669
        }
    }
    &__search-outer{
        padding: 5px 10px 10px;
        border-bottom: 1px solid #eee;
    }
    &__item-outer{
        font-size: 12px;
        max-height: 50vh;
        overflow: auto;
    }
    &__groupname{
        line-height: 30px;
        height: 30px;
        padding: 0 10px;
        color: #999;
        background-color: #f9f9f9;
    }
    &__nodata{
        padding: 10px 0;
    }
}
</style>
