<template>
    <div class="jacp-search">
        <input
            class="jacp-search__input"
            type="text"
            :value="value"
            :placeholder="placeholder"
            @input="emitChange"
            @keydown.enter="emitSearch"
        >
        <transition-group>
            <jacp-icon
                key="search"
                class="icon"
                name="el-icon-search"
                v-show="!value"
            />
            <jacp-icon
                key="clearSearch"
                class="icon clear"
                name="el-icon-circle-close"
                v-show="!!value"
                @click.native="clearSearch"
            />
        </transition-group>
    </div>
</template>

<script>
export default {
    name: 'JacpSearch',
    props: {
        value: {
            default: '',
            type: String,
        },
        placeholder: {
            default: '',
            type: String,
        },
    },
    methods: {
        clearSearch() {
            this.$emit('input', '');
            this.emitSearch();
        },
        emitChange(e) {
            this.$emit('input', e.target.value);
        },
        emitSearch() {
            this.$emit('search', this.value);
        },
    },
};
</script>

<style lang='less'>
.jacp-search{
    position: relative;
}
.jacp-search__input{
    height: 35px;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    outline: none;
    padding: 0 30px 0 10px;

    &:focus{
        border-color: #20A0FF;
    };
}
.jacp-search .icon{
    color: #999;
    position: absolute;
    right: 10px;
    top: 10px;

    &.clear{
        cursor: pointer;
    }
}
</style>
