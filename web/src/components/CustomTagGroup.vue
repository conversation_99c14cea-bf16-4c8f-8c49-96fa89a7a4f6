<template>
    <div class="custom-tag-group">
        <div
            ref="tagsDom"
            :class="['custom-tag-group__item', {
                editing: item == editingTag,
                editable: editable,
            }]"
            v-for="(item, index) in currentValue"
            :key="`${item}-${index}`"
        >
            <custom-tag
                class="view"
                :color="item.color | tag-color"
                :closable="editable && currentValue.length > minCount"
                @dblclick.native="editTag(item)"
                @on-close="handleDelete(item, index)"
                :key="`${item.id}-${item.uTime}`"
            >
                {{ item.name }}
            </custom-tag>
            <div
                v-clickoutside="() => {
                    if (item == editingTag) {
                        handleBlur(item, index);
                    }
                }"
                class="jacp-custom-tag colorful edit"
                :class="{
                    error: item.errorText,
                }"
                :data-color="item.color | tag-color"
            >
                <input
                    class="custom-tag-group__input"
                    :maxlength="maxLength"
                    v-focus="item == editingTag"
                    v-model="item.name"
                    :size="item.name | calc-length"
                    :style="getInputStyle(item)"
                    @input="handleInput($event, item, index)"
                    @blur="doValidate(item, index)"
                    @focus="handleFocus"
                    :readonly="!tagEditable"
                >
                <span class="custom-tag-group__error">{{ item.errorText }}</span>
                <i
                    class="el-icon-close"
                    v-show="editable && currentValue.length > minCount"
                    @click.stop="handleDelete(item, index)"
                />
            </div>
        </div>
        <div
            class="custom-tag-group__add"
            v-if="addable"
        >
            <button
                ref="addButtonEl"
                :class="['custom-tag-group__button', hasError || editingTag ? 'disabled' : '']"
                type="button"
                @click.stop="addTag"
            >
                添加新标签
            </button>
            <color-picker
                v-model="colorPickerVisible"
                :reference="$refs.addButtonEl"
                @on-select="createTagItem"
            />
        </div>
        <ul
            class="custom-tag-group__suggestions el-dropdown-menu"
            ref="suggestionsListEl"
            v-show="suggestionsVisible"
        >
            <li
                class="el-dropdown-menu__item"
                v-for="sug in suggestionsList.slice(0, 5)"
                @click="writeName(sug.name, sug)"
                :key="sug.name"
            >
                {{ sug.name }}
            </li>
        </ul>
    </div>
</template>
<script>
import debounce from 'lodash/debounce';
import { PopupManager } from 'element-ui/src/utils/popup';
import PopperJS from 'element-ui/src/utils/popper';
import Clickoutside from 'element-ui/src/utils/clickoutside';
import { validate, calcLength } from '@/plugins/utils';
import ColorPicker from './CustomTagGroupColorPicker';


const defaultColor = '#2695F1';
const getColor = (color) => {
    if (color && color !== 'NONE') {
        return color;
    }
    return defaultColor;
};
const stop = e => e.stopPropagation();

function filterSuggestions(str, suggestions, value) {
    const selected = (value || []).map(item => item.name);
    if (!str) {
        return suggestions.filter(o => selected.indexOf(o.name) === -1);
    }
    return suggestions.filter((item) => {
        const searchStr = item.name;
        // TODO: 这里拿到的selected一定是包含当前输入的str的，怎么整？
        return searchStr.includes(str) && selected.indexOf(searchStr) === -1;
    });
}
export default {
    name: 'CustomTagGroup',
    props: {
        // addable: Boolean,
        editable: Boolean,
        colorful: Boolean,
        value: {
            type: Array,
            required: true,
        },
        tagEditable: Boolean,
        maxCount: {
            type: [Number, String],
            default: Infinity,
        },
        // currentValue.length不能小于minCount
        minCount: {
            type: [Number, String],
            default: 0,
        },
        validator: {
            type: Function,
            // default: () => {},
        },
        suggestions: Array,
        popperOptions: {
            type: Object,
            default: () => ({
                placement: 'bottom-start',
                offset: 0,
            }),
        },
        rules: Array,
        maxLength: {
            type: [Number, String],
            default: 10,
        },
    },
    components: { ColorPicker },
    data() {
        return {
            colorPickerVisible: false,
            editingTag: null,
            editingTagInputEl: null,
            defaultColor,
            suggestionsList: [],
            currentValue: [],
        };
    },
    directives: {
        focus(el, binding) {
            if (binding.value) {
                el.focus();
            }
        },
        Clickoutside,
    },
    filters: {
        'tag-color': getColor,
        'calc-length': calcLength,
    },
    computed: {
        addable() {
            if (!this.editable) return false;
            return this.value.length < this.maxCount;
        },
        hasError() {
            if (this.currentValue.length) {
                return !this.currentValue.every(item => !item.errorText);
            }
            return false;
        },
        suggestionsVisible() {
            if (!this.suggestions || !this.editingTag) return false;
            return this.suggestionsList.length;
        },
    },
    mounted() {
        const popper = this.$refs.suggestionsListEl;
        /* 在Clickoutside的时候，
        如果context.popperElm并且mouseup.target包含context.popperElm的时候不会执行Clickoutside的绑定事件的，
        所以设置一下popperElm可以判断是否点击的是自己的suggestion，如果是的话就不触发blur */
        this.popperElm = popper;
        popper.addEventListener('click', stop);
    },
    destroyed() {
        if (this.suggestions) {
            this.destroyPopper();
            if (this.$refs.suggestionsListEl) {
                this.$refs.suggestionsListEl.removeEventListener('click', stop);
            }
        }
    },
    methods: {
        createPopper() {
            const reference = this.editingTagInputEl;
            this.destroyPopper();
            this.popperJS = new PopperJS(reference, this.popperElm, this.popperOptions);

            /* eslint-disable no-underscore-dangle */
            this.popperJS._popper.style.zIndex = PopupManager.nextZIndex();
        },
        destroyPopper() {
            if (this.popperJS && this.popperJS.destroy) {
                this.popperJS.destroy();
            }
        },
        validate(item) {
            // 兼容一下空间设置里面的那个校验 TODO: 有时间的话，这个校验可以🙆统一一下形式
            if (this.validator) {
                return this.doValidate(item);
            }
            if (this.rules) {
                return this.doValidateByRules(item);
            }
            return Promise.resolve();
        },
        doValidate(item) {
            let errorText = '';
            if (this.validator) {
                errorText = this.validator.call(this, item, this.currentValue);
                this.$set(item, 'errorText', errorText || undefined);
            }
            return errorText;
        },
        doValidateByRules(item) {
            const vm = this;
            return validate(item, this.rules).then(() => {
                vm.$set(item, 'errorText', undefined);
                return Promise.resolve();
            }).catch(([errorMsg]) => {
                vm.$set(item, 'errorText', errorMsg);
                return errorMsg;
            });
        },
        getInputStyle(item) {
            const color = getColor(item.color);
            return {
                width: item.name === '' ? '24px' : 'auto',
                color,
            };
        },
        // 编辑一个已存在的tag
        editTag(item) {
            if (!this.tagEditable) return;
            if (this.editingTag) return;
            this.originalTagBeforeEditing = JSON.parse(JSON.stringify(item));
            this.editingTag = item;
        },
        resetEdit() {
            if (this.originalTagBeforeEditing) {
                this.originalTagBeforeEditing = null;
            }
            this.editingTag = null;
            this.editingTagInputEl = null;
        },
        doneEdit() {
            this.resetEdit();
            this.$emit('input', JSON.parse(JSON.stringify(this.currentValue)));
        },
        writeName(name) {
            if (this.editingTag) {
                Object.assign(this.editingTag, {
                    name,
                });
                if (this.rules) {
                    this.doValidateByRules(this.editingTag);
                }
            }
            if (!this.tagEditable) {
                this.doneEdit();
            }
        },
        addTag() {
            if (this.colorful) {
                this.colorPickerVisible = true;
                return;
            }
            this.createTagItem(this.defaultColor);
        },
        createTagItem(color) {
            this.editingTag = {
                name: '',
                // color,
            };
            if (this.colorful) {
                this.editingTag.color = color;
            }
            this.currentValue.push(this.editingTag);
            this.$nextTick(() => {
                const inputEl = this.$refs.tagsDom[this.currentValue.length - 1].querySelector('input');
                inputEl.focus();
            });
        },
        handleDelete(item, index) {
            if (this.editingTag && this.editingTag !== item) return;
            this.currentValue.splice(index, 1);
            if (item.id) {
                this.$emit('on-delete', item);
            }
            // 删除当前正在编辑的tag
            if (this.editingTag && this.editingTag === item) {
                this.resetEdit();
            }
            this.$emit('input', JSON.parse(JSON.stringify(this.currentValue)));
        },
        async handleBlur(item, index) {
            if (!this.editingTag) return;
            const errorText = await this.validate(item);
            if (item !== this.editingTag) return;
            if (errorText || item.errorText || item.name === '') return;
            // 编辑原有tag的时候tag值没有发生变化，就不触发更新了
            if (this.originalTagBeforeEditing) {
                if (this.originalTagBeforeEditing.name === item.name) {
                    this.resetEdit();
                    return;
                }
            }
            this.$emit('on-blur', item, index);
            this.doneEdit();
        },
        handleInput: debounce(function handleInput($ev, item) {
            this.validate(item);
        }, 300),
        handleFocus($ev) {
            if ($ev !== this.editingTagInputEl) {
                this.editingTagInputEl = $ev.target;
            }
        },
    },
    watch: {
        editingTag: {
            deep: true,
            handler(val) {
                if (!this.suggestions) return;
                const { suggestions, currentValue } = this;
                if (val) {
                    this.suggestionsList = filterSuggestions(val.name, suggestions, currentValue);
                }
            },
        },
        editingTagInputEl: {
            handler(val) {
                if (val) {
                    this.createPopper();
                } else {
                    this.suggestionsList = [];
                }
            },
        },
        value: {
            immediate: true,
            handler(val = []) {
                if (val === this.currentValue) return;
                this.currentValue = [...val];
            },
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.custom-tag-group{
    display: flex;
    flex-wrap: wrap;
}
.custom-tag-group__add{
    position: relative;
    line-height: 24px;
}
.custom-tag-group__button{
    background-color: #F1F1F1;
    border: 1px solid transparent;
    border-radius: 2px;
    color: @remarkColor;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    padding: 0 6px;
    cursor: pointer;
    &.disabled{
      cursor: default;
      pointer-events: none;
      color: @fontDisabledColor;
    }
    &:not(.disabled):hover{
        background-color: #2695F1;
        color: #fff;
    }
}

.custom-tag-group__item{
    margin: 0 8px 8px 0px;
    &.editable{
        cursor: pointer;
    }
    .edit{
        display: none;
    }
    &.editing{
        /* 编辑状态 */
        & .edit{
            display: block;
            font-size: 12px;
            height: 24px;
            line-height: 24px;
            position: relative;
            .custom-tag-group__input{
                background: transparent;
                border: none;
                max-width: calc(~"100% - 16px");
                padding-left: 10px;
                outline: none;
            }
            .custom-tag-group__error{
                color: @errorColor;
                position: absolute;
                top: 22px;
                left: 0;
                width: fit-content;
                white-space: nowrap;
            }
            & i{
                z-index: 2;
                width: 12px;
                margin-right: 6px;
                transform: scale(.8);
            }
            &.error{
                margin-bottom: 10px;
                box-shadow: 0 0 1px 1px @errorColor;
            }
        }
        /* 显示状态 */
        & .view{
            display: none;
        }
    }
}
/* TODO: 标签的suggestion和errormsg重叠了：suggestion往下挪 覆盖了计算后的行内style，怪怪的 */
.custom-tag-group__suggestions[x-placement="bottom-start"]{
    top: 16px!important;
}
</style>
