<template>
    <span
        :class="['jacp-erp', {
            'has-avatar': avatar,
            'nodata': nodata,
            vertical,
        }]"
        v-if="data.erp || nodata"
    >
        <a
            v-if="avatar"
            :title="`${data.name}(${data.erp})`"
            :class="['jacp-erp__timline', {
                'disabled': disableTimline || nodata,
                'jacp-erp--clearable': clearable,
            }]"
            href="javascript:;"
            :style="{
                width: more ? 'auto' : `${avatarSize}px`,
                height: more ? 'auto' : `${avatarSize}px`,
            }"
        >
            <div
                v-if="more"
                class="jacp-erp-more"
            >
                <jacp-user-avatar
                    :data="data"
                    :size="24"
                    :first-name="!data.headImage"
                    avatar
                />
                <span
                    class="jacp-erp-more-name"
                >{{ data.name || '暂无' }}</span>
                <i
                    v-if="clearable"
                    class="el-icon-error jacp-erp__close"
                    @click.prevent.self="$emit('clear', data)"
                />
            </div>
            <span v-else>
                <jacp-user-avatar
                    :data="data"
                    :size="avatarSize"
                    :first-name="!data.headImage"
                    avatar
                />
                <i
                    v-if="clearable"
                    class="el-icon-error jacp-erp__close"
                    @click.prevent.self="$emit('clear', data)"
                />
            </span>
        </a>
        <!-- <a
            v-else
            :title="`${data.name}(${data.erp})`"
            :class="['jacp-erp__timline j-timline', {
                'j-disabled': disableTimline,
            }]"
            href="javascript:;"
        /> -->
        <span
            class="jacp-erp__name"
            v-show="displayName"
        >{{ data.name || '暂无' }}</span>
        <span
            class="jacp-erp__erp"
            :class="'jacp-erp__erp--' + displayErpMode"
            v-show="displayErp"
        >{{ data.erp }}</span>
    </span>
</template>

<script type="text/javascript">
export default {
    name: 'JacpErp',
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        disableTimline: {
            type: Boolean,
            default: false,
        },
        displayErp: {
            type: Boolean,
            default: false,
        },
        displayName: {
            type: Boolean,
            default: true,
        },
        more: {
            type: Boolean,
            default: false,
        },
        displayErpMode: {
            type: String,
            default: 'inline',
        },
        avatar: Boolean,
        avatarSize: {
            type: Number,
            default: 32,
        },
        clearable: Boolean,
        vertical: Boolean,
        nodata: Boolean,
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.jacp-erp{
    width: fit-content;
    &.nodata{
        pointer-events: none;
        .jacp-erp__timline.disabled{
            & .jacp-user-avatar{
                opacity: .5;
                filter: grayscale(100%);
                filter: gray;
            }
        }
    }
    &:not(.has-avatar) &__timline{
        display: inline-block;
        width: 14px;
        height: 14px;
        margin-top: 1px;
        line-height: 14px;
        vertical-align: middle;
    }
    &__timline.disabled{
        & .jacp-user-avatar{
            opacity: 0.3;
            cursor: default;
        }
    }
    &__name{
        vertical-align: middle;
        white-space: nowrap;
        color: var(--color--base--content);
    }
    &__erp--inline{
        &:before{
            content: '(';
        }
        &:after{
            content: ')';
        }
    }
    &__erp--block{
        display: block;
        padding-left: 16px;
        line-height: 20px;
        color: var(--color--base--description);
        margin-top: -5px;
    }
}
.jacp-erp{
    &-more {
        display: flex;
        border: 1px solid #E1E1E1;
        border-radius: 20px;
        padding: 2px;
        &-name {
            display: inline-block;
            line-height: 24px;
            height: 24px;
            color: var(--color--base--content);
            margin: 0 5px;
        }
    }
    &.has-avatar{
        &.vertical{
            flex-direction: row;
            & .jacp-erp__name{
                margin-left: 4px;
                margin-top: 0px;
                display: inline-block;
            }
        }
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        .jacp-erp__timline{
            // width: 32px;
            // height: 32px;
            position: relative;
            display: inline-block;
            // &:not(.disabled):hover:after{
            //     visibility: visible;
            //     opacity: 1;
            // }
            // &:after{
            //     opacity: 0;
            //     visibility: hidden;
            //     background: url('~@/assets/images/<EMAIL>') no-repeat;
            //     background-size: cover;
            //     content: ' ';
            //     display: block;
            //     width: 100%;
            //     // width: 32px;
            //     // height: 32px;
            //     height: 100%;
            //     position: absolute;
            //     top: 0;
            //     transition: opacity .3s ease;
            // }
        }
        .jacp-erp__name{
            // margin-top: 4px;
            font-size: 12px;
            margin-top: 5px;
        }
    }
}
.jacp-erp--clearable{
    position: relative;
    .jacp-erp__close{
        visibility: hidden;
        position: absolute;
        right: -5px;
        top: 0;
        z-index: 1;
        color: @redColor;
        transition: opacity .3s ease;
        opacity: 0;
    }
    &:hover{
        .jacp-erp__close{
            visibility: visible;
            opacity: 1;
        }
    }
}
</style>
