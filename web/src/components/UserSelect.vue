<template>
    <div>
        <jacp-input-users
            v-bind="$props"
            v-on="$listeners"
            :max-count="maxCount"
        />
    </div>
</template>

<script type="text/javascript">
import UserModel from '@/models/user';

export default {
    name: '<PERSON>ac<PERSON>UserSelect',
    props: {
        placeholder: {
            default: '添加',
            type: String,
        },
        value: {
            default: () => [],
            type: Array,
        },
        beforeAdd: {
            type: Function,
            default: () => {},
        },
        clearable: {
            type: Boolean,
            default: true,
        },
        displayErp: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        loadUsers: {
            type: Function,
            // default: UserModel.search,
            default: UserModel.searchUserInfoFilter,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
        };
    },
    computed: {
        maxCount() {
            return this.multiple ? Infinity : 1;
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
</style>
