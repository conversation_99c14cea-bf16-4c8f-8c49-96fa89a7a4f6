<template>
    <el-dialog
        title="受理人选择"
        :visible.sync="showFlag"
        width="420px"
        :append-to-body="true"
        custom-class="receiver-list"
        @close="$emit('update:showList', false)"
    >
        <div>
            <p class="receiver-tips">
                您所选择受理人部门已开启受理人白名单功能，请从下方正确的受理人中选择
            </p>
            <ul>
                <li
                    v-for="receiver in whiteList"
                    :key="receiver.userErp"
                    @click="selectReceiver(receiver)"
                >
                    <img
                        v-if="receiver.headImage"
                        :key="receiver.userErp"
                        :title="receiver.userName"
                        :src="receiver.headImage"
                    >
                    <div
                        v-else
                        class="jacp-user-avatar__icon"
                        :style="{...sizeStyle, backgroundColor: getBgColor(receiver.userName)}"
                    >
                        {{ firstNameString(receiver.userName) }}
                    </div>
                    <div class="receiver-info">
                        <p class="receiver-name">
                            {{ receiver.userName }}
                        </p>
                        <p
                            class="receiver-org"
                            :title="receiver.orgTierName"
                        >
                            {{ receiver.orgTierName }}
                        </p>
                    </div>
                </li>
            </ul>
        </div>
        <span
            slot="footer"
            class="dialog-footer"
        >
            <el-button
                type="primary"
                size="small"
                plain
                @click="$emit('update:showList', false)"
            >关闭</el-button>
        </span>
    </el-dialog>
</template>
<script>
import { getColorByFirstChar as getBgColor } from '@/plugins/utils.theme';

export default {
    name: 'WhiteList',
    props: {
        showList: {
            type: Boolean,
            default: false,
        },
        whiteList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            showFlag: false,
            getBgColor,
            size: '32',
        };
    },
    computed: {
        sizeStyle() {
            const size = `${this.size.toString()}px`;
            return {
                width: size,
                height: size,
                lineHeight: size,
                fontSize: `${this.size * 0.4}px`,
            };
        },
    },
    methods: {
        selectReceiver(receiver) {
            Object.assign(receiver, {
                erp: receiver.userErp,
                name: receiver.userName,
            });
            this.$emit('select', receiver);
        },
        firstNameString(name) {
            if (name) {
                return name[0];
            }
            return '';
        },
    },
    watch: {
        showList(flag) {
            this.showFlag = flag;
        },
    },
};
</script>
<style lang="less">
.receiver-list{
    & .receiver-tips{
        font-size: 14px;
        padding: 0;
        margin: 0;
        color: #333333;
        line-height: 20px;
    }
    & .receiver-info{
        padding: 8px;
    }
    & .receiver-name{
        font-size: 14px;
        margin: 0;
        line-height: 20px;
    }
    & .receiver-org{
        margin: 0;
        line-height: 16px;
        font-size: 12px;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        color: #999999;
        width: 300px;
    }
    & ul{
        margin-top: 16px;
        list-style: none;
        max-height: 200px;
        overflow-y: auto;
        & li{
            height: 52px;
            font-size: 12px;
            display: flex;
            align-items: center;
            border-bottom: #F1F1F1 1px solid;
            &:first-child{
                border-top: #F1F1F1 1px solid;
            }
            &:hover{
                cursor: pointer;
                background-color: #e9f4fe;
            }
        }
        & img{
            display: inline-block;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #f1f1f1;
            margin-right: 2px;
        }
    }
}
</style>
