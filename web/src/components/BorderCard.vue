<template>
    <div class="border-card-root">
        <div class="border-card-title" v-if="title && position === 'right'">
            <span class="border-card-title__text">{{ title }}</span>
            <div class="border-card-title__extra">
                <slot name="extra" />
            </div>
        </div>
        <div class="border-card-content" :style="{ minHeight: `${minHeight}px`, height: `${height}px` }">
            <slot />
        </div>
    </div>
</template>
<script>
export default {
    name: 'BorderCard',
    props: {
        title: {
            type: String,
            default: () => '',
        },
        minHeight: [Number, String],
        height: [Number, String],
        position: {
            type: String,
            default: 'left',
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.border-card-root {
    position: relative;
    transition: all 0.2s;
    .border-card-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        &__extra {
            font-size: 12px;
            cursor: pointer;
            & a[href] {
                color: @fontSecendColor;
                &:hover {
                    color: @primaryColor;
                }
            }
            & .el-input__inner {
                font-size: 12px;
                color: @fontSecendColor;
            }
            & i {
                color: @fontDisabledColor;
            }
            &:hover i,
            &:hover .el-input__inner {
                color: @primaryColor;
            }
        }
        &__text {
            font-size: 16px;
            color: #303133;
            letter-spacing: 0;
            text-align: justify;
            font-weight: 600;
            line-height: 32px; // element-ui的全局设置成了small以后，为了保留原来的布局，这里恢复成40px
        }
    }
    .border-card-content {
        background-color: #fff;
        font-size: 12px;
        overflow: auto;
        padding: 0 12px;
    }
}
</style>
