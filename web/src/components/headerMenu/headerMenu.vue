<template>
    <div
        :class="{
            'jacp-header-menu': true,
        }"
    >
        <div
            class="jacp-header-menu-list"
            :class="{
                'jacp-header-menu--fixed': fixed,
                'jacp-header-menu--vertical': vertical,
            }"
        >
            <template v-for="item in menus">
                <el-tooltip
                    :content="item.label"
                    :key="item.name"
                    :placement="vertical ? 'right' : 'bottom'"
                    :disabled="item.type === 'dropdown'"
                >
                    <el-dropdown
                        v-if="item.type === 'dropdown'"
                        :key="item.name"
                        trigger="hover"
                        :placement="vertical ? 'right' : 'bottom'"
                    >
                        <i
                            :class="[item.icon]"
                            style="cursor: pointer;"
                            :data-action="item.name"
                        />
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                                v-for="subItem in item.subMenu"
                                :key="subItem.url"
                                v-show="!subItem.disabled"
                                @click.native="subItem.onclick"
                            >
                                <span>{{ subItem.label }}</span>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <router-link
                        class="jacp-header-menu__link"
                        v-else-if="item.type === 'link'"
                        :to="{ name: item.url, query: item.query }"
                        :target="item.target ? item.target : '_blank'"
                    >
                        <i
                            :class="item.icon"
                        />
                    </router-link>
                    <a
                        v-else-if="item.type === 'href'"
                        :href="item.url"
                    >
                        <i
                            :class="item.icon"
                        />
                    </a>
                </el-tooltip>
            </template>
            <!-- <el-tooltip
                v-if="appManager"
                content="控制台"
                key="consoleTab"
                style="margin-right:20px"
                :placement="vertical ? 'right' : 'bottom'"
            >
                <i
                    class="el-icon-monitor"
                    @click="consoleTab"
                />
            </el-tooltip> -->
            <local-user
                class="jacp-header-menu-user"
                :data="userInfo"
            />
        </div>
        <local-product-trend-info
            ref="dlgPreview"
            :lazy-load="showProduct"
        />
        <!-- 新手引导 -->
        <jacp-guide-popper
            ref="guide"
            confirm-button-text=""
            reference="i[data-action='workreport']"
            storagekey="jacp-guide-header-menu"
            cancel-button-type="primary"
            :popper-options="{
                placement: 'right',
                modifiers: [
                    {
                        name: 'offset',
                        options: {
                            offset: [12, 36],
                        },
                    },
                ]
            }"
        >
            <div>【工时填报】、【帮助中心】等小工具移动到了导航栏下方。</div>
        </jacp-guide-popper>
    </div>
</template>
<script>
import SecurityModel from '@/models/security';
import UserModel from '@/models/user';
// import createChat from '@/utils/timline';
import LocalUser from './User';
import LocalProductTrendInfo from './ProductTrendInfo';
// import GlobalSetting from '@/models/globalSetting';
// import { getPermissionUicomponents } from '@/modules/option/modules';
// import AppServer from '@/modules/root/models/appServer';

const { server } = JModule.import('$module.meta');

export default {
    name: 'JacpHeaderMenu',
    components: { LocalUser, LocalProductTrendInfo },
    props: {
        fixed: { type: Boolean, default: true },
        vertical: { type: Boolean, default: false },
    },
    data() {
        return {
            showProduct: false,
            appManager: false,
            userInfo: {},

            menus: [{
                label: '帮助中心',
                icon: 'jacp-icon-help',
                name: 'helpcenter',
                type: 'dropdown',
                url: 'helpcenter',
                jurisdiction: {},
                subMenu: [{
                    url: 'helpcenter',
                    label: '帮助中心',
                    onclick: () => {
                        console.log(this.$store.state.url.helpValue, 'this.$store.state.url.helpValue');
                        if (this.$store.state.url.helpValue !== '') {
                            window.open(`${this.$store.state.url.help}${this.$store.state.url.helpValue}`, '_blank');
                        } else {
                            window.open(`${this.$store.state.url.help}/cn/bizdevops/product-overview`, '_blank');
                        }
                        // this.gotoHelpCenter('helpcenter');
                    },
                }, {
                    url: 'openApi',
                    label: '开放平台',
                    onclick: () => {
                        this.gotoHelpCenter('jacpOpen');
                    },
                },
                // {
                //     label: '产品动向',
                //     icon: 'jacp-icon-navi_ic_guide',
                //     type: 'dialog',
                //     onclick: () => {
                //         this.showProductTrend();
                //     },
                // },
                ],
            },
            ],
            isSdkMode: false,
        };
    },
    mounted() {
        // eslint-disable-next-line no-underscore-dangle
        this.isSdkMode = window.__jagile__env__sdk__;
        // console.log({ isSdkMode: this.isSdkMode });
        // UserModel.getInfo().then((data = {}) => {
        //     this.userInfo = data;
        // });
        // getInfo接口后端有缓存，
        UserModel.getUserInfo(this.$store.state.user.erp).then((data = {}) => {
            this.userInfo = data;
            this.userInfo.headImage = data.headImg;
            this.userInfo.name = data.realName;
            this.userInfo.userName = data.realName;
        });
        if (this.isSdkMode) return;

        // AppServer.getServiceConfig().then(() => {
        //     if (!this.$store.state.app.helpDoc) {
        //         this.menus.forEach((item) => {
        //             if (item.label === '帮助中心') {
        //                 item.subMenu = item.subMenu.filter(res => res.label !== '帮助中心');
        //             }
        //         });
        //     }
        // });
        SecurityModel.getSettingMenu().then((data) => {
            if (!this.$store.state.app.serviveConfig.message) {
                data = data.filter(i => i.code !== 'archNoticeCenter');
            }
            if (data.length !== 0) {
                this.init();
            }
        });
        // SecurityModel.checkPermission(this.$store.state.user.erp, 'change-user').then((data) => {
        //     if (data) {
        //         this.appManager = true;
        //     }
        // });
        // getPermissionUicomponents({
        //     modules: '',
        //     appCode: 'arch',
        // }).then((res) => {
        //     if (res['archOpenPlat:view'] !== 0) {
        //         console.log(this.menus[0].subMenu, 'this.menus.subMenu');
        //         this.menus[0].subMenu = this.menus[0].subMenu.filter(item => item.label !== '开放平台');
        //     }
        // });
    },
    methods: {
        init() {
            UserModel.getMenuAuthor().then((data) => {
                // 判断菜单权限
                if (data.admin.length > 0 && !this.menus.find(item => item.name === 'option')) {
                    this.menus.push({
                        url: `${window.location.origin}/option`,
                        name: 'option',
                        type: 'href',
                        icon: 'jacp-icon-setting',
                        label: '设置',
                        target: '_self',
                    });
                }
                this.$nextTick(() => {
                    // eslint-disable-next-line no-unused-expressions
                    this.$refs.guide && this.$refs.guide.init();
                });
            });
        },
        showProductTrend() {
            this.$refs.dlgPreview.showProductTrend();
        },
        gotoHelpCenter(helper) {
            const routeUrl = this.isSdkMode
                ? { href: `${server}/${helper}` }
                : this.$router.resolve({ name: helper });
            window.open(routeUrl.href, '_blank');
        },
        consoleTab() {
            window.open(this.$store.state.url.security, '_blank');
        },
        gotoPMPWorkReport(isWeek) {
            const path = isWeek ? '/jtime/report/weekly' : '/jtime/report/daily';
            if (this.isSdkMode) {
                window.open(`${server}${path}`, '_blank');
            } else {
                this.$router.push({ path });
            }
        },
    },
};
</script>
<style lang="less" scoped>
.jacp-header-menu{
    &--fixed{
        position: fixed;
        right: 0;
        top: 0;
        padding: 0 24px;
        height: 48px;
        display: flex;
        z-index: 1;
        align-items: center;
    }
    // 横向排列的时候需要通过margin搞定对齐的事情，纵向的时候不不需要，要清除掉所有对齐的margin
    &--vertical{
        flex-direction: column;
        padding-top: var(--gutter--medium);
        padding-bottom: var(--gutter--small);
        &>*{
            margin-bottom: var(--gutter--medium);
        }

        &>*,
        &.jacp-header-menu-list i{
            margin-right: 0!important;
        }
    }

    &-list{
        white-space: nowrap;
        display: flex;
        align-items: center;

        i{
            margin-right: 10px;
            color: #C0C4CC;
            transition: all .3s;
            display: inline-block;
            &:hover{
                transform: scale(1.1);
                color: var(--color--primary--hover);
            }
        }
        /deep/ .el-dropdown{
            margin-right: 16px;
            .icon-edit_fill{
                font-size: 18px;
                transform: translateY(2px);
            }
        }
    }
    &-user{
      /deep/ .el-dropdown{
          margin-right: 0;
      }
    }

    &__link[href]{
        font-size: 14px;
        margin-right: 20px;
    }
}
</style>
