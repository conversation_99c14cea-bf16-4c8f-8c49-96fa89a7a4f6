<template>
    <el-dialog
        :visible.sync="show"
        width="680px"
        class="jacp-product-trend"
        :show-close="false"
        append-to-body
    >
        <div
            class="jacp-product-trend__title"
            slot="title"
        >
            <i
                class="el-icon-close jacp-product-trend__title__close"
                @click="closeProductTrend"
            />
            <img src="@/assets/images/<EMAIL>">
            <span class="main-title-desc">行云 产品动向</span>
            <span class="title-desc">如您在使用中遇到问题，请咨询：<br> {{ supportGroup }}</span>
        </div>
        <div class="jacp-product-trend__content">
            <span class="jacp-product-trend__content__version">{{ innerValue.name }}</span>
            <span class="jacp-product-trend__content__date">
                {{ innerValue.releaseDate | jacp-local-time('YYYY-MM-DD') }}</span>
            <div
                class="jacp-product-trend__content__text"
                v-html="innerValue.content"
            />
        </div>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-button
                @click="closeProductTrend"
                type="primary"
            >
                我知道了
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
import GlobalSetting from '@/models/globalSetting';
import { LocalTime } from '@/filters';

export default {
    name: 'JacpProductTrend',
    props: {
        lazyLoad: {
            type: Boolean,
            default: true,
        },
    },
    filters: {
        'local-time': LocalTime.handler,
    },
    data() {
        return {
            show: false,
            innerValue: () => {},
            supportGroup: GlobalSetting.getSupportGroup(),
        };
    },
    created() {
        // if (!this.lazyLoad) {
        //     if (!localStorage.getItem('showProductTrend')) {
        //         this.getLatestProductTrend();
        //         localStorage.setItem('showProductTrend', '1');
        //     }
        // }
    },
    methods: {
        closeProductTrend() {
            this.show = false;
        },
        // 父组件调用
        showProductTrend(data) {
            if (data) {
                this.innerValue = data;
                this.show = true;
                return;
            }
            if (this.innerValue.length === 0) {
                this.getLatestProductTrend();
                return;
            }
            this.show = true;
        },
        targetNewWindow(url) {
            window.open(url, '_blank');
        },
        getLatestProductTrend() {
            GlobalSetting.getLatestProductTrend().then((info) => {
                // 版本信息存在
                if (info) {
                    this.innerValue = info;
                    this.show = true;
                }
            });
        },
    },
};
</script>

<style lang="less">
    @import '~@/theme/var';
    .jacp-product-trend{
        &__title {
            & img {
                width: 100%;
            }
            &__close {
                color: #999;
                position: absolute;
                right: 16px;
                top: 17px;
                font-size: 16px;
                cursor: pointer;
            }
            & .main-title-desc {
                font-size: 40px;
                font-weight: bold;
                position: absolute;
                left: 0px;
                top: 0px;
                margin: 77px 0 0 40px;
                color: #164D99;
                line-height: 56px;
            }
            & .title-desc {
                font-size: 16px;
                position: absolute;
                left: 0px;
                top: 0px;
                margin: 138px 0 0 40px;
                color: #164D99;
                line-height: 23px;
            }
        }

        &__content {
            padding: 10px 20px 8px;
            min-height: 99px;
            max-height: 191px;
            overflow: auto;
            &__version {
                color: @fontColor;
                font-weight: bold;
                font-size: 20px;
                line-height: 28px;
            }
            &__date {
                color: @fontSecendColor;
                font-size: 14px;
                line-height: 20px;
                margin: 6px 0 2px 6px;
            }
            &__text {
                color: @fontColor;
                font-size: 14px;
                line-height: 20px;
                white-space: pre-wrap;
                word-break: break-all;
                margin-top: 8px;

                & p {
                    margin: 3px 0;
                }
            }
        }
        & .el-dialog__header {
            height: inherit;
            padding: 0;
            line-height: inherit;
        }
    }
    .el-dialog__footer {
            padding: 16px;
            .dialog-footer > .el-button {
                border-radius: 6px;
            }
        }
</style>
