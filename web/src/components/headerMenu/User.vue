<template>
    <div class="jacp-user">
        <el-dropdown
            trigger="click"
            @command="click"
            v-show="data.userName"
            placement="bottom"
        >
            <span class="jacp-user__name">
                <Local-Avatar
                    :data="data"
                    :first-name="!data.headImage"
                    :size="24"
                    avatar
                />
            </span>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                    command="accountSetting"
                    class="jacp-user__item"
                >
                    <i class="el-icon-setting" />账号设置
                </el-dropdown-item>
                <el-dropdown-item
                    command="logout"
                    class="jacp-user__item"
                >
                    <i class="el-icon-place" />退出登出
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>
<script>
import LocalAvatar from '@/components/Avatar';
import UserModel from '@/models/user';
import Cookies from 'js-cookie';
import jsonp from 'jsonp';


export default {
    name: 'JacpUser',
    props: {
        data: {
            default: () => ({}),
            type: Object,
        },
    },
    components: {
        LocalAvatar,
    },
    methods: {
        click(cmd) {
            switch (cmd) {
            case 'logout':
                // UserModel.logout();
                this.handleLogout();
                this.$emit('logout');
                break;
            case 'accountSetting':
                this.$router.push({
                    name: 'accountSetting',
                });
                break;
            default:
                break;
            }
        },

        logoutPage() {
            window.location.href = window.location.origin;
        },
        async handleLogout() {
            UserModel.ssoLogout().then((data) => {
                if (data.includes('callback')) {
                    const url = data.split('?')[0];
                    jsonp(url, '', this.logoutPage);
                } else {
                    window.location.href = data;
                }
                const localData = [
                    'Authorization', // 用户登录认证信息
                    'refreshToken', // 用户重新认证信息
                    'tenantId', // 租户ID
                    'expiresIn', // 用户登录信息过期时间
                    'jacp_org_suggestion', // 需求管理需求来源部门常用推荐部门
                    'showProductTrend', // 是否展示产品动向
                    'jacp-space_recents', // 最近访问团队空间
                    'jacp_calandar_ordCode', // 人力资源日历使用的部门信息
                    'jacp_calandar_orgName', // 人力资源日历使用的部门信息
                    'jacp_calandar_orgFullName', // 人力资源日历使用的部门信息
                    'caseColumnOptions', // 卡片关联用例列宽
                    'jstage_stage_current', // 项目管理阶段数据
                    'userInfo', // 用户信息
                    'version_releaseTime', // 版本信息
                ];
                this.removeLocalStorage(localData);
            });
            Cookies.remove('LastTime');
            Cookies.remove('ARCH-TOKEN');
            // localStorage.removeItem('Authorization');
        },
        removeLocalStorage(localData = []) {
            localData.forEach((item) => {
                if (localStorage.getItem(item)) {
                    localStorage.removeItem(item);
                }
            });
        },
    },
};
</script>
<style lang="less">
    .jacp-user{
        cursor: pointer;
    }
</style>
