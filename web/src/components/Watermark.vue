<template>
    <div class="watermark-outer">
		<div
			class="watermark"
			v-for="i in 200"
			:key="i"
	    >
			<div>{{ text }}</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'WaterMark',
	props: {
		text: {
		    type: String,
		}
	},
}
</script>
 
<style>
.watermark-outer {
	position: fixed;
	top: 10px;
	left: 50px;
	width: 5500px;
	height: 2000px;
	z-index: 10000;
	pointer-events: none;
}
.watermark {
	margin: 10px;
	width: 350px;
	float: left;
	height: 160px;
	color: rgba(17, 17, 17, 0.07);
	transform: rotate(-20deg);
	font-family: "Microsoft Yahei",serif;
	font-size: 14px;
}
</style>
 