<template>
    <div
        class="todolist"
    >
        <div
            :class="[{
                'todolist-itemlist__border': value.length && editing !== item}, { checkable}]"
            v-for="item in value"
            :key="item.id"
        >
            <!-- 编辑状态 -->
            <el-form-item
                :prop="propName"
                :error="editing.errorMsg"
                v-if="editing === item"
                v-clickoutside="cancelAdding"
            >
                <div class="todolist-item">
                    <el-checkbox
                        v-if="checkable"
                        v-model="item.finished"
                        :true-label="1"
                        :false-label="0"
                        disabled
                    />
                    <el-input
                        class="todolist-item__input"
                        v-focus="editing === item"
                        :autosize="autosize"
                        :placeholder="placeholder"
                        type="textarea"
                        v-model.trim="editing.name"
                    />
                    <slot
                        name="edit"
                        :item="item"
                    />
                </div>
                <form-button-group
                    @confirm="save(item)"
                    @cancel="cancel(item)"
                />
            </el-form-item>
            <!-- 显示状态 -->
            <form-wrapper-toolbar
                v-else
                :disabled="disabled"
                :tools="['edit', 'del']"
                :offset="1"
                @on-tool-click="$emit($event, item)"
                placement="right-start"
            >
                <div
                    slot="reference"
                    class="todolist-item"
                >
                    <el-checkbox
                        v-if="checkable"
                        v-model="item.finished"
                        :true-label="1"
                        :false-label="0"
                        @change="$emit('on-save', item);"
                    />
                    <slot
                        name="item"
                        :item="item"
                    >
                        <div
                            v-if="item.name"
                            class="todolist-item__name"
                            @click="disableOnClickEdit ? void 0 : () => edit(item)"
                            v-html="$xss(item.name).replace(/\r?\n/g, '<br />')"
                        />
                    </slot>
                </div>
            </form-wrapper-toolbar>
        </div>
        <span
            v-if="disabled && !value.length"
            class="jacp-form--nodata"
        >暂无</span>
        <el-button
            type="text"
            @click="add"
            :disabled="Boolean(editing)"
            v-show="!disabled"
        >
            {{ addLabel }}
        </el-button>
    </div>
</template>
<script>
import Clickoutside from 'element-ui/src/utils/clickoutside';
import FormWrapperToolbar from './FormsLatest/FormWrapperToolbar';
import TodolistItem from './models/TodolistItem';

export default {
    name: 'Todolist',
    inheritAttrs: false,
    components: { FormWrapperToolbar },
    inject: ['elForm'],
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        disabled: Boolean,
        checkable: Boolean,
        autosize: {
            type: [Boolean, Object],
            default: () => ({ minRows: 1, maxRows: 4 }),
        },
        rules: [Object, Array],
        addLabel: {
            type: String,
            default: '+添加',
        },
        propName: {
            type: String,
            default: 'criterias',
        },
        placeholder: String,
        autoAdd: {
            type: Boolean,
            default: true,
        },
        disableOnClickEdit: { type: Boolean, default: false },
        disableClickoutside: { type: Boolean, default: false },
        onAdd: { type: Function, default: () => new TodolistItem() },
        onDelete: { type: Function, default: () => true },
    },
    directives: {
        focus: (el, binding, { context }) => {
            if (binding.value) {
                const input = el.querySelector('textarea, input');
                context.$nextTick(() => {
                    input.focus();
                });
            }
        },
        Clickoutside,
    },
    data() {
        return {
            editing: null,
        };
    },
    mounted() {
        this.$on('edit', this.edit);
        this.$on('del', this.del);
    },
    computed: {
        validateDisabled() {
            return !this.rules || !this.elForm || !this.propName;
        },
    },
    methods: {
        add() {
            if (this.editing) {
                return;
            }
            this.editing = this.onAdd();
            this.value.push(this.editing);
            this.adding = true;
        },
        doValidate() {
            if (this.validateDisabled) {
                return Promise.resolve(false);
            }
            return new Promise((resolve) => {
                this.elForm.validateField([this.propName], (errorMsg) => {
                    if (errorMsg) {
                        Object.assign(this.editing, {
                            errorMsg,
                        });
                    }
                    resolve(errorMsg);
                });
            });
        },
        edit(item) {
            if (this.disabled) {
                return;
            }
            if (this.editing) {
                return;
            }
            this.beforeEditCache = item.name;
            this.editing = item;
            this.adding = false;
        },
        del(item) {
            let resolved = false;
            const done = () => {
                if (resolved) {
                    return;
                }
                this.$emit('on-delete', item);
                this.value.splice(this.value.indexOf(item), 1);
                resolved = true;
            };
            if (this.onDelete) {
                Promise.resolve(this.onDelete(item, done)).then(done);
            }
        },
        async save() {
            const valid = await this.doValidate();
            if (valid) {
                return;
            }
            if (!this.editing.name) {
                return;
            }
            this.clearValidate();
            this.$emit('on-save', this.editing);
            // this.$emit('input', this.value);
            this.editing = null;
            if (this.autoAdd && this.adding) {
                this.add();
            }
        },
        cancel(item) {
            this.clearValidate();
            this.editing = null;
            if (this.beforeEditCache) {
                Object.assign(item, {
                    name: this.beforeEditCache,
                });
                // this.editing = {};
                this.beforeEditCache = null;
            } else {
                this.value.pop();
            }

            if (this.validateDisabled) {
                return;
            }
            this.elForm.clearValidate([this.propName]);
        },
        clearValidate() {
            if ('errorMsg' in this.editing) {
                delete this.editing.errorMsg;
            }
        },
        // 点击外侧的时候，默认新增的那条数据, 在没有进行任何编辑的时候，要被干掉
        cancelAdding() {
            if (this.disableClickoutside) {
                return;
            }
            if (!this.autoAdd) {
                return;
            }
            if (!this.adding) {
                return;
            }
            if (!this.editing || this.editing.name) {
                return;
            }
            this.cancel();
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.todolist+.el-form-item__error{
  display: none;
}
.todolist-itemlist__border:nth-last-of-type(1){
    .todolist-item__name{
        border-bottom: @borderStyleSecend;
    }
}
.todolist-item{
  display: flex;
  align-items: flex-start;
  position: relative;
  & .el-checkbox{
    padding: 0px;
    .el-checkbox__inner{
      border-radius: 50%;
    }
  }
  & .el-checkbox+.todolist-item__name,
  & .el-checkbox+.todolist-item__input{
    margin-left: 4px;
  }
  .todolist-item__input,
  .todolist-item__name{
    font-size: 12px;
    color: @fontColor;
    flex-basis: 100%;
    border-radius: 4px;
    .el-textarea__inner{
      padding-left: 4px;
    }
  }
  .todolist-item__name{
    &:hover{
      background-color: @hoverBgColor;
      border-top-color: transparent;
    }
    padding: 7px 4px;
    line-height: 1.5;
    min-height: 32px;
    border-top: @borderStyleSecend;
  }
  &+.form-button-group{
    margin-bottom: 12px;
    &+.el-form-item__error{
      top: calc(~"100% - 56px");
    }
  }
}
.checkable{
    .el-form-item__error{
      left: 16px;
    }
  }
</style>
