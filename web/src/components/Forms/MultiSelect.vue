<template>
    <Wrapper
        class="jacp-form-multiselect"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form-multiselect__value jacp-form__value"
            :class="{
                'jacp-form--nodata': !hasValue
            }"
        >
            <jacp-inline-list
                v-show="hasValue"
                :data="value"
                :disable-remove="true"
            />
            <span v-show="!hasValue">{{ noDataText }}</span>
        </div>
        <div slot="edit">
            <jacp-input-multi-selector
                v-model="value"
                @input="input"
                :filter="filter"
                :item-render="itemRender"
            />
        </div>
    </Wrapper>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import Wrapper from './Wrapper';

export default {
    name: 'JacpMultiSelect',
    mixins: [Form],
    components: {
        Wrapper,
    },
    props: {
        filter: {
            type: Function,
        },
        itemRender: {
            type: Function,
        },
    },
    data() {
        return {
            editMode: false,
            originValue: JSON.stringify(this.value || []),
            lockWatch: false,
        };
    },
    methods: {
        blur() {
            this.editMode = false;
            const newValue = JSON.stringify(this.value || []);
            if (newValue !== this.originValue) {
                this.$emit('change', this.value);
                this.originValue = newValue;
            }
        },
        focus() {
            this.editMode = true;
        },
        input() {
            this.lockWatch = true;
        },
    },
    computed: {
        hasValue() {
            return this.value && this.value.length;
        },
    },
    watch: {
        value(n) {
            if (!this.lockWatch) {
                this.originValue = JSON.stringify(n || []);
            } else {
                this.lockWatch = false;
            }
        },
    },
};
</script>

<style lang="less">
.jacp-form-multiselect__value .jacp-inline-list__item{
    color: #000;
}
</style>
