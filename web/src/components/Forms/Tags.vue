<template>
    <Wrapper
        class="jacp-form-tags"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form__value"
            :class="{
                'jacp-form--nodata': !value || !value.length
            }"
        >
            <span v-show="value && value.length">
                <custom-tag
                    :class="['jacp-form-tags__item']"
                    :key="item.name"
                    v-for="item in value"
                    :color="getTagColor(item)"
                >{{ item.name }}</custom-tag>
            </span>
            <span v-show="!value || !value.length">{{ noDataText }}</span>
        </div>
        <div slot="edit">
            <component
                :is="componentName"
                v-model="value"
                @input="input"
                ref="tagsIns"
                :value-width="308"
                :tags-metadata="tagsMetadata"
                :placeholder="placeholder"
                :max-length="maxLength"
                :item-render="itemRender"
                :filter="filter"
            />
        </div>
    </Wrapper>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import Wrapper from './Wrapper';

export default {
    name: 'JacpTags',
    mixins: [Form],
    components: {
        Wrapper,
    },
    props: {
        selectable: Boolean,
        maxCount: {
            default: Infinity,
            type: Number,
        },
        maxLength: {
            default: Infinity,
            type: Number,
        },
        tagsMetadata: {
            default: () => [],
            type: Array,
        },
        filter: {
            type: Function,
            default: (str, cb) => {
                cb([]);
            },
        },
        itemRender: {
            type: Function,
        },
    },
    data() {
        const isElFormItem = this.$parent.$options.componentName === 'ElFormItem';
        return {
            useElFormValidator: !this.rules && isElFormItem,
            errorMsg: undefined,
            editMode: false,
            originValue: JSON.stringify(this.value || []),
            lockWatch: false,
        };
    },
    computed: {
        componentName() {
            return this.selectable ? 'jacp-input-tags-selector' : 'jacp-input-tags';
        },
    },
    methods: {
        getTagColor(item) {
            // selectable的有颜色区别
            if (this.selectable && item.color && item.color !== 'NONE') {
                return item.color;
            }
            return '#2695F1';
        },
        triggerChange() {
            const newValue = JSON.stringify(this.value || []);
            if (newValue !== this.originValue) {
                this.$emit('change', this.value);
                this.originValue = newValue;
            }
        },
        blur() {
            // 使用el表单校验
            if (this.useElFormValidator) {
                // 如果校验出错或未完成，不触发change事件
                if (['error', 'validating'].includes(this.$parent.validateState)) {
                    return;
                }
                // 如果校验成功
                this.editMode = false;
                this.triggerChange();
                return;
            }
            // 使用自带校验
            if (!this.$refs.tagsIns.hasError) {
                this.editMode = false;
                this.triggerChange();
            }
        },
        focus() {
            this.editMode = true;
        },
        input() {
            this.lockWatch = true;
        },
    },
    watch: {
        value() {
            if (!this.lockWatch) {
                this.originValue = JSON.stringify(this.value || []);
            } else {
                this.lockWatch = false;
            }
        },
    },
};
</script>

<style lang="less">
.jacp-form-tags{
    &__item{
        margin-right: 10px;
        margin-bottom: 5px;
    }
}
</style>
