<template>
    <jacp-form-wrapper-latest
        toolbar
        :class="{
            'jacp-form-input': editMode
        }"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form__value"
            :class="{
                'jacp-form--nodata': isEmpty(value)
            }"
        >
            <span v-if="!isEmpty(value)">{{ value }}</span>
            <span v-else>{{ noDataText }}</span>
        </div>
        <div
            slot="edit"
            :class="{
                'jacp-form-input__edit': true,
                'jacp-form-input__error': errorMsg
            }"
        >

            <!-- <user-selector
                v-model="innerValue"
                v-bind="$attrs"
                :space-members="users"
                :show-erp="showErp"
            /> -->
            <el-autocomplete
                v-model="innerValue"
                v-bind="$attrs"
                :fetch-suggestions="querySearch"
                :placeholder="placeholder"
            />
            <div
                class="el-form-item__error"
                v-show="errorMsg"
            >
                {{ errorMsg }}
            </div>
        </div>
    </jacp-form-wrapper-latest>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import { validate } from '@/plugins/utils';

export default {
    name: 'JacpAutocomplete',
    mixins: [Form],
    props: {
        rules: [Object, Array],
    },
    data() {
        return {
            errorMsg: undefined,
            editMode: false,
            innerValue: this.value,
            lockWatch: false,
            placeholder: {
                type: String,
                default: '请输入内容',
            },
            // suggestions 数组项必须是 带有 value 的 object
            suggestions: {
                type: Array,
                default: () => [],
            },
        };
    },
    methods: {
        isEmpty(val) {
            return val === undefined || val === null || val === '';
        },
        // triggerChange() {
        //     if (this.innerValue !== this.value) {
        //         this.$emit('change', this.value);
        //         this.innerValue = this.value;
        //     }
        // },
        blur() {
            const oldValue = this.value;
            // 值发生改变 或者 没有值（当前没选中人）的时候
            if (this.value !== this.innerValue || !this.value) {
                if (this.rules) {
                    validate(this.innerValue, this.rules).then(() => {
                        this.errorMsg = undefined;
                        this.editMode = false;
                        this.$emit('input', this.innerValue);
                        this.$emit('change', this.innerValue, oldValue);
                    }).catch(([errorMsg]) => {
                        this.errorMsg = errorMsg;
                    });
                } else {
                    this.editMode = false;
                    this.$emit('input', this.innerValue);
                    this.$emit('change', this.innerValue, oldValue);
                }
            } else {
                this.editMode = false;
            }
        },
        input() {
            this.lockWatch = true;
        },
        focus() {
            this.editMode = true;
        },

        // autocomplete methods
        querySearch(queryString, cb) {
            const { suggestions, createFilter } = this;
            const results = queryString
                ? suggestions.filter(createFilter(queryString))
                : suggestions;
            cb(results);
        },
        createFilter(queryString) {
            return item => item.value.includes(queryString);
        },
    },
    watch: {
        value() {
            if (!this.lockWatch) {
                this.innerValue = this.value;
            } else {
                this.lockWatch = false;
            }
        },
    },
};
</script>
