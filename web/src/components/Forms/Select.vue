<template>
    <jacp-form-wrapper-latest
        toolbar
        :class="{
            'jacp-form-select': true,
        }"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form__value"
            :class="{
                'jacp-form--nodata': noValue
            }"
        >
            <span v-show="!noValue">{{ valueAlias }}</span>
            <span v-show="noValue">{{ noDataText }}</span>
        </div>
        <div
            slot="edit"
            :class="{
                'jacp-form-input__edit': true,
            }"
        >
            <jacp-input-select
                :filterable="filterable"
                v-model="innerValue"
                @input="validate"
                :data="data"
                :clearable="clearable"
                :placeholder="placeholder"
                :show-code="showCode"
                :option-value-key="optionValueKey"
            />
            <div
                class="el-form-item__error"
                v-show="errorMsg"
            >
                {{ errorMsg }}
            </div>
        </div>
    </jacp-form-wrapper-latest>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import { validate } from '@/plugins/utils';
// import Wrapper from './Wrapper';

function isNull(val) {
    return val === undefined || val === null || val === '';
}
export default {
    name: 'JacpSelect',
    mixins: [Form],
    components: {
        // Wrapper,
    },
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearable: {
            type: Boolean,
            default: false,
        },
        filterable: {
            type: Boolean,
            default: false,
        },
        showCode: {
            type: Boolean,
            default: false,
        },
        optionValueKey: { type: String, default: 'id' },
    },
    data() {
        const isElFormItem = this.$parent.$options.componentName === 'ElFormItem';
        this.$parent.validate = (trigger, callback) => callback; // 禁用ele自己的错误校验
        return {
            errorMsg: undefined,
            editMode: false,
            innerValue: this.value,
            validating: false,
            rules: !this.rules && isElFormItem ? this.$parent.getRules() : this.rules,
            validateDefer: Promise.resolve(),
        };
    },
    methods: {
        blur() {
            this.validateDefer.then(() => {
                this.editMode = false;
                if (this.innerValue !== this.value) {
                    this.$emit('input', this.innerValue);
                    this.$emit('change', this.innerValue, this.dataMap[this.innerValue]);
                }
            });
        },
        focus() {
            this.$emit('focus');
            this.editMode = true;
        },
        validate(newValue) {
            // 如果与原始值相同，不使用校验
            if (newValue === this.value || (this.noValue && isNull(newValue))) {
                this.errorMsg = undefined;
                return;
            }

            this.validateDefer = validate(newValue, this.rules).then(() => {
                this.errorMsg = undefined;
            }).catch((errorMsg) => {
                this.errorMsg = errorMsg[0];
                throw new Error(this.errorMsg);
            });
        },
    },
    computed: {
        noValue() {
            return isNull(this.value);
        },
        dataMap() {
            const res = {};
            this.data.forEach((item) => {
                res[item[this.optionValueKey]] = item;
            });
            return res;
        },
        valueAlias() {
            return (this.dataMap[this.value] || {}).name || this.value;
        },
    },
    watch: {
        value(n) {
            if (n !== this.innerValue) {
                this.innerValue = n;
            }
        },
    },
};
</script>
