<template>
    <Wrapper
        class="jacp-form-users"
        :edit-mode="editMode"
        :disabled="disabled"
        @mousedown.native.capture="preventFocus"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form__value"
            :class="{
                'jacp-form--nodata': !value || !value.length
            }"
        >
            <jacp-erp
                v-for="user in value"
                :key="user.erp"
                class="jacp-form-users__item"
                :disable-timline="disableUser(user)"
                :data="user"
            />
            <span v-if="!value || !value.length">{{ noDataText }}</span>
        </div>
        <div slot="edit">
            <jacp-input-users
                v-model="value"
                :placeholder="placeholder"
                @input="input"
            />
        </div>
    </Wrapper>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import Wrapper from './Wrapper';

export default {
    name: 'JacpFormUsers',
    mixins: [Form],
    components: {
        Wrapper,
    },
    props: {
        noTimlineUsers: {
            type: Array,
            default: () => [],
        },
        maxCount: {
            default: Infinity,
            type: Number,
        },
    },
    data() {
        return {
            editMode: false,
            locked: false,
            originValue: JSON.stringify(this.value || []),
            lockWatch: false,
        };
    },
    methods: {
        updateOriginValue() {
            this.originValue = JSON.stringify(this.value || []);
        },
        preventFocus(e) {
            if (e.target.classList.contains('j-timline')) {
                this.locked = true;
            }
        },
        blur() { // 暂无校验功能
            this.editMode = false;
            if (JSON.stringify(this.value || []) !== this.originValue) {
                this.$emit('change', this.value);
                this.updateOriginValue();
            }
        },
        focus() {
            if (this.locked) {
                this.locked = false;
                return;
            }
            this.editMode = true;
        },
        disableUser(user) {
            const filteredErps = this.noTimlineUsers.map(item => item.erp.toLowerCase());
            return filteredErps.includes(user.erp.toLowerCase());
        },
        input() {
            this.lockWatch = true;
        },
    },
    watch: {
        value() {
            if (!this.lockWatch) { // change by parent component
                this.updateOriginValue();
            } else { // change by input event
                this.lockWatch = false;
            }
        },
    },
};
</script>

<style lang="less">
.jacp-form-users{
    &__item{
        line-height: 20px;
        font-size: 12px;
        display: inline-block;

        &:after{
            content: '，';
        }
        &:last-child:after{
            content: '';
        }
    }
}
</style>
