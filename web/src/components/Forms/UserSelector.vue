<template>
    <jacp-form-wrapper-latest
        toolbar
        :class="{
            'jacp-form-select': editMode
        }"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form__value"
            :class="{
                'jacp-form--nodata': isEmpty(value)
            }"
        >
            <span v-if="!isEmpty(value)">
                {{ showErp ? `${user.name}(${user.erp})` : user.name }}
            </span>
            <span v-else>{{ noDataText }}</span>
        </div>
        <div
            slot="edit"
            :class="{
                'jacp-form-input__edit': true,
                'jacp-form-input__error': errorMsg
            }"
        >
            <user-selector
                v-model="innerValue"
                v-bind="$attrs"
                :space-members="users"
                :show-erp="showErp"
            />
            <div
                class="el-form-item__error"
                v-show="errorMsg"
            >
                {{ errorMsg }}
            </div>
        </div>
    </jacp-form-wrapper-latest>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import { validate } from '@/plugins/utils';
import UserSelector from '@/modules/card/components/userSelector';


export default {
    name: 'JacpUserSelector',
    mixins: [Form],
    components: {
        UserSelector,
    },
    props: {
        rules: [Object, Array],
        users: Array,
        showErp: Boolean,
        currentUser: Object,
    },
    data() {
        return {
            errorMsg: undefined,
            editMode: false,
            innerValue: this.value,
            lockWatch: false,
        };
    },
    computed: {
        user({ users, value, currentUser }) {
            return users.find(user => user.erp === value)
                || currentUser; // 初始化回显，当前选项中没有 erp 为 value 的，则使用 currentUser
        },
    },
    methods: {
        isEmpty(val) {
            return val === undefined || val === null || val === '';
        },
        // triggerChange() {
        //     if (this.innerValue !== this.value) {
        //         this.$emit('change', this.value);
        //         this.innerValue = this.value;
        //     }
        // },
        blur() {
            const oldValue = this.value;
            // 值发生改变 或者 没有值（当前没选中人）的时候
            if (this.value !== this.innerValue || !this.value) {
                if (this.rules) {
                    validate(this.innerValue, this.rules).then(() => {
                        this.errorMsg = undefined;
                        this.editMode = false;
                        this.$emit('input', this.innerValue);
                        this.$emit('change', this.innerValue, oldValue);
                    }).catch(([errorMsg]) => {
                        this.errorMsg = errorMsg;
                    });
                } else {
                    this.editMode = false;
                    this.$emit('input', this.innerValue);
                    this.$emit('change', this.innerValue, oldValue);
                }
            } else {
                this.editMode = false;
            }
        },
        input() {
            this.lockWatch = true;
        },
        focus() {
            this.editMode = true;
        },
    },
    watch: {
        value() {
            if (!this.lockWatch) {
                this.innerValue = this.value;
            } else {
                this.lockWatch = false;
            }
        },
    },
};
</script>
