<template>
    <jacp-form-wrapper-latest
        toolbar
        :class="{
            'jacp-form-datepicker': true,
            'jacp-form-input--edit': editMode
        }"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form__value"
            :class="{
                'jacp-form--nodata': !value
            }"
        >
            <span v-if="value && !Array.isArray(value)">
                {{ value|jacp-local-time('YYYY-MM-DD') }}
            </span>
            <span v-if="value && Array.isArray(value) && value.length">
                {{ value[0] | jacp-local-time('YYYY-MM-DD') }}
                -
                {{ value[1] | jacp-local-time('YYYY-MM-DD') }}
            </span>
            <span v-show="!value">{{ noDataText }}</span>
        </div>
        <div
            slot="edit"
            :class="{
                'jacp-form-input__edit': true,
                'jacp-form-input__error': errorMsg
            }"
        >
            <el-date-picker
                ref="pickerEl"
                style="width: 100%"
                :type="type"
                :popper-class="disabledReason ? 'jacp-form-datepicker__popper' : ''"
                v-bind="[$attrs]"
                :placeholder="placeholder"
                v-model="innerValue"
                @input="input"
                :picker-options="pickerOptions"
                @change="change"
                @focus="handlePickerInputFocus"
                @keydown.enter.native.prevent
            />
            <div
                class="el-form-item__error"
                v-show="errorMsg"
            >
                {{ errorMsg }}
            </div>
        </div>
    </jacp-form-wrapper-latest>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import Vue from 'vue';
import { validate } from '@/plugins/utils';

/**
 * 根据目标位置生成一个假的div覆盖在原有的cell上，作为tooltip的reference
 * @param {HTMLElement} target
 */
const getPositionStyles = targetEl => ({
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    // 这个不准
    backgroundColor: '#000',
    position: 'absolute',
    zIndex: targetEl.style.zIndex + 1,
    opacity: 0,
});

/**
 * @param {HTMLElement} target 要插入tooltip reference的目标位置
 * @param {string} content
 */
const getDisabledTipsConstructor = (targetEl, content) => Vue.extend({
    methods: {
        isTipsDisabled() {
            this.tipsDisabled = !targetEl.classList.contains('disabled');
            return this.tipsDisabled;
        },
    },
    mounted() {
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach(this.isTipsDisabled);
        });
        this.observer.observe(targetEl, {
            attributes: true,
        });
    },
    beforeDestroy() {
        this.observer.disconnect();
    },
    data() {
        return { tipsDisabled: this.isTipsDisabled() };
    },
    render() {
        const directives = [{
            name: 'popover',
            value: 'tooltipReference',
        }];
        // eslint-disable-next-line no-param-reassign
        targetEl.style.position = 'relative';
        const style = getPositionStyles(targetEl);
        const vNode = (
            <el-tooltip
                ref='tooltipReference'
                content={content}
                disabled={this.tipsDisabled} >
                <div {...{ directives, style }} ></div></el-tooltip>);
        return vNode;
    },
    destroyed() {
        targetEl.removeChild(this.$el);
    },
});

/**
 * @param {HTMLElement} target datepicker的弹出层
 * @param {string} content
 * @return {object} { destroy } 用于销毁拆入的一堆tooltip
 */
function initDisabledTooltip(targetEl, content = '') {
    if (!targetEl) return null;
    const targetCell = targetEl.querySelectorAll('.el-date-table__row>td');
    const ins = Array.from(targetCell).map((node) => {
        const T = getDisabledTipsConstructor(node, content);
        const $t = new T();
        $t.$mount();
        node.appendChild($t.$el);
        return $t;
    });
    return {
        destroy: () => {
            ins.forEach(i => i.$destroy());
        },
    };
}
export default {
    name: 'JacpDate',
    inheritAttrs: false,
    mixins: [Form],
    components: {
        // Wrapper,
    },
    props: {
        rules: [Object, Array],
        pickerOptions: Object,
        disabledReason: { type: String, default: '' },
        type: { type: String, default: 'date' },
        startPlaceholder: { type: String, default: '' },
        endPlaceholder: { type: String, default: '' },
    },
    data() {
        return {
            errorMsg: undefined,
            editMode: false,
            innerValue: this.value,
            lockWatch: false,
        };
    },
    methods: {
        blur() {
            const oldValue = this.value;
            const isDateRangeEmpty = Array.isArray(this.innerValue)
                && this.innerValue.every(v => !v);
            if (this.value !== this.innerValue || isDateRangeEmpty) {
                if (this.rules) {
                    validate(this.innerValue, this.rules).then(() => {
                        this.errorMsg = undefined;
                        this.editMode = false;
                        this.$emit('input', this.innerValue);
                        this.$emit('change', this.innerValue, oldValue);
                    }).catch(([errorMsg]) => {
                        this.errorMsg = errorMsg;
                    });
                } else {
                    this.editMode = false;
                    this.$emit('input', this.innerValue);
                    this.$emit('change', this.innerValue, oldValue);
                }
            } else {
                this.editMode = false;
            }
            if (this.$tip) {
                this.$tip.destroy();
                this.$tip = null;
            }
        },
        change(value) {
            if (this.rules) {
                validate(value, this.rules).then(() => {
                    this.errorMsg = undefined;
                }).catch(([errorMsg]) => {
                    this.errorMsg = errorMsg;
                });
            }
        },
        input() {
            this.lockWatch = true;
        },
        focus() {
            this.editMode = true;
        },
        handlePickerInputFocus() {
            if (this.disabledReason && !this.$tip) {
                this.$nextTick(() => {
                    this.$tip = initDisabledTooltip(document.querySelector('.jacp-form-datepicker__popper'), this.disabledReason);
                });
            }
        },
    },
    destroyed() {
        if (this.$tip) {
            this.$tip.destroy();
            this.$tip = null;
        }
    },
    watch: {
        value(n) {
            if (!this.lockWatch) {
                this.innerValue = n;
            } else {
                this.lockWatch = false;
            }
        },
        disabledReason(val) {
            if (val) {
                this.$nextTick(() => {
                    this.$tip = initDisabledTooltip(document.querySelector('.jacp-form-datepicker__popper'), val);
                });
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-form-input--edit{
    outline: none;
    .jacp-form-input{
        &__error .el-input__inner,
        &__error .el-textarea__inner{
            border-color: @errorColor;
        }
        &__value{
            white-space: pre-wrap;
            word-break: break-all;
        }
        &__edit{
            transform: translate(-12px, -3px);
        }
    }
}
</style>
