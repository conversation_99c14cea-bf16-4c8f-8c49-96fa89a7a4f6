<template>
    <div
        class="jacp-forms-wrapper"
        :class="{
            'jacp-forms-wrapper__viewmode': !editMode,
            'jacp-forms-wrapper__disabled': disabled
        }"
        @mousedown.capture="focusBefore($event)"
        @mouseup.capture="focus($event)"
    >
        <slot
            name="view"
            v-if="!editMode"
        />
        <slot
            name="edit"
            v-else
        />
        <i
            class="jacp-icon-edit jacp-forms-wrapper__editicon"
            v-show="!editMode"
        />
    </div>
</template>

<script type="text/javascript">
import { closest } from '@/plugins/utils';

const frequency = 1000; // ms, 冷却时间，禁止1s内连续进入编辑状态
const blurEventData = {};
function isClickEvent(current, old) {
    if (old.x === undefined) { // 特性判断
        return false;
    }
    if (Math.abs(current.x - old.x) + Math.abs(current.y - old.y) > 5) {
        return false;
    }
    return true;
}
function isLeftClick(e = {}) {
    return e.which === 1 || e.button === 0;
}

export default {
    name: 'JacpFormItemWrapper',
    props: {
        editMode: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        // mousedown.capture导致无法在wrapper内部放置无需触发focus事件的元素
        // 如果设置了ignoreFocusElementSelector，那么点击该元素的时候就阻止了focus
        ignoreFocusElementSelector: {
            type: String,
            default: '.cancel-focus',
        },
    },
    data() {
        return {
            lock: false,
        };
    },
    methods: {
        focusBefore($event) {
            // mousedown.capture
            if (this.ignoreFocusElementSelector
            && closest($event.target, this.ignoreFocusElementSelector)) {
                return;
            }
            // 记录focus事件之前的位置
            Object.assign(blurEventData, {
                x: $event.x,
                y: $event.y,
                target: $event.target,
            });
        },
        blur($event) {
            const rootNodeList = Array.from(document.body.querySelectorAll('.app-root'));
            if (this.editMode
                && rootNodeList.some(root => root.contains($event.target))
                && !this.$el.contains($event.target)
            ) {
                this.lock = true;
                this.$emit('blur', $event);
                setTimeout(() => {
                    this.lock = false;
                }, frequency);
            }
        },
        focus(e) {
            if (this.disabled || this.lock || !isLeftClick(e) || !isClickEvent(e, blurEventData)) {
                return;
            }
            if (this.editMode) { // 编辑状态只处理内部交互，不触发全局响应
                e.stopPropagation();
                return;
            }

            // 开始正式业务处理
            this.$emit('focus');
        },
    },
    created() {
        window.document.addEventListener('mousedown', this.blur, true);
    },
    beforeDestroy() {
        window.document.removeEventListener('mousedown', this.blur, true);
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.jacp-forms-wrapper{
    display: inline-block;
    border: 1px solid rgba(0, 0, 0, 0);
    transform: translate(0, -1px);
    min-width: 50px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;

    &__viewmode{
        transform: translate(-6px, -1px);
        position: relative;
        padding: 0 5px;
    }
    &__editicon{
        color: @primaryColor;
        // background: #eee;
        border: 1px solid #ddd;
        // border-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        position: absolute;
        top: -1px;
        width: 30px;
        text-align: center;
        right: -30px;
        height: 30px;
        line-height: 28px;
    }
    & &__editicon{
        display: none;
    }
    &__viewmode:not(&__disabled):hover{
        z-index: 10;
        border-color: @borderColor;
        cursor: pointer;
    }
    &__viewmode:not(&__disabled):hover &__editicon{
        display: inline-block;
    }
}
</style>
