<template>
    <!-- <Wrapper
    class="jacp-form-input"
    :class="{
      'jacp-form-input--edit': editMode
    }"
    :edit-mode="editMode"
    :disabled="disabled"
    @focus="focus()"
    @blur="blur($event)"
  > -->
    <jacp-form-wrapper-latest
        toolbar
        :class="{
            'jacp-form-input': editMode
        }"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form-input__value jacp-form__value"
            :class="{
                'jacp-form--nodata': isEmpty(value)
            }"
        >
            <span v-if="!isEmpty(value)">{{ value }}</span>
            <span v-else>{{ noDataText }}</span>
        </div>
        <div
            slot="edit"
            :class="{
                'jacp-form-input__edit': true,
                'jacp-form-input__error': errorMsg
            }"
        >
            <el-input
                ref="inputIns"
                :type="type"
                :value="value"
                @input="input($event)"
                :autosize="autosize"
                :maxlength="maxlength"
                :rows="rows"
                :placeholder="placeholder"
                @keydown.enter.native.prevent
            />
            <div
                class="el-form-item__error"
                v-show="!useElFormValidator && errorMsg"
            >
                {{ errorMsg }}
            </div>
        </div>
    </jacp-form-wrapper-latest>
</template>

<script type="text/javascript">
import Form from '@/mixins/mixin.form';
import { validate } from '@/plugins/utils';
// import Wrapper from './Wrapper';

export default {
    name: 'JacpInput',
    mixins: [Form],
    components: {
        // Wrapper,
    },
    props: {
        rules: [Object, Array],
        validatorTrigger: {
            type: String,
            default: 'input',
        },
        type: [String],
        rows: [Number],
        autosize: [Boolean, Object],
        maxlength: {
            type: Number,
            default: 9999,
        },
    },
    data() {
        const isElFormItem = this.$parent.$options.componentName === 'ElFormItem';
        return {
            useElFormValidator: !this.rules && isElFormItem,
            errorMsg: undefined,
            editMode: false,
            originValue: this.value,
            lockWatch: false,
        };
    },
    methods: {
        isEmpty(val) {
            return val === undefined || val === null || val === '';
        },
        triggerChange() {
            if (this.originValue !== this.value) {
                this.$emit('change', this.value);
                this.originValue = this.value;
            }
        },
        blur() {
            // 使用el表单校验
            if (this.useElFormValidator) {
                // 如果校验出错或未完成，不触发change事件
                if (['error', 'validating'].includes(this.$parent.validateState)) {
                    return;
                }
                // 如果校验成功
                this.editMode = false;
                this.triggerChange();
                return;
            }
            // 使用自带校验
            validate(this.value, this.rules).then(() => {
                this.errorMsg = undefined;
                this.editMode = false;
                this.triggerChange();
            }).catch(([errorMsg]) => {
                this.errorMsg = errorMsg;
            });
        },
        input(value) {
            if (!this.useElFormValidator && this.validatorTrigger === 'input' && this.rules) {
                validate(value, this.rules).then(() => {
                    this.errorMsg = undefined;
                }).catch(([errorMsg]) => {
                    this.errorMsg = errorMsg;
                });
            }
            this.lockWatch = true;
            this.$emit('input', value);
        },
        focus() {
            this.editMode = true;
            this.$nextTick(() => {
                const elementName = this.type === 'textarea' ? 'textarea' : 'input';
                this.$refs.inputIns.$el.querySelector(elementName).focus();
            });
        },
    },
    watch: {
        value() {
            if (!this.lockWatch) {
                this.originValue = this.value;
            } else {
                this.lockWatch = false;
            }
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.jacp-form-input{
    &__error .el-input__inner,
    &__error .el-textarea__inner{
        border-color: @errorColor;
    }
    &__value{
        white-space: pre-wrap;
        word-break: break-all;
    }
    &__edit{
        transform: translate(-16px, -3px);
    }
}
</style>
