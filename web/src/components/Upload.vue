<template>
    <el-upload
        v-bind="$attrs"
        :with-credentials="withCredentials"
        :headers="headers"
        :action="uploadUrl"
        :on-error="handleError"
        :on-progress="handleProgress"
        :on-success="handleSuccess"
        :disabled="disabled"
        :show-file-list="false"
        :file-list="value"
        :list-type="picCard"
    >
        <slot>
            <i
                slot="default"
                class="el-icon-plus"
            />
        </slot>
    </el-upload>
</template>

<script>
export default {
    inheritAttrs: false,
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        withCredentials: {
            type: Boolean,
            default: true,
        },
        headers: {
            type: Object,
            default: () => ({
            }),
        },
        uploadUrl: {
            type: String,
            // TODO: default upload url
            default: '',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        maxSize: {
            type: Number,
            default: 20 * 1024 * 1024,
        },

        fileType: {
            type: String,
            default: 'image',
        },
        picCard: {
            type: String,
            default: 'text',
        },
    },
    data() {
        return {
            dialogVisible: false,
            dialogImageUrl: '',
        };
    },
    methods: {
        handleError(res) {
            let msg = '';
            try {
                msg = JSON.parse(res.message).message;
            } catch (err) {
                msg = res.message;
            }
            this.$alert(msg, '提示', {
                type: 'error',
                closeOnClickModal: true,
                customClass: 'j--message-box--topstatus',
            });
        },
        handleProgress() {},
        handleSuccess(res) {
            // 目前只支持传单个图片
            if (res.code === 200) {
                this.$emit('success', res.data);
            } else {
                this.handleError(res);
            }
        },
    },
};
</script>
