<template>
    <el-dropdown
        class="org-selector"
        trigger="click"
        ref="dropdownIns"
        :placement="popperPlacement"
    >
        <span class="org-selector__value el-dropdown-link">
            {{ userOrgName.split('-').pop() }}
            <i class="el-icon-caret-bottom el-icon--right" />
        </span>
        <el-dropdown-menu
            :append-to-body="false"
            slot="dropdown"
            class="org-selector__dropdown"
        >
            <el-tree
                class="org-selector__dropdown__tree"
                ref="treeIns"
                v-bind="$attrs"
                :node-key="nodeKey"
                :empty-text="emptyText"
                :data="orgList"
                :props="defaultProps"
                :check-strictly="true"
                :highlight-current="true"
                :load="loadData"
                :current-node-key="userOrgId.split('/').pop()"
                :expand-on-click-node="false"
                @node-click="handleNodeClick"
                lazy
            />
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script type="text/javascript">
import OrgModel from '@/models/org';


export default {
    name: 'JacpOrgTree',
    inheritAttrs: false,
    props: {
        rootOrgId: {
            type: String,
            default: '',
        },
        rootOrgName: {
            type: String,
            default: '',
        },
        defaultOrgId: {
            type: String,
            default: '',
        },
        defaultOrgName: {
            type: String,
            default: '',
        },
        nodeKey: {
            type: String,
            default: 'id',
        },
        emptyText: {
            type: String,
            default: '没有部门数据',
        },
        popperPlacement: {
            default: 'bottom-start',
            type: String,
        },
    },
    data() {
        return {
            userOrgId: '',
            userOrgName: '',
            orgList: [],
            defaultProps: {
                label: 'name',
                children: 'children',
            },
        };
    },
    watch: {
        rootOrgId: {
            immediate: true,
            handler() {
                this.orgList = [{
                    // children: [],
                    id: this.rootOrgId.split('/').pop(),
                    name: this.rootOrgName.split('-').pop(),
                    fullId: this.rootOrgId,
                    fullName: this.rootOrgName,
                }];
                this.userOrgId = this.defaultOrgId || this.rootOrgId;
                this.userOrgName = this.defaultOrgName || this.rootOrgName;
            },
        },
    },
    methods: {
        loadData(node, resolve) {
            const org = node.data;
            const data = [];
            if (org && org.id) {
                OrgModel.loadChildOrgs(org.id).then((orgs) => {
                    orgs.forEach((item) => {
                        data.push({
                            id: item.id,
                            name: item.name,
                            // children: [],
                            fullId: `${org.fullId}/${item.id}`,
                            fullName: `${org.fullName}-${item.name}`,
                        });
                    });
                    resolve(data);
                });
            } else {
                const item = {
                    // children: [],
                    id: this.rootOrgId.split('/').pop(),
                    name: this.rootOrgName.split('-').pop(),
                    fullId: this.rootOrgId,
                    fullName: this.rootOrgName,
                };
                data.push(item);
            }
            resolve(data);
        },
        handleNodeClick(data) {
            this.userOrgId = data.fullId;
            this.userOrgName = data.fullName;
            this.$emit('check-org', data.fullId, data.fullName);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.org-selector{
    &__value{
        cursor: pointer;
        display: flex;
        align-items: center;
        text-align: right;
        width:fit-content;
        white-space: nowrap;
        // min-width: 120px;
        font-size: 12px;
        &__parentsname{
            color: #666;
            font-size: 12px;
        }
        &__name{
            line-height: 1.6;
        }
        &__outer{
            margin-right: 10px;
        }
    }
    &__dropdown{
        overflow: auto;
        padding: 5px 0;
        min-width: 220px;
        & .el-tree{
            border: none;
        }
        & .el-tree-node__content{
            padding-right: 10px;
        }
        & .el-tree-node__label{
            font-size: 12px;
        }
        &__tree{
            overflow: auto;
            max-height: 50vh;
            padding: 0 0 5px 0;
        }
        &__buttons{
            text-align: right;
            padding: 10px 10px 5px;
            border-top: @borderStyle;
        }
    }
}
</style>
