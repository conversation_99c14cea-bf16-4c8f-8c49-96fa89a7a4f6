<template>
    <div
        class="queryCanvas"
        @click="hide"
    >
        <transition name="queryCanvas__fade">
            <div
                class="queryCanvas__bg"
                v-show="openStatus"
                :class="{
                    'queryCanvas__bg-hidden': noGrayBackground,
                    'queryCanvas__bg-nofixed': !fixedOnDocument,
                }"
            />
        </transition>
        <transition
            name="el-drawer-fade"
            @after-leave="afterLeaveHandle()"
        >
            <div
                class="queryCanvas__main"
                :class="{
                    'queryCanvas__main-nofixed': !fixedOnDocument
                }"
                v-show="openStatus"
                @click.stop
            >
                <slot />
            </div>
        </transition>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'QueryCanvas',
    props: {
        noGrayBackground: {
            type: Boolean,
            default: false,
        },
        fixedOnDocument: {
            type: Boolean,
            default: true,
        },
        visible: {
            type: <PERSON>olean,
            default: false,
        },
    },
    data() {
        return {
            parentNodeOverflow: '',
            openStatus: false,
            cachedElement: undefined,
        };
    },
    mounted() {
        this.cachedElement = this.fixedOnDocument ? window.document.body : this.$el.parentNode;
    },
    methods: {
        hide() {
            this.openStatus = false;
            this.$emit('update:visible', false);
        },
        open() {
            const computedStyle = window.getComputedStyle(this.cachedElement);
            if (!this.fixedOnDocument && computedStyle.position === 'static') {
                /* eslint-disable no-console */
                console.warn('parentNode\'s position shouldn\'t be static', this.cachedElement);
            }
            this.openStatus = true;
            this.parentNodeOverflow = this.cachedElement.style.overflow;
            this.cachedElement.style.overflow = 'hidden';
        },
        afterLeaveHandle() {
            this.$emit('hide');
            this.cachedElement.style.overflow = this.parentNodeOverflow;
        },
    },
    destroyed() {
        this.cachedElement.style.overflow = this.parentNodeOverflow;
    },
    watch: {
        visible: {
            immediate: true,
            handler(val) {
                if (val === this.openStatus) {
                    return;
                }
                if (val) {
                    this.open();
                } else {
                    this.hide();
                }
            },
        },
    },
};
</script>

<style lang="less">
.queryCanvas{
    &__bg{
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 100;
        transition: all 0.2s ease;

        &-hidden{
            opacity: 0!important;
        }
        &-nofixed{
            position: absolute;
        }
    }
    &__main{
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        z-index: 101;
        box-shadow: -15px 0 15px -10px rgba(0, 0, 0, 0.2);

        &-nofixed{
            position: absolute;
        }
    }
    &__fade-enter-active,
    &__fade-leave-active {
        transition: opacity 0.2s ease;
    }
    &__fade-leave-active {
        opacity: 0;
    }
    &__fade-enter-active,
    &__fade-leave {
        opacity: 1;
    }
    &__fade-enter {
        opacity: 0;
    }
    &__slide-top-enter-active,
    &__slide-top-leave-active {
        transition: transform 0.3s ease;
    }
    &__slide-top-leave-active {
        transform: translateY(0);
    }
    &__slide-top-enter-active,
    &__slide-top-leave {
        transform: translateY(0);
    }
    &__slide-top-enter {
        transform: translateY(0);
    }
}
</style>
