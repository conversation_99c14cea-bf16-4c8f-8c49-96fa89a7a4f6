<template>
    <span
        :class="{
            'jacp-user-avatar': true,
            'jacp-user-avatar__img': avatar,
            'is-round': round
        }"
        :style="sizeStyle"
        :title="tips"
    >
        <span
            v-if="data.name && firstName"
        >
            <div
                class="jacp-user-avatar__icon"
                :style="{...sizeStyle, ...firstNameColor}"
            >
                {{ firstNameString }}
            </div>
            <div
                class="jacp-user-avatar__name"
                v-if="!noFullName"
            >{{ data.name }}</div>
        </span>
        <template v-if="avatar">
            <!-- 头像会上传不同尺寸的图片 保证图片是圆形用cover -->
            <el-image
                v-if="getHeadImage"
                :style="sizeStyle"
                :src="getHeadImage"
                fit="cover"
            />
            <img
                v-else
                src="@/assets/images/avatar.png"
            >
        </template>
    </span>
</template>

<script type="text/javascript">
import { getColorByFirstChar as getBgColor } from '@/plugins/utils.theme';
import { userImage } from '@/plugins/processPicture';

export default {
    name: 'JacpUserAvatar',
    props: {
        noFullName: {
            type: Boolean,
            default: false,
        },
        noOrgName: Boolean,
        data: {
            type: Object,
            default: () => ({}),
        },
        avatar: Boolean,
        firstName: Boolean,
        round: {
            type: Boolean,
            default: true,
        },
        size: {
            type: Number,
            default: 32,
        },
    },
    data() {
        return {
            headImage: '',
        };
    },
    computed: {
        tips() {
            return this.data.erp ? `${this.data.name}(${this.data.erp})` : this.data.name;
        },
        firstNameString() {
            return (this.data.name || '')[0];
        },
        sizeStyle() {
            const size = `${this.size.toString()}px`;
            return {
                width: size,
                height: size,
                lineHeight: size,
                fontSize: `${this.size * 0.4}px`,
            };
        },
        firstNameColor() {
            return {
                backgroundColor: getBgColor(this.data.name),
            };
        },
        getHeadImage() {
            return userImage(this.data.headImage);
        },
    },
};
</script>

<style lang="less">
.jacp-user-avatar{
    display: inline-block;
    font-size: 0;
    &__icon{
        margin: auto;
        display: block;
        border-radius: 100%;
        text-align: center;
        padding: 0;
        background-color: #c5c5c5;
        // color: #cf6a6a;
        color: #fff;
    }
    &__name{
        font-size: 12px;
        margin-top: 5px;
        overflow: hidden;
        white-space: nowrap;
    }
    &__img>img{
        width: 100%;
    }
    &.is-round{
        overflow: hidden;
    }
    &.is-round,
    &.is-round>img{
        border-radius: 50%;
    }
}
</style>
