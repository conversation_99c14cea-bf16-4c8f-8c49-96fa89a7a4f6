<script>
const defaultSlotMap = {
    batch: 'batch',
    name: 'name',
    cTime: 'time',
    submitTime: 'time',
    expectedReleaseDate: 'time',
    bizStatusUTime: 'time',
    spaceId: 'space',
    sprintName: 'spaceSprint',
    demandTypeId: 'demandTypeId',
    priorityId: 'priority',
    proposer: 'person',
    processor: 'person',
    receivers: 'multiPerson',
    follower: 'follower',
    status: 'status',
    demandSourceId: 'sourceType',
    oprations: 'oprations',
    pmpProjectId: 'pmpProject',
    demanders: 'multiPerson',
    computeStatus: 'computeStatus',
    devStatus: 'devStatus',
    stageId: 'stageId',
    tags: 'tags',
    lastComment: 'lastComment',
    urgent: 'urgent',
    platform: 'platformRelations',
    plannedHours: 'plannedHours',
    actualHours: 'actualHours',
    planDate: 'planDate',
    evaluation: 'evaluation',
    channelRisk: 'channelRisk',
    evaluationUser: 'evaluation',
    evaluationTime: 'evaluation',
    pmpProjectName: 'pmpProjectName',
    projectTypeName: 'projectTypeName',
    pmpProjectCode: 'pmpProjectCode',
    processTime: 'processTime',
    cardCompleteTime: 'cardCompleteTime',
    acceptTime: 'acceptTime',
    revenueEndDate: 'revenueEndDate',
    groupNames: 'groupNames',
    priorityScore: 'priorityScore',
    revenueName: 'revenueName',
    evaluationRealEarnings: 'evaluation',
    evaluationReason: 'evaluation',
    accpetorOrg: 'accpetorOrg',
};
export default {
    name: 'JacpTableBodyResize',
    data() {
        return {
            mixinSlotMap: { ...defaultSlotMap },
        };
    },
    props: {
        columns: {
            type: Array,
            default: () => [],
        },
        rows: {
            type: Array,
            default: () => [],
        },
        sortable: {
            type: Boolean,
            default: false,
        },
        batch: {
            type: Boolean,
            default: false,
        },
        noDataMessage: {
            type: String,
            default: '暂无数据',
        },
        slotMap: {
            type: [Object, Array],
            default: () => ({}),
        },
        currentRow: [Number, String],
        errorRows: {
            type: Array,
            default: () => [],
        },
    },
    methods: {

        /**
         * 有slot的读slot，没有的直接显示value
         * 每增加一个列的template都需要对应的在slotMap增加映射
         */
        renderCell(h, key, rowData) {
            const slot = this.mixinSlotMap[key];
            let cellValue = rowData[key] || '--';
            if (key.includes('.')) {
                // demand.name
                const [obj, objKey] = key.split('.');
                cellValue = rowData[obj] ? rowData[obj][objKey] : '--';
            }
            if (Object.hasOwnProperty.call(this.$scopedSlots, slot)) {
                cellValue = this.$scopedSlots[slot](Object.assign(rowData, {
                    currentKey: key,
                }));
            }
            return h('td', null, [cellValue]);
        },

        /**
         * 渲染一下显示的列，根据columns里的show来filter
         */
        renderRow(h, rowData, context) {
            if (this.batch && this.columns[0] && this.columns[0].name !== 'batch') {
                this.columns.unshift({
                    name: 'batch',
                    show: 1,
                    index: -1,
                    label: '',
                });
            } else if (!this.batch && this.columns[0] && this.columns[0].name === 'batch') {
                this.columns.shift();
            }
            return h('tr', {
                key: rowData.id,
                class: {
                    'j-table__item': true,
                    'j-table__row__child': typeof rowData.level !== 'undefined' ? rowData.level - 1 : rowData.$level,
                    'j-table__row--notmatched': typeof rowData.notMatched !== 'undefined' ? rowData.notMatched : rowData.matched === 1,
                    'j-table__item--undragable': typeof rowData.$isHandler !== 'undefined' ? !rowData.$isHandler : false,
                    'j-table__item--hide': typeof rowData.show !== 'undefined' ? !rowData.show : false,
                    'j-table__item--active': Number(this.currentRow) === Number(rowData.id),
                    'j-table__item--error': this.errorRows.includes(Number(rowData.id)),
                },
                attrs: {
                    'data-v-parent-id': rowData.$vParentId,
                    'data-is-handler': rowData.$isHandler ? 1 : 0,
                    'data-id': rowData.id,
                    name: rowData.name,
                },
                on: {
                    click: (ev) => {
                        context.$emit('on-row-click', rowData, ev);
                    },
                },
            }, this.columns.filter(item => item.show)
                .map(item => this.renderCell(h, item.name, rowData)).concat([h('td')]));
        },
        renderRowNodata(h) {
            return h('tr', {
                class: {
                    'j-nodata': true,
                },
            }, [h('td', {
                attrs: {
                    colspan: this.columns.length + 1,
                },
            }, this.noDataMessage)]);
        },
    },
    render(h) {
        const context = this;
        return (
            <tbody class={{
                'j-table__body': true,
                'j-table__body--sortable': this.sortable,
            }}>
                {
                    this.rows.length
                        ? this.rows.map(item => this.renderRow(h, item, context))
                        : this.renderRowNodata(h)
                }
            </tbody>
        );
    },
    watch: {
        slotMap: {
            immediate: true,
            handler(n) {
                if (Array.isArray(n)) {
                    n.forEach((item) => {
                        this.mixinSlotMap[item] = item;
                    });
                    return;
                }
                this.mixinSlotMap = Object.assign({}, this.mixinSlotMap, n);
            },
        },
    },
};
</script>
