<template>
    <div class="jacp-pagenation">
        <span class="jacp-pagenation__pagesize">每页</span>
        <select
            v-model="currentPageSize"
            class="jacp-pagenation__select"
            @change.self="changePageSize"
        >
            <option
                v-for="num in pageNum"
                :key="num"
                :value="num"
            >
                {{ num }}
            </option>
        </select>
        <span class="jacp-pagenation__pagesize">条</span>
        <span class="jacp-pagenation__pageindex">{{ totalText }}</span>
        <jacp-icon
            class="jacp-pagenation__icon"
            name="el-icon el-icon-arrow-left"
            @click.native="prev"
            :class="{'j-disabled': currentPage === 1}"
            :size="12"
        />
        <jacp-icon
            class="jacp-pagenation__icon"
            name="el-icon el-icon-arrow-right"
            @click.native="next"
            :class="{'j-disabled': currentPage === pageCount}"
            :size="12"
        />
        <span class="jacp-pagenation__pageindex">{{ pageIndexText }}</span>
        <input
            class="jacp-pagenation__input"
            type="text"
            v-model="inputPage"
        >
        <span
            class="jacp-pagenation__btn"
            :class="{
                'j-disabled': disableGoto
            }"
            @click="goto(inputPage)"
        >{{ gotoText }}</span>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'JacpPagenation',
    props: {
        total: {
            type: Number,
            default: 0,
        },
        currentPage: {
            type: Number,
            default: 1,
        },
        pageSize: {
            type: Number,
            default: 10,
        },
        pageNum: {
            type: Array,
            default: () => [10, 20, 50],
        },
    },
    data() {
        return {
            inputPage: this.currentPage,
            currentPageSize: this.pageSize,
        };
    },
    computed: {
        disableGoto() {
            return Number.isNaN(+this.inputPage);
        },
        pageCount() {
            return Math.ceil(this.total / this.pageSize);
        },
        pageIndexText() {
            return this.$t('jacp.pagenation.pageIndex', {
                currentPage: this.currentPage,
                pageCount: this.pageCount,
            });
        },
        totalText() {
            return this.$t('jacp.pagenation.total', {
                totalCount: this.total,
            });
        },
        gotoText() {
            return this.$t('jacp.pagenation.goto');
        },
    },
    methods: {
        goto(page) {
            if (!page || page === this.currentPage) {
                return;
            }
            const inputPage = Math.max(1, Math.min(page, this.pageCount));
            this.$emit('update:currentPage', inputPage);
        },
        next() {
            this.goto(this.currentPage + 1);
        },
        prev() {
            this.goto(this.currentPage - 1);
        },
        changePageSize() {
            this.$emit('update:pageSize', this.currentPageSize);
            this.$emit('update:currentPage', 1);
        },
    },
    watch: {
        currentPage(n) {
            this.inputPage = n;
        },
        pageSize(n) {
            this.currentPageSize = n;
        },
    },
};
</script>

<style lang="less">
.basicstyle-mixin() {
    height: 22px;
    line-height: 20px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
}
.jacp-pagenation{
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #666;

    &__icon{
        width: 22px;
        color: #666;
        line-height: 22px;
        text-align: center;
        cursor: pointer;
        .basicstyle-mixin;

        &:first-child{
            margin-right: 5px;
        }
    }
    &__pageindex{
        margin: 0 20px;
    }
    &__input{
        width: 48px;
        margin-right: 5px;
        padding: 0 5px;
        .basicstyle-mixin;
    }
    &__btn{
        padding: 0 5px;
        cursor: pointer;
        .basicstyle-mixin;
    }
    &__pagesize{
        margin: 0 5px;
    }
    &__select{
        border: 1px solid #d9d9d9;
    }
}
</style>
