<template>
    <div
        class="custom-tag-group__colorpicker"
        v-show="showPopper"
        ref="popper"
        v-clickoutside="() => { showPopper = false;}"
    >
        <span
            class="custom-tag-group__colorpickeritem"
            v-for="(color, index) in colors"
            :key="index"
            :style="`background-color:${color}`"
            @click.stop="handleSelect(color)"
        />
    </div>
</template>
<script>
import Clickoutside from 'element-ui/src/utils/clickoutside';
import Popper from 'element-ui/src/utils/vue-popper';

export default {
    name: 'ColorPicker',
    mixins: [Popper],
    props: {
        visibleArrow: {
            type: Boolean,
            default: true,
        },
    },
    directives: { Clickoutside },
    data() {
        return {
            colors: [
                '#0067B2',
                '#2695F1',
                '#01C0DD',
                '#0ED396',
                '#78C913',
                '#F84E54',
                '#FF8D00',
                '#FFC700',
            ],
        };
    },
    methods: {
        handleSelect(color) {
            this.showPopper = false;
            this.$emit('on-select', color);
        },
    },
};
</script>
<style lang="less">
.custom-tag-group__colorpicker{
    position: relative;
    /* position: absolute;
    top: 35px; */
    background-color: #E1E1E1;
    border-radius: 2px;
    width: 220px;
    padding: 12px 0 12px 4px;
    box-shadow:0px 4px 8px 0px rgba(51,51,51,0.1);
    text-align: center;
    transform: translateY(6px);
    &item{
        display: inline-block;
        width: 16px;
        height: 16px;
        background-color: #0067B2;
        margin-right: 8px;
        border-radius: 2px;
        cursor: pointer;
        &:last-child{
        margin-right: 4px;
        }
    }
    & .popper__arrow{
        content: ' ';
        display: block;
        width: 0;
        height: 0;
        border-width: 0px 6px 6px 6px;
        position: absolute;
        border-color: transparent transparent #E1E1E1 transparent;
        border-style: solid;
        left: 26px;
        top: -6px;
    }
}
</style>
