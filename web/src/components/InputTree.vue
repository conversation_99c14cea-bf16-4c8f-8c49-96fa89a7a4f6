<template>
    <el-dropdown
        class="jacp-input-tree"
        trigger="click"
        ref="dropdownIns"
        @visible-change="dropdownState = $event"
        placement="bottom-start"
    >
        <span
            class="jacp-input-tree__value el-dropdown-link el-input__inner"
            :class="{'jacp-input-tree__value--disabled': disabled}"
            @mousedown.prevent.stop
        >
            <div class="jacp-input-tree__value__outer">
                <div class="jacp-input-tree__value__name">{{ valueName }}</div>
                <div
                    class="jacp-input-tree__placeholder"
                    v-show="!valueName"
                >
                    {{ placeholder }}</div>
            </div>
            <i
                class="jacp-input-tree__icon el-icon-caret-top el-icon--right"
                :class="{'jacp-input-tree__icon--open': dropdownState}"
            />
        </span>
        <el-dropdown-menu
            slot="dropdown"
            class="jacp-input-tree__dropdown"
        >
            <el-tree
                class="jacp-input-tree__dropdown__tree"
                ref="treeIns"
                :data="data"
                :props="defaultProps"
                :check-strictly="true"
                node-key="id"
                :lazy="lazy"
                :load="load"
                :highlight-current="true"
                :empty-text="noDataText"
                :default-expanded-keys="defaultExpandedKeys"
                @node-expand="$emit('expand', $event)"
                @node-click="handleNodeClick"
            />
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script type="text/javascript">
function createIndex(data = []) {
    const res = {};
    function eachCurrent(parent) {
        (parent.children || []).forEach((item) => {
            if (!res[item.id]) {
                res[item.id] = Object.assign(item, {
                    $_parent: parent,
                });
            }
            eachCurrent(item);
        });
    }
    data.forEach((item) => {
        res[item.id] = item;
        eachCurrent(item);
    });
    return res;
}

export default {
    name: 'JacpInputTree',
    props: {
        value: {
            type: [String, Number],
        },
        data: {
            type: Array,
            default: () => ([]),
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        placeholder: {
            type: String,
            default: '请选择',
        },
        noDataText: {
            type: String,
            default: '无数据',
        },
        lazy: {
            type: Boolean,
            default: false,
        },
        load: {
            type: Function,
        },
        valueNameFn: Function,
        closeOnSelect: Boolean,
    },
    data() {
        return {
            innerValue: this.value,
            treeMap: {},
            defaultProps: {
                label: 'name',
                children: 'children',
            },
            defaultExpandedKeys: [],
            dropdownState: false,
            activeNode: {},
        };
    },
    watch: {
        data: {
            handler(n) {
                this.treeMap = createIndex(n);
                this.defaultExpandedKeys = n[0] ? [n[0].id] : undefined;
            },
            deep: true,
            immediate: true,
        },
        value(n) {
            if (this.innerValue !== n) {
                this.innerValue = n;
                if (this.activeNode.id !== n) {
                    this.activeNode = {};
                }
            }
        },
    },
    methods: {
        handleNodeClick(org) {
            this.activeNode = org;
            this.innerValue = org.id;
            this.$emit('select', org);
            this.$emit('input', org.id);
            if (this.closeOnSelect) {
                this.dropdownState = false;
            }
        },
    },
    computed: {
        valueName() {
            if (!this.activeNode.id) {
                return this.value;
            }
            if (this.valueNameFn && typeof this.valueNameFn === 'function') {
                return this.valueNameFn(this.activeNode);
            }
            return this.activeNode.name;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.jacp-input-tree{
    &__placeholder{
        color: @formPlaceholderColor;
    }
    &__icon{
        transition: transform .3s;
        color: @formIconColor;
        transform: rotateZ(-180deg);
        margin-top: 2px;

        &--open{
            transform: rotateZ(0);
        }
    }
    &__value{
        cursor: pointer;
        display: flex;
        align-items: center;
        border: 1px solid @formBorderColor;
        border-radius: @formBorderRadius;
        padding:  3px 10px;
        font-size: 12px;
        justify-content: space-between;
        width: 100%;

        &__name{
            line-height: 1;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        &__outer{
            width: calc(100% - 27px);
            margin-right: 10px;
        }
        &--disabled{
            // pointer-events: none;
            .formDisabled();
        }
    }
    &__dropdown{
        overflow: auto;
        padding: 5px 0;
        min-width: 220px;
        font-size: 12px;

        & .el-tree{
            border: none;
        }
        & .el-tree-node__content{
            padding-right: 10px;
        }
        &__tree{
            overflow: auto;
            max-height: 300px;
            padding: 0 0 5px 0;
        }
        &__buttons{
            text-align: right;
            padding: 10px 10px 5px;
            border-top: @borderStyle;
        }
    }
}
</style>
