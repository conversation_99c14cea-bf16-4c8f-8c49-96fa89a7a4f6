<script>
import Highcharts from 'highcharts';
import HighchartsMore from 'highcharts/highcharts-more';
import HighchartsSolidGauge from 'highcharts/modules/solid-gauge';
import BronkenAxis from 'highcharts/modules/broken-axis';
import Streamgraph from 'highcharts/modules/streamgraph';
import loadExporting from 'highcharts/modules/exporting';

const { Chart } = Highcharts;
loadExporting(Highcharts);
HighchartsMore(Highcharts);
HighchartsSolidGauge(Highcharts);
BronkenAxis(Highcharts);
Streamgraph(Highcharts);

Highcharts.setOptions({
    credits: {
        enabled: false,
    },
    global: {
        useUTC: false,
    },
});

export default {
    name: 'CiHighchart',
    template: '<div style="height: 100%"></div>',
    props: {
        option: {
            default: () => ({}),
            type: Object,
        },
        data: {
            default: () => [],
            type: Array,
        },
    },
    mounted() {
        this.initChart();
    },
    beforeDestory() {
        this.chart.destory();
    },
    methods: {
        initChart() {
            this.chart = new Chart(this.$el, Object.assign({}, this.option, {
                series: this.data,
            }));
        },
    },
    watch: {
        data(n) {
            if (n && n instanceof Array) {
                this.chart.update(this.option, true);
                this.chart.update({
                    series: n,
                });
            }
        },
    },
};
</script>
