import _quill from 'quill';

const BaseModule = _quill.import('core/module');
export default class Counter extends BaseModule {
    constructor(quill, options = {}) {
        super(quill, options);
        if (typeof options.container === 'string') {
            this.container = document.querySelector(options.container);
        } else if (options.container) {
            this.container = options.container;
        } else {
            return;
        }
        // else ：TODO: 是否需要addContainer创建一个？
        quill.on(_quill.events.TEXT_CHANGE, this.update.bind(this));
        this.update(); // Account for initial contents
    }

    calculate() {
        let text = this.quill.getText();
        if (this.options.unit === 'word') {
            text = text.trim();
            // Splitting empty text returns a non-empty array
            return text.length > 0 ? text.split(/\s+/).length : 0;
        }
        // getText的时候存在一个\n占了个长度
        // https://quilljs.com/docs/api/#gettext
        // The editor always has at least a new line character.
        // Call quill.getContensts() and see that you have insert op with "\n".
        // https://github.com/quilljs/quill/issues/1105
        return text.length > 0 ? text.length - 1 : 0;
    }

    update() {
        const { handlers = {} } = this.options;
        const length = this.calculate();
        const { max, unit } = this.options;
        if (max !== Counter.DEFAULTS.max) {
            this.container.innerText = `${length} ${unit || ''}/ ${max}`;
        } else {
            this.container.innerText = `${length} ${unit || ''}`;
        }
        const error = length > max;
        this.container.setAttribute('data-error', error);
        // TODO: 这样添加的样式被vue的update给更新没了
        try {
            if (handlers.update) {
                handlers.update.call(this);
            }
            if (handlers.error) {
                handlers.error.call(this, error, '字数超长');
            }
        } catch (err) {
            throw new Error('Invalid CounterModule Handler');
        }
        // TODO: 怎么通知编辑器进入error状态？
    }
}
Counter.DEFAULTS = {
    container: null,
    max: Number.POSITIVE_INFINITY,
};
