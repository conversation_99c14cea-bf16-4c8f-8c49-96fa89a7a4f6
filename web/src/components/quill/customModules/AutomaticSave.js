import _quill from 'quill';

const BaseModule = _quill.import('core/module');

class AutomaticSaveActions {
    constructor(container, options = {
        restore: {},
        abandon: {},
    }) {
        this.options = options;
        this.el = this.addContainer();
        container.appendChild(this.el);

        container.addEventListener('click', (ev) => {
            const { action } = ev.target.dataset || {};
            if (action && action in options && options[action].handler) {
                options[action].handler();
            }
        });
    }

    addContainer() {
        const span = document.createElement('span');
        span.classList.add('ql-actions');
        span.innerHTML = this.constructor.TEMPLATE(this.options);
        return span;
    }

    hide() {
        this.el.style.display = 'none';
    }

    show() {
        this.el.style.display = 'block';
    }
}
AutomaticSaveActions.TEMPLATE = actions => Object.keys(actions)
    .map(action => `
<a class="ql-action" data-action="${action}">${actions[action].label}</a>`)
    .join('');
AutomaticSaveActions.DEFAULTS = {
    restore: {
        handler: () => {},
        label: '',
    },
    abandon: {},
};
export default class AutomaticSave extends BaseModule {
    #timer = null;

    // TODO: i18n
    static #actionsLabel = {
        saved: () => `已于${new Date().toLocaleTimeString()}保存`,
        saving: '自动保存...',
        unsaved: '存在未保存的编辑内容，是否恢复编辑?',
        restore_success: '已恢复',
        restore_error: '恢复失败',
        restore: '恢复编辑',
        abandon: '丢弃',
    };

    constructor(quill, options = {}) {
        super(quill, options);
        // 用来存取
        if (!options.cacheId || !options.container) {
            return;
        }
        this.cacheId = options.cacheId;
        this.container = document.querySelector(options.container);
        this.container.innerHTML = this.constructor.TEMPLATE;
        this.actions = null;
        const { restore, abandon, unsaved } = AutomaticSave.#actionsLabel;
        // 检查一下是否存在需要恢复的内容
        this.check()
            .then(() => {
                this.actions = new AutomaticSaveActions(this.container, {
                    restore: {
                        handler: this.restore.bind(this),
                        label: restore,
                    },
                    abandon: {
                        handler: this.abandon.bind(this),
                        label: abandon,
                    },
                });
                this.update(unsaved);
            });
        this.init();
    }

    get cache() {
        return window.localStorage.getItem(this.cacheId) || null;
    }

    init() {
        // 定时保存
        this.#timer = setInterval(this.save.bind(this), this.options.interval);
        // 输入就保存
        this.quill.on(_quill.events.TEXT_CHANGE, (delta, oldDelta, source) => {
            if (source !== _quill.sources.USER) {
                return;
            }
            this.save();
        });
    }

    save() {
        const { saving, saved } = AutomaticSave.#actionsLabel;
        // do save
        this.update(saving);
        window.localStorage.setItem(this.cacheId, JSON.stringify(this.quill.getContents()));
        this.update(saved());
        if (this.actions) {
            this.actions.hide();
        }
    }

    update(tips) {
        // do update tips
        if (this.container && this.container instanceof HTMLElement) {
            const tipsEl = this.container.querySelector('.ql-autosave__tips');
            tipsEl.innerHTML = tips;
        }
    }

    /**
     * 查询缓存是否存在
     */
    check() {
        const { handler } = this.options;

        if (this.cache) {
            if (handler && typeof handler === 'function') {
                return handler(this.cache);
            }
            return Promise.resolve(this.cache);
        }
        return Promise.reject();
    }

    /**
     * 恢复缓存
     */
    restore() {
        const { cache } = this;
        try {
            if (cache) {
                this.quill.setContents(JSON.parse(cache), _quill.sources.User);
                // TODO: selection无法focus到最后位置上是怎么回事？
                this.quill.focus();
                this.abandon();
                this.update(AutomaticSave.#actionsLabel.restore_success);
            }
        } catch (err) {
            this.update(AutomaticSave.#actionsLabel.restore_error);
            throw new Error('Invalid cache');
        }
        if (this.actions) {
            this.actions.hide();
        }
    }

    /**
     * 丢弃缓存
     */
    abandon() {
        window.localStorage.removeItem(this.cacheId);
        this.update('');
        if (this.actions) {
            this.actions.hide();
        }
    }

    destroy() {
        this.abandon();
        clearInterval(this.#timer);
        this.#timer = null;
    }
}
AutomaticSave.DEFAULTS = {
    interval: 2 * 60 * 1000,
    cacheId: null,
    container: null,
};
AutomaticSave.TEMPLATE = `
<span class="ql-autosave__tips"></span>
`;
