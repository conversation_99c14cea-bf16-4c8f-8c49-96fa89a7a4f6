import ImageResize from 'quill-image-resize-module';
import Vue from 'vue';
import { closest } from '@/plugins/utils';
import { Image } from 'element-ui';
import _quill from 'quill';

/**
 * 文档里写的 import { BaseModule } from 'quill-image-resize-module';是不可行的。源码并没有输出这些。同时constructor里还存在着console.log.
*/

import {
    primaryColor, borderColor, hoverBgColor, fontSecendColor,
} from '@/theme/var.less';

export const ImageComponent = Vue.extend(Image);
// TODO: 这个有很多重复的，是否可提取，参照jpfront
const listenerCache = new WeakMap();

export class Preview {
   // 1. hover 图片的时候变查看图标，这个放在editor的ql--priview 来实现了？
   // 2. 点击可放大到全屏: 创建一个预览容器，单例，只绑定一次事件
   // 3. 不侵入overlay原本的样式，方便移除'
   // 4. 编辑器全屏的时候无法在上边预览
   // 5. 非全屏状态下体验不好，直接修改quill的options也不行
   #cover = null;

   #imagePreviewer = null;

   constructor(resizer) {
       let self = listenerCache.get(this.constructor);
       if (!self) {
           self = this;
           listenerCache.set(self.constructor, self);
       }

       self.overlay = resizer.overlay;
       self.img = resizer.img;
       self.options = resizer.options;
       self.resizer = resizer;
       self.quill = resizer.quill;
       // FIXME: 每次预览都会重新创建与销毁，比较消耗性能
       self.#cover = self.createCover(self.img);
       return self;
   }

   createCover(img) {
       let cover = document.getElementById('preview-cover');
       const targetPosition = closest(this.quill.root, '.ql--fullscreen') ? this.quill.root.parentNode : document.body;
       cover = document.createElement('div');
       cover.id = 'preview-cover';
       // fake一个img用于触发image-previewer显示
       cover.appendChild(this.createImage(img));
       // 主动触发显示
       this.#imagePreviewer.clickHandler();
       // root上挂的时候会触发editor的update
       // 往body上挂的话全屏编辑的时候就看不见了
       // 不挂body的话在火狐上被容器限制住了，safari上也是灾难
       // 所以现在改成每次预览的时候都创建和销毁，根据是否是全屏状态来选择插入点
       targetPosition.appendChild(cover);
       return cover;
   }

   createImage(img) {
       const imgNode = new ImageComponent();
       imgNode.src = img.src;
       imgNode.previewSrcList = this.getQuillImageList();
       imgNode.$mount();
       imgNode.$watch('showViewer', (val) => {
           if (!val) this.resizer.hide();
       });
       this.#imagePreviewer = imgNode;
       return imgNode.$el;
   }

   getQuillImageList() {
       return Array.from(this.quill.root.querySelectorAll('img'))
           .map(i => i.src);
   }

   onCreate() {
       Object.assign(this.overlay.style,
           this.constructor.DEFAULTS.overlayStyles);
   }

    onDestroy = () => {
        this.img = null;
        this.#cover.parentNode.removeChild(this.#cover);
        this.#imagePreviewer.$destroy();
        this.#imagePreviewer = null;
    };

    onUpdate = () => {};
}

Preview.DEFAULTS = {
    overlayStyles: {
        opacity: '0',
        borderColor: 'transparent',
    },
};

/**
 * 继承自quill-image-resize-module
 * 1. 增加图片预览功能
 * 2. 处理readonly还能resize的bug
 * 3. 增加一些默认样式
 * 4. 处理滚动和全屏切换的定位问题 ： TODO: 是否需要非全屏的时候禁用这个玩意？体验很不好
 * 5. 调整图片大小的handler会溢出到编辑器外面，溢出的部分需要hidden（通过调整层级关系和overflow实现）
 * 6. 图片调整不能触发editor_change事件导致保存失效
 *  https://github.com/kensnyder/quill-image-resize-module/issues/10
 * 7. img上附加的是inline style，但是inline style在editor里会被pasteHtml里清除掉
 * https://github.com/quilljs/quill/issues/1556
 */
export default class ImageResizeAndPreview extends ImageResize {
    constructor(quill, options = {}) {
        super(quill, options);
        // TODO: 这个还需要优化一下，现在收回全屏的时候overlay没有复原
        quill.root.addEventListener('scroll', this.repositionElements.bind(this));
    }

    // @override
    show = (img) => {
        this.img = img;
        this.showOverlay();
        if (this.quill.isEnabled()) {
            this.initializeModules();
        } else {
            this.initializeJustPreview();
        }
    };

    initializeJustPreview() {
        this.removeModules();
        this.modules = [new Preview(this)];
        this.modules.forEach(
            (module) => {
                module.onCreate();
            },
        );

        this.onUpdate();
    }

    checkImage = (evt) => {
        if (this.img) {
            if (evt.keyCode === 46 || evt.keyCode === 8) {
                _quill.find(this.img).deleteAt(0);
            }
            // 编辑状态再隐藏
            if (this.quill.isEnabled()) {
                this.hide();
            }
        }
    };
}

/**
 * 图片调整器的一些样式配置
 * https://github.com/kensnyder/quill-image-resize-module/blob/master/src/DefaultOptions.js
 */
export const defaultStyles = {
    overlayStyles: {
        position: 'absolute',
        boxSizing: 'border-box',
        border: `1px dashed ${borderColor}`,
    },
    handleStyles: {
        position: 'absolute',
        backgroundColor: hoverBgColor,
        border: `1px solid ${primaryColor}`,
        borderRadius: '50%',
    },
    displayStyles: {
        position: 'absolute',
        backgroundColor: hoverBgColor,
        color: '#333',
        border: `1px solid ${borderColor}`,
    },
    toolbarStyles: {
        color: fontSecendColor,
    },
    toolbarButtonStyles: {
        border: `1px solid ${borderColor}`,
    },
    toolbarButtonSvgStyles: {
        fill: fontSecendColor,
        stroke: fontSecendColor,
    },
};
