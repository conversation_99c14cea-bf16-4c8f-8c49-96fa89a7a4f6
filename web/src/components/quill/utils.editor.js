/* eslint-disable no-param-reassign */
import sanitizeHtml from 'sanitize-html';
import { ATTRIBUTES as allowedImageAttributes } from './formats/image';

const listenerCache = new WeakMap();
export function reRenderContainer(vm) {
    if (listenerCache.has(vm)) {
        return listenerCache.get(vm);
    }
    const fn = () => {
        if (vm.loadedReRenderTimer) {
            clearTimeout(vm.loadedReRenderTimer);
        }
        if (vm.collapseContentChecker
            && !vm.collapseContentChecker.collapsed
            && !vm.fullscreenHandler.fullScreenState) {
            return;
        }
        vm.loadedReRenderTimer = setTimeout(() => {
            const { $el } = vm;
            let { height } = vm;
            const { fullScreenState } = vm.fullscreenHandler;
            if (fullScreenState && $el) {
                const statusBarEl = $el.querySelector('.ql-editor__statusbar');
                const headerEl = $el.querySelector('.ql-editor__fullscreen');
                const toolbarEl = $el.querySelector('.ql-toolbar');
                height = window.innerHeight
                - (statusBarEl ? statusBarEl.offsetHeight : 0)
                - (headerEl ? headerEl.offsetHeight : 0)
                - (toolbarEl ? toolbarEl.offsetHeight : 0);
            }
            // 这里在非全屏的预览状态时 resize的时候有点问题,高度直接恢复原状了，所以需要判断一下是否在预览状态
            // if ($el.querySelector('.ql--preview') && !fullScreenState) return;
            vm.containerStyle = {
                height: `${height}px`,
            };
        }, 0);
    };
    listenerCache.set(vm, fn);
    return listenerCache.get(vm);
}
// 预览内容用的
export class CollapseContentChecker {
    #handler = null;

    #vm;

    #target;

    #observer;

    #timer = null;

    constructor(vm, target, handler = () => {}, collapsed = true) {
        this.collapsed = collapsed;
        this.overflow = false;
        this.#handler = handler;
        this.#target = target;
        this.#vm = vm;
        listenerCache.set(this, this.check.bind(this));
        this.#observer = new MutationObserver((mutations) => {
            mutations.forEach(listenerCache.get(this));
        });
    }

    get maskVisible() {
        return this.overflow && this.collapsed;
    }

    get unfoldVisible() {
        return this.overflow && !this.collapsed;
    }

    get target() {
        if (!this.#vm.$el) {
            return null;
        }
        return this.#vm.$el.querySelector(this.#target);
    }

    // mounted的时候调用一下，实现监听
    onMounted() {
        this.#observer.observe(this.target, {
            // attributes: true,
            childList: true,
            subtree: true,
        });
        window.addEventListener('resize', listenerCache.get(this));
    }

    toggle() {
        if (!this.overflow) {
            return;
        }
        this.collapsed = !this.collapsed;
        this.#handler(this.collapsed);
    }

    check() {
        if (this.#vm.editMode) {
            return;
        }
        const { $el: el } = this.#vm;
        if (!el) {
            return;
        }
        if (this.#timer) {
            clearTimeout(this.#timer);
        }
        this.#timer = setTimeout(() => {
            const contentEl = this.target;
            this.overflow = contentEl.scrollHeight > el.offsetHeight;
        }, 50);
    }

    resetStatus() {
        this.collapsed = true;
    }

    destroy() {
        this.#observer.disconnect();
        window.removeEventListener('resize', listenerCache.get(this));
        listenerCache.delete(this);
    }
}

export const removeEmoji = (content, target = '[emoji]') => content.replace(/(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g, target);

const defaultSanitizeHtml = {
    allowedTags: sanitizeHtml.defaults.allowedTags.concat(['img']),
    allowedAttributes: {
        img: [...allowedImageAttributes, 'src'],
        a: ['href'],
    },
};
export const sanitizeRichHtml = (html, config = {}) => sanitizeHtml(html, { ...defaultSanitizeHtml, ...config });
