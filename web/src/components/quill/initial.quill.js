import _quill from 'quill';
import QuillImageDropAndPaste from 'quill-image-drop-and-paste';
import Double from './themes/double';
import Counter from './customModules/Counter';
import AutomaticSave from './customModules/AutomaticSave';
import ImageResizeAndPreview, { defaultStyles } from './customModules/ImageResizeAndPreview';
import ImageFormat from './formats/image';
import {
    ImageHandler,
    FullscreenHanlder,
} from './customHandlers';
import { removeEmoji } from './utils.editor';
/* eslint-disable no-param-reassign */
export { _quill as Quill };

const {
    SELECTION_CHANGE, TEXT_CHANGE,
} = _quill.events;

// 这里是为了把带有class的样式转成inline style
// https://github.com/quilljs/quill/issues/1451
const AlignStyle = _quill.import('attributors/style/align');
const ColorStyle = _quill.import('attributors/style/color');
const DirectionStyle = _quill.import('attributors/style/direction');
const FontStyle = _quill.import('attributors/style/font');
const SizeStyle = _quill.import('attributors/style/size');
// 这个需要设置对应的toolbar里picker的content样式，在editor.less里
SizeStyle.whitelist = ['x-small', 'small', 'normal', 'large', 'x-large', 'xx-large'];
_quill.register(AlignStyle, true);
_quill.register(ColorStyle, true);
_quill.register(DirectionStyle, true);
_quill.register(FontStyle, true);
_quill.register(SizeStyle, true);

/**
 * 扩展quill里的功能
 */
_quill.register('themes/double', Double, true);
_quill.register('modules/imageDropAndPaste', QuillImageDropAndPaste);
_quill.register('modules/imageResize', ImageResizeAndPreview);
_quill.register('modules/counter', Counter);
_quill.register('modules/automaticSave', AutomaticSave);
_quill.register(ImageFormat, true);


/**
 * Quill编辑器的一些初始化配置
 * https://quilljs.com/docs/configuration/
 */
const Delta = _quill.import('delta');
function applyFormat(delta, format, value) {
    if (typeof format === 'object') {
        return Object.keys(format).reduce((d, key) => applyFormat(d, key, format[key]), delta);
    }
    return delta.reduce((d, op) => {
        if (op.attributes && op.attributes[format]) {
            return d.push(op);
        }
        return d.insert(op.insert, Object.assign({}, { [format]: value }, op.attributes));
    }, new Delta());
}
export const TOOLBAR_CONFIG = [
    ['bold', 'italic', 'underline', 'strike'],
    [{ size: SizeStyle.whitelist }],
    // [{ header: [1, 2, 3, 4, 5, 6, false] }],
    [{ color: [] }, { background: [] }],
    [{ list: 'ordered' }, { list: 'bullet' }, 'blockquote', 'code-block'],
    [{ align: [] }],
    ['link', 'image'],
    ['fullscreen'], // TODO: 这个用样式控制了是否显示，合理么
];

export const defaultOptions = {
    theme: 'double',
    boundary: document.body, // TODO: z这个做什么用的？
    modules: {
        clipboard: {
            // 这个部分是从quill的clipboard.js里copy然后扩展的，主要用于对接历史数据的样式
            matchers: [[Node.ELEMENT_NODE, (node, delta) => {
                const formats = {};
                const style = node.style || {};
                if (style.textDecorationLine) {
                    formats.underline = !!style.textDecorationLine.includes('underline');
                    formats.strike = !!style.textDecorationLine.includes('line-through');
                }
                if (Object.keys(formats).length > 0) {
                    delta = applyFormat(delta, formats);
                }
                return delta;
            }]],
        },
        // syntax: true,
        toolbar: {
            container: TOOLBAR_CONFIG,
            handlers: {
                // TODO: 不提前写一个handler就无法在后面的addHanler里添加成功？这是怎么回事
                fullscreen() {},
            },

        }, /* history: {
            delay: 2000,
            maxStack: 500,
            userOnly: true,
        }, */
        imageResize: {
            ...defaultStyles,
            modules: ['Resize', 'DisplaySize', 'Toolbar'], // 这个无法动态设置
        },
        counter: {
            container: '.ql-editor__statusbar-counter',
            max: 1000,
            handers: {
                // update: () => {},
                error: () => {},
            }, // 这个用于update接收一下error状态
        },
        automaticSave: {
            // cacheId: 'ql-editor-123',
            container: '.ql-editor__statusbar-autosave',
        },
    },
    placeholder: '请输入...',
};

export const bindQuillToolbarHanler = (vm, quill) => {
    //  编辑link的时候的tooltip是这么获取的
    // const { tooltip } = quill.theme;
    const toolbar = quill.getModule('toolbar');
    toolbar.addHandler('image', ImageHandler(vm));
    // 全屏编辑在这里处理的
    toolbar.addHandler('fullscreen', FullscreenHanlder(vm));
    // toolbar.addHandler('link', LinkHandler);
};

export const bindQuillEventHandler = (vm, quill) => {
    // 光标发生变化时
    quill.on(SELECTION_CHANGE, (range) => {
        if (range) {
            vm.$emit('focus', quill);
            vm.focused = true;
        } else {
            // TODO: 这里检测一下range改变，根据rangeBound变更bubble的位置和显隐
            vm.$emit('blur', quill);
            vm.focused = false;
        }
    });
    // 内容发生变化时
    quill.on(TEXT_CHANGE, () => {
        let html = vm.element.children[0].innerHTML;
        const text = quill.getText();
        if (html === '<p><br></p>') html = '';
        // TODO: 另外还有其他形式的组合，比如<h2><br></h2> 也是为空的情况，是否要处理？
        vm.innerContent = html;
        vm.$emit('input', vm.innerContent);
        vm.$emit('change', { html, text: removeEmoji(text.slice(0, vm.textMaxCount)), quill });
    });
};

/**
 * Quill.events
 *  EDITOR_CHANGE: "editor-change"
    SCROLL_BEFORE_UPDATE: "scroll-before-update"
    SCROLL_OPTIMIZE: "scroll-optimize"
    SCROLL_UPDATE: "scroll-update"
    SELECTION_CHANGE: "selection-change"
    TEXT_CHANGE: "text-change"
 */
/**
 * Quill.sources
 *  API: "api"
    SILENT: "silent"
    USER: "user"
 */
// TODO: 1.x不支持table，需要引入插件 ：https://github.com/dost/quilljs-table https://www.jianshu.com/p/dc2492160c68
