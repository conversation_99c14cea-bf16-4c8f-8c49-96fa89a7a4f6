<template>
    <div
        :class="['ql-editor__wrapper','notranslate',
                 { 'ql--focus': focused,
                   'ql--fullscreen': fullscreenHandler.fullScreenState,
                   'ql--disabled': disabled,
                   'ql--error': error,
                 }]"
    >
        <div
            class="ql-editor__fullscreen"
            v-show="fullscreenHandler && fullscreenHandler.fullScreenState"
        >
            <div class="ql-editor__fullscreen-header">
                <slot name="full-title" />
                <slot name="full-action" />
            </div>
        </div>
        <div
            :id="id"
            class="ql-editor__container"
            :style="containerStyle"
        />
        <slot
            name="toolbar"
        />
        <div class="ql-editor__statusbar">
            <span class="ql-editor__statusbar-autosave" />
            <span
                class="ql-editor__statusbar-counter"
                ref="counterEl"
            />
        </div>
    </div>
</template>
<script>
import { uuid } from '@/plugins/utils';
import {
    defaultOptions,
    Quill,
    bindQuill<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    bindQuillEventHandler,
} from './initial.quill';
import { uploadImageAndInsert } from './customHandlers';
import { reRenderContainer, sanitizeRichHtml } from './utils.editor';

const Prefix = 'ql-editor';
export default {
    name: 'QuillEditor',
    props: {
        placeholder: {
            type: String,
            default: '请输入...',
        },
        disabled: {
            default: false,
            type: Boolean,
        },
        // 内容为空的时候可以设置一个初始化内容，类比input的value，和placeholder不同
        initialValue: {
            type: String,
            default: '',
        },
        value: {
            type: String,
            default: '',
        },
        editorOptions: {
            type: Object,
            default: () => ({}),
        },
        height: {
            type: [String, Number],
            default: 240,
        },
        autoSaveDisabled: {
            type: Boolean,
            defualt: false,
        },
        autoSaveKey: {
            type: [String, Number],
            default: '',
        },
        maxCount: {
            default: Infinity,
            type: Number,
        },
        // 2019.11.26 字数上限改为2w。同时，富文本纯文本保存时截取1000.
        textMaxCount: {
            default: 1000,
            type: Number,
        },
        fullscreen: {
            type: Boolean,
            default: true,
        },
        // TODO: 这个小尖头以后在做
        /* arrowVisible: {
            type: Boolean,
            default: true,
        }, */
    },
    data() {
        return {
            id: uuid(Prefix),
            innerContent: '',
            loading: false,
            focused: false,
            error: false,
            fullscreenHandler: {},
            containerStyle: {
                height: `${this.height}px`,
            },
        };
    },
    created() {
        this.$on('fullscreenchange', reRenderContainer(this));
        window.addEventListener('resize', reRenderContainer(this));
        this.handleDisable();
    },
    mounted() {
        this.initialize();
    },
    beforeDestroy() {
        const autoSave = this.quill.getModule('automaticSave');
        if (autoSave) {
            autoSave.destroy();
        }
        if (this.fullscreenHandler) {
            this.fullscreenHandler.destroy();
        }
        this.quill = null;
        delete this.quill;
        delete this.fullscreenHandler;
        this.$off('fullscreenchange');
        window.removeEventListener('resize', reRenderContainer(this));
    },
    methods: {
        initialize() {
            const vm = this;
            const options = {
                ...defaultOptions,
                readOnly: vm.disabled,
                placeholder: vm.placeholder,
                bounds: vm.$el.querySelector('.ql-editor__container'), // z这个值在设定的时候需要确保元素已存在
                ...vm.editorOptions,
            };
            // 用来扩展module的options
            if (vm.editorModulesOptions) {
                Object.assign(options.modules, vm.editorModulesOptions);
            }
            // 是否需要自动保存功能，需要的话，请设置一个唯一id
            if (!vm.autoSaveDisabled && vm.autoSaveKey) {
                options.modules.automaticSave.cacheId = `${Prefix}_${vm.autoSaveKey}`;
            }
            // 字数的容器需要换一下
            if (vm.$refs.counterEl) {
                options.modules.counter.container = vm.$refs.counterEl;
            }
            // TODO: 这两个uploadImageAndInsert怎么合一下
            // TODO: 粘贴的时候把excle也变成图片了
            options.modules.imageDropAndPaste = {
                handler: uploadImageAndInsert(vm),
            };
            Object.assign(options.modules.counter, {
                max: vm.maxCount,
                handlers: {
                    update: () => {},
                    error: (val) => {
                        vm.error = val;
                    },
                },
            });
            vm.element = document.getElementById(vm.id);
            // console.log(options);
            vm.quill = new Quill(vm.element, options);
            // Set editor content

            if (vm.value || vm.initialValue) {
                // TODO: 这个在2.x里改成了dangerouslyPasteHTML
                // https://quilljs.com/docs/modules/clipboard/#dangerouslypastehtml
                vm.quill.pasteHTML(sanitizeRichHtml(vm.value || vm.initialValue));
            }
            // vm.quill.enable(false);
            bindQuillEventHandler(vm, vm.quill);
            bindQuillToolbarHanler(vm, vm.quill);
            vm.$emit('ready', vm.quill);
        },
        resetContent(newVal) {
            if (this.quill) {
                if (newVal && newVal !== this.innerContent) {
                    const cleanHtml = newVal;
                    this.innerContent = cleanHtml;
                    this.quill.pasteHTML(cleanHtml);
                } else if (!newVal) {
                    this.quill.setText('');
                }
            }
        },
        handleDisable() {
            this.$watch('disable', (newVal) => {
                if (this.quill) {
                    this.quill.enable(!newVal);
                }
            });
        },
    },
    watch: {
        placeholder: {
            handler(val) {
                if (this.quill && this.$el) {
                    const editor = this.$el.querySelector('.ql-blank');
                    if (editor) {
                        editor.dataset.placeholder = val;
                    }
                }
            },
        },
        // Watch content change
        value: 'resetContent', // 如果有内容变化的时候改变的应该是value，不是initialValue
        // Watch disabled change
        /* disabled(newVal) {
            if (this.quill) {
                this.quill.enable(!newVal);
            }
        }, */
    },
};
</script>
