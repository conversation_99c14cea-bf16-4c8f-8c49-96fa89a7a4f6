import fscreen from 'fscreen';

const bindHandlerCache = new WeakMap();
export default class FullscreenHandler {
  #el = null;

  constructor(el) {
      this.#el = el || document.body;
      this.fullScreenState = false;
      bindHandlerCache.set(this, this.setFullScreenState.bind(this));
      this.attachEvent();
  }

  // TODO: 上传图片会触发fullscreenchange事件然后退出全屏
  setFullScreenState() {
      this.fullScreenState = !!fscreen.fullscreenElement && fscreen.fullscreenElement === this.#el;
  }

  attachEvent() {
      fscreen.addEventListener('fullscreenchange', bindHandlerCache.get(this), false);
      if (fscreen.fullscreenElement) {
          fscreen.exitFullscreen();
      }
  }

  toggle() {
      if (!this.fullScreenState) {
          fscreen.requestFullscreen(this.#el);
      } else {
          fscreen.exitFullscreen();
      }
  }

  destroy() {
      fscreen.removeEventListener('fullscreenchange', bindHandlerCache.get(this));
      bindHandlerCache.delete(this);
      this.#el = null;
  }
}
