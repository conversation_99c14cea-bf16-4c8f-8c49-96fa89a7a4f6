<script>
// 先不引入所有的button
const TOOLBAR_SIMPLE_CONFIG = [
    ['bold', 'italic', 'link'],
    [{ header: 1 }, { header: 2 }, 'blockquote'],
];
// TODO: 后面再做这个功能  这个edit 只有在plus情况才会用到
const PREVIEW_CONFIG = ['copy', 'edit'];
class TextBox {
    constructor({
        preview = '',
        value = '',
        mode = 'link', // TODO: 可能还有formula，video等
    } = {}) {
        this.preview = preview;
        this.value = value;
        this.mode = mode;
    }
}
export default {
    created() {
        // 这个save是在BubbleTooltip里添加进来的
        this.$on('save', (value) => {
            if (typeof this.save === 'function') {
                this.save(value);
            }
        });
    },
    data() {
        return {
            disabled: false, // TODO: 这个在于editable的时候可以展示预览的tools
            editing: false, // 编辑link的时候添加样式
            input: new TextBox(),
            arrowStyles: {
                marginLeft: 0,
            },
            // 根据BubbleTooltip.position()取得的shift计算position
            shift: null,
        };
    },
    // TODO: input在一开始就被textbox读取并保存了。导致必须要有宽度，否则计算root的width会异常，是否有更好的方式？
    render() {
        // TODO: 显示的时候没有focus到input上
        return (
            <div class='ql-bubble__wrapper' >
                <span ref="arrowEl" class="ql-tooltip-arrow" style={this.arrowComputedStyles}></span>
                <div class="ql-bubble-editor" style={{
                    visibility: this.editing ? 'visible' : 'hidden',
                }}>
                    <input
                        type='text'
                        ref='inputEl'
                        value={this.input.value}
                        placeholder={this.input.value || this.input.preview}
                        onInput= {
                            (e) => {
                                this.input.value = e.target.value;
                            }
                        }
                        onKeypress={
                            (e) => {
                                if (e.keyCode === 13) {
                                    this.$emit('save', this.input);
                                }
                            }
                        }
                    ></input>
                </div>
            </div>
        );
    },
    computed: {
        buttons() {
            const { disabled } = this;
            if (disabled) {
                return PREVIEW_CONFIG;
            }
            return TOOLBAR_SIMPLE_CONFIG;
        },
        arrowComputedStyles() {
            const { shift } = this;
            return {
                ...this.arrowStyles,
                marginLeft: shift ? `${-1 * shift - this.$refs.arrowEl.offsetWidth / 2}px` : 0,
            };
        },

    },
    methods: {
        edit(mode = 'link', preview = null) {
            this.editing = true;
            if (preview != null) {
                this.input.value = preview;
                this.input.preview = preview;
            } else if (mode !== this.input.mode) {
                this.input.value = '';
            }
        },
        reset() {
            this.input = new TextBox();
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.ql-bubble__wrapper{
  transition: height .3s;
  background-color: #444;
  border-radius: 25px;
  height: 40px;
  padding: 0 16px;
  .ql-bubble-toolbar{
    // TODO: 这个是toolbar的快捷键
  }
  .ql-bubble-editor{
    display: flex;
    // 这个是link的输入框
    input{
      height: 40px;
      padding: 0;
      line-height: 40px;
      background-color: transparent;
      border: none;
      outline:0;
      flex-basis: 100%;
      color: #fff;
      min-width: 300px;
    }
    a{
      display: inline-block;
      width: 80px;
      text-align: center;
      color: #ccc;
    }
  }
}
</style>
