import Quill from 'quill';
import Vue from 'vue';
import Emitter from 'quill/core/emitter';
import './double.less';
import BubbleTooltipConstructor from './bubbleTooltip';

// import { BaseTooltip } from 'quill/themes/base';

// console.log(BaseTooltip);
// import BaseTheme from 'quill/themes/base';
const LinkBlot = Quill.import('formats/link');
// 继承这个的话有太多需要覆盖的东西了，所以不继承了
// const Tooltip = Quill.import('ui/tooltip');
const Snow = Quill.import('themes/snow');
// https://stackoverflow.com/questions/44625868/es6-babel-class-constructor-cannot-be-invoked-without-new
const icons = Quill.import('ui/icons');
icons.fullscreen = '<i class="jacp-icon-fullscreen"></i>';

const getElementShift = (reference, root, boundsContainer) => {
    root.classList.remove('ql-flip');
    const containerBounds = boundsContainer.getBoundingClientRect();
    const rootBounds = root.getBoundingClientRect();
    let shift = 0;
    let verticalShift = 0;
    if (rootBounds.right > containerBounds.right) {
        shift = containerBounds.right - rootBounds.right;
    }
    if (rootBounds.left < containerBounds.left) {
        shift = containerBounds.left - rootBounds.left;
    }
    if (rootBounds.bottom > containerBounds.bottom) {
        const height = rootBounds.bottom - rootBounds.top;
        verticalShift = reference.bottom - reference.top + height;
        root.classList.add('ql-flip');
    }
    return {
        shift,
        verticalShift,
    };
};
// TODO: 先做link的编辑框，下一步做toolbar的tooltip版
class BubbleTooltip {
    #vm = null;

    #timer = null;

    #reference = null;

    constructor(quill, boundsContainer) {
        this.quill = quill;
        this.boundsContainer = boundsContainer || document.body;
        this.root = quill.addContainer('ql-bubble');
        this.create();
    }

    create() {
        const VM = this.constructor.TEMPLATE;
        VM.prototype.save = this.save.bind(this);
        this.#vm = new VM().$mount();
        this.root.appendChild(this.#vm.$el);
        // textbox在BaseTheme里用于判断是否===document.activeElement，否则会触发hide()导致tooltip被异常关闭
        this.textbox = this.root.querySelector('input[type="text"]');
        this.listen();
        this.hide();
    }


    listen() {
        if (this.quill.root === this.quill.scrollingContainer) {
            this.quill.root.addEventListener('scroll', () => {
                this.root.style.marginTop = `${-1 * this.quill.root.scrollTop}px`;
            });
        }
        this.quill.on(Emitter.events.EDITOR_CHANGE, (type, range, oldRange, source) => {
            if (type !== Emitter.events.SELECTION_CHANGE) return;
            // TODO: 目前预览状态下没有tooltip
            if (!this.quill.isEnabled()) return;
            if (source !== Emitter.sources.USER) return;
            //  没有文字选择，单击link的时候显示tooltip 并且进入编辑的预览状态
            if (range !== null && range.length === 0) {
                const [link, offset] = this.quill.scroll.descendant(LinkBlot, range.index);
                if (link !== null) {
                    this.linkRange = {
                        index: range.index - offset,
                        length: link.length(),
                    };
                    const preview = LinkBlot.formats(link.domNode);
                    this.edit('link', preview);
                    this.#reference = this.quill.getBounds(this.linkRange);
                    this.show();
                    return;
                }
            } else if (
                document.activeElement !== this.textbox
                && document.activeElement !== this.#vm.$el
                && this.quill.hasFocus()) {
                delete this.linkRange;
            }

            if (
                range !== null
                && range.length > 0
            ) {
                // TODO: 选择了文字以后，要显示toolbar
                // this.show();
                const lines = this.quill.getLines(range.index, range.length);
                if (lines.length === 1) {
                    this.#reference = this.quill.getBounds(range);
                } else {
                    const lastLine = lines[lines.length - 1];
                    const index = this.quill.getIndex(lastLine);
                    const length = Math.min(lastLine.length() - 1, range.index + range.length - index);
                    const bounds = this.quill.getBounds({ index, length });
                    this.#reference = bounds;
                }
            } else if (document.activeElement !== this.#vm.$el
                && this.quill.hasFocus()
            ) {
                this.hide();
            }
        });
        // TODO: 什么时候会触发这个事件？
        /*  this.quill.on(Emitter.events.SCROLL_OPTIMIZE, () => {
            // Let selection be restored by toolbar handlers before repositioning
            setTimeout(() => {
                if (!this.#vm.visible) return;
                const range = this.quill.getSelection();
                if (range != null) {
                    this.#reference = this.quill.getBounds(range);
                    // this.position(this.quill.getBounds(range));
                }
            }, 1);
        }); */
    }

    //  保持和super一样的接口 这里位置判断由quill/ui/tooltip.js迁移过来的
    position(reference = this.#reference) {
        if (!reference) return;
        const { root, quill } = this;
        const rootStyle = {
            left: reference.left + reference.width / 2 - root.offsetWidth / 2,
            top: reference.bottom + quill.root.scrollTop,
        };
        // 第一次计算并应用位置
        Object.assign(root.style, {
            left: `${rootStyle.left}px`,
            top: `${rootStyle.top}px`,
        });
        // 根据bound再一次计算一下, 有偏移的话就重新应用一下位置
        const { shift, verticalShift } = getElementShift(
            reference,
            root,
            this.boundsContainer,
        );
        rootStyle.left = `${rootStyle.left + shift}px`;
        rootStyle.top = `${rootStyle.top - verticalShift}px`;
        if (shift || verticalShift) {
            Object.assign(root.style, rootStyle);
        }
        // 主要这里有区别，这里会传给vue来修改arrow的position
        this.#vm.shift = shift;
    }

    show() {
        const { root } = this;
        root.classList.remove('ql-editing');
        root.classList.remove('ql-hidden');
        root.style.left = '0px';
        root.style.width = '';
        root.style.width = `${root.offsetWidth}px`; // 这里需要vm.$el拥有width
        this.position();
    }

    hide() {
        this.root.classList.add('ql-hidden');
        this.#reference = null;
        this.#vm.reset();
    }

    edit(mode = 'link', preview = null) {
        this.show();
        this.#vm.edit(mode, preview);
    }

    save({ value, mode }) {
        const { quill, linkRange } = this;
        const { scrollTop } = quill.root;
        switch (mode) {
        case 'link':
            if (linkRange) {
                quill.formatText(linkRange, 'link', value, Emitter.sources.USER);
                delete this.linkRange;
            } else {
                this.restoreFocus();
                quill.format('link', value, Emitter.sources.USER);
            }
            quill.root.scrollTop = scrollTop;
            break;
        case 'video':
            // value = extractVideoUrl(value);
            break;
        default:
            break;
        }
        this.hide();
        this.#vm.reset();
    }

    restoreFocus() {
        const { scrollTop } = this.quill.scrollingContainer;
        this.quill.focus();
        this.quill.scrollingContainer.scrollTop = scrollTop;
    }
}

BubbleTooltip.TEMPLATE = Vue.extend(BubbleTooltipConstructor);

/**
 * 继承snow主题，复写tooltip，修复由于offcanvas的stop mousedown出现的问题
 *
 * @class Double
 * @extends {Snow}
 */
class Double extends Snow {
    constructor(quill, options = {}) {
        super(quill, options);
        this.selectionHandleDragging();
    }

    // @override
    extendToolbar(toolbar) {
        toolbar.container.classList.add('ql-snow');
        this.buildButtons([].slice.call(toolbar.container.querySelectorAll('button')), icons);
        this.buildPickers([].slice.call(toolbar.container.querySelectorAll('select')), icons);
        this.tooltip = new BubbleTooltip(this.quill, this.options.bounds);
        if (toolbar.container.querySelector('.ql-link')) {
            this.quill.keyboard.addBinding({ key: 'K', shortKey: true }, (range, context) => {
                toolbar.handlers.link.call(toolbar, !context.format.link);
            });
        }
    }

    /**
     * Problem 1：
        在监听EDITOR_CHANGE事件的时候，quill源码中不会在mousedown的时候就触发事件EDITOR_CHANGE，是因为在selection里有判断是否是拖拽效果，但是这个事件是监听了document.body，我们的offcanvas mousedown.stop了到body上的事件，导致无法触发绑定在body mousedown的东东。导致EDITOR_CHANGE被频繁触发。主要影响tooltip位置判断。
     * Fix problem1：
     *  在create的时候改监听editor的mousedown，修改quill.selection.mouseDown 属性
     *
     * @memberof Double
     */
    selectionHandleDragging() {
        const { root, selection } = this.quill;
        if (root) {
            root.addEventListener('mousedown', () => {
                selection.mouseDown = true;
            });
            root.addEventListener('mouseup', () => {
                selection.mouseDown = true;
                selection.update(Emitter.sources.USER);
            });
        }
    }
}
export { Double as default };
