import Quill from 'quill';
/* eslint-disable no-param-reassign */
const BaseImageFormat = Quill.import('formats/image');

export const ATTRIBUTES = [
    'alt',
    'height',
    'width',
    'style',
];

export const WHITE_STYLE = ['margin', 'display', 'float'];
// quill里的matchBlot 会根据 ImageFormat.blotName 找到这个ImageFormatbBlot创建blot
export default class ImageFormat extends BaseImageFormat {
    static create(value) {
        const node = super.create();
        node.setAttribute('alt', value.alt || 'ql-img');
        node.setAttribute('src', value.url);
        node.setAttribute('data-test', value.url);
        return node;
    }

    static value(node) {
        return {
            alt: node.getAttribute('alt'),
            url: node.getAttribute('src'),
        };
    }

    static formats(domNode) {
        return ATTRIBUTES.reduce((formats, attribute) => {
            if (domNode.hasAttribute(attribute)) {
                formats[attribute] = domNode.getAttribute(attribute);
            }
            return formats;
        }, {});
    }

    format(name, value) {
        if (ATTRIBUTES.indexOf(name) > -1) {
            if (value) {
                if (name === 'style') {
                    value = ImageFormat.sanitizeStyle(value);
                }
                this.domNode.setAttribute(name, value);
            } else {
                this.domNode.removeAttribute(name);
            }
        } else {
            super.format(name, value);
        }
    }

    static sanitizeStyle(style) {
        const styleArr = style.split(';');
        let allowStyle = '';
        styleArr.forEach((v) => {
            if (WHITE_STYLE.indexOf(v.trim().split(':')[0]) !== -1) {
                allowStyle += `${v};`;
            }
        });
        return allowStyle;
    }
}
