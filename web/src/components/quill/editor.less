@import '~@/theme/var';
// 新增编辑器样式
.ql-editor__wrapper{
  border: 1px solid @borderColor;
  transition: all .2s ease;
  border-radius: 4px;
  
  &:not(.ql--fullscreen) {
    overflow: hidden;
  }
  // When Fullscreen : 假设全屏后只有一种样式
  &.ql--fullscreen{
    background: #fff;
    margin: 0 auto;
    width: 86vw;
    /* TODO: 全屏的时候样式不同，容器的样式被:fullscreen控制了，宽度只能设置下级元素？ */
    & .ql-editor__fullscreen-header,
    & .ql-editor__container,
    & .ql-editor__statusbar,
    & .ql-toolbar{
      max-width: 1128px;
      margin: 0 auto;
    }
   
    & .ql-editor__container{
      border: 1px solid @borderColor;
      background-color: @bgColor;
      height: 100%;
    }
    & .ql-toolbar{
      background-color: @bgColor;
      border-top: 1px solid @borderColor;
      border-left: 1px solid @borderColor;
      border-right: 1px solid @borderColor;
    }
    & .ql-editor__statusbar{
      padding: 0;
      line-height: 48px;
    }
    &.ql--focus{
      border: 1px solid transparent;
    }
    & .ql-editor__fullscreen{
      display: block;
    }
    & .ql-toolbar.ql-snow .ql-formats .ql-fullscreen{    
      .icon-fullscreen:before {
        content: "\e649";
      }
    }
  }
  // Fullscreen end
  // When focus
  &.ql--focus{
    border: 1px solid @primaryColor;
  }
  &.ql--error{
    border: 1px solid @redColor;
  }
  &.ql--error.ql--focus{
    border: 1px solid @redColor;
    box-shadow: 1px 0 5px #d6d6d6;
  }
  // 集成预览
  &.ql--preview {
    border-color: transparent;
    & .ql-toolbar,
    & .ql-editor__statusbar{
      display: none!important;
    }
    &.ql--collapsed{
      & .ql-editor__container .ql-editor{
        overflow: hidden;
        img{
          pointer-events: none;
          cursor: inherit;
        }
      }
    }
    .ql-editor {
      img {
        cursor: zoom-in;
        pointer-events: auto;
      }
    }
  }
}
// 保证二者置于tooltip上方，单独使用一个不好使是为啥？
// safari上需要transform才行
.ql-editor__fullscreen-header,
.ql-editor__statusbar,
.ql-toolbar{
  z-index: 1;
  transform: translateZ(10px);
}
.ql-editor__fullscreen-header{
  background: #fff;
  position: relative;
  margin-top: -1px;
  &:after{
    content: '';
    height: 1px;
    background: @borderColor;
    width: 9999px;
    position: absolute;
    top: 100%;
    left: 0;
    transform: translateX(-50%);
    z-index: 1;
  }
}

.ql-editor__statusbar{
  display:flex;
  align-content: center;
  color: @fontThirdColor;
  width: 100%;
  background-color: inherit;
  justify-content: space-between;
  font-size: 12px;
  padding: 0 16px;
  line-height: 36px;
  // overflow: hidden;
  &-counter{
    margin-left: 8px;
    &[data-error="true"]{
      color: @redColor;
    }
  }
  &-autosave{
    margin-right: 24px;
    & a[class^='ql-']{
      margin-right: 8px;
      color: @primaryColor;
      &:hover{color: @linkHoverColor;}
    }
  }
  &-tips{
    position: relative;
    .el-popover{
      display:none;
    }
    &:hover{
      .el-popover{
        display:block;
      }
    }
  }
}

// 预览图片的样式TODO:  样式放这里也很怪
// !!在firefox里，这代码中的 fixed 相对的是上层使用 transform 的那个 div，而不再是浏览器窗口了，所以需要去掉上层使用的transform才能相对body fixed
#preview-cover {
  .el-image-viewer__close{ color: #fff; }
}
// 以下是对已有snow样式的复写
.ql-container.ql-snow{
  border: none;
}
.ql-toolbar.ql-snow{ 
  padding: 0;
  line-height: 32px;
  border: none;
  // font-size: 16px;
  .ql-formats{
    margin-right: 4px;
    .ql-picker{
      line-height: 22px; // 保证不被外围的line-height影响
    }
  }
  .ql-formats .ql-fullscreen{    
    position: absolute;
    bottom: 5px;
    right: 4px;
    color: #999;
    font-size: 16px;
    .icon-fullscreen:before {
      content: "\e648";
    }
  }
/**
 * 全屏按钮位置特殊处理： TODO: 这个有点怪
 * */
  position: relative;
  background: inherit;
  
  
  &:after{
    height: 1px;
    background-color:@borderColor;
    width:100%;
  }
  
}
.ql-disabled{
  .ql-editor.ql-blank::before{
    color: @fontThirdColor;
    font-style: normal;
  }
}
.ql-editor.ql-blank::before{
  font-style: normal;
}

// 增加字号选择的处理
.ql-snow .ql-picker.ql-size .ql-picker-label::before, 
.ql-snow .ql-picker.ql-size .ql-picker-item::before{
  content: attr(data-value)!important;
}
@sizes: x-small, small, normal, large, x-large, xx-large;

.make-size(@i: length(@sizes)) when (@i > 0) {
  .make-size(@i - 1);
  @size: extract(@sizes, @i); 
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value=@{size}]::before{
    font-size: @size;
  }
}

.make-size();