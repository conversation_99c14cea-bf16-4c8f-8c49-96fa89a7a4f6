import _quill from 'quill';
import FileModel from '@/models/file';
import FullscreenHandler from './FullscreenHandler';
/* eslint-disable no-param-reassign */
// TODO: 这里把excle里拷贝的内容也变成图片上传了，怎么处理一下？

export const uploadImageAndInsert = ctx => async (files) => {
    const { quill } = ctx;
    ctx.loading = true;
    const url = await FileModel.uploadImg(files);
    // const range = quill.getSelection(true);
    const index = (quill.getSelection(true) || {}).index || quill.getLength();
    const [file] = files;// uploadImg不支持批量
    if (!index) {
        return;
    }
    quill.insertEmbed(index, 'image', {
        alt: file.name,
        url,
    }, _quill.sources.USER);
    quill.setSelection(index + 1, _quill.sources.SILENT);
    ctx.loading = false;
};
export function ImageHandler(ctx) {
    return () => FileModel.showSystemFileDialog({
        inputAttributes: {
            accept: 'image/gif,image/jpeg,image/jpg,image/png,image/svg',
        },
    }).then(uploadImageAndInsert(ctx));
}

export const FullscreenHanlder = (ctx) => {
    // eslint-disable-next-line
    ctx.fullscreenHandler = new FullscreenHandler(ctx.$el);
    ctx.$watch('fullscreenHandler.fullScreenState', (val) => {
        ctx.$emit('fullscreenchange', val);
    }, { deep: true });
    return ctx.fullscreenHandler.toggle.bind(ctx.fullscreenHandler);
};
export default {
    ImageHandler,
};
