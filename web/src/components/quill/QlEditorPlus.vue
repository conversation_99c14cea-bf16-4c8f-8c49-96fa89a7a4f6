<template>
    <div
        class="ql-editor-plus notranslate"
    >
        <!-- 编辑区域 -->
        <div
            :class="['ql-editor__wrapper',
                     { 'ql--focus': focused,
                       'ql--fullscreen': fullscreenHandler.fullScreenState,
                       'ql--disabled': disabled,
                       'ql--error': error,
                       'ql--preview': !editMode,
                       'ql--collapsed': !editMode && !fullscreenHandler.fullScreenState && collapseContentChecker.maskVisible,
                     }]"
        >
            <div
                class="ql-editor__fullscreen"
                v-show="fullscreenHandler && fullscreenHandler.fullScreenState"
            >
                <div
                    class="ql-editor__fullscreen-header"
                >
                    <slot name="full-title" />
                    <slot name="full-action">
                        <span
                            v-if="!editMode"
                        >
                            <span
                                class="ql-editor__fullscreen-action"
                                @click="$emit('edit')"
                                v-if="!disabled"
                            >
                                <i class="jacp-icon-edit-light" />
                                编辑
                            </span>
                            <span
                                class="ql-editor__fullscreen-action"
                                @click="$emit('fullscreen')"
                            >
                                <i class="jacp-icon-fullscreen-exit" />
                                退出全屏
                            </span>
                        </span>
                    </slot>
                </div>
            </div>
            <div
                :id="id"
                class="ql-editor__container"
                :style="containerStyle"
            />
            <slot
                name="toolbar"
            />
            <div
                class="ql-editor__statusbar"
            >
                <span class="ql-editor__statusbar-autosave" />
                <span
                    class="ql-editor__statusbar-tips"
                    v-show="fullscreenHandler && fullscreenHandler.fullScreenState"
                >
                    快捷键
                    <i
                        class="jacp-icon-help"
                        style="font-size: 12px;margin-left: 4px;"
                    />
                    <div
                        class="el-popover"
                        style="position: absolute;top:8px;left:calc(50% - 100px); transform: translateY(-100%); width: 200px; "
                    >
                        CTRL/⌘ + C 复制<br>
                        CTRL/⌘ + V 粘贴<br>
                        CTRL/⌘ + B 加粗<br>
                        CTRL/⌘ + I 斜体<br>
                        CTRL/⌘ + X 剪切<br>
                        CTRL/⌘ + L 链接<br>
                        CTRL/⌘ + Z 撤销<br>
                    </div>
                </span>
                <span
                    class="ql-editor__statusbar-counter"
                    ref="counterEl"
                />
            </div>
        </div>
        <form-button-group
            v-if="editMode"
            @confirm="confirm"
            @cancel="cancel"
            :disabled-buttons="disabledConfirm ? ['confirm'] : []"
        />
        <!-- 预览工具条 -->
        <form-wrapper-toolbar
            v-if="$el"
            :disabled="editMode || fullscreenHandler.fullScreenState || !shiftTools.length"
            :reference="$el"
            :tools="shiftTools"
            @on-tool-click="$emit($event)"
        />
        <!-- 预览样式沿用了原来的 -->
        <template v-if="!fullscreenHandler.fullScreenState && !editMode">
            <div
                class="collapse-box__buttons--unfold"
                v-if="collapseContentChecker.maskVisible"
                @mousedown.stop="() => collapseContentChecker.toggle()"
            >
                {{ unfoldText }}
            </div>
            <div
                class="collapse-box__buttons--fold"
                v-if="collapseContentChecker.unfoldVisible"
                @mousedown.stop="() => collapseContentChecker.toggle()"
            >
                <el-button
                    round
                    plain
                    type="primary"
                    size="mini"
                >
                    {{ foldText }}
                </el-button>
            </div>
        </template>
    </div>
</template>
<script>
import QuillEditor from './QlEditor';
import { CollapseContentChecker, reRenderContainer, removeEmoji } from './utils.editor';
import { closest } from '@/plugins/utils';

export default {
    name: 'QuillEditorPlus',
    extends: QuillEditor,
    props: {
        unfoldText: {
            type: String,
            default: '展开',
        },
        foldText: {
            type: String,
            default: '收起',
        },
        disabledConfirm: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            editMode: false,
            content: this.value,
            // 折叠预览区域
            collapseContentChecker: new CollapseContentChecker(this, '.ql-editor', (collapsed) => {
                // 这里和reRender那个冲突了，如果是全屏的话就不进行处理
                if (this.fullscreenHandler.fullScreenState) {
                    return;
                }
                this.containerStyle.height = !collapsed ? 'auto' : `${this.height}px`;
            }),
            mousedown: false,
            toolbarRef: {},
        };
    },
    mounted() {
        this.collapseContentChecker.onMounted();
        this.handleClick();
        this.toolbarRef = this.$el;
    },
    beforeDestroy() {
        this.collapseContentChecker.destroy();
    },
    created() {
        this.listen();
    },
    computed: {
        shiftTools() {
            const { disabled } = this;
            const tools = [];
            if (!disabled) {
                tools.push('edit');
            }
            tools.push('fullscreen');
            return tools;
        },
    },
    methods: {
        listen() {
            this.$on('ready', () => {
                this.quill.enable(false);
            });
            this.$on('edit', (editing = true) => {
                this.editMode = editing;
                this.quill.enable(editing);

                this.collapseContentChecker.resetStatus();
                // 进入编辑器前，重置编辑器和预览的状态
                reRenderContainer(this)();
                if (!editing) {
                    this.collapseContentChecker.check();// 恢复的时候也主动检查一下，因为有可能内容没有发生变化
                    this.quill.getModule('imageResize').hide();
                }
            });
            // 这个只是悬浮的toolbar上的事件，用来触发实际的全屏操作接口
            this.$on('fullscreen', () => {
                const { collapseContentChecker, fullscreenHandler } = this;
                fullscreenHandler.toggle();
                this.$nextTick(() => {
                    if (collapseContentChecker.collapsed) {
                        // 收起的时候需要滚动内容到最上面
                        collapseContentChecker.target.scrollTop = 0;
                    }
                });
            });
            // 切换全屏的时候更新一下图片编辑器的位置
            this.$on('fullscreenchange', () => {
                this.quill.getModule('imageResize').onUpdate();
            });
        },
        // TODO: 这里还有点问题，应该等返回结果回来了才能退出编辑
        confirm() {
            const { quill, innerContent } = this;
            this.$emit('confirm', {
                html: innerContent,
                text: removeEmoji(quill.getText().slice(0, this.textMaxCount)),
            });
            this.$emit('edit', false);
        },
        cancel() {
            this.$emit('cancel', this.innerContent);
            this.$emit('edit', false);
            const autoSave = this.quill.getModule('automaticSave');
            if (autoSave) {
                autoSave.destroy();
            }
        },
        // @override
        handleDisable() {
            const handler = () => {
                if (this.quill) {
                    this.quill.enable(!this.disabled && this.editMode);
                }
            };
            this.$watch('disabled', { handler, immediate: true });
            this.$watch('editMode', { handler, immediate: true });
        },
        handleClick() {
            const el = this.$el.querySelector('.ql-editor__container');
            if (!el) {
                return;
            }
            const vm = this;
            // 预览图片和链接跳转要阻断。
            el.addEventListener('mousedown', (ev) => {
                if (this.editing) {
                    return;
                }
                if (ev.target.nodeName === 'IMG'
                || closest(ev.target, '#preview-cover')
                || ev.target.nodeName === 'A'
                || closest(ev.target, targetEl => targetEl.nodeName === 'A')) {
                    return;
                }
                this.mousedown = true;
                setTimeout(() => {
                    this.mousedown = false; // 判断是否是拖拽
                }, 300);
            }, true);
            el.addEventListener('mouseup', () => {
                if (vm.mousedown && !vm.editing) {
                    vm.mousedown = false;
                    vm.$emit('on-click');
                    if (vm.disabled) {
                        return;
                    }
                    vm.$emit('edit');
                }
            }, false);
        },
    },

};
</script>
<style lang="less">
@import '~@/theme/var';
.ql-editor-plus{
  position: relative;
  overflow:visible;
  background: #fff;
  & .ql-editor__fullscreen-header{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .ql-editor-plus__fulltitle{
        font-size: 16px;
        height: 48px;
        line-height: 48px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .ql-editor__fullscreen-action{
        &+.ql-editor__fullscreen-action{
            margin-left: 16px;
        }
        border: 1px solid #e1e1e1;
        font-size: 12px;
        display: inline-block;
        padding: 0px 8px;
        border-radius: 4px;
        color: #999;
        cursor: pointer;
      }
  }
  & .ql-editor__wrapper{
    background-color: @bgColor;
  }
  & .ql-editor__wrapper.ql--fullscreen{
    border-color: transparent;
    background-color: transparent;
  }
  &__container{
    &:extend(.ql-editor__container);

    &.ql-container.ql-snow{
      border: 1px solid transparent;
    }
  }
}


</style>
