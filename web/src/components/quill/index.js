
// 这几个样式有重叠的部分
// import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
// import 'quill/dist/quill.bubble.css';

import QuillEditor from './QlEditor';
import QuillEditorPlus from './QlEditorPlus';
import './editor.less';

export { Quill } from './initial.quill';

const install = (Vue) => {
    // console.log('arch-ui===', QuillEditor.name, QuillEditorPlus.name);
    Vue.component(QuillEditor.name, QuillEditor);
    Vue.component(QuillEditorPlus.name, QuillEditorPlus);
};
export default { QuillEditor, QuillEditorPlus, install };
export { QuillEditor, QuillEditorPlus };
