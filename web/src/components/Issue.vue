<template>
    <div class="jacp-issue__root">
        <div class="jacp-issue__item">
            <label class="jacp-issue">缺陷标题</label>
            <label class="jacp-issue__time">缺陷单号</label>
            <label class="jacp-issue__time">缺陷提出人</label>
            <label class="jacp-issue__time">缺陷解决人</label>
            <label class="jacp-issue__time">更新时间</label>
            <label class="jacp-issue__time">状态</label>
            <label class="jacp-issue__time">操作</label>
            <!--<label class="jacp-issue__time">来源</label>-->
        </div>
        <div
            class="jacp-issue__item"
            v-for="data in list"
            :key="data.id"
        >
            <span
                :title="data.issueName"
                class="jacp-issue__title"
            >
                {{ data.issueName || "--" }}
            </span>
            <span class="jacp-issue__issueKey__nowrap">
                {{ data.issueKey }}
            </span>
            <span class="jacp-issue__issueStatus">
                <jacp-erp
                    v-if="data.reporter.erp"
                    class="jacp-issue_time"
                    :data="data.reporter"
                />
            </span>
            <span class="jacp-issue__issueStatus">
                <jacp-erp
                    v-if="data.assignee.erp"
                    class="jacp-issue_time"
                    :data="data.assignee"
                />
            </span>
            <span class="jacp-issue__time">
                {{ +data.uTime|jacp-local-time }}
            </span>
            <span class="jacp-issue__issueStatus">
                {{ data.statusName }}
            </span>
            <!--<span class="jacp-issue__issueStatus">-->
            <!--{{ data.appCode }}-->
            <!--</span>-->

            <span>
                <a
                    target="_blank"
                    v-if="data.callbackUrl"
                    :href="data.callbackUrl"
                >
                    详情
                </a>
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'JacpIssue',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.jacp-issue {
    &__root{
        font-size: 12px;
        line-height: 1.2;
    }
    &__item{
        display: flex;
        margin-bottom: 15px;
        &>label{
            height: 40px;
            background: #FAFAFA;
            color: @fontThirdColor;
            line-height: 40px;
            padding-left: 5px;
        }
        &>label:nth-child(1) {
            flex-grow: 1;
        }
        &>label:nth-child(-2) {
            flex-grow: 1;
        }
        &>span{
            padding-left: 10px;
        }
        &>span:nth-child(1) {
            flex-grow: 1;
        }
        &>*{
            flex: 0.5;
        }
    }
    &__title {
        width: 100%;
        padding-left: 10px;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    &__time{
        width: 100%;
        padding-left: 10px;
    }
    &__issueKey,
    &__issueStatus {
        min-width: 50px;
        width: 100px;
        padding-left: 10px;
    }
}
</style>
