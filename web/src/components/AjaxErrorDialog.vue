<template>
    <el-dialog
        :title="title"
        :visible="showErrorDialog"
        :before-close="close"
        :close-on-click-modal="false"
        width="40%"
        class="error-dialog"
        custom-class="j-dialog"
    >
        <div class="content">
            <div
                v-for="(item, index) in errorMessagesStack"
                :key="`errorStack${index}`"
                class="content-item"
            >
                <i :class="item | getIcon" />
                <!-- 500的统一提示，其他的取msg -->
                <div
                    style="line-height: 24px;"
                    v-html=" item.code && item.code === 500
                        ? '当前操作无响应或请求失败，请重试。'
                        : (item.message || '系统异常，请联系管理员。')"
                />
            </div>
        </div>
        <span
            slot="footer"
            class="dialog-footer"
        >
            <el-button
                type="primary"
                @click="close"
            >确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
const normalizedErrorMessage = (err = {}) => {
    if (err.data?.config?.ignoreForJagile) {
        return undefined;
    }
    if (err.data && err.data.response) {
        return err.data.response.data;
    }
    if (err.response) {
        return err.response.data;
    }
    return err;
};
const errorIconType = {
    400: 'el-icon-warning',
    402: 'el-icon-warning',
    500: 'el-icon-error',
};
export default {
    name: 'JAjaxErrordialog',
    props: {
        errorStack: {
            type: Array,
            default: () => [],
        },
    },
    filters: {
        getIcon(err) {
            const { code = 500 } = err;
            return errorIconType[code];
        },
    },
    methods: {
        close() {
            this.$emit('close');
        },
    },
    computed: {
        showErrorDialog() {
            return !!this.errorMessagesStack.length;
        },
        errorMessagesStack() {
            return this.errorStack.reduce((result, error) => {
                const err = normalizedErrorMessage(error);
                if (err) {
                    result.push(err);
                }
                return result;
            }, []);
        },
        title() {
            return '提示';
            // if (this.errorMessagesStack.length === 1) {
            //     return this.errorMessagesStack[0].code === 402 ? '提示' : '异常';
            // }
            // return '异常';
        },
    },
};
</script>

<style lang='less' scoped>
.error-dialog{
    .content-item{
        overflow: hidden;
        text-overflow: ellipsis;
        display: flex;
        align-items: flex-start;
    }
    [class^="el-icon"] {
        margin: 0 8px;
        font-size: 24px;
    }
    [class*="error"] {
        color: #F55445;
    }
    [class*="warning"] {
        color: #e6a23c;
    }
}
</style>
