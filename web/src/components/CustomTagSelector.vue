<template>
    <div class="custom-tag-group">
        <custom-tag
            class="custom-tag-group__item"
            v-for="(item, index) in innerValue"
            :key="`selected-${item.name}-${index}`"
            :color="item.color"
            :closable="!disabled"
            @on-close="handleDelete(item, index)"
        >
            {{ item.name }}
        </custom-tag>
        <div
            class="custom-tag-group__add"
            v-show="!disabled"
        >
            <el-popover
                placement="bottom"
                width="300"
                trigger="click"
                :disabled="disabled"
                :append-to-body="appendToBody"
            >
                <button
                    slot="reference"
                    ref="addButtonEl"
                    :class="['custom-tag-group__button', {
                        disabled: disabled
                    }]"
                    type="button"
                    :disabled="disabled"
                    @click="handleAdd"
                >
                    添加新标签
                </button>
                <div style="min-height: 34px;">
                    <custom-tag
                        class="custom-tag-group__item"
                        v-for="(item, index) in innerSuggestions"
                        :key="`${item.name}-${index}`"
                        :color="item.color"
                        @click.native="handleSelect(item)"
                        v-show="!innerValue.includes(item)"
                    >
                        {{ item.name }}
                    </custom-tag>
                    <jacp-text
                        v-if="nodata"
                        type="disable"
                        size="12"
                    >
                        {{ noDataText }}
                    </jacp-text>
                </div>
            </el-popover>
        </div>
    </div>
</template>
<script>
import uniqBy from 'lodash/uniqBy';
import isFunction from 'lodash/isFunction';
import i18n from '$platform.i18n';

const suggestionsCache = new WeakMap();
export default {

    /* 这个是在卡片详情和新建的时候使用的 */
    name: 'CustomTagSelector',
    props: {
        value: {
            type: Array,
        },
        disabled: Boolean,
        suggestions: {
            type: Array,
            default: () => [],
        },
        appendToBody: Boolean,
        // 点新增的时候再去加载选项
        remote: { type: Boolean, default: false },
        remoteMethod: { type: Function },
        noDataText: {
            type: String,
            default: i18n.t('jacp.tagNoData'),
        },
    },
    data() {
        return {
            innerValue: [],
            remoteSuggestions: [],
        };
    },
    methods: {
        handleSelect(item) {
            this.innerValue.push(item);
            this.$emit('input', this.innerValue);
            this.$emit('on-select', item);
        },
        handleDelete(item, index) {
            this.$emit('on-delete', item);
            this.innerValue.splice(index, 1);
            this.$emit('input', this.innerValue);
        },
        async handleAdd() {
            if (this.disabled) return;
            if (suggestionsCache.has(this)) return;
            if (this.remote && isFunction(this.remoteMethod)) {
                const result = await Promise.resolve(this.remoteMethod());
                suggestionsCache.set(this, result);
                this.remoteSuggestions = result;
            }
        },
    },
    computed: {
        innerSuggestions() {
            const ids = this.innerValue.map(o => o.id);
            const suggestions = uniqBy([
                ...this.suggestions,
                ...this.remoteSuggestions], 'id').filter(o => !ids.includes(o.id));
            return suggestions;
        },
        nodata() {
            if (this.remote) {
                return !this.remoteSuggestions.length && !this.innerSuggestions.length && suggestionsCache.has(this);
            }
            return !this.innerSuggestions.length;
        },
    },
    watch: {
        value: {
            immediate: true,
            handler(val) {
                if (!val) return;
                this.innerValue = [].concat(val);
            },
        },
    },
    beforeDestroy() {
        suggestionsCache.delete(this);
    },
};
</script>
