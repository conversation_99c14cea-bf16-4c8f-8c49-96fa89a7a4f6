<script>
export default {
    name: 'JacpTableHeaderResize',
    data() {
        return {
            // 数据在排序时所使用排序策略的轮转顺序
            sortOrders: ['asc', 'desc', null],
        };
    },
    props: {
        columns: Array,
        // 每次列表都会刷新，刷新以后需要展示是否有正在排序状态的
        sortedColumn: {
            type: String,
            default: 'index',
        },
        sortedOrder: {
            type: String,
            default: 'asc', // order: ascending, descending
        },
        disabledSortbyColumns: {
            type: Boolean,
            default: false,
        },
        sortableColumns: {
            type: Array,
            /* eslint-disable */
            // 支持排序的字段
            // TODO 这里需要改成动态的，或者按照一定规则的
            // 需求列表里叫priorityId，card里叫priority。。。。。
            // default: () => (['priority', 'priorityId', 'cTime', 'submitTime', 'demandCode', 'index', 'estimated', 'cardCode', 'startDate', 'deadline', 'uTime']),
            default: () => {
                const demandSortableColumns = ['priorityId', 'submitTime', 'demandCode', 'bizStatusUTime', 'expectedReleaseDate', 'status'];
                const cardSortableColumns = ['priority', 'cTime', 'index', 'cardCode', 'startDate', 'deadline', 'uTime', 'testDoneTime', 'realDate'];
                return [].concat(demandSortableColumns, cardSortableColumns);
            },
        },
        expandAll: {
            type: Boolean,
            default: false,
        },
        batch: {
            type: Boolean,
            default: false,
        },
        columnOptions: {
            default: () => ({}),
        },
        showExpand: {
            type: Boolean,
            default: true,
        },
    },
    methods: {
        handleHeaderClick(event, rowData) {
            if (this.sortableColumns.includes(rowData.name)) {
                let newOrder;
                // 当前排序列，轮转排序策略
                if (this.sortedColumn === rowData.name) {
                    const preOrder = this.sortOrders.indexOf(this.sortedOrder);
                    const nowOrderIndex = preOrder + 1 >= this.sortOrders.length ? 0 : preOrder + 1;
                    newOrder = this.sortOrders[nowOrderIndex];
                } else {
                    newOrder = this.sortOrders[0];
                }
                // 默认远端排序
                // 如果数据库里存的index有问题这里就会异常。。比如增加一个新的index的列出来
                this.$emit('sort', rowData, newOrder);
            }
        },
        toggleExpand(e) {
            e.stopPropagation();
            this.$emit('update:expandAll', !this.expandAll);
        },
        columnResizeStart(e, row) {
            e.preventDefault();
            let pointPosition = e.clientX;
            const _t = this;
            const target = e.target;
            const currentTd = target.parentNode;
            const currentWidth = currentTd.offsetWidth;
            const opstions = this.columnOptions;
            document.addEventListener('mousemove', columnResize, false);
            document.addEventListener('mouseup', columnResizeEnd, false);
            function columnResize(e) {
                e.preventDefault();
                const tmpWidth = e.clientX - pointPosition;
                opstions[row.name] = `${currentTd.offsetWidth + tmpWidth > 80 ? currentTd.offsetWidth + tmpWidth : 80}px`;
                pointPosition = e.clientX;
            }
            function columnResizeEnd(e) {
                e.preventDefault();
                _t.$emit('resize-column');
                document.removeEventListener('mousemove', columnResize);
                document.removeEventListener('mouseup', columnResizeEnd);
            }
        },
    },
    render(h) {
        return (
          <thead>
            <tr>
              {
                this.columns.filter(item => item.show).map((row, index) => (<td class= {{
                    [`j-table__header__${row.name}`]: true,
                    'j-table__header--center': row.name.indexOf(['priorityId']) !== -1,
                }} style={{width: this.columnOptions[row.name] || '60px'}}>
                    <div class={{
                        [`${this.sortedOrder}ending`]: this.sortedColumn === row.name,
                    }}
                    on-click={ $event => this.handleHeaderClick($event, row) }
                    >
                        { index === 0 && this.showExpand ?
                            <i
                                class={{'j-table__header__expandAll': true, 'icon-minus': this.expandAll, 'icon-plus': !this.expandAll, 'jacp-hide': this.batch}}
                                onClick={this.toggleExpand}></i> : '' }
                        { row.label }
                        {
                            (!this.disabledSortbyColumns &&
                            this.sortableColumns.includes(row.name)) ? <span class="caret-wrapper">
                                <i class="sort-caret ascending"></i>
                                <i class="sort-caret descending"></i>
                            </span> : ''
                        }
                    </div>
                    <i class={{'j-table__header__handler': true, 'jacp-hide': this.batch && index === 0}}
                        onMousedown={$event => this.columnResizeStart($event, row)}></i>
                </td>))
              }
              <td></td>
            </tr>
          </thead>
        );
    },
};
</script>
