<template>
    <div class="jacp-setting-fields">
        <div class="basic-fields">
            <fields-group
                v-for="(value, key) in categories"
                :key="key"
                :label="key"
                :list="value"
                @updateFields="updateFields"
            />
        </div>
        <div class="midlebar" />
        <div class="other-fields">
            <div v-if="orgKey === 'displayFields' && module === 'demand'">
                <div class="bg-purple-light">固定显示字段</div>

                <div class="fixcolumnwrap">
                    <span>需求名称</span>
                    <el-switch
                        v-model="columnFixedObj.name"
                        :width="32"
                    />
                </div>
                <div class="fixcolumnwrap">
                    <span>操作</span>
                    <el-switch
                        v-model="columnFixedObj.operation"
                        :width="32"
                    />
                </div>
            </div>

            <div class="grid-content bg-purple">
                <div class="bg-purple-light">
                    已选择字段
                </div>
                <ul class="jacp-setting-fields__sortlist">
                    <li
                        v-for="field in sortedList"
                        :key="field.name"
                        :data-id="field.name"
                        :data-disabled="field.disabled"
                    >
                        <div
                            :class="{
                                'drag-handle': true,
                                'drag-disabled': field.disabled === true,
                            }"
                        >
                            <i class="jacp-icon jacp-icon-yidong" />
                            <span>{{ field.label }}</span>
                        </div>
                        <span
                            class="delete"
                            @click="handleCancel(field.orgIndex)"
                        >
                            <jacp-icon
                                name="icon-cancel"
                                v-if="!field.disabled"
                            />
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import Vue from 'vue';
import sortBy from 'lodash/sortBy';
import Sortable from 'sortablejs';

Vue.component('fields-group', {
    template: `
        <div class="grid-content bg-purple">
            <div class="bg-purple-light">{{ label }}</div>
            <ul class="jacp-setting-fields__list">
              <li v-for="(field, $index) in list"  :key="field.name">
                <el-checkbox
                    :checked="!!field.show"
                    :value="!!field.show"
                    :disabled="field.disabled"
                    @change="$emit('updateFields', field.orgIndex)"
                ></el-checkbox>{{ field.label }}
            </li>
            </ul>
      </div>
    `,
    props: {
        label: { type: String, required: true },
        list: {
            type: Array,
            default: () => [],
        },
    },
});
export default {
    name: 'JacpPersonalFields',
    props: {
        label: String,
        orgList: {
            type: Array,
            default: () => [],
        },
        orgKey: String,
        sortable: {
            type: Boolean,
            default: true,
        },
        disabledList: {
            type: Array,
        },
        module: String,
    },
    data() {
        return {
            sortableIns: null,
            fieldList: this.orgList,
            nameList: [],
            columnFixedObj: {
                name: localStorage.jacp_demand_columnFixed ? JSON.parse(localStorage.jacp_demand_columnFixed).name : false,
                operation: localStorage.jacp_demand_columnFixed
                    ? JSON.parse(localStorage.jacp_demand_columnFixed).operation : true,
            },
        };
    },
    mounted() {
        if (this.sortable) {
            this.initSortablePlugin();
        }
    },
    computed: {
        categories() {
            const categories = {};
            // 处理一下分类
            this.fieldList.forEach((item) => {
                if (typeof categories[item.group] === 'undefined') {
                    categories[item.group] = [];
                }
                categories[item.group].push(item);
            });
            return categories;
        },
        sortedList() {
            const unsort = this.fieldList.filter(item => item.show === 1);
            const sorted = sortBy(unsort, ['index']);
            return sorted;
        },
    },
    methods: {
        getGroupByName(name = 'default') {
            return this.groups[name];
        },
        updateFields(index) {
            // 需要给el-checkbox增加一个value属性才能在另一个列表里点删除后同步界面
            const field = this.fieldList[index];
            field.show = !field.show ? 1 : 0;
            field.index = this.nameList.length;
            if (field.show) {
                this.nameList.push(field.name);
            } else {
                this.nameList.splice(this.nameList.indexOf(field.name), 1);
            }
        },
        initSortablePlugin() {
            const options = {
                handle: '.drag-handle',
                animation: 150,
                dataIdAttr: 'data-id',
                ghostClass: 'drag-ghost',
                chosenClass: 'drag-chosen',
                onMove: this.onMove,
                onEnd: this.onEnd,
            };
            this.sortableIns = new Sortable(this.$el.querySelector('.jacp-setting-fields__sortlist'), options);
            this.nameList = this.sortableIns.toArray();
        },
        handleCancel(index) {
            this.updateFields(index);
        },
        onEnd({ oldIndex, newIndex }) {
            if (oldIndex === newIndex) {
                return;
            }
            const targetItem = this.nameList.splice(oldIndex, 1);
            this.nameList.splice(newIndex, 0, targetItem[0]);
        },
        onMove(evt) {
            const cur = evt.dragged;
            const target = evt.related;
            if (target.dataset.disabled || cur.dataset.disabled) {
                return false; // cancel drag
            }
            return true;
        },
    },
    watch: {
        nameList: {
            deep: true,
            handler(list) {
                this.fieldList.forEach((item) => {
                    const i = list.indexOf(item.name);

                    /* eslint-disable no-param-reassign   */
                    item.index = i;
                });
            },
        },
    },
};
</script>

<style lang="less">
  @import '~@/theme/var';
  @lineHeight: 36px;
  .jacp-setting-fields{
      color: var(--color--base--content);
      display: flex;
      .basic-fields{
          flex: 1;
      }
      .midlebar{
          width: 1px;
          border-right: 1px solid var(--color--base--hr);
      }
      .other-fields{
        width: 223px;
      }
      .basic-fields, .other-fields{
          padding: var(--gutter--medium);
          max-height: 498px;
          overflow: auto;
      }
      li{
        list-style-type:none;
      }
      .bg-purple-light{
          color: var(--color--secondary--content);
          font-size: var(--font-size--description);
      }
      .bg-purple:not(:first-child){
          margin-top: var(--gutter--large);
      }
      &__list{
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          li{
            margin-right: 8px;
            width: 135px;
            line-height: @lineHeight;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          li:nth-child(4n){
              margin-right: 0;
          }
      }
      &__sortlist{
        margin-top: var(--gutter--small);
        li{
            min-width: 150px;
            line-height: 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            //padding-left: var(--gutter--small);
            .icon-cancel{
                color: var(--color--base--border)
            }
            &:hover{
                background-color: var(--color--base--background);
                border-radius: var(--radius--default);
            }
            .delete{
                padding: 0 var(--gutter--small);
                cursor: pointer;
            }
        }
        .drag-handle{
            cursor: move;
          }

          .drag-disabled{
              cursor: not-allowed;
              background-image: none;
          }
          .drag-ghost {
            opacity: .5;
            background-color: @menuFocusedBgColor;

          }
          .drag-chosen{
            background-color: @mainBgColor;
          }
      }
      .el-checkbox:last-of-type {
          margin-right: 8px;
      }
      .fixcolumnwrap{
          line-height: 40px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 var(--gutter--small);
          .el-switch__core{
              height: 16px;

          }
          .el-switch__core:after{
              width: 14px;
              height: 14px;
              top: 0;
          }
          .el-switch.is-checked .el-switch__core::after{
              margin-left: -15px;
          }
      }
  }
</style>
