<template>
    <div
        class="list-batch-box"
        :class="{'batch-bar': batchFlag}"
    >
        <el-button
            type="primary"
            size="mini"
            v-if="!batchFlag"
            @click="$emit('openBatch')"
        >
            批量操作
        </el-button>
        <div
            v-if="batchFlag"
            style="display: flex"
        >
            <el-button
                class="j-mgr16"
                type="primary"
                size="mini"
                v-show="cardsList.length !== batchList.length"
                @click="checkAll(true)"
            >
                全选
            </el-button>
            <el-button
                type="primary"
                class="j-mgr16"
                size="mini"
                v-show="cardsList.length === batchList.length"
                @click="checkAll(false)"
            >
                取消全选
            </el-button>
            <div>
                <slot name="options" />
            </div>
            <el-button
                class="j-mgl16"
                type="primary"
                plain
                size="mini"
                @click="closeBatch"
            >
                结束批量
            </el-button>
        </div>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'JacpListBatch',
    props: {
        batchFlag: {
            type: Boolean,
            default: false,
        },
        batchList: {
            type: Array,
            default: () => [],
        },
        cardsList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
        };
    },
    methods: {
        checkAll(flag) {
            this.$emit('checkAll', flag);
        },
        closeBatch() {
            this.$emit('closeBatch');
        },
    },
};
</script>
<style lang="less">
.list-batch-box{
    position: absolute;
    left: 0;
    bottom: 4px;
    // width: 100%;
    padding: 0 16px;
    & .el-button {
        height: 28px;
        border-radius: 4px;
    }
}
</style>
