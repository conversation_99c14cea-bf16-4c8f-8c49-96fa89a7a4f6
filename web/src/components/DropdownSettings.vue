<template>
    <el-dropdown
        trigger="click"
        @command="handleCommand"
        class="jacp-dropdown-settings"
    >
        <span class="el-dropdown-link">
            <jacp-icon
                name="el-icon-more"
                active
            />
        </span>
        <el-dropdown-menu
            slot="dropdown"
        >
            <el-dropdown-item
                v-for="(item, $index) in items"
                :divided="item.divided"
                :key="$index"
                :disabled="item.disabled"
                :command="$index"
                class="j-nowrap-ellipsis "
            >
                <jacp-icon :name="item.icon" />
                <span>{{ item.text }}</span>
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script type="text/javascript">
import { mapState } from 'vuex';
import unionBy from 'lodash/unionBy';
import PersonalModel from '@/models/personal';
import Dialog from '@/models/dialog';
import personalFieldsDialog from './SettingFields';
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

const filter = ['name', 'cardName'];
const demandData = [
    {
        text: '导出需求',
        command: 'export',
        icon: 'el-icon-document',
        divided: true,
    }
];
const remandSetting = [
    {
        text: '设置提醒',
        command: 'remand',
        icon: 'el-icon-alarm-clock',
        divided: true,
    }
];
const demandDataImport = {
    text: '导入需求',
    command: 'import',
    val: 'demand',
    icon: 'el-icon-document',
    divided: true,
};
const cardData = [
    {
        text: '按处理人导出',
        command: 'export',
        val: 'processors',
        icon: 'el-icon-document',
        divided: true,
    },
    {
        text: '按工作项导出',
        command: 'export',
        val: 'card',
        icon: 'el-icon-document',
    },
];
const cardDataImport = {
    text: '导入工作项',
    command: 'import',
    val: 'card',
    icon: 'el-icon-document',
    divided: true,
};
export default {
    name: 'JacpDropdownSettings',
    data() {
        return {
        };
    },
    props: {
        // spacePrivilage: Object,
        label: String,
        moduleKey: String,
        settingType: String,
        archived: {
            type: Boolean,
            default: false,
        },
        disabled: {
            default: () => [],
            type: Array,
        },
        unShowSettingFields: {
            default: () => [],
            type: Array,
        },
    },
    methods: {
        popupSettingField(options) {
            const orgList = [...options.list].filter(
                e => !this.unShowSettingFields.includes(e.name)
            ).map((item, orgIndex) => Object.assign({}, item,
                    { 
                        orgIndex, 
                        disabled: filter.indexOf(item.name) !== -1 
                    }));
            return Dialog.confirm({
                title: `${options.title}`,
                slot: personalFieldsDialog,
                confirmBtnText: '确定',
                customClass: 'j-dialog--large dialog-border-tmb',
                width: '840px',
                beforeConfirm: (vm) => {
                    const {
                        orgKey, fieldList, 
                        sortedList, columnFixedObj,
                    } = vm;
                    const tempFieldList = unionBy(fieldList, options.list, 'name');
                    const tempSortedList = unionBy(sortedList, options.list, 'name');
                    const format = (list, sortedNameList) => list.map(
                        (item) => {
                            const o = Object.assign({}, item);
                            o.index = sortedNameList.indexOf(item.name);
                            // TODO: what??附加的参数还后期重构
                            delete o.orgIndex;
                            delete o.sortable;
                            delete o.disabled;
                            return o;
                    });
                    const params = {
                        module: this.moduleKey,
                        type: this.settingType,
                        subType: orgKey,
                        data: format(
                            tempFieldList, 
                            tempSortedList.map(item => item.name)
                        ),
                    };
                    PersonalModel.saveSettings(params).then(() => {
                        this.$emit(`save-${options.key}`, sortedList);
                    });
                    // 固定列的设置存在前端
                    localStorage.jacp_demand_columnFixed = JSON.stringify(columnFixedObj);
                    this.$emit('save-columnFixedSetting');
                },
                slotProps: {
                    // 需求名称不能拖拽,也不能取消勾选
                    orgList,
                    orgKey: options.key,
                    module: this.moduleKey,
                },
            });
        },
        handleCommand(index) {
            const item = this.items[index];
            const { command } = item;
            const { val } = item;
            const title = item.text;
            switch (command) {
            case 'import':
                this.$emit(command, val);
                break;
            case 'export':
                this.$emit(command, val);
                break;
            case 'remand':
                this.$emit(command, val)
                break;
            default:
                PersonalModel[command](this.moduleKey).then(
                    (list) => {
                        this.popupSettingField({
                            title,
                            key: item.key,
                            list,
                        });
                    });
                break;
            }
        },
        getParams(...arg) {
            return [this.settingType].concat(arg).join('.');
        },
    },
    computed: {
        ...mapState('chilldteamspace', ['spacePrivilage']),
        items() {
            const defaultValue = [
                {
                    text: '设置显示的字段',
                    command: 'getListDisplayFields',
                    key: 'displayFields',
                    icon: 'icon-edit_fill',
                },
                {
                    text: '设置导出的字段',
                    command: 'getListExportFields',
                    key: 'exportFields',
                    icon: 'icon-edit_fill',
                },
            ];
            let menuList = [];
            // TODO: 此处需要重写
            if (this.label === '卡片') {
                if (this.archived) {
                    cardDataImport.disabled = true;
                } else {
                    cardDataImport.disabled = this.spacePrivilage
                        && !this.spacePrivilage.createCard;
                }
                menuList = [
                    ...defaultValue, 
                    ...cardData, 
                    cardDataImport,
                ];
            } else if (this.label === '任务') {
                menuList = [{
                    text: '设置显示的字段',
                    command: 'getListDisplayFields',
                    key: 'displayFields',
                    icon: 'icon-edit_fill',
                }, {
                    text: '按任务导出',
                    command: 'export',
                    icon: 'el-icon-document',
                    divided: true,
                }];
            } else {
                menuList = [
                    ...defaultValue, 
                    ...demandData, 
                    demandDataImport,
                    ...remandSetting,
                ];
            }
            return menuList.filter(
                menu => !this.disabled.includes(menu.command));
        },
    },
};
</script>

<style lang="less">
.jacp-dropdown-settings{
    margin-left: 15px;
    .el-dropdown-menu__item{
        span{
        margin-left: 10px;
        }
    }
}
.minWidth{
    min-width: 55%;
    min-width: 55vw;
}
.el-dialog.dialog-border-tmb{
    .el-dialog__header{
        border-bottom: 1px solid var(--color--base--hr);
    }
    .el-dialog__body{
        padding: 0;
    }
    .el-dialog__footer{
        border-top: 1px solid var(--color--base--hr);
    }
}
</style>
