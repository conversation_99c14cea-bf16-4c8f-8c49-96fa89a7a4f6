<template>
    <el-dialog
        title="系统工具"
        :visible="showDebugFlag"
        class="jacp-debug"
        @close="closeDebug"
        ref="userInfo"
    >
        <div class="jacp-debug__content">
            <el-form
                :inline="true"
                label-width="80px"
                size="small"
            >
                <el-form-item label="切换用户">
                    <jacp-input-users
                        v-model="userInfo"
                        placeholder="输入用户姓名/工号"
                        :max-count="1"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button @click="doChangeUserAction">
                        {{ $t('jacp.button.changeUserBtn') }}
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-button @click="closeDebug">
                取 消
            </el-button>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import UserModel from '@/models/user';

export default {
    name: 'JacpDebug',
    props: {
        showDebug: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            userInfo: [],
            showDebugFlag: this.showDebug,
        };
    },
    methods: {
        doChangeUserAction() {
            UserModel.doChangeUserAction(this.userInfo[0].erp);
        },
        closeDebug() {
            this.showDebugFlag = false;
            this.$emit('update:showDebug', false);
        },
    },
    watch: {
        showDebug(value) {
            this.showDebugFlag = value;
            if (value) {
                this.$nextTick(() => {
                    this.$refs.userInfo.$el.querySelector('input').focus();
                });
            }
        },
    },
};
</script>
<style lang="less">
    .jacp-debug{
        &__content{
            min-height: 100px;
            display: flex;
            & .el-form{
                margin: auto 20px;
            }
        }
    }
</style>
