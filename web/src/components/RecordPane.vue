<template>
    <div
        class="jacp-record-pane"
        :class="[mode]"
    >
        <div
            class="jacp-record-pane__itemwrapper"
            v-for="(data, index) in list"
            :key="data.id"
        >
            <time
                class="jacp-record-pane__splitime"
                v-if="mode === 'default' && isDiffDate(index)"
            >
                <span class="jacp-record-pane__splitime--inner">
                    {{ +data.cTime | jacp-local-time('YYYY-MM-DD') }} 星期{{ +data.cTime | weekday }}
                </span>
            </time>
            <div
                class="jacp-record-pane__item"
            >
                <template v-if="$slots.prefix">
                    <slot
                        :style="{width: width}"
                        name="prefix"
                        :row="data"
                    />
                </template>
                <div class="jacp-record-pane__operator">
                    <span class="jacp-record-pane__operator__user">
                        <el-tooltip placement="top">
                            <div slot="content">{{ data.operator.name }}({{ data.operator.erp }})</div>
                            <jacp-erp
                                avatar
                                display-erp
                                :avatar-size="24"
                                :data="data.operator"
                                vertical
                                :disable-timline="data.operator.isCurrentUser"
                            />
                        </el-tooltip>
                    </span>
                    <span class="jacp-record-pane__operator__time">
                        <el-tooltip placement="top">
                            <div slot="content">
                                <span
                                    class="jacp-record-pane__time"
                                    v-if="mode === 'remark'"
                                >{{ +data.cTime|jacp-local-time('YYYY-MM-DD HH:mm') }}</span>
                            </div>
                            <span
                                class="jacp-record-pane__time"
                                v-if="mode === 'remark'"
                            >{{ +data.cTime|jacp-local-time('YYYY-MM-DD HH:mm') }}</span>
                        </el-tooltip>
                    </span>
                </div>
                <div
                    class="jacp-record-pane__remark"
                    v-if="mode === 'remark'"
                >
                    <slot :row="data">
                        <span v-html="$xss(data.remark)" />
                    </slot>
                </div>
                <el-tooltip
                    v-else
                >
                    <div
                        slot="content"
                        class="jacp-record-pane__remark__tooltip"
                    >
                        <slot :row="data">
                            <span v-html="$xss(data.remark)" />
                        </slot>
                    </div>
                    <div
                        :class="remarkWidth === '200px'
                            ? 'jacp-record-pane__remark__data' : 'jacp-record-pane__remark__moredata'"
                    >
                        <slot :row="data">
                            <span v-html="$xss(data.remark)" />
                        </slot>
                    </div>
                </el-tooltip>
                <label
                    class="jacp-record-pane__time"
                    v-if="mode !== 'remark'"
                >
                    {{ +data.cTime|jacp-local-time(timeFormat) }}
                </label>
            </div>
        </div>
        <slot name="pager" />
        <div
            class="j-nodata"
            v-if="!list.length"
        >
            暂无记录
        </div>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    // 就是原来的history.vue
    name: 'JacpRecordPane',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        // 增加个mode，用来展示不同的布局
        mode: {
            type: String,
            default: 'default',
            validator(val) {
                return ['simple', 'default', 'remark'].includes(val);
            },
        },
        timeFormat: {
            default: 'HH:mm',
            type: String,
        },
        width: {
            type: String,
            default: 'auto',
        },
        remarkWidth: {
            type: String,
            default: '200px',
        },
    },
    filters: {
        weekday(time) {
            const weekStr = '日一二三四五六';
            return weekStr.charAt(moment(time).day());
        },
    },
    methods: {
        isDiffDate(index) {
            const { list } = this;
            if (index === 0) return true;
            const next = list[index].cTime;
            const prev = list[index - 1].cTime;
            return !moment(prev).isSame(next, 'day');
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.jacp-record-pane {
    font-size: 14px;
    line-height: 1.5;
    .jacp-erp.has-avatar .jacp-erp__name,
    .jacp-erp__name{
        font-size: 14px;
    }
    &__item{
        display: flex;
        justify-content: space-between;
    }
    &.remark &__item{
        flex-direction: column;
        align-items: flex-start;
        margin: 12px 0;
        .jacp-erp{
            color: @remarkColor;
        }
        .jacp-record-pane__operator{
            display: flex;
            align-items: center;
        }
        .jacp-record-pane__time{
            margin-left: 8px;
            line-height: 24px;
        }
        .jacp-record-pane__remark{
            color: @fontColor;
            margin-left: 32px;
            max-width: 99%;
        }
    }
    &.default &__item{
        align-items: flex-start;
        margin: 12px 0;
        .jacp-record-pane__remark{
            flex-basis: 100%;
        }
        .jacp-record-pane__remark,
        .jacp-record-pane__time{
            padding-top: 2px;
        }
    }
    &.simple &__item{
        border-bottom: 1px solid @borderColorSecend;
        margin-bottom: 0;
        height: 48px;
        align-items: center;
    }
    &.simple &__itemwrapper:first-of-type{
        margin-top: -10px;
    }
    &__itemwrapper{
        display: flex;
        flex-direction: column;
    }
    &__time{
        text-align: right;
        color: @remarkColor;
    }
    &__remark{
        display: inline-block;
        white-space: pre-wrap;
        word-break: break-all;
        max-width: 400px;
        &__tooltip {
            max-width: 500px;
        }
    }
    &__splitime{
        text-align: center;
        margin: 6px 0 6px 0;
        position: relative;
        color: #999999;
        font-size: 12px;
        &--inner{
            display: inline-block;
            width: fit-content;
            padding: 0 16px;
            background: #FFF;
            z-index: 1;
            position: relative;
        }
        &::after{
            content: ' ';
            display: inline-block;
            height: 1px;
            width: 100%;
            position: absolute;
            top: 50%;
            left: 0;
            background-color: #F1F1F1;
        }
    }
    .jacp-record-pane__operator{
        &__user, &__time {
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
    .jacp-record-pane__remark__data {
        width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .jacp-record-pane__remark__moredata {
        display: inline-block;
        white-space: pre-wrap;
        word-break: break-all;
        width: 400px;
    }
}
</style>
