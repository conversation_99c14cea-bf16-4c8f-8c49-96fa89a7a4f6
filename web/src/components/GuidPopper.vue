<template>
    <div
        class="jacp-guide-popper-tooltip"
        role="tooltip"
        style="width: 236px"
    >
        <el-row><slot>{{ content }}</slot></el-row>
        <el-row
            type="flex"
            class="j-mgt16 "
        >
            <el-button
                class="jacp-guide-popper-tooltip__button"
                size="mini"
                v-if="confirmButtonText"
                :type="confirmButtonType"
                @click="() => {
                    $emit('confirm')
                    guide.hideGuide();
                    $emit('input', false);
                }"
            >
                {{ confirmButtonText }}
            </el-button>
            <el-button
                class="jacp-guide-popper-tooltip__button"
                size="mini"
                v-if="cancelButtonText"
                :type="cancelButtonType"
                @click="() => {
                    $emit('cancel')
                    guide.hideGuide();
                    $emit('input', false);
                }"
            >
                {{ cancelButtonText }}
            </el-button>
        </el-row>
        <div
            class="jacp-guide-popper-tooltip__arrow"
            data-popper-arrow
        />
    </div>
</template>
<script>
import isString from 'lodash/isString';
import Guide from '@/plugins/guidePopper';

export default {
    name: 'JacpGuidePopper',
    props: {
        value: { type: Boolean, default: false },
        reference: { type: [HTMLElement, String], default: '' },
        storagekey: { type: String, default: '' },
        content: { type: String, default: '' },
        confirmButtonText: { type: String, default: '让我试试' },
        confirmButtonType: { type: String, default: 'primary' },
        cancelButtonText: { type: String, default: '我知道了' },
        cancelButtonType: { type: String, default: 'text' },
        popperOptions: { type: Object, defualt: () => ({}) },
    },
    data() {
        return {
            guide: new Guide({ key: this.storagekey }),
        };
    },
    mounted() {
        this.init();
        this.$emit('inited', this.guide);
    },
    methods: {
        // Public
        init() {
            if (!this.reference) {
                return;
            }
            if (this.popper) {
                this.popper.update();
                return;
            }

            let guideRef = this.reference || null;
            if (isString(this.reference)) {
                guideRef = document.querySelector(this.reference);
            }
            if (!guideRef) {
                return;
            }
            const tooltipEl = this.$el;
            this.popper = this.guide.initPopper(guideRef, tooltipEl, this.popperOptions);
        },
    },
    computed: {
        visible() {
            return this.guide.visible;
        },
    },
    watch: {
        value: {
            immediate: true,
            handler(val) {
                if (!val) {
                    this.guide.hideGuide(false);
                } else {
                    this.init();
                }
            },
        },
    },
};
</script>
