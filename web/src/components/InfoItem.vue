<template>
    <div class="jacp-info-item">
        <label
            class="jacp-info-item__label"
            v-if="label"
            :style="{width: labelWidth + 'px'}"
        >{{ label }}</label>
        <div
            class="jacp-info-item__value"
            :style="{marginLeft: labelWidth + 'px'}"
        >
            <slot>
                <template v-if="value">
                    {{ value }}
                </template>
                <span
                    v-else
                    class="jacp-info-item__value__nodata"
                >{{ noDataText }}</span>
            </slot>
        </div>
    </div>
</template>
<script type="text/javascript">
export default {
    name: 'JacpInfoItem',
    inject: ['labelWidth'],
    props: {
        label: String,
        value: [String, Number],
    },
    computed: {
        noDataText() {
            return this.$t('jacp.noData');
        },
    },
};
</script>
<style lang="less">
.jacp-info-item{
    font-size: 12px;
    margin: 10px 0;
    clear: both;
    overflow: hidden;
    &__label{
        float: left;
        color: #666;
        display: inline-block;
        line-height: 1;
        padding: 8px 0;
        &:after{
            content: ':';
        }
    }
    &__value{
        padding: 4px 0;
        color: #333;
        line-height: 20px;
        word-break: break-all;

        &__nodata{
            color: #999;
        }
    }
}
</style>
