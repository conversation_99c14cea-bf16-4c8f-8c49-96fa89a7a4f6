<template>
    <el-dropdown
        split-button
        :type="type"
        trigger="click"
        @click="handleClick(-1)"
        @command="handleClick"
        class="jacp-multi-button__dropdown jacp-multi-button"
        :class="{
            'jacp-multi-button__dropdown--loading': loading
        }"
        size="small"
    >
        <jacp-icon
            name="el-icon-loading"
            :size="12"
            v-show="loading"
        />
        <span>{{ loading && loadingText ? loadingText : primaryButton.name }}</span>
        <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
                class="jacp-multi-button__dropdown__item"
                v-for="(button, $index) in buttons"
                :key="$index"
                v-show="$index > 0"
                :command="$index"
            >
                {{ button.name }}
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script type="text/javascript">
export default {
    name: 'JacpMultiButton',
    props: {
        type: {
            type: String,
            default: 'primary',
        },
        loading: {
            type: <PERSON>olean,
            default: false,
        },
        loadingText: {
            type: String,
        },
        buttons: {
            type: Array,
            default: () => [],
        },
    },
    methods: {
        handleClick(index) {
            const button = index === -1 ? this.primaryButton : this.buttons[+index];
            if (!button || !button.handle || !(button.handle instanceof Function)) {
                return;
            }
            button.handle.bind(this.$parent)();
        },
    },
    computed: {
        primaryButton() {
            return this.buttons[0] || {};
        },
    },
};
</script>

<style lang="less">
.jacp-multi-button{
    &__dropdown{
        height: 32px;
        overflow: hidden;

        &--loading{
            opacity: 0.6;
            pointer-events: none;
        }

        .el-dropdown__caret-button{
            height: 32px;
        }

        &__item{
            font-size: 12px;
        }
    }
}
</style>
