<template>
    <div
        class="jacp-guide"
        v-show="visible"
    >
        <div class="jacp-guide-content">
            <i
                class="el-icon-close"
                @click="$emit('update:visible', false)"
            />
            <el-carousel
                trigger="click"
                v-bind="[$attrs]"
                :height="height"
                :indicator-position="indicatorPosition"
                :autoplay="autoplay"
                :loop="loop"
            >
                <el-carousel-item
                    v-for="item in list"
                    :key="item"
                >
                    <img :src="item.startsWith('http') ? item : `/images/${item}`">
                </el-carousel-item>
            </el-carousel>
        </div>
    </div>
</template>
<script>
export default {
    name: 'JacpGuide',
    inheritAttrs: false,
    props: {
        visible: { type: Boolean, default: false },
        list: { type: Array, default: () => [] },
        height: { type: String, default: '480px' },
        autoplay: { type: Boolean, default: false },
        loop: { type: <PERSON>olean, default: false },
        indicatorPosition: { type: String, default: 'outside' },
    },
};
</script>
<style lang="less">
.jacp-guide{
    position: fixed;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0,0,0,.7);
    z-index: 9;
    display: flex;
    &-content{
        width: 768px;
        height: 480px;
        margin: auto;
        position: relative;
        & i.el-icon-close{
            width: 24px;
            height: 24px;
            position: absolute;
            right: -10px;
            top: -10px;
            z-index: 3;
            font-weight: 900;
            cursor: pointer;
            background-color: rgba(255, 255, 255, .7);
            border-radius: 12px;
            text-align: center;
            line-height: 24px;
            font-size: 12px;
        }
    }
    & .el-carousel{
        overflow: hidden;
    }
    & img{
        width: 100%;
        height: 100%;
    }
}
</style>
