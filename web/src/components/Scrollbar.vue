<script>
import PerfectScrollbar from 'perfect-scrollbar';

export default {
    name: 'JacpScrollbar',
    props: {
        options: {
            type: Object,
            default: () => ({
                wheelSpeed: 2,
                // wheelPropagation: false,
            }),
        },
    },
    template: '<div class="jacp-scrollbar"><slot></slot></div>',
    data() {
        return {
            psIns: undefined,
        };
    },
    methods: {
        update() {
            this.psIns.update();
        },
    },
    mounted() {
        this.psIns = new PerfectScrollbar(this.$el, this.options);
    },
    beforeDestory() {
        this.psIns.destroy();
    },
};
</script>

<style lang="less">
.jacp-scrollbar{
    overflow: hidden;
    position: relative;

    & .ps__rail-y,
    & .ps__rail-x{
        position: absolute;
    }
    & .ps__rail-y{
        right: 0!important;
        left: initial!important;
    }
    & .ps__thumb-y{
        border-radius: 3px;
        background-color: rgba(0, 0, 0, 0.2);
        position: absolute;
        right: 1px;
        width: 6px;
    }
}
</style>
