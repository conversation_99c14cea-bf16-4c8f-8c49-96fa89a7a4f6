<template>
    <jacp-form-select
        v-bind="$attrs"
        v-on="$listeners"
        :options="data"
        :confirm="confirm"
    />
</template>

<script type="text/javascript">
export default {
    name: 'JacpInputSelect',
    inheritAttrs: false,
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        confirm: { type: Boolean, default: false },
    },
};
</script>
