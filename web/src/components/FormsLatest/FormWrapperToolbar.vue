<template>
    <!-- <transition name="el-zoom-in-top"> -->
    <div>
        <ul
            ref="popper"
            v-show="showPopper"
            class="forms-wrapper-toolbar__wrap el-popover"
            :style="adjustingPlacementStyle"
        >
            <li
                class="forms-wrapper-toolbar__item"
                v-for="item in tools"
                :key="item"
                @mousedown.stop="$emit('on-tool-click', item, $event)"
            >
                <i
                    v-if="item.startsWith('copy')"
                    class="jacp-icon-copy_surface"
                    v-j-copy
                    title="复制"
                    :data-clipboard-text="clipboardText"
                />
                <i
                    v-else
                    :class="`jacp-icon-${item}`"
                />
            </li>
        </ul>
        <slot name="reference" />
    </div>
    <!-- </transition> -->
</template>
<script>
// FIXED: [zIndex问题](https://segmentfault.com/a/1190000014215259)
import Popper from 'element-ui/lib/utils/vue-popper';
import { on, off } from 'element-ui/lib/utils/dom';

export default {
    name: 'FormWrapperToolbar',
    mixins: [Popper],
    props: {
        placement: {
            type: String,
            default: 'right-start', // 目前只支持放左边或者右边，别的位置没有样式
        },
        tools: {
            default: () => ['edit', 'copy', 'del', 'download'],
            type: Array,
        },
        disabled: Boolean,
        openDelay: Number,
        clipboardText: String,
    },
    updated() {
        if (this.disabled) {
            return;
        }
        if (!this.showPopper) {
            return;
        }
        this.$nextTick(() => {
            if (this.popperJS) {
                this.updatePopper();
            }
        });
    },
    mounted() {
        this.bindEvent();
    },
    methods: {
        bindEvent() {
            let reference = this.reference || this.$refs.reference;
            const popper = this.popper || this.$refs.popper;
            if (!reference && this.$slots.reference && this.$slots.reference[0]) {
                reference = this.$slots.reference[0].elm;
            }
            if (!reference) {
                return;
            }
            // console.log('mounted bindEvent referenceElm', this.referenceElm);
            if (this.referenceElm) {
                return;
            }
            this.referenceElm = reference;
            // console.log('mounted bindEvent reference', reference);
            on(reference, 'mouseenter', this.handleMouseEnter);
            on(popper, 'mouseenter', this.handleMouseEnter);
            on(reference, 'mouseleave', this.handleMouseLeave);
            on(popper, 'mouseleave', this.handleMouseLeave);
        },
        handleMouseEnter() {
            if (this.disabled) {
                return;
            }
            clearTimeout(this.timer);
            if (this.openDelay) {
                this.timer = setTimeout(() => {
                    this.showPopper = true;
                }, this.openDelay);
            } else {
                this.showPopper = true;
            }
        },
        handleMouseLeave() {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.showPopper = false;
            }, 100);
        },
    },
    computed: {
        adjustingPlacementStyle() {
            const x = this.placement.includes('left') ? `-${26 * (this.tools.length - 1)}` : 0;
            return {
                transform: `translateX(${x}px)`,
            };
        },
    },
    beforeDestroy() {
        const { reference } = this;
        off(reference, 'mouseleave', this.handleMouseLeave);
        off(reference, 'mouseenter', this.handleMouseEnter);
        this.referenceElm = null;
        if (this.$refs.popper) {
            off(this.$refs.popper, 'mouseleave', this.handleMouseLeave);
            off(this.$refs.popper, 'mouseenter', this.handleMouseEnter);
        }
    },
    watch: {
        reference: 'bindEvent',
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.forms-wrapper-toolbar__wrap.el-popover,
.forms-wrapper-toolbar__wrap{
    display: flex;
    min-width: 40px;
    padding: 4px;
    li{
        list-style: none;
        & [class^="jacp-icon"] {
            color: @primaryColor;
            margin: 0 8px;
            cursor: pointer;
            font-size: 12px;
            &:hover{
                color: lighten(@primaryColor, 10%);
            }
        }
    }
    &[x-placement*="right"] {
        box-shadow: 6px 3px 5px 0 rgba(0, 0, 0, 0.1);
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px;
        border-left: none;
    }
    &[x-placement*="left"] {
        box-shadow: -6px 3px 5px 0 rgba(0, 0, 0, 0.1);
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: 20px;
        border-bottom-left-radius: 20px;
        border-right: none;
    }
}
</style>
