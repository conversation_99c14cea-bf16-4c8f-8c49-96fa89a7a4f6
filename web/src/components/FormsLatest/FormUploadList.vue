<template>
    <transition-group
        tag="ul"
        :class="[
            'form-upload-list',
            'form-upload-list--' + listType,
            { 'is-disabled': disabled }
        ]"
        name="fade"
    >
        <li
            v-for="(file, index) in files"
            :key="file.id || file.uid"
            :class="{
                'disabled': disabled || file.readOnly,
            }"
        >
            <form-wrapper-toolbar
                :tools="(disabled || file.readOnly) ? ['download'] : ['download', 'del']"
                :offset="toolbarOffset"
                @on-tool-click="$emit($event, file, index)"
                :placement="toolbarPlacement"
            >
                <div
                    slot="reference"
                    class="form-upload-list__item"
                >
                    <i
                        class="form-upload-list__item-icon"
                        :class="`icon-filetype-${getFileTypeIconName(file.name)}`"
                    />
                    <a
                        class="form-upload-list__item-name j-hover-highlight"
                        :title="file.name"
                        @click="handleClick(file, index)"
                    >
                        {{ file.name }}
                    </a>
                    <label
                        class="form-upload-list__item-label"
                        v-if="file.status !== 'uploading'"
                    >
                        <slot
                            name="label"
                            :file="file"
                        /></label>
                    <el-progress
                        v-if="file.status === 'uploading'"
                        :type="listType === 'picture-card' ? 'circle' : 'line'"
                        :stroke-width="listType === 'picture-card' ? 6 : 2"
                        :percentage="parsePercentage(file.percentage)"
                    />
                </div>
            </form-wrapper-toolbar>
        </li>
    </transition-group>
</template>
<script>
import FormWrapperToolbar from './FormWrapperToolbar';
import { getFileTypeIconName } from '@/plugins/utils.filetype';

export default {
    name: 'JacpFormUploadList',
    data() {
        return {
            // filetypeIconMap,
            // focusing: false,
        };
    },
    components: { FormWrapperToolbar },
    props: {
        files: {
            type: Array,
            default() {
                return [];
            },
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        // handlePreview: Function,
        listType: String,
        toolbarPlacement: {
            type: String,
            default: 'left-start',
        },
        toolbarOffset: {
            type: Number,
            default: 6,
        },
    },
    methods: {
        parsePercentage(val) {
            return parseInt(val, 10);
        },
        handleClick(file, index) {
            this.$emit('on-preview', file, index);
        },
        getFileTypeIconName,
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.mapFiletypes(length(@filetypes));
.form-upload-list{
    & .el-progress__text{
        // 在el-dialog里用的时候word会被转行
       word-break: initial;
    }
    & li{
         list-style: none;
         position: relative;
         padding: 4px 0;
         &:hover{
             background-color: #E9F4FD;
         }
    }
    &__item{
        display: flex;
        font-size: 12px;
        vertical-align: middle;
        align-items: center;
        &-name{
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: color .3s;
            white-space: nowrap;
            font-weight: normal;
            line-height: 16px;
            height: 16px;
            margin-left: 8px;
        }
        &-icon{
            width: 16px;
            height: 16px;
            display: block;
        }
        &-label{
            display: none;
            background-color: #E9F4FD;
            position: absolute;
            right: 0;
            top: 5px;
            padding: 0 4px;
            cursor: pointer;
            line-height: 1.2;
        }
    }
    &:not(.is-disabled) {
        .form-upload-list__item:hover{
            & .form-upload-list__item-label{
                display: block;
            }
        }
    }
}
</style>
