<template>
    <el-tooltip
        :content="disabledText"
        :placement="tooltipPlacement"
        :disabled="!disabledText"
    >
        <div :class="{'jacp-form-select': true, 'jacp-simple-select': simple}">
            <el-select
                v-auto-width="autoWidth"
                v-bind="$attrs"
                v-on="inheritListeners"
                :size="size"
                :value="currentValue"
                :disabled="disabled"
                :no-data-text="noDataText"
                :placeholder="disabled ? noDataText : placeholder"
                @visible-change="handleVisibleChange"
                @change="handleChange"
            >
                <slot>
                    <el-option
                        v-for="item in innerOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="getCompatibleOptionValue(item)"
                        :disabled="item.disabled"
                    >
                        <slot
                            name="option"
                            :item="item"
                        >
                            <span class="name">{{ item.name }}</span><br>
                            <span
                                class="code"
                                v-if="showCode"
                            >{{ item.code || "" }}</span>
                        </slot>
                    </el-option>
                    <div
                        style="text-align: center;margin-bottom:8px;"
                        v-if="loading"
                    >
                        <jacp-text
                            type="disable"
                            size="12"
                        >
                            {{ $t('jacp.loadingText') }}
                        </jacp-text>
                    </div>
                </slot>
                <slot
                    name="empty"
                    slot="empty"
                />
                <slot
                    name="prefix"
                    slot="prefix"
                />
            </el-select>
            <form-button-group
                v-if="editMode"
                @confirm="handleConfirm"
                @cancel="handleCancel"
            />
        </div>
    </el-tooltip>
</template>
<script>
import isUndefined from 'lodash/isUndefined';
import isFunction from 'lodash/isFunction';
import uniqBy from 'lodash/uniqBy';
import omit from 'lodash/omit';
import i18n from '$platform.i18n';
import { findOptionExistsByGroup } from '@/plugins/utils';

const optionCache = new WeakMap();
const mixinLazySelect = {
    props: {
        lazy: { type: Boolean, default: false },
        lazyMethod: { type: Function },
        optionValueKey: { type: String, default: 'id' },
    },
    data() {
        return { lazyOptions: [], loading: false };
    },
    computed: {
        innerOptions() {
            // 兼容以前直接读取了option.code的地方
            const filterInvalidOption = o => !isUndefined(o[this.optionValueKey] || !isUndefined(o.code));
            if (!this.lazy || !Array.isArray(this.lazyOptions)) return this.options.filter(filterInvalidOption);
            // 这两个顺序不能变，以服务器返回的option优先。否则name就对不上了。
            // 这里就没法兼容带code的了？
            return uniqBy([
                ...this.lazyOptions,
                ...this.options,
            ], this.optionValueKey).filter(filterInvalidOption); // 过滤掉不合法的值
        },
    },
    methods: {
        async loadOptions() {
            if (!this.lazy) return;
            if (optionCache.has(this)) return;
            if (isFunction(this.lazyMethod)) {
                this.loading = true;
                this.lazyOptions = await Promise.resolve(this.lazyMethod());
                this.loading = false;
                optionCache.set(this, this.lazyOptions);
            }
        },
    },
    beforeDestroy() {
        optionCache.delete(this);
    },
};

/* 带有二次确认的select */
export default {
    name: 'JacpFormSelect',
    inheritAttrs: false,
    mixins: [mixinLazySelect],
    props: {
        value: [Number, String, Array],
        noDataText: {
            type: String,
            default: i18n.t('jacp.noData'),
        },
        options: Array,
        confirm: {
            type: Boolean,
            default: true,
        },
        disabled: Boolean,
        placeholder: { type: String, default: i18n.t('jacp.selectPlaceHolder') },
        showCode: { type: Boolean, default: false },
        disabledText: String,
        tooltipPlacement: { type: String, default: 'top' },
        optionValueKey: { type: String, default: 'id' },
        simple: { type: Boolean, default: false },
        autoWidth: { type: Boolean, default: false },
        minInputWidth: {
            type: [Number, String],
            default: 60,
        },
        maxInputWidth: {
            type: [Number, String],
            default: 100,
        },
    },
    directives: {
        autoWidth: {
            bind(el, binding, vnode) {
                if (!binding.value) return;
                const { minInputWidth } = vnode.context;
                const input = el.querySelector('input');
                input.style.width = `${minInputWidth + 8}px`; // 来自elementUI input.
            },
            update(el, binding, vnode) {
                if (!binding.value) return;
                const {
                    // currentItem,
                    currentValue,
                    currentValueLabel,
                    minInputWidth,
                    maxInputWidth,
                } = vnode.context;
                if (!isUndefined(currentValue)) return;
                function resize() {
                    const SingleWidth = 15;
                    const input = el.querySelector('input');
                    const inputWidth = currentValueLabel
                        ? Math.min((currentValueLabel.length + 1) * SingleWidth, maxInputWidth)
                        : minInputWidth;
                    input.style.width = `${inputWidth + 8}px`; // 一个magicNumber，距离右侧8px
                }
                resize();
            },
        },
    },
    data() {
        const inheritListeners = omit(this.$listeners, ['input', 'change']);
        return {
            currentValue: this.value,
            editMode: false,
            inheritListeners,
        };
    },
    computed: {
        hasGroup() {
            return !!this.innerOptions.find(option => Array.isArray(option.options));
        },
        currentItem() {
            return this.innerOptions.find((o) => {
                const value = this.getCompatibleOptionValue(o);
                return value === this.currentValue;
            });
        },
        currentValueLabel() {
            const current = this.currentItem;
            if (!current || !current.name) return '';
            return current.name;
        },
        size() {
            if (this.$attrs.size) return this.$attrs.size;
            if (this.simple) return 'mini';
            return (this.$ELEMENT || {}).size;
        },
    },
    methods: {
        handleChange(val) {
            this.currentValue = val;
            if (this.confirm) {
                this.editMode = this.currentValue !== this.value;
            } else {
                this.handleConfirm();
            }
        },
        handleConfirm() {
            // 如果是分组的option，下面的currentItem代码是不工作的
            const isCurrent = o => (o.id === 'undefined'
                ? o.code : o.id) === this.currentValue;
            const currentItem = this.hasGroup
                ? findOptionExistsByGroup(this.innerOptions, isCurrent)
                : this.innerOptions.find(isCurrent);

            this.$emit('input', this.currentValue);
            this.$emit('change', this.currentValue, currentItem);
            this.editMode = false;
        },
        handleCancel() {
            this.currentValue = this.value;
            this.editMode = false;
        },
        handleVisibleChange($event) {
            if ($event) {
                this.loadOptions();
            }
            this.$emit('visible-change', $event);
        },
        getCompatibleOptionValue(option = {}) {
            const value = !isUndefined(option[this.optionValueKey]) ? option[this.optionValueKey] : option.code;
            return value;
        },
    },
    watch: {
        value: {
            immediate: true,
            handler(val) {
                this.currentValue = val;
            },
        },
    },
};
</script>
<style lang="less">
@height: 32px;
.jacp-form-select .el-select{
    width: 100%;
    & .el-input__inner{
        line-height: @height;
        height: @height;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .el-select-dropdown__item {
        height: auto;
    }
    .extra {
        font-size: var(--font-size--description);
        color: var(--color--error);
        line-height: 20px;
    }
}
.jacp-simple-select{
    .el-input__inner{
        padding: 0;
        border: none;
    }
}
</style>
