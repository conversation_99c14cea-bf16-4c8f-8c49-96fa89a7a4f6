<template>
    <div
        class="form-button-group"
        :id="id"
    >
        <el-button
            type="primary"
            size="small"
            :disabled="disabledButtons.includes('confirm')"
            @click="$emit('confirm')"
        >
            确认
        </el-button>
        <el-button
            size="small"
            :disabled="disabledButtons.includes('cancel')"
            @click="$emit('cancel')"
        >
            取消
        </el-button>
    </div>
</template>
<script>
import { uuid } from '@/plugins/utils';
import { event } from '@/plugins/event';

export default {
    name: 'FormButtonGroup',
    props: {
        disabledButtons: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            id: uuid(this.$options.name),
        };
    },
    created() {
        event.$emit('double-check:start', this.id);
    },
    destroyed() {
        event.$emit('double-check:end', this.id);
    },
};
</script>

<style lang="less">
.form-button-group{
    text-align: right;
    margin-top: 12px;
    .el-button--small,
    .el-button--small.is-round{
        padding: 7px 17px;
    }
}
</style>
