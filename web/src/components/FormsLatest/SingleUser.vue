<template>
    <form-users-group
        v-model="innerValue"
        :max-count="1"
        v-bind="[$attrs]"
        v-on="{
            ...$listeners,
            input: () => $emit('input', innerValue[0])
        }"
    >
        <template slot="reference">
            <slot
                name="reference"
            />
        </template>
    </form-users-group>
</template>
<script>
export default {
    name: 'SingleUser',
    inheritAttrs: false,
    props: {
        // eslint-disable-next-line vue/require-default-prop
        value: { type: Object },
    },
    data() {
        return { innerValue: [] };
    },
    watch: {
        value: {
            immediate: true,
            handler(val) {
                if (val && val.erp) {
                    this.innerValue = [val];
                } else {
                    this.innerValue = [];
                }
            },
        },
        innerValue() {
            const [value] = this.innerValue;
            if (value === this.value) {
                return;
            }
            this.$emit('input', value);
        },
    },
};
</script>
