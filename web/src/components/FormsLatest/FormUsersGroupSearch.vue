<template>
    <jacp-autocomplete
        v-model="text"
        class="form-users-search"
        popper-class="form-users-search__popper"
        clearable
        :suggestions-label="suggestionsLabel"
        :fetch-suggestions="querySearch"
        :placeholder="placeholder"
        :trigger-on-focus="true"
        @select="handleSelect"
    >
        <i
            class="el-icon-search el-input__icon"
            slot="prefix"
        />
        <template slot-scope="{ item }">
            <div class="form-users-search__item">
                <jacp-user-avatar
                    :data="item"
                    :first-name="!item.headImage"
                    avatar
                />
                <div class="form-users-search__info">
                    <span class="form-users-search__erp">{{ item.name }}({{ item.erp }})</span>
                    <div
                        v-if="!noOrg"
                        class="form-users-search__org j-text-overflow"
                    >
                        <el-tooltip
                            :content="item.orgTierName"
                            placement="left"
                        >
                            <span>{{ item.orgTierName|jacp-user-orgname }}</span>
                        </el-tooltip>
                    </div>
                </div>
            </div>
        </template>
    </jacp-autocomplete>
</template>

<script>
import UserModel from '@/models/user';

export default {
    name: 'FormUsersGroupSearch',
    props: {
        noOrg: Boolean,
        placeholder: {
            default: '添加',
            type: String,
        },
        ignoreErps: {
            default: () => [],
            type: Array,
        },
        initialSuggestions: {
            type: Array,
            default: () => ([]),
        },
        initialSuggestionsLabel: String,
        // 自定义数据加载函数
        loadData: {
            type: Function,
        },
        showSuggestions: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            text: '',
            suggestionsLabel: '',
        };
    },
    methods: {
        handleSelect(user) {
            this.text = '';
            this.$emit('add', user);
        },
        querySearch(str, cb = () => {}) {
            const ignoreErpsFilter = user => !this.ignoreErps.includes(user.erp.toLowerCase());
            const queryFn = this.loadData || UserModel.search;
            if (!str && this.showSuggestions) {
                const suggestions = this.initialSuggestions.filter(ignoreErpsFilter);
                this.suggestionsLabel = suggestions.length ? this.initialSuggestionsLabel : '';
                return cb(suggestions);
            }
            this.suggestionsLabel = '';
            return queryFn(str).then(users => users.filter(ignoreErpsFilter)).then(cb);
        },
        focus() {
            const inputEl = this.$el.querySelector('.el-input input');
            if (inputEl) {
                this.$nextTick(() => {
                    inputEl.focus();
                });
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.form-users-search{
    max-height: 376px;
    overflow: auto;
    .jacp-autocomplete-input__wrapper{
        position: sticky;
        top: 0;
    }
    &.el-autocomplete{
        width: 260px;
    }
    &__popper{
        width: 260px;
    }
    &__item{
        display: flex;
        list-style: none;
        width: 100%;
    }
    &__info{
        display: flex;
        flex-direction: column;
        margin-left: 8px;
        width: calc(100% - 40px);
        // width: 100%;
    }
    &__erp{
        font-size: 14px;
        color: @fontColor;
    }
    &__org{
        font-size: 12px;
        color: @fontThirdColor;
        display: inline-block;
    }
    .jacp-user-avatar{
        // min-width: 32px;
    }
}
</style>
