<template>
    <div
        ref="wrapperEl"
        class="jacp-forms-wrapper--latest"
        :aria-owns="id"
        :class="{
            'jacp-forms-wrapper__viewmode': !editMode,
            'jacp-forms-wrapper__disabled': disabled
        }"
        @mousedown.capture="focusBefore($event)"
        @mouseup.capture="focus($event)"
    >
        <div
            ref="contentEl"
            class="jacp-forms-wrapper__content"
            v-if="!editMode"
        >
            <el-tooltip
                :content="disabledText"
                placement="top"
                :disabled="!disabledText"
            >
                <slot name="view" />
            </el-tooltip>
        </div>
        <div
            v-else
            class="jacp-forms-wrapper__edit"
        >
            <slot name="edit" />
            <slot
                name="confirm"
                v-if="confirm"
            >
                <form-button-group
                    @confirm="$emit('confirm')"
                    @cancel="$emit('cancel')"
                />
            </slot>
        </div>
        <slot />
        <form-wrapper-toolbar
            v-if="toolbarVisible"
            ref="toolbarEl"
            :reference="toolbarRef"
            :visible-arrow="false"
            :class="[popperClass ? popperClass : 'jacp-forms-wrapper-toolbar']"
            :popper-options="popperOptions"
            :placement="placement"
            :id="id"
            :tools="toolbarTools"
            :clipboard-text="clipboardText"
            @on-tool-click="handleToolbarClick"
        />
    </div>
</template>
<script>
import { generateId } from 'element-ui/src/utils/util';
// TODO:
// eslint-disable-next-line
import Wrapper from '../Forms/Wrapper';
import FormWrapperToolbar from './FormWrapperToolbar';

export default {
// 与旧版的做区分，增加fresh
    name: 'JacpFormWrapperLatest',
    components: { FormWrapperToolbar },
    extends: Wrapper,
    props: {
        confirm: Boolean, // 需要确认按钮触发change的
        /* top/top-start/top-end/bottom/
        bottom-start/bottom-end/left/
        left-start/left-end/right/right-start/right-end */
        placement: {
            type: String,
            default: 'right-start', // 目前只支持放左边或者右边，别的位置没有样式
        },
        popperClass: String,
        popperOptions: Object,
        // availableTools: ['edit'],
        toolbar: Boolean,
        toolbarTools: {
            type: Array,
            default: () => ['edit'],
        },
        disabledText: [String],
        clipboardText: String,
    },
    data() {
        return {
            toolbarRef: null,
        };
    },
    mounted() {
        // 子组件的mounted优先于父组件的mounted，导致在toolbar mounted的时候拿不到refrence，需要父组建mounted的时候手动赋值， TODO: 有没有好一点的toolbar实现方式呢？
        this.toolbarRef = this.$el;
    },
    methods: {
        toggleToolbarVisible(val) {
            const { toolbarEl } = this.$refs;
            if (toolbarEl) {
                toolbarEl.showPopper = val;
            }
        },
        handleToolbarClick(tool) {
            switch (tool) {
            case 'edit':
                if (this.disabled || this.lock) {
                    return;
                }
                // 开始正式业务处理
                this.$emit('focus');
                break;
            default:
                this.$emit(tool);
                break;
            }
        },
    },
    computed: {
        toolbarVisible() {
            return this.toolbar && !this.editMode && !this.disabled;
        },
        id() {
            return `jacp-form-wrapper-${generateId()}`;
        },
    },
    watch: {
        toolbarVisible: {
            immediate: true,
            handler: 'toggleToolbarVisible',
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-forms-wrapper{
    /* 以下是复写样式 */
    &--latest&__viewmode{
        padding: 0;
        transform: translate(0, 0);
    }
    /* 以下是新增样式 */
    &__content{
        display: flex;
    }
}
</style>
