<template>
    <el-tooltip
        :content="disabledText"
        placement="top"
        :disabled="!disabledText"
    >
        <div class="form-upload">
            <form-upload-list
                :files="fileList"
                :disabled="disabled || fileList.length <= minCount"
                :toolbar-offset="toolbarOffset"
                :toolbar-placement="toolbarPlacement"
                @on-preview="fileDownloadOrPreview"
                @del="beforeRemove"
                @download="fileDownload"
            >
                <template
                    slot="label"
                    slot-scope="{file}"
                >
                    <a
                        href="#"
                        v-if="canReload && !file.readOnly"
                        @click.stop.prevent="handleReload(file)"
                    >重新上传</a>
                </template>
            </form-upload-list>
            <el-upload
                class="jacp-input-upload"
                :class="{
                    'jacp-input-upload-disabled': disabled,
                    'jacp-input-upload-showplaceholder': disabled && !value.length
                }"
                :with-credentials="withCredentials"
                :action="uploadUrl"
                :headers="getHeader"
                :on-error="handleError"
                :on-progress="handleProgress"
                :on-success="handleSuccess"
                :before-upload="fileCheck"
                :disabled="disabled"
                :show-file-list="false"
                :file-list="value"
                v-bind="$attrs"
            >
                <el-button
                    ref="triggerEl"
                    size="small"
                    type="text"
                    :disabled="disabled || (limit !== undefined && fileList.length >= limit)"
                >
                    {{ uploadText.button }}
                </el-button>
                <span
                    slot="tip"
                    class="el-upload__tip"
                    v-show="!disabled"
                > ({{ uploadText.tip }})</span>
            </el-upload>
            <jacp-thumbnail-view
                ref="previewEl"
                :attachments="value"
                :has-preview="hasPreview"
            />
        </div>
    </el-tooltip>
</template>

<script>
import { ByteTransform } from '@/filters';
import { getFileTypeIconName } from '@/plugins/utils.filetype';
import { downloadFile } from '@/plugins/utils';
import FormValidate from '@/mixins/mixin.form.validate';
import FormUploadList from './FormUploadList';
// import { childHost } from '../../plugins/http';

function getUrlParams(obj) {
    return Object.keys(obj).map(p => `${p}=${obj[p]}`).join('&');
}
function isImage(name) {
    return getFileTypeIconName(name) === 'image';
}

export default {
    name: 'JacpFormUpload',
    inheritAttrs: false,
    components: { FormUploadList },
    mixins: [FormValidate],
    props: {
        limit: {
            type: Number,
            default: undefined,
        },
        withCredentials: {
            type: Boolean,
            default: true,
        },
        value: {
            type: Array,
            default: () => [],
        },
        uploadUrl: {
            type: String,
            default: '',
            // default: `${childHost}/space-demand/api/v1/files/upload`,
        },
        // 用来禁止必填项被删除。。。
        minCount: {
            type: Number,
            default: 0,
        },
        maxSize: {
            type: Number,
            default: 20 * 1024 * 1024,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        onRemove: {
            type: Function,
        },
        hasPreview: Boolean,
        // 是否支持重新上传
        canReload: {
            type: Boolean,
            default: true,
        },

        /**
         * @params Object reloadParams = {
                attachmentType: 'demand',
                entityId: demandid / cardid,
                id: fileid
            }
        */
        reloadParams: Object,
        disabledText: String,
        fileDownload: {
            type: Function,
            default: (file) => {
                let { url } = file;
                try {
                    url = new URL(file.url).pathname;
                } catch (err) {
                    // url =/s file.url;
                }
                downloadFile(Object.assign({}, file, { url }));
            },
        },
        toolbarPlacement: String,
        toolbarOffset: Number,
    },
    data() {
        return {
            fileList: [],
            // uploadUrl: '/space-demand/api/v1/files/upload',
            reloadFile: null,
        };
    },
    created() {
    },
    methods: {
        handleError(res = {}) {
            let msg = '';
            try {
                msg = JSON.parse(res.message).message;
            } catch (err) {
                msg = res.message;
            }
            this.showError(msg || '上传失败！');
            // 移除出错的文件
            // fileList.splice(fileList.indexOf(file), 1);
        },
        handleProgress(ev, file, uploadFiles) {
            this.fileList = uploadFiles;
        },
        handleSuccess(res, file, fileList) {
            if (res.code === 200) {
                const fileInfo = {
                    url: res.data.uri,
                    name: res.data.fileName,
                    id: res.data.id,
                    readOnly: false,
                };
                if (this.reloadFile) {
                    // this.reloadFile = null;
                    const target = this.fileList.find(o => +o.id === +fileInfo.id);
                    if (target) {
                        Object.assign(target, fileInfo);
                    }
                    // 清理现场，去掉中间过程产生的文件
                    this.fileList.splice(this.fileList.indexOf(file), 1);
                } else {
                    this.$emit('on-uploaded', Object.assign(file, fileInfo));
                    // this.fileList.push(fileInfo);
                }
                this.$emit('input', this.fileList);
                this.$_triggerValidateChangeEvent();
            } else {
                this.handleError(res, file, fileList);
                // this.showError(res.message || '文件格式错误！');
                // 移除出错的文件
                // fileList.splice(fileList.indexOf(file), 1);
            }
            if (this.reloadFile) {
                this.reloadFile = null;
            }
        },
        handleReload(file) {
            if (!this.canReload) {
                return;
            }
            this.reloadFile = file;
            if (this.$refs.triggerEl) {
                this.$refs.triggerEl.$el.click();
            }
        },
        fileCheck(file) {
            const fileType = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt',
                'pptx', 'txt', 'log', 'xml', 'vsd', 'vsdx', 'md', 'mpp', 'msg',
                'xmind', 'graffle', 'msg', 'olm', 'eml', 'zip', 'rar', 'gz',
                'csv', '7z', 'rp', 'mov', 'flv', 'avi', 'wvm', 'mp4', 'mpeg',
                'svg', 'png', 'jpg', 'jpeg', 'gif', 'bmp'];
            const type = file.name.split('.').pop();
            if (!fileType.includes(type)) {
                this.$message.error(`文件上传不支持${type}类型`);
                return false;
            }
            if (file.size > this.maxSize) {
                this.showError(this.uploadText.maxSizeError);
                return false;
            }
            return true;
        },
        // 直接下载的时候，需要处理一下url
        async fileDownloadOrPreview(file) {
            const isImageFile = isImage(file.name);
            if (isImageFile) {
                // file.url = await imageToBolb(file.url);
                this.$refs.previewEl.openPicViewer(file);
                return;
            }
            downloadFile(file);
        },
        beforeRemove(file) {
            if (file.readOnly) {
                return;
            }
            this.$confirm(`确定移除附件「${file.name}」？`).then(() => {
                const doRemoved = () => {
                    const index = this.fileList.indexOf(file);
                    this.fileList.splice(index, 1);
                    this.$emit('input', this.fileList);
                };
                this.$emit('on-delete', file);
                doRemoved();
                this.$_triggerValidateChangeEvent();
                // this.fileRemoved(file);
            });
        },
        showError(msg) {
            this.$alert(msg, this.uploadText.errorTitle, {
                type: 'error',
                closeOnClickModal: true,
                customClass: 'j--message-box--topstatus',
            });
        },
    },
    computed: {
        uploadText() {
            const size = ByteTransform.handle(this.maxSize, 'B');
            return {
                button: this.$t('jacp.upload.button'),
                tip: this.$t('jacp.upload.tip', { size }),
                errorTitle: this.$t('jacp.upload.errorTitle'),
                maxSizeError: this.$t('jacp.upload.maxSizeError', { size }),
            };
        },
        getHeader() {
            return { Authorization: localStorage.getItem('Authorization') };
        },
    },
    watch: {
        value: {
            immediate: true,
            // deep: true,
            handler(newVal, oldVal) {
                if (newVal === oldVal) {
                    return;
                }
                this.fileList = newVal.slice();
            },
        },
        reloadFile: {
            handler(val) {
                if (this.canReload) {
                    const { uploadUrl } = this;
                    this.uploadUrl = val ? `${uploadUrl}?attachmentId=${val.id}` : uploadUrl.substr(0, uploadUrl.indexOf('?'));
                    if (this.reloadParams && val) {
                        this.uploadUrl += `&${getUrlParams(this.reloadParams)}`;
                    }
                }
            },
        },
    },
};
</script>
