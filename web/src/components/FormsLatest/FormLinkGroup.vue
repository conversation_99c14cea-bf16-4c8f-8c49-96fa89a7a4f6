<template>
    <el-tooltip
        :content="disabledText"
        placement="top"
        :disabled="!disabledText"
    >
        <div class="form-link-group">
            <ul v-if="links.length > 0">
                <li
                    v-for="item in links"
                    :key="item.id"
                    :class="{
                        'disabled': disabled,
                    }"
                >
                    <form-wrapper-toolbar
                        :disabled="disabled"
                        :tools="['del']"
                        :offset="6"
                        @on-tool-click="deleteLink(item)"
                        placement="left-start"
                    >
                        <div
                            class="form-link-group__item"
                            slot="reference"
                        >
                            <img
                                class="form-link-group__prefix"
                                src="@/assets/images/filetype/<EMAIL>"
                                alt="链接"
                            >
                            <a
                                :href="item.path"
                                target="_blank"
                                :title="item.path"
                            >{{ item.attachmentName }}</a>
                        </div>
                    </form-wrapper-toolbar>
                </li>
            </ul>
            <span
                v-if="!links.length && disabled"
                class="jacp-form--nodata"
            >{{ noDataText }}</span>
            <slot name="action">
                <el-button
                    v-if="!disabled"
                    v-show="!editMode"
                    @click="editMode = true"
                    class="form-link-group__add"
                    type="text"
                >
                    +添加
                </el-button>
            </slot>

            <el-form
                v-if="editMode"
                class="form-link-group__form"
                ref="editingItemEl"
                :model="editingItem"
                :inline="true"
                :rules="rules"
            >
                <el-form-item prop="attachmentName">
                    <el-input
                        ref="firstInputEl"
                        v-model="editingItem.attachmentName"
                        placeholder="请填写链接名称"
                    />
                </el-form-item>
                <el-form-item
                    prop="path"
                    style="margin-top: 8px"
                >
                    <el-input
                        v-model="editingItem.path"
                        :placeholder="`例如：${origin}`"
                    />
                </el-form-item>
            </el-form>
            <form-button-group
                v-if="editMode"
                @confirm="handleConfirm"
                @cancel="handleCancel"
            />
        </div>
    </el-tooltip>
</template>
<script>
import isFunction from 'lodash/isFunction';
import { formRequiredValidator as validator } from '@/plugins/utils';
import Form from '@/mixins/mixin.form';
import FormWrapperToolbar from './FormWrapperToolbar';

const { origin } = window.location;
export default {
    name: 'JacpFormLinkGroup',
    components: { FormWrapperToolbar },
    mixins: [Form],
    props: {
        rules: {
            type: Object,
            default: () => ({
                attachmentName: [{
                    required: true,
                    message: '链接名称不能为空',
                    validator,
                    trigger: 'change',
                },
                { max: 200, message: '最大字符为200', trigger: 'change' },
                ],
                path: [{

                    required: true,
                    message: '链接地址不能为空',
                    validator,
                    trigger: 'change',
                }, {
                    type: 'url',
                    message: '请输入合法的链接地址',
                }],
            }),
        },
        disabledText: String,
        onDelete: { type: Function },
    },
    data() {
        return {
            editMode: false,
            editingItem: {},
            links: [],
            origin,
        };
    },
    methods: {
        handleConfirm() {
            const { editingItem, links } = this;
            this.$refs.editingItemEl.validate((valid) => {
                if (valid) {
                    const newItem = Object.assign({}, editingItem);
                    links.push(newItem);
                    this.originValueForReset = links;
                    this.$emit('input', links);
                    this.$emit('on-add', newItem);
                    this.editingItem = {};
                    this.editMode = false;

                    /* this.$nextTick(() => {
                        this.focus();
                    }); */
                }
            });
        },
        deleteLink(item) {
            const { links } = this;
            const index = links.indexOf(item);
            const doRemove = () => {
                links.splice(index, 1);
                this.$emit('input', links);
                this.$emit('on-delete', item);
            };
            if (isFunction(this.onDelete)) {
                Promise.resolve(this.onDelete(item)).then(() => {
                    doRemove();
                });
            } else {
                doRemove();
            }
        },
        focus() {
            const { firstInputEl } = this.$refs;
            if (firstInputEl) {
                firstInputEl.$el.querySelector('input').focus();
            }
        },
    },
    watch: {
        editMode: {
            handler(val) {
                if (val) {
                    this.$nextTick(() => {
                        this.focus();
                    });
                } else {
                    this.editingItem = {};
                }
            },
        },
        value: {
            handler(val) {
                if (val !== this.links) {
                    this.links = val;
                }
            },
            immediate: true,
        },
    },
};
</script>
<style lang="less">
.form-link-group{
    & li{
        list-style: none;
        position: relative;
        // margin: 4px 0;
        padding: 4px 0;
        &:hover {
            background-color: #E9F4FD;
        }

    }
    &__item{
        // line-height: 18px;
        vertical-align: middle;
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 16px;
        &>a{
            word-break: break-word;
        }
    }
    &__prefix{
        width: 16px;
        margin-right: 8px;
    }
    &__add{
        // font-size: 12px;
        margin-top: 0px;
    }
    & .el-input__inner{
        line-height: 32px;
        height: 32px;
    }
    & .el-form-item{
        transition: all .3s;
        margin-bottom: 8px;

        &.is-error{
            margin-bottom: 22px;
        }
    }
    & .el-form-item,
    & .el-form-item__content{
        width: 100%;
    }
}
</style>
