<template>
    <div
        class="jacp-form__value"
        :class="{
            'jacp-form--nodata': !value || !value.length,
            'form-users__wrapper': true
        }"
    >
        <el-popover
            ref="searchPopperEl"
            v-if="addBtnVisible"
            :disabled="disabled"
            :placement="placement"
            trigger="click"
            @show="focus"
        >
            <form-users-group-search
                ref="searchInputEl"
                v-clickoutside="hidePopper"
                :ignore-erps="value.map(user => user &&user.erp && user.erp.toLowerCase())"
                placeholder="搜索工号或名称"
                @add="handleAdd"
                :initial-suggestions="initialSuggestions"
                :initial-suggestions-label="initialSuggestionsLable"
                :load-data="loadData"
                :show-suggestions="showSuggestions"
                style="margin-right: 4px"
            />
            <div
                slot="reference"
                class="form-users__item form-users__item--add"
                :class="{ 'form-users__item--add_disabled': disabled }"
                role="button"
                ref="tianjiadom"
                v-if="addReference"
            >
                <slot name="reference">
                    <i class="el-icon-plus" />
                    <span>添加</span>
                </slot>
            </div>
        </el-popover>
        <jacp-erp
            v-for="user in currentValue"
            v-bind="$attrs"
            :class="['form-users__item']"
            :key="user.erp"
            :avatar="avatar"
            :avatar-size="size"
            :disable-timline="disableUser(user)"
            :display-name="displayName"
            :data="user"
            :clearable="!disabled && currentValue.length > minCount"
            @clear="handleDelete(user)"
            :style="`margin-left: ${marginLeft}px`"
        />
        <span
            v-if="visibleValue && visibleValue.length > 0"
            class="form-users-more"
        >
            <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="form-users-more-popover"
            >
                <jacp-erp
                    v-for="user in visibleValue"
                    v-bind="$attrs"
                    class="form-users-more-popover-erp"
                    :class="['form-users__item']"
                    :key="user.erp"
                    :avatar="avatar"
                    :avatar-size="size"
                    :disable-timline="disableUser(user)"
                    :display-name="false"
                    :data="user"
                    :clearable="!disabled && visibleValue.length > minCount"
                    @clear="handleDelete(user)"
                    :style="`margin-left: ${marginLeft}px`"
                    :more="true"
                />
                <span
                    slot="reference"
                    class="form-users-more-button"
                >+{{ value.length - maxVisibleItemLength }}</span>
            </el-popover>
        </span>
    </div>
</template>
<script>
import Emitter from 'element-ui/src/mixins/emitter';
import Clickoutside from 'element-ui/src/utils/clickoutside';
import Form from '@/mixins/mixin.form';
import FormValidate from '@/mixins/mixin.form.validate';
import FrequentlyErp from '@/mixins/mixin.frequentlyErp';

export default {
    name: 'FormUsersGroup',
    mixins: [Form, FormValidate, FrequentlyErp, Emitter],
    directives: { Clickoutside },
    props: {
        noTimlineUsers: {
            type: Array,
            default: () => [],
        },
        disabled: Boolean,
        avatar: {
            type: Boolean,
            default: true,
        },
        maxCount: {
            default: Infinity,
            type: Number,
        },
        minCount: {
            default: 0,
            type: Number,
        },
        // 超过这个数字就收起多余的
        maxVisibleItemLength: {
            default: 5,
            type: Number,
        },
        // 是否展示全部
        expandAll: {
            type: Boolean,
            default: false,
        },
        // 自定义数据加载函数
        loadData: {
            type: Function,
        },
        showSuggestions: {
            type: Boolean,
            default: true,
        },
        size: { default: 32, type: Number },
        displayName: {
            type: Boolean,
            default: true,
        },
        addReference: {
            type: Boolean,
            default: true,
        },
        marginLeft: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            initialSuggestions: [],
            initialSuggestionsLable: '常用联系人',
            placement: 'bottom',
        };
    },
    computed: {
        innerValue() {
            return this.value || [];
        },
        addBtnVisible() {
            return this.value.length < this.maxCount;
        },
        currentValue() {
            if (this.expandAll) {
                return this.value;
            }
            if (this.value.length > this.maxVisibleItemLength) {
                return this.value
                    .slice(0, this.maxVisibleItemLength)
                    .map(o => Object.assign({}, o));
            }
            return this.value;
        },
        visibleValue() {
            if (this.expandAll || this.value.length <= this.maxVisibleItemLength) {
                return [];
            }
            return this.value
                .slice(this.maxVisibleItemLength)
                .map(o => Object.assign({}, o));
        },
    },
    methods: {
        handleAdd(user) {
            this.value.push(user);
            this.$emit('input', this.value);
            this.$emit('on-add', user);
            this.hidePopper();
            if (this.storageKey) {
                this.$_updateFrequentlyStorageValue(user);
            }
            this.$_triggerValidateChangeEvent();
        },
        handleDelete(user) {
            const index = this.value.findIndex(i => i.erp === user.erp);
            this.value.splice(index, 1);
            this.$emit('input', this.value);
            this.$emit('on-delete', user);
            this.$_triggerValidateChangeEvent();
        },
        disableUser(user = {}) {
            const filteredErps = this.noTimlineUsers.map(item => item.erp.toLowerCase());
            return filteredErps.includes(user?.erp?.toLowerCase());
        },
        hidePopper() {
            this.$refs.searchPopperEl.showPopper = false;
            this.$refs.searchInputEl.text = '';
        },
        focus() {
            if (!this.$refs.searchInputEl) {
                return;
            }
            this.$refs.searchInputEl.focus();
            if (!this.storageKey) {
                return;
            }
            this.$_getFrequentlyStoragePureList().then((data) => {
                this.initialSuggestions = data;
            });
        },
    },
    mounted() {
        this.$nextTick(() => {
            if (this.$refs.tianjiadom && this.$refs.tianjiadom.getBoundingClientRect) {
                const { bottom } = this.$refs.tianjiadom.getBoundingClientRect();
                const h = window.outerHeight - bottom;
                if (h < 420) {
                    console.warn('========11==================================11========', h);
                    this.placement = 'top';
                }
            }
        });
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.form-users{
    &__wrapper{
        display: flex;
    }

    &__item{
        // margin: 6px 7px;
        &--add{
            display: flex;
            flex-direction: column;
            align-content: center;
            align-items: center;
            text-align: center;
            color: @primaryColor;
            cursor: pointer;
            i{
                font-size: 20px;
                display: inline-block;
                width: 32px;
                height: 32px;
                border: 1px dotted @primaryColor;
                line-height: 32px;
                border-radius: 50%;
            }
            span{
                margin-top: 5px;
                font-size: 12px;
            }

            &_disabled{
                cursor: not-allowed;
                color:#c0c4cc;
                i{
                    border: 1px dotted #c0c4cc;
                }
            }
        }
    }
}
.form-users-more {
    &-popover {
        display: flex;
        flex-wrap: wrap;
        max-width: 400px;
        border-radius: 8px;
        &-erp {
            margin: 8px;
        }
    }
    &-button {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(245, 246, 250, 1);
        text-align: center;
        line-height: 32px;
        font-size: 12px;
        font-family: PingFang SC;
        float: left;
        font-weight: normal;
        color: rgba(144, 147, 153, 1);
        margin-left: 5px;
        cursor: pointer;
        text-align: center;
    }
}
</style>
