<template>
    <jacp-form-wrapper-latest
        toolbar
        :confirm="confirm"
        :class="[mode, {
            'jacp-form-input--latest': true,
            'jacp-form-input--edit': editMode
        }]"
        :edit-mode="editMode"
        :disabled="disabled"
        :disabled-text="disabledText"
        :toolbar-tools="toolbarTools"
        :clipboard-text="value"
        @confirm="doValidate"
        @cancel="beforeCancel"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            :class="{
                'jacp-form-input__value': true,
                'jacp-form--nodata': !value
            }"
        >
            <span v-if="mode === 'underline'">{{ value || noDataText | normalized-string }}</span>
            <jacp-collapse-box
                :max-height="maxHeight"
                v-else
                :value="value"
                :no-data-text="noDataText"
            />
        </div>
        <div
            slot="edit"
            :class="{
                'jacp-form-input--error': errorMsg,
                'jacp-form-input__editor': true,
                'show-limit-count': showWordLimit,
            }"
        >
            <el-input
                ref="inputIns"
                v-bind="[$attrs]"
                :value="value"
                :type="type"
                :show-word-limit="showWordLimit"
                @input="input"
            />
            <div
                class="el-form-item__error"
                v-show="!useElFormValidator && errorMsg"
            >
                {{ errorMsg }}
            </div>
        </div>
    </jacp-form-wrapper-latest>
</template>
<script>
import { validate } from '@/plugins/utils';
import Form from '@/mixins/mixin.form';

export default {
    name: 'JacpFormInput',
    mixins: [Form],
    inheritAttrs: false,
    // extends: Input,
    props: {
        rules: [Array],
        type: [String],
        // rows: [Number],
        // autosize: [Boolean, Object],
        confirm: Boolean,
        // resize: String,
        mode: {
            type: String,
            validator: val => ['underline', 'background'].includes(val),
            default: 'underline',
        },
        showWordLimit: Boolean,
        maxHeight: [Number, String],
        disabledText: [String],
    },
    filters: {
        normalizedString(val) {
            if (!val) {
                return val;
            }
            let value = val;
            value = value.replace(/\r\n/g, ' ');
            value = value.replace(/\n/g, ' ');
            return value;
        },
    },
    data() {
        const isElFormItem = this.$parent.$options.componentName === 'ElFormItem';
        return {
            useElFormValidator: !this.rules && isElFormItem,
            errorMsg: undefined,
            editMode: false,
            originValue: this.value,
            lockWatch: false,
            toolbarTools: ['edit', 'copy'],
        };
    },
    methods: {
        triggerChange() {
            const { value } = this;
            const trimValue = value.trim();
            if (this.originValue !== trimValue.trim()) {
                this.$emit('change', trimValue.trim());
                this.originValue = trimValue;
                this.originValueForReset = trimValue;
            }
        },
        blur() {
            if (this.confirm) {
                return;
            }
            this.doValidate();
        },
        doValidate() {
            // 使用el表单校验
            if (this.useElFormValidator) {
                // 如果校验出错或未完成，不触发change事件
                if (['error', 'validating'].includes(this.$parent.validateState)) {
                    return;
                }
                // 如果校验成功
                this.editMode = false;
                this.triggerChange();
                return;
            }
            this.doSelfValidate();
        },
        doSelfValidate() {
            // 使用自带校验
            validate(this.value.trim(), this.rules).then(() => {
                this.errorMsg = undefined;
                this.editMode = false;
                this.triggerChange();
            }).catch((errorMsg) => {
                this.errorMsg = errorMsg[0];
            });
        },
        input(value) {
            if (!this.useElFormValidator && this.rules) {
                if (!this.getFilteredRule('change').length) {
                    return;
                }
                validate(value, this.rules).then(() => {
                    this.errorMsg = undefined;
                }).catch((errorMsg) => {
                    this.errorMsg = errorMsg[0];
                });
            }
            this.lockWatch = true;
            this.$emit('input', value);
        },
        focus() {
            this.editMode = true;
            this.originValueForReset = this.value;
            this.$nextTick(() => {
                const elementName = this.type === 'textarea' ? 'textarea' : 'input';
                this.$refs.inputIns.$el.querySelector(elementName).focus();
            });
        },
        beforeCancel() {
            if (this.useElFormValidator && this.$parent.clearValidate) {
                this.$parent.clearValidate();
            }
            this.handleCancel();
        },
    },
    watch: {
        value() {
            if (!this.lockWatch) {
                this.originValue = this.value;
            } else {
                this.lockWatch = false;
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.underline-error() {
    .underline-input--error();
}
.background-error() {
    .el-input__inner,
    .el-input__inner:focus,
    .el-textarea__inner,
    .el-textarea__inner:focus{
        border-color: @errorColor;
    }
}

.jacp-form-input--latest{
    /* 以下是复写样式 */
    .jacp-form-input{
        &__editor{
            position: relative;
        }
        &__editor,
        &__editor .el-input,
        &__editor .el-textarea,
        &__editor .el-input .el-input__inner,
        &__editor .el-textarea .el-textarea__inner{
            font-size: inherit;
            caret-color: @primaryColor;
        }

        &__editor.show-limit-count .el-input .el-input__inner,
        &__editor.show-limit-count .el-textarea .el-textarea__inner {
            padding-bottom: 32px;
        }
        &__editor .el-input__count{
            font-size: 12px;
            color: @fontThirdColor;
            position: absolute;
            bottom: 0;
            right: 16px;
            transform: translateY(-10px);
            background: #F5F5F5;
            line-height: 16px;
        }
        &__value:not(.jacp-form--nodata){
            line-height: 1.5;
            border: 1px solid transparent;
            color: @fontColor;
        }
    }
    /* 复写的end */
    /* 不使用el-form-item的校验，用自己的校验 */
    /* 两种形态：
    1. 下划线的
    2. 带背景颜色的
     */
    &.underline{
        &.jacp-form-input--edit{
            .underline-input();
        }
        & .jacp-form-input--error{
            .underline-error();
        }
    }
    &.background{
        .jacp-form-input__value{
            width: 100%;
            white-space: normal;
            font-size: 12px;
            color: @fontColor;
        }
        &.jacp-form-input--edit{
            .el-input__inner,
            .el-input__inner:focus,
            .el-textarea__inner,
            .el-textarea__inner:focus{
                border-color: transparent;
                padding: 0;
                border-color: @primaryColor;
                // color: inherit;
                line-height: 1.5;
                background-color: #F5F5F5;
                padding: 16px;
                font-size: 12px;
            }
        }
        & .jacp-form-input--error{
            .background-error();
        }
    }
    & .el-form-item__error{
        padding-top: 8px;
    }
}
/* 使用el-form-item的校验 */
.el-form-item.is-error{
    & .jacp-form-input--latest.underline{
        .underline-error();
    }
    & .jacp-form-input--latest.background{
        .background-error();
    }
}
</style>
