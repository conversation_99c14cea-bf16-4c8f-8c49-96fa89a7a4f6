<template>
    <transition name="progress-fade">
        <div
            class="xhr-progress"
            v-show="progress && progress < 1"
        >
            <div
                class="progress-bar"
                :style="{width: progress * 100 + '%'}"
            />
        </div>
    </transition>
</template>

<script>
export default {
    name: '<PERSON><PERSON><PERSON>xProgress',
    props: {
        progress: {
            type: Number,
            default: 0,
        },
    },
};
</script>

<style lang='less' scoped>
.xhr-progress{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    z-index: 10000;
    .progress-bar{
        background-color: var(--color--primary);
        height: 2px;
        box-shadow: 0 0 2px var(--color--primary);
        width: 0;
        transition: 0.1s;
    }
}
.progress-fade-enter-active{
    transition: opacity 0.1s;
}
.progress-fade-leave-active {
    transition: opacity 0.5s
}
.progress-fade-enter, .progress-fade-leave-active {
    opacity: 0
}
</style>
