<template>
    <div class="jacp-logo">
    <!--div class="jacp-logo__text">{{text}}</div-  树杰调整UI-->
    </div>
</template>

<script>
export default {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    props: {
        text: {
            default: '',
            type: String,
        },
    },
};
</script>

<style lang="less">
    .jacp-logo {
        display: inline-block;
        width: 100%;
        text-align: center;
        font-family: cursive;
        font-size: 22px;

        &__text{
            color: inherit;
        }
    }
</style>
