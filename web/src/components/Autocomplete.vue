<template>
    <!-- 新版的autocomplete不能使用popper来展示suggestion，elem的无法使用，需要自己写一个 -->
    <div
        class="jacp-autocomplete"
        :aria-expanded="suggestionVisible"
        :aria-owns="id"
    >
        <div class="jacp-autocomplete-input__wrapper">
            <el-input
                :clearable="clearable"
                ref="input"
                size="small"
                v-bind="[$props, $attrs]"
                @input="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
                @clear="handleClear"
            >
                <template
                    slot="prepend"
                    v-if="$slots.prepend"
                >
                    <slot name="prepend" />
                </template>
                <template
                    slot="append"
                    v-if="$slots.append"
                >
                    <slot name="append" />
                </template>
                <template
                    slot="prefix"
                    v-if="$slots.prefix"
                >
                    <slot name="prefix" />
                </template>
                <template
                    slot="suffix"
                    v-if="$slots.suffix"
                >
                    <slot name="suffix" />
                </template>
            </el-input>
        </div>
        <jacp-autocomplete-suggestions
            :title="suggestionsLabel"
            ref="suggestions"
            :class="[popperClass]"
        >
            <li
                v-for="(item, index) in suggestions"
                :key="index"
                @click="select(item)"
                :id="`${id}-item-${index}`"
                role="option"
            >
                <slot :item="item">
                    {{ item[valueKey] }}
                </slot>
            </li>
        </jacp-autocomplete-suggestions>
    </div>
</template>
<script>
import { Autocomplete } from 'element-ui';
// import Clickoutside from 'element-ui/src/utils/clickoutside';

export default {
    name: 'JacpAutocomplete',
    extends: Autocomplete,
    // directives: { Clickoutside },
    props: {
        suggestionsLabel: String,
        clearable: Boolean,
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-autocomplete-input__wrapper{
  padding: 4px 4px 16px 4px;
  background: #fff;
  & .el-input__validateIcon{
      display: none;
  }
}
.jacp-autocomplete-suggestions__list{
  // margin-top: 8px;
  li{
    list-style: none;
    margin: 8px 4px;
    cursor: pointer;
    &:first-child{
      margin-top: 0;
    }
    &:hover{
        background-color: @hoverBgColor;
    }
  }

}
</style>
