<template>
    <div
        class="jacp-plain-list"
        :style="{height: `${height}px`}"
    >
        <!-- <transition-group tag="ul"> -->
        <ul>
            <li
                v-for="item in data"
                :key="item.id"
                @click="$emit('on-item-click', item)"
                :class="['jacp-plain-list-item', {'hide': item.hide}]"
            >
                <slot :row="item" />
            </li>
        </ul>
        <!-- </transition-group> -->
        <div class="jacp-plain-list-more">
            <span
                v-if="hasMore"
                @click="$emit('more', currentPage)"
            >查看更多</span>
        </div>
        <jacp-empty v-if="isEmpty" />
    </div>
</template>
<script>
export default {
    name: 'PlainList',
    props: {
        data: {
            type: Array,
        },
        currentPage: [Number, String],
        total: Number,
        pageSize: {
            default: 20,
            type: Number,
        },
        height: [Number, String],
    },
    computed: {
        hasMore() {
            const p = 1 * this.currentPage;
            // eslint-disable-next-line no-self-compare
            const cp = ((typeof p === 'number') && p === p) ? p : 1;
            return this.total > cp * this.pageSize;
        },
        isEmpty() {
            return !this.data.length || !this.data.filter(item => !item.hide).length;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-plain{
    position: relative;
    .no-borderbg-select();
    .layout-card-content{
        margin-top: -9px;
    }
    .el-checkbox__input+.el-checkbox__label{
        float: left;
        margin-top: 14px;
        font-size: 12px;
        margin-right: 10px;
        font-weight: normal;
        padding-left: 0;
    }
    &-tabs{
        // max-width: calc(~"100% - 276px");
        // width: fit-content;
        // position: absolute;
        // top: 18px;
        // left: 112px;
        & .el-tabs__item{
          font-size: 14px !important;
          font-weight: normal;
          // padding: 0 8px;
          //color: @fontSecendColor;
          border: var(--border--hr);
          border-radius: var(--radius--large);
          padding: 0 var(--gutter--base);
          margin: 0 6px;
        }
        .el-tabs__item.is-active {
          background: var(--color--base--background);
        }
        & .el-tabs__nav-wrap::after{
          background-color: transparent;
        }
        & .el-tabs__active-bar{
          bottom: 10px;
        }
    }
    &__remainingHours{
        width: fit-content;
        // min-width: 80px;
        text-align: center;
        cursor: pointer;
        position: relative;
        &:hover > span, &:hover > i{
            color: @primaryColor;
        }
        & [class^=el-icon-]{
            visibility: hidden;
        }
        &__input{
            display: inline;
            position: absolute;
            left: -16px;
            .el-input__inner{
                height: 32px;
                width: calc(~"100% - 10px");
                min-width: 50px;
                padding: 0 0 0 5px;
            }
        }
        &:hover [class^=el-icon-],
        &.editing &__input,
        &__text{
            visibility: visible;
        }
        &.editing &__text,
        &__input{
            visibility: hidden;
        }
    }
}
.jacp-plain-list{
  min-width: 320px;
  margin-top: 8px;
  margin-bottom: 8px;
  position: relative;
  overflow: auto;
  &-more{
    text-align: center;
    color: @primaryColor;
    line-height: 48px;
    cursor: pointer;
    height: 48px;
  }
  &-item{
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;
    height: 48px;
    padding: 0 8px;
    color: @fontColor;
    position: relative;
    // transition: all 3s ease-in-out;
    overflow: hidden;
    cursor: default;
    transition: all .3s ease-in-out;
    &__link{
      &:hover{
        color: @primaryColor;
      }
      cursor: pointer;
    }
    &.hide{
      transform: translateX(100px);
      opacity: 0;
      height: 0;
    }
    &-colunm{
      display: flex;
      line-height: 48px;
      &.left{
        justify-content:flex-start;
        overflow: hidden;
      }
      &.right{
        justify-content:flex-end;
      }
      &.light{
        color: @remarkColor;
      }
    }
    & .no-shrink{
      flex-shrink: 0;
    }
    &:after{
      position: absolute;
      bottom: 0;
      display: block;
      left: 0;
      height: 1px;
      width: calc(~"100% - 16px");
      background-color: #F1F1F1;
      content: ' ';
      transform: translateX(8px);
      // border-bottom: 1px solid #F1F1F1;

    }
    &:hover{
      background-color: #e6f5fd;
    }
    &.v-leave {
      transform: translateX(0px);
    }
    &.v-leave-active {
      transition: all .3s;
    }
    &.v-leave-to {
      transform: translateX(100px);
      opacity: 0;
    }
    &.v-enter {
      transform: translateX(-100px);
      opacity: 0;
    }
    &.v-enter-active {
      transition: all .3s;
    }
    &.v-enter-to {
      transform: translateX(0px);
      opacity: 1;
    }
  }
}
</style>
