<template>
    <div
        class="time-machine"
        v-show="isShowPic"
    >
        <!-- 此段代码无用 -->
        <div class="time-machine-content">
            <i @click="close" />
        </div>
    </div>
</template>
<script>

export default {
    name: 'TimeMachine',
    props: {
        showPic: {
            default: false,
            type: <PERSON><PERSON><PERSON>,
        },
    },
    data() {
        return {
            isShowPic: false,
        };
    },
    mounted() {
        if (localStorage.jacp_tm_count) {
            localStorage.jacp_tm_count = parseInt(localStorage.jacp_tm_count, 10) + 1;
        } else {
            localStorage.jacp_tm_count = 1;
            this.isShowPic = true;
        }
    },
    methods: {
        close() {
            this.isShowPic = false;
            this.$emit('update:showPic', false);
        },
    },
    watch: {
        showPic(value) {
            this.isShowPic = value;
        },
    },
};
</script>

<style lang="less">
.time-machine{
    position: fixed;
    display: flex;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 9999;
    background-color: rgba(0, 0, 0, .6);
    &-content{
        margin: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}
</style>
