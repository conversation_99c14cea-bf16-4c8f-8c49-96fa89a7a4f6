<template>
    <div
        v-if="active && data.length"
        class="el-dropdown dropdown-sort"
    >
        <div
            class="dropdown-sort__header"
            @click="toggleMenu"
        >
            <span>{{ active.name }}</span>
            <jacp-icon :name="active.icon" />
            <jacp-icon
                name="el-icon-caret-bottom"
                :size="12"
            />
        </div>
        <transition name="el-zoom-in-top">
            <div
                class="dropdown-sort__menu el-dropdown-menu"
                v-show="showList"
            >
                <ul class="dropdown-sort__item-outer">
                    <li
                        class="jacp-dropdown__item dropdown-sort__item"
                        v-for="item in data"
                        :key="item.id"
                        v-show="active !== item"
                        @click="select(item)"
                    >
                        <span>{{ item.name }}</span>
                        <jacp-icon :name="item.icon" />
                    </li>
                </ul>
            </div>
        </transition>
    </div>
</template>

<script type="text/javascript">
import { event } from '@/plugins/event';

export default {
    name: 'JacpDropdownSort',
    props: {
        data: {
            type: Array,
            required: true,
        },
        value: {
            type: String,
        },
    },
    data() {
        let active = this.data.filter(item => item.id === this.value)[0] || this.data[0];
        if (!active) {
            active = this.data[0];
            this.$emit('input', active ? active.id : undefined);
        }
        return {
            active,
            showList: false,
        };
    },
    watch: {
        value(n) {
            if (n && this.active && this.active.id === n) {
                return;
            }
            this.active = this.data.filter(item => item.id === n)[0];
        },
    },
    methods: {
        toggleMenu() {
            this.showList = !this.showList;
        },
        select(obj) {
            this.showList = false;
            this.active = obj;
            this.$emit('input', obj ? obj.id : undefined);
            this.$emit('change', obj);
        },
        closePanel(e) {
            if (this.showList && !this.$el.contains(e.target)) {
                this.showList = false;
            }
        },
    },
    mounted() {
        event.$on('document.click.capture', this.closePanel);
    },
    beforeDestroy() {
        event.$off('document.click.capture', this.closePanel);
    },
};
</script>

<style lang="less">
.dropdown-sort__header{
    cursor: pointer;
    width: max-content;
}
.dropdown-sort{
    &__menu{
        margin-top: 30px;
    }
    &__item-outer{
        margin: 0;
        padding: 0;
    }
    &__item{
        display: flex;
        white-space: nowrap;
        list-style: none;
        line-height: 36px;
        padding: 0 10px;
        margin: 0;
        cursor: pointer;
        font-size: 12px;

        &:hover{
            background-color: #e5e9f2;
            color: #475669
        }
    }
}
</style>
