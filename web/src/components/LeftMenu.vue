<template>
    <div
        class="comp-leftmenu"
        :class="{
            'comp-leftmenu--horizontal': isHorizontal,
            'comp-leftmenu--collapse': isCollapse }"
    >
        <el-menu
            :class="{
                'comp-leftmenu__menu': true,
                'jacp-tabs-menu': isHorizontal
            }"
            v-bind="$attrs"
            :collapse="isCollapse && !isHorizontal"
            @select="select"
            :collapse-transition="false"
            :default-active="defaultActive"
        >
            <template v-for="menu in data">
                <el-menu-item
                    :index="menu.id"
                    v-if="!menu.children || !menu.children.length"
                    :key="menu.name"
                >
                    <jacp-icon
                        v-show="!isHorizontal"
                        :name="menu.icon"
                    />
                    <span slot="title">
                        <slot
                            name="title"
                            :menu="menu"
                        >
                            {{ menu.name }}
                        </slot></span>
                </el-menu-item>
                <el-submenu
                    :index="menu.id"
                    :key="menu.name"
                    v-else
                >
                    <template slot="title">
                        <jacp-icon
                            v-show="!isHorizontal"
                            :name="menu.icon"
                        />
                        <span slot="title">
                            <slot
                                name="title"
                                :menu="menu"
                            >
                                {{ menu.name }}
                            </slot></span>
                    </template>
                    <el-menu-item
                        v-for="child in menu.children"
                        :key="child.name"
                        :index="child.id"
                    >
                        {{ child.name }}
                    </el-menu-item>
                </el-submenu>
            </template>
        </el-menu>
        <div
            v-if="$attrs.mode !== 'horizontal'"
            class="comp-leftmenu__shrinkicon"
            :class="{'comp-leftmenu__shrinkicon--shrink': isCollapse}"
            @click="toggleMenu"
        >
            <jacp-icon
                :size="12"
                :name="isCollapse ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"
            />
        </div>
    </div>
</template>

<script type="text/javascript">
export default {
    inheritAttrs: false,
    name: 'JacpLeftMenu',
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        defaultActive: {
            type: [String, Number],
        },
        isCollapse: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        isHorizontal() { return this.$attrs.mode === 'horizontal'; },
    },
    methods: {
        toggleMenu() {
            this.$emit('update:isCollapse', !this.isCollapse);
        },
        select(index, indexPath) {
            this.$emit('select', index, indexPath);
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.comp-leftmenu{
    position: relative;
    width: 200px;
    background-color: #fff;
    &:not(.comp-leftmenu--horizontal){
        padding-top: 30px;
        // margin-right: 5px;
    }
    &--collapse:not(&--horizontal){
         width: 64px;
    }
    &--horizontal{
        width: -moz-fit-content;
        width: fit-content;
        & .el-menu-item{
            padding: 0 4px;
            margin: 0 12px;
        }
        & .el-menu--horizontal>.el-menu-item.is-active{
            // color: @primaryColor;
        }
        & .el-menu--horizontal>.el-menu-item{
            // color: #303133;
        }
    }
    & .horizontal-collapse-transition{
        transition: none; // 禁用动画
    }
    & .el-menu{
        background-color: inherit;
        border-right: 0px solid transparent;
        &--collapse{
            width: 64px;
        }
    }
    & .el-menu-item.is-active{
        color: #303133;
    }
    & .comp-leftmenu__menu {
        padding: 0 8px;
        & .el-submenu__title{
            color: #909399;
        }
    }
    & .el-menu-item,
    & .el-submenu__title{
        height: 40px;
        line-height: 40px;
        min-width: 160px;
    }
    & .el-submenu .el-menu{
        padding: 0 8px;
        background-color: #fff;
        box-sizing: border-box;
    }
    &:not(.comp-leftmenu--horizontal) .el-menu-item.is-active{
        background-color:@mainBgColor;
        border-radius: 12px;
    }
    &:not(.comp-leftmenu--horizontal) .el-menu-item{
        &:hover{
            background-color: @mainBgColor;
            border-radius: 12px;
        }
    }
    &__shrinkicon{
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        cursor: pointer;
        height: 30px;
        line-height: 30px;
        background-color: #fff;
        color: @primaryColor;
        text-align: center;
        &--shrink{
            width: 64px;
        }
    }

}
.comp-leftmenu--horizontal{
    .el-menu--horizontal>.el-menu-item{
        height: 40px;
        line-height: 40px;
    }
}
</style>
