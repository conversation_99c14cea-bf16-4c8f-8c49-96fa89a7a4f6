<template>
    <div
        v-if="visibleArrow"
        class="jacp-arrow"
        :class="direction"
    >
        <div
            class="jacp-arrow__inner"
            :style="styles"
        />
        <div
            class="jacp-arrow__outer"
            v-if="outline"
            :style="outlineStyles"
        />
    </div>
</template>
<script>

/* 临时用于富文本的显示和编辑，如需扩展，需要自理 */
export default {
    name: 'JacpArrow',
    props: {
        // 是否显示箭头
        visibleArrow: Boolean,
        // 箭头距离左边的位置
        arrowOffset: {
            type: [String],
            default: '50%',
        },
        size: {
            type: [Number, String],
            default: 6,
        },
        color: {
            type: String,
            default: '#F5F5F5',
        },
        direction: {
            default: 'up',
            validator(val) {
                return ['up', 'down'].includes(val);
            },
        },
        outline: Boolean,
        outlineColor: {
            type: String,
            default: '#ccc',
        },
    },
    data() {
        return {
            styles: {},
            outlineStyles: {
                transform: 'translateX(-1px) scale(1.1)',
            },
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.styles = this.getArrowStyles(this);
            if (this.outline) {
                const { size, outlineColor, arrowOffset } = this;
                Object.assign(this.outlineStyles, this.getArrowStyles({
                    zIndex: 0,
                    size: size + 1,
                    color: outlineColor,
                    arrowOffset,
                }));
            }
        },
        getArrowStyles({
            zIndex = 1,
            size,
            color,
            arrowOffset,
        }) {
            const { direction } = this;
            const styles = {
                left: arrowOffset,
                zIndex,
            };
            if (direction === 'up') {
                Object.assign(styles, {
                    borderColor: `transparent transparent ${color}`,
                    borderWidth: `0 ${size}px ${size}px`,
                    top: `${-size}px`,
                });
            } else {
                Object.assign(styles, {
                    borderColor: `${color} transparent transparent`,
                    borderWidth: `${size}px ${size}px 0`,
                    bottom: `${-size}px`,
                });
            }
            return styles;
        },
    },
    watch: {
        outlineColor: 'init',
    },
};
</script>

<style lang="less">
.arrow() {
    width: 0;
    height: 0;
    border-width: 6px;
    left: 50%;
    border-style: solid;
    border-top-width: 0;
    position: absolute;
    border-color: transparent;
}
.jacp-arrow{
    position: absolute;
    top: 0;
    left: 0;
    &.up{
        transform: translateY(2px);
    }
    &.down{
        transform: translateY(-2px);
    }
    &__inner,
    &__outer{
        .arrow();
    }
}
</style>
