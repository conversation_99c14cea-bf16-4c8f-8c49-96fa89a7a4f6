<template>
    <div class="jacp-tags-selector__root">
        <div
            class="jacp-tags-selector__input"
            @click.stop="focus"
        >
            <custom-tag
                :key="item.name"
                v-for="(item, $index) in value"
                class="jacp-tags-selector__tag"
                :color="item.color"
                closable
                @on-close="remove(item, $index)"
            >
                {{ item.name }}
            </custom-tag>
            <el-popover
                ref="popover"
                placement="bottom-start"
                popper-class="jacp-tags-selector__popper"
                v-model="suggestionShow"
            >
                <custom-tag
                    v-for="tag in avaliableSuggestions"
                    :key="tag.id"
                    class="jacp-tags-selector__item"
                    :color="tag.color"
                    @click.native.stop="toggleTag(tag);"
                >
                    {{ tag.name }}
                </custom-tag>
                <span
                    style="font-size: 12px;color: #999999"
                    v-if="suggestions.length && !avaliableSuggestions.length"
                >暂无可选</span>
                <span
                    style="font-size: 12px;color: #999999"
                    v-if="!suggestions.length"
                >请去空间设置维护标签</span>
                <span
                    slot="reference"
                    class="jacp-tags-selector__placeholder"
                >添加标签</span>
            </el-popover>
        </div>
    </div>
</template>

<script type="text/javascript">
/* eslint-disable no-param-reassign */
// 不支持输入，仅支持选择已有标签
export default {
    name: 'JacpInputTagsSelector',
    props: {
        placeholder: {
            default: 'New Tag',
            type: String,
        },
        value: {
            default: () => [],
            type: Array,
        },
        valueWidth: {
            default: Infinity,
            type: Number,
        },
        tagsMetadata: {
            default: () => [],
            type: Array,
        },
        maxCount: {
            default: Infinity,
            type: Number,
        },
        maxLength: {
            default: Infinity,
            type: Number,
        },
        filter: {
            type: Function,
            default: (str, cb) => {
                cb([]);
            },
        },
        itemRender: {
            type: Function,
        },
    },
    data() {
        return {
            text: '',
            errorMsg: '',
            suggestionShow: false,
            suggestions: [],
        };
    },
    computed: {
        avaliableSuggestions() {
            return this.suggestions.filter(sug => !sug.selected);
        },
    },
    methods: {
        load() {
            if (this.suggestions.length > 0) {
                return false;
            }
            this.tagsMetadata.forEach((_tag) => {
                let selected1 = false;
                this.value.forEach((val) => {
                    if (_tag.name === val.name) {
                        selected1 = true;
                    }
                });
                this.suggestions.push({
                    id: _tag.id,
                    name: _tag.name,
                    color: _tag.color === 'NONE' ? '#79CCFF' : _tag.color,
                    selected: selected1,
                });
            });
            if (this.value.length > 0) {
                this.suggestionShow = true;
            }
            return false;
        },
        toggleTag(_tag) {
            if (_tag.selected) {
                let index = 0;
                this.value.forEach((val, i) => {
                    if (_tag.name === val.name) {
                        index = i;
                    }
                });
                this.value.splice(index, 1);
            } else {
                this.value.push({
                    id: _tag.id,
                    name: _tag.name,
                    color: _tag.color,
                });
            }
            _tag.selected = !_tag.selected;
            this.$emit('input', this.value);
        },
        remove(item, index) {
            this.value.splice(index, 1);
            this.suggestions.forEach((val) => {
                if (item.name === val.name) {
                    val.selected = false;
                }
            });
            this.$emit('input', this.value);
        },
        focus() {
            this.load();
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.jacp-tags-selector__root{
  position: relative;
  font-size: 12px;
  border-radius: 2px;
  .el-form-item__content{
    line-height: 32px;
  }
  .el-tag .el-icon-close:hover, .el-tag .el-icon-close:active{
    background-color: transparent;
  }
  .jacp-tags-selector__input {
    border: 1px solid @formBorderColor;
    padding: 0 10px;
    min-height: 40px;
    line-height: 40px;
    min-width: 197px;
    max-width: 640px;
  }
  .jacp-tags-selector__placeholder{
    color: #DCDFE3;
    height: 24px;
    line-height: 24px;
    white-space: nowrap;
  }
}
.jacp-tags-selector__tag,
.jacp-tags-selector__item{
  /*padding: 0 10px;*/
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  color: white;
  border: none;
  border-radius: 2px;
  white-space: nowrap;
  margin-right: 10px;
}
.jacp-tags-selector__popper{
  max-width: 480px;

  .jacp-tags-selector__content{
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    width: 100%;
    padding: 12px 12px 2px 12px;
  }

  .jacp-tags-selector__item{
    padding-left: 10px;
    padding-right: 10px;
    cursor: pointer;
    width: fit-content;
    margin-bottom: 10px;
    list-style: none;
  }
}
</style>
