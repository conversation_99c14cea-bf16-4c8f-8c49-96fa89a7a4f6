import isPlainObject from 'lodash/isPlainObject';
import isFunction from 'lodash/isFunction';

import './quill'; // 里面有样式要引入一下
import '@/modules/_components/asyncComponents';

const useInstallFnComponents = [];
function getComponents() {
    const components = [];
    const combine = contextFn => (item) => {
        const comp = contextFn(item).default;
        if (comp.name) {
            components.push(comp);
        }
    };
    // modules目录里面的也注册一下
    const modulesFiles = require.context('../modules/_components', false, /\.vue$/);
    modulesFiles.keys().forEach(combine(modulesFiles));
    const files = require.context('./', false, /\.vue$/);
    files.keys().forEach(combine(files));
    return components;
}
// 加载子目录里的components
function getSubdirectories() {
    let compGroups = [];
    const files = require.context('./', true, /\.\/[^/]+\/index\.js$/);
    files.keys().forEach((item) => {
        const comps = files(item).default;
        if (Array.isArray(comps)) {
            compGroups = [...compGroups, ...comps];
        } else if (isPlainObject(comps) && typeof comps.name === 'string') {
            compGroups.push(comps);
        } else if (isPlainObject(comps) && isFunction(comps.install)) {
            // 用自定义的install
            useInstallFnComponents.push(comps);
        }
    });
    return compGroups;
}

function install(Vue) {
    [
        ...getComponents(),
        ...getSubdirectories(),
    ].forEach((item) => {
        // console.log('arch-ui', item.name);
        Vue.component(item.name, item);
    });
    useInstallFnComponents.forEach(comp => Vue.use(comp));
}

export default {
    install,
};
