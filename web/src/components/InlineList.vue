<template>
    <div class="jacp-inline-list">
        <div
            class="jacp-inline-list__item"
            v-for="(item, $index) in data"
            :key="item.id"
        >
            <span class="jacp-inline-list__name">
                <slot>{{ item[nameAlias] }}</slot>
            </span>
            <span
                class="jacp-inline-list__removebtn"
                v-if="!disableRemove"
                @click="remove(item, $index)"
            >{{ $t('jacp.remove') }}</span>
        </div>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'JacpInlineList',
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        nameAlias: {
            type: String,
            default: 'name',
        },
        disableRemove: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        remove(item, index) {
            this.data.splice(index, 1);
            this.$emit('change', this.data);
        },
    },
};
</script>

<style lang="less">
@import '../theme/var';

.jacp-inline-list{
    &__item{
        border-right: 1px solid @borderColor;
        display: inline-block;
        margin-right: 20px;
        line-height: 1.2;
        padding-right: 20px;
        font-size: 12px;
        color: #666;

        &:last-child{
            border-right: none;
        }
    }
    &__name{
        margin-right: 5px;
    }
    &__removebtn{
        color: @linkColor;
        cursor: pointer;
        margin-left: 5px;
    }
}
</style>
