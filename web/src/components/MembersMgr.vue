<template>
    <div class="members-mgr__wrapper">
        <div class="members-mgr--left">
            <div class="members-mgr__header">
                <slot name="header" />
                <div class="members-mgr__searchbar">
                    <el-input
                        style="width: 235px"
                        placeholder="请输入工号/姓名/邮箱/部门名称"
                        v-model.trim="keyWord"
                        prefix-icon="el-icon-search"
                        @change="getSuggestion"
                        size="small"
                        clearable
                    />
                </div>
            </div>
            <div class="members-mgr__list">
                <el-table
                    header-row-class-name="table-head"
                    :data="suggestions"
                    height="364"
                    ref="multipleTableEl"
                >
                    <el-table-column width="60">
                        <template slot-scope="scope">
                            <i
                                class="el-icon-circle-plus members-mgr__icon"
                                v-if="!scope.row.checked"
                                :class="{ primary: !scope.row.checked }"
                                @click="doChecked(scope.row)"
                            />
                            <i
                                class="el-icon-remove members-mgr__icon j-hover-highlight"
                                @click="unChecked(scope.row)"
                                v-else
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="userErp"
                        label="工号"
                    />
                    <el-table-column
                        prop="userName"
                        label="姓名"
                    />
                    <el-table-column
                        prop="email"
                        label="邮箱"
                        :show-overflow-tooltip="true"
                    />
                    <el-table-column
                        prop="orgTierName"
                        label="部门"
                        :show-overflow-tooltip="true"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.orgTierName ? scope.row.orgTierName.split('-').pop() : '-' }}
                        </template>
                    </el-table-column>
                </el-table>
                <div class="members-mgr__list__pagenation">
                    <el-button
                        v-if="isSupportBatch"
                        class="j-mgt8"
                        type="primary"
                        size="small"
                        @click="batchAdd"
                    >
                        批量添加
                    </el-button>
                    <!-- <jacp-pagenation
                        class="demands__pagenation"
                        :total="data.total"
                        :current-page.sync="current"
                        :page-size.sync="pageSize"
                        @update:currentPage="getSuggestion"
                    /> -->
                    <el-pagination
                        class="demands__pagenation"
                        :total="data.total"
                        pager-count="5"
                        :current-page.sync="current"
                        :page-size.sync="pageSize"
                        @current-change="getSuggestion"
                        layout="prev, pager, next, sizes, jumper"
                    />
                </div>
            </div>
        </div>
        <div class="members-mgr--right">
            <slot
                name="checked"
                :data="checkedMembers"
            >
                <label class="members-mgr__label members-mgr__label--sticky">
                    <span>已选择{{ checkedMembers.length }}人</span>
                    <span
                        v-if="checkedMembers.length"
                        class="members-mgr__label--red"
                        @click="checkedMembers = []"
                    >清空</span>
                </label>
                <el-tag
                    class="members-mgr__item--checked"
                    size="small"
                    closable
                    v-for="item in checkedMembers"
                    @close="unChecked(item)"
                    :key="item.userErp"
                >
                    {{ item.userName }}
                </el-tag>
            </slot>
        </div>
    </div>
</template>
<script>
import SpaceModel from '@/models/space';
import pageMixis from '@/mixins/mixin.pagenation';

export default {
    name: 'JacpMembersMgr',
    mixins: [pageMixis],
    props: {
        members: {
            type: Array,
            default: () => [],
        },
        // 需要过滤掉的人
        except: {
            type: Array,
            default: () => [],
            validator: (arr) => {
                let isValid = true;
                arr.forEach((item) => {
                    if (item.constructor !== String) {
                        isValid = false;
                        /* eslint no-console: ["error", { allow: ["warn", "log"] }] */
                        console.warn('[props:"except"]请传个只有工号的数组就好了');
                    }
                });
                return isValid;
            },
        },
        isSupportBatch: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            checkedMembers: [],
            keyWord: '',
            // memberType: [],
        };
    },
    mounted() {
        this.getSuggestion();
    },
    computed: {
        checkedMembersAccountList() {
            return this.checkedMembers.map(item => item.userErp);
        },
        suggestions() {
            const { except, checkedMembersAccountList: checked } = this;
            const suggestions = this.data.records.map((item) => {
                const isChecked = checked.includes(item.userErp);
                if (except.length && except.includes(item.userErp)) {
                    return undefined;
                }
                return Object.assign(item, {
                    checked: isChecked,
                });
            }) || [];
            return suggestions.filter(item => item !== undefined);
        },
    },
    methods: {
        getSuggestion() {
            const { keyWord, pageSize, current } = this;
            SpaceModel.getUserListForAdd({
                keyWord: keyWord || this.$store.state.user.orgTierName,
                pageSize,
                current,
            }).then((data) => {
                // map为兼容方案，account与name已不使用，后期删除
                data.records.map((item) => {
                    const { account, name } = item;
                    return Object.assign(item, { userErp: item.userErp || account, userName: item.userName || name });
                });
                this.setData(data);
            });
        },
        setData(data) {
            this.data = Object.assign({}, this.data, data);
        },
        doChecked(item) {
            this.pushIntoCheckedMembers(item);
            this.$emit('on-checked', item);
        },
        pushIntoCheckedMembers(item) {
            function hasMember(user) {
                return user.userErp === item.userErp;
            }
            if (this.checkedMembers.some(hasMember)) {
                return;
            }
            this.checkedMembers.push(item);
        },
        unChecked(item) {
            this.checkedMembers = this.checkedMembers.filter(o => o.userErp !== item.userErp);
            this.$emit('on-unchecked', item);
        },
        clearALl() {
            this.checkedMembers = [];
        },
        batchAdd() {
            this.suggestions.forEach(this.pushIntoCheckedMembers);
        },
        reset() {
            this.keyWord = '';
            this.current = 1;
            this.getSuggestion();
        },
    },
    watch: {
        members: {
            immediate: true,
            handler(val) {
                if (this.checkedMembers === val || !val || !Array.isArray(val)) {
                    return;
                }
                this.checkedMembers = val.slice();
            },
        },
        checkedMembers: {
            handler(val) {
                this.$emit('change', val);
            },
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.members-mgr{
    &__wrapper{
        display: flex;
    }
    &--left{
        width: 616px;
        border-right: solid 1px #EBEEF5;
        border-bottom: solid 1px #EBEEF5;
        padding: 0 16px;
    }
    &__list{
        margin-top: 16px;
        & > .el-table::before {
            height: 0;
        }
        &__pagenation {
            height: 32px;
            margin: 16px 0;
        }
        .table-head th {
            background-color: #f5f7fa;
        }
    }

    &--right{
        width: 223px;
        padding: 0 16px;
        max-height: 490px;
        overflow: auto;
        position: relative;
        border-bottom: solid 1px #EBEEF5;
    }
    &__label{
        display: flex;
        width: 100%;
        justify-content: space-between;
        font-size: 12px;
        padding: 16px 0;
        &--sticky{
            top: 0;
            position: sticky;
            background: #fff;
            z-index: 1;
        }
    }
    &__label--red{
        color: @redColor;
    }
    &__item--checked{
        margin-right: 8px;
        margin-top: 8px;
    }
    &__icon{
        &.primary{
            color: @primaryColor;
        }
        font-size: 14px;
    }
    &__header{
        display: flex;
        justify-content: space-between;
        margin-top: 16px;
        line-height: 28px;
        font-size: 12px;
        color: #303133;
    }
    &__searchbar {
        .el-input__inner {
            height: 28px;
        }
    }
}
</style>
