<template>
    <div class="jacp-autocomplete-suggestions">
        <strong
            v-if="title"
            class="jacp-autocomplete-suggestions__title"
        >{{ title }}</strong>
        <ul class="jacp-autocomplete-suggestions__list">
            <li v-if="!parent.hideLoading && parent.loading">
                <i class="el-icon-loading" />
            </li>
            <slot v-else />
        </ul>
    </div>
</template>
<script>
export default {
    name: 'JacpAutocompleteSuggestions',
    props: {
        title: String,
    },
    data() {
        return {
            parent: this.$parent,
        };
    },
};
</script>
<style lang="less">
.jacp-autocomplete-suggestions{
    &__title{
        margin-bottom: 16px;
        display: block;
    }
    &__list{
        // margin-top: 8px;
        li{
            list-style: none;
            margin: 8px 4px;
            &:first-child{
                margin-top: 0;
            }
        }
    }
}
</style>
