<template>
    <div :class="['j-nodata', position]">
        <slot>
            <div class="j-nodata__img">
                <img
                    v-if="placeholderImg"
                    src="../assets/images/<EMAIL>"
                    :alt="label"
                >
                <p><slot name="label">{{ label }}</slot></p>
            </div>
        </slot>
    </div>
</template>
<script>
export default {
    name: 'JacpEmpty',
    props: {
        label: {
            default: '暂无数据',
            type: String,
        },
        placeholderImg: {
            type: Boolean,
            default: true,
        },
        position: {
            type: String,
            default: 'middle',
        },
    },
};
</script>
<style lang="less">
.j-nodata{
    &.middle{
        margin: auto;
        position: absolute;
        top: 0; left: 0; bottom: 0; right: 0;
        height: 180px;
        height: min-content;
        display: flex;
        flex-direction: column;
        p{
            margin: 8px 0;
        }
    }
    .j-nodata__img>img{
        width: 80px;
    }
}
</style>
