<script>
export default {
    name: 'CustomTag',
    props: {
        isSelected: Boolean,
        color: {
            type: String,
            // default: '#C5C5C5',
        },
        borderColor: String,
        closable: Boolean,
        // 镂空的，边框和文字一个色，背景透明10%，selected也是一个色
        plain: Boolean,
        // 背景和文字同色,背景20%透明度
        colorful: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            // 用来区分是否有color送过来，没有color送过来，selected的时候用primaryColor
            defaultColor: this.color || '#C5C5C5',
        };
    },
    // eslint-disable-next-line
    render(h) {
        const { plain, colorful } = this;
        return (
            <el-tag
                class={{
                    'jacp-custom-tag': true,
                    selected: this.isSelected,
                    plain,
                    colorful: colorful && !plain,
                    default: !this.color,
                }}
                closable={this.closable}
                data-color={ this.color === 'NONE' ? '#2695F1' : this.defaultColor }
                onClose={this.handleClose}
                size="small">{ this.$slots.default }<i class="el-icon-check jacp-custom-tag__mark"></i></el-tag>
        );
    },
    methods: {
        handleClose() {
            this.$emit('on-close');
        },
    },
};

</script>
<style lang="less">
@import '~@/theme/var';
.mapTagColors(@value) {
    .jacp-custom-tag[data-color="@{value}"] {
        &.colorful{
            .colorful(@value);
            &:hover{
                background-color: fade(@value, 20%);
            }
            &.selected{
                .selected();
                color: @value;
                border-color: @value;
                background-color: fade(@value, 10%);
                &:after{
                    border-bottom: 14px solid @value;
                }
            }
        }

        &.plain{
            .plain(@value);
            &:hover{
                background-color: fade(@value, 10%);
            }
            &.selected{
                .selected();
                color: @value;
                border-color: @value;
                background-color: transparent;
                &:after{
                    border-bottom: 14px solid @value;
                }
            }
        }
        &.plain.default{
            &.selected{
                .selected();
            }
        }
        .el-tag__close{
            color: @value;
            // position: absolute;
            right: 0px;
            // top: 4px;
            &:hover{
                background-color: @value;
                color: #fff;
            }
        }
    }
}
// 包括新色值和老色值
each(@cardTypeColors, {
    .mapTagColors(@value);
});
each(@tagColors, {
    .mapTagColors(@value);
});

.jacp-custom-tag{
    border-radius: 2px;
    position: relative;
    max-width: 180px;
    white-space: normal;
    height: auto;
    text-align: left;
    &:hover{
        // font-weight: 600;
    }
    &__mark{
        display: none;
        position: absolute;
        bottom: -2px;
        right: -2px;
        color: #fff;
        z-index: 1;
        transform: scale(.7);
        font-weight: 800;
    }
    &.selected{
        .jacp-custom-tag__mark{
            display: inline-block;
        }
    }
}
</style>
