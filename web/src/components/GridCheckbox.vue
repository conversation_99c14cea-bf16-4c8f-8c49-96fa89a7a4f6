<template>
    <div class="jacp-grid-checkbox">
        <label
            class="jacp-grid-checkbox__title"
            v-if="title"
        >
            {{ title }}
        </label>
        <ul
            class="jacp-grid-checkbox__list"
            :style="{
                'grid-template-columns': `repeat(${colunm}, ${itemWidth})`,
                'grid-gap': `${span}px`
            }"
        >
            <li
                v-for="(field) in data"
                :key="field.name"
            >
                <slot :item="field">
                    <el-checkbox
                        :label="field.name"
                        :checked="field[valueKey]"
                        v-model="field[valueKey]"
                        :disabled="field.disabled"
                        @change="$emit('change', field)"
                    >
                        <slot
                            name="item"
                            :item="field"
                        >
                            {{ field[labelKey] }}
                        </slot>
                    </el-checkbox>
                </slot>
            </li>
        </ul>
    </div>
</template>
<script>
export default {
    name: 'JacpGridCheckbox',
    inheritAttrs: false,
    props: {
        title: { type: String, default: '' },
        data: { type: Array, default: () => [] },
        valueKey: { type: String, default: 'required' },
        labelKey: { type: String, default: 'label' },
        // 加一个简单的布局处理，用于方便控制输出n列
        colunm: { type: Number, default: 4 },
        span: { type: Number, default: 16 },
        itemWidth: { type: String, default: '1fr' },
    },
};
</script>
<style lang="less">
  .jacp-grid-checkbox{
      &+&{
          margin-top: var(--gutter--xlarge);
      }
    &__title{
      font-size: var(--font-size--description);
      color: var(--color--base--description);
    }
    &__list{
        display: grid;
        margin-top: var(--gutter--small);
        &ul, & li {
            list-style-type: none;

        }
        & li .el-checkbox{
            align-items: center;
            display: flex;
        }
        & li .el-checkbox__label{
            font-weight: var(--font-weight-default);
            line-height:  18px;
            vertical-align: middle;
        }
    }
  }
</style>
