<template>
    <el-tooltip
        :content="tips"
        placement="top"
        :disabled="!tips"
    >
        <jacp-form-select
            style="width: 100%;"
            ref="selectEl"
            placeholder="请选择关联的项目"
            popper-class="related-propject__popper"
            :value="value"
            :loading="loading"
            :options="projectGroup"
            :filter-method="$event => searchStr = $event"
            v-bind="[$attrs]"
            v-on="$listeners"
            @input="($event) => $emit('input', $event)"
            @visible-change="$event => {
                if ($event) loadOptions()
            }"
        >
            <!-- 统一使用分组 -->
            <el-option-group
                v-for="group in filteredGroup"
                :key="group.value"
                :label="group.label"
                v-show="isGroupVisible(group)"
            >
                <el-option
                    v-for="item in group.options"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                    <div class="name">{{ item.name }}</div>
                    <div v-if="item.extra" class="extra">{{ item.extra }}</div>
                </el-option>
                <el-option
                    v-if="!group.options.length"
                    key="no-data"
                    style="cursor: default;"
                    label="无"
                    value="nodata"
                    :disabled="true"
                />

                <el-option
                    v-if="group.value !== 'other' && hasMore && !moreVisible"
                    key="more"
                    class="related-propject__more"
                    label="查看更多"
                    value="more"
                    :disabled="true"
                    @click.native="() => moreVisible = true"
                />
            </el-option-group>
        </jacp-form-select>
    </el-tooltip>
</template>
<script>
import PmpModel, { PmpProject } from '@/models/pmp';
import { findOptionExistsByGroup } from '@/plugins/utils';

const cacheMap = new WeakMap();

// 检查两个分组中是否存在当前需求的项目，如果不存在的话，则作为选项回填到分组all中
const fillWithCurrentProject = (projectGroup = [], entity = {}) => {
    if (!entity.pmpProjectId) {
        return projectGroup;
    }
    const exists = findOptionExistsByGroup(projectGroup, item => item.id === entity.pmpProjectId);
    if (!exists) {
        const other = projectGroup.find(g => g.value === 'other');
        if (other) {
            other.options.push(new PmpProject(entity));
        }
    }
    return projectGroup;
};
const isParmasChanged = (oldValue = {}, newValue = {}) => {
    const oldValueKeys = Object.keys(oldValue);
    const newValueKeys = Object.keys(newValue);
    if (oldValueKeys.length !== newValueKeys.length) {
        return true;
    }
    return !newValueKeys.every(key => oldValueKeys.includes(key) && (oldValue[key] === newValue[key]));
};
export default {
    name: 'RelatedProjects',
    inheritAttrs: true,
    props: {
        value: { type: [String, Number] },
        entity: { type: Object, required: true },
        tips: { type: String, default: '' },
        // 是否在点击下拉选框的时候再拉取选项, 默认是
        delayLoadOptions: { type: Boolean, default: true },
        requestParameters: { type: Object, default: () => ({}) },
    },
    data() {
        return {
            projectGroup: [], moreVisible: false, searchStr: '', loading: false,
        };
    },
    computed: {
        hasMore() {
            return !!this.projectGroup.find(g => g.value === 'other');
        },
        filteredGroup() {
            return this.searchStr ? this.projectGroup.map(group => ({
                ...group,
                options: group.options.filter(option => option.name.includes(this.searchStr)),
            })) : this.projectGroup;
        },
    },
    methods: {
        // 如果有项目结项了的话，需要把option回填到all中。另外，还要保证在选项中能找到，不能用v-if或者filter处理all的隐藏
        isGroupVisible(group) {
            return this.moreVisible ? true : group.value !== 'other';
        },
        loadOptions() {
            if (cacheMap.has(this)) {
                return Promise.resolve(cacheMap.get(this));
            }
            this.loading = true;
            // TODO: 目前只有需求的分组了，所以传的就是需求id
            return PmpModel.getProjectList(this.requestParameters, true)
                .then((res = []) => {
                    this.loading = false;
                    this.projectGroup = fillWithCurrentProject(res, this.entity);
                    cacheMap.set(this, this.projectGroup);
                });
        },
    },
    watch: {
        delayLoadOptions: {
            immediate: true,
            handler(val) {
                if (!val) {
                    this.loadOptions();
                }
            },
        },
        // 当前需求的信息发生变化时重新回填一下
        entity: {
            immediate: true,
            deep: true,
            handler() {
                this.projectGroup = fillWithCurrentProject(
                    this.projectGroup.length ? this.projectGroup : PmpProject.createProjectGroup(),
                    this.entity,
                );
            },
        },
        // 监听查询条件的变化
        requestParameters: {
            deep: true,
            handler(newValue = {}, oldValue) {
                if (oldValue && oldValue !== newValue) {
                    const changed = isParmasChanged(oldValue, newValue);
                    if (changed) {
                        this.$emit('input', ''); // 搜索条件发生变化， 清空已选项目。
                        cacheMap.delete(this);
                    }
                }
            },
        },
    },
    destroyed() {
        cacheMap.delete(this);
    },
};
</script>
<style lang="less">
@import '~@/theme/var.less';
.related-propject__more.el-select-dropdown__item.is-disabled{
    cursor: pointer;
    text-align: center;
    color: @primaryColor;
    &:hover{
        color: @linkHoverColor;
    }
}
.related-propject__popper {
    .el-select-dropdown__item {
        height: auto;
    }
    .extra {
        font-size: var(--font-size--description);
        color: var(--color--error);
        line-height: 20px;
    }
}
</style>
