<template>
    <!-- 一个没有边框的时间选择器。。。用在新版详情页面 -->
    <div class="simple-datepicker">
        <div
            class="simple-datepicker__viewmode"
            @click="handleClick"
            :class="{
                highlight: isActived && !disabled,
                'j-hover-highlight': !disabled,
            }"
        >
            <span v-if="type === 'date'">{{ value | jacp-local-time('YYYY-MM-DD') }}</span>
            <span v-else>{{ value | jacp-local-time }}</span>
            <i
                class="el-icon-date"
                v-if="!disabled"
            />
        </div>
        <el-date-picker
            ref="pickerEl"
            class="simple-datepicker__trigger"
            :value="value"
            v-bind="[$props, $attrs]"
            v-on="$listeners"
            :picker-options="pickerOptions"
            :type="type"
            @focus="isActived = true"
            @blur="handleBlur"
        />
    </div>
</template>
<script>
import Form from '@/mixins/mixin.form';

export default {
    name: 'JacpSimpleDatePicker',
    mixins: [Form],
    inheritAttrs: false,
    props: {
        pickerOptions: {
            type: Object,
            default: () => ({}),
        },
        type: {
            type: String,
        },
    },
    data() {
        return {
            isActived: false,
        };
    },
    methods: {
        handleClick() {
            if (this.disabled) {
                return;
            }
            if (!this.$refs.pickerEl) {
                return;
            }
            this.$refs.pickerEl.focus();
        },
        handleBlur() {
            this.isActived = false;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.simple-datepicker{
    position: relative;

    &__viewmode{
        position: relative;
        &.highlight{
            color: @primaryColor;
        }
        z-index: 1;
    }
    &__trigger{
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        // z-index: 1;
    }
}
</style>
