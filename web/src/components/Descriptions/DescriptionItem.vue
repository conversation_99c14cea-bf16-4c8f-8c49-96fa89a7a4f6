<script>
import isFunction from 'lodash/isFunction';
import isString from 'lodash/isString';
import isDate from 'lodash/isDate';
import isUndefined from 'lodash/isUndefined';
import moment from 'moment';

// 判断value类型
const isDateRange = type => type === 'dateRange';
const isTimeValue = type => type.includes('date') || type.includes('time');
const isEnumValue = type => type.includes('enum');

// 默认的时间format格式
const DEFAULT_TIME_FORMAT = {
    time: 'HH:mm:ss',
    datetime: 'YYYY-MM-DD HH:mm:ss',
    date: 'YYYY-MM-DD',
};
// 处理时间类型的value
const formatTimeValue = (type, value, config = {}) => {
    // console.log('formatTimeValue', value);
    const format = config.timeValueFormat || DEFAULT_TIME_FORMAT[type] || '';
    if (isDate(value)) return moment(value).format(format);
    // 时间段的是个数组
    let valueArray;
    if (Array.isArray(value)) {
        valueArray = value;
        return valueArray
            .map(dateValue => moment(dateValue).format(format))
            .join(config.dateRangeDelimiter);
    } if (isDateRange(type) || (isString(value) && value.startsWith('[') && value.endsWith(']'))) {
        try {
            valueArray = JSON.parse(value);
        } catch (e) {
            // console.log(e);
        }
        return valueArray
            .map(dateValue => moment(dateValue).format(format))
            .join(config.dateRangeDelimiter);
    }
    return moment(+value).format(format);
};
// 给value包裹一层class
const createValueNode = (h, value, config = {}) => h('span', {
    class: 'jacp-description-item__value',
    ...config,
}, value);
// 获取所有children的text
const getChildrenTextContent = children => children.map(node => (node.children
    ? getChildrenTextContent(node.children)
    : node.text)).join('');

// 返回value的vNode
const getValueNode = (h, context) => {
    const {
        value: originValue,
        valueType, unit, valueFormatter, timeValueFormat, dateRangeDelimiter,
        valueEnum,
        valueStyle,
    } = context.props;

    if (!valueType) return null;
    let textContent;
    if (isUndefined(originValue)) {
        textContent = getChildrenTextContent(context.children)
            .replace(/\r\n/g, '')
            .replace(/\n/g, '')
            .replace(/\s/g, '');
    }
    let value = originValue ?? textContent;
    // 优先使用自定义的format方式
    if (valueFormatter && isFunction(valueFormatter)) {
        return [createValueNode(h, valueFormatter(value), { style: valueStyle })];
    }
    // 时间类型
    if (isTimeValue(valueType)) {
        const timeFormatConfig = { timeValueFormat, dateRangeDelimiter };
        value = formatTimeValue(valueType, value, timeFormatConfig);
    }
    // 枚举类型
    if (isEnumValue(valueType)) {
        if (valueEnum.length) {
            if (Array.isArray(originValue)) {
                value = valueEnum.reduce((valueLabelArray, v) => {
                    if (originValue.includes(v.value)) {
                        valueLabelArray.push(v.label);
                    }
                    return valueLabelArray;
                }, []).join(',');
            } else {
                const targetValueObj = valueEnum.find(v => v.value === value);
                value = targetValueObj ? targetValueObj.label : value;
            }
        }
    }
    const valueNode = createValueNode(h, value, { style: valueStyle });
    if (!unit) {
        return [valueNode];
    }
    // 有单位的追加一个单位的vNode，便于处理成与value不同的样式
    const unitNode = h('span', {
        class: 'jacp-description-item__unit',
    }, unit);
    return [valueNode, unitNode];
};
const isValidValueType = val => [
    'text', 'number', 'integer',
    'date', 'datetime', 'dateRange', 'time',
    'enum'].includes(val);
export default {
    name: 'DescriptionItem',
    functional: true,
    props: {
        label: { type: String, default: '' },
        labelWidth: { type: Number, default: 80 },
        vertical: { type: Boolean, default: false },
        value: { type: [String, Object, Number, Array] },
        // text, number, date, datetime, dateRange, enum, time
        valueType: {
            type: String,
        },
        valueEnum: {
            type: Array,
            default: () => [],
        },
        unit: { type: String, default: '' },
        timeValueFormat: { type: String, default: '' },
        dateRangeDelimiter: { type: String, default: '-' },
        valueFormatter: { type: Function },
        valueStyle: { type: Object, default: () => ({}) },
        align: { type: String, default: 'flex-start' },
    },
    render(h, context) {
        const { props, slots } = context;
        let valueNode;
        if (isValidValueType(props.valueType)) {
            valueNode = getValueNode(h, context);
        }
        if (!valueNode) {
            valueNode = slots().default;
        }

        /* <EllipsisText
                    maxWidth={props.labelWidth}
                >{props.label}</EllipsisText> */
        return (<div
            class={{
                'jacp-description-item': true,
                'jacp-description-item--vertical': props.vertical,
            }}
            style={{ alignItems: props.align }}
        >
            { props.label ? <div class="jacp-description-item__label"
                title={props.label}
                style={{
                    width: `${props.labelWidth}px`,
                }}>{props.label}</div>
                : null }
            <div class="jacp-description-item__content">{valueNode}</div>
        </div>);
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-description-item{
    display: flex;

    width: 100%;
    padding: 2px 0;
    &--vertical{
        display: flex;
        flex-direction: column;
    }
    &__label{
        display: inline-block;
        flex-shrink: 0;
        color: @fontThirdColor;
        font-size: var(--font-size--description);
        font-weight: normal;
        line-height: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: var(--gutter--mini) 0;
    }
    &__value,
    &__unit{
        font-size: var(--font-size--description);
        color: @fontColor;
        line-height: 16px;
    }
    &__value{
        padding-top: var(--gutter--mini);
        display: inline-block;
        white-space: normal;
    }
    &__content{
        margin-left: var(--gutter--small);
    }
}
</style>
