<template>
    <span>{{ timeStr }}</span>
</template>

<script type="text/javascript">
import moment from 'moment';

function getDuration(a, b) {
    if (!a || !b) {
        return '--:--';
    }
    const start = moment(a);
    const end = moment(b);
    const res = moment.utc(Math.abs(start - end)).format('HH:mm:ss');
    if (res.indexOf('00:') === 0) {
        return res.replace(/^00:/, '');
    }
    return res;
}

export default {
    name: 'JacpTimer',
    props: {
        startTime: {
            type: Number,
        },
        autoTimer: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            time: this.startTime,
            timeStr: '',
        };
    },
    methods: {
        startTimer() {
            clearInterval(this.timer);
            if (!this.startTime) {
                return;
            }
            this.timer = setInterval(() => {
                this.timeStr = getDuration(new Date(), this.startTime);
            }, 1000);
        },
        endTimer() {
            clearInterval(this.timer);
        },
    },
    watch: {
        autoTimer: {
            handler(n) {
                if (n && this.time) {
                    this.startTimer();
                } else {
                    this.endTimer();
                }
            },
            immediate: true,
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
};
</script>
