<template>
    <div class="jacp-history__root">
        <div
            class="jacp-history__item"
            v-for="data in list"
            :key="data.id"
        >
            <label class="jacp-history__time">
                {{ +data.cTime|jacp-local-time }}
            </label>
            <jacp-erp
                class="jacp-history__operator"
                :data="data.operator"
                :disable-timline="data.operator.isCurrentUser"
            />
            <div class="jacp-history__remark">
                <slot :row="data">
                    <span v-html="data.remark" />
                </slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'JacpHistory',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
    },
};
</script>

<style lang="less">
.jacp-history {
    &__root{
        font-size: 12px;
        line-height: 1.2;
    }
    &__item{
        display: flex;
        margin-bottom: 15px;
    }

    &__time,
    &__operator {
        min-width: 100px;
        margin-right: 20px;
    }
    &__time{
        min-width: 130px;
    }
    &__remark{
        display: inline-block;
        white-space: pre-wrap;
        word-break: break-all;
    }
}
</style>
