<template>
    <div
        class="jacp-users-input__root"
        :class="{
            'jacp-users-input--isradio': localMaxCount === 1
        }"
        @click="focus"
    >
        <div
            class="jacp-users-input__multi"
            :class="{
                'jacp-users-input__multi__disabled': disabled
            }"
            v-if="localMaxCount > 1"
        >
            <el-tag
                :key="item.name"
                v-for="(item, $index) in value"
                class="jacp-users-input__tag"
                :closable="!disabled"
                @close="remove($index)"
            >
                {{ item.name }}
            </el-tag>
            <el-autocomplete
                class="jacp-users-input__input"
                v-model="text"
                v-show="value.length < localMaxCount"
                :disabled="disabled"
                :fetch-suggestions="querySearch"
                :placeholder="placeholder"
                placement="bottom-end"
                :trigger-on-focus="false"
                popper-class="jacp-users-input__autocomplete"
                @select="add"
                @keydown.native="pop"
                :prefix-icon="prefixIcon"
            >
                <li
                    class="jacp-useritem"
                    slot-scope="{item}"
                >
                    <div class="jacp-useritem__name">
                        {{ item.name }}({{ item.erp }})
                    </div>
                    <span class="jacp-useritem__erp">{{ item.orgTierName|jacp-user-orgname }}</span>
                </li>
            </el-autocomplete>
        </div>
        <div
            class="jacp-users-input__radio"
            v-else
        >
            <el-select
                class="jacp-users-input__select"
                ref="selectIns"
                v-model="radioSelectedErp"
                filterable
                remote
                @change="updateValue"
                :disabled="disabled"
                :loading="loading"
                :clearable="clearable"
                :placeholder="placeholder"
                :remote-method="querySearchForRadio"
            >
                <el-option
                    v-for="(user, index ) in options"
                    class="jacp-users-input__item"
                    :key="index"
                    :label="user.name + (displayErp ? ' ('+user.erp+')':'')"
                    :value="user.erp"
                >
                    <div class="jacp-useritem__name">
                        {{ user.name }}({{ user.erp }})
                    </div>
                    <span class="jacp-useritem__erp">{{ user.orgTierName|jacp-user-orgname }}</span>
                </el-option>
            </el-select>
        </div>
    </div>
</template>

<script type="text/javascript">
import { promisefy } from '@/plugins/utils';
import UserModel from '@/models/user';
import FormValidate from '@/mixins/mixin.form.validate';

export default {
    name: 'JacpInputUsers',
    mixins: [FormValidate],
    props: {
        placeholder: {
            default: '添加',
            type: String,
        },
        value: {
            default: () => [],
            type: Array,
        },
        maxCount: {
            default: Infinity,
            type: Number,
        },
        beforeAdd: {
            type: Function,
            default: () => {},
        },
        clearable: {
            type: Boolean,
            default: true,
        },
        displayErp: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        loadUsers: {
            type: Function,
            // default: UserModel.search,
            default: UserModel.searchUserInfoFilter,
        },
        prefixIcon: {
            type: String,
            default: '',
        },
    },
    data() {
        const isRadio = this.maxCount === 1;
        const erp = this.value.map(item => item.erp)[0] || '';
        return {
            localMaxCount: this.maxCount,
            loading: false,
            isRadio,
            text: '',
            radioSelectedErp: erp,
            errorMsg: '',
            options: [],
            beforeAddFn: promisefy(this.beforeAdd).bind(this),
            loadUsersFn: promisefy(this.loadUsers).bind(this),
        };
    },
    methods: {
        querySearchForRadio(str) {
            const searchStr = str.replace(/\(.*/ig, '').replace(/\s/ig, '');
            if (!searchStr) {
                this.options = [];
            } else {
                this.loading = true;
                this.loadUsersFn(searchStr).then((users) => {
                    this.loading = false;
                    this.options = users;
                }).catch(() => {
                    this.loading = false;
                });
            }
        },
        querySearch(str, cb = () => {}) {
            if (!str) {
                cb([]);
            }
            return this.loadUsersFn(str).then(users => users.filter(
                user => !this.selectedErps.includes(user.erp.toLowerCase()),
            )).then(cb);
        },
        add(user) {
            this.beforeAddFn(user, this.value).then(() => {
                this.value.push(user);
                this.text = '';
                this.$emit('add', user);
                this.$emit('input', this.value);
                this.focus();
            });
        },
        updateValue(userErp) { // just for radio
            const oldValue = (this.value[0] || {}).erp;
            const newValue = userErp || undefined;
            if (newValue === oldValue) {
                return;
            }
            if (!userErp && this.value.length) {
                this.remove();
                return;
            }
            const users = this.options.filter(user => user.erp === userErp);
            if (oldValue) {
                this.$emit('input', users);
                return;
            }
            this.beforeAddFn(users[0], this.value).then(() => {
                this.$emit('input', users);
                this.$_triggerValidateChangeEvent();
            }).catch(() => {
                // restore
                this.radioSelectedErp = this.value.map(user => user.erp)[0] || '';
                this.$refs.selectIns.query = '';
            });
        },
        remove(index) {
            if (this.isRadio) {
                if (!this.value.length) {
                    return;
                }
                this.$emit('remove', this.value[0]);
                this.$emit('input', []);
            } else {
                this.$emit('remove', this.value[index]);
                this.value.splice(index, 1);
                this.$emit('input', this.value);
            }
        },
        pop(e) {
            if (e.keyCode === 8 && !this.text) { // delete键
                this.remove(this.value.length - 1);
            }
        },
        focus() {
            const self = this;
            this.$nextTick(() => {
                self.$el.querySelector('input').focus();
            });
        },
    },
    computed: {
        selectedErps() {
            return (this.value || []).map(user => user.erp.toLowerCase());
        },
    },
    watch: {
        value: {
            handler: function valueChange(n) {
                let radioValue;
                if (this.isRadio) {
                    if (n.length === 0) {
                        this.radioSelectedErp = '';
                    }
                    if (n && n.length) {
                        radioValue = n[0].erp;
                    }
                    if (radioValue) {
                        this.options = this.value;
                    }
                    if (radioValue && (radioValue !== this.radioSelectedErp)) {
                        this.radioSelectedErp = radioValue;
                    }
                    if (!radioValue && this.radioSelectedErp) {
                        this.radioSelectedErp = '';
                    }
                }
                this.$emit('change', n);
            },
            deep: true,
            immediate: true,
        },
        maxCount: {
            handler() {
                this.localMaxCount = this.maxCount;
            },
            immediate: true,
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.is-error{
    .jacp-users-input__multi{
        border-color: var(--color--error);
    }
}
.jacp-users-input__select{
    width: 100%;
}
.jacp-users-input__multi {
    border: 1px solid @formBorderColor;
    padding: 0 5px;
    background-color: #fff;
    border-radius: var(--radius--default);

    &__disabled {
        background-color: #f5f7fa;
    }
}
.jacp-users-input__tag{
    margin-right: 5px;
}
.jacp-users-input__input{
    outline: none;
    font-size: 14px;

    & input{
        border: 0;
        padding-left: 0;
    }
}
.jacp-users-input__item{
    height: auto;
}
.jacp-useritem__erp{
    font-size: 12px;
    color: #b4b4b4;
}
.jacp-users-input__autocomplete{
    width: 300px!important;

    & .el-select-dropdown__item.selected .jacp-useritem__erp{
        color: var(--color--base--description);
    }
    & li.jacp-useritem{
        padding: 7px 10px;
        line-height: 1.4;

        & .highlighted .jacp-useritem__erp{
            color: var(--color--base--description);
        }
    }
}
</style>
