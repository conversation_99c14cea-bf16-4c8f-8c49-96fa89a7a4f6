<template>
    <div
        class="jacp-offcanvas"
        @mousedown="hide"
    >
        <transition name="jacp-offcanvas__fade">
            <div
                class="jacp-offcanvas__bg"
                v-show="openStatus"
                :class="{
                    'jacp-offcanvas__bg-hidden': noGrayBackground,
                    'jacp-offcanvas__bg-nofixed': !fixedOnDocument,
                }"
            />
        </transition>
        <transition
            name="jacp-offcanvas__slide-right"
            @after-leave="afterLeaveHandle()"
        >
            <div
                class="jacp-offcanvas__main"
                ref="mainEl"
                :class="{
                    'jacp-offcanvas__main-nofixed': !fixedOnDocument
                }"
                v-if="openStatus"
                @mousedown.stop
            >
                <slot />
            </div>
        </transition>
    </div>
</template>

<script type="text/javascript">
import Emitter from 'element-ui/src/mixins/emitter';

export default {
    name: 'JacpOffCanvas',
    componentName: 'JacpOffCanvas',
    mixins: [
        Emitter,
    ],
    props: {
        noGrayBackground: {
            type: Boolean,
            default: false,
        },
        fixedOnDocument: {
            type: Boolean,
            default: true,
        },
        beforeHide: {
            type: Function,
        },
    },
    created() {
        document.dispatchEvent(new MouseEvent('mousedown'));
    },
    data() {
        return {
            parentNodeOverflow: '',
            openStatus: false,
            cachedElement: undefined,
        };
    },
    mounted() {
        this.cachedElement = this.fixedOnDocument ? window.document.body : this.$el.parentNode;

        this.$on('on-hide', this.hide);
    },
    methods: {
        hide() {
            if (!this.openStatus) {
                return;
            }
            if (!this.beforeHide) {
                this.openStatus = false;
                return;
            }
            // before返回true就正常关闭，返回了false就不关
            const before = this.beforeHide();
            if (typeof before !== 'undefined' && before.then) {
                before.then(() => {
                    this.openStatus = false;
                });
                return;
            }
            this.openStatus = !before;
        },
        setOpenStatus(status) {
            this.openStatus = status;
        },
        open() {
            setTimeout(() => {
                if (!this.cachedElement) {
                    return;
                }
                const computedStyle = window.getComputedStyle(this.cachedElement);
                if (!this.fixedOnDocument && computedStyle.position === 'static') {
                /* eslint-disable no-console */
                    console.warn('parentNode\'s position shouldn\'t be static', this.cachedElement);
                }
                this.openStatus = true;
                this.parentNodeOverflow = this.cachedElement.style.overflow;
                this.cachedElement.style.overflow = 'hidden';
                this.$emit('on-open');
                this.$nextTick(() => {
                    if (this.$refs.mainEl) {
                        this.$refs.mainEl.addEventListener('scroll', this.handleScroll, false);
                    }
                });
            });
        },
        afterLeaveHandle() {
            this.$emit('hide');
            if (this.cachedElement?.style) {
                this.cachedElement.style.overflow = this.parentNodeOverflow;
            }
        },
        handleScroll(ev) {
            // 寻找匹配的componentName
            this.broadcast('JacpLayoutDetail', 'off-canvas-scroll', ev);
        },
    },
    destroyed() {
        this.cachedElement.style.overflow = this.parentNodeOverflow;
        if (this.$refs.mainEl) {
            this.$refs.mainEl.removeEventListener('scroll', this.handleScroll, false);
        }
        this.cachedElement = null;
    },
};
</script>

<style lang="less">
.jacp-offcanvas{
    &__bg{
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        // background-color: rgba(0, 0, 0, 0.35);
        z-index: 100;

        &-hidden{
            opacity: 0!important;
        }
        &-nofixed{
            position: absolute;
        }
    }
    &__main{
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        background-color: #fff;
        z-index: 101;
        overflow: auto;
        box-shadow: -15px 0 15px -10px rgba(0, 0, 0, 0.2);

        &-nofixed{
            position: absolute;
        }
    }

    &__fade-enter-active,
    &__fade-leave-active {
        transition: opacity 0.2s ease;
    }
    &__fade-leave-active {
        opacity: 0;
    }
    &__fade-enter-active,
    &__fade-leave {
        opacity: 1;
    }
    &__fade-enter {
        opacity: 0;
    }

    &__slide-right-enter-active,
    &__slide-right-leave-active {
        transition: transform 0.3s ease;
    }
    &__slide-right-leave-active {
        transform: translateX(100%);
    }
    &__slide-right-enter-active,
    &__slide-right-leave {
        transform: translateX(0);
    }
    &__slide-right-enter {
        transform: translateX(100%);
    }
}
</style>
