<template>
    <div class="jacp-input-checkbox">
        <el-checkbox
            v-for="item in data"
            :key="item.id"
            :label="item.id"
            :value="value[item.id]"
            @input="updateVal(item.id, $event)"
        >
            {{ item.name }}
        </el-checkbox>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'JacpInputCheckbox',
    props: {
        data: {
            type: Array,
            required: true,
        },
        value: {
            type: Object,
        },
    },
    methods: {
        updateVal(key, newVal) {
            this.$set(this.value, key, newVal);
            this.$emit('input', this.value);
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';


</style>
