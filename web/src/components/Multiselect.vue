<template>
    <div
        class="jacp-multiselect"
        @click.stop
    >
        <div
            class="jacp-multiselect__header el-input__inner"
            @click="toggleDropdown"
            :class="{
                'jacp-multiselect__header--hasvalue': selected.length,
                'jacp-multiselect__header--clearable': clearable
            }"
        >
            <span
                class="jacp-multiselect__value j-text-overflow"
                :class="{
                    'jacp-multiselect__value--nodata': !valueNames
                }"
            >{{ valueNames || placeholder }}</span>
            <jacp-icon
                class="jacp-multiselect__header__icon"
                :size="12"
                :name="dropdownState ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            />
            <jacp-icon
                class="jacp-multiselect__header__closeicon"
                :size="12"
                @click.native.stop="clearValue"
                name="el-icon-circle-close"
            />
        </div>
        <transition name="el-zoom-in-top">
            <div
                class="jacp-multiselect__body el-select-dropdown is-multiple el-select-dropdown__list"
                v-show="dropdownState"
            >
                <div
                    v-for="item in data"
                    :key="item.id"
                    class="jacp-multiselect__item el-select-dropdown__item"
                    :class="{'jacp-multiselect__item--selected': selected.includes(item.id)}"
                    @click="toggle(item)"
                >
                    {{ item.name }}
                </div>
            </div>
        </transition>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'JacpMultiselect',
    props: {
        placeholder: {
            type: String,
            default: '请选择',
        },
        clearable: {
            type: Boolean,
            default: true,
        },
        value: {
            type: Array,
            default: () => [],
        },
        data: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            dropdownState: false,
            selected: this.value,
        };
    },
    methods: {
        clearValue() {
            this.selected = [];
            this.$emit('input', this.selected);
        },
        closeDropdown() {
            if (this.dropdownState) {
                this.toggleDropdown();
            }
        },
        toggleDropdown() {
            this.dropdownState = !this.dropdownState;
            if (this.dropdownState) {
                this.$emit('open');
                document.body.addEventListener('click', this.closeDropdown, false);
            } else {
                this.$emit('close');
                document.body.removeEventListener('click', this.closeDropdown, false);
            }
            this.$emit(this.dropdownState ? 'open' : 'close');
        },
        toggle(item) {
            const i = this.selected.indexOf(item.id);
            if (i < 0) {
                this.selected.push(item.id);
            } else {
                this.selected.splice(i, 1);
            }
            this.$emit('input', this.selected);
        },
    },
    watch: {
        value(n = []) {
            if (n === this.selected) {
                return;
            }
            this.selected = n;
        },
    },
    computed: {
        dataMap() {
            const res = {};
            this.data.forEach((item) => {
                res[item.id] = item;
            });
            return res;
        },
        valueNames() {
            return this.selected.map(id => (this.dataMap[id] || {}).name).join('、');
        },
    },
};
</script>

<style lang="less">
.jacp-multiselect{
    position: relative;
    font-size: 12px;
    & .el-input__inner{
        line-height: 32px;
        height: 32px;
    }
    &__body{
        position: absolute;
        top: 34px;
        width: 100%;
        background-color: #fff;
        max-height: 400px;
        overflow-y: auto;
    }
    &__header__icon,
    &__header__closeicon{
        color: #bfcbd9;
        width: 20px;
        text-align: right;
        margin-top: 2px;
    }
    &__header{
        display: flex;
        align-items: center;

        &__closeicon{
            cursor: pointer;
            display: none;
        }
        &--clearable&--hasvalue:hover &__closeicon{
            display: block;
        }
        &--clearable&--hasvalue:hover &__icon{
            display: none;
        }
    }
    &__item{
        padding-right: 30px;
        &--selected{
            color: #20a0ff;
            background-color: #fff;
            &::after{
                position: absolute;
                right: 10px;
                font-family: element-icons;
                content: "\E6DA";
                font-size: 11px;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }
    }

    &__value{
        flex: 1;
        line-height: 32px;
        &--nodata{
            color: #b4bccc;
        }
    }
    &__header,
    &__item{
        cursor: pointer;
    }
}
</style>
