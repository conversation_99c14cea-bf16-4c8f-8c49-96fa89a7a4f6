<script>

/* TODO: 从项目管理里拷来的，项目管理里的应该可以废掉了 */
export default {
    name: 'TableCell',
    functional: true,
    inheritAttrs: false,
    props: {
        column: { type: Object, default: () => ({}), required: true },
        row: { type: Object, default: () => ({}) },
        col: { type: Object, default: () => ({}) },
        index: { type: [Number, String], default: 0 },
        emptyRender: {
            type: Function, default: h => h('div', '-'),
        },
    },
    render(h, context) {
        const { props } = context;
        const {
            row, col, index, column, emptyRender,
        } = props;

        if (column.cellRender) {
            return column.cellRender(h, {
                row, col, index, column,
            });
        }
        if (column.type === 'index') {
            return column.indexMethod ? column.indexMethod(index) : h('span', index);
        }
        return row[column.prop] ? h('span', row[column.prop]) : emptyRender(h);
    },
};
</script>
