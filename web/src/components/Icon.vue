<template>
    <i
        :class="[scopedClass.cn, name, { active }, { background }]"
        v-bind="$attrs"
        :style="{fontSize: size + 'px'}"
    />
</template>

<script>
export default {
    name: 'Jac<PERSON>I<PERSON>',
    props: {
        name: {
            default: '',
            type: String,
        },
        size: {
            default: 16,
            type: [Number, String],
        },
        active: { type: Boolean, default: false },
        background: { type: Boolean, default: false },
    },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-icon'),
        };
    },
};
</script>

<style lang='less'>
@import '~@/theme/var';
    .jacp-icon{
        display: inline-block;
        color: inherit;
        &.active{
            color: var(--color--secondary--content);
            cursor: pointer;
            &:hover{
                color: @secendColor;
            }
        }
        // 这个直接作用于icon
        &.background{
            transition: all 0.2s;
            color: var(--color--arrow-base);
            border-radius: 4px;
            display: inline-block;
            text-align: center;
            padding: var(--gutter--mini) var(--gutter--mini);
            &:hover{
                background: #F2F5FA;
            }
        }
        // 这个作用于外层容器？？
        &__background{
            color: var(--color--arrow-base);
            width: 24px;
            height: 24px;
            line-height:24px;

            border-radius: 4px;
            display: inline-block;
            text-align: center;
            &:hover{
                background: #F2F5FA;
            }
        }
    }
</style>
