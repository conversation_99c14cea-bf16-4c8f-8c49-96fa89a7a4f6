<template>
    <div
        class="jacp-switch-radio"
        ref="options"
    >
        <div class="radio-btn bg" />
        <div label="teamspace">
            <i
                class="j-schedule"
                :class="{'active': 'teamspace' === value}"
            />
        </div>
        <div label="workload">
            <i
                class="j-workload"
                :class="{'active': 'workload' === value}"
            />
        </div>
    </div>
</template>

<script type="text/javascript">
export default {
    name: 'JacpIconRadio',
    props: {
        value: {
            type: [String, Number],
        },
    },
    mounted() {
        const radio = this.$refs.options;
        radio.querySelectorAll('div').forEach((item) => {
            item.addEventListener('click', (e) => {
                radio.querySelectorAll('.radio-btn').forEach((el) => {
                    const curRadio = el;
                    curRadio.style.color = '#333';
                });
                const target = e.currentTarget;
                target.style.color = '#2695f1';
                radio.querySelector('.radio-btn.bg').style.left = `${target.offsetLeft - 2}px`;
                this.$nextTick(() => {
                    this.$emit('change', target.getAttribute('label'));
                });
            }, false);
        });
    },
    methods: {
    },
};
</script>

<style lang="less">
.jacp-switch-radio{
    position: relative;
    background-color: #f5f5f5;
    display: flex;
    width: 80px;
    height: 28px;
    border-radius: 4px;
    overflow: hidden;
    &>div{
        position: relative;
        flex: 1;
        text-align: center;
        width: 36px;
        height: 24px;
        line-height: 26px;
        z-index: 2;
        margin: 2px;
        display: flex;
        & i{
            margin: auto;
        }
        &.bg{
            left: 0;
            position: absolute;
            background-color: #fff;
            width: 36px;
            height: 24px;
            z-index: 1;
            transition: left .5s;
            border-radius: 4px;
        }
    }
}
</style>
