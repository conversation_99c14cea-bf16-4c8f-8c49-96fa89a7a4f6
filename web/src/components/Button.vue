<template>
    <el-button
        class="jacp-button"
        v-bind="$attrs"
        :size="size"
        :loading="loading"
        @click.prevent="clickHandle"
    >
        <slot />
    </el-button>
</template>
<script type="text/javascript">
export default {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    inheritAttrs: false,
    props: {
        size: {
            type: String,
            default: 'small',
        },
        onClick: {
            type: Function,
        },
    },
    data() {
        return {
            loading: false,
        };
    },
    methods: {
        clickHandle(evt) {
            if (!this.onClick) {
                this.$emit('click');
            } else {
                const res = this.onClick.bind(this.$parent)(evt);
                if (res && res.then) {
                    this.loading = true;
                    res.then(() => {
                        this.loading = false;
                    }).catch((e) => {
                        this.loading = false;
                        throw e;
                    });
                }
            }
        },
    },
};
</script>
