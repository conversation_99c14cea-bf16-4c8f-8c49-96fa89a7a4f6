<template>
    <div
        class="jacp-tags-input__root"
        @click="focus"
    >
        <custom-tag
            :key="item.name"
            v-for="(item, $index) in value"
            class="jacp-tags-input__tag"
            color="#2695F1"
            closable
            @on-close="remove(item, $index)"
        >
            {{ item.name }}
        </custom-tag>
        <el-autocomplete
            class="jacp-tags-input__input"
            v-model="text"
            v-show="value.length < maxCount"
            :fetch-suggestions="filter"
            :placeholder="placeholder"
            @select="add"
            @input="validate(text)"
            @keydown.native.enter="add(text)"
            @keydown.native="pop"
        >
            <template slot-scope="{ item }">
                <li>{{ item.name }}</li>
            </template>
        </el-autocomplete>
        <div
            class="el-form-item__error"
            v-if="errorMsg"
        >
            {{ errorMsg }}
        </div>
    </div>
</template>

<script type="text/javascript">
import { calcLength } from '@/plugins/utils';

function inList(text, arr) {
    return arr.some(item => item.name === text);
}

export default {
    name: 'JacpInputTags',
    props: {
        placeholder: {
            default: 'New Tag',
            type: String,
        },
        value: {
            default: () => [],
            type: Array,
        },
        maxCount: {
            default: Infinity,
            type: Number,
        },
        maxLength: {
            default: Infinity,
            type: Number,
        },
        filter: {
            type: Function,
            default: (str, cb) => {
                cb([]);
            },
        },
        itemRender: {
            type: Function,
        },
    },
    data() {
        return {
            text: '',
            errorMsg: '',
        };
    },
    methods: {
        add(text) {
            let textAlias = text;
            if (typeof text === 'string') {
                textAlias = {
                    name: text,
                };
            }
            if (!textAlias.name || inList(textAlias.name, this.value) || this.errorMsg) {
                return;
            }
            this.value.push(textAlias);
            this.text = '';
            this.$emit('input', this.value);
        },
        remove(item, index) {
            this.value.splice(index, 1);
            this.$emit('input', this.value);
        },
        validate(text = '') {
            this.text = text;
            const strLen = calcLength(text);
            if (strLen > this.maxLength) {
                this.errorMsg = `超出${strLen - this.maxLength}个字符`;
            } else {
                this.errorMsg = '';
            }
            this.$emit('error', this.errorMsg);
        },
        pop(e) {
            if (e.keyCode === 8 && !this.text) { // delete键
                this.value.pop();
                this.$emit('input', this.value);
            }
        },
        focus() {
            const self = this;
            this.$nextTick(() => {
                self.$el.querySelector('input').focus();
            });
        },
    },
    computed: {
        hasError() {
            return !!this.errorMsg;
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.jacp-tags-input__root {
    border: 1px solid @formBorderColor;
    padding: 0 10px;
}
.jacp-tags-input__tag{
    margin-right: 5px;
}
.jacp-tags-input__input{
    outline: none;
    font-size: 14px;

    & input{
        border: 0;
        padding-left: 0;
    }
}
</style>
