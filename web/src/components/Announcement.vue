<template>
    <div
        :class="{
            'jacp-announcement': true,
            'slide-in': mini,
            'slide-out': !mini,
            'is-expired': isExpired,
        }"
    >
        <div
            ref="arrow"
            class="arrow test123"
            @click="toggleVisible"
            :draggable="!mini"
            :style="arrowPosition"
            @dragend="changePosition"
            @dragstart="signPositions"
        >
            <strong><i :class="{'el-icon-arrow-right' : mini , 'el-icon-arrow-left': !mini }" /></strong>
            <span v-show="!mini">{{ title }}</span>
        </div>
        <el-card
            shadow="hover"
        >
            <div
                slot="header"
                class="clearfix"
            >
                <span>{{ title }}</span>
                <i
                    class="el-icon-arrow-right"
                    style="float: right; line-height: 15px;"
                    @click="toggleVisible"
                />
            </div>
            <slot>
                <div>
                    <p>各位行云小伙伴们，为在春节假期服务好线上业务，行云团队安排了节假日值班，对于产品相关问题可以优先电话联系值班人员，值班表如下：</p>
                    <el-table
                        :show-header="false"
                        :data="list"
                        style="max-height: 60vh;overflow: auto;"
                    >
                        <el-table-column
                            width="140"
                            prop="module"
                        />
                        <el-table-column prop="users">
                            <template slot-scope="scope">
                                <div
                                    v-for="user in scope.row.users"
                                    :key="user.erp"
                                >
                                    <jacp-erp
                                        :data="user"
                                    />
                                    <span>{{ user.phone }}</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </slot>
        </el-card>
    </div>
</template>

<script>

const checkExpired = expiredTime => Date.now() > new Date(expiredTime).getTime();
export default {
    name: 'JacpAnnouncement',
    props: {
        title: { type: String, default: '系统公告' },
        expiredTime: { type: [Date, String, Number], default: '' },
        minimized: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            show: true,
            arrawMovedPosition: null,
            timer: null,
            isExpired: false,
            list: [],
        };
    },
    created() {
        if (this.expiredTime) {
            this.isExpired = checkExpired(this.expiredTime);
            this.isExpiredTimer = setInterval(() => {
                this.isExpired = checkExpired(this.expiredTime);
                if (this.isExpired) {
                    clearTimeout(this.isExpiredTimer);
                }
            }, 1000 * 60);
        }
        this.timer = setTimeout(this.autoToggle.bind(this), 10000);
    },
    computed: {
        arrowPosition() {
            const defaultPosition = {
                bottom: '20px',
            };
            return this.show ? defaultPosition : (this.arrawMovedPosition || defaultPosition);
        },
        mini() {
            return this.minimized !== this.show;
        },
    },
    methods: {
        autoToggle() {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            if (this.mini) {
                this.show = !this.show;
            }
        },
        toggleVisible() {
            if (this.timer) {
                clearTimeout(this.timer);
            }
            if (this.expiredTime) {
                this.isExpired = checkExpired(this.expiredTime);
                this.show = this.isExpired ? false : !this.show;
            } else {
                this.show = !this.show;
            }
        },
        signPositions() {
            this.currenoffsetTop = this.$refs.arrow.offsetTop;
        },
        changePosition({ offsetY, toElement }) {
            const { offsetParent } = toElement;
            let targetPosition = this.currenoffsetTop + offsetY;
            if (targetPosition + offsetParent.offsetTop < 0) {
                targetPosition = -offsetParent.offsetTop;
            }
            if (targetPosition + 20 > offsetParent.offsetHeight) {
                targetPosition = offsetParent.offsetHeight - 20;
            }
            this.arrawMovedPosition = {
                top: `${targetPosition}px`,
            };
        },
    },
    watch: {
        minimized(val) {
            this.show = !val;
        },
    },
    destroyed() {
        clearTimeout(this.timer);
        clearInterval(this.isExpiredTimer);
    },
};
</script>
<style lang="less" scoped>
@import '~@/theme/var.less';
.is-expired{
    display: none;
}
.jacp-announcement,
.arrow,
.arrow span{
    transition: all .3s ease-in-out;
}
.jacp-announcement{
    position: fixed;
    right: 0;
    bottom: 10px;
    width: 420px;
    z-index: 11;
    font-size: 12px;
    p{
        line-height: 1.5;
        margin: 0;
    }
}
.arrow{
    position: absolute;
    left: -30px;
    bottom: 20px;
    width: 30px;
    height: 30px;
    background: @primaryColor;
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    color: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 30px;
    overflow: hidden;
    span{
        width: 0;
    }
}
.el-card{
   &__header span{
       font-size: 14px;
       font-weight: 400;
   }
}
.slide-in{
    transform: translateX(0px);
}
.slide-out{
    transform: translateX(100%);
    & .arrow{
        width: 80px;
        left: -80px;
        span{
            width: max-content;
        }
    }
}
</style>
