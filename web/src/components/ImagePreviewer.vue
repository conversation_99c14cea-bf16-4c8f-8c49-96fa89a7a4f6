<template>
    <div class="imgae-previewer el-upload-list el-upload-list--picture-card">
        <ul class="el-upload-list el-upload-list--picture-card">
            <li
                class="el-upload-list__item is-ready"
                v-for="(url, index) in value"
                :key="`${index}-${url}`"
                :style="imageStyle"
            >
                <el-image
                    ref="imageRefs"
                    class="el-upload-list__item-thumbnail"
                    :src="url"
                    :preview-src-list="value"
                />
                <span class="el-upload-list__item-actions">
                    <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(index)"
                    >
                        <i class="el-icon-zoom-in" />
                    </span>
                    <span
                        v-if="actions.includes('download')"
                        class="el-upload-list__item-delete"
                        @click="handleDownload(url)"
                    >
                        <i class="el-icon-download" />
                    </span>
                    <span
                        v-if="actions.includes('delete')"
                        class="el-upload-list__item-delete"
                        @click="handleRemove(index)"
                    >
                        <i class="el-icon-delete" />
                    </span>
                </span>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        width: {
            type: [String, Number],
            default: 'auto',
        },
        height: {
            type: [String, Number],
            default: 'auto',
        },
        actions: {
            type: Array,
            default: () => [
                'preview',
                // 'download',
                'delete',
            ],
        },
    },
    data() {
        return {
            imageRefs: [],
        };
    },
    computed: {
        imageStyle() {
            const toValue = val => (typeof val === 'number'
                ? `${val}px`
                : val);
            return {
                width: toValue(this.width),
                height: toValue(this.height),
            };
        },
        previewSrcList() {
            return this.value;
        },
    },
    methods: {
        handlePictureCardPreview(index) {
            this.$refs.imageRefs[index].clickHandler();
        },
        handleDownload() {},
        handleRemove(index) {
            // TODO: avoid mutate prop
            this.value.splice(index, 1);
            this.$emit('delete', index);
        },
    },
};
</script>

<style lang="less">

</style>
