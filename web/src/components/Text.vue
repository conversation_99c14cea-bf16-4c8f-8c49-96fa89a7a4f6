<template>
    <component
        v-bind="$attrs"
        :is="tag"
        :style="style"
        :class="{
            'jacp-text--active': active,
            'jacp-text--withicon': !!icon
        }"
    >
        <slot
            name="icon"
            v-if="icon"
        >
            <jacp-icon
                :name="icon"
                v-bind="iconProps"
            />
        </slot>
        <slot>{{ text }}</slot>
    </component>
</template>
<script>
import { fontColorMap } from '@/plugins/utils.theme';

export default {
    name: 'JacpText',
    inheritAttrs: false,
    props: {
        tag: { type: String, default: 'span' },
        text: { type: [String, Number] },
        size: { type: [Number, String], default: 14 },
        type: {
            type: String,
            default: 'default',
            validator(val) {
                return [
                    'primary',
                    'default',
                    'secend',
                    'disable',
                    'error',
                    'warning',
                    'required',
                ].includes(val);
            },
        },
        unit: { type: String, default: 'px' },
        fontWeight: { type: [String, Number], default: 'inherit' },
        active: { type: Boolean, default: false },
        activeColor: { type: String, default: '#2695F1' },
        icon: { type: String, default: '' },
        iconProps: { type: Object },
    },
    computed: {
        style() {
            const {
                size, unit, type, fontWeight, activeColor, active,
            } = this;
            const style = {
                fontSize: `${size}${unit}`,
                color: type in fontColorMap ? fontColorMap[type] : fontColorMap.default,
                fontWeight,
            };
            return active ? {
                ...style,
                '--active-color': activeColor,
            } : style;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-text--active{
    cursor: pointer;
    &:hover{
        color: var(--active-color)!important;
    }
}
.jacp-text--withicon{
    .jacp-icon{
        color: var(--color--secondary--content);
    }
    &:hover{
        .jacp-icon{
            color: var(--active-color);
        }
    }
}
</style>
