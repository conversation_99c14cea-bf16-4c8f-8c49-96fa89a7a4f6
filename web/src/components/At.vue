<template>
    <div class="jacp-at__wrapper">
        <div
            v-show="suggestionVisible"
            ref="popupEl"
            :class="['jacp-at__popup',
                     'el-select-dropdown__wrap',
                     'el-select-dropdown el-popper',
                     popperClass]"
            :style="popupStyle"
        >
            <ul class="jacp-at__suggestions el-select-dropdown__list">
                <li
                    v-for="(item, index) in suggestions"
                    :ref="`suggestionsitemEl_${index}`"
                    :class="['jacp-at__suggestionsitem',
                             'el-select-dropdown__item',
                             { 'current': current === index }]"
                    :key="index"
                    :data-index="index"
                    @click="handleSelected"
                >
                    <slot
                        name="suggestion"
                        :item="item"
                    >
                        <p v-text="item.value" />
                        <p
                            v-text="item.orgTierName || item.orgName"
                            style="color: #ccc"
                        />
                    </slot>
                </li>
            </ul>
        </div>
        <el-input
            ref="inputEl"
            v-focus="autoFocus"
            :disabled="disabled"
            type="textarea"
            :autosize="autosize"
            :rows="textareaRows"
            :placeholder="placeholder"
            :value="value"
            :resize="resize"
            @blur="handleBlur"
            @input="handleInput"
            @focus="handleFocus"
            @keydown.native="handleKeydown"
        />
        <slot name="button">
            <jacp-button
                ref="buttonEl"
                class="jacp-at__button"
                :style="{
                    height: buttonHeight,
                }"
                v-if="showSubmit"
                type="primary"
                @click="$emit('on-submit', value)"
            >
                {{ $t('jacp.button.add') }}
            </jacp-button>
        </slot>
    </div>
</template>
<script>
import debounce from 'lodash/debounce';
import trim from 'lodash/trim';
import getCaretCoordinates from 'textarea-caret';
import { closest } from '@/plugins/utils';

function execHeight(el, target) {
    const style = target.getBoundingClientRect();
    return `${style.height}px`;
}
export default {
    // 支持@某人(erp)的input
    name: 'JacpAt',
    props: {
        value: String,
        at: {
            type: String,
            default: '@',
        },
        fetchSuggestions: {
            type: Function,
            required: true,
        },
        popperClass: String,
        placeholder: {
            type: String,
            default: '填写备注后可@人发消息哦，试一下吧！',
        },
        disabled: Boolean,
        textareaRows: {
            type: Number,
            default: 3,
        },
        autosize: [Boolean, Object],
        resize: {
            default: 'both',
            type: String,
        },
        showSubmit: Boolean,
        autoFocus: {
            type: Boolean,
            default: false,
        },
    },
    directives: {
        focus: {
            componentUpdated(el, binding) {
                if (binding.value) {
                    const input = el.querySelector('textarea');
                    if (input) {
                        input.focus();
                    }
                }
            },
        },
    },
    data() {
        return {
            // value 必须要有，用做回填值的
            suggestions: [],
            maxSuggestionsLength: 20, // 最多给推荐20个
            debouncedGetData: null,
            debounceInterval: 500,
            current: 0,
            isActived: true,
            popupStyle: {},
            popperObserver: null,
            keyWord: '',
            buttonHeight: '30px',
        };
    },
    mounted() {
        const observer = new MutationObserver(this.getPopupStyle);
        observer.observe(this.$refs.popupEl, {
            childList: true,
            subtree: true,
        });
        this.popperObserver = observer;
        this.debouncedGetData = debounce(this.getSuggestionsData.bind(this), this.debounceInterval);
    },
    beforeDestroy() {
        this.popperObserver.disconnect();
    },
    methods: {
        handleFocus() {
            this.isActived = true;
        },
        handleBlur() {
            // this.isActived = false;
        },
        handleInput(val) {
            // 输入at的时候要开始获取输入建议，建议从@开始到光标结束或者下一个@的位置为关键词进行搜索
            const currentStr = val.slice(0, val.length);
            this.$emit('input', val);
            if (!val.includes(this.at)) {
                this.resetSuggestions();
                return;
            }
            const { before, after } = this.getClosestAtPosition();
            if (before !== -1) {
                const prev = val[before - 1];
                // 以空格作为分割中间@的关键字
                const reg = /@([\w+\u4e00-\u9fa5]+)\([\w]+\)/g;
                const targetStr = trim(currentStr.slice(before, after || val.length));
                const matched = targetStr.match(/@([\w+\u4e00-\u9fa5]+)/);
                // console.log(reg.exec(targetStr));
                if (reg.exec(targetStr) && reg.lastIndex !== 0) {
                    this.suggestions = [];
                    return;
                }
                this.keyWord = matched ? matched[1] : '';
                // 过滤邮箱的情况
                if (/^[a-z0-9]$/i.test(prev)) {
                    return;
                }
                this.debouncedGetData(this.keyWord);
            }
        },
        handleKeydown(ev) {
            if (!this.suggestionVisible) {
                return;
            }
            // esc 关闭Suggestions
            const keyCode = ev.keyCode || ev.which || ev.charCode;
            switch (keyCode) {
            case 38:
            case 40:
                // console.log('up dwon');
                if (!(ev.metaKey || ev.ctrlKey)) { // ↑/↓
                    ev.preventDefault();
                    ev.stopPropagation();
                    this.handleMovingCursor(keyCode === 38);
                }
                break;
            case 13:
                this.insertCurrentItem();
                ev.preventDefault();
                ev.stopPropagation();
                break;
            case 27:
                this.resetSuggestions();
                break;
            default:
                break;
            }
        },
        // 选中，支持鼠标选中
        handleSelected(ev, index) {
            if (typeof index === 'undefined') {
                /* eslint-disable no-param-reassign */
                const targetEl = closest(ev.target, el => el.getAttribute('data-index'));
                index = +targetEl.getAttribute('data-index');
            }
            this.current = index;
            this.insertCurrentItem();
        },
        //  或者 键盘上下移动然后回车选中
        handleMovingCursor(isUp) {
            const offset = isUp ? -1 : 1;
            const current = this.current + offset;
            // console.log(current);
            if (current >= this.suggestions.length) {
                this.current = 0;
            } else if (current < 0) {
                this.current = this.suggestions.length - 1;
            } else {
                this.current = current;
            }
            // 把current scrollIntoView
            this.$nextTick(debounce(function scrollIntoView() {
                const [curEl] = this.$refs[`suggestionsitemEl_${this.current}`];
                if (curEl) {
                    curEl.scrollIntoView({ behavior: 'smooth', block: 'end' });
                }
            }, 100));
        },
        insertCurrentItem() {
            const [currentItem] = this.suggestions.filter((item, index) => this.current === index);
            if (currentItem && currentItem.value) {
                this.insertValue(currentItem.value);
            }
        },
        insertValue(val) {
            // 从距离最近的at开始截取并且插入，插入后更新选区，emit input
            const { before } = this.getClosestAtPosition();
            this.setInputRangeText({
                replacement: `${val} `,
                start: before + 1,
                end: before + 1 + this.keyWord.length,
            });
            this.resetSuggestions();
        },
        getSuggestionsData(queryString) {
            this.fetchSuggestions(queryString, (suggestions) => {
                const { value } = this.$el.querySelector('textarea');
                if (!value || !value.includes(this.at)) {
                    return;
                }
                if (Array.isArray(suggestions)) {
                    this.suggestions = suggestions.slice(0, this.maxSuggestionsLength);
                } else {
                    throw new Error('[Autocomplete]autocomplete suggestions must be an array');
                }
            });
        },
        // 获取距离当前光标位置最近的两个@
        getClosestAtPosition() {
            const inputEl = this.$el.querySelector('textarea');
            const { selectionEnd, value } = inputEl;
            const textBeforeCursor = value.slice(0, selectionEnd);
            const before = textBeforeCursor.lastIndexOf(this.at);
            const textAfterCursor = value.slice(before + 1, value.length);
            const nextAtPosition = textAfterCursor.indexOf(this.at);
            return {
                before,
                after: nextAtPosition === -1 ? null : nextAtPosition + before + 1,
            };
        },
        getPopupStyle() {
            const inputEl = this.$el.querySelector('textarea');
            const popupEl = this.$el.querySelector('.jacp-at__popup');
            const { top, left } = inputEl.getBoundingClientRect();// textarea相对于屏幕的位置
            // @相对于textarea的位置
            const caret = getCaretCoordinates(inputEl, this.getClosestAtPosition().before);
            const bodyH = document.body.offsetHeight;
            const popupStyle = {
                top: +caret.top + top + 16,
                left: +caret.left + left + 16,
                margin: 10,
            };
            // 高度是否溢出，溢出就需要翻转
            const isNeedFlip = bodyH - popupStyle.top - popupStyle.margin < popupEl.offsetHeight;
            // 弹出框是否超出了输入框，超出就固定紧靠右侧
            const isNeedFixRight = inputEl.offsetWidth - caret.left < popupEl.offsetWidth;
            if (isNeedFlip) {
                Object.assign(popupStyle, {
                    // 内容特别长的时候caret里的top就不能用了，因为会带着scrollHeight
                    // eslint-disable-next-line
                    top: (top - popupEl.offsetHeight - popupStyle.margin) + Math.min(caret.top, inputEl.offsetHeight - caret.height - 16),
                });
            }
            if (isNeedFixRight) {
                this.popupStyle = {
                    position: 'fixed',
                    top: `${popupStyle.top}px`,
                    right: '24px',
                };
                return;
            }
            this.popupStyle = {
                position: 'fixed',
                top: `${popupStyle.top}px`,
                left: `${popupStyle.left}px`,
            };
        },
        setInputRangeText({ replacement, start, end }) {
            const inputEl = this.$el.querySelector('textarea');
            if (replacement) {
                // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setRangeText
                inputEl.setRangeText(replacement, start, end, 'end');
            } else {
                inputEl.setSelectionRange(start, end);
            }
            this.$emit('input', inputEl.value);
            inputEl.focus();// 替换完了文字以后聚焦回来
        },
        resetSuggestions() {
            this.suggestions = [];
            this.current = 0;
            this.keyWord = '';
        },
        execHeight() {
            clearTimeout(this.timer);
            // this.$nextTick(() => {
            this.timer = setTimeout(() => {
                const { inputEl, buttonEl } = this.$refs;
                if (inputEl && buttonEl) {
                    this.buttonHeight = execHeight(buttonEl.$el, inputEl.$el);
                }
            }, 16);
            // });
        },
    },
    computed: {
        suggestionVisible() {
            const { suggestions } = this;
            const isValidData = Array.isArray(suggestions) && suggestions.length > 0;
            return isValidData && this.isActived;
        },
    },
    watch: {
        suggestionVisible: {
            handler(val) {
                if (val) {
                    this.$nextTick(() => {
                        this.getPopupStyle();
                    });
                }
            },
        },
        value: {
            immediate: true,
            handler() {
                if (this.showSubmit) {
                    this.execHeight();
                }
            },
        },
    },
};
</script>
<style lang="less">
.jacp-at__wrapper{
    position: relative;
    font-size: 12px;
    .el-select-dropdown__wrap{
        overflow: auto;
        max-height: 330px;
    }
}
.jacp-at__suggestionsitem{
    height: auto;
    line-height: 1;
    font-size: 12px;
    &.current{
        background-color: darken(#f5f7fa, 2%);
    }
}
.jacp-at__button{
    position: absolute;
    right: 0;
    top: 0;
    transform: translateX(100% - 1px);
}
</style>
