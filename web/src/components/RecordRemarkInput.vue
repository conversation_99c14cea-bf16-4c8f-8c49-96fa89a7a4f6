<template>
    <div
        v-if="visible"
        class="record-remark-input"
        @keyup.ctrl.enter="submit"
    >
        <jacp-user-avatar
            :data="$store.state.user"
            :first-name="!$store.state.user.headImage"
            :size="24"
            avatar
        />
        <jacp-at
            class="record-remark-input__input"
            v-model="innerValue"
            v-on="$listeners"
            resize="none"
            :placeholder="placeholder"
            :auto-focus="visible"
            :autosize="{ minRows: 3, maxRows: 8 }"
            :fetch-suggestions="querySearch"
        >
            <form-button-group
                slot="button"
                @confirm="submit"
                @cancel="hide"
            />
        </jacp-at>
    </div>
</template>
<script>
import UserModel from '@/models/user';

export default {
    // 留言的输入框，用于详情备注
    name: 'RecordRemarkInput',
    inheritAttrs: false,
    props: {
        visible: Boolean,
        value: {
            type: String,
            required: true,
        },
        inputSuggestions: {
            type: Array,
            default: () => [],
        },
        placeholder: { type: String, default: '填写备注后，可@TA发送消息哦，试一下吧！（Ctrl+Enter键快速发送）' },
    },
    data() {
        return {
            innerValue: '',
        };
    },
    methods: {
        hide() {
            this.$emit('update:visible', false);
        },
        submit() {
            if (this.innerValue.trim() !== '') {
                this.$emit('submit', this.innerValue);
                // this.$emit('input', this.innerValue);
            } else {
                // TODO:
                // console.log('备注不能为空！');
            }
        },
        // 添加备注挪到了外面
        querySearch(str, cb = () => {}) {
            if (!str) {
                return cb(this.inputSuggestions);
            }
            // 获取当前需求所有干系人
            // 推荐  提交人、受理人、关注人、当前操作人、需求人
            return UserModel.search(str).then(users => users.map(
                user => Object.assign(user, {
                    value: `${user.name}(${user.erp})`,
                }),
            )).then(cb);
        },
    },
    watch: {
        value: {
            handler(val) {
                if (val !== this.innerValue) {
                    this.innerValue = val;
                }
            },
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.record-remark-input{
    margin-top: 16px;
    margin-left: 24px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid @borderColorSecend;
    overflow: hidden;
    padding-bottom: 16px;
    margin-right: 24px;
    &__input{
        flex-basis: 100%;
        // max-width: 600px;
        margin-left: 16px;
    }
    & .jacp-user-avatar {
        align-self: flex-start;
    }
}
</style>
