<template>
    <el-dialog
        id="j-dialog"
        :title="title"
        :visible.sync="visible"
        @close="close"
        :class="{
            'success-dialog': true,
            'success-dialog--icon': showIcon,
        }"
        custom-class="j-dialog"
    >
        <el-row
            type="flex"
            align="center"
        >
            <div
                class="icon"
                v-if="showIcon"
            >
                <i :class="icon" />
            </div>
            <div class="content">
                <slot>
                    <div v-html="content" />
                </slot>
            </div>
        </el-row>
    </el-dialog>
</template>

<script>
export default {
    name: 'JacpAlert',
    props: {
        title: String,
        content: String,
        type: String,
        showIcon: { type: Boolean, default: true },
    },
    data() {
        return {
            visible: true,
            icon: this.type === 'success' ? 'el-icon-circle-check' : 'el-icon-warning',
        };
    },
    methods: {
        close() {
            this.visible = false;
            this.$el.remove();
            this.$destroy();
        },
    },
};
</script>

<style lang='less' scoped>
@import '../../theme/var';

.success-dialog{
    .icon{
        color: @primaryColor;
        font-size: 60px;
        margin-bottom: 20px;
    }
    &--icon{
        .content{
            margin-left: 70px;
        }
    }
}
</style>
