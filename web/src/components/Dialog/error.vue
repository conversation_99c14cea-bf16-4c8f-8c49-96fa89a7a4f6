<template>
    <el-dialog
        id="j-dialog"
        :title="title"
        :visible.sync="visible"
        class="error-dialog"
        custom-class="j-dialog"
    >
        <el-row
            type="flex"
            align="center"
        >
            <div class="icon">
                <i class="el-icon-circle-close" />
            </div>
            <div class="content">
                <div class="content-item">
                    <slot>
                        {{ content }}
                    </slot>
                </div>
            </div>
        </el-row>
    </el-dialog>
</template>

<script>
export default {
    name: 'JacpError',
    props: {
        title: String,
        content: String,
    },
    data() {
        return {
            visible: true,
        };
    },
    methods: {
        close() {
            this.visible = false;
            this.$el.remove();
            this.$el.$destroy();
        },
    },
};
</script>


<style lang='less' scoped>
.error-dialog{
    .icon{
        color: #FF4949;
        font-size: 60px;
        margin-bottom: 20px;
    }
    .content, h2{
        margin-left: 70px;
    }
    h2 {
        margin-top: -20px;
        margin-bottom: 10px;
    }
    .content-item{
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 5px;
    }
}
</style>
