<template>
    <el-dialog
        id="j-dialog"
        :title="title"
        :visible.sync="visible"
        :width="width"
        @close="close('$cancel')"
        :custom-class="'j-dialog ' + customClass"
        :close-on-click-modal="closeOnClickModal"
    >
        <div v-if="content !== '' && content !== undefined">
            {{ content }}
        </div>
        <slot />
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-checkbox
                v-if="showNoNotice"
                @change="noNotice = $event"
                class="dialog-footer-no_notice"
            >
                下次不再提醒
            </el-checkbox>
            <jacp-button @click="visible = false">
                {{ cancelBtnText || $t('jacp.button.cancelBtn') }}
            </jacp-button>
            <jacp-button
                v-if="confirmDisplay"
                :on-click="success"
                :disabled="disableOk"
                type="primary"
            >
                {{ confirmBtnText || $t('jacp.button.confirmBtn') }}
            </jacp-button>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import isFunction from 'lodash/isFunction';

let defered = {};

export default {
    name: 'JacpConfirm',
    props: {
        title: {
            default: '',
            type: String,
        },
        confirmBtnText: {
            default: '',
            type: String,
        },
        cancelBtnText: {
            default: '',
            type: String,
        },
        customClass: {
            type: String,
            default: '',
        },
        confirmDisplay: {
            type: Boolean,
            default: true,
        },
        content: {
            type: String,
            default: '',
        },
        closeOnClickModal: {
            type: Boolean,
            default: true,
        },
        showNoNotice: {
            ype: Boolean,
            default: false,
        },
        beforeCancel: { type: Function },
        width: { type: String },
    },
    data() {
        return {
            reason: '$cancel',
            visible: false,
            disableOk: false,
            noNotice: false,
            beforeConfirm: () => {},
        };
    },
    mounted() {
        this.$on('update:disable-ok', (val) => {
            this.disableOk = val;
        });
    },
    methods: {
        open(fn) {
            this.beforeConfirm = fn || this.beforeConfirm;
            if ((/^true$/i).test(this.getNoNotice())) {
                return this.success();
            }
            return new Promise((resolve, reject) => {
                defered = {
                    resolve,
                    reject,
                };
                this.visible = true;
            });
        },
        async close() {
            let canClose = true;
            if (isFunction(this.beforeCancel)) {
                const preventClose = await Promise.resolve(this.beforeCancel());
                if (preventClose) {
                    canClose = false;
                }
            }
            if (!canClose) {
                return;
            }
            this.visible = false;
            this.$el.remove();
            this.$nextTick(() => {
                this.$destroy();
            });
            const action = this.reason === 'ok' ? 'resolve' : 'reject';
            defered[action]();
        },
        success() {
            const res = this.beforeConfirm(this);
            const deferFn = (res && res.then) ? res : Promise.resolve(res);
            this.setNoNotice();
            return deferFn.then((val) => {
                this.reason = 'ok';
                if (val !== false) {
                    this.visible = false;
                }
                return false;
            });
        },
        setNoNotice() {
            if (this.showNoNotice) {
                const tag = this.getNoticeTag();
                if (tag && !(/^true$/i).test(localStorage[tag])) {
                    localStorage[tag] = this.noNotice || false;
                }
            }
        },
        getNoticeTag() {
            if (this.showNoNotice) {
                const slot = this.$children[0].$slots.default.filter(o => o.tag);
                if (slot && slot.length > 0) {
                    return `${slot[0].componentOptions.Ctor.options.name || slot[0].tag}_confirm`;
                }
            }
        },
        getNoNotice() {
            return localStorage[this.getNoticeTag()] || 'false';
        },
    },
};
</script>
<style lang="less">
.dialog-footer {
    &-no_notice {
        line-height: var(--height--small);
        float: left;
        color: var(--color--base--border);
    }
}
</style>
