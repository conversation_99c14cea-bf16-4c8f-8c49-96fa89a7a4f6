<template>
    <el-autocomplete
        ref="auto"
        class="jacp-orgs-autocomplete"
        v-model="innerValue"
        v-bind="$attrs"
        v-on="$listeners"
        :fetch-suggestions="loadOrglist"
        :popper-class="!innerValue ? 'org-sugguestion-popper--empty org-sugguestion-popper' : 'org-sugguestion-popper'"
        @select="handleSelect"
    />
</template>
<script>
import { promisefy } from '@/plugins/utils';
import OrgModel from '@/models/org';
import AdminOption from '@/models/adminOption';

function setSuggestion(key, maxLength) {
    return (targetArray, newItem) => {
        let index = -1;
        // 存在么？
        targetArray.every((o, i) => {
            if (o.id === newItem.id) {
                index = i;
                return false;
            }
            return true;
        });
        if (index !== -1) {
            targetArray.splice(index, 1);// 存在？删掉
        }
        if (targetArray.length >= maxLength) {
            // eslint-disable-next-line no-param-reassign
            targetArray.length -= 1;
        }
        targetArray.splice(0, 0, newItem);// 强势插入
        const targetIdArray = (targetArray || []).map(t => t.id || t);
        localStorage.setItem(key, JSON.stringify(targetIdArray));
        return targetIdArray;
    };
}
function getSuggestion(key) {
    let result = localStorage.getItem(key);

    // TODO: jscrypto
    try {
        result = result ? JSON.parse(result) : [];
    } catch (e) {
        result = [];
    }
    return result.map(item => item.id || item);
}

export default {
    name: 'JacpOrgsAutocomplete',
    props: {
        value: { type: String, default: '' },
        filterMethod: { type: Function },
        storageKey: {
            type: String, default: 'jacp_org_suggestion',
        },
        maxStorageCount: { type: Number, default: 10 },
        loadOrgs: {
            type: Function,
            default: AdminOption.getOrgList,
        },
    },
    data() {
        return {
            innerValue: this.value,
            suggestion: [],
            loadOrgsFn: promisefy(this.loadOrgs).bind(this),
        };
    },
    created() {
        this.setSuggestion = setSuggestion(this.storageKey, this.maxStorageCount);
        this.$watch('value', {
            handler(val) {
                if (val !== this.innerValue) {
                    this.innerValue = val;
                }
            },
        });
    },
    methods: {
        // 加载部门列表
        loadOrglist(keyword, callback) {
            if (!keyword) {
                const orgIds = getSuggestion(this.storageKey);
                if (orgIds && orgIds.length > 0) {
                    OrgModel.queryOrganizations(orgIds).then((data) => {
                        this.suggestion = data;
                        callback(data);
                    });
                } else {
                    callback([]);
                }
            } else {
                this.loadOrgsFn({
                    keyWord: keyword.trim().replace(/\s+/g, ','),
                }).then((data = {}) => {
                    if (this.filterMethod) {
                        callback((data.records || []).filter(this.filterMethod));
                    } else {
                        callback(data.records);
                    }
                });
            }
            // 此处有个bug：https://github.com/ElemeFE/element/pull/19174
            this.$refs.auto.activated = true;
        },
        handleSelect(org) {
            this.setSuggestion(this.suggestion, org);
        },
    },
};
</script>
<style lang="less">
.jacp-orgs-autocomplete{
  width: 100%;
}
.org-sugguestion-popper{
  &:before{
    content: '匹配部门';
    display:block;
    line-height: 1;
    font-size: 14px;
    color: #333;
    margin: 24px 0 8px 20px;
    font-weight: bold;
  }
  &--empty{
    &:before{
      content: '常用推荐部门';
    }
  }
}
</style>
