<template>
    <div
        class="el-collapse-item"
        :class="{
            'jacp-collapse-item': true,
            'jacp-collapse-item--flexable': flexable,
            'jacp-collapse-item--border': border,
            'jacp-collapse-item--sticky': shouldStick,
            'is-active': isActive,
            'is-disabled': disabled
        }"
        @click="handleClick"
    >
        <div
            class="jacp-collapse-item__header__wrap"
            role="tab"
            :aria-expanded="isActive"
            :aria-controls="`el-collapse-content-${id}`"
            :aria-describedby="`el-collapse-content-${id}`"
            :style="wrapperStyle"
        >
            <div
                class="el-collapse-item__header"
                @click.stop="handleHeaderClick"
                role="button"
                :id="`el-collapse-head-${id}`"
                :tabindex="disabled ? undefined : 0"
                @keyup.space.enter.stop="handleEnterClick"
                :class="{
                    'jacp-collapse-item__header': true,
                    'focusing': focusing,
                    'is-active': isActive,
                    'is-left': flexable,
                }"
                :style="{
                    width: isActive && flexable ? leftHeaderWidth : 'auto',
                    minWidth: flexable ? leftHeaderWidth : undefined,
                }"
                @focus="handleFocus"
                @blur="focusing = false"
            >
                <i
                    class="el-collapse-item__arrow el-icon-arrow-right"
                    :class="{'is-active': isActive}"
                />
                <slot
                    name="title"
                    :isActive="isActive"
                >
                    {{ title }}
                </slot>
            </div>
        </div>
        <el-collapse-transition>
            <div

                class="el-collapse-item__wrap"
                v-show="isActive"
                role="tabpanel"
                :aria-hidden="!isActive"
                :aria-labelledby="`el-collapse-head-${id}`"
                :id="`el-collapse-content-${id}`"
            >
                <div
                    class="el-collapse-item__content"
                    ref="contentEl"
                >
                    <slot />
                </div>
            </div>
        </el-collapse-transition>
    </div>
</template>
<script>
import debounce from 'lodash/debounce';
import elementResizeDetectorMaker from 'element-resize-detector';

import { CollapseItem } from 'element-ui';

const erd = elementResizeDetectorMaker({
    strategy: 'scroll', // <- For ultra performance.
});
function getElementViewTop(element) {
    let actualTop = element.offsetTop;
    let current = element.offsetParent;
    let elementScrollTop;
    while (current !== null) {
        actualTop += current.offsetTop;
        current = current.offsetParent;
    }
    if (document.compatMode === 'BackCompat') {
        elementScrollTop = document.body.scrollTop;
    } else {
        elementScrollTop = document.documentElement.scrollTop;
    }
    return actualTop - elementScrollTop;
}
export default {
    name: 'JacpCollapseItem',
    extends: CollapseItem,
    props: {
        // 展开以后会变成header居左模式
        flexable: { type: Boolean, default: false },
        leftHeaderWidth: { type: String, default: 'auto' },
        border: { type: Boolean, default: false },
        shouldStick: { type: Boolean, default: false },
        stickTo: { type: String, default: '.el-collapse' },
        stickOffsetTop: { type: Number, default: 0 },
    },
    data() {
        return { wrapperStyle: {} };
    },

    /* computed: {
        headerStyle() {
            if (this.flexable) {
                return this.isActive ?
            }
        },
    }, */
    mounted() {
        if (this.shouldStick && this.flexable) {
            this.stickToContainer = document.querySelector(this.stickTo);
            this.setHeaderWrapStyle = debounce(this.setHeaderWrapStyle.bind(this), 200);
            erd.listenTo(this.$refs.contentEl, this.setHeaderWrapStyle);
            window.addEventListener('resize', this.setHeaderWrapStyle);
        }
    },
    methods: {
        setHeaderWrapStyle() {
            if (this.shouldStick && this.flexable) {
                const container = this.stickToContainer;
                if (!container || !this.$refs.contentEl) return;
                // stickTo容器相对于视口的位置
                const offsetTop = getElementViewTop(container);
                const windowHeight = window.innerHeight;
                const contentHeight = this.$refs.contentEl.scrollHeight;
                this.wrapperStyle = {
                    height: this.isActive ? `${Math.min(windowHeight - offsetTop, contentHeight)}px` : 'auto',
                    top: `${this.stickOffsetTop}px`,
                };
            }
        },
        handleClick() {
            if (!this.flexable) return;
            if (this.isActive) return;
            this.handleHeaderClick();
        },
    },
    beforeDestroy() {
        erd.removeAllListeners(this.$refs.contentEl);
        window.removeEventListener('resize', this.setHeaderWrapStyle);
    },
    watch: {
        isActive(val) {
            if (!val) {
                this.wrapperStyle.height = 'auto';
            } else {
                if (!this.$refs.contentEl || !this.$el) return;
                this.setHeaderWrapStyle();
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-collapse{
    &.el-collapse{
        border: none;
    }
}
.jacp-collapse-item{
    // padding:0 16px;
    background-color: #fff;
    &+&{
          margin-top: 24px;
    }
    /* 复写小三角样式 */
    .el-collapse-item__arrow{
        position: absolute;
        left: 16px;
        color: @primaryColor;
        text-align: center;
        transform-origin: center;
        line-height: 48px;// 否则在safari上line-height=1的时候不生效
    }
    .el-collapse-item__header{
        padding-left: 40px;
        position: relative;
        border-bottom: 0;
        font-size: 14px;
        font-weight: 600;
        &:before{
            content: ' ';
            width: 16px;
            height: 16px;
            background: #F2F5FA;
            border-radius: 4px;
            display: inline-block;
            position: absolute;
            left: 15px;
        }
    }
    .el-collapse-item__wrap{
        border-bottom: 0;
    }
    .el-collapse-item__content{
        padding-bottom: 0;
    }
}

// 带border的collapse item
.jacp-collapse-item--border{
    transition: border .3s;
    &+&{
        position: relative;
        margin-top: 0;
        border-top: 1px solid #EBEEF5;
        &.is-active{
            border-top: 1px solid #dcdfe6;
        }
    }

    &:last-child{
        border-bottom: 1px solid #EBEEF5;
    }
}
.jacp-collapse-item--flexable{
    display: flex;
    .jacp-collapse-item__header__wrap{
        will-change: height;
        // transition: height 0.2s;
    }
    &.jacp-collapse-item:not(.is-active) {
        cursor: pointer;
    }
}
.jacp-collapse-item--sticky{
    position: relative;
    .jacp-collapse-item__header{
        z-index: 1;
    }
    .jacp-collapse-item__header__wrap{
        z-index: 200;
        position: -webkit-sticky;
        position: sticky;
        left: 0px;
        // top: 32px;
        background-color: #fff;
    }
    &.is-active{
        .jacp-collapse-item__header__wrap{
            border-right: 1px solid#EBEEF5;
        }
    }
}

</style>
