<template>
    <!-- 看板外套一层容器，以免erd追加的div会影响grid布局 -->
    <div>
        <draggable
            class="kanban-root"
            tag="ul"
            :style="containerStyle"
            :list="stages"
            v-bind="columnDraggableOption"
        >
            <li
                v-for="(stage, stageIndex) in stages"
                :key="`kanban-stage-${stage[stageIdProp]}`"
                :data-stage-id="stage[stageIdProp]"
                class="kanban-column"
            >
                <span
                    class="kanban-column-header"
                    v-if="headerVisible"
                >
                    <slot
                        :stage="stage"
                        :count="stage.$_data.reduce((result, arr) => {
                            return result += arr.length;
                        }, 0)"
                        name="column-header"
                    >
                        <h2>{{ stage.name }}-{{ stageIndex }}</h2>
                    </slot>
                </span>
                <div
                    class="kanban-inner-wrap"
                    :class="{
                        'kanban-inner-wrap--flexable': calculatedColumnCount > 1,
                    }"
                    @mouseenter="onMouseenterColumn"
                    @mouseleave="onMouseleaveColumn"
                >
                    <!-- 此处需要判断是否是瀑布流 瀑布流的话需要根绝宽度计算子列个数 -->
                    <draggable
                        class="kanban-inner-list"
                        tag="ul"
                        v-for="(innerColumn, index) in columnsData[stage[stageIdProp]]"
                        v-bind="blockDraggableOption"
                        v-model="columnsData[stage[stageIdProp]][index]"
                        :class="{'kanban-inner-list--empty' : !innerColumn.length }"
                        :style="getListStyle(index)"
                        :key="`inner-column-${index}-${stage[stageIdProp]}`"
                        :data-stage-id="stage[stageIdProp]"
                        :data-column-index="index"
                        :move="onMove"
                        @start="onStart"
                        @sort="($event) => onSort($event, columnsData[stage[stageIdProp]])"
                        @end="onEnd"
                        @change="($event) => onChange($event, stage)"
                    >
                        <li
                            class="kanban-block"
                            :class="{
                                'kanban-block--active': activeBlockId === block[blockIdProp],
                            }"
                            v-for="(block, blockIndex) in innerColumn"
                            :style="blockStyle"
                            :key="`${block[blockIdProp]}-${blockIndex}`"
                            :data-block-id="block[blockIdProp]"
                            :data-block-index="blockIndex"
                            :data-real-index="getRealIndexByXY(
                                +index + 1,
                                +blockIndex + 1,
                                columnsData[stage[stageIdProp]].length
                            )"
                        >
                            <!--  <p>
                                {{ getRealIndexByXY(
                                    +index + 1,
                                    +blockIndex + 1,
                                    columnsData[stage[stageIdProp]].length
                                ) }}
                            </p> -->
                            <slot
                                class="kanban-block__inner"
                                :block="block"
                                :stage="stage"
                                name="block"
                            >
                                <div>{{ block[blockIdProp] }}</div>
                            </slot>
                        </li>
                        <div
                            slot="footer"
                            class="kanban-inner-list__footer kanban-block--filter"
                            v-if="$scopedSlots['list-footer']"
                        >
                            <slot
                                name="list-footer"
                                :stage="stage"
                            />
                        </div>
                    </draggable>
                <!-- 单列的元素充满甬道 -->
                </div>
                <div class="kanban-column-footer">
                    <slot
                        name="column-footer"
                        :stage="stage"
                    />
                </div>
            </li>
        </draggable>
    </div>
</template>
<script>
import flattenDeep from 'lodash/flattenDeep';
import elementResizeDetectorMaker from 'element-resize-detector';
import draggable from 'vuedraggable';
import debounce from 'lodash/throttle';
import isFunction from 'lodash/isFunction';
import { closest, onElementScrollToBottom } from '@/plugins/utils';
import LazyLoader from '@/plugins/lazyLoader';
import {
    getFilledArray,
    spliceIntoChunk,
    calcColumnCount,
    getRelatedElement,
    getRealIndexByXY,
} from './util';

const erd = elementResizeDetectorMaker({
    strategy: 'scroll', // <- For ultra performance.
});

/*  [vue-draggable文档](http://www.itxst.com/vue-draggable/tutorial.html)
坑1: the header slot option only works if the header is placed after the draggable elements
https://github.com/SortableJS/Vue.Draggable/issues/776
坑2:   vue-dragable在使用transition-group，在group组间拖拽的时候会出现多个重复的item闪动又消失的情况
https://github.com/SortableJS/Vue.Draggable/issues/749
                    */
const setDropData = (dataTransfer) => { // 覆盖组件默认行为，修正火狐下自动打开标签页搜索的问题
    dataTransfer.setData('Text', '');
};
const DEFAULT_COLUMN_DRAGGABLE_CONFIG = {
    group: 'column',
    scrollSensitivity: 200,
    scrollSpeed: 15,
    disabled: true, // 目前甬道不支持拖动，所以先不开放
    animation: 200,
    setDropData,
    handle: '.kanban-column-header',
    ghostClass: 'kanban-column-ghost',
};
const DEFAULT_BLOCK_DRAGGABLE_CONFIG = {
    animation: 200,
    group: 'block',
    scroll: true,
    bubbleScroll: true,
    scrollSensitivity: 200,
    scrollSpeed: 10,
    fallbackOnBody: true, // 解决ghost元素乱飞问题
    forceFallback: true, // 如果想要使用vue-draggable的自动滚动，就不能使用prefect-scrollbar，否则触发不了自动滚动。但是不用scrollbar的话，那滚动条在windows下就很难看了
    setDropData,
    handle: '.kanban-block',
    filter: '.kanban-block--filter',
    fallbackClass: 'kanban-block-fallback',
    ghostClass: 'kanban-block-ghost',
    chosenClass: 'kanban-block--chosen', // Class name for the chosen item
    dragClass: 'kanban-block--dragging', // Class name for the dragging item
};


// 分批加载的最大值，超过该值则启用触底加载
const MAX_COLUMN_SIZE = 100;
export default {
    name: 'KanbanBoard',
    components: { draggable },
    props: {
        stages: {
            type: Array,
            default: () => [],
        },
        stageIdProp: {
            type: String,
            default: 'id',
        },
        blocks: {
            type: Array,
            default: () => [],
        },
        blockStatusProp: {
            type: String,
            default: 'status',
        },
        blockIdProp: {
            type: String,
            default: 'id',
        },
        sortFn: {
            type: Function,
            default: (x, y) => x.index - y.index,
        },
        blocksFilterFn: {
            type: Function,
        },

        /* 目前没用 */
        columnDraggableOption: {
            type: Object,
            default: () => DEFAULT_COLUMN_DRAGGABLE_CONFIG,
        },
        draggableOption: {
            type: Object,
            default: () => ({}),
        },
        // 是否要瀑布模式
        enableMasonry: {
            type: Boolean,
            default: true,
        },
        margin: {
            type: [Object, Number],
            default: 12,
        },
        columns: {
            type: Number,
            default: 1,
        },
        blockWidth: {
            type: Number,
            default: 168,
        },
        // 当前激活的block的id
        activeBlockId: {
            default: null,
            type: [String, Number],
        },
        disabled: {
            type: Boolean,
        },
        headerVisible: { type: Boolean, default: true },
        needLazy: { type: Boolean, default: true },
    },
    data() {
        return {
            dragging: false,
            columnsData: {},
            calculatedColumnCount: this.columns,
            lazy: false,
        };
    },
    created() {
        this.fireLazyLoader = debounce(this.fireLazyLoader.bind(this), 200);
        this.initInnerColums = debounce(this.initInnerColums.bind(this), 200);
    },
    mounted() {
        this.rootContainer = this.$el.querySelector('.kanban-root');
        this.initListener();
    },
    computed: {
        blockDraggableOption() {
            let { filter: customFilter } = this.draggableOption;
            if (customFilter) {
                const filters = customFilter.slice(',');
                if (Array.isArray(filters) && !filters.includes('.kanban-block--filter')) {
                    filters.push('.kanban-block--filter');
                    customFilter = filters.join(',');
                }
            }
            return {
                ...DEFAULT_BLOCK_DRAGGABLE_CONFIG,
                ...this.draggableOption,
                filter: customFilter,
                disabled: this.disabled,
            };
        },
        data() {
            const { stages, blocks, calculatedColumnCount } = this;
            return { stages, blocks, calculatedColumnCount };
        },
        // FIXME: 这里样式处理的有点生硬
        blockStyle() {
            const {
                enableMasonry, calculatedColumnCount,
            } = this;
            const widthStyle = enableMasonry || calculatedColumnCount > 1 ? {
                maxWidth: `${this.blockWidth}px`,
                minWidth: `${this.blockWidth}px`,

            } : {
                width: `${this.blockWidth}px`,
            };
            return {
                ...widthStyle,
                marginTop: `${this.margin}px`,
                marginBottom: `${this.margin}px`,
            };
        },
        listStyle() {
            const { enableMasonry, margin, blockWidth } = this;
            return enableMasonry ? {
                marginRight: `${margin}px`,
                minWidth: `${blockWidth}px`,
            } : {
                flexBasis: '100%',
            };
        },
        containerStyle() {
            return {
                gridTemplateColumns: `repeat(${this.stages.length}, 1fr)`,
            };
        },
    },
    watch: {
        data: {
            handler() {
                this.initColumnsData();
            },
            immediate: true,
        },
    },
    methods: {
        // Public methods
        scrollToTop() {
            const rootContainer = this.$el.querySelector('.kanban-root');
            rootContainer.scrollTop = 0;
        },
        // Private methods
        initColumnsData() {
            const {
                stages = [],
                stageIdProp,
                getBlocks,
                $set,
            } = this;
            this.lazy = false;
            stages.forEach((stage, index) => {
                const blockGroups = getBlocks(stage);
                const loadersList = {};
                $set(this.stages[index], '$_data', blockGroups);
                const updateGroups = blockGroups.map((group, groupIndex) => {
                    if (group.length > MAX_COLUMN_SIZE && this.needLazy) {
                        this.lazy = true;
                        const realGroup = group;
                        const lazyLoader = new LazyLoader({ originData: realGroup });
                        lazyLoader.load();
                        loadersList[groupIndex] = lazyLoader;
                        // console.log('need loader:', lazyLoader);
                        return lazyLoader.loadedData;
                    }
                    return group;
                });
                $set(this.stages[index], '$_lazyLoader', loadersList);
                // TODO: 根据某个字段进行初始排序的
                $set(this.columnsData, stage[stageIdProp], updateGroups);
            });
            this.$nextTick(() => {
                this.$emit('initialized');
            });
        },
        initListener() {
            if (this.enableMasonry) {
                erd.listenTo(this.$el, this.initInnerColums);
            }
            if (this.needLazy) {
                onElementScrollToBottom({
                    el: this.rootContainer,
                    distance: 300,
                    callback: this.fireLazyLoader,
                });
            }
        },
        initInnerColums() {
            this.calculatedColumnCount = calcColumnCount(
                this.rootContainer,
                this.stages.length,
                this.blockWidth,
                this.margin,
            );
            this.$emit('update:columns', this.calculatedColumnCount);
        },
        fireLazyLoader() {
            if (!this.lazy) return;
            const { columnsData, stageIdProp, $set } = this;
            this.stages.forEach((stage) => {
                if (stage.$_lazyLoader) {
                    const id = stage[stageIdProp];
                    Object.keys(stage.$_lazyLoader).forEach((groupIndex) => {
                        const loader = stage.$_lazyLoader[groupIndex];
                        if (loader.hasMore) {
                            loader.load();
                            $set(columnsData[id], groupIndex, loader.loadedData);
                        } else {
                            delete stage.$_lazyLoader[groupIndex];
                        }
                    });
                }
            });
        },
        // Event methods
        onMouseleaveColumn(e) {
            if (this.disabled) return;
            e.target.classList.remove('kanban-column-ghost');
        },
        onMouseenterColumn(e) {
            if (this.disabled) return;
            if (this.dragging) {
                e.target.classList.add('kanban-column-ghost');
            }
        },
        onStart(evt) {
            this.dragging = true;
            this.$emit('start', evt);
        },
        onMove(evt) {
            this.$emit('move', evt);
            return true;// TODO: xstate should
        },

        onEnd(evt) {
            this.$nextTick(() => {
                this.removeColumnsHighlight();
            });
            this.dragging = false;
            this.$emit('end', evt);
        },
        // 在甬道内拖动
        // TODO: 判断是否落在原始位置了，如果落在原来的位置不应触发change
        onSort(evt, list) {
            if (this.doubleLimit) return;
            this.doubleLimit = true;
            const timeout = setTimeout(() => {
                this.doubleLimit = false;
                clearTimeout(timeout);
            }, 500);

            const { calculatedColumnCount } = this;
            const { to: toEl, item } = evt;
            const { _underlying_vm_: block } = item;
            const containerEl = closest(toEl, '.kanban-inner-wrap');
            const existingElement = containerEl.querySelectorAll('.kanban-block');
            const targetIndex = getRealIndexByXY(+toEl.getAttribute('data-column-index') + 1, evt.newIndex + 1, calculatedColumnCount);
            const boundaryIndex = getRealIndexByXY(+toEl.getAttribute('data-column-index') + 1, existingElement.length, calculatedColumnCount);

            const { prevEl, nextEl } = getRelatedElement({
                evt,
                containerEl,
                targetIndex,
                boundaryIndex,
                existingElement,
                selector: findIndex => `.kanban-block[data-real-index='${findIndex}']`,
            });
            const flatList = flattenDeep(list);
            const prev = prevEl && flatList.find(b => b[this.blockIdProp] === +prevEl.getAttribute('data-block-id'));

            const next = nextEl && flatList.find(b => b[this.blockIdProp] === +nextEl.getAttribute('data-block-id'));

            /* console.log({
                prevEl,
                nextEl,
                list,
                flatList,
                prev,
                next,
            }); */
            this.$emit('sort', evt, {
                block,
                prevEl,
                nextEl,
                prevBlock: prev,
                nextBlock: next,
            });
        },
        onChange(arg = {}, stage = {}) {
            this.$emit('change', { ...arg, stage });
        },
        // Utils methods
        removeColumnsHighlight() {
            const columns = this.$el.querySelectorAll('.kanban-column');
            columns.forEach(el => el.classList.remove('kanban-column-ghost'));
        },
        getListStyle(index) {
            const { listStyle, margin } = this;
            return { ...listStyle, marginLeft: index === 0 ? `${margin}px` : 0 };
        },
        // 如果有列数的话，就返回具体列数的block二维数组。需要在这之前计算columns个数
        getBlocks(stage = {}) {
            const {
                blocks, blocksFilterFn, blockStatusProp, stageIdProp,
                calculatedColumnCount,
            } = this;
            let filteredBlocks;

            if (isFunction(blocksFilterFn)) {
                filteredBlocks = blocksFilterFn(stage, blocks);
            } else {
                filteredBlocks = blocks.filter(block => block[blockStatusProp] === stage[stageIdProp]);
            }
            // 如果是多列的话，
            if (calculatedColumnCount > 1) {
                if (filteredBlocks.length === 0) {
                    // 给列占位，允许拖入
                    return getFilledArray(calculatedColumnCount);
                }
                return spliceIntoChunk(filteredBlocks.sort(this.sortFn), calculatedColumnCount);
            }
            return [filteredBlocks.sort(this.sortFn)];
        },
        getRealIndexByXY,
    },
    beforeDestroy() {
        erd.removeAllListeners(this.$el);
        this.rootContainer.removeEventListener('scroll', this.fireLazyLoader);
        this.rootContainer = null;
    },
};
</script>
<style lang="less">

/* 看板容器 */
.kanban-root {
    ul, li{
        list-style-type: none;
        margin: 0;
        padding: 0;
    }
    // 处理成等高容器
    display: grid;
    overflow: auto;
    // grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: 1fr;
    grid-column-gap: 0;
    grid-row-gap: 0;
}

/* 甬道 */
.kanban-column{
    display: flex;
    flex-direction: column;
    // flex-grow: 1;
    margin: 0;
    position: relative;
    background: #fafbfc;
    border-left: 1px solid #DCDFE6;
    border-right: 1px solid #DCDFE6;
    transition: background .2s;
    &+&{
      border-left: none;
    }

    h2 {
        font-size: 14px;
        line-height: 20px;
        margin: 0;
        text-transform: uppercase;
        font-weight: 600;
    }
    &-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 6px 12px;
        background-color: #fff;
        position: sticky;
        position: -webkit-sticky;
        top: 0;
        z-index: 1;
        min-height: 32px;
        overflow: hidden;
        border-bottom: 1px solid #EBEEF5;
    }
}

/* 甬道内的仿瀑布列的外层容器 */
.kanban-inner-wrap{
    //  display: flex;
    //  align-items:flex-start;
    //  height: 100%;
     // 充满甬道，方便跨甬道拖拽
    flex-basis: 100%;

    &--flexable{
        display: flex;
        align-items:flex-start;
    }
}
.kanban-inner-list{
    height: 100%;
    &--empty{
        // flex-basis: 100%;
        &:after {
            content: " ";
            display: block;
            width: 100%;
        }
    }
}

.kanban-column-ghost{
    // 目前不需要一个底色
    background-color: #EBEEF5;
}

.kanban-block{
    --border-color: #C0C4CC;
    /* Removing position:relative only benefits single-drag-and-sort.
    for multiple dragging and dragging across lists among tons of items, I have to dynamically turn off the animation. */
    // position: relative;
    will-change: transform;
    cursor: pointer;
    // cursor: move;
    // cursor: -webkit-grab;
    box-shadow: 0px 1px 2px 0px rgba(48,49,51,0.16);
    border-radius: 4px;

    &__inner{
        position: relative;
        // 用法： :style="`--border-color: ${stage.color}`"
        overflow: hidden;
        border-bottom-left-radius: 4px;
        border-top-left-radius: 4px;
        &::after{
            content: "";
            display: block;
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 8px;
            transform: translateX(-8px);
            background-color: var(--border-color);
            // 用transform不会触发reflow
            border-bottom-left-radius: 4px;
            border-top-left-radius: 4px;
            transition: transform .2s linear;
        }
    }
    &:hover,
    &--active{

        .kanban-block__inner{
            &::after{
                transform: translateX(0);
            }
        }
    }
    &__inner[border-color]{
        &::after{
            background-color: var(--border-color);
        }
    }
}
// .kanban-block--chosen{}
.kanban-block--dragging{
    opacity: .6;
    cursor: -webkit-grabbing;
    // border: 1px solid #ccc;
    background-color: #fff;
}
.kanban-block-ghost{
    position: relative;
    // 用一个色块代替占位
    &:after{
        content: ' ';
        display: block;
        width: 100%;
        height: 100%;
        background-color: #DCDFE6;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        border-radius: 4px;
    }
}
.kanban-block-fallback{
    position: absolute;
    top: 0;
    left: 0;
    list-style: none;
}
/*
传入color的方法如下：
https://stackoverflow.com/questions/27529374/css-add-color-with-a-data-attribute-attrdata-color-color
You can pass css values from html:

<button style="
    --tooltip-string: 'Ug. Tooltips.';
    --tooltip-color: #f06d06;
    --tooltip-font-size: 11px;
    --tooltip-top: -10px">
  Button
</button>
to css:

button::after {
  content: var(--tooltip-string);
  color: var(--tooltip-color);
  font-size: var(--tooltip-font-size);
} */


</style>
