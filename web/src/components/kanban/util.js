export const getFilledArray = n => Array(n).fill(null).map(() => []);
// 把一组数据按z字排列拆分到n列中
export function spliceIntoChunk(originalArray = [], n) {
    if (n <= 1) {
        return [originalArray];
    }
    const initResult = getFilledArray(n);
    return originalArray.reduce((result, item, index) => {
        const chunkIndex = index % n;
        if (!result[chunkIndex]) {
            result[chunkIndex] = [];
        }
        const target = result[chunkIndex];
        target.push(item);
        return result;
    }, initResult);
}
export function calcColumnCount(container, columnCount, blockWidth, margin) {
    const bounds = container.getBoundingClientRect();
    // 计算单列的宽度
    const singleColumnWidth = Math.floor(+bounds.width / columnCount) - +margin * (columnCount + 1);
    // 计算列个数
    const count = (Math.floor(singleColumnWidth / +blockWidth));
    return count;
}
export function scrollElementToTop(el, position) {
    // 获取当前元素滚动的距离
    let scrollTopDistance = el.scrollTop;

    function smoothScroll() {
        // 如果你要滚到顶部，那么position传过来的就是0，下面这个distance肯定就是负值。
        const distance = position - scrollTopDistance;
        // 每次滚动的距离要不一样，制造一个缓冲效果
        scrollTopDistance += distance / 5;
        // 判断条件
        if (Math.abs(distance) < 1) {
            el.scrollTop += position;
            // window.scrollTo(0, position);
        } else {
            el.scrollTop += scrollTopDistance;
            // window.scrollTo(0, scrollTopDistance);
            requestAnimationFrame(smoothScroll);
        }
    }

    smoothScroll();
}

export function getRealIndexByXY(x, y, column) { // x\y 从1开始c, olumn是列数
    if (y <= 1) {
        return x;
    }
    return (y - 1) * column + x;
}
export function getRelatedElement({
    evt,
    containerEl,
    targetIndex = 0,
    // boundaryIndex = 0,
    existingElement,
    selector = findIndex => `.kanban-block[data-real-index='${findIndex}']`,
}) {
    let prevEl = null;
    let nextEl = null;
    let findIndex;
    let direction = (
        +targetIndex <= existingElement.length / 2
        || targetIndex >= existingElement.length) ? 0 : 1;
    if (evt.newIndex === 0) {
        direction = 1;
    }
    if (direction === 0) {
        // 向前找
        findIndex = targetIndex - 1;
        while (prevEl === null && findIndex > 0) {
            // console.log('向前找：finding....', findIndex);
            prevEl = containerEl.querySelector(selector(findIndex));
            // console.log('向前找：find result....', prevEl);
            if (prevEl === null) {
                findIndex -= 1;
            }
        }
        if (prevEl === null) {
            direction = 1;
        }
    }
    if (direction) {
        // 向后找，单纯的把下一个挤了下去，不需要跨列找
        const { to, newIndex } = evt;
        nextEl = to.children[newIndex + 1];
    }
    return { prevEl, nextEl };
}
export default {
    getFilledArray,
    spliceIntoChunk,
    calcColumnCount,
    scrollElementToTop,
};
