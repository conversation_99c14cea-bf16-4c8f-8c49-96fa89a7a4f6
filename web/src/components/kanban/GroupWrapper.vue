<template>
    <div
        class="group-wrapper"
        ref="container"
    >
        <div class="expend-group">
            <el-button
                v-if="pageGroups.length"
                :icon="`el-icon-arrow-${expend ? 'down' : 'up'}`"
                @click="openAll">{{expend ? '展开' : '收起'}}全部</el-button>
        </div>
        <!-- TODO: header need sticky -->
        <!-- 如果是任务看板的话，不定宽，需要根据外层容器的大小判断状态列宽 -->
        <div
            class="group-wrapper-header"
        >
            <jacp-text
                type="default"
                class="grouplist-header__item"
                :style="{
                    minWidth: `${sidebarWidth + 1}px`
                }"
            >
                <strong>{{ title }}</strong>
            </jacp-text>
            <slot name="header" />
        </div>
        <!-- 分组列表 -->
        <el-collapse
            class="jacp-collapse group-wrapper-content"
            v-bind="$attrs"
            v-on="$listeners"
            v-model="activeNames"
        >
            <jacp-collapse-item
                v-for="group in pageGroups"
                :key="group.id"
                :title="group.name"
                :name="group.id"
                :disabled="!!group.disabled"
                :left-header-width="`${sidebarWidth}px`"
                :stick-offset-top="33"
                border
                should-stick
                flexable
            >
                <template
                    v-slot:title="{isActive}"
                >
                    <slot
                        name="title"
                        :group="group"
                        :isActive="isActive"
                    />
                </template>
                <slot
                    name="content"
                    :group="group"
                />
            </jacp-collapse-item>
        </el-collapse>
    </div>
</template>
<script>
import { onElementScrollToBottom } from '@/plugins/utils';
import LazyLoader from '@/plugins/lazyLoader';

/* 对于Collapse包裹了一层，用于实现fix header */
export default {
    name: 'GroupWrapper',
    props: {
        title: { type: String, default: '' },
        groups: { type: Array, default: () => [] },
        sidebarWidth: { type: [Number], default: 168 },
    },
    data() {
        return {
            pageGroups: [],
            lazyLoader: undefined,
            activeNames: [],
            expend: true,
        };
    },
    beforeDestroy() {
        this.rootContainer.removeEventListener('scroll', this.loadMore);
        this.rootContainer = null;
    },
    mounted() {
        this.expend = true;
        this.rootContainer = this.$el;
        this.initListener();
    },
    methods: {
        openAll() {
            this.activeNames = this.expend ? this.pageGroups.map(item => item.id) : [];
            this.expend = !this.expend;
        },
        initLazyLoader() {
            this.lazyLoader = new LazyLoader({ originData: this.groups, pageSize: 20 });
            this.loadMore();
        },
        initListener() {
            onElementScrollToBottom({
                el: this.rootContainer,
                distance: 100,
                callback: this.loadMore,
            });
        },
        loadMore() {
            this.lazyLoader.load();
            this.pageGroups = this.lazyLoader.loadedData;

            /** 检测没有出现 滚动条 */
            this.$nextTick(() => {
                if (this.pageGroups.length !== this.groups.length
                    && this.rootContainer.scrollHeight <= this.rootContainer.clientHeight) {
                    this.loadMore();
                }
            });
        },
    },
    watch: {
        groups: {
            deep: true,
            handler: 'initLazyLoader',
        },
        activeNames: {
            handler(val) {
                if (val.length === 0) {
                    this.expend = true;
                }
                console.log(this.pageGroups.length, val.length);
                if (val.length === this.pageGroups.length) {
                    this.expend = false;
                }
            },
            deep: true,
            immediate: true,
        }
    },
};
</script>
<style lang="less">
.group-wrapper{
   position: relative;
   height: calc(~"100vh - 176px");
   width: 100%;
   overflow-x: auto;
//    margin-top: 24px;
   display: grid;
   // 保证子容器等宽
    grid-template-columns: 1fr;
    grid-column-gap: 0;
    align-content: start;
   ul, li{
       list-style-type: none;
   }
   .expend-group {
    padding: 6px 15px;
   }
   &-header{
        display: flex;
        position: relative;
        position: -webkit-sticky;
        position: sticky;
        top: 0px;
        z-index: 2;
        border-bottom: 1px solid #EBEEF5;
   }
   .grouplist-header__item{
        &:first-child{
              position: -webkit-sticky;
              position: sticky;
              left: 0px;
              padding-left: 16px;
              border-right: 1px solid #EBEEF5;
        }
        padding-left: 12px;
        white-space: nowrap;
        background-color: #fff;
        font-size: 14px;
        height: 32px;
        line-height: 32px;
   }
   .group-wrapper-content{
     height: 100%;
     z-index: 1;
   }
}
</style>
