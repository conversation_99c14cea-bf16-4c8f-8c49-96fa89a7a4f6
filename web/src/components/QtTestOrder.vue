<template>
    <div class="jacp-test-order__root">
        <div class="jacp-test-order__item">
            <label>提测名称</label>
            <label>提测单号</label>
            <label>提测负责人</label>
            <!-- <label>提测类型</label> -->
            <label>提测时间</label>
            <label>状态</label>
            <!--<label>来源</label>-->
            <label>操作</label>
        </div>
        <div
            class="jacp-test-order__item"
            v-for="data in list"
            :key="data.id"
        >
            <span
                :title="data.recordName"
                class="jacp-test-order__nowrap"
            >
                {{ data.recordName }}
            </span>
            <span>{{ data.recordKey }} </span>
            <span
                :title="data.ownerErpName"
                class="jacp-test-order__nowrap"
            >
                {{ data.ownerErpName }}
            </span>
            <!-- <span>{{ data.recordType ||"--" }}</span> -->
            <span>{{ data.submitTime|jacp-local-time }}</span>
            <span>{{ data.statusName }}</span>
            <!--<span>{{ data.appCode }}</span>-->
            <span>
                <a
                    target="_blank"
                    v-if="data.editUrl"
                    :href="data.editUrl"
                >
                    编辑
                </a>
                <a
                    target="_blank"
                    v-if="data.detailUrl"
                    :href="data.detailUrl"
                >
                    详情
                </a>
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'JacpQtTestOrder',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
    },
    filters: {
        ownerNamesFormatter(testOwners) {
            return (testOwners || []).map(owner => owner.name || owner.erp).join(',');
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.jacp-test-order {
    &__root{
        font-size: 12px;
        line-height: 1.2;
    }
    &__item{
        display: flex;
        margin-bottom: 15px;
        &>label{
            height: 40px;
            background: #FAFAFA;
            color: @fontThirdColor;
            line-height: 40px;
            padding-left: 5px;
        }
        &>label:nth-child(1) {
            flex-grow: 1;
        }
        &>label:nth-child(-2) {
            flex-grow: 1;
        }
        &>span{
            padding-left: 5px;
        }
        &>span:nth-child(1) {
            flex-grow: 1;
        }
        &>*{
            flex: 0.5;
        }
    }
    &__nowrap{
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
        -webkit-box-orient: vertical;
        word-break: break-all;
        width:100%;
    }
}
</style>
