<template>
    <el-dialog
        :visible="show"
        width="680px"
        class="jacp-version"
        :show-close="false"
    >
        <div
            class="jacp-version__title"
            slot="title"
        >
            <i
                class="el-icon-close jacp-version__title__close"
                @click="closeVersion"
            />
            <img src="../assets/images/<EMAIL>">
            <span class="main-title-desc">升级公告</span>
            <span class="title-desc">如您在使用中遇到问题，请咨询：<br> {{ supportGroup }}</span>
        </div>
        <div class="jacp-version__content">
            <span class="jacp-version__content__version">{{ innerValue.name }}</span>
            <span class="jacp-version__content__date">
                {{ innerValue.releaseDate | jacp-local-time('YYYY-MM-DD') }}</span>
            <div class="jacp-version__content__text">
                <span>{{ innerValue.content }}</span>
            </div>
        </div>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-button @click="closeVersion">
                我知道了
            </el-button>
            <el-button
                v-if="innerValue.detailUrl"
                @click="targetNewWindow(innerValue.detailUrl)"
                type="primary"
            >
                查看详情
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
import GlobalSetting from '@/models/globalSetting';

export default {
    name: 'JacpVersion',
    props: {
        lazyLoad: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            show: false,
            innerValue: () => {},
            supportGroup: GlobalSetting.getSupportGroup(),
        };
    },
    created() {
        if (!this.lazyLoad) {
            this.getVersionInfo();
        }
    },
    methods: {
        closeVersion() {
            this.show = false;
        },
        // 父组件调用
        showVersion(data) {
            if (data) {
                this.innerValue = data;
                this.show = true;
                return;
            }
            if (this.innerValue.length === 0) {
                this.getVersionInfo();
                return;
            }
            this.show = true;
        },
        targetNewWindow(url) {
            window.open(url, '_blank');
        },
        getVersionInfo() {
            // GlobalSetting.getVersionInfo().then((info) => {
            //     // 版本信息存在
            //     if (info) {
            //         this.innerValue = info;
            //         const reTime = parseInt(localStorage.version_releaseTime, 10);
            //         if (reTime < info.cTime || Number.isNaN(reTime)) {
            //             localStorage.version_releaseTime = info.cTime;
            //             this.show = true;
            //         }
            //     }
            // });
        },
    },
};
</script>

<style lang="less">
    @import '~@/theme/var';
    .jacp-version{
        &__title {
            & img {
                width: 100%;
            }
            &__close {
                color: #999;
                position: absolute;
                right: 16px;
                top: 17px;
                font-size: 16px;
                cursor: pointer;
            }
            & .main-title-desc {
                font-size: 40px;
                font-weight: bold;
                position: absolute;
                left: 0px;
                top: 0px;
                margin: 77px 0 0 40px;
                color: #164D99;
                line-height: 56px;
            }
            & .title-desc {
                font-size: 16px;
                position: absolute;
                left: 0px;
                top: 0px;
                margin: 138px 0 0 40px;
                color: #164D99;
                line-height: 23px;
            }
        }

        &__content {
            padding: 10px 20px 8px;
            min-height: 99px;
            max-height: 191px;
            overflow: auto;
            &__version {
                color: @fontColor;
                font-weight: bold;
                font-size: 20px;
                line-height: 28px;
            }
            &__date {
                color: @fontSecendColor;
                font-size: 14px;
                line-height: 20px;
                margin: 6px 0 2px 6px;
            }
            &__text {
                color: @fontColor;
                font-size: 14px;
                line-height: 20px;
                white-space: pre-wrap;
                word-break: break-all;
                margin-top: 8px;
            }
        }
        & .el-dialog__header {
            height: inherit;
            padding: 0;
            line-height: inherit;
        }
    }
</style>
