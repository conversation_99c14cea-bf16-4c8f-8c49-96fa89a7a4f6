<template>
    <div class="jacp-input-checkitems-root">
        <div
            class="jacp-input-checkitems__list"
            v-show="value.length"
        >
            <div
                v-for="(item, $index) in value"
                :key="$index"
                class="jacp-input-checkitems__item"
            >
                <el-checkbox
                    v-model="item.$$checked"
                    :disabled="disableCheck"
                    @change="$emit('check', item)"
                />
                <span
                    class="jacp-input-checkitems__item-name"
                    @click="edit($index)"
                >{{ item.name }}</span>
                <jacp-icon
                    class="jacp-input-checkitems__removebtn"
                    name="icon-close2"
                    v-show="!disableEdit"
                    :size="16"
                    @click.native="remove($index)"
                />
            </div>
        </div>
        <jacp-button
            size="small"
            type="text"
            @click.native.prevent.stop="showAndFocusInput"
            :disabled="disableEdit"
            v-show="!showInputBox"
        >
            {{ buttonText }}
        </jacp-button>
        <div
            class="jacp-input-checkitems__form"
            v-show="showInputBox"
        >
            <el-input
                v-model="text"
                :placeholder="placeholder"
                @keydown.native.enter.prevent="addText($event)"
                ref="inputIns"
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 6 }"
                @input="validate"
                class="jacp-input-checkitems__input"
            />
            <br>
            <jacp-button
                size="small"
                type="text"
                :on-click="addText"
            >
                {{ confirmBtnText }}
            </jacp-button>
            <jacp-button
                size="small"
                type="text"
                @click.native.prevent.stop="cancelText"
            >
                {{ cancelBtnText }}
            </jacp-button>
            <span
                class="jacp-input-checkitems__error j-error"
                v-show="errorMsg"
            >{{ errorMsg }}</span>
        </div>
    </div>
</template>

<script type="text/javascript">
import { promisefy } from '@/plugins/utils';

export default {
    name: 'JacpInputCheckitems',
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        buttonText: {
            type: String,
            default: '+',
        },
        placeholder: {
            type: String,
            default: '+',
        },
        validator: {
            type: Function,
            default: () => {},
        },
        disableCheck: {
            type: Boolean,
            default: false,
        },
        onAdd: {
            type: Function,
            default: () => {},
        },
        onEdit: {
            type: Function,
            default: () => {},
        },
        onRemove: {
            type: Function,
            default: () => {},
        },
        disableEdit: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            showInputBox: false,
            editItem: undefined,
            text: '',
            errorMsg: '',
            validateFn: promisefy(this.validator).bind(this),
            addFn: promisefy(this.onAdd).bind(this),
            removeFn: promisefy(this.onRemove).bind(this),
            editFn: promisefy(this.onEdit).bind(this),
        };
    },
    methods: {
        showAndFocusInput() {
            this.showInputBox = true;
            this.$nextTick(() => {
                this.$refs.inputIns.$el.querySelector('textarea').focus();
            });
        },
        edit(i) {
            const item = this.value[i];
            this.showAndFocusInput();
            this.text = item.name;
            this.editItem = item;
        },
        addText() {
            const value = this.text.replace(/\s/ig, '');
            if (!value) {
                return Promise.reject(new Error('no value'));
            }
            return this.validateFn(value).then(() => {
                const newItem = {
                    name: value,
                    id: this.editItem ? this.editItem.id : null,
                    $$checked: false,
                };
                if (this.editItem) {
                    return this.editFn(newItem).then(() => {
                        this.editItem.name = value;
                        this.editItem = null;
                        this.text = '';
                        this.showInputBox = false;
                        this.$nextTick(() => {
                            this.$refs.inputIns.$el.querySelector('textarea').focus();
                        });
                    });
                }
                return this.addFn(newItem).then(() => {
                    this.value.push(newItem);
                    this.text = '';
                    this.$nextTick(() => {
                        this.$refs.inputIns.$el.querySelector('textarea').focus();
                    });
                });
            });
        },
        cancelText() {
            this.showInputBox = false;
            this.editItem = null;
            this.text = '';
        },
        remove(i) {
            const value = this.value[i];
            if (this.editItem && value.id === this.editItem.id) {
                this.editItem = null;
                this.text = '';
                this.showInputBox = false;
            }
            this.value.splice(i, 1);
            return this.removeFn(value).catch(() => {
                this.value.splice(i, 0, value);
            });
        },
        validate() {
            this.validateFn(this.text).then(() => {
                this.errorMsg = '';
            }).catch((msg) => {
                this.errorMsg = msg;
            });
        },
    },
    computed: {
        removeBtnText() {
            return this.$t('jacp.button.removeBtn');
        },
        confirmBtnText() {
            return this.$t('jacp.button.confirmBtn');
        },
        cancelBtnText() {
            return this.$t('jacp.button.cancelBtn');
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.jacp-input-checkitems{
    &-root{
        font-size: 12px;

        & .el-checkbox__label{
            white-space: normal;
            word-break: break-all;
        }
    }
    &__item{
        transition: all .5s cubic-bezier(.55,0,.1,1);
        position: relative;
        line-height: 1.4;
        font-size: 12px;
        color: #666;
        padding: 4px 24px 4px 5px;
        &:hover{
            background-color: #eef1f6;
        }
        & .el-checkbox__label{
            font-size: 12px;
        }
    }
    &__item:hover &__removebtn{
        opacity: 0.7;
    }
    &__item:hover &__removebtn:hover{
        opacity: 1;
    }
    &__item-name{
        margin-left: 5px;
        color: #2695F1;
        cursor: pointer;
    }
    &__removebtn{
        position: absolute;
        right: 5px;
        top: 5px;
        color: #48576a;
        opacity: 0;
        cursor: pointer;
    }
    &__input{
        width: 400px;
        display: inline-block;
    }
    &__form{
        margin-top: 5px;
    }
    &__error{
        margin-left: 10px;
    }
}
</style>
