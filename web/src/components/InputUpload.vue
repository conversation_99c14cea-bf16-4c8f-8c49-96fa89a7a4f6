<template>
    <div>
        <div v-if="readOnlyList.length">
            <upload-list
                :files="readOnlyList"
                :disabled="true"
                class="jacp-input-upload-readonly"
                :handle-preview="fileDownload"
            />
            <hr
                style="height: 1px;
            color: #f5f5f5;
            background-color: #f5f5f5;
            border: none;"
            >
        </div>
        <el-upload
            class="jacp-input-upload"
            :class="{
                'jacp-input-upload-disabled': disabled,
                'jacp-input-upload-showplaceholder': disabled && !value.length
            }"
            :action="uploadUrl"
            :headers="getHeader"
            :on-preview="fileDownload"
            :on-success="fileUploaded"
            :on-error="fileUploadError"
            :on-remove="fileRemoved"
            :before-upload="fileCheck"
            :before-remove="beforeRemove"
            :disabled="disabled"
            :file-list="value.filter(o => !o.readOnly)"
        >
            <el-button
                size="small"
                type="text"
                :disabled="disabled"
            >
                {{ uploadText.button }}
            </el-button>
            <span
                slot="tip"
                class="el-upload__tip"
                v-show="!disabled"
            > ({{ uploadText.tip }})</span>
        </el-upload>
    </div>
</template>

<script type="text/javascript">
import UploadList from 'element-ui/packages/upload/src/upload-list';
import { downloadFile } from '@/plugins/utils';
import Dialog from '@/models/dialog';
import store from '$platform.store';
import { ByteTransform } from '@/filters';

export default {
    name: 'JacpInputUpload',
    components: { UploadList },
    props: {
        value: {
            type: Array,
            default: () => [],
        },
        uploadUrl: {
            type: String,
            default: '/devops-api/space-demand/api/v1/files/upload',
        },
        maxSize: {
            type: Number,
            default: 20 * 1024 * 1024,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        onRemove: {
            type: Function,
        },
    },
    data() {
        return {
            fileList: [],
        };
    },
    methods: {
        fileCheck(file) {
            const fileType = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt',
                'pptx', 'txt', 'log', 'xml', 'vsd', 'vsdx', 'md', 'mpp', 'msg',
                'xmind', 'graffle', 'msg', 'olm', 'eml', 'zip', 'rar', 'gz',
                'csv', '7z', 'rp', 'mov', 'flv', 'avi', 'wvm', 'mp4', 'mpeg',
                'svg', 'png', 'jpg', 'jpeg', 'gif', 'bmp'];
            const type = file.name.split('.').pop();
            if (!fileType.includes(type)) {
                this.$message.error(`文件上传不支持${type}类型`);
                return false;
            }
            if (file.size > this.maxSize) {
                Dialog.error({
                    title: this.uploadText.errorTitle,
                    content: this.uploadText.maxSizeError,
                });
                return false;
            }
            return true;
        },
        fileUploaded(res, file, fileList) {
            if (res.code === 200) {
                const fileInfo = [{
                    url: res.data.uri,
                    name: res.data.fileName,
                    id: res.data.id,
                    readOnly: false,
                }];
                this.$emit('uploaded', fileInfo[0]);
                this.$emit('input', this.value.concat(fileInfo));
            } else {
                store.commit('ajax_error_push', {
                    url: this.uploadUrl,
                    message: res.message || '',
                });
                Dialog.error({
                    title: this.uploadText.errorTitle,
                    content: res.message || '文件格式错误！',
                });
                // 移除出错的文件
                fileList.splice(fileList.indexOf(file), 1);
            }
        },
        fileUploadError(res, file, fileList) {
            const errorInfo = JSON.parse(res.message);
            Dialog.error({
                title: this.uploadText.errorTitle,
                content: errorInfo.message || '文件上传失败！',
            });
            // 移除出错的文件
            fileList.splice(fileList.indexOf(file), 1);
        },
        fileRemoved(file) {
            const doRemoved = () => {
                const index = this.value.indexOf(file);
                this.value.splice(index, 1);
                this.$emit('input', this.value);
            };
            if (typeof this.onRemove === 'function') {
                this.onRemove(file).then(() => {
                    doRemoved();
                }).catch(() => {
                    this.value.push(file);
                    this.$emit('input', this.value);
                });
            } else {
                doRemoved();
            }
        },
        fileDownload(file) {
            this.$emit('preview', file);
            downloadFile(file);
        },
        beforeRemove(file) {
            if (file.readOnly) {
                return false;
            }
            return this.$confirm(`确定移除附件「${file.name}」？`);
        },
    },
    computed: {
        uploadText() {
            const size = ByteTransform.handle(this.maxSize, 'B');
            return {
                button: this.$t('jacp.upload.button'),
                tip: this.$t('jacp.upload.tip', { size }),
                errorTitle: this.$t('jacp.upload.errorTitle'),
                maxSizeError: this.$t('jacp.upload.maxSizeError', { size }),
            };
        },
        readOnlyList() {
            return this.value.filter(o => o.readOnly);
        },
        getHeader() {
            return { Authorization: localStorage.getItem('Authorization') };
        },
    },
    watch: {
        value: {
            immediate: true,
            handler(val) {
                this.fileList = val.filter(o => !o.readOnly);
            },
        },
    },
};
</script>

<style lang="less">
.jacp-input-upload{
    &-showplaceholder:before{
        content: '无附件';
        color: #999;
        font-size: 12px;
    }
    &&-disabled{
        & .el-upload,
        & .el-upload-list__item-status-label{
            display: none;
        }
        & .el-upload-list{
            margin-top: -9px;
        }
    }
    &-readonly{
        cursor: pointer;
    }
}
</style>
