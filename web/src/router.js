/* 平台与业务模块解耦，在main.js中进行组装 */
import VueRouter from 'vue-router';

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) {
        return originalPush.call(this, location, onResolve, onReject);
    }
    return originalPush.call(this, location).catch(err => err);
};
// 解决重复点击路由报错的BUG
const overrideMethods = ['push', 'replace'];
// const { isNavigationFailure, NavigationFailureType } = VueRouter;
// https://github.com/vuejs/vue-router/issues/2963
overrideMethods.forEach((method) => {
    const originalMethod = VueRouter.prototype[method];
    if (!originalMethod) {
        return;
    }
    // https://router.vuejs.org/zh/guide/advanced/navigation-failures.html#检测导航故障
    // eslint-disable-next-line func-names
    VueRouter.prototype[method] = function (location, ...args) {
        const methodResult = originalMethod.call(this, location, ...args);
        if (!methodResult || !methodResult.then) {
            return methodResult;
        }
        return methodResult.catch((failure) => {
            console.warn('[JAGILE]平台导航故障', failure);
            // if (isNavigationFailure(failure, NavigationFailureType.cancelled)) {
            //     console.warn('[JAGILE]平台', failure);
            //     return;
            // }
            // console.trace('[JAGILE]平台路由异常：', failure);
        });
    };
});

const router = new VueRouter({
    mode: 'history',
    routes: [],
    // duplicateNavigationPolicy: 'ignore' // 如果vue-router能加一下这个配置就好了
});
export default router;
