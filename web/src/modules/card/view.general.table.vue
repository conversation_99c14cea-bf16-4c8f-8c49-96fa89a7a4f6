<template>
    <div class="teamspace-cards-table-root">
        <el-row
            type="flex"
            justify="space-between"
            class="j-mgt16 j-mgl24"
        >
            <local-tabs-menu
                ref="tabMenu"
                size="mini"
                :menu="menu"
            />
            <div
                class="j-mgr24"
                style="padding-top:16px"
            >
                <jacp-dropdown-settings
                    style="margin-right: 12px;"
                    label="卡片"
                    :module-key="spaceMode === 1 ? 'space_general' : 'space'"
                    setting-type="list"
                    :space-privilage="spacePrivilage"
                    :archived="archived"
                    :disabled="spaceMode === 1 ? ['import'] : []"
                    @export="exportCards"
                    @import="importCards"
                    @save-displayFields="() => $refs.cardFilter.filterCards()"
                />
                <jacp-icon
                    name="jacp-icon-icon_fullscreen"
                    active
                    @click.native="$emit('full-screen')"
                />
            </div>
        </el-row>
        <!--  <list-header-tabs
            value="card"
            :tabs="entities"
            @input="(name, entity) => $utils.replaceUrl({}, entity.routeName, true)"
        >
            <div
                slot="extra"
                class="j-mgr16"
                style="padding-top:16px"
            >
                <jacp-dropdown-settings
                    style="margin-right: 12px;"
                    label="卡片"
                    :module-key="spaceMode === 1 ? 'space_general' : 'space'"
                    setting-type="list"
                    :space-privilage="spacePrivilage"
                    :archived="archived"
                    :disabled="spaceMode === 1 ? ['import'] : []"
                    @export="exportCards"
                    @import="importCards"
                    @save-displayFields="() => $refs.cardFilter.filterCards()"
                />
                <jacp-icon
                    name="jacp-icon-icon_fullscreen"
                    active
                    @click.native="$emit('full-screen')"
                />
            </div>
        </list-header-tabs> -->
        <cards-filter
            class="teamspace-cards-table__filter"
            ref="cardFilter"
            v-model="filterCondition"
            @filter="filterCard"
            :archived="archived"
            :space-privilage="spacePrivilage"
            :space-mode="spaceMode"
        >
            <div
                slot="extra"
                style="white-space: nowrap;display: flex;align-items: center;"
            >
                <view-switcher :disabled-views="['report']" />
                <entity-creator
                    :visible-actions="['createCard']"
                    :handler-parameters="{ spaceMode: 1 }"
                    class="j-mgl16"
                />
            </div>
        </cards-filter>
        <div class="teamspace-cards-table__list">
            <cards-table
                ref="cardList"
                :data="cardsList"
                :base-data="baseData"
                :org-columns="cardsTableColumns"
                :sortable="sortable"
                :archived="archived"
                :space-privilage="spacePrivilage"
                :editable="spacePrivilage.createCard"
                :plan-id="currentPlanId"
                :sort-condition="sortCondition"
                :batch="batchFlag"
                :batch-list="batchList"
                @sort-change="sortCardList"
                @drag-end="sort"
                @click-title="viewCardDetail"
                @toggle-children="toggleExpandStatus"
                @toggle-expand-all="toggleExpandAll"
                @after-action="cardActionHook"
                @refresh-data="updateData"
            />
            <jacp-pagenation
                class="teamspace-cards-table__pagenation"
                v-show="!batchFlag"
                :total="totalCount"
                :current-page.sync="pageData.currentPage"
                :page-size.sync="pageData.pageSize"
                @update:currentPage="updateData"
            />
            <jacp-list-batch
                :cards-list="cardsList"
                :batch-list="batchList"
                :batch-flag="batchFlag"
                v-show="cardsList.length !== 0
                    && !spacePrivilage.isGuest
                    && (!$store.state.plan
                    || ($store.state.plan
                    && $store.state.plan.activePlan
                    && $store.state.plan.activePlan.archived !== 1))"
                @openBatch="batchFlag = true"
                @closeBatch="closeBatch"
                @checkAll="checkAll"
            >
                <div slot="options">
                    <el-cascader
                        placeholder="操作"
                        v-model="action"
                        style="width: 100px;"
                        size="mini"
                        :show-all-levels="false"
                        :options="options"
                        :props="{value: 'id', label: 'name'}"
                        @change="batchAction"
                    />
                </div>
            </jacp-list-batch>
        </div>
    </div>
</template>

<script type="text/javascript">
import { mapState } from 'vuex';
import { CancelToken } from 'axios';
import fscreen from 'fscreen';
import sortBy from 'lodash/sortBy';
import Dialog from '@/models/dialog';
import { card as CardModel, space as SpaceModel } from '@/models/teamspace';
import PersonalModel from '@/models/personal';
import { clearRepeatInArray, getSpaceId, getSpaceInfo } from '@/plugins/utils';
import { Menu, MenuItem } from '@jacpbiz/menu-business';
import GeneralMode from './models/general';
import cardsFilter from './components/cardsFilter';
import cardsTable from './components/cardsTable.general';
import MixinCardDetail from '$module/mixins/mixin.cardDetail';
import importCard from './dialogs/importCard';
import suspendRemark from './dialogs/suspendRemark';
import { cardSuspendedStatus } from '@/models/config';
import LocalTabsMenu from '@/modules/root/components/tabsMenu';

function removeChildren(row, list) {
    /* eslint-disable no-param-reassign */
    (row.$children || []).forEach((item) => {
        const i = list.indexOf(item);
        item.$expandStatus = false;
        removeChildren(item, list);
        if (i > -1) {
            list.splice(i, 1);
        }
    });
}

function formatCardList(list = [], baseLevel = 0, currentPlanId) {
    if (!list || !list.length) {
        return [];
    }
    list.forEach((cardItem) => {
        const processors = clearRepeatInArray(cardItem.processors || [], 'erp', true);
        Object.assign(cardItem, {
            processors,
            notMatched: cardItem.matched !== 2,
            $children: formatCardList(cardItem.childCardList, baseLevel + 1, currentPlanId),
            $level: baseLevel,
            $vParentId: baseLevel === 0 ? -1 : cardItem.parentCardId,
            hasChild: cardItem.hasChildren,
            processorSummary: processors.map(item => item.name).join(','),
            // 批量操作标识
            // checkedCard: false,
        });
    });
    return list;
}

export default {
    mixins: [MixinCardDetail],
    props: {
        spacePrivilage: {
            type: Object,
            default: () => {},
        },
        archived: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        cardsFilter,
        cardsTable,
        LocalTabsMenu,
    },
    data() {
        const menu = new Menu([
            new MenuItem({
                id: 'card',
                name: '卡片',
            }),
        ]);
        menu.activeById('card');
        return {
            filterCondition: {},
            sortCondition: {
                orderField: 'index',
                orderType: 'asc',
            },
            pageData: {
                currentPage: 1,
                pageSize: 50,
            },
            cardsList: [],
            // 开启全局搜索时暂存列表
            cardsListTemp: [],
            totalCount: 0,
            sourceToken: CancelToken.source(),
            cardsTableColumns: [],
            // 后台返回原数据
            baseData: [],
            batchFlag: false,
            batchList: [],
            options: [{
                id: '0',
                name: '移动卡片',
            }, {
                id: '1',
                name: '添加标签',
            }],
            action: [],
            menu,
        };
    },
    created() {
        if (fscreen.fullscreenElement) {
            fscreen.exitFullscreen();
        }
    },
    methods: {
        sort({
            oldIndex, newIndex, demand: card, data,
        }) {
            const i = data.indexOf(card);
            // 下移，取前一条的index
            const target = oldIndex < newIndex ? data[i - 1] : data[i + 1];
            const targetId = (target || {}).id;
            // 0:目标id之前，1:目标id之后
            const position = oldIndex < newIndex ? 1 : 0;
            return CardModel.updateIndex(card.id, targetId, position).then(() => {
                // 序号重排
                this.reSortIndex(card, data);
            }).catch(this.updateData);
        },
        reSortIndex(card, data) {
            let index = data[0].sortIndex;
            // 从下面推到第一位
            if (index === card.sortIndex) {
                for (let i = 1; i < data.length; i += 1) {
                    if (data[i].parentCardId === -1) {
                        index = data[i].sortIndex;
                        break;
                    }
                }
            }
            // 从第一位推到下面
            if (index > card.sortIndex) {
                index = card.sortIndex;
            }
            data.forEach((d) => {
                if (d.parentCardId === -1) {
                    d.sortIndex = index;
                    this.setChildrenSortIndex(d, index);
                    index += 1;
                }
            });
        },
        setChildrenSortIndex(card, index) {
            card.childCardList.forEach((c) => {
                c.sortIndex = index;
                this.setChildrenSortIndex(c, index);
            });
        },
        cardActionHook({ cmd }) {
            if (cmd === 'remove') {
                this.$emit('plan-changed');
                this.updateData();
            }
        },
        updateData() { // used by parent component
            this.sourceToken.cancel();
            this.sourceToken = CancelToken.source();
            const params = Object.assign({
                cancelToken: this.sourceToken.token,
            }, this.filterCondition, this.pageData, this.sortCondition,
            // 全局查询的时候去掉sprintId字段
            this.$store.state.plan.isGlobalMode ? {} : { sprintId: this.currentPlanId });
            GeneralMode.getGeneralTreeList(params).then((list) => {
                this.totalCount = list.resultCount;
                this.baseData = list.resultList ? list.resultList.slice(0) : [];
                this.cardsList = formatCardList(list.resultList, 0, this.currentPlanId);
                // 清空批量数据
                this.batchList = [];
                [].forEach.call(this.$refs.cardList.$el.querySelectorAll('tbody tr'), (tr) => {
                    const trObj = tr;
                    trObj.className = trObj.className.replace('checkedRow', '');
                });
                if (this.batchFlag) {
                    this.toggleExpandAll(true);
                }
                return this.cardsList;
            }).then(this.expandCards)
                .then(() => PersonalModel.getListDisplayFields('space_general').then(this.setTableColumns))
                .then(() => this.$emit('isGlobalResultMode', this.$store.state.plan.isGlobalMode));
        },
        expandCard(card, cardsList, deep) {
            if (!card.hasChild || card.$expandStatus) {
                return Promise.resolve();
            }
            card.$expandStatus = true;
            return this.loadChildren(card).then(() => {
                const i = cardsList.indexOf(card);
                cardsList.splice(i + 1, 0, ...(card.$children || []));
                if (card.$children && card.$children.length > 0 && deep) {
                    card.$children.forEach((item) => {
                        this.expandCard(item, cardsList, true);
                    });
                }
            }).catch(() => {
                card.$expandStatus = false;
            });
        },
        shrinkCard(card, cardsList) {
            if (!card.$expandStatus) {
                return;
            }
            card.$expandStatus = false;
            removeChildren(card, cardsList);
        },
        toggleExpandStatus(card) {
            if (card.$expandStatus) {
                this.shrinkCard(card, this.cardsList);
            } else {
                this.expandCard(card, this.cardsList);
            }
        },
        expandCards(cardsList) {
            return (cardsList || []).map(card => this.expandCard(card, cardsList));
        },
        toggleExpandAll(value) {
            if (value) {
                this.cardsList.map(card => this.expandCard(card, this.cardsList, true));
            } else {
                this.cardsList.forEach((card) => {
                    if (card.$level === 0 && card.$children && card.$children.length > 0) {
                        this.shrinkCard(card, this.cardsList);
                    }
                });
            }
        },
        pagination(pageNo, pageSize, array) {
            const offset = (pageNo - 1) * pageSize;
            return (offset + pageSize >= array.length)
                ? array.slice(offset, array.length) : array.slice(offset, offset + pageSize);
        },
        filterCard() {
            this.pageData.currentPage = 1;
            this.updateData();
        },
        exportCards(val) {
            const condition = this.$refs.cardFilter.getAdvanceCondition() || {};
            return GeneralMode.exportCards(Object.assign({
                spaceId: getSpaceId(this.$route.params.spaceKey),
                sprintId: -1,
                exportLatitude: val,
            }, this.sortCondition, condition, this.filterCondition));
        },
        importCards() {
            return Dialog.confirm({
                title: '导入卡片',
                slot: importCard,
                confirmDisplay: false,
                cancelBtnText: '关闭',
                slotProps: {
                    spaceId: getSpaceId(this.$route.params.spaceKey),
                    sprintId: this.currentPlanId,
                },
            }).then(() => this.filterCard())
                .catch(() => this.filterCard());
        },
        sortCardList({ orderField, orderType }) {
            if (!orderType) {
                orderField = 'index';
                orderType = 'asc';
            }
            this.sortCondition = Object.assign({}, {
                orderField,
                orderType,
            });
            this.updateData();
        },
        viewCardDetail(card) {
            this.$emit('click-card', Object.assign(card, {
                sprintId: this.currentPlanId,
            }));
            this.$refs.cardList.setActive(card.id);
        },
        loadChildren(card, refresh) {
            if (!refresh && card.hasChild && card.$children.length) {
                return Promise.resolve(card.$children);
            }
            return CardModel.getChildren(card.id, null, { cancelToken: this.sourceToken.token })
                .then((list) => {
                    Object.assign(card, {
                        $children: formatCardList(list, card.$level + 1, this.currentPlanId),
                    });
                    return card.$children;
                });
        },
        setTableColumns(data) {
            this.cardsTableColumns = sortBy(data, 'index');
        },
        resetAndUpdateList() {
            if (this.$refs.cardFilter && this.$refs.cardFilter.filterByDefault) {
                this.$refs.cardFilter.filterByDefault();
            } else {
                this.pageData.currentPage = 1;
                this.totalCount = 0;
                this.cardsList = [];
                this.cardsListTemp = [];
                this.filterCondition = Object.create(null); // 清空查询条件
                this.$store.commit('plan/isGlobalMode_change', false);
                // console.log(this.$store.state.plan.isGlobalMode);
                this.updateData();
            }
        },
        checkAll(flag) {
            this.batchList = [];
            if (flag) {
                this.cardsList.forEach(card => this.batchList.push(card.id));
            }
            [].forEach.call(this.$refs.cardList.$el.querySelectorAll('tbody tr'), (tr) => {
                const trObj = tr;
                if (flag) {
                    if (trObj.className.indexOf('checkedRow') < 0) {
                        trObj.className = `${trObj.className} checkedRow`;
                    }
                } else {
                    trObj.className = trObj.className.replace('checkedRow', '');
                }
            });
        },
        closeBatch() {
            this.batchFlag = false;
            this.batchList = [];
            [].forEach.call(this.$refs.cardList.$el.querySelectorAll('tbody tr'), (tr) => {
                const trObj = tr;
                trObj.className = trObj.className.replace('checkedRow', '');
            });
        },
        initBatchInfo() {
            // 查询标签
            /* SpaceModel.getSpaceTags().then((tagList) => {
                this.options[1].children = this.defaultNoData(tagList);
            }); */
            // 查询迭代列表
            /* PlanModel.getCurrents().then((list) => {
                this.options[2].children = list.concat([{
                    id: -1,
                    name: 'Backlog',
                }]);
            }); */
        },
        defaultNoData(dataList) {
            if (dataList && dataList.length === 0) {
                dataList.push({
                    name: '暂无数据',
                    code: -1,
                    disabled: 'true',
                });
            }
            return dataList;
        },
        batchAction(action) {
            const index = action[0];
            const actionInfo = this.options[index].children.filter(item => item.id === action[1]);
            switch (index) {
            // 移动卡片 修改卡片状态
            case '0':
                this.batchChangeStatus(actionInfo[0]);
                break;
            // 添加标签
            case '1':
                this.batchAddTag(actionInfo[0]);
                break;
            default:
                break;
            }
            this.action = [];
        },
        batchAddTag(tag) {
            if (this.batchList.length < 1) {
                this.$alert('请选择批量操作的卡片！', '提示', {
                    confirmButtonText: '确定',
                });
                return;
            }
            this.$confirm(`此操作将为${this.batchList.length}张卡片添加「${tag.name}」标签, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                SpaceModel.batchAddTag({
                    cardIdList: this.batchList,
                    tagId: tag.id,
                }).then(() => {
                    this.batchFlag = false;
                    this.updateData();
                    this.$notify({
                        title: '保存成功！',
                        type: 'success',
                        duration: 2000,
                    });
                });
            });
        },
        batchChangeStatus(status) {
            if (this.batchList.length < 1) {
                this.$alert('请选择批量操作的卡片！', '提示', {
                    confirmButtonText: '确定',
                });
                return;
            }
            let defer = Promise.resolve();
            if (+status.code === cardSuspendedStatus) {
                defer = new Promise((resolve) => {
                    Dialog.confirm({
                        title: '提示',
                        content: `此操作将移动${this.batchList.length}张卡片到「${status.name}」状态, 是否继续?`,
                        slot: suspendRemark,
                        beforeConfirm: vm => resolve(vm.data.remark),
                    });
                });
            } else {
                defer = new Promise((resolve) => {
                    this.$confirm(`此操作将移动${this.batchList.length}张卡片到「${status.name}」状态, 是否继续?`, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }).then(() => resolve());
                });
            }
            defer.then(res => GeneralMode.batchChangeStatusForGeneral({
                cardIdList: this.batchList,
                cardStatusId: status.code,
                remark: res,
            }).then(() => {
                this.batchFlag = false;
                this.updateData();
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            }));
        },
    },
    computed: {
        ...mapState('chilldteamspace', ['planList', 'tagsList']),
        // 通用空间目前所有卡片迭代为-1
        currentPlanId() {
            return -1;
        },
        sortable() {
            return this.sortCondition.orderField === 'index'
                && this.sortCondition.orderType === 'asc'
                && this.spacePrivilage.createCard
                && !this.$store.state.plan.isGlobalMode
                && !this.batchFlag;
        },
        statusList() {
            return this.$store.state.chilldteamspace.availableStatusList;
        },
        spaceMode() {
            return getSpaceInfo(this.$route.params.spaceKey).mode;
        },
    },
    watch: {
        tagsList: {
            handler(val) {
                this.options[1].children = this.defaultNoData(val.concat([]));
            },
            immediate: true,
        },
        currentPlanId: {
            handler: function updateList() {
                this.resetAndUpdateList();
                // this.initBatchInfo();
                // 批量状态切换到归档迭代关闭批量
                const { plan } = this.$store.state;
                if (plan && plan.activePlan) {
                    this.batchFlag = plan.activePlan.archived !== 1 && this.batchFlag;
                }
            },
            immediate: true,
        },
        // 开启全局搜索的时候先清空列表
        '$store.state.chilldteamspace.availableStatusList': {
            deep: true,
            handler(n) {
                if (n) {
                    // 查询状态列表
                    const types = [];
                    this.statusList.forEach((item) => {
                        types.push({
                            id: item.statusCode,
                            name: item.statusName,
                            code: item.statusCode,
                        });
                    });
                    this.$set(this.options[0], 'children', this.defaultNoData(types));
                }
            },
            immediate: true,
        },
    },
};
</script>
