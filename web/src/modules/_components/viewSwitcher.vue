<template>
    <el-dropdown
        class="jacp-view-switcher"
        trigger="click"
        v-if="!icon"
    >
        <span class="el-dropdown-link">
            {{ listViewType[viewType].name }}
            <i
                class="el-icon-caret-bottom"
                style="margin-left: -4px;"
            />
        </span>
        <el-dropdown-menu
            slot="dropdown"
            :append-to-body="true"
        >
            <el-dropdown-item
                v-for="item in listViewType"
                :key="item.value"
                :disabled="disabledViews.includes(item.value) || disabledViews.includes(item.name)"
                @click.native="switchView(item.value)"
            >
                <div style="white-space: nowrap;">{{ item.name }}</div>
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
    <div
        v-else
        class="jacp-view-switcher-icon"
    >
        <span
            v-for="item in listViewType"
            v-show="!(disabledViews.includes(item.value) && disabledViews.includes(item.name))"
            item
            :key="item.value"
            :active="viewType === item.value"
            :title="`${item.name}视图`"
            @click="() => switchView(item.value)"
        >
            <i :class="item.icon" />
        </span>
    </div>
</template>
<script>
import { getSpaceInfo } from '@/plugins/utils';
import { listViewType } from '@/modules/teamspace/constant';

export default {
    name: 'ViewSwitcher',
    props: {
        disabledViews: {
            type: Array,
            default: () => [],
        },
        icon: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            viewType: 'board',
            listViewType,
        };
    },

    /* created() {
        this.viewType = this.getViewType();
    }, */
    methods: {
        switchView(val) {
            this.viewType = val;
            this.$store.commit('plan/isGlobalMode_change', false);
            this.$router.push({
                path: this.$route.path,
                query: {
                    view: this.viewType,
                },
            });
            localStorage.defaultSpaceView = this.viewType;
        },
        getViewType() {
            // 未传参的通用空间，但缓存值为敏捷空间特有视图时  默认显示看板
            if (!this.$route.query.view && this.spaceMode === 1 && localStorage.defaultSpaceView === 'report') {
                return 'board';
            }
            // 参数优先 其次缓存 默认看板
            return this.$route.query.view || localStorage.defaultSpaceView || 'board';
        },
    },
    watch: {
        viewType: {
            immediate: true,
            handler(val) {
                this.$emit('update:view-type', val);
            },
        },
        '$route.query.view': {
            immediate: true,
            handler() {
                this.viewType = this.getViewType();
            },
        },
    },
    computed: {
        spaceMode() {
            return getSpaceInfo(this.$route.params.spaceKey).mode;
        },
    },
};
</script>
<style lang="less">
@borderRadius: 100px;
.jacp-view-switcher{
    .el-dropdown-link{
        white-space: nowrap;
    }
    &-icon{
        white-space: nowrap;
        padding: 2px;
        background: var(--color--base--table-header);
        border-radius: @borderRadius;
        height: 28px;
        line-height: 24px;
        & [item]{
            display: inline-block;
            text-align: center;
            width: 36px;
            transition: background .3s;
        }
        & [active]{
            background: rgba(255,255,255,1);
            border-radius: @borderRadius;
            box-shadow:  0 2px 4px 0 rgba(48,49,51,0.08);
            i{
                color: var(--color--secondary--content);
            }
        }
    }
}
</style>
