<template>
    <div class="user-filter-root">
        <div class="user-filter__head j-mgt24">
            <slot name="label">
                <jacp-text
                    size="14"
                    tag="label"
                    class="user-filter__label"
                >
                    成员
                </jacp-text>
            </slot>
            <div
                class="user-filter__showAll j-hover-highlight"
                @click="showAll = !showAll"
                v-if="data.length > showCount"
            >
                <span>{{ !showAll ? '展开全部' : '收起' }}</span>
                <i
                    v-if="!showAll"
                    class="el-icon-arrow-down"
                />
                <i
                    v-if="showAll"
                    class="el-icon-arrow-up"
                />
            </div>
        </div>
        <el-input
            placeholder="输入工号/姓名"
            prefix-icon="el-icon-search"
            v-model="keyword"
            class="user-filter__searchuser"
            clearable
        />
        <div
            class="user-filter__users"
            :class="[{'showmore': showAll}]"
        >
            <div
                class="user-filter__user"
                v-for="(user, index) in filteredData"
                :key="`${user[valueProp]}-${index}`"
                @click="() => onSelect(user)"
            >
                <div
                    class="user-filter__user-pic"
                    :title="user[valueProp]"
                >
                    <jacp-user-avatar
                        :class="{ 'user-filter__user-select': value.includes(user[valueProp])}"
                        :data="{
                            headImage: user.headImage,
                            erp: user[valueProp],
                            name: user[nameProp],
                        }"
                        :first-name="!user.headImage"
                        avatar
                    />
                </div>
                <div class="name">
                    {{ user[nameProp] }}
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'UserFilter',
    props: {
        value: { type: Array, default: () => [], required: true },
        data: { type: Array, default: () => [] },
        showCount: { type: Number, default: 10 },
        onFilter: {
            type: Function,
            default: (user, keyword, vm) => (user[vm.nameProp].includes(keyword)
                    || user[vm.valueProp].toLowerCase().includes(keyword.toLowerCase())),
        },
        valueProp: {
            type: String,
            default: 'userErp',
        },
        nameProp: {
            type: String,
            default: 'userName',
        },
    },
    data() {
        return { keyword: '', filteredData: [], showAll: false };
    },
    methods: {
        doFilter() {
            const { keyword } = this;
            if (keyword) {
                this.filteredData = this.data.filter(user => this.onFilter(user, keyword, this));
            } else {
                this.filteredData = this.data.slice();
            }
        },
        onSelect(user) {
            const innerValue = [...this.value];
            const index = innerValue.indexOf(user[this.valueProp]);
            if (index > -1) {
                innerValue.splice(index, 1);
            } else {
                innerValue.push(user[this.valueProp]);
            }
            this.$emit('input', innerValue);
        },
    },
    watch: {
        keyword: {
            handler: 'doFilter',
        },
        data: {
            immediate: true,
            handler() {
                this.filteredData = this.data;
            },
        },
    },
};
</script>
<style lang="less">
.user-filter{
    &__head{
        display: flex;
        justify-content: space-between;
    }
  &__users{
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
        height: 124px;
        overflow: hidden;
        transition: all .5s;
        &.showmore{
            height: auto;
        }
    }
  &__user{
        margin: 8px 16px 4px 0;
        display: flex;
        flex-direction: column;
        &:nth-of-type(5n){
            margin-right: 0px;
        }
        &-pic{
            position: relative;
        }
        &-select{
            &:after{
                display: block;
                content: '';
                position: absolute;
                z-index: 1;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                border: 1px solid #f1f1f1;
                left: 0;
                top: 0;
                // left: 50%;
                transform: translateX(-1px);
                background: url('~@/assets/icons/confirm.png') no-repeat;
                background-size: 100% 100%;
                background-color: #023A7F;
                opacity: .7;
            }
        }
        & .name{
            text-align: center;
            font-size: 12px;
            padding-top: 4px;
            color: #999999;
            white-space:nowrap;
            width: 100%;
        }
    }
    &__searchuser{
        margin-top: 8px;
        & .el-input__inner{
            height: 32px;
        }
    }
     &__showAll{
        font-size: 12px;
        color: #999999;
        cursor: pointer;
    }
}
</style>
