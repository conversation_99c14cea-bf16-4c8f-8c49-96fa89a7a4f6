<template>
    <el-form
        class="card-task-form"
        :model="formData"
        label-width="120px"
        ref="cardTaskForm"
        :rules="rules"
        label-position="top"
    >
        <el-form-item
            label="名称"
            prop="content"
            class="card-task-form__content"
        >
            <el-input
                v-model.trim="formData.content"
                placeholder="请输入任务名称"
            />
        </el-form-item>
        <el-row :gutter="16">
            <el-col :span="12">
                <el-form-item
                    label="处理人"
                    prop="erp"
                >
                    <el-select
                        v-model="formData.erp"
                        placeholder="请选择"
                        filterable
                        size="small"
                    >
                        <el-option
                            v-for="item in members"
                            :key="item.processorErp"
                            :label="item.processorName+'('+item.processorErp+')'"
                            :value="item.processorErp"
                        />
                        <template v-if="!members.length">
                            <el-option
                                v-for="item in memberList"
                                :key="item.erp"
                                :label="item.name+'('+item.erp+')'"
                                :value="item.erp"
                            />
                        </template>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item
                    label="角色职责"
                    prop="roleCode"
                >
                    <el-select
                        v-model="formData.roleCode"
                        filterable
                        clearable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in customizedConfigRole"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="16">
            <el-col :span="12">
                <el-form-item
                    label="开始日期"
                    prop="startDate"
                >
                    <el-date-picker
                        v-model="formData.startDate"
                        type="date"
                        value-format="timestamp"
                        :picker-options="startDateOpt(formData)"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item
                    label="结束日期"
                    prop="deadline"
                >
                    <el-date-picker
                        v-model="formData.deadline"
                        type="date"
                        value-format="timestamp"
                        :picker-options="deadlineOpt(formData)"
                    />
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="16">
            <el-col :span="12">
                <el-form-item
                    label="计划工时(H)"
                    prop="plannedHours"
                >
                    <el-input
                        v-model="formData.plannedHours"
                        placeholder="小时"
                        @change="setRemainHours"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item
                    label="剩余工时(H)"
                    prop="remainingHours"
                >
                    <el-input
                        :disabled="remainHoursDisabled"
                        v-model="formData.remainingHours"
                        placeholder="小时"
                    />
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="16">
            <el-col :span="12">
                <el-form-item
                    label="迭代/Backlog"
                    prop="sprint"
                >
                    <jacp-belong-to-selector
                        style="width: 100%;"
                        v-model="formData.sprint"
                        :data-map="{
                            sprint: sprintList
                        }"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item
                    label="所属卡片"
                    prop="cardId"
                >
                    <el-select
                        placeholder="请选择所属卡片"
                        filterable
                        v-model="formData.cardId"
                        size="small"
                    >
                        <el-option
                            v-for="item in cardList"
                            :key="item.id"
                            :label="item.cardName"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col
                :span="12"
                :offset="12"
                style="padding-left: 8px"
            >
                <jacp-button
                    size="medium"
                    type="text"
                    v-show="!showAddCard && showAddCardBtn"
                    :on-click="() => showAddCard = true"
                >
                    <i class="el-icon-plus">添加新卡片</i>
                </jacp-button>
                <new-card
                    :show-add-card.sync="showAddCard"
                    :sprint-id="formData.sprint"
                    :space-id="spaceId"
                    @newCard="newCard"
                />
            </el-col>
        </el-row>
    </el-form>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import newCard from '@/modules/teamspace/components/schedule/scheduleCardAdd';
import CardModel from '@/models/card';
import mixinCardTask from '@/modules/card/mixins/mixin.cardTask';
import { getSpaceId } from '@/plugins/utils';

export default {
    mixins: [mixinCardTask],
    components: {
        newCard,
    },
    props: {
        cardTaskInfo: {
            type: Object,
            default: () => ({}),
        },
        members: {
            type: Array,
            default: () => [],
        },
        defaultDate: {
            type: Date,
            default: () => new Date(),
        },
        remainHoursDisabled: {
            type: Boolean,
            default: false,
        },
        showAddCardBtn: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            formData: {
                id: undefined,
                erp: '',
                name: '',
                content: '',
                roleCode: undefined,
                startDate: '',
                deadline: '',
                remainingHours: undefined,
                plannedHours: undefined,
                sprint: -1,
                cardId: '',
                status: 1,
            },
            sprintList: [],
            cardList: [],
            showAddCard: false,
            spaceId: undefined,
        };
    },
    computed: {
        ...mapGetters('chilldteamspace', ['memberList']),
        ...mapState('chilldteamspace', {
            customizedConfigRole: 'customizedConfigRoleList',
        }),
    },
    created() {
        this.spaceId = getSpaceId(this.$route.params.spaceKey);
    },
    methods: {
        getDefaultRole(erp, spaceId, initFlag) {
            CardModel.getRoleCodeDefault(erp, spaceId).then((data) => {
                if (data && data.length > 0) {
                    const defaultRole = data[0];
                    if (initFlag) {
                        // 首次加载任务只获取职位id 优先保留原任务数据
                        this.formData.roleCode = this.formData.roleCode || defaultRole[spaceId];
                    } else {
                        // 人员切换导致的二次加载人员职位信息 直接设置查询到的默认职位
                        this.formData.roleCode = defaultRole[spaceId];
                    }
                }
            });
        },
        getPlanList() {
            // 查询迭代列表
            CardModel.getPlanList({
                spaceId: this.spaceId,
                // 查询未归档
                type: 1,
            }).then((data) => {
                this.sprintList = data || [];
                // if (this.formData.sprint !== -1) return;
                // const { length } = this.sprintList;
                // const index = length > 1 ? length - 2 : length - 1;
                // this.formData.sprint = this.sprintList[index] ? this.sprintList[index].id : -1;
                // 加载迭代内卡片列表
                this.getCardList();
            });
        },
        getCardList() {
            // 查询迭代内卡片列表
            CardModel.getCardList({
                spaceId: this.spaceId,
                sprintId: this.formData.sprint || '-1',
            }).then((data) => {
                this.cardList = data || [];
                const isCurSprint = this.cardList
                    .find(card => card.id === this.formData.cardId);
                if (!isCurSprint) {
                    this.formData.cardId = '';
                }
            });
        },
        setRemainHours(plannedHours) {
            if (this.remainHoursDisabled) {
                this.formData.remainingHours = plannedHours;
            }
        },
        resetForm() {
            if (this.cardTaskInfo) {
                Object.assign(this.formData, this.cardTaskInfo);
            }
        },
        clear() {
            this.$refs.cardTaskForm.clearValidate();
        },
        newCard(cardInfo) {
            this.cardList.push(cardInfo);
            this.formData.cardId = cardInfo.id || '';
        },
    },
    watch: {
        cardTaskInfo: {
            deep: true,
            handler() {
                Object.assign(this.formData, this.cardTaskInfo);
            },
        },
        defaultDate(newValue) {
            this.formData.startDate = newValue;
            this.formData.deadline = newValue;
        },
        '$route.params.spaceKey': {
            handler(val) {
                this.spaceId = getSpaceId(val);
            },
        },
        spaceId() {
            this.getPlanList();
            // 重置新建任务页面数据
            this.resetForm();
            this.clear();
        },
        'formData.erp': {
            deep: true,
            immediate: true,
            handler() {
                // 新增任务，需要 name
                const curMember = this.memberList.find(member => member.erp === this.formData.erp) || {};
                this.formData.name = curMember.name;
                // 切换处理人时，获取该处理人的默认角色职责
                if (this.formData.erp) {
                    this.getDefaultRole(this.formData.erp, this.spaceId);
                }
            },
        },
        'formData.sprint': {
            handler() {
                this.getCardList();
            },
        },
        // 看板/列表中，新增任务时，默认选择当前激活的迭代
        '$store.state.plan.activePlan.id': {
            deep: true,
            immediate: true,
            handler() {
                if (this.$store.state.plan.activePlan.id && this.$store.state.plan.activePlan.archived === 0) {
                    this.formData.sprint = this.$store.state.plan.activePlan.id || -1;
                }
            },
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.card-task-form {
    width: 616px;

    & .el-form-item__label {
        padding: 0;
        font-size: 14px;
    }
    & .el-form-item{
        margin-bottom: 16px;
    }
    & .el-input,.el-input__inner{
        width: 300px;
        font-size: 14px;
    }
    // & .el-icon-plus{
    //     font-size: 12px;
    // }
    &__content .el-input,.el-input__inner{
        width: 100% !important;
    }

    // .el-form-item.is-required>.el-form-item__label:before{
    //     display: none;
    // }
    // .el-form-item.is-required>.el-form-item__label:after{
    //     content: '*';
    //     color: @requiredColor;
    //     margin-left: 4px;
    // }
}
</style>
