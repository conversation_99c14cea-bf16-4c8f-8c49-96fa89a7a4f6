import debounce from 'lodash/debounce';
import isFunction from 'lodash/isFunction';
import Sortable from 'sortablejs';

/* demand|teamspace 拖动排序 */


function restoreMutiItem(evt) {
    const { oldIndicies, newIndicies, to } = evt;

    // 移除新追加的元素
    newIndicies.forEach((multiDragElementObj) => {
        const { multiDragElement } = multiDragElementObj;
        if (multiDragElement.parentNode) {
            multiDragElement.parentNode.removeChild(multiDragElement);
        }
    });
    // 按顺序还原旧元素
    oldIndicies.forEach((multiDragElementObj) => {
        to.insertBefore(multiDragElementObj.multiDragElement, to.children[multiDragElementObj.index]);
    });
}

function restoreSingleItem(evt) {
    const { oldIndex, item } = evt;
    const positionEl = item.parentNode.children[oldIndex];
    if (positionEl) {
        item.parentNode.insertBefore(item, positionEl);
    }
}
const removeAllHighlight = (dropPlaceholderClass) => {
    document.querySelectorAll(`.${dropPlaceholderClass}`)
        .forEach(el => el.classList.remove(dropPlaceholderClass));
};
// 拖动到外面的时候需要高亮该容器，同时该容器内元素不发生变化
const highlightPlaceHolder = debounce((placeholderEl = {}, dropPlaceholderClass = '') => {
    removeAllHighlight(dropPlaceholderClass);
    placeholderEl.classList.add(dropPlaceholderClass);
}, 200);
// 从原列表中移除被拖走的元素
const removeItemFromList = (items = [], target) => {
    items.forEach((item) => {
        if (!item) {
            return;
        }
        const index = target.indexOf(item);
        if (index > -1) {
            target.splice(index, 1);
        }
    });
    return target;
};
const restoreSortableFrom = (evt) => {
    const { oldIndicies, from } = evt;
    if (evt.items.length > 0) {
        // For multi-select.
        if (Array.isArray(oldIndicies)) {
            // [removeChild error when using in Meteor/React #1149](https://github.com/SortableJS/Sortable/issues/986)
            oldIndicies.forEach((o) => {
                if (o.index > -1) {
                    from.insertBefore(o.multiDragElement, from.children[o.index]);
                }
            });
        }
    } else {
        // For single select.
        const itemEl = evt.item;
        const origParent = evt.from;
        origParent.appendChild(itemEl);
        itemEl.style.display = 'none';
    }
};

function onDragOutOfBounds(evt) {
    const {
        dragedItems, dragedItem,
    } = this;
    const { data } = this.scope;
    const {
        items,
        item,
    } = evt;
    const elItems = items.length ? items : [item];
    const dataItems = dragedItems && dragedItems.length ? dragedItems : [dragedItem];
    // console.log('onDragOutOfBounds', items);
    // 收拾残局，把目标容器中移入的这个玩意移除
    elItems.forEach((el) => {
        if (el.parentNode) { // to
            el.parentNode.removeChild(el);
        }
    });
    // 同步新列表到源列表
    removeItemFromList(dataItems, data);
    // 要按原来的顺序插回到列表里，否则，在patchNode的时候会报异常
    // https://github.com/SortableJS/Sortable/issues/986
    restoreSortableFrom(evt);
    // 整理结束，通知外侧完成drop
    this.scope.$emit('pull-end', {
        evt,
        dragedItems,
        dragedItem,
        related: this.related,
    });
}
function setDragedData(evt) {
    const { scope } = this;
    const { oldIndicies = [] } = evt;
    const avaliableOldIndicies = oldIndicies.filter(item => item && item.index > -1);
    if (avaliableOldIndicies.length) {
        // console.log('MutiDragHandler start', evt);
        this.dragedItems = avaliableOldIndicies.reduce((result, multiDragElement = {}) => {
            const target = scope.data[multiDragElement.index];
            if (target) {
                result.push(target);
            }

            return result;
        }, []);
    } else {
        const demand = scope.data[evt.oldIndex];
        if (!demand) {
            return false;
        }
        this.dragedItem = demand;
    }
    return true;
}
class FallbackHandler {
    constructor(scope, {
        forceFallback = false, // 需要配合Sortable里的forceFallback=true来使用
        fallbackContent = () => '', // 这里返回的内容会被设置到el的data-fallback上
    }) {
        this.forceFallback = forceFallback;
        this.fallbackContent = fallbackContent;
    }

    onClone(evt) {
        if (!this.forceFallback || !isFunction(this.fallbackContent)) {
            return;
        }
        setDragedData.call(this, evt);

        const origEl = evt.item;
        const demand = this.dragedItems && this.dragedItems.length ? this.dragedItems : this.dragedItem;
        const content = this.fallbackContent(demand);
        origEl.dataset.fallback = content;
    }
}
export default class DragHandler extends FallbackHandler {
    constructor(scope, {
        dropPlaceholderClass = 'sprint-sortable-ghost',
        ...props
    } = {}) {
        super(scope, props);
        if (!scope) {
            throw Error('生成DragHandler实例失败: 没有传递正确的外部环境参数');
        }
        this.scope = scope;
        this.dragedItem = undefined; // 单个拖动的元素
        this.demandIsExpanded = false;
        this.dropPlaceholderClass = dropPlaceholderClass;
    }

    onStart(evt) {
        const { scope } = this;
        setDragedData.call(this, evt);
        if (!this.dragedItem) {
            return false;
        }
        const demand = this.dragedItem;
        this.dragedItem = demand;
        if (demand.hasChild && demand.$expandStatus === true) {
            this.demandIsExpanded = demand.$expandStatus;
            scope.$emit('toggle-children', demand, scope.data);
        }
        scope.$emit('drag-start', demand, scope.data);

        return true;
    }

    onMove(evt) {
        if (!this.dragedItem) {
            return false;
        }
        if (!this.scope.$el.contains(evt.to)) {
            // console.log('拖到外面');
            highlightPlaceHolder(evt.related, this.dropPlaceholderClass);
            this.related = evt.related;
            return true;
        }
        // evt.related.style.border = '1px solid green';
        const demandParentId = this.dragedItem.$vParentId;
        const onRelatedElBottom = evt.relatedRect.top - evt.draggedRect.top > 0;

        // 转换为向下插入的环境
        let relatedEl = onRelatedElBottom ? evt.related : evt.related.previousSibling;
        let nextRelatedEl = onRelatedElBottom ? evt.related.nextSibling : evt.related;
        if (relatedEl && relatedEl.nodeType !== 1) {
            relatedEl = null;
        }
        if (nextRelatedEl && nextRelatedEl.nodeType !== 1) {
            nextRelatedEl = null;
        }

        // 获取当前节点与子节点的parentId
        const relatedParentId = relatedEl ? +relatedEl.dataset.vParentId : -1;
        const nextParentId = nextRelatedEl ? +nextRelatedEl.dataset.vParentId : -1;
        const relatedIsHandler = relatedEl ? +relatedEl.dataset.vParentId : 1; // 默认目标位置可插入

        // 判断当前节点是否是子节点的父
        const nextIsChild = relatedEl && (+relatedEl.dataset.id === nextParentId);

        // 禁止拖动到同级的非待我处理的需求后面
        if (demandParentId === relatedParentId && !relatedIsHandler) {
            return false;
        }

        // 与两节点任意一个同级且不是插入父子中间的情况
        return demandParentId === nextParentId
            || (demandParentId === relatedParentId && !nextIsChild);
    }

    onEnd(evt) {
        if (this.dropPlaceholderClass) {
            removeAllHighlight(this.dropPlaceholderClass);
        }
        // 拖动到外面的时候不改变当前列表的顺序，TODO: 但是应该执行移除吧？
        if (evt.pullMode) {
            onDragOutOfBounds.call(this, evt);
            this.dragedItem = undefined;
            return;
        }
        // 放回原位置的也恢复一下展开状态
        if (evt.oldIndex === evt.newIndex) {
            this.scope.$emit('drag-abort');
            this.scope.$emit('toggle-children', this.dragedItem, this.scope.data);
            this.dragedItem = undefined;
            return;
        }
        const { scope } = this;
        scope.data.splice(evt.oldIndex, 1);
        scope.data.splice(evt.newIndex, 0, this.dragedItem);
        scope.$emit('drag-end', {
            oldIndex: evt.oldIndex,
            newIndex: evt.newIndex,
            demand: this.dragedItem,
            data: scope.data,
        });
        // 收拾残局
        if (this.demandIsExpanded) {
            // 展开子列表
            scope.$emit('toggle-children', this.dragedItem, scope.data);
            this.demandIsExpanded = false;
            this.dragedItem = undefined;
        }
    }
}

// 多个拖拽的逻辑，

export class MultiDragHandler extends FallbackHandler {
    constructor(scope, {
        dropPlaceholderClass = 'sprint-sortable-ghost',
        sort = false, // sorting inside list
        ...props
    } = {}) {
        super(scope, props);
        if (!scope) {
            throw Error('生成DragHandler实例失败: 没有传递正确的外部环境参数');
        }
        this.scope = scope;
        this.dragedItems = []; // 被拖动的列表
        this.dropPlaceholderClass = dropPlaceholderClass;
        this.sort = sort;
        if (!this.scope.event || !this.scope.event.$on) {
            this.scope.event = this.scope.$initEvent();
        }
        // 批量全选和取消
        this.scope.event.$on('batch-select-all', (flag, elements = []) => {
            if (flag) {
                MultiDragHandler.selectAll(elements);
            } else {
                MultiDragHandler.deselectAll(elements);
            }
        });
    }

    onStart(evt) {
        const { scope } = this;
        setDragedData.call(this, evt);
        scope.$emit('drag-start', this.dragedItem, scope.data);
        return true;
    }

    onMove(evt) {
        // console.log('拖到外面', evt);
        if (!this.scope.$el.contains(evt.to)) {
            highlightPlaceHolder(evt.related, this.dropPlaceholderClass);
            this.related = evt.related;
        }
        return true;
    }

    onEnd(evt) {
        // console.log('onEnd', evt);
        if (this.sort) {
            // TODO: 在列表内拖动，整理一下data的新顺序,还没做允许列表内拖动的场景，目前没需求
            this.scope.$emit('drag-end', {
                oldIndex: evt.oldIndex,
                newIndex: evt.newIndex,
                items: this.dragedItems,
                data: this.scope.data,
            });
        } else {
            removeAllHighlight(this.dropPlaceholderClass);
            // console.log('MutiDragHandler on drop outside', evt);
            // Placeholder is out of bounds
            if (!this.scope.$el.contains(evt.to)) {
                // console.log('MutiDragHandler add ', evt);
                onDragOutOfBounds.call(this, evt);
                this.dragedItems.length = 0;
                removeAllHighlight(this.dropPlaceholderClass);
                return;
            }
            this.scope.$emit('drag-abort');
            // 不允许在列表内拖动，但是却落回了原列表，需要将被挪动的元素恢复原状， 并且取消选中状态
            if (evt.items && evt.items.length) {
                // 选中了以后的拖动，按照oldIndicies恢复原状
                restoreMutiItem(evt);
                return;
            }
            // 拽起来直接拖的单个元素恢复原状，按照index恢复原状
            restoreSingleItem(evt);
        }
    }

    onSelect(evt) {
        this.emitBatchEvent(evt);
    }

    onDeselect(evt) {
        this.emitBatchEvent(evt);
    }

    emitBatchEvent(evt) {
        const { item } = evt;
        if (item) {
            const id = +item.dataset.id;
            // 与批量操作同步数据
            this.scope.$emit('batch-check', evt, { id });
        }
    }

    // 由批量操作点击全选和取消全选来触发
    static selectAll(htmlElementList = []) {
        const { select } = Sortable.utils;
        htmlElementList.forEach(el => select(el));
    }

    static deselectAll(htmlElementList = []) {
        const { deselect } = Sortable.utils;
        htmlElementList.forEach(el => deselect(el));
    }
}
