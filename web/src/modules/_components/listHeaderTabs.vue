<template>
    <div class="list-header">
        <slot />
        <div class="list-header__filter">
            <slot name="filter" />
        </div>
        <div class="list-header__extra">
            <slot name="extra" />
        </div>
    </div>
</template>
<script>
export default {
    name: 'ListHeaderTabs',
    props: {
        value: {
            type: String,
        },
        tabs: {
            type: Array,
            default: () => [],
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.list-header{
  position: relative;
  width: 100%;
  display: inline-flex;
  align-items: center;
  height: 48px;
//   border-bottom: 1px solid #efefef;
  background: #fff;
  ul, li{
    list-style: none;
  }
  &-tab{
    display: inline-flex;
    margin: 8px 16px;
    margin-right: 0;
    height: 32px;
    // line-height: 32px;
    border: 1px solid #F7F8F9;
    background-color: #F7F8F9;
    border-radius: 4px;
    white-space: nowrap;
  }
  &-tab__item{
      margin: 1px;
      line-height: 28px;
      padding: 0 8px;
      color: @fontColor;
      cursor: pointer;
      font-size: 12px;
      border-radius: 4px;
      &--active{
        font-weight: 600;
      }
      &--active,
      &:hover{
          color: @primaryColor;
          background-color: #fff;
      }
      &--disabled{
          pointer-events: none;
          color: @fontDisabledColor;
      }
  }
  &__extra{
    position: absolute;
    right: 0;
    top: 0;
    height: 48px;
    width: -moz-fit-content;
    width: fit-content;
  }
  &__filter{
      display: inline-flex;
      align-items: center;
    //   flex-basis: 100%;
      flex: 1;
      overflow: hidden;
    //   width: calc(100% - 155px);
  }
}
</style>
