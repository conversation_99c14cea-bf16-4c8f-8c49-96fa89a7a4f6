<template>
    <el-tooltip
        :disabled="spacePrivilage.createCard"
        :content="$t('jacp.noPermission')"
        placement="left"
        effect="light"
    >
        <!-- TODO: 这里以后要改成下拉选择新建不同的实体类型 -->
        <el-dropdown
            v-if="filteredActions.length > 1"
            placement="bottom"
            trigger="click"
            :disabled="!spacePrivilage.createCard"
        >
            <span>
                <el-button
                    size="small"
                    type="primary"
                    circle
                    icon="el-icon-plus"
                    class="jacp-button--gradient jacp-button--mini"
                    :disabled="!spacePrivilage.createCard"
                />
            </span>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                    v-for="action in filteredActions"
                    :key="action.cmd"
                    :disabled="!spacePrivilage.createCard || action.disabled"
                    @click.native="() => handleAction(action)"
                >
                    {{ action.text }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
        <el-button
            v-else
            size="small"
            type="primary"
            circle
            icon="el-icon-plus"
            class="jacp-button--gradient jacp-button--mini"
            :disabled="!spacePrivilage.createCard || actions[0].disabled"
            @click="() => handleAction(actions[0])"
        />
    </el-tooltip>
</template>
<script>
import isFunction from 'lodash/isFunction';
import Action from '@/models/action';
import cardActions from '@/models/cardActions';
import taskActions from '@/modules/task/models/taskActions';
import { mapState } from '$node_modules.vuex';

// 目前都是默认的，一些创建方式。如果有需要变更的可以再
const defaultActions = [
    new Action({
        cmd: 'createCard',
        handler: cardActions.create,
        text: '新建卡片',
    }),
    new Action({
        cmd: 'createTask',
        handler: taskActions.create,
        text: '新建任务',
    }),
];
export default {
    name: 'EntityCreator',
    props: {
        visibleActions: {
            default: () => ['createCard', 'createTask'],
            type: Array,
        },
        actions: {
            type: Array,
            default: () => defaultActions,
        },
        handlerParameters: {
            type: [Object, String, Array, Number],
            default: () => ({}),
        },
        afterActionHandler: {
            type: Function,
            default: () => {},
        },
    },
    computed: {
        ...mapState('chilldteamspace', ['spacePrivilage']),
        filteredActions() {
            const { visibleActions } = this;
            return this.actions.filter(action => visibleActions.includes(action.cmd));
        },
    },
    methods: {
        async handleAction(action = {}) {
            if (action.disabled) {
                return;
            }
            if (isFunction(action.handler)) {
                const handleResult = await action.handler
                    .call(null, this.handlerParameters);
                this.$emit(action.cmd, action, handleResult);
            }
        },
    },
};
</script>
