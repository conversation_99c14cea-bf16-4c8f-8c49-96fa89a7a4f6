<template>
    <div class="import-error-dialog">
        <div class="import-error-dialog__message">
            {{ message }}
        </div>
        <div class="import-error-dialog__tips">
            <span>您可以：</span>
            <jacp-button
                class="import-error-dialog__button1"
                @click="downloadDoc(reportUrl)"
                type="primary"
            >
                下载错误报告
            </jacp-button>
            <jacp-button
                class="import-error-dialog__button2"
                @click="downloadDoc(templateUrl)"
            >
                下载模板
            </jacp-button>
        </div>
    </div>
</template>

<script type="text/javascript">
import { downloadFile } from '@/plugins/utils';

export default {
    props: {
        message: String,
        reportUrl: String,
        templateUrl: String,
    },
    methods: {
        downloadDoc(url) {
            downloadFile({ url });
        },
    },
};
</script>

<style lang="less">
.import-error-dialog{
    &__tips{
        margin-top: 20px;
    }
}
</style>
