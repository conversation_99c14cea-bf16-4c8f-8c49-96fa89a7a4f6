<template>
    <div class="basic-table-root">
        <el-table
            :data="data"
            style="width: 100%"
            v-on="$listeners"
            v-bind="$attrs"
        >
            <el-table-column
                v-for="(column, index) in columns"
                :key="`${column.prop}-${index}`"
                v-bind="column"
            >
                <template slot-scope="{ row, col, $index }">
                    <table-cell
                        :row="row"
                        :col="col"
                        :index="$index"
                        :column="column"
                    />
                </template>
            </el-table-column>
            <slot />
        </el-table>
        <slot name="pager" />
    </div>
</template>
<script>

export default {
    name: 'BasicTable',
    inheritAttrs: false,
    props: {
        data: { type: Array, default: () => [] },
        columns: { type: Array, default: () => [] },
    },
};
</script>
