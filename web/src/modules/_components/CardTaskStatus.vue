<template>
    <el-dropdown
        class="card-task-status__dropdown"
        ref="dropdown"
        size="medium"
        placement="bottom-start"
        trigger="click"
        @click.native.stop="showDropdownMenu"
        @command="handleChangeStatus"
    >
        <span class="el-dropdown-link">
            <i
                :style="`color: ${currentStatusInfo.color}`"
                :class="currentStatusInfo.icon"
            />
            <span
                style="font-size: 12px; margin: 0 4px;"
                v-if="showText"
            >{{ statusOptions.find(e => e.code === status).name }}</span>
            <i
                v-if="showText && !changeStatusDisabled"
                class="el-icon-caret-bottom"
                style="color: #C0C4CC;"
            />
        </span>
        <el-dropdown-menu
            :append-to-body="appendToBody"
            slot="dropdown"
            class="card-task-status__dropdown-menu"
        >
            <el-dropdown-item
                v-for="item in statusOptions"
                :key="item.code"
                :command="item.code"
                :disabled="changeStatusDisabled"
            >
                <i
                    :style="`color: ${item.color}`"
                    :class="item.icon"
                />
                <span>{{ item.name }}</span>
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script>
import { availableStatus } from '@/modules/task/constant';

export default {
    name: 'CardTaskStatus',
    model: {
        prop: 'status',
        event: 'command',
    },
    props: {
        showText: {
            type: Boolean,
            default: true,
        },
        status: {
            type: Number,
            default: 1,
        },
        appendToBody: {
            type: Boolean,
            default: true,
        },
        changeStatusDisabled: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            statusOptions: availableStatus,
        };
    },
    computed: {
        currentStatusInfo() {
            return this.statusOptions.find(e => e.code === this.status);
        },
    },
    methods: {
        handleChangeStatus(status) {
            if (this.status === status) {
                return;
            }
            this.$emit('command', status);
            this.$emit('change-status', status);
        },
        showDropdownMenu() {
            if (this.changeStatusDisabled) {
                this.$refs.dropdown.hide();
            }
        },
    },
};
</script>

<style lang="less">
.card-task-status__dropdown {
    flex: none;
    & .el-dropdown-link,
    &-menu .el-dropdown-menu__item {
        display: flex;
        align-items: center;
    }
}
</style>
