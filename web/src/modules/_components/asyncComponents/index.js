import Vue from 'vue';
// import { JModule } from '@jmodule/client';
import AsyncComp from './asyncComponent';

// 来自模块的通用的业务组件？
// vue里用全局组件要想不报错，模块在使用的时候行云里就已经这个组件存在了，至少名字是存在的，有点尴尬
// 独立访问的模块的就自己处理吧，只在行云里通用
JModule.getModuleAsync('prdm').then(async (module) => {
    await module.load('load', { autoApplyStyle: true });
});
Vue.component('JacpPrdmProductlineTree', AsyncComp(JModule.require('prdm')
    .then(extension => extension?.$globalComponents?.productLineTree)));

Vue.component('JacpPrdmRelateToProduct', AsyncComp(JModule.require('prdm')
    .then(extension => extension?.$globalComponents?.relateToProduct)));

/* Vue.component('JacpPrdmProductlineTree', AsyncComp(new Promise((resolve) => {
    setTimeout(() => {
        resolve({ template: '<div>dd</div>' });
    }, 100000);
})));
 */
