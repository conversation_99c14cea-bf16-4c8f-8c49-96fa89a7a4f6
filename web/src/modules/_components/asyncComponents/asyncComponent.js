import Loading from './loading.vue';

const defaultAsyncConfig = {
    delay: 200,
    // 最长等待时间。超出此时间则渲染错误组件。默认：Infinity
    timeout: 30000,
};
const AsyncComp = (componentDefer, config = {}) => () => ({
    // 需要加载的组件。应当是一个 Promise
    component: componentDefer,
    // 加载中应当渲染的组件
    loading: Loading,
    // 出错时渲染的组件
    error: { template: '<jacp-text size="12" type="disable">对不起，资源未找到</jacp-text>' },
    // 渲染加载中组件前的等待时间。默认：200ms。
    ...defaultAsyncConfig,
    ...config,
});
export default AsyncComp;
