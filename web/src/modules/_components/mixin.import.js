import DialogModel from '@/models/dialog';
import FileModel from '@/models/file';
import importErrorDialog from './importErrorDialog';
import { downloadFile } from '@/plugins/utils';

function showFileError() {
    DialogModel.error({
        title: '导入失败',
        content: '文件格式错误，请参考模板文件',
    });
}
function showImportError(error) {
    DialogModel.error({
        title: '导入失败',
        slot: importErrorDialog,
        slotProps: error,
    });
}
function fileValidator(files) {
    return /\.(xlsx|xls)$/.test(files[0].name);
}

export default {
    data() {
        return {
            importTemplateUrl: '',
            importUrl: '',
            uploading: false,
            uploadingText: '正在导入',
            importButtons: [{
                name: '从Excel导入',
                handle() {
                    return FileModel.showSystemFileDialog({
                        validator: fileValidator,
                    }).then((files) => {
                        this.uploading = true;
                        FileModel.upload(files, this.importUrl).then((data) => {
                            if (data.error) {
                                showImportError({
                                    message: '导入的Excel文件与模板不符，或所填字段不符合模板要求',
                                    reportUrl: data.errorLink,
                                    templateUrl: this.importTemplateUrl,
                                });
                            } else {
                                this.$emit('import-success');
                                this.$notify({
                                    title: '导入成功',
                                    type: 'success',
                                    duration: 2000,
                                });
                            }
                        }).finally(() => {
                            this.uploading = false;
                        });
                    }).catch(showFileError);
                },
            }, {
                name: '下载模板',
                handle() {
                    downloadFile({
                        url: this.importTemplateUrl,
                    });
                },
            }],
        };
    },
};
