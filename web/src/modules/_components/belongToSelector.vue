<template>
    <el-tooltip
        :content="disabledText"
        :placement="tooltipPlacement"
        :disabled="!disabledText"
    >
        <el-cascader
            v-bind="$attrs"
            :options="optionManager.options"
            :show-all-levels="false"
            :props="{
                value: 'id',
                label: 'name',
                expandTrigger: 'hover',
                checkStrictly: false ,
                emitPath: false,
            }"
            :placeholder="placeholder"
            v-on="$listeners"
            @visible-change="handleVisibleChange"
            @change="handleChange"
        >
            <template slot-scope="{ node, data }">
                <!-- FIXME: 有其他实体的时候呢？ -->
                <LocalSprintItem
                    :sprint="data"
                    v-if="node.level > 1"
                />
                <span v-else>{{ data.name }} </span>
            </template>
        </el-cascader>
    </el-tooltip>
</template>
<script>
import isEqual from 'lodash/isEqual';
import LocalSprintItem from '@/modules/sprints/components/sprintInlineItem';
import { backlogPlan } from '@/modules/sprints/models/sprint';

class OptionManager {
    constructor(options = []) {
        this.options = options;
    }

    option(key, value = {}) {
        const existing = this.options.find(option => option.key === key);
        if (existing) {
            Object.assign(existing, value);
        } else {
            this.options.push(value);
        }
    }
}
const defaultOptions = [{
    key: 'backlog',
    ...backlogPlan,
    name: 'Backlog（待办事项）',
}, {
    key: 'sprint',
    name: '迭代',
    id: 'sprint',
    children: [],
}];
// 迭代/backlog（原来的所属迭代）
const optionCache = new WeakMap();
export default {
    name: 'JacpBelongToSelector',
    inheritAttrs: false,
    components: {
        LocalSprintItem,
    },
    props: {
        // sprintData: { type: Array, default: () => [] },
        options: {
            type: Array,
        },
        dataMap: { type: Object, default: () => ({}) },
        disabledText: String,
        tooltipPlacement: { type: String, default: 'top' },
        placeholder: { type: String, default: '请选择' },
        // FIXME: 这个主要解决卡片详情页里的第一次选项出现需要lazyload的问题
        whenFirstShowOption: { type: Function },
    },
    data() {
        return {
            optionManager: new OptionManager(this.options || defaultOptions),
        };
    },
    methods: {
        // Public
        setOption(key, children = [], props = {}) {
            this.optionManager.option(key, {
                children,
                ...props,
            });
        },
        handleChange(val) {
            this.$emit('change', val);
        },
        handleVisibleChange(val) {
            this.$emit('visible-change', val);
            if (!val || optionCache.has(this)) {
                return;
            }
            if (!this.whenFirstShowOption) {
                return;
            }
            optionCache.set(this, this.whenFirstShowOption(this));
        },
    },
    watch: {
        dataMap: {
            // deep: true,
            immediate: true,
            handler(val = {}, old) {
                if (isEqual(val, old)) {
                    return;
                }
                Object.keys(val)
                    .forEach(key => this.setOption(key, val[key]));
            },
        },
    },
};
</script>
