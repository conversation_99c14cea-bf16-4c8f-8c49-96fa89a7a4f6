<template>
    <div class="jacp-appstore appstore-content-wrap">
        <section class="store-section-tabs">
            <div class="store-tabs">
                <div>
                    <span
                        v-for="block in blockList"
                        :key="block.id"
                        :class="['store-tab', activeBlock.some(item => item === block.id) ? 'is-active' : '']"
                        @click="clickTab(block)"
                    >
                        {{ block.name }}
                    </span>
                </div>
                <el-input
                    v-model="keyword"
                    placeholder="输入关键字搜索"
                    @input="search"
                >
                    <i
                        slot="prefix"
                        class="el-input__icon el-icon-search"
                    />
                </el-input>
            </div>
        </section>
        <section class="store-section-apps">
            <div
                v-if="!filteredAppList.length"
                class="store-app-list-placeholder"
            >
                <img
                    class="j-server-error__img"
                    src="@/assets/images/errorpage/<EMAIL>"
                >
                <jacp-text type="disable">暂无应用</jacp-text>
            </div>
            <div
                class="store-app-list"
                v-else
            >
                <app-card
                    ref="appCard"
                    v-for="app in filteredAppList"
                    :key="app.id"
                    :app="app"
                    :daa="data"
                    @on-action-click="(star) => actionClick(star, app)"
                    @card-click="cardClick"
                />
            </div>
        </section>
    </div>
</template>

<script>
import appCard from '@/modules/appstore/components/appCard';
import devCenter from '../open/model/devCenter';
import AppServer from '@/modules/root/models/appServer';

// const MENU_ALL = 'menu-all';
const BLOCK_ALL = { id: 0, name: '全部' };
const OTHER = { id: -1, name: '其他' };

export default {
    components: { appCard },
    props: {
        star: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            blockList: [],
            allAppList: [],
            activeBlock: [0],
            keyword: '',
            filteredAppList: [],
            appId: '',
            data: false,
            labelIdList: [],
        };
    },
    mounted() {
        devCenter.getLabelInfoByName().then((blockList = []) => {
            this.blockList = [BLOCK_ALL, ...blockList, OTHER];
        });
        AppServer.getAppStore().then((appList = []) => {
            this.allAppList = appList;
        }).then(() => {
            this.activeAppList();
        });
    },
    methods: {
        async activeAppList() {
            if (this.activeBlock.includes(0)) {
                this.filteredAppList = this.allAppList;
                return;
            }
            if (this.activeBlock.includes(-1)) {
                this.labelIdList = [];
                this.labelIdList.push(-1);
                devCenter.getLabelInfoByName([]).then((res) => {
                    res.forEach((item) => {
                        this.labelIdList.push(item.id);
                    });
                }).then(async () => {
                    this.filteredAppList = await AppServer.getAppStore(this.labelIdList);
                });
            } else {
                const appIds = [];
                this.activeBlock.forEach((item) => {
                    appIds.push(item);
                });
                // eslint-disable-next-line vue/no-async-in-computed-properties
                this.filteredAppList = await AppServer.getAppStore(appIds);
            }
            // return this.activeBlock === MENU_ALL
            //     ? this.allAppList
            //     : this.allAppList.filter(app => app.block.code === this.activeBlock);
        },
        goDetail(code) {
            this.$router.push({ name: 'jacpAppDetail', params: { code } });
        },
        clickTab(block) {
            if (block.id === 0) {
                this.activeBlock = [block.id];
            } else if (block.id === -1) {
                if (!(this.activeBlock[0] === -1)) {
                    this.activeBlock = [-1];
                } else {
                    this.activeBlock = [0];
                }
            } else if (!this.activeBlock.some(item => item === block.id)) {
                if (this.activeBlock[0] === 0 || this.activeBlock[0] === -1) {
                    this.activeBlock.splice(0, 1);
                }
                this.activeBlock = [block.id];
            } else {
                this.activeBlock.forEach((item, index) => {
                    if (item === block.id) {
                        this.activeBlock.splice(index, 1);
                    }
                });
            }
            if (this.activeBlock.length === 0) {
                this.activeBlock = [0];
            }
            this.keyword = '';
            // AppModel.getAppListByIds(this.activeBlock).then((res) => {
            //     console.log(res);
            // });
            this.activeAppList();
        },
        actionClick(star, app) {
            this.$emit('on-action-click', star, app);
            this.appId = app.id;
        },
        goDetail1(code) {
            // this.$router.push({ name: 'jacpAppDetail', params: { code } });
            this.$router.push({ name: 'jacpDevAppSetting', params: { code } });
        },
        starChange() {
            // this.$refs.appCard.forEach((item) => {
            //     item.init();
            // });
            this.data = !this.data;
        },
        search(keyCode) {
            console.log(keyCode, 'kkkkkkk');
            this.activeBlock = [0];
            this.filteredAppList = this.allAppList.filter(item => item.name.indexOf(keyCode) !== -1);
        },
        cardClick(app) {
            console.log(app);
            this.$emit('card-click', app);
        },
    },
};
</script>

<style lang="less">
@lineHeight: 1.5;

.jacp-appstore {
    display: flex;
    flex-direction: column;
    line-height: @lineHeight;
    margin-top: 20px;

    .open-section-banner {
        background: rgba(38, 149, 241, 0.1);
    }
    .store-section-tabs {
        border-bottom: 1px solid var(--color--base--hr);
        margin-bottom: 20px;
    }

    .store-tabs {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 70px;
        & > div {
            width: calc(100% - 230px);
        }
        .store-tab {
            padding-right: var(--gutter--large);
            color: var(--color--regular--content);
            cursor: pointer;
            &:hover,
            &.is-active {
                color: var(--color--base--content);
                font-weight: var(--font-weight-bold);
            }
        }

            .el-tag {
                margin-right: 10px;
            }
        .el-input {
            width: 216px;
            &__icon,
            &__inner {
                height: 28px;
                line-height: 28px;
            }
            &__inner {
                background: var(--color--base--background);
                border-radius: 16px;
                border: none;
                transition: .3s;
            }
        }
    }

    .store-section-apps {
        flex: 1;
        // margin: 24px auto 64px;
    }
    .store-app {
        &-list {
            display: grid;
            grid-template-columns: repeat(auto-fill,minmax(210px,1fr));
            grid-auto-flow: row dense;
            grid-template-rows: repeat(3,72px);
            grid-gap: var(--gutter--medium);
        }
        &-list-placeholder {
            margin-top: 100px;
            grid-column-start: 2;
            grid-column-end: 4;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: calc(100% - var(--gutter--large));
        }
    }
    &.section-main{
        height: auto;
    }
}

</style>
