<template>
    <div class="jacp-appdetail">
        <section>
            <div class="detail-wrapper">
                <div class="detail-left">
                    <div class="detail-icon">
                        <img :src="detail.icon">
                    </div>
                </div>
                <div class="detail-right">
                    <div class="block">
                        <div class="detail-title">{{ detail.name }}</div>
                        <div class="text-gray j-mgb8">由「{{ supplier }}」提供</div>
                        <div class="detail-help j-mgb24">
                            <span class="detail-help-erp">
                                <i class="jacp-icon-a-dongdongicon" />
                                <a :href="getChatHref([owner.erp])">
                                    {{ owner.name }}
                                </a>
                            </span>
                            <span
                                class="detail-help-doc"
                                v-if="detail.helpUrl"
                            >
                                <i class="jacp-icon-help" />
                                <a
                                    :href="detail.helpUrl"
                                    target="_blank"
                                >帮助文档</a>
                            </span>
                        </div>
                        <!-- <div class="button detail-button">已安装</div> -->
                    </div>

                    <div class="section-divider" />
                    <div class="block">
                        <div class="detail-subtitle">应用评分</div>
                        <el-row
                            class="rate-container"
                            type="flex"
                            align="middle"
                        >
                            <el-col :span="12">
                                <el-row type="flex">
                                    <span class="rate-score">
                                        <span>{{ Number(score.average).toFixed(1) }}</span>
                                        <span>分</span>
                                    </span>
                                    <span class="rate-count">
                                        <span>共</span>
                                        <span>{{ score.count }}</span>
                                        <span>个用户评分</span>
                                    </span>
                                </el-row>
                            </el-col>
                            <el-col
                                :span="12"
                                class="rate-figure"
                            >
                                <el-row
                                    class="rate-row"
                                    type="flex"
                                    align="middle"
                                    v-for="item in scores"
                                    :key="item"
                                >
                                    <multi-stars
                                        class="rate-stars"
                                        :count="item"
                                    />
                                    <el-progress
                                        class="rate-bar"
                                        :percentage="calcPercentage(score.scoreCount[item], score.count)"
                                        :show-text="false"
                                        :stroke-width="3"
                                    />
                                </el-row>
                            </el-col>
                        </el-row>
                    </div>

                    <el-tabs v-model="activeTab">
                        <el-tab-pane
                            :name="tabs.intro.value"
                            :label="tabs.intro.label"
                        >
                            <div class="block">
                                <div class="detail-subtitle">应用介绍</div>
                                <p class="detail-introduction text-dark text-sm">{{ detail.desc }}</p>
                            </div>

                            <div class="section-divider" />
                            <div class="block">
                                <div class="detail-subtitle">应用预览</div>
                                <div class="detail-pics">
                                    <div
                                        class="detail-pic"
                                        v-for="(pic, index) in previewPics"
                                        :key="index"
                                    >
                                        <img :src="pic">
                                    </div>
                                    <div
                                        class="detail-pic-placeholder"
                                        v-if="!previewPics.length"
                                    >
                                        <span class="text-sm text-gray2">暂未上传预览图片</span>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane
                            :name="tabs.entry.value"
                            :label="tabs.entry.label"
                            v-if="!disabledEntrance"
                        >
                            <div class="block">
                                <div class="detail-subtitle">应用入口</div>
                                <div class="detail-entrance">
                                    <span class="text-dark text-small">{{ detail.name }}</span>
                                    <el-divider direction="vertical" />
                                    <div
                                        class="detail-entrance-text text-gray text-xs"
                                        @click="showEntrance=true"
                                    >
                                        <el-tooltip
                                            content="查看应用入口"
                                            placement="top"
                                            effect="dark"
                                        >
                                            <span>行云 · 应用导航</span>
                                        </el-tooltip>
                                    </div>
                                    <div
                                        class="detail-entrance-img-wrapper"
                                        v-if="showEntrance"
                                        @click="showEntrance=false"
                                    >
                                        <img src="http://storage.jd.com/jacp.attachment/app_store/entrance_imgs/jacp_navigation.png">
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane
                            :name="tabs.comment.value"
                            :label="`${tabs.comment.label}(${score.commentCount})`"
                        >
                            <div
                                class="block"
                                style="padding-bottom: 16px;"
                            >
                                <el-row
                                    class="form-comment-row"
                                    type="flex"
                                    align="middle"
                                >
                                    <el-col :span="6">
                                        <span style="color: #909399; font-size: 14px;">
                                            点按进行评分
                                        </span>
                                    </el-col>
                                    <el-col
                                        :span="6"
                                        class="rate-slider"
                                    >
                                        <el-rate
                                            v-model="form.score"
                                            :void-color="baseColor"
                                            :colors="rateColors"
                                        />
                                    </el-col>
                                </el-row>
                                <el-row
                                    class="form-comment-row"
                                    type="flex"
                                >
                                    <jacp-erp
                                        :data="$store.state.user"
                                        :display-name="false"
                                        :avatar="true"
                                        :avatar-size="24"
                                        style="padding-right: 16px;"
                                    />
                                    <el-input
                                        class="j-input"
                                        type="textarea"
                                        :rows="3"
                                        placeholder="请输入评论"
                                        v-model="form.advise"
                                        autofocus
                                    />
                                </el-row>
                                <el-row
                                    type="flex"
                                    justify="end"
                                    class="form-comment-row"
                                >
                                    <el-button
                                        class="jacp-button"
                                        @click="doReset"
                                    >
                                        取消
                                    </el-button>
                                    <el-button
                                        class="jacp-button"
                                        :disabled="!form.score"
                                        @click="doRate"
                                        type="primary"
                                    >
                                        评论
                                    </el-button>
                                </el-row>
                            </div>
                            <div class="section-divider" />
                            <div class="block">
                                <div class="detail-subtitle">全部评论</div>
                                <div
                                    class="detail-comment"
                                    v-for="comment in scoreHistory"
                                    :key="comment.id"
                                >
                                    <div class="detail-comment-title">
                                        <jacp-erp
                                            :data="comment.creator"
                                            :display-name="false"
                                            :avatar="true"
                                            :avatar-size="24"
                                        />
                                        <div class="detail-comment-creator">{{ comment.creator.name }}</div>
                                        <el-rate
                                            v-model="comment.score"
                                            disabled
                                            void-color="#DBDFE7"
                                        />
                                        <span class="detail-comment-time">{{ comment.createTime }}</span>
                                        <span
                                            v-if="comment.creator.erp === $store.state.user.erp"
                                            class="detail-comment-delete"
                                            @click="deleteComment(comment.id)"
                                        >
                                            删除
                                        </span>
                                    </div>
                                    <div class="detail-comment-advise">
                                        {{ comment.advise }}
                                    </div>
                                    <div class="section-divider detail-comment-divider" />
                                </div>
                                <div
                                    class="detail-comment-more"
                                    v-show="!allHis && score.commentCount > 4"
                                >
                                    <el-link
                                        type="primary"
                                        :underline="false"
                                        @click="queryAppScore(true)"
                                    >
                                        查看全部{{ score.commentCount }}条评论<i class="el-icon-arrow-down" />
                                    </el-link>
                                </div>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
        </section>
    </div>
</template>

<script>
import multiStars from './components/multiStars';
import AppModel from './model';
import { getChatHref } from '@/utils/timline';

const baseColor = '#2695F1';
const rateColors = [baseColor, baseColor, baseColor];
const scores = [5, 4, 3, 2, 1];
const tabs = {
    intro: {
        value: 'intro',
        label: '应用介绍',
    },
    entry: {
        value: 'entry',
        label: '应用入口',
    },
    comment: {
        value: 'comment',
        label: '应用评价',
    },
};

export default {
    components: { multiStars },
    props: {
        disabledEntrance: { type: Boolean, default: false },
    },
    data() {
        return {
            detail: {},
            showEntrance: false,
            activeTab: undefined,
            form: {
                advise: '',
                score: undefined,
            },
            scoreHistory: [],
            allHis: false,
        };
    },
    computed: {
        code() {
            return this.$route.params.code;
        },
        owner() {
            return this.detail.owner ?? {};
        },
        supplier() {
            return (this.owner?.fullOrgName || '')
                .split('-')
                .slice(1)
                .join('-');
        },
        score() {
            if (this.detail.score && Object.keys(this.detail.score).length) {
                return this.detail.score;
            }
            return {
                average: 0,
                count: 0,
                lastScore: 0,
                scoreCount: {},
                commentCount: 0,
            };
        },
        previewPics() {
            return this.detail.introducePics ?? [];
        },
    },
    created() {
        this.baseColor = baseColor;
        this.rateColors = rateColors;
        this.scores = scores;
        this.tabs = tabs;
    },
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            const tabValues = Object.values(tabs).map(tab => tab.value);
            let { tab } = to.query;
            if (!tabValues.includes(tab)) {
                tab = tabs.intro.value;
            }
            vm.activeTab = tab;
        });
    },
    mounted() {
        this.getAppDetail();
    },
    methods: {
        getChatHref,
        getAppDetail() {
            AppModel.getAppDetail(this.code).then((data) => {
                this.detail = data;
                this.queryAppScore(false);
            });
        },
        calcPercentage(count = 0, base) {
            return count !== 0 ? Math.floor((count / base) * 100) : 0;
        },
        doReset() {
            this.form.score = undefined;
            this.form.advise = '';
        },
        doRate() {
            const { id: appId } = this.detail;
            const { score, advise = '' } = this.form;
            AppModel.commitAppScore({
                appId,
                score,
                advise: advise.trim(),
            }).then(() => {
                this.getAppDetail();
                this.$message({
                    message: '评分成功',
                    type: 'success',
                });
            });
            this.doReset();
        },
        queryAppScore(all = false) {
            this.allHis = all;
            AppModel.queryAppScore({
                appId: this.detail.id,
            }, this.allHis).then((data = []) => {
                this.scoreHistory = data;
            });
        },
        deleteComment(id) {
            this.$confirm('确定删除该条评价吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '再想想',
                type: 'warning',
            }).then(() => {
                AppModel.deleteComment(id).then(() => {
                    this.$message({
                        type: 'success',
                        message: '删除成功',
                    });
                    this.getAppDetail();
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除',
                });
            });
        },
    },
};
</script>

<style lang="less">
@lineHeight: 1.5;

.text-dark {
    color: var(--color--base--content);
}
.text-gray {
    color: var(--color--secondary--content);
}
.text-gray2 {
    color: var(--color--extralight--content);
}

.text-xs {
    font-size: var(--font-size--description);
}
.text-sm {
    font-size: var(--font-size--content);
}
.text-base {
    font-size: var(--font-size--subtitle);
}
.text-lg {
    font-size: var(--font-size--title);
}

.jacp-appdetail {
    line-height: @lineHeight;
    .block {
        position: relative;
    }
    section {
        padding: var(--gutter--xlarge);
    }
    .detail-wrapper {
        display: flex;
        padding-bottom: var(--gutter--xlarge);
        .detail {
            .section-divider {
                margin: var(--gutter--large) 0 var(--gutter--xlarge);
            }

            &-icon {
                width: 168px;
                height: 168px;
                img {
                    width: 100%;
                    border-radius: 50px;
                }
            }
            &-right {
                position: relative;
                width: 100%;
                padding-left: var(--gutter--large);
            }
            &-title,
            &-subtitle {
                color: var(--color--base--content);
                font-weight: var(--font-weight-bold);
            }
            &-title {
                margin-bottom: var(--gutter--small);
                font-size: 24px;
            }
            &-subtitle {
                height: 40px;
                line-height: 40px;
                font-size: var(--font-size--subtitle);
                margin: var(--gutter--large) 0 var(--gutter--small);
            }
            // &-comment {
            //     margin-bottom: var(--gutter--small);
            // }
            &-help {
                font-size: var(--font-size--subtitle);
                color: var(--color--primary);
                i {
                    color: inherit;
                    padding-right: var(--gutter--mini);
                }
                & > *:hover {
                    color: var(--color--primary--hover);
                }
                &-doc {
                    margin-left: var(--gutter--medium);
                }
            }
            &-button {
                position: absolute;
                right: 0;
                bottom: 0;
            }

            &-introduction {
                margin: 0;
                white-space: pre-line;
            }

            &-pic {
                width: 100%;
                margin-bottom: var(--gutter--base);
                img {
                    width: 100%;
                }
            }
            &-pic-placeholder {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 585px;
                background: #fafbfc;
                border: 1px solid rgba(220, 223, 230, 1);
                border-radius: 16px;
            }

            &-entrance {
                .el-divider--vertical {
                    margin: 0 var(--gutter--medium);
                }
                &-text {
                    position: relative;
                    cursor: pointer;
                    display: inline-block;
                    &:hover {
                        color: var(--color--primary--hover);
                    }
                }
                &-img-wrapper {
                    position: fixed;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 10;
                    background: rgba(0, 0, 0, 0.2);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    img {
                        width: 1080px;
                    }
                }
            }

            &-info {
                display: inline-block;
                padding-right: 100px;
                &-key {
                    margin-bottom: var(--gutter--small);
                }
            }
        }
    }

    .rate-container {
        color: var(--color--base--content);
    }
    .rate-score > span {
        &:first-child {
            padding-right: var(--gutter--small);
            font-size: 56px;
            font-weight: var(--font-weight-bold);
        }
        &:last-child {
            font-size: var(--font-size--content);
        }
    }
    .rate-count {
        flex: 1;
        font-size: var(--font-size--content);
        text-align: right;
        > span:nth-child(2) {
            padding: 0 4px;
        }
        :first-child::before {
            content: " ";
            font-size: 56px;
        }
    }

    .rate-row {
        --rate-stars-width: 120px;
        .rate-stars {
            margin-right: 8px;
            width: var(--rate-stars-width);
            line-height: 8px;
            text-align: right;
        }
        .rate-bar {
            width: calc(100% - var(--rate-stars-width));
        }
    }

    // rate progress bar
    .rate-bar {
        & .el-progress-bar__outer {
            background-color: #f0f2f5;
        }
        & .el-progress-bar__inner {
            background-color: #909399;
        }
    }

    .rate-slider {
        text-align: right;
        transform: translateX(8px);
        .el-rate__icon {
            font-size: 24px;
        }
    }

    .form-comment-row {
        margin-bottom: var(--gutter--medium);
        &:first-child {
            height: 40px;
        }
        &:last-child {
            margin-bottom: var(--gutter--small);
        }
    }
    .detail-comment {
        padding: var(--gutter--medium) 0;
        font-size: var(--font-size--content);
        &-title {
            display: flex;
            align-items: center;
            &:hover {
                .detail-comment-delete {
                    display: inline-block;
                    cursor: pointer;
                }
            }
        }
        &-creator {
            margin-left: 8px;
        }
        &-delete {
            display: none;
            margin-left: 8px;
            color: var(--color--error);
            font-size: 12px;
        }
        .el-rate {
            margin: 0 var(--gutter--base);
            &__icon {
                margin-right: -4px;
            }
        }
        &-time {
            font-size: var(--font-size--description);
            color: var(--color--secondary--content);
        }
        &-advise {
            word-break: break-all;
            padding: 8px 0 0 32px;
        }
        &-divider {
            float: right;
            margin-top: var(--gutter--base);
            width: calc(100% - 32px);
        }
    }
    .detail-comment-more {
        display: flex;
        justify-content: center;
        margin-top: var(--gutter--medium);
        height: 40px;
        line-height: 1;
        font-size: var(--font-size--content);
    }
}
</style>
