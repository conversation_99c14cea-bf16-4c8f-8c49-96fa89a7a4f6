<template>
    <div
        class="appManage"
        v-if="jurisdicList['archAppManage:view'] === 0"
    >
        <div class="appBox">
            <div class="searchBox">
                <el-form
                    ref="App"
                    :model="App"
                    class="appForm"
                    :label-position="labelPosition"
                    label-width="100px"
                    :inline="true"
                    min-height="800"
                >
                    <el-form-item
                        label="应用类型:"
                        prop="appType"
                    >
                        <el-radio-group
                            v-model="App.appType"
                            :border="false"
                        >
                            <el-radio label="0">全部</el-radio>
                            <el-radio label="1">外采应用</el-radio>
                            <el-radio label="2">自研应用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="应用名称: "
                        prop="name"
                    >
                        <el-input
                            v-model="App.name"
                            class="search-input"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item
                        label="应用状态:"
                        prop="status"
                    >
                        <el-radio-group v-model="App.status">
                            <el-radio label="0">全部</el-radio>
                            <el-radio label="1">准备中</el-radio>
                            <el-radio label="3">审核中</el-radio>
                            <el-radio label="2">已上架</el-radio>
                            <el-radio label="4">已下架</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="应用负责人:"
                        prop="userAccount"
                    >
                        <el-select
                            v-model="App.userAccount"
                            class="search-input"
                            filterable
                            clearable
                            :filter-method="filterMethod"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in App.userAccountOptions"
                                :key="item.user"
                                :label="item.userErp"
                                :value="item.user"
                                class="option"
                            >
                                <div style="height:40px">
                                    <span style="display: inline-block;">{{ item.user }}</span>
                                    <span style="display: block; color: #ccc; font-size:14px">{{ item.orgTierName }}</span>
                                </div>
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div class="flex-center">
                    <el-button
                        type="primary"
                        @click="onSubmit"
                    >
                        搜索
                    </el-button>
                    <el-button @click="resetForm('App')">重置</el-button>
                </div>
            </div>
            <div class="search-btn mt-5">
                <el-button
                    type="primary"
                    @click="dialogVisible = true"
                    v-if="jurisdicList['archAppManage:app:store:add'] === 0 ||
                        jurisdicList['archAppManage:app:third:add'] === 0"
                >
                    创建应用
                </el-button>
            </div>
            <div class="AppTable">
                <el-table
                    :data="AppTable"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="name"
                        label="应用名称"
                        width="180"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                :underline="false"
                                class="toolTip"
                                :disabled="Boolean(scope.row.authApp)"
                                :href="`/open/devCenter/app/${scope.row.code}?appType=${scope.row.appType}`"
                            >
                                {{ scope.row.name }}
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="appTypeName"
                        label="类型"
                        width="120"
                    />
                    <el-table-column
                        prop="statusName"
                        label="状态"
                    />
                    <el-table-column
                        prop="auditStatusName"
                        label="审核状态"
                        :filters="[{ text: '审批通过', value: '审批通过' }, { text: '审批不通过', value: '审批不通过' },
                                   { text: '审批中', value: '审批中' }]"
                        :filter-method="filterHandler"
                    />
                    <el-table-column
                        prop="Principal"
                        label="负责人"
                        width="180"
                    >
                        <template slot-scope="scope">
                            <el-tooltip
                                class="item"
                                effect="dark"
                                :content="scope.row.Principal"
                                placement="top"
                            >
                                <span class="toolTip">{{ scope.row.Principal }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="desc"
                        label="描述"
                        width="180"
                    >
                        <template slot-scope="scope">
                            <el-tooltip
                                class="item"
                                effect="dark"
                                placement="top"
                            >
                                <div
                                    slot="content"
                                    class="toolTipR"
                                >
                                    {{ scope.row.desc }}
                                </div>
                                <span class="toolTip">{{ scope.row.desc }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column
                        fixed="right"
                        label="操作"
                        width="270"
                        prop="permissions"
                    >
                        <template slot-scope="scope">
                            <el-button
                                @click="auditPass(scope.row)"
                                type="text"
                                size="small"
                                v-if="((jurisdicList['archAppManage:storeSelf:approved'] === 0 && scope.row.appType == 2)
                                    || (jurisdicList['archAppManage:thirdParty:approved'] === 0 && scope.row.appType == 1)
                                    && !scope.row.authApp)"
                                :disabled="!(scope.row.permissions &&
                                    scope.row.permissions.some(item => item === 'audited-app'))"
                            >
                                审核通过
                            </el-button>
                            <el-button
                                @click="auditFall(scope.row)"
                                type="text"
                                size="small"
                                v-if="((jurisdicList['archAppManage:storeSelf:failed'] === 0 && scope.row.appType == 2)
                                    || (jurisdicList['archAppManage:thirdParty:failed'] === 0 && scope.row.appType == 1)
                                    && !scope.row.authApp)"
                                :disabled="!(scope.row.permissions &&
                                    scope.row.permissions.some(item => item === 'audit-failed-app'))"
                            >
                                审核不通过
                            </el-button>
                            <el-button
                                @click="putAway(scope.row)"
                                type="text"
                                size="small"
                                v-if="(jurisdicList['archAppManage:storeSelf:online'] === 0 && scope.row.appType == 2)
                                    || (jurisdicList['archAppManage:thirdParty:online'] === 0 && scope.row.appType == 1)"
                                :disabled="!(scope.row.permissions &&
                                    scope.row.permissions.some(item => item === 'online-app'))"
                            >
                                上架
                            </el-button>
                            <el-button
                                @click="soldOut(scope.row)"
                                type="text"
                                size="small"
                                v-if="(jurisdicList['archAppManage:storeSelf:offline'] === 0 && scope.row.appType == 2)
                                    || (jurisdicList['archAppManage:thirdParty:offline'] === 0 && scope.row.appType == 1)"
                                :disabled="!(scope.row.permissions &&
                                    scope.row.permissions.some(item => item === 'offline-app'))"
                            >
                                下架
                            </el-button>
                            <el-button
                                @click="deleteRow(scope.row)"
                                type="text"
                                size="small"
                                v-if="(jurisdicList['archAppManage:storeSelf:del'] === 0 && scope.row.appType == 2)
                                    || (jurisdicList['archAppManage:thirdParty:del'] === 0 && scope.row.appType == 1
                                    && !scope.row.authApp)"
                                :disabled="!(scope.row.permissions &&
                                    scope.row.permissions.some(item => item === 'delete-app'))"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog
            custom-class="j-dialog j-dialog--center devcenter-app-dialog"
            title="创建应用"
            width="728px"
            top="0"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            :before-close="beforeClose"
        >
            <el-row
                type="flex"
                style="align-items: stretch;overflow: hidden;line-height: 24px;"
            >
                <el-col
                    :span="10"
                    style="background: rgba(38,149,241,0.06);"
                >
                    <div style="padding: 24px;">
                        <h3>
                            <i
                                class="prdm-icon-chanpin"
                                style="color: var(--color--primary)"
                            />应用
                        </h3>
                        <p>
                            「应用」是可上架到行云应用商店的软件或者服务。<br>
                        </p>
                        <p v-if="helpDoc">
                            <strong>创建应用前，请仔细阅读<a
                                :href="`${$store.state.url.help}/#/Operation-Guide/appstore/open_platform`"
                                target="_blank"
                            >
                                接入流程
                            </a>。</strong> <br>
                        </p>
                        <span>
                            应用创建后，您可以获得以下功能：
                        </span>
                        <ul style="margin-left: 20px">
                            <li>
                                维护应用的基本信息；
                            </li>
                            <li>
                                开发配置应用；
                            </li>
                            <li>
                                申请上架到应用商店；
                            </li>
                            <!-- <li>
                                - 应用运营情况分析<i>（敬请期待）</i>；
                            </li> -->
                        </ul>
                    </div>
                </el-col>
                <el-col
                    :span="14"
                    style="padding-left: 16px"
                >
                    <el-form
                        ref="form"
                        :model="appForm"
                        :rules="rules"
                    >
                        <el-form-item
                            prop="name"
                            label="应用名称"
                        >
                            <el-input
                                v-model="appForm.name"
                                :placeholder="rules.name.message"
                            />
                        </el-form-item>
                        <el-form-item
                            prop="code"
                            label="唯一标识"
                        >
                            <el-tooltip
                                content="采用微前端接入时，需要和「入口代码」中的moduleKey保持一致"
                                placement="right"
                            >
                                <i
                                    class="jacp-icon-question"
                                    style="color: #c0c4cc;"
                                />
                            </el-tooltip>
                            <el-input
                                v-model="appForm.code"
                                :placeholder="rules.code.message"
                            />
                        </el-form-item>
                        <el-form-item label="类型">
                            <br>
                            <el-radio-group v-model="appForm.appType">
                                <el-radio
                                    label="2"
                                    v-if="jurisdicList['archAppManage:app:store:add'] === 0"
                                >
                                    自研应用
                                </el-radio>
                                <el-radio
                                    label="1"
                                    v-if="jurisdicList['archAppManage:app:third:add'] === 0"
                                >
                                    外采应用
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item
                            prop="desc"
                            label="描述"
                        >
                            <el-input
                                v-model="appForm.desc"
                                type="textarea"
                                :rows="3"
                                :placeholder="rules.desc.message"
                            />
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>

            <span slot="footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    @click="createApp"
                >创建</el-button>
            </span>
        </el-dialog>
        <!-- 审核不通过 -->
        <el-dialog
            title="确认审批不通过吗？"
            :visible.sync="dialogFallVisible"
            width="30%"
            :close-on-click-modal="false"
            style="font-size:16px"
        >
            <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入原因"
                v-model="reason"
            />
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="dialogFallVisible = false">取 消</el-button>
                <el-button
                    type="primary"
                    @click="confirmPutAway"
                >确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
// import CreateAppDialog from './components/createApp';
import DevCenterModel from '../open/model/devCenter';
import Users from '@/models/user';
import { mapState } from 'vuex';
import { getPermissionUicomponents } from '@/modules/option/modules';
import { MessageBox } from 'element-ui';

const rules = {
    name: {
        required: true,
        min: 2,
        max: 10,
        trigger: 'blur',
        message: '可输入2-10个字符',
    },
    code: {
        required: true,
        pattern: /^[a-zA-Z0-9_]{2,20}$/,
        trigger: 'blur',
        message: '限英文字符、下划线和数字，2-20个字符，一旦设置不可更改',
    },
    desc: {
        required: true,
        max: 200,
        message: '请输入应用描述，最多不可超过200个字符',
    },
};
export default {
    // components: { CreateAppDialog },
    data() {
        return {
            radioShow: false,
            App: {
                appType: '0',
                status: '0',
                name: '',
                userAccount: '',
                userAccountOptions: [],
            },
            appForm: {
                appType: '2',
                name: '',
                code: '',
                desc: '',
            },
            rules,
            AppTable: [],
            dialogVisible: false,
            dialogFallVisible: false,
            reason: '',
            labelPosition: 'right',
            applyId: '',
            type: 1,
            flag: true,

        };
    },
    created() {
        if (this.$route.query.create && this.flag) {
            this.dialogVisible = true;
            this.flag = false;
        }
        // 判断当前用户是否管理员/商店管理员 如果是管理员则显示外采应用
        DevCenterModel.isAdmin(1).then((res) => {
            if (res) {
                this.radioShow = true;
            } else {
                this.radioShow = false;
            }
        });
        // Users.getMenuAuthor().then((data) => {
        //     if (data.admin.length > 0) {
        //         this.radioShow = true;
        //     }
        // });
        getPermissionUicomponents().then((res) => {
            if (res['archAppManage:view'] !== 0) {
                MessageBox({
                    title: '提示',
                    message: '您没有权限，如有诉求，请联系管理员',
                    type: 'error',
                });
                this.$router.push({ path: '/' });
            }
        });
        this.init();
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            helpDoc: state => state.app.helpDoc,
        }),
    },
    methods: {
        init() {
            DevCenterModel.searchAllAppInfoList(this.App).then((res) => {
                this.AppTable = res;
                this.AppTable.Principal = '';
                this.AppTable.forEach((item) => {
                    if (item.appTypeName === '第三方应用') {
                        item.auditStatusName = '-';
                    }
                    item.Principal = '';
                    // item.Principal += item.appUserRelList.fliter(item1 => item1.userAccount);
                    item.appUserRelList.forEach((item1, index) => {
                        item.Principal += `${item1.userName}(${item1.userAccount})`;
                        if (index !== item.appUserRelList.length - 1) {
                            item.Principal += ',';
                        }
                    });
                });
            });
        },
        beforeClose() {
            this.closeDialog();
        },
        filterMethod(key) {
            Users.search(key).then((res) => {
                res.forEach((item) => {
                    item.user = `${item.userName}(${item.erp})`;
                });
                // this.App.userAccountOptions = res;
                this.$set(this.App, 'userAccountOptions', res);
            });
        },
        onSubmit() {
            this.init();
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.init();
        },
        auditPass(row) {
            DevCenterModel.audit({ applyId: row.id, opsType: 1, opinion: '' }, row.appType).then((res) => {
                this.AppTable.forEach((item) => {
                    if (item.id === res.id) {
                        this.$set(item, 'permissions', res.permissions);
                        this.$set(item, 'statusName', res.statusName);
                        this.$set(item, 'auditStatusName', res.auditStatusName);
                        if (item.appTypeName === '第三方应用') {
                            item.auditStatusName = '-';
                        }
                    }
                });
                this.$message.success('审核通过');
            });
        },
        auditFall(row) {
            this.dialogFallVisible = true;
            this.reason = '';
            this.applyId = row.id;
            this.type = row.appType;
        },
        confirmPutAway() {
            if (!this.reason) {
                this.$message.error('审核不通过原因还没填写');
                return;
            }
            DevCenterModel.audit({ applyId: this.applyId, opsType: 2, opinion: this.reason }, this.type).then(() => {
                this.$message.success('审核不通过');
            }).catch((err) => {
                console.log(err);
            }).finally(() => {
                this.dialogFallVisible = false;
                this.reason = '';
                this.init();
            });
        },
        putAway(row) {
            DevCenterModel.lineTypeApply({ appId: row.id, lineType: 0, appType: row.appType }).then((res) => {
                this.AppTable.forEach((item) => {
                    if (item.id === res.id) {
                        this.$set(item, 'permissions', res.permissions);
                        this.$set(item, 'statusName', res.statusName);
                        this.$set(item, 'auditStatusName', res.auditStatusName);
                        if (item.appTypeName === '第三方应用') {
                            item.auditStatusName = '-';
                        }
                    }
                });
                this.$message.success('上架成功');
            }).catch((err) => {
                this.$message.err(err);
            });
        },
        soldOut(row) {
            console.log(row);
            DevCenterModel.lineTypeApply({ appId: row.id, lineType: 1, appType: row.appType }).then((res) => {
                this.AppTable.forEach((item) => {
                    if (item.id === res.id) {
                        this.$set(item, 'permissions', res.permissions);
                        this.$set(item, 'statusName', res.statusName);
                        this.$set(item, 'auditStatusName', res.auditStatusName);
                        if (item.appTypeName === '第三方应用') {
                            item.auditStatusName = '-';
                        }
                    }
                });
                this.$message.success('下架成功');
            }).catch((err) => {
                this.$message.err(err);
            });
        },
        deleteRow(row) {
            console.log(row);
            this.$confirm('一旦删除了应用，该应用的所有内容都将会被永久删除。这是一个不可恢复的操作，请谨慎对待！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '再想想',
                type: 'warning',
            }).then(() => {
                DevCenterModel.deleteApp(row.id, row.appType).then(() => {
                    this.init();
                    this.$message.success('删除成功!');
                });
            });
        },
        changeDialog(e) {
            this.dialogVisible = e;
        },
        filterHandler(value, row, column) {
            const { property } = column;
            return row[property] === value;
        },
        // resetForm(formName) {
        //     this.$refs[formName].resetFields();
        // },
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('change-dialog', false);
            this.resetForm('form');
            this.$route.query.create = false;
        },
        createApp() {
            console.log(this.appForm, 'ffffff');
            this.$refs.form.validate((valid) => {
                if (valid) {
                    DevCenterModel.createApp(this.appForm).then(() => {
                        const { code } = this.appForm;
                        this.$nextTick(() => {
                            this.$router.push({ path: `/open/devCenter/app/${code}?appType=${this.appForm.appType}` });
                        });
                        this.closeDialog();
                    });
                }
            });
        },
    },
};
</script>

<style lang="less">
.appManage {
    padding: 10px 50px;
    background: #fafbfc;

    .search-input {
        width: 430px;
    }

    .el-radio-group {
        margin-bottom: 4.5px;

        .el-radio {
            margin-right: 20px;

            &__label {
                font-size: 12px;
            }
        }
    }

    .appBox {
        position: relative;
        padding: 24px;
        background-color: #fff;
        border-radius: 20px;
        min-height: 100vh;

        &:hover {
            box-shadow: 0 3px 16px 0 rgba(48, 49, 51, 0.1);
        }

        .searchBox {
            border-radius: 16px;

            .appForm {
                width: 12 00px;
                // margin: 0 auto;
                position: relative;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;

                .el-form-item {
                    width: calc((100% - 100px) / 2);
                    display: flex;
                    align-items: center;

                    .el-radio-group {
                        margin-bottom: 4.5px;

                        .el-radio {
                            margin-right: 20px;

                            &__label {
                                font-size: 12px;
                            }

                            .el-radio__input {
                                display: none;
                            }
                        }
                    }
                }
            }
        }
    }

    .AppTable {
        margin-top: 50px;

        .toolTip {
            display: inline-block;
            width: 170px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .el-dialog__body {
        padding: 5px 20px;
    }
}

.auditStatusName {
    position: relative;

    &::after {
        content: '';
        font-family: element-icons !important;
        display: block;
        width: 10px;
        height: 10px;
        position: absolute;
        left: 60px;
        top: 13px;
    }
}

.option {
    height: 80px;
}

.toolTipR {
    max-width: 300px;
}
</style>
