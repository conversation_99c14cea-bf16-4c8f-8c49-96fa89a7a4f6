// export { default as Layout } from '@/modules/open/layout.outer';
// export { default as AppStore } from './layout.appStore';
export { default as AppStore } from './layout.appManage';
// export { default as AppDetail } from './layout.appDetail';

export default {
    index: () => import(/* webpackChunkName: "chunck-layout-appstore" */'@/modules/open/layout.outer'),
    // appStore: () => import(/* webpackChunkName: "chunck-layout-appstore" */'./layout.appStore'),
    appManage: () => import(/* webpackChunkName: "chunck-layout-appstore" */'./layout.appManage'),
    appDetail: () => import(/* webpackChunkName: "chunck-layout-appstore" */'./layout.appDetail'),
};
