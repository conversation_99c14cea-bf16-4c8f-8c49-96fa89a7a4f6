import { jacpSecurityHttp } from '@/plugins/http';
export default {
    // 查询应用的标签信息列表
    getAppBlockList() {
        return jacpSecurityHttp.cache().get('/apps/block');
    },
    getAppDetail(appCode) {
        return jacpSecurityHttp.get(`/apps/code/${appCode}`, {
            debounce: false,
        });
    },
    commitAppScore({
        appId,
        score,
        advise,
        recommend,
    }) {
        return jacpSecurityHttp.post(`/app/${appId}/score`, {
            appId,
            score,
            advise,
            recommend,
        }, {
            debounce: false,
        });
    },
    queryAppScore({
        appId,
    }, all = false) {
        return jacpSecurityHttp.get(`/app/${appId}/score?all=${all}`, {
            debounce: false,
        });
    },
    deleteComment(id) {
        return jacpSecurityHttp.delete(`/app/score/${id}`);
    },
};
