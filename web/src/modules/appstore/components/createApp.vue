<template>
    <!-- 创建应用dialog -->
    <el-dialog
        custom-class="j-dialog j-dialog--center devcenter-app-dialog"
        title="创建应用"
        width="728px"
        top="0"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
    >
        <el-row
            type="flex"
            style="align-items: stretch;overflow: hidden;line-height: 24px;"
        >
            <el-col
                :span="10"
                style="background: rgba(38,149,241,0.06);"
            >
                <div style="padding: 24px;">
                    <h3>
                        <i
                            class="prdm-icon-chanpin"
                            style="color: var(--color--primary)"
                        />应用
                    </h3>
                    <p>
                        「应用」是可上架到行云应用商店的软件或者服务。<br>
                    </p>
                    <p v-if="helpDoc">
                        <strong>创建应用前，请仔细阅读<a
                            :href="`${$store.state.url.help}/#/Operation-Guide/appstore/open_platform`"
                            target="_blank"
                        >
                            接入流程
                        </a>。</strong> <br>
                    </p>
                    <ul>
                        <li>
                            应用创建后，您可以获得以下功能：
                        </li>
                        <li>
                            - 维护应用的基本信息；
                        </li>
                        <li>
                            - 开发配置应用；
                        </li>
                        <li>
                            - 申请上架到应用商店；
                        </li>
                        <!-- <li>
                            - 应用运营情况分析<i>（敬请期待）</i>；
                        </li> -->
                    </ul>
                </div>
            </el-col>
            <el-col :span="14">
                <el-form
                    style="padding: var(--gutter--large);"
                    ref="form"
                    :model="appForm"
                    :rules="rules"
                >
                    <el-form-item
                        prop="name"
                        label="应用名称"
                    >
                        <el-input
                            v-model="appForm.name"
                            :placeholder="rules.name.message"
                        />
                    </el-form-item>
                    <el-form-item
                        prop="code"
                        label="唯一标识"
                    >
                        <el-tooltip
                            content="采用微前端接入时，需要和「入口代码」中的moduleKey保持一致"
                            placement="right"
                        >
                            <i
                                class="jacp-icon-question"
                                style="color: #c0c4cc;"
                            />
                        </el-tooltip>
                        <el-input
                            v-model="appForm.code"
                            :placeholder="rules.code.message"
                        />
                    </el-form-item>
                    <el-form-item
                        prop="desc"
                        label="描述"
                    >
                        <el-input
                            v-model="appForm.desc"
                            type="textarea"
                            :rows="3"
                            :placeholder="rules.desc.message"
                        />
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>

        <span slot="footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button
                type="primary"
                @click="createApp"
            >创建</el-button>
        </span>
    </el-dialog>
</template>

<script>
import DevCenterModel from '../../open/model/devCenter';
import { mapState } from 'vuex';

const rules = {
    name: {
        required: true,
        min: 2,
        max: 10,
        trigger: 'blur',
        message: '可输入2-10个字符',
    },
    code: {
        required: true,
        pattern: /^[a-zA-Z0-9_]{2,20}$/,
        trigger: 'blur',
        message: '限英文字符、下划线和数字，2-20个字符，一旦设置不可更改',
    },
    desc: {
        required: true,
        max: 200,
        message: '请输入应用描述，最多不可超过200个字符',
    },
};
export default {
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            appForm: {
                name: undefined,
                code: undefined,
                desc: undefined,
            },
            rules,
        };
    },
    computed: {
        ...mapState({
            helpDoc: state => state.app.helpDoc,
        }),
    },
    methods: {
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('change-dialog', false);
            this.resetForm('form');
        },
        createApp() {
            console.log(this.appForm, 'ffffff');
            this.$refs.form.validate((valid) => {
                if (valid) {
                    DevCenterModel.createApp(this.appForm).then((data) => {
                        this.appList.push(data);
                    });
                    const { code } = this.appForm;
                    this.$nextTick(() => {
                        this.$router.push({ path: `/open/devCenter/app/${code}` });
                    });
                    this.closeDialog();
                    // this.$router.push({ name: 'jacpDevAppSetting', query: { code: this.appForm.code } });
                }
            });
        },
    },
};
</script>

<style>

</style>
