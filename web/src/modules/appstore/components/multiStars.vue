<script>
export default {
    props: {
        count: Number,
        color: {
            type: String,
            default: '#909399',
        },
        size: {
            type: [String, Number],
            default: 12,
        },
    },
    render() {
        const { count, color, size } = this;
        const fontSize = `${size}px`;
        const renderStar = (
            <i
                class="el-icon-star-on"
                style={{
                    color,
                    fontSize,
                }}
            />
        );

        return (
            <div>
                {Array(count).fill(renderStar)}
            </div>
        );
    },
};
</script>
