<template>
    <el-tooltip
        class="item"
        effect="dark"
        :content="app.licenseStatus == 1 ?
            '应用license未找到，不可以使用，请联系应用负责人或商店管理员推动应用后续流程' :
            (app.licenseStatus == 3?'应用已过期，不可以使用，请联系应用负责人或商店管理员推动应用后续流程':'') "
        placement="bottom"
        :disabled="app.licenseStatus != 1 && app.licenseStatus != 3"
    >
        <div
            class="store-app-card"
            @click.stop="goDetail(app)"
            :style="app.licenseStatus == 1 || app.licenseStatus == 3 ? 'background:#eee;cursor: not-allowed':''"
        >
            <div class="store-app-card__left">
                <img
                    class="store-app-card__icon"
                    :src="app.icon"
                >
            </div>
            <div
                class="store-app-card__right"
                style="flex:1"
            >
                <slot
                    name="name"
                >
                    <div
                        class="store-app-card__right-content"
                    >
                        <el-row
                            type="flex"
                            align="middle"
                            class="store-app-card__title"
                            style="justify-content: space-between;"
                        >
                            <div>
                                <div class="store-app-card__name">{{ app.name }}</div>
                                <i
                                    v-if="tag"
                                    :class="[ 'store-app-card__tag', tag ]"
                                />
                            </div>
                            <el-tag
                                type="info"
                                v-if="app.licenseStatus==1 || app.licenseStatus==3"
                            >
                                {{ app.licenseStatus == 1 ? '未授权' : '已过期' }}
                            </el-tag>
                        </el-row>
                        <el-tooltip
                            placement="bottom"
                        >
                            <div
                                slot="content"
                                class="store-app-card__description-tooltip"
                            >
                                {{ app.desc }}
                            </div>
                            <div class="store-app-card__description">{{ app.desc }}</div>
                        </el-tooltip>
                        <slot name="link" />
                    </div>
                </slot>
            </div>
            <slot
                name="action"
                v-if="app.licenseStatus != 1"
            >
                <i
                    :class="star ? 'el-icon-star-on pointer' : 'el-icon-star-off pointer'"
                    @click.stop="starApp"
                />
            </slot>
            <slot name="link">
                <div class="footer">
                    <!-- <div class="store-app-card__peoRight">
                        <template>
                            <el-tooltip
                                class="item"
                                effect="dark"
                                placement="top-start"
                            >
                                <div
                                    slot="content"
                                    class="maintainer"
                                >
                                    {{ appOrgName? `${appOrgName}提供,`:'' }}由{{ people }}维护
                                </div>
                                <span class="maintainer">{{ firstPeople ? `${firstPeople}维护`:'' }}</span>
                            </el-tooltip>
                        </template>
                    </div> -->
                    <!-- <div
                        class="store-app-card__more"
                        v-if="helpDoc">
                        <span
                            :underline="false"
                            v-if="app.helpUrl"
                            @click.stop="linkClick(app.helpUrl)"
                        >
                            <span style="color: #2695F1">查看文档</span>
                            <i class="jacp-icon-a-appmarket-smallarrow" />
                        </span>
                        <span v-else>
                            <span style="color: #ccc">查看文档</span>
                            <i
                                class="jacp-icon-a-appmarket-smallarrow"
                                style="opacity: 0;"
                            />
                        </span>
                    </div> -->
                </div>
            </slot>
        </div>
    </el-tooltip>
</template>

<script>
import { TagPriorityOrder } from '@/modules/root/constant';
import http from '@/plugins/http';
import { event } from '@/plugins/event';
import AppServer from '@/modules/root/models/appServer';
import { mapState } from 'vuex';

export default {
    props: {
        app: { type: Object, default: () => {} },
    },
    data() {
        return {
            star: false,
            people: '',
            firstPeople: '',
            appOrgName: '',
        };
    },
    computed: {
        ...mapState({
            helpDoc: state => state.app.helpDoc,
        }),
        tag() {
            const { tags = [] } = this.app;
            if (!tags.length) return '';

            const tagsSorted = tags.sort((a, b) => TagPriorityOrder[a] - TagPriorityOrder[b]);
            return `jacp-icon-tag-${tagsSorted[0].toLowerCase()}`;
        },
    },
    mounted() {
        this.init();
        // events = event;
        event.$on('change-star', (key) => {
            if (this.app.id === key) {
                this.star = false;
            }
        });
    },
    methods: {
        init() {
            http.get('v1/setting/personal/menus/self/favorite?type=1').then((res) => {
                res.forEach((item) => {
                    if (item.appId === this.app.id) {
                        this.star = true;
                    }
                });
            });
            AppServer.getAppStore([]).then((res) => {
                // let people = '';
                res.forEach((item) => {
                    if (item.id === this.app.id) {
                        this.appOrgName = item.appOrgName;
                        item.appUserRelList.forEach((item1, index) => {
                            if (index === 0) {
                                this.firstPeople = item1.userName;
                            }
                            this.people += item1.userName;
                            if (index !== item.appUserRelList.length - 1) {
                                this.people += '、';
                            }
                        });
                    }
                });
                // this.$set(this, 'people', people);
            });
        },
        goDetail(app) {
            if (app.licenseStatus === 1 || app.licenseStatus === 3) return;
            // this.$router.push({ name: 'jacpAppDetail', params: { code } });
            // this.$router.push({ name: 'jacpDevAppSetting', params: { code } });
            this.$emit('card-click', this.app);
        },
        starApp() {
            this.star = !this.star;
            this.$emit('on-action-click', this.star ? 'addToFavo' : 'removeFromFavo');
        },
        linkClick(url) {
            window.open(url, '_blank');
        },
    },
};
</script>

<style lang="less">
.box-shadow() {
    box-shadow:  0 3px 16px 0 rgba(48,49,51,0.1);
}
.not-allow {
    cursor: not-allowed
}
.store-app-card {
    display: flex;
    // height: fit-content;
    padding: 16px;
    border: 1px solid var(--color--base--hr);
    border-radius: 16px;
    cursor: pointer;
    transition: .3s;
    position: relative;
    &:hover {
        .box-shadow();
        i[class^="jacp-icon-"] {
            opacity: 1;
        }
    }
    &__icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
    }
    &__right {
        padding-left: 8px;
        &-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 40px;
        }
    }
    &__title {
        line-height: 20px;
    }
    &__name {
        color: var(--color--base--content);
        font-size: 14px;
        font-weight: var(--font-weight-default);
        // margin-left: 5px;
    }
    &__tag {
        font-size: var(--font-size--content);
        color: var(--color--error);
        margin-left: var(--gutter--mini);
    }
    &__description,
    &__more {
        font-size: var(--font-size--description);
        margin-top: 4px;
    }
    &__description {
        color: var(--color--secondary--content);
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: normal;
        &-tooltip {
            max-width: 200px;
        }
    }
    .footer {
        position: absolute;
        bottom: 10px;
        display: flex;
        justify-content: space-between;
        width: calc(100% - 55px);
        .store-app-card__more {
            color: var(--color--primary);
            cursor: pointer;
            &:hover {
                // .box-shadow();
                i[class^="jacp-icon-"] {
                    opacity: 0;
                }
            }
            span {
                font-size: 12px;
            }
            i {
                padding-left: var(--gutter--mini);
                color: inherit;
                font-size: inherit;
                transition: .4s;
                opacity: 0;
            }
        }
            &__peoRight {
                font-size: 12px;
                width: 180px;
                color: var(--color--secondary--content);
                display: -webkit-box;
                overflow: hidden;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
                word-break: break-all;
                white-space: normal;
            }
    }


& > i {
    position: absolute;
}
.el-icon-star-on {
    color: #facd91;
    font-size: 22px;
    right: 16px;
    top: 10px;
}
.el-icon-star-off{
    font-size: 18px;
    right: 17px;
    top: 12px;
}
.pointer:hover {
    cursor: pointer;
}
}
span.maintainer {
    font-size: 12px;
    color: var(--color--base--content);;
}
</style>
