import Layout from './layout';

export const routes = {
    path: '/appManage',
    name: 'jacpAppStoreIndex',
    component: Layout.index,
    // redirect: { name: 'jacpAppStore' },
    children: [{
        // path: 'index',
        path: '',
        name: 'jacpAppStore',
        component: Layout.appManage,
    }, {
        // path: 'detail',
        path: ':code',
        name: 'jacpAppDetail',
        component: Layout.appDetail,
        props: route => ({
            disabledEntrance: !!route.query.ex,
        }),
    }],
};

/**
 * 导出的是作为子模块，子模块的路由出口在module/root/layoutIndex的router-view中
 */
export default [];
