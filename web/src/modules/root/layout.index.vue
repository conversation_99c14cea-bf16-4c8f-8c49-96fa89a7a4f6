<template>
    <el-container class="jacp-root">
        <el-alert
            title="浏览器兼容性提示"
            type="warning"
            v-if="showNavigatorAlert"
            show-icon
        >
            <div class="el-alert__description">
                当前浏览器版本过低，为保证最佳使用体验，建议使用 <a
                    href="https://www.google.cn/chrome/browser/desktop/index.html"
                    target="_blank"
                >Chrome浏览器</a>
            </div>
        </el-alert>
        <div
            @mouseover="onHover"
            @mouseleave="onLeave"
            v-show="asideMenu"
            style="z-index: 99;"
        >
            <LocalAsideMenu />
        </div>
        <el-container style="position: relative;">
            <el-main class="main-panel">
                <div
                    @mouseover="onHover"
                    @mouseleave="onLeave"
                >
                    <LocalRootHeader>
                        <span slot="icon">
                            <i
                                class="el-icon-arrow-left main-panel-left"
                                @click="hiddenAsideMenu"
                                :class="isHidden ? 'hidden' : 'visible'"
                            />
                            <i
                                class="el-icon-s-unfold main-panel-unfold"
                                v-if="!asideMenu"
                                @click="visibleAsideMenu"
                            />
                        </span>
                    </LocalRootHeader>
                </div>
                <!-- 子应用路由添加之后自动进入 router 渲染 -->
                <router-view
                    v-if="!fromNonExisting"
                    ref="child"
                    class="main-inner"
                />
                <local-module-loader v-else />
            </el-main>

            <!-- <el-footer
                class="footer-panel"
                v-if="$route.name === 'myzone'"
                height="48px"
            >
                <span>{{ copyright }}</span>
                <span>{{ powered }}</span>
            </el-footer> -->
            <jacp-debug :show-debug.sync="showDebug" />
        </el-container>

        <!-- 系统公告 -->
        <jacp-announcement
            :expired-time="announcementExpiredTime"
            v-if="showAnnouncement"
            title="满意度调查"
            :minimized="minimized"
        >
            <csi />
        </jacp-announcement>
        <!-- 水印浮层 -->
        <div
            ref="water"
            class="jacp-water"
        >
            <canvas
                id="canvas"
                style="display: none"
                width="240"
                height="240"
            />
        </div>

        <!-- 右下角的帮助按钮（加群反馈，应用评价） -->
        <!-- <div class="feedback-wrapper" v-show="currentModule">
            <ul class="feedback-list">
                <li
                    class="feedback-item"
                    v-if="currentModuleInfo.techSupportNumber"
                >
                    加群反馈
                    <a
                        style="padding-top: 8px;"
                        :href="joinChat(currentModuleInfo.techSupportNumber)"
                    >
                        {{ currentModuleInfo.techSupportNumber }}
                    </a>
                </li>
                <li
                    class="feedback-item"
                    v-if="currentModuleInfo.helpUrl"
                >
                    <a
                        :href="currentModuleInfo.helpUrl"
                        style="color: inherit;"
                        target="_blank"
                    >帮助文档</a>
                </li>
                <li class="feedback-item" @click="toAppComment">应用评价</li>
            </ul>
            <i
                class="jacp-icon-feedback"
            />
        </div> -->
    </el-container>
</template>

<script>
import bowser from 'bowser';
import '@jacpbiz/menu-business/menu-business.css';

import SecurityModel from '@/models/security';
import LocalAsideMenu from '$module/components/asideMenu';
import LocalModuleLoader from '$module/components/moduleLoader';
import LocalRootHeader from '$module/components/rootHeader';
import csi from './csi.vue';

import interact from '@/utils/interact';
import { joinChat } from '@/utils/timline';
import { openLink } from '@/plugins/utils';
import DevCenterModel from '@/modules/open/model/devCenter';

export default {
    name: 'JacpAdmin',
    props: {
        fromNonExisting: { type: Boolean },
    },
    components: {
        LocalAsideMenu,
        LocalRootHeader,
        LocalModuleLoader,
        csi,
    },
    data() {
        // 关于行云 将来会是一个弹出框，版本说明和帮助中心是跳转链接
        return {
            announcementExpiredTime: Date.parse(new Date('2021-09-30')),
            showAnnouncement: false,
            showNavigatorAlert: false,
            showDebug: false,
            showMenuSetting: false,
            value: [],
            // showNotFound: false,
            copyright: process.env.VUE_APP_COPYRIGHT,
            powered: process.env.VUE_APP_POWERED,
            currentModuleInfo: {},
            asideMenu: true,
            isHidden: true,
        };
    },
    computed: {
        minimized() {
            const csiPeriod = localStorage.getItem('csi');
            return csiPeriod === '2021Q3';
        },
        currentModule() {
            const moduleKey = this.$route.fullPath
                .split('/')[1]
                .replace('#', '');
            return this.$store.getters['root/getActiveModuleByCode'](moduleKey);
        },
    },
    created() {
        if (!bowser.check({
            chrome: '50',
            firefox: '50',
            safari: '1',
            opera: '80',
        }, true)) {
            this.showNavigatorAlert = true;
        }
    },
    mounted() {
        // 全局调试事件 ctrl+/
        // document.addEventListener('keydown', this.openDebugDialog, false);
        // this.addWaterMarker(this.$store.state.user.erp);

        const feedback = this.$el.querySelector('.feedback-wrapper');
        if (feedback) {
            interact(feedback).draggable(true);
        }
    },
    watch: {
        currentModule(val) {
            if (val) {
                if (this.$route.fullPath.split('/')[1].replace('#', '') === 'archSpace') {
                    DevCenterModel.getBasicInfo(val.appId).then((data = {}) => {
                        if (data.appSlotList) {
                            this.$store.commit('extension/setextensionList', data.appSlotList);
                        }
                        this.currentModuleInfo = data;
                    });
                }
            }
        },
    },
    methods: {
        joinChat,
        openDebugDialog(e) {
            if (!this.showDebug && e.ctrlKey && e.keyCode === 191) {
                SecurityModel.checkPermission(this.$store.state.user.erp, 'change-user').then((data) => {
                    if (data) {
                        this.showDebug = true;
                    } else {
                        this.showDebug = false;
                    }
                });
            }
        },
        addWaterMarker(erp) {
            const layer = this.$refs.water;
            const can = layer.querySelector('#canvas');

            const cans = can.getContext('2d');
            cans.rotate(-20 * Math.PI / 180);
            cans.font = '400 12px PingFangSC-Regular';
            cans.fillStyle = 'rgba(17, 17, 17, 0.06)';
            cans.textAlign = 'left';
            cans.textBaseline = 'Middle';
            cans.fillText(erp, can.width / 3 - 40, can.height / 2, 240);
            layer.style.backgroundImage = `url(${can.toDataURL('image/png')})`;
        },
        toAppComment() {
            if (!this.currentModule) {
                return;
            }

            openLink({
                route: {
                    name: 'jacpAppDetail',
                    params: { code: this.currentModule.code },
                    query: { tab: 'comment' },
                },
                isPopup: true,
            });
        },
        hiddenAsideMenu() {
            this.asideMenu = false;
            this.isHidden = true;
        },
        visibleAsideMenu() {
            this.asideMenu = true;
        },
        onHover() {
            if (this.asideMenu) {
                this.isHidden = false;
            }
        },
        onLeave() {
            this.isHidden = true;
        },
    },
};

</script>

<style lang='less'>
@import '~@/theme/var.less';
.jacp-root{
    // min-width: 800px;
    .main-panel{
        background-color: @mainBgColor !important;
        height: 100vh;
        padding: 0;
        min-width: 1080px;
        display: flex;
        flex-direction: column;
        &-left {
            background-color: rgba(255,255,255,var(--tw-bg-opacity));
            border-radius: 50%;
            height: 20px;
            font-size: 1rem;
            position: absolute;
            width: 20px;
            z-index: 200;
            line-height: 20px;
            text-align: center;
            color: var(--color--primary);
            left: -9px;
            top: 13px;
            box-shadow: 0 1px 5px 0 rgba(0,0,0,.13);
            cursor: pointer;
        }
        &-unfold {
            font-size: 1rem;
            width: 28px;
            z-index: 200;
            line-height: 28px;
            text-align: center;
            color: #909399;
            font-size: 23px;
            margin-right: 6px;
            cursor: pointer;
            &:hover {
                background: #f0f2f5
            }
        }
    }
    .main-inner{
        overflow: auto;
        height: 100%;
    }

    .footer-panel{
        background-color: #ffffff;
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 48px;
        line-height: 48px!important;
        font-size: 12px;
        color: @fontDisabledColor;
        letter-spacing: 0;
        display: flex;
        justify-content: space-between;
        padding: 0 32px;
        z-index: 2;
        & span{
            display: inline-block;
            width: fit-content;
        }
    }
    .main-inner-loading{
        .el-loading-text{
            color: var(--color--secondary--content);
        }
        .el-loading-spinner i{
            font-size: 48px;
        }
    }
    .feedback {
        &-wrapper {
            position: fixed;
            z-index: 1001;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            bottom: 72px;
            right: 32px;
            width: 40px;
            height: 40px;
            background: var(--color--primary);
            box-shadow:  0 2px 12px -3px rgba(45,122,185,0.83);
            font-size: 32px;
            border-radius: 50%;
            user-select: none;
            & i {
                color: #fff;
                font-size: 18px;
            }
            &::before {
                content: ' ';
                position: absolute;
                width: 64px;
                height: 64px;
                cursor: grab;
            }
        }
        &-wrapper:hover .feedback-list {
            display: block;
        }
        &-list {
            display: none;
            position: absolute;
            right: 48px;
            margin: 0;
            padding: 0;
            min-width: max-content;
            background: #fff;
            border-radius: 4px;
            border: 1px solid #ebeef5;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            word-break: break-all;
        }
        &-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: var(--gutter--base) var(--gutter--medium);
            line-height: 1;
            color: var(--color--regular--content);
            font-size: 14px;
            list-style: none;
            cursor: pointer;
            &:hover {
                background: #ecf5ff;
                color: #66b1ff;;
            }
        }
    }
    .feedback-item + .feedback-item {
        border-top: var(--border--hr);
    }
}

.jacp-water {
    width: 100vw;
    height: 100vh;
    position: absolute;
    z-index: 9999;
    pointer-events: none;
}
.hidden {
    visibility: hidden;
}
.visible {
    visibility: visible;
}
</style>
