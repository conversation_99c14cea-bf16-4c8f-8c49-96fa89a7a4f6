<template>
    <el-container :class="scopedClass.cn">
        <el-header
            :class="scopedClass.el('header').cn"
            height="48px"
        >
            <jacp-text
                size="16"
                font-weight="600"
            >
                {{ $t('jacp.module.title') }}
            </jacp-text>
            <div
                style="float: right;font-size: var(--font-size--content)"
            >
                <!-- <a
                    href="/appStore"
                    target="_blank"
                    class="j-link j-mgr16"
                >
                    <jacp-icon name="jacp-icon-a-icon-appmaket" />
                    应用商店
                </a> -->

                <a
                    href="/open"
                    target="_blank"
                    class="j-link"
                >
                    <jacp-icon
                        name="jacp-icon-a-icon-appopen"
                    />
                    开放平台
                </a>
            </div>
        </el-header>
        <el-main style="padding: 0;">
            <local-menu-define />
        </el-main>
    </el-container>
</template>
<script>
import LocalMenuDefine from './components/menu/menuDefine';
import { mapState } from 'vuex';
import { getPermissionUicomponents } from '@/modules/option/modules';

export default {
    name: 'JacpLayoutModule',
    props: {
        menuScope: {
            type: Number,
            default: 1,
        },
    },
    components: {
        LocalMenuDefine,
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
        }),
    },
    mounted() {
        this.init();
    },
    data() {
        return {
            scopedClass: this.$scopedClass('module-layout'),
        };
    },
    methods: {
        init() {
            getPermissionUicomponents();
        },
    },
};
</script>
<style lang="less">
.module-layout{
  background-color: #fff;
  &__header{
    border-bottom: var(--border--hr);
    line-height:var(--height--top-header);
    padding: 0 var(--gutter--large);
  }
  .j-link [class^=jacp-icon] {
    color: var(--color--secondary--content);
  }
}
</style>
