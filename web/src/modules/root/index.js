import routes from './routes';
import store from './store';
import {
    registerLocalModules,
    registerRemoteModules,
} from './combineModules';
import { User } from '@/models/user';
import { MessageBox } from 'element-ui';
import Cookies from 'js-cookie';

export default {
    routes,
    store,
    init() {
        registerLocalModules();
        User.getInfo().then((response) => {
            // 在main.js里调用
            registerRemoteModules();
            if (!Cookies.get('LastTime')) {
                Cookies.set('LastTime', true, {
                    expires: 1,
                });
                User.verifyLastTime(response.erp).then((res) => {
                    if (res.isRemind) {
                        MessageBox.alert(res.message, '提示', {
                            confirmButtonText: '确定',
                            type: 'warning',
                        });
                    }
                });
            }
        });
    },
};
