import router from '$platform.router';
import AppModule from '$module/models/appModule';
import appServer from '$module/models/appServer';
import ModuleRender from '$module/components/ModuleRender';
import Iframe from '$module/components/iframe';
import { patchDocumentTitle, toCamelCase } from '.';


let currentModule;
// 不会被卸载样式的模块：团队空间里用到了产品管理的一个组件，但是进入团队空间的时候，产品管理这个模块的css不能被移除
const permanentStyleModules = ['prdm'];
router.afterEach((to) => {
    // 离开模块的时候恢复一下title
    patchDocumentTitle(to);
    if (currentModule && permanentStyleModules.includes(currentModule.key)) {
        return;
    }
    // 离开模块的时候主动移除
    if (currentModule
        && currentModule.resource
        && !to.matched.some(item => item.path.includes(currentModule.key))) {
        // console.log('【afterEach】离开模块时卸载样式', currentModule.resource);

        currentModule.resource.removeStyle();
    }
});
// TODO: 这里还是耦合了appModule
// rootName 为模块的根路由，其实也可以改成全局的router钩子处理
const rootRouteBeforeEnter = (rootName, module) => (to, from, next) => {
    // 进入根路由则自动寻找需要进入的子路由
    // byLGH: app 类型走mount方式自己解析 route
    if (to.name === rootName && module.type === 'page') {
        // eslint-disable-next-line no-use-before-define
        const app = AppModule.getInstance(module.key);
        // 当 topRooter 与 to.name 相同时, 原逻辑会造成无限循环
        if (to.name === app.register.topRooter) {
            next();
        } else {
            next({ name: app.register.topRooter });
        }
        appServer.getAppServer();
        return;
    }
    if (currentModule && currentModule.resource && currentModule !== module) {
        // console.log('【rootRouteBeforeEnter】卸载上一个模块样式', currentModule.resource);
        if (!permanentStyleModules.includes(currentModule.key)) {
            currentModule.resource.removeStyle();
        }
    }

    currentModule = module;
    if (module.type !== 'iframe') {
        module.load('load', { autoApplyStyle: true }).then(() => {
            next();
        });
    } else {
        next();
    }
};


export function isValidRootRoute(rootRoute = [], moduleIns = {}) {
    if (!Array.isArray(rootRoute) || !rootRoute.length) {
        return false;
    }
    if (rootRoute.length > 1) {
        return false;
    }
    if (!rootRoute[0].path || rootRoute[0].path !== moduleIns.key) {
        return false;
    }
    return true;
}

// TODO: 需要提供类似激活模块的方式，允许模块自己处理加载和移除的时机？
function createSpaceRouter(module = {}, children = []) {
    // const originRootRoutes = module.routes;
    const { key, name: moduleName, type } = module;
    const name = toCamelCase(`root_${key}`);

    return {
        path: type === 'app' ? `${key}*` : key,
        name,
        component: ModuleRender,
        children,
        beforeEnter: rootRouteBeforeEnter(name, module),
        meta: {
            title: moduleName,
            type,
        },
        props: () => ({ moduleKey: key }),
    };
}

export const initRootRoutes = (routes, module) => {
    // const originRootRoutes = routes;
    // 如果模块输出了符合条件的根路由就直接使用，不符合条件的进行一层根路由包裹
    if (isValidRootRoute(routes, module)) {
        routes[0].path = ''; // 等价于子路由 path 为空, 且能保留原 beforeEnter钩子，后续流程统一处理
        // const originalBeforeEnter = originRootRoutes[0].beforeEnter;
        // originRootRoutes[0].beforeEnter = rootRouteBeforeEnter(originRootRoutes[0].name, module, originalBeforeEnter);
        // return [originRootRoutes, 'root'];
    }
    // 创建一层顶级路由
    const moduleRouter = createSpaceRouter(module, routes);

    return ['root', moduleRouter];
};
export const createIframeRoute = (module) => {
    const {
        key, name: moduleName, type, url: src,
    } = module;
    const name = toCamelCase(`root_${key}`);
    return {
        path: `${key}/(.*)?`,
        name,
        component: Iframe,
        beforeEnter: rootRouteBeforeEnter(name, module),
        meta: {
            title: moduleName,
            type,
        },
        props: () => ({ src }),
    };
};
