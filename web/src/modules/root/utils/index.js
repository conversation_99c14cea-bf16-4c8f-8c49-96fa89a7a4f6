import Vue from 'vue';
import { initRootRoutes, createIframeRoute } from '$module/utils/rootRoutes';
import store from '$platform.store';
import router from '$platform.router';
// util
import { StorageQueue } from '@/utils/localStorage';

export * from './rootRoutes';
export const timeoutFn = async (time, label) => {
    await new Promise(res => setTimeout(res, time));
    console.warn(label);
    return label;
};

export const footprint = Vue.observable(new StorageQueue({
    key: 'jacp_module_footprint',
}).init());

export const updateFootprint = (moduleKey) => {
    footprint.add(moduleKey);
    footprint.setItem();
};

export function patchDocumentTitle(route = {}) {
    document.title = route.meta?.title
        ? `${process.env.VUE_APP_TITLE}-${route.meta?.title}`
        : process.env.VUE_APP_TITLE;
}

export function toCamelCase(str = '') {
    return str.replace(/(?:(-|_)\w)/g, a => a.slice(1).toUpperCase());
}
export function getCurrentModuleKey(pathname = window.location.pathname) {
    const paths = pathname.split('/');
    return ['en', 'zh-cn'].includes(paths[1]) ? paths[2] : paths[1];
}

// initial
export function initAppModule(module, pkg) {
    Object.assign(module.metadata, pkg);
    router.addRoute(
        ...initRootRoutes([], module),
    );
}
export function initPageModule(module, pkg) {
    const { routes, store: storeConfig } = pkg;
    if (routes) {
        // const parentRouteName = toCamelCase(`root_${module.key}`);
        // 调用的addRoutes()方法是router自带的原生方法，是动态添加路由的，它并没有删除之前路由中原有的路由,我们需要在动态添加路由时，清空一下之前的路由，就可以防止报这个错误，否则开发环境会被warning卡死
        // resetRouter(router);
        // byLGH: vue-router版本要能高一点，直接就支持 parentRoute了。然而，所以，为什么没有清空呢？
        router.addRoute(
            ...initRootRoutes(routes, module),
        );
    }
    // 注册 vuex 模块
    if (storeConfig) {
        Object.keys(storeConfig).forEach((storeModuleName) => {
            store.registerModule(storeModuleName, storeConfig[storeModuleName]);
        });
    }
}


// add preload
export function manualModuleLoader({
    autoApplyScript = false,
    autoApplyStyle = false,
    parallel = true,
    timeout = 0,
} = {}) {
    return async (module) => {
        let defer;
        if (module.type === 'iframe') {
            return null;
        }
        if (!module.resource) {
            return null;
        }
        if (!parallel) {
            module.load(autoApplyScript ? 'load' : 'preload', { autoApplyStyle });
            defer = module.hooks.complete;
        } else {
            defer = module.load(autoApplyScript ? 'load' : 'preload', { autoApplyStyle }).catch((error) => {
                console.trace(error);
            });
        }
        return timeout > 0 ? Promise.race([
            timeoutFn(
                timeout,
                `[JAGILE]${module.name}load超时${timeout}s，可能是js资源访问不到或者未执行到JModule.define`,
            ),
            defer,
        ]) : defer;
    };
}


export const initIframeModule = (module = {}) => {
    const moduleRoute = createIframeRoute(module);
    router.addRoute('root', moduleRoute);
};
