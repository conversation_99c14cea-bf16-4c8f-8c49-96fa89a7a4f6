import Vue from 'vue';
// import store from '$platform.store';
import { workspaceManager } from '@/modules/myzone/models/workspaceManager';

export const EXTENSION_TYPE = {
    GLOBAL: '$globalComponents',
    WORKSPACE: '$workspace',
    MOUNT_POINT: '$extensions',
};
// 注册全局 myzone 配置
export function registerExtensionPoint(module, pkg) {
    const { exports: moduleExports = {} } = pkg;
    // 注册扩展点
    const globals = moduleExports[EXTENSION_TYPE.GLOBAL];
    // 【全局组件】直接注册
    if (globals) {
        console.log('globals==', globals);
        Object.keys(globals).forEach((name) => {
            const comp = globals[name];
            const componentName = comp.name || name;
            Vue.component(componentName, comp);
        });
    }
    // 【个人工作台】
    if (moduleExports[EXTENSION_TYPE.WORKSPACE]) {
        console.log('【个人工作台】==', module, moduleExports[EXTENSION_TYPE.WORKSPACE]);
        workspaceManager.addWidgets(module, moduleExports[EXTENSION_TYPE.WORKSPACE]);
    }
}
