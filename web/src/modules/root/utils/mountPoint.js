import { EXTENSION_TYPE } from '@/modules/root/utils/extension';
import { uuid } from '@/plugins/utils';

export const mountPointCache = {};
export const bootstraped = {};
export const mountTimes = {};

export function initAndGetArea(module, wrapped = false) {
    let pointKey;
    let pointElId;
    if (typeof module === 'string') {
        pointKey = module;
    }
    pointKey = module.key;
    if (!mountPointCache[pointKey]) {
        // vue $mount的时候原始node会被替换导致无法移除，改成包裹一层容器，移除的时候先移除firstChild然后再移除wrapper本身
        const divWrapper = document.createElement('div');
        if (wrapped) {
            pointElId = uuid(pointKey);
            const div = document.createElement('div');
            div.id = pointElId;
            divWrapper.appendChild(div);
        }
        mountPointCache[pointKey] = divWrapper;
    }
    return mountPointCache[pointKey];
}

function removeModulePointCache(key) {
    const div = mountPointCache[key];

    if (div && div.remove) {
        div.remove();
    }

    mountPointCache[key] = undefined;
}
export function unmountModule(module = {}) {
    // console.log('---before unmount module', mountPointCache);
    const result = module.metadata?.unmount?.(module, mountPointCache[module.key]);
    if (module.metadata?.cacheMountPoint !== true) {
        removeModulePointCache(module.key);
    }
    // console.log('---after unmount module', mountPointCache);
    return result;
}


// 挂载点的挂载和移除
// FIXME: joyspace的资源在load以后，执行JModule.require的时候取不到export了
export async function mountModulePoint(modulePointKey, module, el, contextParams) {
    const extension = module.metadata?.exports || await JModule.require(module.key) || {};
    if (!extension) {
        return null;
    }
    const extensionList = extension[EXTENSION_TYPE.MOUNT_POINT] || extension.$extentions; // FIXME: 以前单词拼错了
    if (Array.isArray(extensionList)) {
        const mountPoint = extensionList
            .find(mountPointItem => mountPointItem.key === modulePointKey);
        if (mountPoint) {
            return mountPoint?.mount?.(module, el, contextParams);
        }
    }
    return null;
}

export async function unmountModulePoint(modulePointKey, module, el) {
    const extension = module.metadata?.exports || await JModule.require(module.key) || {};
    if (module.resource && module.resource.styleMounted) {
        module.resource.removeStyle();
    }

    if (extension && Array.isArray(extension[EXTENSION_TYPE.MOUNT_POINT])) {
        const mountPoint = extension[EXTENSION_TYPE.MOUNT_POINT]
            .find(mountPointItem => mountPointItem.key === modulePointKey);
        if (mountPoint) {
            const result = mountPoint?.unmount?.(module, el);
            removeModulePointCache(modulePointKey);
            return result;
        }
    }
    return null;
}
