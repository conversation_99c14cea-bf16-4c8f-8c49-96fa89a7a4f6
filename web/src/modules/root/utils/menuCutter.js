import debounce from 'lodash/debounce';
import isEqual from 'lodash/isEqual';
import { addStyle, erd } from '@/plugins/utils.dom';

// 主菜单最多显示5个应用 + 当前应用的菜单（根据页面变化动态显示隐藏）+ 更多
// 1. 高度进行限制
// 2. 直接在菜单的样式中>5的menuItem进行样式上的隐藏：:nth-child(5) ~ div
// 3. 在切换应用的时候需要在已收藏的菜单内进行查找的时候，index>5的会被视为查找不到，然后该菜单会追加在fixedMenu中进行显示


export default class MenuCutter {
    constructor(el, options = {}, listeners = {
        // onLengthChanged = () => {},
    }) {
        this.el = el;
        this.styleMountedEl = null;
        this.options = options;
        this.listeners = listeners;
    }

    init() {
        if (!this.el) {
            console.debug('[菜单截取内容异常]容器未找到，菜单长度截取失败', this);
            return;
        }
        this.resize(this.el);
        erd.listenTo(this.el, debounce(this.resize.bind(this), 100));
    }

    setOption(key, value) {
        if (!isEqual(this.options[key], value)) {
            this.options[key] = value;
            this.resize(this.el);
        }
    }

    resize(element) {
        const styleContent = this.getStyleTemplate(this.getLenInElView(element));
        // console.log('resize', styleContent);
        if (this.styleMountedEl) {
            this.styleMountedEl.textContent = styleContent;
            return;
        }
        this.styleMountedEl = addStyle(styleContent);
    }

    getLenInElView(element) {
        const height = element.offsetHeight - this.options.cut * this.options.itemHeight;
        const n = Math.floor(height / this.options.itemHeight);
        return n;
    }

    // 计算当前容器可容纳的item个数
    getStyleTemplate(realLength) {
        const {
            min = 1, max = 5,
        } = this.options;
        const realMin = realLength >= min ? realLength : min;
        const n = Math.min(realMin, max);
        if (this.listeners?.onLengthChanged) {
            this.listeners.onLengthChanged(n);
        }
        return `.jacp-aside-menu__menu-view--dynamic.jacpbiz-menu--shrink.jacpbiz-menu [level='1']:nth-child(${n}) ~ div{
                display: none !important;
            }
          
            `;
    }

    destroy() {
        erd.removeAllListeners(this.el);
        this.styleMountedEl.remove();
        this.el = null;
    }
}
