/* eslint-disable func-names */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-use-before-define */
// 通过原生构造函数 - PopStateEvent，创建一个 popstate 事件对象
function createPopStateEvent(state, originalMethodName) {
    let evt;
    try {
        evt = new PopStateEvent('popstate', {
            state,
        });
    } catch (err) {
        evt = document.createEvent('PopStateEvent');
        evt.initPopStateEvent('popstate', false, false, state);
    }
    evt.__jagile__ = true;
    evt.__jagile__originalName__ = originalMethodName;
    return evt;
}
// 重写 updateState、replaceState 方法，通过 window.dispatchEvent 方法，手动触发 popstate 事件
function patchedUpdateState(updateState, methodName) {
    return function (...args) {
        const urlBefore = window.location.href;
        const result = updateState.apply(this, args);
        const urlAfter = window.location.href;

        if (urlBefore !== urlAfter) {
            window.dispatchEvent(createPopStateEvent(window.history.state, methodName));
        }
        return result;
    };
}
// singleSpa进行全局复写了pushState，目前看上去没有必要
// window.history.pushState = patchedUpdateState(window.history.pushState, 'pushState');
// window.history.replaceState = patchedUpdateState(window.history.replaceState, 'replaceState');


export function navigateToUrl(obj) {
    let url;
    if (typeof obj === 'string') {
        url = obj;
    } else if (this && this.href) {
        url = this.href;
    } else if (
        obj
    && obj.currentTarget
    && obj.currentTarget.href
    && obj.preventDefault
    ) {
        url = obj.currentTarget.href;
        obj.preventDefault();
    } else {
        console.warn('[JAGILE]菜单跳转地址无效.URL：', url);
        return;
    }

    const current = new URL(window.location.href);
    const destination = new URL(url);

    if (url.indexOf('#') === 0) {
        window.location.hash = destination.hash;
    } else if (current.host !== destination.host && destination.host) {
        window.location.href = url;
    } else if (
        destination.pathname === current.pathname
    && destination.search === current.search
    ) {
        window.location.hash = destination.hash;
    } else {
        const patchedPushState = patchedUpdateState(window.history.pushState, 'pushState');
        // 切应用的时候需要手动触发popstate
        patchedPushState.call(window.history, null, null, url);
        // window.history.pushState(null, null, url);
    }
}
