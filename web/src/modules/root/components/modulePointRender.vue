<template>
    <div
        :class="scopedClass.cn"
    >
        <div
            v-if="addon.meta && addon.meta.type === 'extension'"
            :class="scopedClass.el('extension').cn"
            v-loading="!currentModule"
        />
        <!-- TODO: searchParams -->
        <local-iframe
            v-else-if="addon.meta && addon.meta.type === 'iframe'"
            v-bind="$attrs"
            :src="addon.meta.value"
        />
        <component
            v-else-if="addon.meta && addon.meta.type === 'component'"
            v-bind="[$attrs, contextParams]"
            :is="addon.meta.value"
        />
        <el-alert
            v-if="addon.meta && addon.meta.error"
            :title="addon.meta.error"
            :closable="false"
            type="error"
        />
    </div>
</template>
<script>
import LocalIframe from '@/modules/root/components/iframe';
import {
    initAndGetArea,
    mountModulePoint,
    unmountModulePoint,
} from '$module/utils/mountPoint';
import {
    manualModuleLoader,
} from '$module/utils';
import { Addon } from '@/models/app';

const DefaultLoaderOption = {
    autoApplyScript: true,
    autoApplyStyle: true,
};
export default {
    name: 'JacpModulePointRender',
    inheritAttrs: false,
    components: { LocalIframe },
    props: {
        addon: { type: Addon, required: true },
        contextParams: {
            type: Object,
            default: () => ({}),
        },
        loaderOptions: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            currentModule: null,
            scopedClass: this.$scopedClass('jacp-module-point'),
        };
    },
    computed: {
        moduleKey() {
            return this.addon?.app?.appCode;
        },
        modulePointKey() {
            const { addon } = this;
            if (addon.meta && addon.meta.type === 'extension') {
                return this.addon.meta.value;
            }
            return undefined;
        },
    },
    mounted() {
        this.initModule();
    },
    methods: {
        async initModule() {
            if (!this.modulePointKey) {
                return;
            }
            const {
                moduleKey,
                modulePointKey,
                contextParams,
                addon,
            } = this;
            const container = this.$el.querySelector('.jacp-module-point__extension');
            if (container) {
                container.appendChild(initAndGetArea(modulePointKey));
            }
            const loaderOptions = {
                ...DefaultLoaderOption,
                ...this.loaderOptions,
            };

            this.currentModule = await JModule.getModuleAsync(moduleKey, loaderOptions.timeout)
                .catch(() => {
                    addon.meta.error = `获取appCode为【${moduleKey}】时超时，该应用不存在或者未注册。`;
                });
            if (this.currentModule) {
                const loader = manualModuleLoader(loaderOptions);
                await loader(this.currentModule);
                mountModulePoint(modulePointKey, this.currentModule, container, contextParams);
            }
        },
    },
    beforeDestroy() {
        if (this.modulePointKey) {
            const container = this.$el.querySelector('.jacp-module-point__extension');
            unmountModulePoint(this.modulePointKey, this.currentModule, container);
            this.currentModule = undefined;
        }
    },
    watch: {
        // 如果是extension类型的，就获取模块和组件
        modulePointKey: {
            handler(newVal, oldVal) {
                if (!newVal || newVal === oldVal) {
                    return;
                }
                this.$nextTick(() => {
                    this.initModule();
                });
            },
        },
    },
};
</script>
<style lang="less">
.jacp-module-point{
    &__extension{
        min-height: 72px;
    }
}
</style>
