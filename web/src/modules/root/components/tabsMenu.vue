<script>
import {
    Menu, MenuItem,
} from '@jacpbiz/menu-business';

const moreMenuItem = menuData => new MenuItem({
    name: '更多',
    ...menuData,
});
export default {
    name: 'JacpTabsMenu',
    inheritAttrs: false,
    props: {
        menu: { type: Menu },
        min: { type: Number, default: 2 },
        max: { type: Number, default: Infinity },
        size: {
            type: String,
            default: 'small',
            validator(val) {
                return ['small', 'mini'].includes(val);
            },
        },
    },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-tabs-menu'),
        };
    },
    computed: {
        // TODO: 高高
        activedMenuId() {
            return this.menu.activedMenu?.id;
        },
    },
    methods: {
        async activeMenu(menuItemId) {
            const menuItem = this.menu.find(menuItemId) || {};
            this.menu.activedMenu = menuItem;
            const { clickAction, noclick, disabled } = menuItem;
            if (!clickAction || noclick || disabled) {
                return undefined;
            }

            return this.menu.runClick(menuItem);
        },
    },
    render() {
        const mode = this.$attrs?.mode || 'horizontal';
        const {
            scopedClass,
            activedMenuId, activeMenu,
            menu,
            $scopedSlots,
            max,
            size,
        } = this;
        const visibleMenus = menu.menus.length <= max
            ? menu.menus
            : [
                ...menu.menus.slice(0, max),
                moreMenuItem({
                    scopes: menu.scope ? [menu.scope] : undefined,
                    children: menu.menus.slice(max), // invisible menus
                }),
            ];
        return (<el-menu
            class={scopedClass.mod([mode, size]).cn}
            default-active={activedMenuId}
            mode="horizontal"
            {...{
                props: {
                    ...this.$attrs,
                    mode, // 只做横向
                },
            }}
            onSelect={activeMenu}
        >
            { visibleMenus.map(menuItem => <JacpNestedMenuItem
                key={menuItem.id}
                menu-item={menuItem}
                scope={menu.scope}
                {...{
                    scopedSlots: $scopedSlots,
                }}
            />) }
        </el-menu>);
    },
};
</script>
<style lang="less">
.jacp-tabs-menu{
    z-index: 1;
    &--horizontal{
        &.el-menu.el-menu--horizontal{
            border-bottom: none;
        }
        &.el-menu--horizontal>.el-menu-item{
            height:  var(--height--base);
            line-height:  var(--height--base);
            border-bottom: none;
            color: var(--color--regular--content);
            & *{
                vertical-align: baseline;
            }
            &.is-active{
                font-weight: 600;
                color: var(--color--base--content);
                border-bottom: none;
            }
        }
        .el-submenu__title{
            height:  var(--height--base);
            line-height:  var(--height--base);
            padding-left: 0;
            &:hover{
                background: transparent;
                font-weight: var(--font-weight-blod);
            }
            & i{
                right: 0;
            }
        }
    }
     &--mini{
         &.el-menu--horizontal>.el-menu-item{
            height:  var(--height--menu-item);
            line-height:  var(--height--menu-item);
            padding: 0 var(--gutter--base);
         }

     }
}
</style>
