<template>
    <el-image
        :class="scopedClass.cn"
        :src="menuItem.icon"
        :style="style"
    >
        <div
            slot="placeholder"
            :class="scopedClass.el('loading').cn"
            v-loading="true"
            element-loading-spinner="el-icon-loading"
        />
        <div
            slot="error"
            :class="scopedClass.el('error').cn"
            :style="{
                ...style,
                lineHeight: width+unit,
                textAlign: 'center',
                borderRadius: borderRadius,
            }"
        >
            <span>{{ (menuItem.name || '')[0] }}</span>
        </div>
    </el-image>
    <!-- <i
        v-else
        :style="style"
        :class="menuItem.icon"
    /> -->
</template>
<script>
export default {
    name: 'JacpMenuItemIcon',
    props: {
        width: { type: Number, default: 20 },
        unit: { type: String, default: 'px' },
        borderRadius: { type: String, default: 'var(--radius--default)' },
        menuItem: { type: Object, default: () => ({}) },
    },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-menu-item-icon'),
        };
    },
    computed: {
        style() {
            return {
                width: this.width + this.unit,
                height: this.width + this.unit,
                fontSize: this.menuItem.icon.startsWith('http') ? '14px' : this.width + this.unit,
                borderRadius: this.borderRadius,
            };
        },
    },
};
</script>
<style lang="less">
.jacp-menu-item-icon{
    &__error{
        background-color: var(--color--primary);
        color: #fff;
        font-style: normal;
    }
    &__loading{
        .el-loading-spinner{
            transform: translateY(-50%);
            margin-top: 0;
           i{
               color: var(--color--base--placeholder);
           }
        }
    }
}
</style>
