<template>
    <div
        style="marginTop:10px;"
        class="menuTransFerTitle"
        :class="scopedClass.cn"
    >
        <!-- <el-alert
            :title="`前8个应用可常驻在左侧应用导航，上下拖动可排序`"
            type="warning"
            show-icon
            :closable="false"
            :class="scopedClass.el('tips').cn"
        /> -->
        <!-- <el-alert
            type="warning"
            show-icon
            :closable="false"
            :class="scopedClass.el('tips').cn"
        /> -->
        <!-- left: 全部应用 -->
        <!-- <div :class="scopedClass.el('list').cn">
            <el-input
                v-model="keyword"
                clearable
                :class="[scopedClass.el('sticky').cn, 'j-input']"
                :placeholder="$t('jacp.module.searchPlaceholder')"
            >
                <i
                    slot="prefix"
                    class="el-input__icon el-icon-search"
                />
            </el-input>
            <local-menu-item
                v-for="menuItem in filteredMenus"
                :class="scopedClass.el('item').mod({
                    disabled: disabled && !innerValue.find(menuItem.id) || menuItem.freeze,
                }).cn"
                :key="menuItem.id+'-l'"
                :menu-item="menuItem"
                :shadow="false"
                :border="false"
                @click.native.prevent="() => handleChange(!(menuItem.id in innerValue.menuItemIndex), menuItem)"
            >
                <span slot="action">
                    <el-checkbox
                        class="j-mgt12"
                        :disabled="disabled && !innerValue.find(menuItem.id) || menuItem.freeze"
                        :value="menuItem.id in innerValue.menuItemIndex || menuItem.freeze"
                    />
                </span>
            </local-menu-item>
        </div> -->
        <!-- right: 已选择 -->
        <div
            :class="scopedClass.el('list').cn"
            v-if="innerValue"
        >
            <el-row
                :class="scopedClass.el('sticky').cn"
                type="flex"
                justify="space-between"
                align="middle"
            >
                <!-- <span v-if="!disabled">
                    {{ $t('jacp.selected') }}常用应用：{{ innerValue.menus.length }}
                </span>
                <jacp-text
                    v-else
                    size="12"
                    type="warning"
                >
                    {{ $t('jacp.module.maxLengthTip', { n: maxLength }) }}
                </jacp-text> -->
                <!-- <el-button
                    type="text"
                    style="float: right;font-weight:var(--font-weight-default)"
                    @click="handleClear"
                >
                    {{ $t('jacp.clear') }}
                </el-button> -->
            </el-row>
            <div :class="scopedClass.el('sortable').cn">
                <local-menu-item
                    v-for="menuItem in innerValue.menus"
                    :class="scopedClass.el('item').mod({
                        sortable:true,
                    }).cn"
                    :key="menuItem.id+'-r'"
                    :menu-item="menuItem"
                    :shadow="false"
                    :border="false"
                >
                    <span slot="action">
                        <!-- <jacp-icon
                            v-if="!menuItem.freeze"
                            active
                            name="el-icon-close j-mgt12"
                            @click.native="() => handleChange(false, menuItem)"
                        /> -->
                    </span>
                </local-menu-item>
            </div>
        </div>
    </div>
</template>
<script>
import Sortable from 'sortablejs';
import cloneDeep from 'lodash/cloneDeep';
import { Menu } from '@jacpbiz/menu-business';
import LocalMenuItem from './menuItem';
import {
    // removeMenuItem,
    clearMenuItem,
} from '@/modules/root/models/menuActions';

export default {
    name: 'JacpMenuTransfer',
    props: {
        left: { type: Menu, required: true },
        right: { type: Menu, required: true },
        maxLength: { type: Number, default: 5 },
    },
    components: {
        LocalMenuItem,
    },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-menu-transfer'),
            keyword: '',
            innerValue: new Menu(),
        };
    },
    mounted() {
        this.initSortablePlugin();
    },
    methods: {
        initSortablePlugin() {
            const el = this.$el.querySelector('.jacp-menu-transfer__sortable');
            const options = {
                scroll: true, // Enable the plugin. Can be HTMLElement.
                bubbleScroll: true,
                animation: 150,
                dataIdAttr: 'data-id',
                onEnd: () => {
                    const order = this.sortableIns.toArray();
                    this.innerValue.menus
                        .sort((a, b) => order.indexOf(a.id) - order.indexOf(b.id));
                },
            };
            this.sortableIns = new Sortable(el, options);
        },
        // el-radio组件包含了label和input标签，在el-radio上设置了点击事件，让两个标签都拥有了该事件。checkbox也有这个问题，导致click被执行了两次.解决方法：native后面加prevent或者stop
        handleChange(val, menuItem) {
            if (val) {
                if (this.disabled || this.innerValue.find(menuItem.id)) {
                    return;
                }
                this.innerValue.add([menuItem]);
            } else {
                // freeze 代表固有应用
                if (menuItem.freeze) {
                    return;
                }
                const targetMenuItem = this.innerValue.find(menuItem.id);
                if (targetMenuItem) {
                    this.innerValue.remove(targetMenuItem);
                } else {
                    console.warn('[JAGILE]未找到要移除的菜单：', menuItem);
                }
            }
        },
        handleClear() {
            clearMenuItem.call(this.innerValue, menuItem => menuItem.freeze);
        },
    },
    computed: {
        filteredMenus() {
            if (!this.keyword) {
                return this.left.menus;
            }
            return this.left.menus.filter(menuItem => menuItem.name.includes(this.keyword));
        },
        disabled() {
            return this.innerValue.menus.length >= this.maxLength;
        },
    },
    watch: {
        'right.menus': {
            immediate: true,
            handler(value = []) {
                this.innerValue = new Menu(cloneDeep(value), 'id');
            },
        },
    },
};
</script>
<style lang="less">
.jacp-menu-transfer{
  display: grid;
  border: var(--border--hr);
  border-radius: 6px;
  grid-template-columns: 1fr;
  grid-template-areas: 'a a'
                     'b c';
    &__tips{
        grid-area: a;
    }
  &__list{
    overflow: auto;
    padding: 0 var(--gutter--base);
    max-height: 430px;
    position: relative;
    &+&{
      border-left: var(--border--hr);
    }
  }
  &__item{
    padding: var(--gutter--small);
    .setting-menu-item__info{
      flex-basis: 100%;
    }
    &--disabled{
        cursor: not-allowed;
    }
    &--sortable{
        margin-left: -8px;
        &:before{
            cursor: move;
            display: block;
            content: ' ';
            background-image: url("~@/assets/icons/sortable_simple_32.svg");
            background-repeat: no-repeat;
            background-size: 24px 24px;
            background-position: -6px center;
            width: 16px;
            height: 40px;
            padding-left: 4px;
        }
    }
  }
  &__sticky{
    position: sticky;
    top: 0;
    width: 100%;
    background: #fff;
    z-index: 10;
    padding-bottom: var(--gutter--base);
    padding-top: var(--gutter--base);
  }
}
.menuTransFerTitle::after {
    content: '收藏的前9个应用可以常驻菜单~';
    position: absolute;
    top: 60px;
    left: 50px;
    color:#999;
    font-size:12px;
    padding-bottom:10px;
}
</style>
