<template>
    <div :class="scopedClass.cn">
        <jacp-text
            font-weight="600"
            size="16"
        >
            我的收藏
        </jacp-text>
        <!-- 常用应用 -->
        <!-- 显示的时候依然使用当前菜单current，保持ui同步，操作数据的时候使用的是menus里根据scope存储的元数据 -->
        <span
            @click="openTrasfer"
            style="color:#339dfb;font-size:12px;float:right;cursor:pointer;"
        >目录排序</span>
        <div
            :class="scopedClass.el('grid').cn"
            v-if="favoMenuManager.current"
            style="margin-top:40px"
        >
            <local-menu-item
                v-for="(menuItem, index) in favoMenuManager.current.menus"
                ref="menuItem"
                :key="menuItem.key"
                :menu-item="menuItem"
                :class="scopedClass.el('item').mod({
                    sticky: index < MAX_MENU_LENGTH,
                }).cn"
                :star="true"
                :actions="true | getActions(exceed, menuItem.freeze)"
                @click.native="$ev => hanldeAction(menu, menuItem, 'jacpClickHandle', true)"
                @on-action-click="($ev) => hanldeAction(menu, menuItem, $ev)"
            >
                <template v-slot="{menuItem}">
                    <span class="setting-menu-item__name">{{ menuItem.name }}</span>
                    <el-tooltip
                        content="此应用已常驻在左侧导航，如需取消，可以点击「五角星取消收藏」"
                        placement="bottom-end"
                        v-if="index < MAX_MENU_LENGTH"
                        :class="scopedClass.el('item').el('tips').cn"
                    >
                        <span class="setting-menu-item__tips">常驻</span>
                    </el-tooltip>
                </template>
            </local-menu-item>
            <div
                v-if="!favoMenuManager.current.menus.length"
                class="store-app-list-placeholder"
            >
                <img
                    class="j-server-error__img"
                    src="@/assets/images/errorpage/<EMAIL>"
                >
                <jacp-text type="disable">暂无收藏</jacp-text>
            </div>
            <!-- <span
                :class="[
                    scopedClass.el('add').cn,
                    $scopedClass('setting-menu-item').mod({
                        border: true,
                        shadow: true,
                    }).cn,
                ]"
                @click="openTrasfer"
            >
                <jacp-icon
                    :class="scopedClass.el('add').el('icon').cn"
                    size="20"
                    name="el-icon-plus j-mgr8"
                />
                <jacp-text
                    size="14"
                    role="button"
                    tag="div"
                    font-weight="400"
                >
                    {{ $t('jacp.module.modifyFavo') }}
                </jacp-text>
            </span> -->
        </div>
        <!-- 全部应用 -->
        <div style="margin-top:48px;position: relative;">
            <jacp-text
                font-weight="600"
                size="16"
                style="margin-bottom: 20px"
            >
                {{ $t('jacp.module.titleAll') }}
                <!-- <a
                    href="/appStore"
                    target="_blank"
                    class="j-mgt4"
                    style="float: right;font-size: 14px;font-weight:400"
                >
                    <jacp-icon name="jacp-icon-a-icon-appmaket" />
                    获取更多应用
                </a> -->
            </jacp-text>

            <!-- <el-input
                :class="[scopedClass.el('search').cn, 'j-input--background']"
                v-model="keyword"
                placeholder="输入关键字搜索"
                size="mini"
            >
                <i
                    slot="prefix"
                    class="el-input__icon el-icon-search"
                />
            </el-input>
            <el-tabs
                :class="[
                    'jacp-tabs--gray',
                    scopedClass.el('main').cn]"
                v-model="activeTab"
                v-bind="$attrs"
                v-on="$listeners"
            >
                <el-tab-pane
                    name="all"
                    :label="$t('jacp.module.block.all')"
                />
                <el-tab-pane
                    name="recent"
                    :label="$t('jacp.module.block.recent')"
                    v-if="footprint && menu"
                />
                <template v-for="(block) in blockList">
                    <el-tab-pane
                        :key="block.code"
                        :name="block.code"
                        :label="block.name"
                        lazy
                    />
                </template>
            </el-tabs>
            <div
                class="menuItemBig"
            >
                <local-menu-item-big
                    v-for="menuItem in filteredMenuItems"
                    ref="menuItem"
                    :key="`${menuItem.id}`"
                    :menu-item="menuItem"
                    :star="getStar(favoMenu, menuItem.id)"
                    :actions="!!(favoMenu && favoMenu.find && favoMenu.find(menuItem.id)) | getActions(exceed, menuItem.freeze)"
                    @click.native="$ev => hanldeAction(menu, menuItem, 'jacpClickHandle')"
                    @on-action-click="($ev) => hanldeAction(menu, menuItem, $ev)"
                >
                    <template slot="name">
                        <span class="setting-menu-item__name">{{ menuItem.name }}</span>
                        <i
                            v-if="menuItem.meta && menuItem.meta.tags.length"
                            :class="getMenuTag(menuItem.meta.tags)"
                            :style="{
                                fontSize: 'var(--font-size--content)',
                                color: 'var(--color--error)',
                                marginLeft: 'var(--gutter--mini)',
                            }"
                        />
                    </template>
                </local-menu-item-big>
                <jacp-empty
                    v-if="!recents.length && activeTab === 'recent'"
                    label="暂无最近使用"
                    :placeholder-img="false"
                />
            </div> -->
            <app-store
                ref="appStore"
                @on-action-click="(star, app) => actionClick(star, app)"
                @card-click="cardClick"
            />
        </div>
        <!-- 新手引导 -->
        <jacp-guide-popper
            ref="guide"
            reference=".settings-menu__add"
            storagekey="jacp-guide-menu-define"
            @confirm="() => {
                openTrasfer()
            }"
        >
            <div>点击【五角星取消收藏】可将应用取消收藏，前8个会常驻在左侧导航菜单栏</div>
        </jacp-guide-popper>
    </div>
</template>

<script>
import Dialog from '@/models/dialog';
import { openLink } from '@/plugins/utils';
import {
    loadMenu, menuManager, MAX_MENU_LENGTH,
} from '@/modules/root/models/menu';
import LocalMenuItem from './menuItem';
import LocalMenuTransfer from './menuTransfer';
import {
    // removeMenuItem,
    removeFromFavo,
    addToFavo,
    shareMenu,
    openModuleDetail,
    openModuleDetailComment,
    saveFavo,
    clearMenuItem,
} from '@/modules/root/models/menuActions';
import AppModel from '@/modules/appstore/model/index';
import { footprint as moduleFootprint } from '$module/utils';
import { TagPriorityOrder } from '@/modules/root/constant';
import AppStore from '@/modules/appstore/layout.appStore';
import http from '@/plugins/http';
import { event } from '@/plugins/event';
import { jacpMenuItemAdapter } from '../../models/menuService';

// 目前设一个大值，表示不限制
const MAX_FAVO_LENGHT = 1000;
export default {
    inheritAttrs: false,
    components: { LocalMenuItem, AppStore },
    props: {
        menuScope: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            scopedClass: this.$scopedClass('settings-menu'),
            sortableIns: new WeakMap(),
            currentDragGroup: null,
            favoMenuManager: menuManager,
            activeTab: 'all',
            activeTabs: [],
            menu: undefined,
            blockList: [],
            footprint: moduleFootprint,
            keyword: '',
            MAX_MENU_LENGTH,
            star: false,
        };
    },
    filters: {
        getActions(exists = false, exceed = false, freeze = false) {
            if (freeze) {
                return ['share', 'detail', 'comment'];
            }
            if (exceed) {
                return exists ? ['removeFromFavo', 'share', 'detail', 'comment'] : ['share', 'detail', 'comment'];
            }
            return exists ? ['removeFromFavo', 'share', 'detail', 'comment'] : ['addToFavo', 'share', 'detail', 'comment'];
        },
    },
    created() {
        this.initPage();
        // watch once
        /*   this.unwatch = this.$watch('recents', (newVal) => {
            if (newVal.length) {
                this.activeTab = 'recent';
                this.unwatch();
            }
        }); */
        this.events = event;
    },
    methods: {
        openLink,
        async initPage() {
            this.menu = await loadMenu();
            await this.favoMenuManager.loadMenu(this.menuScope);
            this.defineDefaultActions();
            this.defineFavoActions();
            this.blockList = await AppModel.getAppBlockList();
        },
        defineDefaultActions() {
            const { menu } = this;
            // menu.defineClickAction(
            //     // 这个是不响应菜单折叠的
            //     'jacpClickHandle',
            //     menuClickHandler(this.$router, false),
            // );
            menu.defineClickAction(
                'share',
                shareMenu.bind(menu),
            );
            menu.defineClickAction(
                'detail',
                openModuleDetail.bind(menu),
            );
            menu.defineClickAction(
                'comment',
                openModuleDetailComment.bind(menu),
            );
        },
        // FIXME: 所有和favo相关的操作都使用的是favoMenuManager.current来完成的。这感觉有点奇怪。看上去应该每个拥有action的菜单的行为都是一样的才对？
        async defineFavoActions() {
            const menu = this.favoMenu;
            // menu.defineClickAction(
            //     // 这个是不响应菜单折叠的
            //     'jacpClickHandle',
            //     menuClickHandler(this.$router, false),
            // );
            menu.defineClickAction(
                'addToFavo',
                addToFavo.bind(menu),
            );
            menu.defineClickAction(
                'removeFromFavo',
                removeFromFavo.bind(menu),
            );
            menu.defineClickAction(
                'share',
                shareMenu.bind(menu),
            );
            menu.defineClickAction(
                'detail',
                openModuleDetail.bind(menu),
            );
        },
        async hanldeAction(menu, menuItem, action, flag) {
            if (flag && (menuItem.licenseStatus === 1 || menuItem.licenseStatus === 3)) {
                return;
            }
            if (action === 'removeFromFavo') {
                event.$emit('change-star', menuItem.appId);
            }
            menuItem.clickAction = action;
            if (action.includes('Favo')) {
                this.favoMenu.runClick(menuItem);
                // 跑完了其他action的时候需要把favo的clickAction恢复一下，保持主菜单能被触发跳转
                menuItem.clickAction = 'jacpClickHandle';
                return;
            }
            menu.runClick(menuItem);
            menuItem.clickAction = 'jacpClickHandle';
            if (this.$refs.menuItem && this.$refs.menuItem.star) {
                this.$refs.menuItem.star = !this.$refs.menuItem.star;
            }
        },
        //  <span style="color:#999;font-size:12px">收藏的前8个应用可以常驻菜单~</span>
        openTrasfer() {
            Dialog.confirm({
                title: this.$t('jacp.module.modifyFavoFullName'),
                closeOnClickModal: false,
                // slot: LocalMenuTransferAndTip,
                slot: LocalMenuTransfer,
                width: '424px',
                slotProps: {
                    left: this.menu,
                    right: this.favoMenu,
                    maxLength: MAX_FAVO_LENGHT,
                },
                beforeConfirm: vm => vm.innerValue && saveFavo.call(vm.innerValue).then(() => {
                    clearMenuItem.call(this.favoMenuManager.current);
                    // 这里可能会产生重复数据
                    this.favoMenuManager.current.merge(vm.innerValue);
                    this.favoMenuManager.menus[this.menuScope] = this.favoMenuManager.current;
                }),
            });
        },
        getMenuTag(tags) {
            if (!tags || !tags.length) {
                return '';
            }

            const tagsUnsorted = [...tags];
            const tagsSorted = tagsUnsorted.sort((a, b) => TagPriorityOrder[a] - TagPriorityOrder[b]);
            return `jacp-icon-tag-${tagsSorted[0].toLowerCase()}`;
        },
        getStar(favoMenu, id) {
            if (favoMenu && favoMenu.find && favoMenu.find(id)) {
                return true;
            }
            return false;
        },
        updataApps() {

        },
        actionClick(star, app) {
            if (star === 'addToFavo') {
                http.get(`v1/setting/personal/menus/appId/${app.id}`).then((res) => {
                    this.$set(res[0], 'name', app.name);
                    this.$set(res[0], 'appId', app.id);
                    this.$set(res[0], 'accessMode', app.accessMode);
                    res[0].scopes = 'hideChildren';
                    this.menu.menus.push(res[0]);
                    this.$forceUpdate();
                    this.hanldeAction(this.menu, res[0], 'addToFavo');
                });
            }
            if (star === 'removeFromFavo') {
                http.get(`v1/setting/personal/menus/appId/${app.id}`).then((res) => {
                    this.$set(res[0], 'name', app.name);
                    this.$set(res[0], 'appId', app.id);
                    this.menu.menus = this.menu.menus.filter(item => item.appId !== res[0].appId);
                    this.favoMenuManager.current.menus = this.favoMenuManager.current.menus.filter(
                        item => item.appId !== res[0].appId,
                    );
                    this.hanldeAction(this.menu, res[0], 'removeFromFavo');
                });
            }
        },
        cardClick(app) {
            this.hanldeAction(this.menu, jacpMenuItemAdapter(app), 'jacpClickHandle');
        },
    },
    computed: {
        innerBlock() {
            return this.menu?.menus.map(menu => menu.block).map(o => o.code);
        },
        favoMenu() {
            return this.favoMenuManager?.current || {};
        },
        recents() {
            const { menu, footprint } = this;
            if (!footprint || !footprint.data || !menu) {
                return [];
            }
            // 只找一级菜单，子菜单的过滤掉
            return footprint.data
                .map(moduleKey => menu.menus.find(menuItem => menuItem.id === moduleKey))
                .filter(o => o).reverse();
        },
        filteredMenuItems() {
            const { activeTab, menu = {}, keyword } = this;
            let list = [];
            switch (activeTab) {
            case 'recent':
                list = this.recents;
                break;
            case 'all':
                list = menu.menus || [];
                break;
            default:
                list = menu.menus.filter(menuItem => menuItem.block.code === activeTab);
                break;
            }
            return keyword
                ? list.filter(menuItem => menuItem.name.includes(keyword) || menuItem.id.includes(keyword))
                : list;
        },
        exceed() {
            if (this.favoMenu.menus) {
                return this.favoMenu.menus.length >= MAX_FAVO_LENGHT;
            }
            return false;
        },
    },
    watch: {
        'favoMenuManager.current': {
            immediate: true,
            handler(val) {
                if (!val) {
                    return;
                }
                this.$nextTick(() => {
                    this.$refs.guide.init();
                });
            },
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
@--item-min-width: 210px;
@--item-height: 72px;
.settings-menu {
    // height: calc(~"100vh - 50px");
    padding: var(--gutter--large);
    &__grid{
        position: relative;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(@--item-min-width, 1fr)) ;
        grid-auto-flow: row dense;
        grid-template-rows: repeat(2, @--item-height);
        grid-gap: var(--gutter--medium) var(--gutter--medium);
    }
    &__item{
        &--sticky{
            // 前8个出现在菜单里，追加背景色
            &:nth-child(-n+9) {
                position:relative;
                overflow: hidden;
                @arrow-width: 40px;
                  &:after {
                    display: block;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    content: ' ';
                    width: 0;
                    height: 0;
                    border-bottom: @arrow-width solid var(--color--base--background);
                    border-left: @arrow-width solid transparent;
                    transition: border 0.2s;
                }
            }
        }
       &__tips{
            position: absolute;
            right: 0;
            bottom: 2px;
            transform: rotate(-45deg) scale(0.8);
            z-index: 1;
            color: var(--color--extralight--content);
        }
    }
    &__main {
        padding: var(--gutter--medium) 0 0 0;
        overflow-y: auto;
    }
    // TODO: 背景要动起来？
    &__banner{
        grid-column: 1 / span 2;
        grid-row: 1 / span 2;
        background: no-repeat url('http://storage.jd.com/jacp.attachment/app_store/banner/app_store_banner.png');
        // background-size: auto 100%;
        background-size: cover;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.2s;
        &:hover{
           box-shadow:  0 4px 16px 0 rgba(48,49,51,0.1);
        }
    }
    &__carousel-banner{
            grid-column: 1 / span 2;
            grid-row: 1 / span 2;
            border-radius: 12px;
            cursor: pointer;
            &:hover{
            box-shadow:  0 4px 16px 0 rgba(48,49,51,0.1);
        }
    }

    &__add{
        background-color: #fff!important;
        position: relative;
        // align-items: center;
         &__icon.jacp-icon{
            color: var(--color--arrow-base);
            width: 40px;
            height: 40px;
            border-radius: var(--radius--medium);
            line-height: 40px;
            text-align: center;
            background: var(--color--base--background);
        }
    }
    &__search,  &__search.el-input{
        position: absolute;
        right: 0;
        width: var(--width--base);
        top: 40px;
        z-index: 1;
        .el-input__inner{
            border-radius: var(--radius--medium);
        }
    }
    .store-app-list-placeholder {
            margin-top: 100px;
            grid-column-start: 4;
            grid-column-end: 4;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: calc(100% - var(--gutter--large));
    }

}
// .tab {
//     display: inline-block;
//     padding: 20px;
// }
.menuItemBig {
    position: relative;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)) ;
        grid-auto-flow: row dense;
        grid-template-rows: repeat(3, 200px);
        grid-gap: var(--gutter--medium) var(--gutter--medium);
}
</style>
