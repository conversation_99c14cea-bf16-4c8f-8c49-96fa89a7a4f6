<template>
    <el-tooltip
        class="item"
        effect="dark"
        :content="menuItem.licenseStatus == 1 ?
            '应用license未找到，不可以使用，请联系应用负责人或商店管理员推动应用后续流程' :
            (menuItem.licenseStatus == 3?'应用已过期，不可以使用，请联系应用负责人或商店管理员推动应用后续流程':'') "
        placement="bottom"
        :disabled="menuItem.licenseStatus != 1 && menuItem.licenseStatus != 3"
    >
        <div
            :class="scopedClass.mod({
                border,
                shadow,
            }).cn"
            :key="menuItem.id"
            :data-id="menuItem.id"
            :style="menuItem.licenseStatus == 1 || menuItem.licenseStatus == 3 ? 'background:#eee;cursor: not-allowed':''"
        >
            <LocalMenuIcon
                :class="scopedClass.el('icon').cn"
                v-if="menuIcon"
                :menu-item="menuItem"
                :width="40"
                border-radius="12px"
            />

            <div :class="scopedClass.el('info').cn">
                <slot :menuItem="menuItem">
                    <el-row
                        type="flex"
                        align="middle"
                        :class="scopedClass.el('title').cn"
                    >
                        <slot
                            name="name"
                            :menuItem="menuItem"
                        >
                            <span :class="scopedClass.el('name').cn">{{ menuItem.name }}</span>
                        </slot>
                    </el-row>
                    <!-- <el-tooltip
                        :content="menuItem.desc"
                        :disabled="!menuItem.desc"
                    >
                        <span :class="scopedClass.el('desc').cn">{{ menuItem.desc }}</span>
                    </el-tooltip> -->
                </slot>
            </div>

            <slot
                name="action"
                :menuItem="menuItem"
            >
                <!-- <el-dropdown
                :class="scopedClass.el('action').cn"
                v-if="actions.length"
                trigger="click"
                slot="action"
                placement="right-start"
                @command="hanldeAction"
                @click.native.stop
            >
                <jacp-icon
                    background
                    name="el-icon-more"
                    role="button"
                />
                <el-dropdown-menu
                    slot="dropdown"
                >
                    <el-dropdown-item
                        v-for="(action, $index) in actions"
                        :key="$index"
                        :command="action"
                        style="white-space: nowrap;"
                    >
                        <span>{{ $t(`jacp.module.action.${action}`) }}</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown> -->
                <i
                    class="i-star"
                    :class="star ? 'el-icon-star-on pointer' : 'el-icon-star-off pointer'"
                    @click.stop="starApp"
                />
            </slot>
        </div>
    </el-tooltip>
</template>
<script>
import LocalMenuIcon from '@/modules/root/components/menu/menuIcon';

export default {
    name: 'JacpMenuItem',
    props: {
        menuItem: {
            type: Object,
            default: () => ({}),
        },
        actions: {
            type: Array,
            default: () => ([]),
        },
        border: {
            type: Boolean,
            default: true,
        },
        shadow: {
            type: Boolean,
            default: true,
        },
        star: {
            type: Boolean,
            default: false,
        },
        menuIcon: {
            type: Boolean,
            default: true,
        },
    },
    components: {
        LocalMenuIcon,
    },
    data() {
        return {
            scopedClass: this.$scopedClass('setting-menu-item'),
        };
    },
    methods: {
        hanldeAction(cmd) {
            this.$emit('on-action-click', cmd);
        },
        starApp() {
            // this.star = !this.star;
            this.$emit('on-action-click', this.star ? 'removeFromFavo' : 'addToFavo');
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.setting-menu-item{
    display: flex;
    font-size: var(--font-size--content);
    padding: var(--gutter--medium);
    line-height: 20px;
    position: relative;
    cursor: pointer;
    align-items: center;
    background-color: #fff;
    // hover 的时候再显示actions
    &:hover &__action{
        visibility: visible;
        opacity: 1;
    }
    &--border{
        border: 1px solid var(--color--base--hr);
        border-radius: 12px;
    }
    &--shadow{
        transition: box-shadow 0.2s;
    }
    &--shadow:hover{
        background-color: #fff;
        box-shadow:  0 4px 16px 0 rgba(48,49,51,0.1);
    }

    &__icon{
        min-width: 40px;
        height: 40px;
        margin-right: var(--gutter--small);
    }
    &__desc{
        .get-content-overflow(1);
    }
    &__title {
        line-height: 20px;
    }
    &__name {
        color: var(--color--base--content);
    }
    &__desc{
        color: var(--color--secondary--content);
        font-size: var(--font-size--description);
    }
    &__action,  &__action.el-dropdown{
        position: absolute;
        right: var(--gutter--medium);
        top: var(--gutter--base);
        visibility: hidden;
        opacity: 0;
        transition: all 0.2s;
    }
    .i-star {
        position: absolute;
        right: 15px;
        top: 10px;
        font-size: 20px;
    }
    .el-icon-star-on {
        color: #facd91;
        font-size: 20px;
    }
    .pointer:hover {
        cursor: pointer;
    }
}
</style>
