<template>
    <div class="jacp-module-root">
        <router-view v-if="currentModule && currentModule.type === 'page'" />
        <div class="jacp-module-root__app" />
    </div>
</template>

<script>
import { JModule } from '@jmodule/client';
import { getCurrentModuleKey } from '$module/utils';
import {
    bootstraped, mountTimes,
    initAndGetArea,
    unmountModule,
} from '$module/utils/mountPoint';

export default {
    beforeRouteLeave(to, from, next) {
        // 销毁也需要卸载
        this.unmountModule(this.currentModule);
        next();
    },
    mounted() {
        this.areaEl = this.$el.querySelector('.jacp-module-root__app');
    },
    methods: {
        async unmountModule(module) {
            if (!module) {
                return;
            }
            await unmountModule(module);
            this.currentModule = undefined;
        },
        async bootstrapModule(module) {
            if (!module) {
                return;
            }
            await module.hooks.complete;
            if (!bootstraped[module.key]) {
                await module.metadata?.bootstrap?.(module);
                bootstraped[module.key] = true;
            }
        },
        async mountModule(module) {
            if (!module || !this.areaEl) {
                return;
            }
            const wrapped = true;
            const mountPoint = initAndGetArea(module, wrapped);
            this.areaEl.appendChild(mountPoint);
            const el = wrapped && mountPoint.firstChild ? mountPoint.firstChild : mountPoint;
            await module.metadata?.mount?.(module, el, {
                mountTimes: mountTimes[module.key] || 0,
            });
            mountTimes[module.key] = (mountTimes[module.key] || 0) + 1;
        },
    },
    data() {
        return {
            currentModule: undefined,
            areaEl: undefined,
        };
    },
    watch: {
        '$route.path': {
            async handler() {
                const modulekey = getCurrentModuleKey();
                if (modulekey !== this.currentModule?.key) {
                    // 卸载旧模块。在当前组件但切换了应用，需要卸载
                    await this.unmountModule(this.currentModule);
                    // 获取新模块
                    this.currentModule = await JModule.getModuleAsync(modulekey);
                    // 初始化新应用
                    await this.bootstrapModule(this.currentModule);
                    // 挂载应用
                    await this.mountModule(this.currentModule);
                }
            },
            immediate: true,
        },
    },
};
</script>
