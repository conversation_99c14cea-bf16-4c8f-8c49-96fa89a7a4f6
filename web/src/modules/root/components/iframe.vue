<template>
    <iframe
        class="jacp-iframe"
        v-bind="$attrs"
        :width="width"
        :height="height"
        :src="parsedUrl"
        @load="onload"
    />
</template>
<script>
export default {
    name: 'JacpIframe',
    inheritAttrs: false,
    props: {
        src: { type: String, required: true },
        width: { type: String, default: '100%' },
        height: { type: String, default: '100%' },
        searchParams: { type: Object },
        adjustHeight: { type: Boolean, default: false },
    },
    computed: {
        parsedUrl() {
            if (!this.searchParams || !this.src) {
                return this.src;
            }
            const queryString = Object.keys(this.searchParams)
                .map(key => `${key}=${this.searchParams[key]}`)
                .join('&');
            const connectorString = this.src.includes('?') ? '&' : '?';
            return `${this.src}${queryString ? connectorString : ''}${queryString}`;
        },
    },
    methods: {
        onload(evt) {
            this.$emit('iframe:onload', evt);
            // TODO:容器高度自适应iframe
        },
    },
};
</script>
<style lang="less">
.jacp-iframe{
    border: none;
}
</style>
