<template>
    <div
        v-loading="loading"
        :style="pageMode ? {
            width: '100%',
            height: '100vh',
        } : {}"
        element-loading-text="加载中..."
        element-loading-custom-class="main-inner-loading"
    >
        <slot />
        <local-error
            v-if="error.message || error.code"
            v-bind="error"
        >
            <template
                v-if="error.opration"
                slot="opration"
            >
                <div v-html="error.opration" />
            </template>
        </local-error>
    </div>
</template>
<script>
import LocalError from '@/modules/root/layout.error';
import {
    getCurrentModuleKey,
    manualModuleLoader,
    toCamelCase,
} from '$module/utils';
// 根据跟路由名称是否存在匹配来判断是否真的404？
function isActiveInRouteMatch(rootRouteName, matched = []) {
    const matchedNames = matched.map(item => item.name);
    return matchedNames.includes(rootRouteName);
}

const timeout = 30000;


export default {
    name: 'ModuleLoader',
    props: {
        showLoading: {
            default: true, type: Boolean,
        },
        pageMode: {
            default: true, type: Boolean,
        },
        moduleKey: {
            type: String,
        },
        afterLoaded: {
            default: () => {},
            type: Function,
        },
        loaderOptions: {
            default: () => ({ autoApplyScript: true, parallel: false }),
            type: Object,
        },
    },
    created() {
        if (this.pageMode) {
            this.$watch('$route.path', {
                handler: 'loadModule',
                immediate: true,
            });
        } else {
            this.loadModule();
        }
    },
    components: {
        LocalError,
    },
    data() {
        return {
            loading: this.showLoading,
            currentModule: null,
            error: {},
        };
    },
    methods: {
        async loadModule() {
            console.warn('111111111');
            const modulekey = this.moduleKey || getCurrentModuleKey();
            let errorMsg;
            const loader = manualModuleLoader({ timeout, ...this.loaderOptions });
            // 获取新模块
            this.currentModule = await JModule.getModuleAsync(modulekey, timeout)
                .catch(() => {
                    errorMsg = `获取appCode为【${modulekey}】时超时，该应用不存在或者未注册。`;
                });
            if (this.currentModule) {
                // 重新加载模块是应该在这里catch处理？
                await loader(this.currentModule);
            } else {
                this.loading = false;
                this.error = {
                    message: errorMsg,
                    // opration: '<div> 您可以尝试重新加载模块<a href="/" class="j-server-error__link">返回首页</a></div>',
                };
                return;
            }
            // this.loading = false;
            setTimeout(() => {
                if (this.pageMode) {
                    try {
                        const router = this.$router;
                        //  currentRoute里的matched无法match动态路由，所以需要重新获取一下匹配
                        const rematched = router.match(window.location.pathname, router.currentRoute)?.matched;
                        const rootRouteName = toCamelCase(`root_${modulekey}`);
                        const is404 = !isActiveInRouteMatch(rootRouteName, rematched);
                        if (is404) {
                            setTimeout(() => {
                                this.loading = false;
                                this.error = {
                                    code: 404, message: '',
                                };
                            }, timeout);
                        }
                    } catch (e) {
                        console.error(e);
                    }
                }
                this.afterLoaded({
                    module: this.currentModule,
                    error: this.error,
                });
            }, 1000);
        },
    },
};
</script>
