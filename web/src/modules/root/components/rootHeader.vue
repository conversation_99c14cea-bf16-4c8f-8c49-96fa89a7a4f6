<template>
    <div
        :class="scopedClass.cn"
        v-if="rootVisible"
    >
        <slot name="icon" />
        <span
            :class="scopedClass.el('title').cn"
            @click="menuClick"
        >
            {{ rootMenuName }}
        </span>
        <template v-if="subVisible">
            <el-divider direction="vertical" />
            <LocalTabsMenu
                :menu="subMenu"
                :collapse-transition="false"
                v-if="subMenu"
            />
            <jacp-guide-popper
                ref="guide"
                confirm-button-text=""
                reference=".jacp-tabs-menu"
                storagekey="jacp-guide-root-header-menu"
                cancel-button-type="primary"
            >
                <div>应用子菜单搬家到这里啦~~</div>
            </jacp-guide-popper>
        </template>
        <router-link
            v-if="isShowAdmin"
            style="position: absolute;right: var(--gutter--large);"
            :to="{
                path:toPath
            }"
        >
            <jacp-text
                icon="jacp-icon-setting"
                type="default"
                active
                active-color="var(--color--primary--hover)"
            >
                {{ rootMenuName }}设置
            </jacp-text>
        </router-link>
    </div>
</template>
<script>
import { getCurrentModuleKey } from '$module/utils';
import {
    loadModuleMenu, subMenu,
} from '$module/models/menu';
import LocalTabsMenu from './tabsMenu';
import { getPermissionUicomponents } from '@/modules/option/modules';

// 子应用自身包含rootHeader
// 团队空间包含子菜单，应用自身处理了
const hiddenAppCode = ['archSpace', 'option'];
// 需要检查应用管理员的应用
const checkUiComponentMap = {
    archDemand: { menuKey: 'demand:view' },
    codingRoot: { menuKey: 'coding:message:view' },
    jStatisticsRoot: { menuKey: 'statisticsRoot:view' },
    pmp: { menuKey: 'projectManage:view' },
    jpipeRoot: { menuKey: 'jpipe:view' },
    testManage: { menuKey: 'testManage:view' },
};

// 跳转地址
const menuPathMap = {
    archDemand: '/archDemand/options/formCustom',
    codingRoot: '/codingRoot/codingSetting',
    jStatisticsRoot: '/jStatisticsRoot/Settings/memberManage',
    pmp: '/pmp/projectSetting/msgSetting',
    testManage: '/testManage/jtestOption/jtestSetting',
    jpipeRoot: '/jpipeRoot/setting',
};

export default {
    name: 'JacpRootHeader',
    components: { LocalTabsMenu },
    async created() {
        this.init();
    },
    data() {
        return {
            rootMenuCache: {},
            scopedClass: this.$scopedClass('jacp-root-header'),
            rootMenuName: '',
            rootMenuCode: '',
            subMenu,
            isShowAdmin: false, // 是否超管理员
        };
    },
    methods: {
        menuClick() {
            this.rootMenuObj.runClick(this.rootMenuCache);
        },
        async init() {
            this.rootMenuObj = null;
            this.isShowAdmin = false;
            // load menu data
            const menuKey = getCurrentModuleKey();
            // 缓存一份数据 点击时使用
            const rootMenuObj = await loadModuleMenu(menuKey);
            this.rootMenuObj = rootMenuObj;
            const [rootMenu] = rootMenuObj.menus || [];
            // 缓存根节点菜单，切换子菜单时使用
            this.rootMenuCache = rootMenu;
            // id = appCode name = app名称
            const { id = '', name = '' } = rootMenu;
            this.rootMenuName = name;
            this.rootMenuCode = id;
            // 因为有几个应用目前只有消息设置一个页面，权限还需要单独控制，所以暂时这边没有权限直接隐藏设置菜单
            const diaplayMenu = ['archSpace', 'pmp', 'testManage'];
            if (diaplayMenu.includes(this.rootMenuCode) && !this.$store.state.app.serviveConfig.message) {
                this.isShowAdmin = false;
            } else if (checkUiComponentMap[id]) {
                // 历史原因，需转换
                const appCodeConvert = {
                    jStatisticsRoot: 'jeep-dc',
                    testManage: 'jtest',
                };
                getPermissionUicomponents({
                    modules: '',
                    appCode: appCodeConvert[id] || id,
                }).then((res) => {
                    const targetMenuKey = checkUiComponentMap[id].menuKey;
                    this.isShowAdmin = res[targetMenuKey] === 0;
                });
            }
        },
    },
    computed: {
        toPath() {
            return menuPathMap[this.rootMenuCode] || '';
        },
        rootVisible() {
            return this.rootMenuCache && this.rootMenuCache.id && !hiddenAppCode.includes(this.rootMenuCache.id);
        },
        subVisible() {
            return !!(this.subMenu && this.subMenu.menus.length);
        },
    },
    watch: {
        '$route.path': {
            async handler(newValue, oldValue) {
                // 第一级path没有发生变化的时候不做处理
                if (getCurrentModuleKey(newValue) === getCurrentModuleKey(oldValue)) {
                    return;
                }
                this.init();
            },
            immediate: true,
        },
    },

};
</script>
<style lang="less">
.jacp-root-header{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: var(--height--base);
    min-height: var(--height--base);
    background-color: #fff;
    padding: 0 var(--gutter--large);
    border-bottom: var(--border--hr);
    overflow: hidden;
    &__title{
        margin-right: var(--gutter--medium);
        font-size: var(--font-size--subtitle);
        font-weight: var(--font-weight-bold);
        cursor: pointer;
    }
    &__banner{
        width: 160px;
        cursor: pointer;
        img{
            width:100%;
        }
    }
}
</style>
