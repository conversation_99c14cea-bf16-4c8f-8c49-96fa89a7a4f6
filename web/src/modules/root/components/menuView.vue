<script>
import { MenuView as LocalMenu, Menu } from '@jacpbiz/menu-business';

export default {
    name: 'JacpMenuView',
    props: {
        menu: { type: Menu, required: true },
        activedMenuId: { type: String, default: '' },
        theme: { type: String, default: 'light' },
        noShrink: { type: Boolean, default: true },
        uniqueUnfold: { type: Boolean, default: true },
    },
    components: { LocalMenu },
    render() {
        const { menu } = this;
        return !menu
            ? <div vLoading={true}></div>
            : <LocalMenu
                ref="menu"
                class={'jacp-menu-business'}
                theme={this.theme}
                menu={menu}
                noShrink={this.noShrink}
                scopedSlots={this.$scopedSlots} />;
    },
    computed: {
        menus() {
            return this.menu.menus;
        },
    },
    mounted() {
        this.updateMenuTitle();
    },
    methods: {
        forceUpdatePosition() {
            if (!this.$refs.menu) {
                return;
            }
            const { $children } = this.$refs.menu;
            this.$nextTick(() => {
                if (Array.isArray($children)) {
                    $children.forEach((vm) => {
                        if (vm.initPopover) {
                            vm.initPopover();
                        }
                    });
                }
            });
        },
        updateMenuTitle() {
            this.$nextTick(() => {
                const menuItemNodeList = this.$el.querySelectorAll('.jacpbiz-menu-item[data-menu-item-id]') || [];
                menuItemNodeList.forEach((menuItemNode) => {
                    const menuItemId = menuItemNode.getAttribute('data-menu-item-id');
                    const title = this.menus.find(menu => menu.id === menuItemId).name;
                    // const title = this.menus.find(menu => menu.id === menuItemId);
                    menuItemNode.setAttribute('title', title);
                });
            });
        },
        activeMenu(activedMenuId) {
            if (!this.menu) {
                return;
            }
            if (activedMenuId) {
                this.menu.activeById(activedMenuId);
            } else {
                this.menu.active();
            }
        },
    },
    watch: {
        menus(menus) {
            if (menus && menus.length) {
                this.forceUpdatePosition();
                this.updateMenuTitle();
                this.$emit('card-change', menus);
            }
        },
        activedMenuId: 'activeMenu',
    },
};
</script>
