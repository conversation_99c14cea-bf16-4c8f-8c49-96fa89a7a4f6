<template>
    <div
        :class="scopedClass.mod({
            collapsed: isCollapse,
        }).cn"
    >
        <!-- logo -->
        <div
            :class="scopedClass.el('logo').cn"
            style="text-align: center; margin-right: 8px;"
            @click="goHome"
        >
            <!-- <i
                :class="{
                    'jacp-icon-navi_tab_logo_expansion': !isCollapse,
                    'jacp-icon-navi_tab_logo_eceipts': isCollapse,
                }"
            /> -->
            <img src="@/assets/images/logo.png" style="width: 32px;" />
        </div>
        <!-- 只有菜单区域需要滚动 -->
        <el-scrollbar
            style="flex-basis:  100%;"
            :class="scopedClass.el('menu-wrapper').cn"
        >
            <!-- 我的应用 -->
            <LocalMenuView
                v-if="menuManager.current"
                :menu="menuManager.current"
                ref="menuViewEl"
                :class="scopedClass.el('menu-view').mod({
                    dynamic: true,
                    [menuManager.current.scope]: !!menuManager.current.scope
                }).cn"
                :actived-menu-id="activedMenuId"
                @card-change="cardChange"
            >
                <template
                    slot="icon"
                    slot-scope="menuItem"
                >
                    <LocalMenuIcon
                        :menu-item="menuItem"
                        :width="20"
                    />
                </template>
            </LocalMenuView>
            <!-- 固定不变的菜单 -->
            <LocalMenuView
                v-if="fixedMenuManager.current"
                :menu="fixedMenuManager.current"
                :class="scopedClass.el('menu-view').mod({
                    fixed: true,
                    [menuManager.current.scope]: !!menuManager.current.scope
                }).cn"
                :actived-menu-id="activedMenuId"
            >
                <template
                    slot="icon"
                    slot-scope="menuItem"
                >
                    <LocalMenuIcon
                        :menu-item="menuItem"
                        :width="20"
                    />
                </template>
            </LocalMenuView>
        </el-scrollbar>

        <!-- setting -->
        <div
            :class="scopedClass.el('header-menu').cn"
        >
            <JacpHeaderMenu
                ref="headerMenu"
                vertical
                :fixed="false"
            />
        </div>
        <!-- 新手引导 -->
        <jacp-guide-popper
            ref="guide"
            reference=".jacp-aside-menu__menu-view--fixed > .jacpbiz-menu__scroll > .jacpbiz-menu__space"
            storagekey="jacp-guide-aside-menu"
            :popper-options="{
                placement: 'right',
                modifiers: [
                    {
                        name: 'offset',
                        options: {
                            offset: [-30, 8],
                        },
                    },
                ]
            }"
            @confirm="() => {
                $router.push({
                    name: 'rootModuleCenter'
                })
            }"
        >
            <div>【导航菜单栏】升级了，默认展示前9个常用应用，点击【更多】可查看全部常用应用</div>
        </jacp-guide-popper>
    </div>
</template>
<script>
import { Menu } from '@jacpbiz/menu-business';
import LocalMenuView from './menuView';
import LocalMenuIcon from '@/modules/root/components/menu/menuIcon';
import {
    menuManager,
    fixedMenuManager,
    loadModuleMenu,
    activeByMatched,
    MAX_MENU_LENGTH,
} from '$module/models/menu';

import { getCurrentModuleKey } from '$module/utils';
import MenuCutter from '$module/utils/menuCutter';
import { updateFootprint as updateModuleFootprint } from '@/modules/root/utils';

export default {
    name: 'JacpAsideMenu',
    components: {
        LocalMenuView,
        LocalMenuIcon,
    },
    created() {
        this.initMenu();
    },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-aside-menu'),
            menuScope: 1,
            menuManager,
            fixedMenuManager,
            activedMenuId: '',
            styleMountedEl: null,
            shownMenuLengh: MAX_MENU_LENGTH,
        };
    },
    mounted() {
        setTimeout(() => {
            this.initMenuCutter();
        }, 0);
    },
    beforeDestroy() {
        if (this.menuCutter) {
            this.menuCutter.destroy();
        }
    },
    computed: {
        isCollapse() {
            if (menuManager.current) {
                return menuManager.current.shrink;
            }
            return true;
        },
        menu: {
            get() {
                return menuManager.current ? menuManager.current : new Menu([]);
            },
            set(menuIns) {
                menuManager.current = menuIns;
            },
        },
        fixedMenu() { return this.fixedMenuManager.current; },
        activeMenu() {
            if (this.fixedMenu
            && this.fixedMenu.activedMenu
            && this.fixedMenu.activedMenu.actived) return this.fixedMenu;
            if (this.menu
            && this.menu.activedMenu
            && this.menu.activedMenu.actived) return this.menu;
            return null;
        },
    },
    methods: {
        async initMenu() {
            this.menuManager.current = await this.menuManager.loadMenu(this.menuScope);
            // console.log('=this.menuScope=', this.menuScope);
            // console.table(this.menuManager.current.menus);
            this.fixedMenuManager.current = await this.fixedMenuManager.loadMenu(this.menuScope);
            const { matched } = this.$router.currentRoute;
            if (this.menuManager.current) {
                activeByMatched.call(this.menuManager.current, matched);
            }
            this.refreshMenus(getCurrentModuleKey());
        },
        updateGuidePosition() {
            if (this.$refs.guide && this.$refs.guide.visible) {
                this.$nextTick(() => {
                    this.$refs.guide.init();
                });
            }
        },
        goHome() {
            this.$router.push({ path: '/' });
        },
        // FIXME:refreshMenus怎么优化一下
        async refreshMenus(modulekey) {
            if (!modulekey || !this.menu || !this.fixedMenu) return;

            const { fixedMenu, menu } = this;
            // 移除上一个模块的菜单
            if (fixedMenu && fixedMenu.menus.length > 1) {
                const menuItem = fixedMenu.menus.shift();
                fixedMenu.remove(menuItem);
            }
            this.initMenuCutter();
            const orderInMenu = menu.menus.findIndex(item => item.id === modulekey);
            if (orderInMenu > -1 && orderInMenu < this.shownMenuLengh) {
                fixedMenu.active();
                // 在我的应用里有 ，并且在可显示数量范围内，高亮之
                return;
            }
            menu.active();
            // 访问的是「更多」
            // 我的应用里没有，追加当前模块的菜单并反序显示
            const tempMenu = await loadModuleMenu(modulekey);
            fixedMenu.merge(tempMenu);
            fixedMenu.menus = fixedMenu.menus.reverse();
            this.initMenuCutter();
            const { matched } = this.$router.currentRoute;
            activeByMatched.call(fixedMenu, matched);

            this.updateGuidePosition();
        },
        initMenuCutter() {
            const max = MAX_MENU_LENGTH;
            if (!this.$el) return;
            if (!this.menuCutter) {
                this.menuCutter = new MenuCutter(this.$el.querySelector('.jacp-aside-menu__menu-wrapper'), {
                    min: 1, // TODO:有两个固定元素还是一个？还是不需要固定？
                    max,
                    cut: this.fixedMenu ? this.fixedMenu.menus.length : 0,
                    itemHeight: 58 + 8, // menuItem height
                }, {
                    onLengthChanged: (len) => {
                        this.shownMenuLengh = len;
                    },
                });
                this.menuCutter.init();
            } else {
                this.menuCutter.setOption('cut', this.fixedMenu ? this.fixedMenu.menus.length : 0);
            }
        },
        cardChange() {
            // console.log(val, 'vvvvvv');
            // console.log(this.menuManager.current, 'vvvvvv221424');
            // this.menuManager.current.menus = val;
            // this.initMenu();
        },
    },
    watch: {
        '$route.path': {
            async handler(newValue, oldValue) {
                // 第一级path没有发生变化的时候不做处理
                if (getCurrentModuleKey(newValue) === getCurrentModuleKey(oldValue)) {
                    return;
                }
                const modulekey = getCurrentModuleKey(newValue);

                this.refreshMenus(modulekey);
                if (modulekey) {
                    updateModuleFootprint(modulekey);
                }
            },
            immediate: true,
        },

        /* 两个对于显示菜单的特殊处理
            1. 追加更多菜单
            2. TODO: 根据当前所处应用追加该应用入口，如果已存在则不追加 ，需要看一下从哪里取最合适
        */
        'menuManager.current': {
            immediate: true,
            handler() {
                this.refreshMenus(getCurrentModuleKey());
                this.updateGuidePosition();
            },
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
@menu-background: #fff;
@menu-background--hover: #e9f4fd;
@menu-background--actived: var(--color--base--background);
@menu-width--collapsed: 72px;
@menu-item-height: 58px;
@menu-shadow:   0 0 8px 0 rgba(48,49,51,0.1);
@menu-popper-shadow:   0 4px 24px 0 rgba(48,49,51,0.2);
.jacp-aside-menu{
  background-color: @menu-background;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  box-shadow: @menu-shadow;
  z-index: 99;
  .el-scrollbar__bar.is-vertical{
      width: 0!important;
  }
  .el-scrollbar__wrap{
      overflow: auto;
  }

  &--collapsed{
    width: @menu-width--collapsed;
    min-width: @menu-width--collapsed;
    transition: all 0.2s;

      [collapse] {
        display: none;
      }
  }
  &__logo{
    color: var(--color--primary);
    transition: all 0.3s;
    padding: var(--gutter--medium) 0;
    margin-left: 8px;
    margin-bottom:var(--gutter--mini);
    overflow: hidden;
    i{
        font-size: 24px;
        color: inherit;
    }
  }

  &__header-menu{
    justify-items: stretch;
  }

    .jacp-aside-menu__menu-view--hideChildren{
        .jacpbiz-menu-item__children-wrapper{
            display: none;
        }
    }
    // 以上为布局相关，以下为复写了menu的一些内部默认样式
    // override
  .jacpbiz-menu{
      height: auto; // 在safari中需要给父元素设置一个height值或者auto，否则子元素中的100%无法生效
      .jacpbiz-menu-item{
          color: var(--color--regular--content);
      }
      &.jacpbiz-menu--shrink{
        width: @menu-width--collapsed;
        .jacpbiz-menu-item--actived{
                &> .jacpbiz-menu-item__self{
                    background-color: var(--color--base--background);
                    color: var(--color--base--content);
                    font-weight: 600;
                }
            }
        // 只有一级的样式做特殊处理
        & [level='1']{
            // padding: 4px 8px;
            &>.jacpbiz-menu-item__self{
                flex-direction: column;
                height: 58px;
                line-height: 18px;
                width: 100%;
                padding-top: var(--gutter--base);
                margin: 4px 0;
                border-radius: var(--radius--medium);
                .jacpbiz-menu-item__name{
                    display: block!important;
                    margin-top: var(--gutter--mini);
                    font-size: var(--font-size--description);
                    max-width: 64px;
                    overflow:hidden;
                    text-overflow: ellipsis;
                }
                .jacpbiz-menu-item__icon{
                    width: 20px;
                }
            }
            &:hover>.jacpbiz-menu-item__name {
                display: none;
            }
            &.jacpbiz-menu-item--has-children{
                position: relative;
                // 1. hasChildren && in-actived-path
                &.jacpbiz-menu-item--in-actived-path{
                    &>.jacpbiz-menu-item__self{
                        background-color: var(--color--base--background);
                    }
                    /* 一级菜单先不要角标了 */
                   /*  &:after{
                        border-bottom: 8px solid var(--color--primary);
                    } */
                }
                // 2. hasChildren && hover
                &:hover{
                    /* &:after{
                         border-bottom: 8px solid var(--color--primary--hover);
                    } */
                }
                // 2. hasChildren
                /* &:after {
                    display: block;
                    position: absolute;
                    right: 12px;
                    bottom: 24px;
                    content: ' ';
                    width: 0;
                    height: 0;
                    border-bottom: 8px solid var(--color--base--border--hover);
                    border-left: 8px solid transparent;
                    transition: border 0.2s;
                } */
            }
        }
    }
  }
  .jacpbiz-menu-item[level="3"] .jacpbiz-menu-item__self{
    padding-left: 78px;
  }
  .jacpbiz-menu__scroll{
      .j-scrollbar--hidden();
  }
  // theme变成light以后有一些样式需要调整
  // TODO: menu light主题二级菜单的背景是黑色。。是故意而为之的么？
  [data-popper-escaped]>.jacpbiz-menu-item__children{
      min-width: 144px;
      background: @menu-background;
      box-shadow: @menu-popper-shadow;
      border-radius: var(--radius--medium);
      padding-bottom: 0;
      & .jacpbiz-menu-item{

        &:hover{
            background: @menu-background--hover;
        }
      }
    .jacpbiz-menu-item__name{
        color: var(--color--regular--content);
    }
    &>.jacpbiz-menu-item__name{
        display:none;
    }
  }
}

</style>
