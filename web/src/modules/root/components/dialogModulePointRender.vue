<template>
    <div>
        <jacp-button
            type="text"
            :disabled="disabled || !addon"
            @click="showDialog"
        >
            <slot name="button">
                +添加
            </slot>
        </jacp-button>
        <el-dialog
            :title="$attrs.title"
            v-bind="$attrs"
            :visible.sync="dialogVisible"
            :custom-class="dialogScopedClass.mod({
                noheader: !$attrs.title,
                full:true,
            }).cn"
            append-to-body
        >
            <div>
                <ModulePointRender
                    :addon="addon"
                    :context-params="contextParams"
                    :loader-options="{
                        timeout: 5000
                    }"
                />
            </div>

            <slot />
        </el-dialog>
    </div>
</template>

<script>
import ModulePointRender from '@/modules/root/components/modulePointRender';

export default {
    name: 'DialogModulePointRender',
    inheritAttrs: false,
    props: {
        ...ModulePointRender.props,
        disabled: { type: Boolean, default: false },
        onUpdated: { type: Function, default: () => {} },
    },
    components: { ModulePointRender },
    data() {
        return {
            dialogVisible: false,
            dialogScopedClass: this.$scopedClass('j-dialog'),
        };
    },

    methods: {
        showDialog() {
            this.dialogVisible = true;
        },
        hideDialog() {
            this.dialogVisible = false;
        },
    },
};
</script>
