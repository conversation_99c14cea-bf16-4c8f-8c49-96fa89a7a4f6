<template>
    <ul :class="[scopedClass.el('grid').cn, 'j-mgt16']">
        <LocalMenuItem
            v-for="(item, index) in data"
            :key="`${item.id}-${index}`"
            :menu-item="item"
            :class="scopedClass.el('item').cn"
            @click.native="() => onClick(item)"
        >
            <template v-slot="{menuItem}">
                <div :class="scopedClass.el('item-content').cn">
                    <span>
                        <jacp-text
                            size="16"
                            font-weight="600"
                        > {{ menuItem.name }}</jacp-text>
                        <i
                            :class="[
                                scopedClass.el('item').el('status').cn,
                                item.status ? 'el-icon-success' : '', 'j-mgl8'
                            ]"
                        /></span>

                    <ul :class="[scopedClass.el('addons').cn, 'j-mgt8']">
                        <li
                            v-for="addon in menuItem.addons"
                            :key="addon.id"
                            :title="addon.desc"
                        >
                            <div :class="scopedClass.el('addons-item').mod('cut').cn">{{ addon.desc }}</div>
                            <el-divider direction="vertical" />
                            <jacp-text
                                type="disable"
                                size="12"
                            >
                                {{ addon.extensionPoint.name }}
                            </jacp-text>
                        </li>
                    </ul>

                    <jacp-button
                        style="pointer-event: default"
                        :disabled="disabled"
                        :class="scopedClass.el('item').el('button').cn"
                        :type="disabled ? 'info' : (!item.status ? 'primary' : 'danger')"
                        :on-click="() => !disabled && onChange(item, 'status', !item.status, item.status)"
                        size="mini"
                        plain
                        @click.native.stop
                    >
                        <template v-if="!disabled">
                            {{ !item.status ? '开启' : '关闭' }}
                        </template>
                        <template v-else>
                            <el-tooltip
                                :disabled="!disabled || !disabledText"
                                :content="disabledText"
                            >
                                <span>{{ item.status ? '已开启' : '已关闭' }}</span>
                            </el-tooltip>
                        </template>
                    </jacp-button>
                </div>
            </template>
        </localmenuitem>
    </ul>
</template>
<script>
import LocalMenuItem from '@/modules/root/components/menu/menuItem';

export default {
    name: 'Manage',
    props: {
        data: { type: Array, default: () => [] },
        disabled: { type: Boolean, default: true },
        disabledText: { type: String, default: '' },
        onChange: { type: Function, default: () => {} },
        onClick: { type: Function, default: () => {} },
    },
    components: { LocalMenuItem },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-app-extension'),
        };
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
@--item-min-width: 248px;
@--item-height: 148px;
.jacp-app-extension{
  &__grid{
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(@--item-min-width, 1fr)) ;
    grid-auto-flow: row dense;
    grid-template-rows: repeat(3, @--item-height);
    grid-gap: var(--gutter--medium) var(--gutter--medium);
    &ul,li{
      list-style: none;
    }
  }
  &__item{
    align-items: flex-start;
    position: relative;
    cursor: pointer;
    .setting-menu-item__info{
        height: 100%;
    }
    &__button{
        padding: var(--gutter--mini) var(--gutter--small);
        width: fit-content;
        border-radius: var(--radius--small);
        font-weight: normal;
        &.is-plain{
            background: transparent;
        }
    }
    &__status{
        color:var(--color--secondary);
        position: absolute;
        right: 15px;
        top: 10px;
        font-size: 20px;
    }
  }
   &__item-content{
       display: flex;
       flex-direction: column;
       height: 100%;
   }
   &__addons{
       flex-basis: 100%;
       li{
           display: flex;
           flex-wrap: nowrap;
           white-space: nowrap;
       }
       &-item--cut{
           .get-content-overflow(1);
           font-size: var(--font-size--description);
       }
   }
}
</style>
