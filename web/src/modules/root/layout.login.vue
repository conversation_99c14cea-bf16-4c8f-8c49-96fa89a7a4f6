<template>
    <div class="login-box">
        <div class="login-box-content">
            <div class="login-box-content-card">
                <div class="card-box">
                    <!-- <div class="card-box-header">
                        <span>{{ title }}</span>
                    </div> -->
                    <div class="card-box-content">
                        <el-progress
                            v-if="havePromise"
                            type="circle"
                            :percentage="percentage"
                            width="100"
                            class="card-box-content-progress"
                        />
                        <i
                            class="card-box-content-icon el-icon-warning"
                            :class="havePromise ? 'card-box-content-icon-defalut': 'card-box-content-icon-warning'"
                            v-if="!havePromise"
                        />
                        <span v-if="beginUse" class="card-box-content-progressText">{{ progressText }}</span>
                        <span v-html="content"></span>
                    </div>
                    <!-- <div class="card-box-footer">
                        <el-button
                            @click="goBack"
                            :loading="isloading"
                        >
                            {{ footerBack }}
                        </el-button>
                        <el-button
                            type="primary"
                            :disabled="!havePromise"
                            @click="goPage"
                            :loading="isloading"
                        >
                            {{ footerConfirm }}
                        </el-button>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { uaosHttp } from '@/plugins/http';

export default {
    components: {
    },
    computed: {
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        this.timer = null;
    },
    data() {
        const { statusCode = 406 } = this.$route.query;
        return {
            dialogVisible: true,
            title: '用户登录',
            content: '<p>亲爱的行云用户，检测到您初次使用行云系统，</p>正在进行数据初始化，预计需要15s，感谢您的理解与支持！',
            footerBack: '返回京东云',
            footerConfirm: '立即使用',
            percentage: 0,
            beginUse: false,
            isloading: false,
            progressText: '数据初始化中，感谢您的耐心等待~ ',
            havePromise: true,
            statusCode,
            timer: null,
        };
    },
    watch: {
        statusCode: {
            handler(val) {
                if (Number(val) === 406) {
                    this.content = '<p>您的账号暂时没有登录行云系统的权限。</p><p>请联系您的管理员添加您的账号到行云系统中！</p>';
                    this.havePromise = false;
                }
                if (Number(val) === 412) {
                    this.content = '<p>您对应的主账号没有开通行云系统的权限。</p><p>请联系您的管理员添加到行云系统中！</p>';
                    this.havePromise = false;
                }
                if (Number(val) === 402) {
                    this.goPage();
                }
            },
            immediate: true,
        },
    },
    methods: {
        init() {
        },
        goBack() {
            const data = window.location.origin.split('.');
            const registry = data.pop();
            const subDomain = data.pop();
            window.location.href = `https://${subDomain}.${registry}`;
        },
        goPage() {
            uaosHttp.post('/v1/tenant/create').then(() => {
                this.timer = setInterval(() => {
                    this.beginUse = true;
                    this.percentage += 1;
                    if (this.percentage >= 100) {
                        clearInterval(this.timer);
                        window.location.href = window.location.origin;
                    }
                }, 150);
            }).catch(() => {
                this.beginUse = false;
            });
        },
    },
};
</script>

<style lang='less' scoped>
.login-box {
    // background-image: url('../../assets/images/appstore-banner.png');
    // background-image: url('../../assets/images/<EMAIL>');
    // background-image: url('../../assets/images/<EMAIL>');
    // background-image: url('../../assets/images/holiday_bg_kanban.png');
    height: 100vh;
    background-color: #edf3f8;
    &-content {
        // width: 600px;
        display: flex;
        justify-content: center;
        align-items: center;
        // background-color: #fff;
        height: 100vh;
        &-card {
            width: 464px;
            border-radius: 4px;
            border: 1px solid #ebeef5;
            background-color: #fff;
            overflow: hidden;
            color: #303133;
            transition: .3s;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            .card-box {
                &-header {
                    height: 48px;
                    border-bottom: 1px solid #ebeef5;
                    padding: 13px 16px;
                    box-sizing: border-box;
                    font-size: 16px;
                    font-weight: 600;
                }
                &-content {
                    height: 326px;
                    padding: 8px;
                    line-height: 10px;
                    display: flex;
                    font-size: 14px;
                    align-items: center;
                    flex-direction: column;
                    &-button {
                        display: inline-block;
                        width: 200px;
                        height: 50px;
                    }
                    &-icon {
                        font-size: 100px;
                        color: rgba(38,149,241,1);
                        margin: 32px;
                    }
                    &-icon-defalut {
                        color: rgba(38,149,241,1);
                    }
                    &-icon-warning {
                        color: rgba(255,141,0,1);;
                    }
                    span {
                        text-align: center;
                    }
                    &-progress {
                        margin: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    &-progressText {
                        font-weight: 600;
                        font-size: 16px;
                        margin-bottom: 16px;
                    }
                }
                &-footer {
                    height: 64px;
                    padding: 16px;
                    text-align: right;
                }
                .el-button {
                    border-radius: 6px;
                    height: 32px;
                }
            }
        }
    }
}
</style>
