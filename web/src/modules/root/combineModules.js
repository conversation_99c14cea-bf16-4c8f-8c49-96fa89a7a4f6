
import Vue from 'vue';
import { JModule, Resource } from '@jmodule/client';
import AppModule from '$module/models/appModule';
import store from '$platform.store';
import router from '$platform.router';
import { isFunction } from 'lodash';
import {
    initAppModule, initPageModule, initIframeModule,
} from './utils';
import { registerExtensionPoint } from '$module/utils/extension';
// import { menuManager } from '@/modules/root/models/menu';

Vue.config.productionTip = false;
Vue.config.devtools = JModule.debug || process.env.NODE_ENV === 'development';
JModule.debug = true;
// JModule.options.autoApplyStyle = false;
// 去掉全局的auto配置，根据场景来选择加载
// JModule.options.autoApplyScript = false; // default true
Resource.enableAsyncChunk();

const routeChildren = [];
// FIXME: remove
export function registerLocalModules() {
    const modulesBootstrap = require.context('@/modules', true, /\.\/[^/]+\/index\.js$/);
    modulesBootstrap.keys().forEach((item) => {
        if (item.includes('jacpRoot')) { // 忽略模块形式启动的root
            return;
        }
        if (item.includes('teamspace')) { // 忽略团队空间
            return;
        }
        if (item.includes('demand')) { // 忽略需求管理
            return;
        }
        const localModule = modulesBootstrap(item).default || {};
        console.log('localModule===', localModule);
        const { routes: moduleRoutes, store: moduleStore } = localModule;
        const moduleKey = item.split('/')[1];
        // 更新路由
        if (moduleRoutes && Array.isArray(moduleRoutes)) {
            if (!item.includes('root')) { // 忽略root
                routeChildren.push(...moduleRoutes);
                // console.log(routeChildren);
                // moduleRoutes.forEach(route => router.addRoute('root', route));
            } else if (moduleRoutes) {
                moduleRoutes.forEach(route => router.addRoute(route));
            }
        }

        // 注册 store
        if (moduleStore) {
            Object.keys(moduleStore).forEach((storeModuleName) => {
                store.registerModule(storeModuleName, moduleStore[storeModuleName]);
            });
        }
        setTimeout(() => {
            // 注册扩展点--myzone--左右组件
            registerExtensionPoint(localModule, { ...localModule, key: moduleKey });
        }, 1);
    });
    routeChildren.forEach(route => router.addRoute('root', route));
    routeChildren.length = 0;
}
// 注掉 需求管理 团队空间
const localModules = [
    'myzone',
    'demands',
    'teamspace',
];
const isLocalModule = (register = {}) => localModules.includes(register.key);

export async function registerRemoteModules() {
    const allModules = await AppModule.getModuleList({ status: 'active' });
    // 过滤掉 个人中心，需求管理 团队空间
    // 其他服务使用JModule 注册进去
    const jmodules = allModules
        .map(item => item.register)
        .filter(item => !isLocalModule(item) && item.url);
    console.log({ allModules, jmodules });
    console.warn('=====');
    JModule.registerModules(jmodules)
        .then(modules => modules.forEach((module) => {
            if (module.type === 'iframe') {
                initIframeModule(module);
            } else {
                module.load('preload');
            }
        }));
}


JModule.addHook('afterRegisterModules', async (modules) => {
    modules.forEach(async (module) => {
        // byLGH: 开发调试环境会走这里（但其实可以去掉 if，走统一流程：fetchData -> registerModule -> new AppModule）
        // byLGH: 不然开发调试还需要先去平台上注册
        if (!AppModule.hasInstance(module.key)) {
            // eslint-disable-next-line no-new
            new AppModule({
                code: module.key,
                register: {
                    ...module,
                    topRouter: module.meta?.topRouter,
                },
            });
        }
    });
});

// 在原来的位置，由于在 await 之后，将导致开发中的子应用的路由一定概率不能被添加进去
// 它只负责添加路由，404处理 与 特定module无关，故删掉；
JModule.addHook('afterInit', async (module, pkg) => {
    // 记录类型字段，区分处理逻辑，app 为独立框架子应用，page 为之前的子页面形式的应用
    module.type = isFunction(pkg.mount) ? 'app' : 'page';
    if (module.type === 'app') {
        initAppModule(module, pkg);
    } else {
        initPageModule(module, pkg);
    }

    return [module, pkg];
});


JModule.addHook('afterExports', (module, pkg = {}) => {
    registerExtensionPoint(module, pkg);
    return [module, pkg];
});
