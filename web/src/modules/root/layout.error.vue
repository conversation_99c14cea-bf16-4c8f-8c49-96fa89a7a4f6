<script>
const DEFAULT_MSG = {
    404: '抱歉!  您访问的页面失联啦...',
    502: '咦，页面出现故障了，返回首页试试？',
};
export default {
    name: 'Error',
    props: {
        code: { type: [String, Number], default: 404 },
        message: { type: String },
    },
    data() {
        // eslint-disable-next-line
        const img = require(`@/assets/images/errorpage/${this.code}@2x.png`);
        return {
            img,
        };
    },
    computed: {
        errorMsg() {
            if (this.message) {
                return this.message;
            }
            return DEFAULT_MSG[this.code];
        },
    },
    // eslint-disable-next-line
    render(h) {
        const { errorMsg, $scopedSlots } = this;
        return (<div v-refresh class="j-server-error">
            <img class="j-server-error__img" src={this.img}/>
            <p>{ `${errorMsg}` }</p>
            {
                $scopedSlots.opration ? $scopedSlots.opration(this.errorMsg) : <p>
            您可以尝试：<a href="/" class="j-server-error__link">返回首页</a>
                </p>
            }

        </div>);
    },
    directives: {
        refresh: {
            inserted(el, binding, vnode) {
                const that = vnode.context;
                document.addEventListener('keydown', that.refresh, false);
            },
            unbind(el, binding, vnode) {
                const that = vnode.context;
                document.removeEventListener('keydown', that.refresh, false);
            },
        },
    },
    methods: {
        refresh(e) {
            const keyCode = e.keyCode || e.which || e.charCode;
            const ctrlKey = e.ctrlKey || e.metaKey;
            switch (true) {
            case keyCode === 116:
            case ctrlKey && keyCode === 116:
            case ctrlKey && keyCode === 82:
                this.$router.go(-1);
                // location.href = '/';
                e.preventDefault();
                return false;
            default:
                return true;
            }
        },

    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.j-server-error{
    margin: auto;
    position: absolute;
    top: 25vh; left: 0; right: 0;
    height: min-content;
    display: flex;
    flex-direction: column;
    text-align: center;
    color: @fontColor;
    &__img{
        width: 240px;
        margin: 0 auto;
        margin-bottom: 16px;
    }
    &__link{
        margin-left: 16px;
    }
    font-size: 14px;
}
</style>
