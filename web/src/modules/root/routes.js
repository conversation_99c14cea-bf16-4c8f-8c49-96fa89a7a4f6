import Index from './layout.index';
import Error from '@/modules/root/layout.error';
import helpcenter from '@/modules/helpcenter/layout.index';
import login from '@/modules/root/layout.login';
import { routes as openRoutes } from '@/modules/open/routes';
import { routes as appStoreRoutes } from '@/modules/appstore/routes';
import { routes as competitionRoutes } from '@/modules/competition/routes';
// import accountSetting from '@/modules/setting/accountSetting';
import layout from '@/modules/setting/layout';
import ModuleIndex from './layout.module';

export const NotFoundRoutes = {
    path: '*',
    component: Index,
    props: {
        fromNonExisting: true,
    },
};
export default [{
    path: '/(en|\\zh-cn)?',
    name: 'root',
    component: Index,
    redirect: { name: 'myzone' }, // 默认进入个人工作台
    children: [{
        path: 'rootModuleCenter',
        name: 'rootModuleCenter',
        component: ModuleIndex,
    }, {
        path: '/accountSetting', // 账号设置页面
        name: 'accountSetting',
        component: layout.accountSetting,
        redirect: { name: 'basicInfo' }, // 默认进入基本信息
        children: [{
            path: 'basicInfo',
            name: 'basicInfo',
            component: layout.basicInfo,
        }, {
            path: 'fixPassword',
            name: 'fixPassword',
            component: layout.fixPassword,
        }, {
            path: 'fixAvator',
            name: 'fixAvator',
            component: layout.fixAvator,
        }],
    }],
    // children: routes,
}, {
    path: '/helpcenter',
    name: 'helpcenter',
    component: helpcenter,
},
{
    path: '/login',
    name: 'loginPage',
    component: login,
},
{
    path: '/error/:code',
    name: 'serverError',
    component: Error,
    props: route => ({ code: route.params.code }),
}, ...openRoutes, appStoreRoutes, competitionRoutes,
NotFoundRoutes,
];
