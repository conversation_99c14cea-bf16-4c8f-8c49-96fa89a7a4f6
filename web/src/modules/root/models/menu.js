import Vue from 'vue';
import img from './app_store_icons_more_app.png';
import {
    JacpMenuManager,
} from '@jacpbiz/menu-business';
import {
    loadMenu, creatRouteMenu, createSubRouteMenu,
} from './menuService';

export * from './menuService';
export * from './menuActions';
// 应用中心常驻菜单
export const rootModuleCenterMenu = {
    code: 'rootModuleCenter', // -> id
    label: '更多', // -> name
    icon: `${window.location.origin}${img}`,
    url: 'rootModuleCenter',
    attrs: {}, // -> clickParams
    // scopes: ['more', 'visibleFavo'],
};


// 初始化菜单的options
// manager用于主菜单的view层
// 2021.8.20 折叠功能去掉了,只支持收起状态，不再展开
export const menuManagerOptions = {
    shrink: true, // 初始值
    menuProvider: () => loadMenu.call(null, '/favorite'),
};
// 常驻应用
export const menuManager = Vue.observable(new JacpMenuManager(menuManagerOptions));
// 应用中心+当前页面一级菜单
export const fixedMenu = creatRouteMenu(
    [rootModuleCenterMenu],
    { menuScope: 'hideChildren' },
);
export const fixedMenuManager = Vue.observable(new JacpMenuManager({
    shrink: true, // 初始值
    menuProvider: () => Promise.resolve(fixedMenu),
}));

// 二级菜单
export const subMenu = createSubRouteMenu();

// 一级菜单的最多显示个数
export const MAX_MENU_LENGTH = 9;

export const openMenu = creatRouteMenu([{
    code: 'jacpOpen', // -> id
    label: '开放平台', // -> name
    url: 'jacpOpen',
}, {
    code: 'innerOpenApi', // -> id
    label: '开放文档', // -> name
    accessMode: 2,
    url: '/open/doc/detail/111/开放平台.md',
}, {
    code: 'jacpAppStoreIndex', // -> id
    label: '应用管理', // -> name
    url: 'jacpAppStore',
},
// {
//     code: 'appOps', // -> id
//     label: '平台运营', // -> name
//     url: 'appOps', // ->
// },
]);
