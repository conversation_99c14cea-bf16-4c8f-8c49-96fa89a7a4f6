import store from '@/store';
import { jacpSecurityHttp } from '@/plugins/http';

export default class AppServer {
    static #serverConfig;

    static #appServer;

    constructor(option = {}) {
        const self = this;
        self.appCode = option.appCode;
        self.accessMode = option.accessMode;
        self.appOrgId = option.appOrgId;
        self.appOrgName = option.appOrgName;
        self.appType = option.appType;
        self.appTypeName = option.appTypeName;
        self.appUserRelList = option.appUserRelList;
        self.auditStatus = option.auditStatus;
        self.block = option.block;
        self.createTime = option.createTime;
        self.creator = option.creator;
        self.createTime = option.createTime;
        self.desc = option.desc;
        self.helpUrl = option.helpUrl;
        self.licenseStatus = option.licenseStatus;
        self.owner = option.owner;
        self.permission = option.permission;
        self.permissions = option.permissions;
        self.status = option.status;
        self.statusName = option.statusName;
        self.tags = option.tags;
        self.techSupportNumber = option.techSupportNumber;
        self.url = option.url;
        self.code = option.code;
        self.id = option.id;
        self.name = option.name;
        self.icon = option.icon;
        return self;
    }

    static getAppServer() {
        if (AppServer.#appServer) {
            return false;
        }
        return jacpSecurityHttp.post('/appstore', []).then((res) => {
            const data = new Map();
            res.forEach((item) => {
                data.set(item.code, new AppServer(item));
            });
            store.commit('app/setappList', data);
            AppServer.#appServer = data;
        });
    }

    static getServiceConfig() {
        if (AppServer.#serverConfig) {
            return false;
        }
        return jacpSecurityHttp.get('/bizConfig/getServiceConfig').then((res) => {
            store.commit('app/setHelpDoc', res.help);
            store.commit('app/setServiveConfig', res);
            AppServer.#serverConfig = res;
        });
    }

    static getAppStore(labelIdList = []) {
        return jacpSecurityHttp.post('/appstore', labelIdList).then(res => res.map(data => new AppServer(data)));
    }
}
