export const Ruler = new Map();
export class MenuBridge {
    static defineRuler(key, activeRuleFn = () => {}) {
        if (Ruler.get(key)) {
            console.log('Ruler duplicate');
        }
        Ruler.set(key, activeRuleFn);
    }
}
// eslint-disable-next-line no-underscore-dangle
// window.__jagile__navigate__submenu__ = MenuBridge;
/* MenuBridge demo */
/* MenuBridge.defineRuler('art_store', (to, menuItems = []) => {
    // 写你自己的rules
    console.log('test-manage get actived menuItem', menuItems);
    return menuItems[0];
});
MenuBridge.defineRuler('jStatisticsRoot', (to, menuItems = []) => {
    let target = menuItems[0];
    // 写你自己的rules
    if (to.path.startsWith('/jStatisticsRoot/editMeta')) {
        target = menuItems[0];
    } else if (to.path.startsWith('/jStatisticsRoot/statisticsDashbord') || to.path.startsWith('/jStatisticsRoot/singlePointRequirement')) {
        target = menuItems.find(menuItem => menuItem.clickParams?.url === 'statisticsDashbord');
    } else {
        target = menuItems.find(menuItem => menuItem.id === to.name);
    }

    return target;
});
 */
