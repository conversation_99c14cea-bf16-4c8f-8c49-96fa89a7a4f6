
// import findIndex from 'lodash/findIndex';
import {
    toggleFold,
} from '@jacpbiz/menu-business';

import { Message } from 'element-ui';
import isFunction from 'lodash/isFunction';
import pick from 'lodash/pick';
import uniqBy from 'lodash/uniqBy';
import router from '$platform.router';
import Dialog from '@/models/dialog';
import RootMenus from '@/models/rootMenus';
import LocalMenuShare from '@/modules/root/components/menu/menuShare';
import { menuItemAdapterToServer } from './menu';
import { navigateToUrl } from '$module/utils/patchRoute';
import { jacpMenuItemAdapter } from './menuService';

// 根据menuItem生成路由信息
export function generateMenuItemHref(menuItem = {}) {
    const { url: routeName, menuData: params = {} } = menuItem.clickParams || {};
    if (routeName.startsWith('/')) {
        // 约定attrs里只放置菜单的searchParams
        const search = Object.keys(params).length
            ? `?${Object.keys(params).map(key => `${key}=${params[key]}`).join('&')}`
            : '';
        return new URL(`${routeName}${search}`, window.location.href);
    }
    if (router && router.resolve({ name: routeName }).resolved?.matched?.length) {
        const { resolved } = router.resolve({ name: routeName, params });

        if (resolved.path) {
            return new URL(resolved.path, window.location.href);
        }
    }
    return new URL(`/${menuItem?.meta?.appCode}`, window.location.href);
}
export function clearMenuItem(filterFn) {
    this.menus = isFunction(filterFn) ? this.menus.filter(filterFn) : [];
    // TODO: 还有children的index需要留着
    this.menuItemIndex = pick(this.menuItemIndex, this.menus.map(menuItem => menuItem.id));
}
export async function saveFavo() {
    await RootMenus.savePersonalMenu({
        menus: this.menus.map(menuItemAdapterToServer),
    });
}
export function removeFromFavo(menuItem) {
    const targetMenuItem = this.find(menuItem.id);
    this.remove(targetMenuItem);
    // removeMenuItem.call(this, menuItem);
    RootMenus.savePersonalMenu({
        menus: uniqBy(this.menus, 'id').map(menuItemAdapterToServer),
    }).catch(() => {
        this.add([menuItem]);
    });
}
export async function addToFavo(menuItem) {
    const newMenu = jacpMenuItemAdapter(menuItem);
    // const newMenu = menuItem;
    if (!this.find(menuItem.id)) {
        this.add([newMenu]);
        await RootMenus.savePersonalMenu({
            menus: this.menus.map(menuItemAdapterToServer),
        }).catch(() => {
            this.remove(menuItem);
        });
    }
}
// width：464px
export function shareMenu(menuItem) {
    const url = generateMenuItemHref(menuItem);
    if (url.href) {
        Dialog.alert({
            title: '获取分享链接',
            slot: LocalMenuShare,
            slotProps: {
                value: new URL(url.href, window.location.href),
                id: 'shareUrl',
            },
            showIcon: false,
        });
    } else {
        Message.error('获取分享链接异常');
    }
}

export function openModuleDetail(menuItem) {
    const routeUrl = router.resolve({
        name: 'jacpAppDetail',
        params: { code: menuItem.id },
    });
    window.open(routeUrl.href, menuItem.id);
}

export function openModuleDetailComment(menuItem) {
    const routeUrl = router.resolve({
        name: 'jacpAppDetail',
        params: { code: menuItem.id },
        query: { tab: 'comment' },
    });
    window.open(routeUrl.href, menuItem.id);
}

export function toggleMenuActiveById(menuItemId) {
    const existing = this.activeById(menuItemId);
    if (!existing && this.activedMenu) {
        // TODO: 这个变量名字需要publish一个新的business包
        this.activedMenu.actived = false;
    }
}
export function toggleMenuActive(flag) {
    if (this.activedMenu) {
        this.activedMenu.actived = flag;
    }
}

export function isMenuItemParent(menuItem, id) {
    const { parents = [] } = this.menuItemIndex[menuItem.id] || {};
    return parents.includes(id);
}

export function isActiveInRouteMatch(menuItem = {}, matched = []) {
    const matchedNames = matched.map(item => item.name);
    // console.log(`finding....${matchedNames.join(',')}========`, menuItem?.clickParams?.url);
    return matchedNames.includes(menuItem?.clickParams?.url); // 这个url里存的是routeName
}
export function isActivedByPath(path, currentLocaltion = window.location.href) {
    return currentLocaltion.includes(path);
}
export function activeByMatched(matched = []) {
    let matcheMenuItem;
    const reversedMatched = [...matched].reverse();
    reversedMatched.some((match) => {
        const { name } = match;
        const target = this.findPath(name);
        matcheMenuItem = target.pop();
        this.active(matcheMenuItem);
        return matcheMenuItem;
    });
    if (matcheMenuItem) {
        return;
    }
    Object.keys(this.menuItemIndex).some((menuItemId) => {
        const menuItem = this.findPath(menuItemId).pop();
        if (menuItem
            && menuItem.clickParams?.url?.startsWith('/')
            && isActivedByPath(menuItem.clickParams?.url)) {
            matcheMenuItem = menuItem;
        } else if (menuItem && isActiveInRouteMatch(menuItem, reversedMatched)) {
            // console.log('FOUNDED by menuItemIndex!!!', menuItem.id);
            matcheMenuItem = menuItem;
        }
        this.active(matcheMenuItem);
        return matcheMenuItem;
    });
}

export function menuClickHandler(canToggleFold = true) {
    return function jacpClickHandle(menuItem) {
        if (menuItem.hasChildren) {
            if (canToggleFold) {
                toggleFold.bind(this)(menuItem);
                return;
            }
        }
        this.active(menuItem);
        // const { url: routeName } = menuItem.clickParams || {};
        const url = generateMenuItemHref(menuItem);
        // 注册到vue-router的直接通过router.push进行跳转


        // 路由外的url使用自定义的pushState进行跳转
        console.log(menuItem, 'ssssssss');
        if (menuItem && menuItem.accessMode && Number(menuItem.accessMode) === 2) {
            window.open(menuItem.clickParams.url, '_blank');
            return;
        }
        if (!url.route && url.pathname) {
            navigateToUrl.call(url);
            return;
        }
        // TODO: 菜单上做一个loading？
        // 这里也不行，不是有效path
        Message.warning({ message: '菜单资源准备中，请稍后再试。' });
    };
}
