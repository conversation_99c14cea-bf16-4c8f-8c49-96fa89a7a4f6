import { jacpSecurityHttp } from '@/plugins/http';
import store from '$platform.store';

let isActivedLoaded = false;

export default class AppModule {
  static #modulesMap = new Map();

  #moduleInstance = null; // instanceof JModule

  constructor({
      id,
      appId,
      code,
      name,
      status,
      register = {},
      provider,
  } = {}) {
      let self = this;
      if (AppModule.hasInstance(code)) {
          self = AppModule.getInstance(code);
      }
      self.id = id;
      self.appId = appId;
      self.status = status;
      self.code = code; // TODO: 这个code和resisters里的key是一致的，应该保持全局唯一才对？
      self.name = name;
      self.register = register;
      self.provider = provider;


      AppModule.#modulesMap.set(code, self);
      return self;
  }

  set moduleInstance(moduleInstance) {
      this.#moduleInstance = moduleInstance;
  }

  get moduleInstance() {
      return this.#moduleInstance;
  }

  initModule(module) {
      this.moduleInstance = module;
      return this.moduleInstance;
  }

  // TODO: 移除moduleManagement.js和moduleOption.js
  //   updateStatus() {}
  //   remove() {}
  //   update() {}

  static create() {}

  static getModuleList(params = {}) {
      const { status } = params;
      if (status === 'active' && isActivedLoaded) {
          return Promise.resolve(Array.from(AppModule.getInstanceList(module => module.status === 'active')));
      }
      return jacpSecurityHttp.get('/appWeb/person/default')
          .then((data = []) => {
              // TODO: remove. 这个active状态已经没用了
              if (status === 'active') {
                  isActivedLoaded = true;
              }
              const modulesMap = new Map();
              const result = data.reduce((arr, m) => {
                  modulesMap.set(m.code, m);
                  if (m.register && m.register.url) {
                      arr.push(new AppModule(m));
                  }
                  return arr;
              }, []);
              store.commit('root/setActiveModuleMap', modulesMap);
              return result;
          });
  }

  static getModuleListWithCode() {
      return jacpSecurityHttp.get('/appWeb/withAppcode');
  }

  static getInstance(code) {
      return AppModule.#modulesMap.get(code);
  }

  static hasInstance(code) {
      return AppModule.#modulesMap.has(code);
  }

  static getInstanceList(filterFn) {
      const values = Array.from(AppModule.#modulesMap.values());
      return typeof filterFn === 'function'
          ? values.filter(filterFn)
          : values;
  }

  static getActiveModule(code) {
      return AppModule.getModuleList({ status: 'active' }).then(() => AppModule.getInstance(code));
  }
}
