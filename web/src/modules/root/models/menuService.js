import Vue from 'vue';
import cloneDeep from 'lodash/cloneDeep';

import {
    Menu, MenuItem,
} from '@jacpbiz/menu-business';
import http from '@/plugins/http';
import { getCurrentModuleKey } from '$module/utils';
// import { Message } from 'element-ui';
import router from '$platform.router';
import { activeByMatched, menuClickHandler, clearMenuItem } from './menu';
// import devCenter from '../../open/model/devCenter';
import { Ruler } from './menuBridge';

export { MenuItem };
class JacpSubMenu extends Menu {
    // eslint-disable-next-line class-methods-use-this
    isRulerExist(key) {
        return Ruler.has(key);
    }

    activeByRuler(key, location) {
        if (Ruler.has(key)) {
            const activeMenu = Ruler.get(key).call(null, location, cloneDeep(this.menus));
            this.active(activeMenu);
            return activeMenu;
        }
        return undefined;
    }
}
const freezeMenuItemIds = [
    'myzone',
    'teamspace',
];

const defaultFolded = true;
const moduleMenuMap = {};
const scopedMenuItemAdapters = {
    hideChildren: (menu) => {
        menu.menus.forEach((menuItem) => {
            menuItem.scopes = menuItem.scopes ? [...menuItem.scopes, 'hideChildren'] : ['hideChildren'];
        });
    },
};
// 适配到本地的菜单
export function jacpMenuItemAdapter(menuData = {}) {
    const {
        block = {},
        tags = [],
        appCode = '', desc,
        disabled = false,
    } = menuData;
    return new MenuItem({
        id: menuData.code || menuData.id,
        appId: menuData.appId,
        accessMode: menuData.accessMode,
        name: menuData.label || menuData.name,
        licenseStatus: menuData.licenseStatus,
        clickAction: 'jacpClickHandle',
        clickParams: {
            url: menuData.url,
            menuData: menuData.attrs,
        },
        type: menuData.attrs?.type,
        icon: menuData.icon,
        scopes: menuData.scopes || ['jacp'],
        meta: {
            appCode,
            tags,
        },
        // 以下为行云追加的自用字段 TODO: 收到meta
        disabled,
        block, // 分组
        folded: defaultFolded,
        desc,
        freeze: freezeMenuItemIds.includes(menuData.code),
        children: (menuData.children || []).map(jacpMenuItemAdapter),
    });
}
// 保存到服务器的时候专为服务器需要的参数
export function menuItemAdapterToServer(menuItem = {}) {
    if (menuItem instanceof MenuItem) {
        const {
            id: code,
            name: label,
            icon,
            children,
            clickParams,
            disabled = false,
        } = menuItem;
        return {
            code,
            name: code, // FIXME:这没啥用啊
            label,
            icon,
            children: children?.map(menuItemAdapterToServer),
            attrs: { ...(clickParams?.menuData || {}), disabled },
            url: clickParams?.url,
        };
    }
    return menuItem;
}


export const creatRouteMenu = (
    menus = [],
    {
        menuScope,
        autoActiveByRoute = true,
        immediateActive = false,
        shrink = true,
        canToggleFold = false,
        apdater = jacpMenuItemAdapter,
    } = {},
) => {
    const menu = Vue.observable(new Menu(apdater ? menus.map(apdater) : menus));
    menu.defineClickAction(
        'jacpClickHandle',
        menuClickHandler(canToggleFold),
    );
    menu.shrink = shrink;
    menu.scope = menuScope;
    // menu.licenseStatus = menus.licenseStatus;
    if (menuScope in scopedMenuItemAdapters) {
        scopedMenuItemAdapters[menuScope](menu);
    }
    if (autoActiveByRoute) {
        router.afterEach(async (to) => {
            const { matched = [] } = to;
            activeByMatched.call(menu, matched);
        });
    }
    if (immediateActive) {
        activeByMatched.call(menu, router.currentRoute?.matched);
    }
    return menu;
};

// 获取常驻应用 loadMenu(1, '/favorite')
// 获取全部应用 loadMenu(1)
export async function loadMenu(path = '') {
    const result = await http.get(`v1/setting/personal/menus/self${path || ''}?type=1`)
        .catch(() => []);
    // const result = await devCenter.getAppStore([]);
    console.log('result,==', result);
    // result = result.filter(item => item.url !== 'teamspace');
    // result = result.filter(item => item.url !== 'demands');
    // console.log('result,==', result);
    return creatRouteMenu(result, { menuScope: 'hideChildren' });
}

// 获取单个应用的菜单
export async function loadModuleMenu(moduleKey) {
    if (!moduleKey) {
        return Promise.reject();
    }
    if (moduleMenuMap[moduleKey]) {
        return Promise.resolve(moduleMenuMap[moduleKey]);
    }
    const result = await http.cache().get(`v1/setting/personal/menus/appCode/${moduleKey}`);
    console.log('result,==result==', result);
    const menu = creatRouteMenu(result, { menuScope: 'hideChildren' });
    moduleMenuMap[moduleKey] = menu;
    return Promise.resolve(menu);
}
export const createSubRouteMenu = (
    menus = [],
    {
        autoActiveByRoute = true,
        shrink = true,
        canToggleFold = false,
        apdater = jacpMenuItemAdapter,
    } = {},
) => {
    const menu = Vue.observable(new JacpSubMenu(apdater ? menus.map(apdater) : menus));
    menu.defineClickAction(
        'jacpClickHandle',
        menuClickHandler(canToggleFold),
    );
    menu.shrink = shrink;
    if (autoActiveByRoute) {
        router.afterEach(async (to) => {
            const moduleKey = getCurrentModuleKey(to.path);
            const rootMenu = await loadModuleMenu(moduleKey);
            clearMenuItem.call(menu);
            if (rootMenu.menus.length) {
                menu.add(rootMenu.menus[0].children);
            }
            if (menu.isRulerExist(moduleKey)) {
                menu.activeByRuler(moduleKey, to);
            } else {
                const { matched = [] } = to;
                activeByMatched.call(menu, matched);
            }
        });
    }
    return menu;
};
