<template>
    <el-form
        ref="form"
        :model="form"
        label-width="80px"
        label-position="top"
        :rules="rules"
        class="csi"
    >
        <div class="csi-title">
            感谢您对行云的喜爱和支持，为了不断提升用户体验，诚邀您参加行云满意度调查(将有机会获得<font color="red">精美礼品</font>哦)。
        </div>
        <el-form-item
            label="1、你对行云的整体满意度评分？"
            prop="score"
        >
            <el-rate
                v-model="form.score"
            />
        </el-form-item>
        <el-form-item
            label="2、你经常使用行云哪些功能？"
            prop="flove"
        >
            <el-checkbox-group
                v-model="form.used"
            >
                <el-checkbox
                    v-for="item in modules"
                    :label="item"
                    :key="item"
                    class="csi-checkbox"
                >
                    {{ item }}
                </el-checkbox>
            </el-checkbox-group>
        </el-form-item>
        <el-form-item
            label="3、你最喜欢行云的什么功能？"
            prop="flove"
        >
            <el-input
                type="textarea"
                v-model="form.good"
            />
        </el-form-item>
        <el-form-item
            label="4、在使用行云过程中你觉得哪些场景用户体验不够好？"
            prop="fbad"
        >
            <el-input
                type="textarea"
                v-model="form.bad"
            />
        </el-form-item>
        <el-form-item
            label="5、你希望在行云里面添加什么样的功能满足你的日常工作需要？"
            prop="fadd"
        >
            <el-input
                type="textarea"
                v-model="form.expect"
            />
        </el-form-item>
        <el-form-item
            label="6、你是否愿意向身边还没用行云的同事推荐使用行云？"
            prop="recommend"
        >
            <el-radio-group v-model="form.recommend">
                <el-radio :label="1">
                    愿意
                </el-radio>
                <el-radio :label="0">
                    不愿意
                </el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item>
            <el-button
                type="primary"
                @click="doSubmit"
            >
                提交
            </el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import http from '@/plugins/http';

export default {
    data() {
        return {
            form: {
                score: undefined,
                period: '2021Q3',
                used: [],
                good: '',
                bad: '',
                expect: '',
                recommend: undefined,
            },
            modules: ['需求管理', '团队空间', '项目管理', '测试管理', '效能度量', '部署发布', '代码库', '流水线'],
            rules: {
                score: [
                    { required: true, message: '请选择', trigger: 'change' },
                ],
                recommend: [
                    { required: true, message: '请选择', trigger: 'change' },
                ],
            },
        };
    },
    mounted() {
        http.get('v1/questionaire?period=2021Q3').then((data) => {
            if (data) {
                this.form.score = data.score;
                this.form.used = JSON.parse(data.used || []);
                this.form.good = data.good;
                this.form.bad = data.bad;
                this.form.expect = data.expect;
                this.form.recommend = data.recommend;
            }
        });
    },
    methods: {
        doSubmit() {
            if (!this.form.score) {
                this.$message('满意度评分不能为空!');
                return false;
            }
            if (this.form.recommend === undefined) {
                this.$message('是否分享不能为空!');
                return false;
            }
            http.post('v1/questionaire', this.form).then(() => {
                localStorage.setItem('csi', '2021Q3');
                this.$message('感谢您的评价!');
            });
            return true;
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.csi {
    height: 80vh;
    overflow-y: auto;
    .csi-title {
        line-height: 18px;
        margin-bottom: 12px;
    }
    .csi-checkbox {
        font-size: var(--font-size--description);
        .el-checkbox__label {
            font-size: var(--font-size--description);
        }
    }
}
</style>
