// import { childHttp } from '@/plugins/http';
// import axios from 'axios';

export default {
    state: {
        pipeline: '',
        coding: '',
        help: '',
        devops: '',
        security: '',
        helpValue: '',
    },
    mutations: {
        url_update(state, data) {
            // eslint-disable-next-line no-restricted-syntax
            for (const item in data) {
                if (data[item].indexOf('http') === -1 && item !== 'helpValue') {
                    data[item] = `http://${data[item]}`;
                }
            }
            const {
                pipeline, coding, help, devops, security, minio, spaceDemand, deepTest, helpValue,
            } = data;
            console.log('-- pipeline, coding, help ,devops --', pipeline, coding, help, devops, security);
            // console.log('=childHttp=', childHttp);
            // console.log('=childHttp.defaults.baseURL=', childHttp.defaults.baseURL);
            // childHttp.defaults.baseURL = `${spaceDemand}/space-demand/api`;
            // axios.defaults.baseURL = spaceDemand;
            Object.assign(state, {
                pipeline, coding, help, devops, security, minio, spaceDemand, deepTest, helpValue,
            }, data);
            if (helpValue !== '') {
                state.help = `${window.location.origin}/docs`;
            }
        },
    },
    actions: {
        url_update({ commit }, data) {
            // console.log('-- data --', data);
            commit('url_update', data);
        },
    },
};
