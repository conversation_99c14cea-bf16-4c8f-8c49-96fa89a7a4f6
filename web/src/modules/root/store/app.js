/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

export default {
    namespaced: true,
    state: {
        // 获取所有上架的应用数据
        allAppList: new Map([]),
        helpDoc: false,
        serviveConfig: {
            message: true,
            help: false,
            license: false,
        },
    },
    mutations: {
        setappList(state, val) {
            state.allAppList = val;
        },
        setHelpDoc(state, val) {
            state.helpDoc = val;
        },
        setServiveConfig(state, val) {
            state.serviveConfig = val;
        },
    },
    actions: {
    },
    getters: {
        // 与模块资源加载成功与否无关的
        getAppByCode: state => code => state.allAppList.get(code),
    },
};
