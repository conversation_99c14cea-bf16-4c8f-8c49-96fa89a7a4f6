export default {
    state: {
        name: '',
        erp: '',
        tenantId: '',
    },
    mutations: {
        user_update(state, data) {
            const { orgCode, orgName } = data; //  这块没和store拆干净
            Object.assign(state, { orgCode, orgName }, data);
        },
        tenantId_update(state, data) {
            state.tenantId = data;
        },
    },
    actions: {
        user_update({ commit }, data) {
            commit('user_update', data);
        },
        tenantId_update({ commit }, data) {
            commit('tenantId_update', data);
        },
    },
};
