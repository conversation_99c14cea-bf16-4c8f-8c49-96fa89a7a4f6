/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

export default {
    namespaced: true,
    state: {
        // 获取所有上架的应用数据
        extensionList: [],
    },
    mutations: {
        setextensionList(state, val) {
            state.extensionList = val;
        },
    },
    actions: {
        setextensionList({ commit }, data) {
            // console.log('-- data --', data);
            commit('setextensionList', data);
        },
    },
};
