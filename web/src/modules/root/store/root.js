export default {
    namespaced: true,
    state: {
        activeModuleMap: new Map(),
        currentPathAvailable: false,
        currentModulePkg: null,
        currentModule: null,
    },
    mutations: {
        setActiveModuleMap(state, val = {}) {
            state.activeModuleMap = val;
        },
        updateRootState(state, val = {}) {
            Object.assign(state, val);
        },
    },
    getters: {
        // 与模块资源加载成功与否无关的
        getActiveModuleByCode: state => code => state.activeModuleMap.get(code),
    },
};
