<template>
    <el-container
        class="option-index"
        :class="{'option-index--shrink': isCollapse}"
    >
        <el-header
            class="jacp-root-header"
            height="48px"
        >
            <span class="jacp-root-header__title">账号设置</span>
        </el-header>

        <el-container>
            <el-aside style="width: min-content;">
                <!-- 左侧菜单 -->
                <jacp-left-menu
                    class="option-index__menu"
                    :is-collapse.sync="isCollapse"
                    :default-active="$route.name"
                    :data="validMenus"
                    @select="gotoPage"
                />
            </el-aside>
            <el-main class="option-index__main">
                <router-view />
            </el-main>
        </el-container>
    </el-container>
</template>

<script type="text/javascript">
export default {
    data() {
        return {
            isCollapse: false,
            validMenus: [{
                children: [],
                icon: 'icon-module',
                id: 'basicInfo',
                name: '基本信息',
                router: 'basicInfo',
            },
            // {
            //     children: [],
            //     icon: 'icon-module',
            //     id: 'fixPassword',
            //     name: '密码修改',
            //     router: 'fixPassword',
            // },
            {
                children: [],
                icon: 'icon-module',
                id: 'fixAvator',
                name: '头像修改',
                router: 'fixAvator',
            }],
        };
    },
    mounted() {

    },
    methods: {
        gotoPage(routerName) {
            this.$router.push({
                name: routerName,
            });
        },
    },
};
</script>

<style lang="less">
.option-index {
    display: flex;
    &__menu {
        min-width: 200px;
        max-width: 200px;
        flex: 0;
    }
    &__main {
        flex: 1;
        overflow: hidden;
        // background-color: #fff;
    }
    &__menu,
    &__main {
        height: calc(~"100vh - 50px");
    }
    .option-index__main {
        padding: 0;
        overflow-y: auto;
    }
    &--shrink &__menu {
        min-width: 64px;
        max-width: 64px;
    }
}
</style>
