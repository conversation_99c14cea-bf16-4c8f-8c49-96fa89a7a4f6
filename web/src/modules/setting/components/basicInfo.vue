<template>
    <el-tabs
        v-model="activeName"
        type="border-card"
        style="height: 100%"
    >
        <el-tab-pane
            label="基本信息"
            name="basicInfo"
        >
            <div class="basic-content">
                <div class="avator">
                    <jacp-erp
                        :data="basicInfoForm"
                        :display-name="false"
                        :avatar-size="100"
                        avatar
                    />
                </div>
                <el-form
                    label-position="top"
                    label-width="80px"
                    :model="basicInfoForm"
                >
                    <el-form-item label="工号">
                        <el-input
                            v-model="basicInfoForm.erp"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="部门">
                        <el-input
                            v-model="basicInfoForm.fullOrgName"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="姓名">
                        <el-input
                            v-model="basicInfoForm.realName"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="邮箱">
                        <el-input
                            v-model="basicInfoForm.email"
                            disabled
                        />
                    </el-form-item>
                </el-form>
            </div>
        </el-tab-pane>
    </el-tabs>
</template>
<script type="text/javascript">
import UserModel from '@/models/user';

export default {
    data() {
        return {
            activeName: 'basicInfo',
            basicInfoForm: {
                erp: '',
                fullOrgName: '',
                realName: '',
                email: '',
            },
        };
    },
    created() {
        this.getUserInfo();
    },
    methods: {
        // 获取用户信息
        getUserInfo() {
            UserModel.getUserInfo(this.$store.state.user.erp).then((data = {}) => {
                this.basicInfoForm = data;
                data.headImage = data.headImg;
                data.name = data.realName;
                data.userName = data.realName;
            });
        },

    },
};
</script>
<style lang="less">
.basic-content{
    width: 50%;
    .avator {
        width: 100px;
        height: 100px;
        margin: 0 auto;
        border-radius: 100%;
        border: 1px solid #eee;
    }
}
</style>
