<template>
    <el-tabs
        v-model="activeName"
        type="border-card"
        style="height: 100%"
    >
        <el-tab-pane
            label="头像修改"
            name="basicInfo"
        >
            <div
                class="avator-content"
            >
                <!-- 用户头像 -->
                <!-- <el-form-item prop="url"> -->
                <!-- 头像显示 -->
                <div class="demo-image">
                    <div
                        class="image-header"
                    >
                        <el-button
                            class="edit-btn"
                            type="text"
                            v-show="!edit"
                            @click="edit=true"
                        >
                            编辑
                        </el-button>
                    </div>
                    <div
                        class="block"
                        style="margin:50px 100px"
                    >
                        <jacp-erp
                            :data="user"
                            :display-name="false"
                            :avatar-size="200"
                            avatar
                        />
                        <el-button
                            class="upload-btn"
                            v-show="edit"
                            type="text"
                            icon="el-icon-camera-solid"
                            title="注意！一旦上传则直接修改"
                            @click="uploadProfile = true;num=1;tipShow=false;fileList1=[]"
                        />
                    </div>
                </div>
                <!-- 上传头像 dialog弹窗-->
                <el-dialog
                    title="上传头像"
                    width="420px"
                    :visible.sync="uploadProfile"
                    :before-close="beforeDialogClose"
                >
                    <!-- drag upload -->
                    <el-upload
                        class="upload-demo"
                        ref="upload"
                        drag
                        accept=".jpg,.jpeg,.png,.JPG,.JPEG"
                        list-type="picture"
                        :with-credentials="true"
                        :headers="getHeader"
                        :multiple="false"
                        :auto-upload="true"
                        :action="uploadeUrl"
                        :before-upload="beforeAvatarUpload"
                        :on-change="onchangeUpload"
                        :file-list="fileList1"
                    >
                        <i class="el-icon-upload" />
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        <!-- <div
                                class="el-upload__tip"
                                slot="tip"
                            >
                                只能上传jpg/png文件，且不超过500kb
                            </div> -->
                        <div
                            class="el-upload__tip"
                            slot="tip"
                            style="color:red"
                            v-show="tipShow"
                        >
                            请上传头像~
                        </div>
                    </el-upload>
                    <!-- 头像预览子弹窗 -->
                    <el-dialog
                        width="30%"
                        title="预览头像"
                        :visible.sync="confirmProfile"
                        append-to-body
                        :before-close="beforeDialogClose"
                    >
                        确认更改头像如下吗？<br>
                        <div align="center">
                            <el-image
                                style="width: 200px; height: 200px"
                                :src="previewImgURL"
                                fit="cover"
                            >
                                <div
                                    slot="error"
                                    class="image-slot"
                                    style="width:200px;height:200px;background:#eee;line-height:200px;text-align:200px;"
                                >
                                    图片加载中
                                    <i class="el-icon-loading" />
                                </div>
                            </el-image>
                        </div>
                        <div
                            slot="footer"
                            class="dialog-footer"
                        >
                            <el-button @click="confirmCancel">换一个</el-button>
                            <el-button
                                type="primary"
                                @click="confirmSubmit"
                            >
                                确认
                            </el-button>
                        </div>
                    </el-dialog>
                    <div
                        slot="footer"
                        class="dialog-footer"
                    >
                        <el-button @click="cancelAvatarUpload">取 消</el-button>
                        <el-button
                            type="primary"
                            @click="updateUserDetail"
                        >
                            确定
                        </el-button>
                    </div>
                </el-dialog>
                <!-- </el-form-item> -->
            </div>
        </el-tab-pane>
    </el-tabs>
</template>
<script type="text/javascript">

import UserModel from '@/models/user';

export default {
    name: 'Personal',
    data() {
        return {
            activeName: 'basicInfo',
            edit: false,
            user: {}, // 用户信息
            uploadeUrl: `${window.location.origin}/devops-api/arch/api/rest/appInfo/uploadHeadImg?type=icons`, // 上传地址
            // 上传头像对话框显示与否
            uploadProfile: false,
            confirmProfile: false,
            fileList1: [], // 图片列表
            // upload-demo
            previewImgURL: '', // 预览图片地址
            tipShow: false, // 提示上传图片
            urlBase: '接口url，如http://*************',
            num: 1,
        };
    },
    created() {
        this.getUserInfo();
    },
    watch: {
        fileList1(val) { // 如果列表图片大于2个的时候清掉第一个图片
            if (val.length > 1) {
                val.splice(0, 1);
            }
        },
    },
    computed: {
        getHeader() {
            return { Authorization: localStorage.getItem('Authorization') };
        },
    },
    methods: {

        /* 上传头像对话框 */
        beforeDialogClose(done) { // 用户临时退出上传头像，应清空
            this.$refs.upload.clearFiles();
            done();
        },
        cancelAvatarUpload() { // 用户临时退出上传头像，应清空
            this.uploadProfile = false;
            this.edit = false;
            this.$refs.upload.clearFiles();
        },
        beforeAvatarUpload(file) { // 大小限制
            const fileType = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/JPEG' || file.type === 'image/JPG' || file.type === 'image/jpg';
            const isLt5M = file.size / 1024 / 1024 < 5;
            if (!fileType) {
                this.$message.error('上传头像图片只能是 JPG、JPEG、PNG 格式!');
                this.confirmProfile = false;
                return false;
            }
            if (!isLt5M) {
                this.confirmProfile = false;
                this.$message.error('上传头像图片大小不能超过 5MB!');
                return false;
            }
            const binaryData = [];
            binaryData.push(file.raw);
            this.previewImgURL = window.URL.createObjectURL(new Blob(binaryData));
            // this.confirmProfile = true // 预览图片
            // alert('' + this.confirmProfile)
            return isLt5M && fileType;
        },
        onchangeUpload(file, fileList) {
            // 预保存上传的图片
            if (fileList.length > 0) { this.tipShow = false; }
            const binaryData = [];
            binaryData.push(file.raw);
            this.previewImgURL = window.URL.createObjectURL(new Blob(binaryData));
            this.confirmProfile = true; // 预览图片
            this.fileList1 = fileList;
        },
        confirmCancel() { // 换一个
            this.confirmProfile = false;
            this.uploadProfile = true;
            this.previewImgURL = null;
            this.fileList1 = [];
            if (this.num === 1) { // 如果是未上传过图片，则清空图片列表，换一个是并未上传
                this.$refs.upload.clearFiles();
            }
        },
        confirmSubmit() {
            // post上传头像 存到数据库，显示在个人中心
            // this.$refs.upload.submit();
            this.num = 2;
            this.confirmProfile = false;
        },
        // 确认上传按钮
        updateUserDetail() {
            if (this.fileList1.length === 0) { this.tipShow = true; return; } // 如果没有上传图片，点确定时则提示上传
            const params = { erp: this.user.erp, headImg: this.fileList1[0].response.data };
            UserModel.updateUserDetail(params).then((res) => {
                console.log(res, '头像修改成功');
                this.uploadProfile = false;
                this.$message.success('头像修改成功，请刷新页面！');
                this.$refs.upload.clearFiles();
                this.edit = false;
                this.getUserInfo();
            });
        },
        // 获取用户信息
        getUserInfo() {
            UserModel.getUserInfo(this.$store.state.user.erp).then((data = {}) => {
                console.log(data, 'ddddata');
                this.user = data;
                data.headImage = data.headImg;
                data.name = data.realName;
                data.userName = data.realName;
            });
        },

    },
};
</script>
<style lang="less">
.avator-content{
    width: 350px;
    .avator {
        width: 100px;
        height: 100px;
        margin: 0 auto;
        border-radius: 100%;
        border: 1px solid red;
    }
    .demo-image {
        position: relative;
        .image-header{
            height:32px;
            overflow: hidden;
            &>.edit-btn {
                float: right;
            }
        }
        .el-image {
            width: 200px;
            height: 200px;
        }
        .upload-btn {
            position:absolute;
            top: 82px;
            left: 100px;
            width: 200px;
            height: 200px;
            color:#d9d7d7;
            background: rgba(0,0,0,.3);
            font-size: 80px;
        }
    }
        .image-footer {
            overflow: hidden;
            &>.el-button {
                float: right;
                margin-left: 20px;
            }
        }
}
</style>
