<template>
    <el-tabs
        v-model="activeName"
        type="border-card"
        style="height: 100%"
    >
        <el-tab-pane
            label="密码修改"
            name="basicInfo"
        >
            <div class="basic-content">
                <el-form
                    label-position="top"
                    label-width="80px"
                    :model="basicInfoForm"
                    :rules="rules"
                    ref="basicInfoForm"
                >
                    <el-form-item
                        label="旧密码"
                        prop="oldPassword"
                    >
                        <el-input
                            v-model="basicInfoForm.oldPassword"
                            @blur="checkPassword"
                            type="password"
                            :class="!check?'red-border':''"
                        />
                        <span
                            v-show="!check"
                            class="el-form-item__error"
                        >旧密码不正确</span>
                    </el-form-item>
                    <el-form-item
                        prop="newPassword"
                    >
                        <template slot="label">
                            新密码
                            <el-tooltip
                                class="item layout-block-title__tooltip"
                                effect="dark"
                                placement="bottom"
                            >
                                <div slot="content">
                                    必须包含大小写字母，特殊字符和数字，且长度不低于8位，<br>
                                    设置的密码中包含与工号&姓名&邮箱相关的信息不允许超过3位（包括字母或者数字，不区分大小写）
                                </div>
                                <i
                                    style="font-size: 14px;"
                                    class="el-icon-question"
                                />
                            </el-tooltip>
                        </template>
                        <el-input
                            v-model="basicInfoForm.newPassword"
                            type="password"
                        />
                    </el-form-item>
                    <el-form-item
                        label="确认密码"
                        prop="surePassword"
                    >
                        <el-input
                            v-model="basicInfoForm.surePassword"
                            type="password"
                        />
                    </el-form-item>
                </el-form>
                <div
                    class="basic-content-footer"
                >
                    <el-button
                        type="primary"
                        @click="updatePassword('basicInfoForm')"
                    >
                        确定
                    </el-button>
                    <el-button @click="resetForm('basicInfoForm')">重置</el-button>
                </div>
            </div>
        </el-tab-pane>
    </el-tabs>
</template>
<script type="text/javascript">
import UserModel from '@/models/user';
import Cookies from 'js-cookie';

export default {
    data() {
        // 校验旧密码 这里调接口不好使
        // const validateOldPas = (rule, value, callBack) => {
        // const params = { erp: this.user.erp, password: value };

        // UserModel.checkPassword(params).then((res) => {
        //     this.check = res;
        // });
        //     if (!this.check) {
        //         callBack(new Error('旧密码不正确'));
        //     }
        // };
        // 校验密码
        const validatePas = (rule, value, callback) => {
            // eslint-disable-next-line no-plusplus
            let data = (this.user.erp).toString().toLowerCase();
            const Pwd = value.toString().toLowerCase();
            if (value === this.basicInfoForm.oldPassword) {
                callback(new Error('密码不正确,新密码与旧密码不能重复'));
            }
            // eslint-disable-next-line no-plusplus
            for (let i = 0; i < data.length - 2; i++) {
                if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
                    callback(new Error('密码不正确,密码不可包含与用户特征相关的信息，不允许提交'));
                }
            }
            data = this.user.userName.toString().toLowerCase();
            // eslint-disable-next-line no-plusplus
            for (let i = 0; i < data.length - 2; i++) {
                if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
                    callback(new Error('密码不正确,密码不可包含与用户特征相关的信息，不允许提交'));
                }
            }
            data = this.user.email.toString().toLowerCase();
            // eslint-disable-next-line no-plusplus
            for (let i = 0; i < data.length - 2; i++) {
                if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
                    callback(new Error('密码不正确,密码不可包含与用户特征相关的信息，不允许提交'));
                }
            }
            callback();
        };
        // 检验确认密码
        const validateSurePas = (rule, value, callback) => {
            const pwd = this.basicInfoForm.newPassword;
            if (pwd === '') {
                callback(new Error('请先输入密码'));
            }
            if (value === '') {
                callback(new Error('请输入确认密码'));
            }
            if (pwd !== this.basicInfoForm.surePassword) {
                callback(new Error('确认密码不正确,确认密码与新密码不一致'));
            }
            callback();
        };
        return {
            activeName: 'basicInfo',
            basicInfoForm: {
                oldPassword: '',
                newPassword: '',
                surePassword: '',
            },
            check: true,
            user: '',
            rules: {
                oldPassword: [
                    { required: true, message: '请输入旧密码', trigger: 'blur' },
                    // { required: true, trigger: ['blur'], validator: validateOldPas },
                ],
                newPassword: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    {
                        pattern: /^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/,
                        trigger: ['blur', 'change'],
                        message: '密码不正确,密码必须包含大小写字母,特殊字符和数字，且长度不低于8位。',
                    },
                    { required: true, trigger: ['blur', 'change'], validator: validatePas },
                ],
                surePassword: [
                    { required: true, trigger: ['blur', 'change'], validator: validateSurePas },
                ],
            },
        };
    },
    created() {
        this.user = this.$store.state.user;
    },
    methods: {
        // 取消 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        // 校验旧密码
        checkPassword() {
            const params = { erp: this.user.erp, password: this.basicInfoForm.oldPassword };
            UserModel.checkPassword(params).then((res) => {
                this.$nextTick(() => {
                    this.check = !params.password ? true : res;
                });
            });
        },
        // 确定修改密码按钮
        updatePassword(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const params = { erp: this.user.erp, password: this.basicInfoForm.surePassword };
                    UserModel.updatePassword(params).then((res) => {
                        console.log(res, 'mmmm');
                        this.$message.success('密码修改成功，请重新登录');
                        this.handleLogout();
                        UserModel.logout();
                        this.$emit('logout');
                    });
                }
            });
        },
        handleLogout() {
            Cookies.remove('LastTime');
        },
    },
};
</script>
<style lang="less">
.basic-content{
    width: 50%;
    .basic-content-header {
        overflow: hidden;
        .edit-btn {
            float: right;
        }
    }
    .basic-content-footer {
        overflow: hidden;
        &>.el-button {
            float: right;
            margin-left: 20px;
        }
    }
    .red-border{
        .el-input__inner{
            border-color: #f55445!important;
        }
    }
}
</style>
