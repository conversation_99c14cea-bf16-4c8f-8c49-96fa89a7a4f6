import spaceList from './layout.spaceList';
import scrum from './layout.scrum';
import general from './layout.general';
// FIXME: 这里多一层结构有点怪
import generalCardList from './layout.general.cardList';
import backlog from './layout.backlog';

export default {
    codeRepo: () => import(/* webpackChunkName: "chunck-layout-t" */'./layout.codeRepo'),
    calendar: () => import(/* webpackChunkName: "chunck-layout-t" */'./layout.calendar'),
    spaceSetting: () => import(/* webpackChunkName: "chunck-layout-t" */'./layout.spaceSetting'),
    leaderView: () => import(/* webpackChunkName: "chunck-layout-t" */'./layout.leaderView'),
    leaderViewPromise: () => import(/* webpackChunkName: "chunck-layout-t" */'./layout.leaderViewPromise'),
    footmark: () => import(/* webpackChunkName: "chunck-layout-t" */'./layout.footmark'),
    spaceIntro: () => import(/* webpackChunkName: "chunck-layout-t" */'./layout.spaceIntro'),
    spaceList,
    scrum,
    general,
    generalCardList,
    backlog,
    // index,
};
