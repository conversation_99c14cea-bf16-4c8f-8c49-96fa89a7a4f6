<template>
    <el-container class="leader-view">
        <el-main class="leader-view-main">
            <el-container>
                <el-aside
                    width="266px"
                    class="leader-view-aside"
                >
                    <el-header height="48px">
                        <div style="justify-content: space-between;">
                            <el-input
                                placeholder="请输入空间名称"
                                style="width: 200px;"
                                size="mini"
                                v-model="spaceKeyWord"
                                clearable
                            >
                                <i
                                    slot="prefix"
                                    class="el-input__icon el-icon-search"
                                />
                            </el-input>
                            <el-dropdown
                                @command="sortSpaceList"
                                trigger="click"
                                size="mini"
                            >
                                <i class="j-sort" />
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item
                                        command="cardCount-DESC"
                                        :class="{'jacp-dropdown-selected-item': spaceOrderType === 'cardCount-DESC'}"
                                    >
                                        按卡片数量从多到少
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="cardCount-ASC"
                                        :class="{'jacp-dropdown-selected-item': spaceOrderType === 'cardCount-ASC'}"
                                    >
                                        按卡片数量从少到多
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="memberCount-DESC"
                                        :class="{'jacp-dropdown-selected-item': spaceOrderType === 'memberCount-DESC'}"
                                    >
                                        按团队成员从多到少
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        command="memberCount-ASC"
                                        :class="{'jacp-dropdown-selected-item': spaceOrderType === 'memberCount-ASC'}"
                                    >
                                        按团队成员从少到多
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                    </el-header>
                    <div class="leader-view-spaces">
                        <div
                            class="leader-view-space"
                            :class="{'selectedSpace': condition.spaceIds.includes(space.id)}"
                            v-for="space in spaceList"
                            v-show="spaceKeyWord === '' || space.name.indexOf(spaceKeyWord) > -1"
                            :key="space.id"
                            @click="selectSpace(space.id)"
                        >
                            <el-dropdown
                                placement="bottom-start"
                                @command="spaceOption"
                                @click.native.stop
                            >
                                <span
                                    class="el-dropdown-link"
                                    @click.stop
                                >
                                    <i class="el-icon-more" />
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item
                                        :command="{command: 'copy', space}"
                                    >
                                        复制空间标识
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        :disabled="true"
                                        :command="{command: 'option', space}"
                                    >
                                        查看空间报表
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <div class="title">
                                <div
                                    class="img"
                                    :style="`background-color: ${getBgColor(space.id)}`"
                                >
                                    {{ space.name.slice(0, 1) }}
                                </div>
                                <span :title="space.name">{{ space.name }}</span>
                            </div>
                            <div class="info">
                                <span :title="`团队成员 ${space.memberCount}`">
                                    团队成员 {{ space.memberCount }}</span>&nbsp;
                                <span :title="`卡片数量 ${space.totalCardCount}`">
                                    卡片数量 {{ space.totalCardCount }}</span>
                            </div>
                            <div
                                v-show="multipleSpace"
                                class="leader-view-space__lightbox"
                                :class="{'lightbox__check': condition.spaceIds.includes(space.id)}"
                                @click.stop="addSpace(space.id)"
                            />
                        </div>
                    </div>
                    <el-footer height="48px">
                        <div class="leader-view-left spacesInfo">
                            <span>空间总数 {{ spaceList.length }}</span>
                        </div>
                        <div class="leader-view-right">
                            <el-button
                                size="mini"
                                style="padding: 7px 10px"
                                v-if="!multipleSpace"
                                @click="toggleSelectType"
                                round
                            >
                                多选模式
                            </el-button>
                            <el-button
                                v-if="multipleSpace"
                                size="mini"
                                style="padding: 7px 10px"
                                @click="toggleSelectType"
                                round
                            >
                                单选模式
                            </el-button>
                        </div>
                    </el-footer>
                </el-aside>
                <el-main
                    class="content"
                >
                    <el-alert
                        title="已列出时间范围内所选团队空间的卡片，您也可以点击左侧的团队空间选择查看"
                        type="warning"
                        show-icon
                        style="margin-bottom: 16px;flex: none"
                        @close="fitContent = false"
                    />
                    <div class="leader-view-list">
                        <div
                            class="leader-view-list__header"
                        >
                            <div class="leader-view-left">
                                <el-input
                                    v-model="condition.keyWord"
                                    placeholder="请输入卡片名称/编号"
                                    style="width: 200px;"
                                    size="mini"
                                    clearable
                                    @change="getCardListForLeader"
                                >
                                    <i
                                        slot="prefix"
                                        class="el-input__icon el-icon-search"
                                    />
                                </el-input>
                                <el-date-picker
                                    style="width: 260px;"
                                    v-model="dateRang"
                                    :clearable="false"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    size="mini"
                                    value-format="timestamp"
                                    :picker-options="pickerOptions"
                                    @change="getCardListForLeader"
                                />
                                <el-select
                                    v-model="condition.sprintType"
                                    placeholder="请选择"
                                    style="width: 100px;"
                                    size="mini"
                                    @change="getCardListForLeader"
                                >
                                    <el-option
                                        v-for="item in sprintTypeList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </div>
                            <div class="leader-view-right">
                                <el-button
                                    v-if="$store.state.user.isLeader"
                                    size="mini"
                                    @click="$router.push({ name: 'teamspaceLeaderViewPromise' });"
                                >
                                    领域授权
                                </el-button>
                                <el-button
                                    size="mini"
                                    @click="$router.push({ name: 'teamspaceSpaceList' });"
                                >
                                    退出领域
                                </el-button>
                                <label style="height: 24px; border-left: solid #F1F1F1 1px;" />
                                <jacp-dropdown-settings
                                    label="卡片"
                                    module-key="space"
                                    setting-type="list"
                                    :space-privilage="spacePrivilage"
                                    @export="exportCards"
                                    @save-displayFields="getCardListForLeader"
                                />
                            </div>
                        </div>
                        <div class="leader-view-table">
                            <cards-table
                                :class="{'cards-table--fit': fitContent}"
                                ref="cardList"
                                style="margin-right: 0;"
                                :data="cardList"
                                :base-data="baseData"
                                :sortable-columns="[]"
                                :org-columns="cardsTableColumns"
                                :sort-condition="sortCondition"
                                :space-privilage="spacePrivilage"
                                :editable="spacePrivilage.createCard"
                                @click-title="viewCardDetail"
                                @toggle-children="toggleExpandStatus"
                                @toggle-expand-all="toggleExpandAll"
                            />
                            <jacp-pagenation
                                style="float: right; margin-top: 6px;"
                                :total="totalCount"
                                :current-page.sync="condition.currentPage"
                                :page-size.sync="condition.pageSize"
                                @update:currentPage="getCardListForLeader(false)"
                            />
                        </div>
                    </div>
                </el-main>
            </el-container>
        </el-main>
    </el-container>
</template>

<script type="text/javascript">
import sortBy from 'lodash/sortBy';
import { mapActions, mapState } from '$node_modules.vuex';
import { card as CardModel, space as SpaceModel } from '@/models/teamspace';
import PersonalModel from '@/models/personal';
import { copyToClipBoard } from '@/plugins/utils';
import cardsTable from '@/modules/card/components/cardsTable';
import RouterMixin from '@/mixins/mixin.router';

function removeChildren(row, list) {
    /* eslint-disable no-param-reassign */
    (row.$children || []).forEach((item) => {
        const i = list.indexOf(item);
        item.$expandStatus = false;
        removeChildren(item, list);
        if (i > -1) {
            list.splice(i, 1);
        }
    });
}

export default {
    mixins: [RouterMixin],
    components: {
        cardsTable,
    },
    data() {
        return {
            pickerOptions: {
                shortcuts: [{
                    text: '最近两周',
                    onClick: (picker) => {
                        const end = new Date();
                        const start = new Date();
                        start.setDate(start.getDate() - 14);
                        picker.$emit('pick', [start, end]);
                    },
                }, {
                    text: '最近一个月',
                    onClick: (picker) => {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 1);
                        picker.$emit('pick', [start, end]);
                    },
                }, {
                    text: '最近两个月',
                    onClick: (picker) => {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 2);
                        picker.$emit('pick', [start, end]);
                    },
                }, {
                    text: '最近三个月',
                    onClick: (picker) => {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 3);
                        picker.$emit('pick', [start, end]);
                    },
                }],
            },
            sprintTypeList: [{
                value: 0,
                label: '全部迭代',
            }, {
                value: 1,
                label: '活跃迭代',
            }, {
                value: 2,
                label: '归档迭代',
            }, {
                value: 3,
                label: 'Backlog',
            }],
            spaceList: [],
            cardList: [],
            baseData: [],
            dateRang: [],
            spaceOrderType: 'memberCount-DESC',
            condition: {
                startDate: '',
                endDate: '',
                sprintType: 0,
                spaceIds: [],
                currentPage: 1,
                pageSize: 50,
                orderType: 'DESC',
                keyWord: '',
            },
            totalCount: 0,
            cardsTableColumns: [],
            sortCondition: {
                orderField: 'index',
                orderType: 'asc',
            },
            spaceKeyWord: '',
            multipleSpace: false,
            fitContent: true,
        };
    },
    created() {
        this.sortSpaceList(this.spaceOrderType);
        const curentDate = new Date();
        this.dateRang = [curentDate.setDate(curentDate.getDate() - 14), (new Date()).getTime()];
    },
    methods: {
        sortSpaceList(command) {
            this.spaceOrderType = command;
            const orderInfo = command.split('-');
            this.getSpaceList(orderInfo[0], orderInfo[1]);
        },
        async getSpaceList(orderField, orderType) {
            this.spaceList = await SpaceModel.getSpaceListForLeader({
                orderType, orderField,
            });
            if (this.spaceList.length > 0 && this.condition.spaceIds.length === 0) {
                // this.condition.spaceIds = [this.spaceList.slice(0, 1).id];
                this.condition.spaceIds = [this.spaceList[0].id];
                this.fetchAllInfoBySpaceId({
                    spaceKey: this.spaceList[0].key,
                    keys: ['priorityList', 'demandSourceConfig', 'typeListOptions'],
                });
                this.getCardListForLeader();
            }
        },
        async getCardListForLeader(resetPage = true) {
            if (resetPage) {
                this.condition.currentPage = 1;
            }
            this.condition.startDate = this.dateRang[0];
            this.condition.endDate = this.dateRang[1];
            const {
                resultCount,
                resultList,
            } = await SpaceModel.getCardListForLeader(this.condition);
            this.totalCount = resultCount;
            this.baseData = resultList.slice(0);
            this.cardList = this.formatCardList(resultList, 0);
            const columns = await PersonalModel.getListDisplayFields('space');
            this.setTableColumns(columns);
        },
        formatCardList(list = [], baseLevel = 0) {
            if (!list || !list.length) {
                return [];
            }
            list.forEach((cardItem) => {
                Object.assign(cardItem, {
                    notMatched: cardItem.matched !== 2,
                    $children: this.formatCardList(cardItem.childCardList, baseLevel + 1),
                    $level: baseLevel,
                    $vParentId: baseLevel === 0 ? -1 : cardItem.parentCardId,
                    hasChild: cardItem.hasChildren,
                });
            });
            return list;
        },
        setTableColumns(data) {
            this.cardsTableColumns = sortBy(data, 'index');
        },
        selectSpace(spaceId) {
            this.condition.spaceIds = [spaceId];
            this.getCardListForLeader();
        },
        toggleSelectType() {
            if (this.multipleSpace === true) {
                this.condition.spaceIds = this.condition.spaceIds.slice(0, 1);
                this.getCardListForLeader();
            }
            this.multipleSpace = !this.multipleSpace;
        },
        addSpace(spaceId) {
            const index = this.condition.spaceIds.indexOf(spaceId);
            // 选中及取消选中操作
            if (index > -1) {
                // 至少保留一个选中空间
                if (this.condition.spaceIds.length > 1) {
                    this.condition.spaceIds.splice(index, 1);
                }
            } else {
                this.condition.spaceIds.push(spaceId);
            }
            this.getCardListForLeader();
        },
        // TODO: remove
        getBgColor(spaceId) {
            const colorList = ['#0067B2', '#0067B2', '#01C0DD', '#0ED396', '#78C913', '#F84E54', '#FF8D00', '#FFC700', '#FF8D00', '#28A59A'];
            return colorList[spaceId % 10];
        },
        exportCards(val) {
            return CardModel.exportCardsForLeader(Object.assign(this.condition, {
                exportLatitude: val,
            }));
        },
        viewCardDetail({ spaceKey, sprintId, id }) {
            if (!id || !spaceKey) {
                return;
            }
            this.$_routerOpen({
                name: 'teamspaceCardDetail',
                params: {
                    spaceKey,
                },
                query: {
                    cardId: id,
                    sprintId,
                },
            });
        },
        expandCard(card, cardList, deep) {
            if (!card.hasChild || card.$expandStatus) {
                return Promise.resolve();
            }
            card.$expandStatus = true;
            return this.loadChildren(card).then(() => {
                const i = cardList.indexOf(card);
                cardList.splice(i + 1, 0, ...(card.$children || []));
                if (card.$children && card.$children.length > 0 && deep) {
                    card.$children.forEach((item) => {
                        this.expandCard(item, cardList, true);
                    });
                }
            }).catch(() => {
                card.$expandStatus = false;
            });
        },
        shrinkCard(card, cardList) {
            if (!card.$expandStatus) {
                return;
            }
            card.$expandStatus = false;
            removeChildren(card, cardList);
        },
        toggleExpandStatus(card) {
            if (card.$expandStatus) {
                this.shrinkCard(card, this.cardList);
            } else {
                this.expandCard(card, this.cardList);
            }
        },
        toggleExpandAll(value) {
            if (value) {
                this.cardList.map(card => this.expandCard(card, this.cardList, true));
            } else {
                this.cardList.forEach((card) => {
                    if (card.$level === 0 && card.$children && card.$children.length > 0) {
                        this.shrinkCard(card, this.cardList);
                    }
                });
            }
        },
        loadChildren(card, refresh) {
            if (!refresh && card.hasChild && card.$children.length) {
                return Promise.resolve(card.$children);
            }
            return CardModel.getChildren(card.id, null, { cancelToken: this.sourceToken.token })
                .then((list) => {
                    Object.assign(card, {
                        $children: this.formatCardList(list, card.$level + 1, this.currentPlanId),
                    });
                    return card.$children;
                });
        },
        spaceOption({ command, space }) {
            if (command === 'copy') {
                if (copyToClipBoard(space.key)) {
                    this.$message({
                        message: '空间标识复制成功！',
                        type: 'success',
                    });
                }
            }
        },
        ...mapActions('chilldteamspace', [
            'fetchAllInfoBySpaceId']),
    },
    computed: {
        ...mapState('chilldteamspace', ['spacePrivilage']),
    },
};
</script>

<style lang="less">
.leader-view{
    background-color: #F5F5F5;
    position: relative;
    overflow: visible;
    .el-header,
    &-list__header{
        background-color: #fff;
        border-bottom: 1px solid #F5F5F5;
        padding: 0 16px;
        &>div{
            display: flex;
            height: 100%;
            justify-content: space-between;
            align-items: center;
        }
        label{
            line-height: 28px;
            font-size: 14px;
        }
        .el-input__inner,
        .el-button.el-button--default,
        .el-range-separator{
            color: #999;
        }
        .el-range-separator{
            width: 10%;
        }
    }
    &-list__header{
        display: flex;
        justify-content: space-between;
        min-height: 48px;
        overflow-x: auto;
    }
    &-left{
        // float: left;
        &>*{
            margin-right: 16px;
        }
    }
    &-right{
        // float: right;
        &>*{
            margin-left: 16px;
        }
    }
    &-main{
        padding: 0;
        .el-aside{
            background-color: #fff;
            & .el-main{
                padding: 16px;
                overflow: auto;
                flex: 1;
                height: 100%;
            }
        }
        .content{
            padding: 24px;
            display: flex;
            flex-direction: column;
            height: calc(~"100vh - 50px");
        }
        .leader-view-list{
            background-color: #fff;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    }
    &-aside{
        display: flex;
        flex-direction: column;
        height: calc(~"100vh - 50px");
        overflow: hidden;
        .el-footer{
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #F5F5F5;
            padding: 0 16px;
            &>div{
                display: flex;
                align-items: center;
            }
        }
    }
    &-spaces{
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: auto;
        padding: 16px;
        flex: 1;
    }
    &-space{
        position: relative;
        flex: 0 0 auto;
        align-self: stretch;
        height: 80px;
        border-radius: 4px;
        border: 1px solid #E1E1E1;
        margin-bottom: 8px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        & .title{
            display: flex;
            font-size: 14px;
            font-weight: 500px;
            & .img{
                flex: none;
                width: 24px;
                height: 24px;
                line-height: 24px;
                border-radius: 14px;
                text-align: center;
                background-color: #2695F1;
                color: white;
            }
            span{
                margin-left: 8px;
                margin-right: 16px;
                align-items: center;
                line-height: 24px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
            }
        }
        & .info{
            padding-left: 32px;
            display: flex;
            font-size: 12px;
            color: #999;
            span{
                align-items: center;
                line-height: 28px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                &:first-child{
                    margin-right: 4px;
                }
            }
        }
        &.selectedSpace{
            background:rgba(233,244,253,1);
        }
        &__lightbox{
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            background: url("~@/assets/icons/board-unchecked.png")  no-repeat right top;
            background-size: 32px 32px;
            &.lightbox__check{
                background: url("~@/assets/icons/board-checked.png")  no-repeat right top;
                background-size: 32px 32px;
            }
        }
        & .el-dropdown{
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            line-height: 30px;
            font-size: 18px;
            text-align: center;
            color: #c5c5c5;
            border: solid 1px #c5c5c5;
            border-radius: 50%;
            cursor: pointer;
            transform: scale(.5, .5);
            &:hover{
                color: #2695F1;
                border: solid 1px #2695F1;
            }
        }
        & .el-dropdown-link:hover{
            border: none;
        }
    }
    & .spacesInfo{
        line-height: 28px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-size: 12px;
        color: #999;
    }
    &-table{
        padding: 0 16px;
        flex: 1;
        .cards-table--fit{
            height: calc(~"100vh - 290px");
        }
    }
    & .icon-list-unorder,
      .j-sort{
        cursor: pointer;
    }
}
& .jacp-dropdown-selected-item{
    background-color: rgb(233, 244, 254);
    color: rgb(81, 170, 244);
}
</style>
