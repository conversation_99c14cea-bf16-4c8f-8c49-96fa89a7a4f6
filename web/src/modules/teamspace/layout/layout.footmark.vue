<template>
    <div class="teamspace-footmark-root">
        <el-tabs
            :class="tabScopedClass.mod({
                gray: true,
                large: true,
            }).cn"
        >
            <el-tab-pane label="今日动态">
                <el-table
                    :data="todayData"
                    style="width: 100%"
                >
                    <el-table-column
                        v-for="item in columns"
                        :prop="item.name"
                        :label="item.label"
                        :key="item.name"
                        min-width="180"
                    />
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="本周动态">
                <el-table
                    :data="weekData"
                    style="width: 100%"
                >
                    <el-table-column
                        v-for="item in columns"
                        :prop="item.name"
                        :label="item.label"
                        :key="item.name"
                        min-width="180"
                    />
                </el-table>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import moment from 'moment';
import PlanModel from '@/models/plan';

export default {
    props: {
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
        spaceKey: { type: String },
    },
    data() {
        return {
            columns: [
                { name: 'cardCode', label: '卡片编号' },
                { name: 'cardName', label: '卡片名称' },
                { name: 'processors', label: '任务执行人' },
                { name: 'fromStatus', label: '历史状态' },
                { name: 'currentStatus', label: '当前状态' },
                { name: 'optTime', label: '操作时间' },
            ],
            weekData: [],
            todayData: [],
            requestData: {
                role: 'spaceAdmin',
                startDate: '2020-04-26',
                filters: [
                    {
                        column: 'snapDate',
                        type: 'term',
                        value: '2020-04-27',
                    },
                    {
                        column: 'card.space.id',
                        type: 'term',
                        value: [30],
                    },
                    {
                        column: 'card.sprint.archived',
                        type: 'term',
                        value: false,
                    },
                ],
            },
            tabScopedClass: this.$scopedClass('jacp-tabs'),
        };
    },
    methods: {
        spaceFilter(space) {
            const { spaceKey } = this;
            const curSpace = this.$store.state.chilldteamspace.keyMap[spaceKey];
            return space.mode === curSpace.mode;
        },
        fetchDayData(start, end) {
            const requestDataCopy = Object.assign({}, this.requestData);
            const { spaceKey } = this;
            const curSpace = this.$store.state.chilldteamspace.keyMap[spaceKey];
            requestDataCopy.filters[1].value = [curSpace.id];
            requestDataCopy.startDate = moment(start).format('YYYY-MM-DD');
            requestDataCopy.filters[0].value = moment(end).format('YYYY-MM-DD');
            PlanModel.getSpaceFootmark(requestDataCopy).then((data) => {
                this.todayData = data.data.details;
            });
        },
        fetchWeekData(start, end) {
            const requestDataCopy = Object.assign({}, this.requestData);
            requestDataCopy.startDate = moment(start).format('YYYY-MM-DD');
            requestDataCopy.filters[0].value = moment(end).format('YYYY-MM-DD');
            PlanModel.getSpaceFootmark(requestDataCopy).then((data) => {
                this.weekData = data.data.details;
            });
        },
        init() {
            const today = new Date();
            const weekStart = new Date(today.getTime() - (3600 * 1000 * 24 * 7));
            const yesterday = new Date(today.getTime() - (3600 * 1000 * 24 * 1));
            this.fetchDayData(yesterday, today);
            this.fetchWeekData(weekStart, today);
        },
    },
    watch: {
        spaceKey: {
            immediate: true,
            handler: 'init',
        },
    },
};
</script>
<style lang="less">
.teamspace-footmark-root{
    background: #fff;
    padding: var(--gutter--small) var(--gutter--large) var(--gutter--large) var(--gutter--large);
}
</style>
