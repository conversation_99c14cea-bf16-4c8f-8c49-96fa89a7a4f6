<template>
    <div class="layout-sprint-report">
        <el-row :gutter="24">
            <el-col :span="8">
                <sprint-summary
                    v-model="value.taskFinishRate"
                    :sprint="value.sprint"
                    class="layout-sprint-summary"
                />
            </el-col>
            <el-col :span="8">
                <layout-block class="layout-sprint-mini-card">
                    <jacp-icon
                        name="icon-activity"
                        :size="16"
                        class="layout-sprint-mini-card__icon"
                    />
                    <number-grow
                        v-model="value.cardCount"
                        class="layout-sprint-mini-card__number"
                    /><br>
                    <span class="layout-sprint-mini-card__desc">
                        卡片总数（个）
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="'该迭代中父子卡片的总和'"
                            placement="bottom"
                        >
                            <jacp-icon
                                name="icon-help_outline"
                                :size="16"
                            />
                        </el-tooltip>
                    </span>
                </layout-block>
            </el-col>
            <el-col :span="8">
                <layout-block class="layout-sprint-mini-card">
                    <jacp-icon
                        name="icon-task1"
                        :size="16"
                        class="layout-sprint-mini-card__icon"
                    />
                    <number-grow
                        v-model="value.plannedHourSum"
                        class="layout-sprint-mini-card__number"
                    /><br>
                    <span class="layout-sprint-mini-card__desc">
                        计划工时（小时）
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="'父子卡片中处理人计划工时的总数'"
                            placement="bottom"
                        >
                            <jacp-icon
                                name="icon-help_outline"
                                :size="16"
                            />
                        </el-tooltip>
                    </span>
                </layout-block>
            </el-col>
            <el-col
                :span="8"
            >
                <layout-block class="layout-sprint-mini-card">
                    <jacp-icon
                        name="icon-createtask"
                        :size="16"
                        class="layout-sprint-mini-card__icon"
                    />
                    <number-grow
                        v-model="value.taskCount"
                        class="layout-sprint-mini-card__number"
                    /><br>
                    <span class="layout-sprint-mini-card__desc">
                        卡片任务（个）
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="'父子卡片中存在处理人条目总数'"
                            placement="bottom"
                        >
                            <jacp-icon
                                name="icon-help_outline"
                                :size="16"
                            />
                        </el-tooltip>
                    </span>
                </layout-block>
            </el-col>
            <el-col
                :span="8"
            >
                <layout-block class="layout-sprint-mini-card">
                    <jacp-icon
                        name="icon-timer"
                        :size="16"
                        class="layout-sprint-mini-card__icon"
                    />
                    <number-grow
                        v-model="value.remainingHourSum"
                        class="layout-sprint-mini-card__number"
                    /><br>
                    <span class="layout-sprint-mini-card__desc">
                        剩余工时（小时）
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="'父子卡片中处理人剩余工时的总数'"
                            placement="bottom"
                        >
                            <jacp-icon
                                name="icon-help_outline"
                                :size="16"
                            />
                        </el-tooltip>
                    </span>
                </layout-block>
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="12">
                <burndown-chart
                    :sprint="value.sprint"
                    ref="burndownChart"
                    check-type="dropdown"
                    :exporting="false"
                    :display-fullscreen="false"
                    :title="'燃尽图'"
                    :height="26"
                    fullscreen-key="burndown"
                />
            </el-col>
            <el-col :span="12">
                <sprint-card-status v-model="value.cardStatusList" />
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="12">
                <sprint-card-types v-model="value.cardTypeList" />
            </el-col>
            <el-col :span="12">
                <sprint-card-burndata
                    :sprint="value.sprint"
                />
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="12">
                <sprint-card-status-flow-diagram
                    :sprint="value.sprint"
                />
            </el-col>
            <el-col :span="12">
                <sprint-card-status-scatter-plot
                    :sprint="value.sprint"
                />
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="24">
                <sprint-members-data v-model="value.memberData" />
            </el-col>
        </el-row>
    </div>
</template>

<script>
import SprintSummary from '$module/components/sprintReport/sprintSummary';
import SprintMembersData from '$module/components/sprintReport/sprintMembersData';
import SprintCardTypes from '$module/components/sprintReport/sprintCardTypes';
import SprintCardStatus from '$module/components/sprintReport/sprintCardStatus';
import SprintCardBurndata from '$module/components/sprintReport/sprintCardBurndata';
import SprintCardStatusFlowDiagram from '$module/components/sprintReport/sprintCardStatusFlowDiagram';
import SprintCardStatusScatterPlot from '$module/components/sprintReport/sprintCardStatusScatterPlot';
import BurndownChart from '@/modules/sprints/components/burndownChart';

export default {
    name: 'LayoutSprintReport',
    components: {
        SprintCardStatusScatterPlot,
        SprintCardStatusFlowDiagram,
        SprintCardBurndata,
        BurndownChart,
        SprintCardStatus,
        SprintCardTypes,
        SprintMembersData,
        SprintSummary,
    },
    props: {
        value: {
            type: Object,
            default: () => ({}),
        },
    },
};
</script>

<style  lang="less">
.layout-sprint-report{
    overflow: auto;
    width: 100%;
    height: calc(~"100% - 24px");
    & .el-row{
        margin: 0;
    }
    & .el-col{
        border: 1px solid #F5F5F5;
        margin: 0;
    }
}
.layout-sprint-summary {
  min-height: 240px;
//   height: calc(~"30.4vh + 24px");
}
.layout-sprint-mini-card{
    height: 15.21vh;
    min-height: 128px;
    &__number{
        font-size: 32px;
        margin-left: 6px;
    }
    &__icon{
        color: #2695F1;
        position: relative;
        top: -3px;
    }
    &__desc{
        top: 15px;
        margin-left: 24px;
        position: relative;
        color: #999999;
    }
}
</style>
