<template>
    <div
        :class="{
            'teamspace-scrum-root': true,
            'teamspace-scrum-root--shrink': shrinkPlanlistPanel,
            'cardslist-root--fullscreen': fullScreenState,
        }"
    >
        <template v-if="!isNoPermissionSpace">
            <!-- 左边迭代 -->
            <div class="teamspace-scrum__plans">
                <LocalSprints
                    class="teamspace-scrum__plans__list"
                    ref="sprintEl"
                    :sprint-id="sprintId"
                    :on-change="onSprintChange"
                    :on-tab-change="() => {
                        $router.push({
                            ...$router.currentRoute,
                            query: {
                                ...$router.currentRoute.query,
                                sprintId: undefined,
                            },
                        });
                    }"
                />
                <div
                    class="teamspace-scrum__border"
                    @click="togglePlanlist()"
                >
                    <jacp-icon :name="shrinkPlanlistPanel ? 'icon-arrow-right' : 'icon-arrow-left'" />
                </div>
            </div>
            <!-- 右侧内容区 -->
            <div
                class="cardslist-root__main teamspace-scrum__main"
                ref="mainEl"
            >
                <!-- 有迭代内容的时候 -->
                <template v-if="activeSprintList.filter(s => s.deleted !== 1).length || archivedCard">
                    <el-row
                        type="flex"
                        justify="space-between"
                        class="j-mgt16 j-mgl12"
                    >
                        <!-- header：切换实体的菜单 -->
                        <local-tabs-menu
                            class="teamspace-scrum__header"
                            ref="tabMenu"
                            size="mini"
                            :menu="menu"
                        />
                        <!-- header：新建 和 全屏  -->
                        <div
                            class="j-mgr24"
                            style="display: flex; align-items: center;"
                        >
                            <!-- 新建实体 -->
                            <entity-creator
                                v-if="entityPostCreate[this.currentView]"
                                class="j-mgl16"
                                :handler-parameters="{
                                    onTaskAdded: this.entityPostCreate[this.currentView],
                                }"
                            />
                            <jacp-icon
                                name="jacp-icon-icon_fullscreen j-mgl16"
                                active
                                @click.native="toggleFullscreen"
                            />
                        </div>
                    </el-row>

                    <component
                        element-loading-background="rgba(255, 255, 255, 0.4)"
                        v-loading="loading"
                        ref="listIns"
                        :active-plan="$store.state.plan.activePlan"
                        :is="currentView"
                        :space-privilage="spacePrivilage"
                        :archived="archivedCard"
                        :disabled="!spacePrivilage.updateCard"
                        @click-card="$_showDetail($event)"
                    />
                </template>
                <!-- 没有迭代，并且不是已归档列表的时候，再显示空 -->
                <jacp-empty v-else>
                    <div style="display: flex;flex-direction: column">
                        <img
                            class="j-server-error__img"
                            src="@/assets/images/errorpage/<EMAIL>"
                        >
                        <jacp-text type="disable">暂无迭代数据，请先新建迭代或者去 Backlog 看一看</jacp-text>
                        <div class="j-mgt24">
                            <el-button
                                type="primary"
                                :disabled="!spacePrivilage.createSprint"
                                @click="createSprint"
                            >
                                {{ $t('jacp.newPlan') }}
                            </el-button>

                            <el-button
                                @click="$router.push({ name: 'teamspaceBacklog' })"
                                style="margin-left: var(--gutter--medium)"
                            >
                                去Backlog
                            </el-button>
                        </div>
                    </div>
                </jacp-empty>
            </div>
        </template>
        <jacp-empty
            v-else
            :style="{
                marginRight: emptyMarginRight,
            }"
            label="您尚无权限查看其他卡片信息"
        />
        <jacp-off-canvas
            ref="cardDetailPanelIns"
            @on-open="emptyMarginRight = '880px'"
            @hide="emptyMarginRight = 0"
            :before-hide="$_closeDetail"
        >
            <router-view
                @after-action="refreshData(), $_closeDetail()"
                :disabled="!spacePrivilage.updateCard"
                :space-privilage="spacePrivilage"
                :archived="archivedCard"
                @refresh-list="refreshData"
            />
        </jacp-off-canvas>
    </div>
</template>

<script type="text/javascript">
import { createNamespacedHelpers } from 'vuex';
import HttpStack from '$platform.HttpStack';

import { cardsExcelTemplateUrl } from '@/models/config';
import MixinImport from '@/modules/_components/mixin.import';
import MixinCardDetail from '@/modules/card/mixins/mixin.cardDetail';
import ViewCardListTable from '@/modules/card/view.cardsList.table';
import ViewCardListBoard from '@/modules/card/view.cardsList.board';
import ViewTaskListTable from '@/modules/task/view.task.table';
import ViewTaskBoard from '@/modules/task/view.task.board';
import ViewCardListReport from '@/modules/card/view.sprint.report';
import mixinFullscreen from '@/modules/card/mixins/mixin.fullscreen';
import LocalSprints from '@/modules/sprints';
import cardActions from '@/models/cardActions';
import { sprintActionDefine } from '@/modules/sprints/models/sprintAction';
import LocalTabsMenu from '@/modules/root/components/tabsMenu';
// import { Menu, MenuItem } from '@jacpbiz/menu-business';
import { creatRouteMenu } from '@/modules/root/models/menu';

const { clickHandler: createSprint } = sprintActionDefine.createSprint('createSprint');
const viewMap = {
    card: {
        board: 'ViewCardListBoard',
        table: 'ViewCardListTable',
    },
    task: {
        board: 'ViewTaskBoard',
        table: 'ViewTaskListTable',
    },
    report: {
        board: 'ViewCardListReport',
        table: 'ViewCardListReport',
        report: 'ViewCardListReport',
    },
};
const { mapGetters } = createNamespacedHelpers('chilldteamspace');

const menu = creatRouteMenu([
    {
        id: 'report',
        name: '迭代概览',
        url: 'teamspaceSprintReport',
    },
    {
        id: 'card',
        name: '卡片',
        url: 'teamspaceCardsList',
    },
    {
        id: 'task',
        name: '任务',
        url: 'teamspaceTaskList',
    },
], { immediateActive: true });

export default {
    componentName: 'LayoutCardList', // table更新用
    name: 'LayoutScrum',
    mixins: [
        MixinCardDetail,
        MixinImport,
        mixinFullscreen,
    ],
    props: {
        spacePrivilage: {
            type: Object,
            default: () => {},
        },
        isNoPermissionSpace: Boolean,
        sprintId: { type: [String, Number], default: '' },
    },
    components: {
        ViewTaskListTable,
        ViewTaskBoard,
        ViewCardListTable,
        ViewCardListBoard,
        ViewCardListReport,
        LocalSprints,
        LocalTabsMenu,
    },
    created() {
        this.event.$on('create-card', cardActions.create);
        this.event.$on('create-card-task', this.createTask);
    },
    mounted() {
        this.importTemplateUrl = cardsExcelTemplateUrl;
        this.importUrl = 'v1/bizSpaceCard/import';
        this.$on('import-success', () => this.refreshData({ key: 'name' }));
        this.$on('refresh-list', this.refreshData);
    },
    data() {
        return {
            shrinkPlanlistPanel: false,
            emptyMarginRight: 0,
            event: this.$initEvent(),
            menu,
            entityPostCreate: {
                ViewCardListBoard: () => this.$refs.listIns.updateBoard(this.$refs.listIns.currentPlan),
                ViewTaskBoard: () => this.$refs.listIns.load(),
                ViewCardListTable: () => this.$refs.listIns.updateData(),
                ViewTaskListTable: () => this.$refs.listIns.loadData(),
            },
        };
    },
    methods: {
        createSprint,
        togglePlanlist() {
            this.shrinkPlanlistPanel = !this.shrinkPlanlistPanel;
        },
        refreshPlansData() {
            const { sprintEl } = this.$refs;
            if (sprintEl) {
                sprintEl.reload();
            }
        },
        refreshData() {
            const { listIns } = this.$refs;
            if (this.currentViewName === 'board') {
                listIns.updateBoard(listIns.currentPlan);
            }
            if (this.currentViewName === 'table') {
                listIns.updateData();
            }
        },
        renderOffcanvas(to) {
            const vm = this;
            if (!vm.$refs.cardDetailPanelIns) {
                return;
            }
            if (to.meta && to.meta.showOffCanvas) {
                vm.$refs.cardDetailPanelIns.open();
            } else {
                vm.$refs.cardDetailPanelIns.setOpenStatus(false);
            }
        },
        onSprintChange(sprint = {}) {
            if (!sprint || !sprint.id) {
                return;
            }
            this.$store.commit('plan/isGlobalMode_change', false);
            this.$router.push({
                ...this.$router.currentRoute,
                query: {
                    ...this.$router.currentRoute.query,
                    sprintId: sprint.id,
                },
            });
        },
    },
    computed: {
        ...mapGetters(['activeSprintList']),
        // 局部loading
        loading() {
            const { length } = HttpStack.pendings.filter(req => req.config?.displayLoading);
            return length !== 0;
        },
        currentViewName() {
            // 默认进入看板
            return this.$route.query.view || localStorage.defaultSpaceView || 'board';
        },
        currentView() {
            const comps = viewMap[this.menu.activedMenu?.id || 'card'];
            if (this.currentViewName in comps) {
                return comps[this.currentViewName];
            }
            const views = Object.values(comps);
            return views.length && views.shift();
        },
        archivedCard() {
            const currentPlan = this.$store.state.plan.activePlan;
            return currentPlan && currentPlan.archived === 1;
        },
    },
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            vm.renderOffcanvas(to);
        });
    },
    beforeRouteUpdate(to, from, next) {
        this.renderOffcanvas(to);
        next();
    },
    // 开启全局搜索的时候默认收起
    watch: {
        '$store.state.plan.isGlobalMode': {
            deep: true,
            handler(n) {
                this.shrinkPlanlistPanel = Boolean(n);
            },
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.cardslist-root{
    &--fullscreen{
            /* 只能使用fixed模拟该容器进入全屏的状态，否则appendToBody的所有popper都会无法显示出来 */
            .cardslist-root__main{
                position: fixed;
                top: 0;
                left: 0;
                z-index: 100;
                width: 100vw;
                height: 100vh;
                padding: 0;
            }
        }
}
.teamspace-scrum{
    &-root{
        display: flex;
        width: 100%;
        position: relative;
        /* FIXME: 去掉这些写死的高度计算 */
        & .task-table-root,
        & .teamspace-cards-table-root{
            height: calc(100vh - 104px);
        }
        & .teamspace-cards-table__list{
            height: calc(~"100vh - 160px");
        }
        &.cardslist-root--fullscreen{
            .teamspace-cards-table-root{
                padding-top: 0;
                margin-top: 0;
                background: #fff;
            }
            & .task-table-root,
            & .teamspace-cards-table-root{
                height: calc(100vh - 56px);
            }
            & .teamspace-cards-table__list{
                height: calc(~"100vh - 112px");
            }
        }
    }
    &-root--shrink &__plans{
        width: 0;
        z-index: 1;
        // overflow: hidden;
    }
    &-root--shrink &__plans{
        transform: translateX(-240px);
    }
    &-root--shrink &__border{
        transform: translate(256px, -32px);
        // right:  -216px;
    }
    &__header{
        .el-menu-item{
            font-size: var(--font-size--subtitle);
        }
    }
    &__border{
        width: 16px;
        display: flex;
        align-items: center;
        position: absolute;
        right: 0;
         top: 50%;
        transform: translate(100%, -32px);
        // top: calc(~"50% - 32px");
        // transform: translate(100%, -32px);
        z-index: 1;
        & *{
            color: #fff;
            background-color: #f0f2f5;
            padding: 8px;
            transform: scaleX(0.5);
            transform-origin: left;
            transition: all 0.15s;
            cursor: pointer;
            border-bottom-right-radius: 6px;
            border-top-right-radius: 6px;
            &:hover{
                background-color: @primaryColor;
            }
        }
    }
    &__plans{
        // transform: translateX(0);: translateZ(1);
        transition: transform 0.15s;
        position: relative;

        &__list, &__shrinktip{
            transform: translateX(0);
            transition: 0.15s;
        }
    }
    &__plans{
        display: flex;
        flex-shrink: 0;
        width: 240px;
        // overflow-x: hidden;
        // overflow-y: auto;
        height: 100%;
        background-color: #fff;
        border-right: var(--border--hr);
        // border: 1px solid @borderColor;

        &__list{
            width: 240px;
            flex-shrink: 0;
            padding-bottom: 20px;
        }
        &__shrinktip{
            color: #999;
            font-size: 12px;
            align-items: center;
            display: flex;
            padding-left: 3px;
        }
    }
    &__main{
        flex: 1;
        overflow: hidden;
        position: relative;
        background-color: #fff;
        .el-tabs__header {
            margin-bottom: 0px;
        }

    }
    &__detail{
        width: 920px;
    }
}
</style>
