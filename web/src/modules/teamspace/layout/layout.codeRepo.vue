<template>
    <div class="code-repo">
        <div
            v-if="!spaceRepoList.length"
            class="code-repo__empty"
        >
            <img
                class="j-server-error__img"
                src="@/assets/images/errorpage/<EMAIL>"
            >
            <p class="code-repo__empty-text">{{ codeBaseText.empty }}</p>
            <el-button
                class="j-rounded"
                type="primary"
                @click="openDialog"
            >
                {{ codeBaseText.associate }}
            </el-button>
        </div>
        <template v-else>
            <el-row style="padding-bottom: 8px;">
                <el-button
                    class="j-rounded"
                    type="primary"
                    @click="openDialog"
                >
                    {{ codeBaseText.associate }}
                </el-button>
                <el-tooltip
                    v-if="showFixButton"
                    content="修复自己关联代码库的连通性"
                    placement="right"
                >
                    <el-button
                        class="j-rounded"
                        @click="fixRepo"
                    >
                        一键修复
                    </el-button>
                </el-tooltip>
            </el-row>
            <basic-table
                :data="spaceRepoList"
                :columns="spaceRepoColumns"
            >
                <el-table-column
                    label="操作"
                    width="80px"
                    align="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            class="j-rounded"
                            type="text"
                            @click="unbindRepo(scope.row, scope.$index)"
                        >
                            取消关联
                        </el-button>
                    </template>
                </el-table-column>
            </basic-table>
        </template>

        <!-- 关联代码库的 dialog -->
        <el-dialog
            custom-class="j-dialog"
            :title="codeBaseText.associate"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            @closeed="closeDialog"
        >
            <div
                class="el-alert el-alert--warning is-light"
                style="margin-bottom: 16px; font-size: 12px;"
            >
                <i
                    class="el-alert__icon el-icon-warning"
                    style="margin-right: 4px; font-size: 14px;"
                />
                {{ codeBaseText.alert }}
            </div>
            <el-input
                v-model="keyword"
                :placeholder="codeBaseText.placeholder"
                @keydown.enter.native="getRepoList"
            >
                <i
                    slot="prefix"
                    class="el-input__icon el-icon-search"
                />
            </el-input>
            <basic-table
                class="code-repo__bind-table"
                v-loading="loading"
                :height="364"
                :data="personalRepoList"
                :columns="associateRepoColumns"
            >
                <el-table-column width="40px">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.checked" />
                    </template>
                </el-table-column>
            </basic-table>
            <el-row type="flex" justify="center">
                <el-button
                    class="j-rounded"
                    type="text"
                    :style="{ visibility: showLoadMoreBtn ? 'visible' : 'hidden' }"
                    @click="getRepoList({ isLoadMore: true })"
                >
                    {{ $t('jacp.button.loadMore') }}
                </el-button>
            </el-row>
            <span
                slot="footer"
                class="code-repo__bind-dialog__footer"
            >
                <span style="font-size: 12px; color: #909399;">已选择 {{ checkedRepoList.length }} 项</span>
                <span>
                    <el-button
                        class="j-rounded"
                        @click="dialogVisible = false"
                    >
                        {{ $t('jacp.button.cancel') }}
                    </el-button>
                    <el-button
                        class="j-rounded"
                        type="primary"
                        @click="bindRepo"
                    >
                        {{ $t('jacp.button.confirmBtn') }}
                    </el-button>
                </span>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { Message } from 'element-ui';

import { LocalTime } from '@/filters';
import Dialog from '@/models/dialog';
import {
    loadSpaceAddons,
    spaceTabExtensionPoint,
} from '@/models/app';
import {
    repo as RepoModel,
    spaceRepo as SpaceRepoModel,
} from '@/models/codeRepo';

const { handle: timeFormat } = LocalTime;

const ADDON_NAME = '代码库';

const tableColumns = {
    repo: (showStatus = false) => ({
        prop: 'repo',
        label: '代码仓库',
        cellRender(h, { row }) {
            const {
                isConnected = true,
                link,
                repoName,
            } = row;
            const connectedText = isConnected ? '正常访问' : '访问异常';
            const connectedColor = isConnected ? '#1CBB84' : '#F55445';
            const connectedStyle = {
                display: 'inline-block',
                width: '8px',
                height: '8px',
                borderRadius: '4px',
                marginRight: '4px',
                backgroundColor: connectedColor,
            };

            return (<div>
                {showStatus && (
                    <el-tooltip content={connectedText} placement="top-start">
                        <i style={connectedStyle} />
                    </el-tooltip>
                )}
                <el-link href={link} target="_blank">{repoName}</el-link>
            </div>);
        },
    }),
    visibility: {
        prop: 'visibility',
        label: '访问级别',
        width: '200px',
        cellRender(h, { row }) {
            return (
                <span style="display: inline-flex; align-items: center;">
                    <i class={`jacp-icon-${row.visibility}`} style="padding-right: 4px;" />
                    {row.visibility}
                </span>
            );
        },
    },
};

const spaceRepoColumns = [tableColumns.repo(true), tableColumns.visibility, {
    prop: 'creator',
    label: '关联人',
    width: '200px',
    cellRender(h, { row }) {
        return (
            <div style="display: flex; align-items: center">
                <jacp-erp
                    data={{
                        erp: row.creatorErp,
                        name: row.creatorName,
                        headImage: row.headImage,
                    }}
                    displayName={false}
                    avatar
                />
                <span style="padding-left: 4px;">{row.creatorName}</span>
            </div>
        );
    },
}, {
    prop: 'uTime',
    label: '关联时间',
    width: '200px',
    cellRender(h, { row }) {
        return (
            <span>{timeFormat(row.uTime)}</span>
        );
    },
}];

const associateRepoColumns = [tableColumns.repo(), tableColumns.visibility];

const codeBaseText = {
    associate: '关联代码库',
    disassociate: '取消关联',
    alert: '可以关联 Coding 中本人为 Owner 或着 Master 的仓库',
    empty: '还没有关联代码库，请先关联代码库',
    placeholder: '搜索代码库',
};

const checkCodeRepoAuth = (to, from, next) => {
    const { spaceKey } = to.params;
    loadSpaceAddons(spaceTabExtensionPoint, spaceKey)
        .then((addons = []) => addons.filter(addon => addon.name === ADDON_NAME).length)
        .then((isAvailable) => {
            if (isAvailable) {
                next();
            } else {
                Message({
                    message: '空间还未开启代码库权限，开启需管理员权限。',
                    type: 'warning',
                    offset: 48,
                });
                next({ path: `/teamspace/cardlist/${spaceKey}` });
            }
        });
};

export default {
    data() {
        return {
            codeBaseText,
            spaceRepoList: [],
            personalRepoList: [],
            spaceRepoColumns,
            associateRepoColumns,
            dialogVisible: false,
            keyword: '',
            showLoadMoreBtn: false,
            currentPage: 1,
            loading: false,
        };
    },
    computed: {
        ...mapGetters('chilldteamspace', ['currentSpaceId']),
        checkedRepoList() {
            return this.personalRepoList.filter(item => item.checked);
        },
        showFixButton() {
            return this.spaceRepoList.filter(item => !item.isConnected).length;
        },
    },
    beforeRouteEnter: checkCodeRepoAuth,
    beforeRouteUpdate: checkCodeRepoAuth,
    methods: {
        openDialog() {
            this.dialogVisible = true;
            this.getRepoList();
        },
        closeDialog() {
            this.personalRepoList = [];
        },
        getSpaceRepoList() {
            SpaceRepoModel.getRepoList(this.currentSpaceId)
                .then((data = []) => {
                    this.spaceRepoList = data;
                });
        },
        getRepoList({ isLoadMore = false } = {}) {
            this.loading = true;
            this.currentPage = isLoadMore ? this.currentPage + 1 : 1;

            RepoModel.getRepoList({
                search: this.keyword,
                currentPage: this.currentPage,
            })
                .then((data = []) => {
                    const repoList = data.map(item => ({
                        ...item,
                        repoType: 'coding', // TODO: 暂时写死
                        checked: false,
                    }));
                    this.personalRepoList = isLoadMore ? [
                        ...this.personalRepoList,
                        ...repoList,
                    ] : repoList;

                    this.loading = false;
                    this.showLoadMoreBtn = data.length >= 20;
                });
        },
        unbindRepo(row, index) {
            this.$confirm(`确定取消关联 ${row.repoName} 么？`, '取消关联', {
                customClass: 'j-message-box j-message-box--small',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                confirmButtonClass: 'j-rounded',
                cancelButtonClass: 'j-rounded',
            })
                .then(() => SpaceRepoModel.unbindRepo(row.id))
                .then(() => {
                    this.spaceRepoList.splice(index, 1);
                    this.$message({
                        type: 'success',
                        message: '取消关联成功',
                    });
                });
        },
        bindRepo() {
            this.dialogVisible = false;
            // TODO: repo 中有些字段是后端不需要的。。
            const repoList = this.checkedRepoList.map(repo => ({
                ...repo,
                spaceId: this.currentSpaceId,
            }));
            SpaceRepoModel.bindRepo(repoList)
                .then((failedList = []) => {
                    this.getSpaceRepoList();
                    if (failedList.length) {
                        Dialog.alert({
                            title: '提示',
                            content: `
                                关联以下代码库失败，无权限!<br>
                                ${failedList.join('<br>')}
                            `,
                        });
                    } else {
                        this.$message({
                            type: 'success',
                            message: '成功关联代码库',
                        });
                    }
                });
        },
        fixRepo() {
            SpaceRepoModel.fixRepo(this.currentSpaceId)
                .then(() => {
                    this.getSpaceRepoList();
                    this.$message({
                        type: 'success',
                        message: '修复成功',
                    });
                });
        },
    },
    watch: {
        currentSpaceId: {
            immediate: true,
            handler() {
                this.getSpaceRepoList();
            },
        },
    },
};
</script>

<style lang="less">
.code-repo {
    background: #fff;
    padding: var(--gutter--large);
    &__bind-table {
        // height: 364px;
        .el-table::before {
            height: 0;
        }
    }
    &__bind-dialog {
        &__footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }
    &__empty {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        &-text {
            font-size: var(--font-size--content);
            color: var(--color--secondary--content);
            padding-bottom: var(--gutter--large);
        }
    }

    .el-table .cell,
    .el-button,
    .el-link {
        font-weight: var(--font-weight-default);
    }
}
</style>
