<template>
    <div
        class="teamspace-calendar-box"
        v-if="currentSpaceId"
    >
        <schedule
            :current-space-id="currentSpaceId"
            :modify="!spacePrivilage.isGuest"
            :view-type.sync="viewType"
            :space-privilage="spacePrivilage"
        />
    </div>
</template>
<script type="text/javascript">
import { mapGetters } from 'vuex';
import schedule from '$module/components/schedule/layout.schedule';

export default {
    props: {
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        schedule,
    },
    data() {
        return {
            viewType: 'teamspace',
        };
    },
    computed: {
        ...mapGetters('chilldteamspace', ['currentSpaceId']),
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.teamspace-calendar-box{
    overflow-y: auto;
}
</style>
