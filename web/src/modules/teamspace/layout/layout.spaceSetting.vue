<template>
    <el-row
        v-if="currentSpaceId"
        class="teamspace-spacesetting-root"
    >
        <el-col class="teamspace-spacesetting-left">
            <div
                class="teamspace-spacesetting__wrap"
                v-for="(menu, order) in menuList"
                :key="order"
            >
                <div
                    class="teamspace-spacesetting__title"
                    :key="`'p'${order}`"
                    v-if="menu.item.length > 0"
                >
                    {{ menu.label }}
                </div>
                <ul
                    class="teamspace-spacesetting__list"
                    :key="`'ul'${order}`"
                >
                    <li
                        v-for="(submenu, idx) in menu.item"
                        :class="{'teamspace-spacesetting__listitem': true, 'liactive': currenComponent === submenu.value || currentSubTab === submenu.showTab }"
                        @click="leftMenuClick(submenu.value, submenu.showTab)"
                        :key="`'li'${idx}`"
                    >
                        {{ submenu.label }}
                    </li>
                </ul>
            </div>
        </el-col>
        <el-col class="teamspace-spacesetting-right">
            <!--没有二级tab里显示名称-->
            <div
                class="teamspace-spacesetting__subtitle"
                v-if="currentSubTab === ''"
            >
                {{ spaceSettingMenuTitle[currenComponent] }}
            </div>
            <!--页面中二级tab菜单：属性与看版tab、状态与工作流tab-->
            <div
                class="teamspace-spacesetting__tab"
                v-if="subTabMenu.length > 0"
            >
                <span
                    v-for="(tabMenu, ids) in subTabMenu"
                    :key="`tab${ids}`"
                    @click="leftMenuClick(tabMenu.value, tabMenu.showTab)"
                    :class="{'teamspace-spacesetting__tabbtn': true, 'tabactive': currenComponent === tabMenu.value }"
                >
                    {{ tabMenu.label }}
                </span>
            </div>

            <!--tab内容-->
            <div class="teamspace-spacesetting__tabcontent">
                <component
                    :is="showComponet"
                    :space-key="spaceKey"
                    :space-iey="currentSpaceId"
                    :space-privilage="spacePrivilage"
                    :current-space-id="currentSpaceId"
                />
            </div>
        </el-col>
    </el-row>
</template>

<script type="text/javascript">
import { mapGetters, mapState } from 'vuex';
import { spaceSettingMenuTitle, spaceSettingMenuList, spaceSettingSubTabMenu } from '@/modules/teamspace/constant';

export default {
    props: {
        spacePrivilage: { type: Object, default: () => ({}) },
        spaceKey: { type: String, default: '' },
    },
    data() {
        return {
            spaceSettingMenuTitle,
            spaceSettingMenuList,
            spaceSettingSubTabMenu,
            currenComponent: this.$route.params.menu ? this.$route.params.menu : 'spaceInfo',
            currentSubTab: this.$route.params.tab ? this.$route.params.tab : '',
            cardStatusComponents: { 1: 'cardStatusOptionForGeneral', 2: 'cardStatusOption' },
        };
    },
    components: {
        CustomizedRoleSetting: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/customizedRoleSetting'),
        spaceInfo: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/spaceInfo'),
        spacePermission: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/spacePermission'),
        cardOption: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/cardOption'),
        cardStatusOption: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/cardStatusOption'),
        cardStatusOptionForGeneral: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/cardStatusOptionForGeneral'),
        spaceTag: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/spaceTag'),
        spacePermAssign: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/spacePermAssign'),
        flowOption: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/flowOption'),
        pluginManage: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/pluginManage'),
        CustomizeCardFields: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/customizeCardFields'),
        processManage: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/process/layout.process'),
        boardSetting: () => import(/* webpackChunkName: "chunk-spaceSetting" */'$module/components/spaceSetting/boardSetting'),
    },
    computed: {
        ...mapState('chilldteamspace', ['keyMap', 'keyMapPlus']),
        ...mapGetters('chilldteamspace', ['currentSpaceId']),
        spaceMode() {
            const { spaceKey, keyMap, keyMapPlus } = this;
            const space = keyMap[spaceKey] || keyMapPlus[spaceKey] || {};
            return space.mode;
        },
        showComponet() {
            return this.currenComponent === 'cardStatus' ? this.cardStatusComponents[this.spaceMode] : this.currenComponent;
        },
        menuList() {
            const vm = this;
            // 根据spaceMode展示左侧menu
            const newArr = this.spaceSettingMenuList.map((menuObj) => {
                const submenu = menuObj.item;
                return {
                    label: menuObj.label,
                    item: submenu.filter(o => o.spaceModeCondition.includes(vm.spaceMode)),
                };
            });
            return newArr;
        },
        subTabMenu() {
            const vm = this;
            // 根据点击左侧menu和spaceMode，右侧显示相应的tab list；
            const tabList = this.spaceSettingSubTabMenu.filter(t => t.showTab === vm.currentSubTab && t.spaceModeCondition.includes(vm.spaceMode));
            return tabList;
        },
    },
    methods: {
        spaceFilter(space) {
            const { spaceKey } = this;
            const curSpace = this.keyMap[spaceKey];
            return space.mode === curSpace.mode;
        },
        leftMenuClick(currentComponentName, tabName) {
            this.currenComponent = currentComponentName;
            switch (currentComponentName) {
            case 'CustomizeCardFields':
            case 'boardSetting':
                this.currentSubTab = tabName;
                break;
            case 'cardStatus':
            case 'flowOption':
            case 'processManage':
                this.currentSubTab = tabName;
                break;
            default:
                this.currentSubTab = '';
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

  .teamspace-spacesetting {
    &-root{
        display: flex;
        background: #fff;
        height: calc(~"100vh - 50px");
        // flex: 1;
    }
    &-left {
        min-width: 240px;
        max-width: 240px;
        overflow-x:hidden;
        border-right: var(--border--hr);
        padding: 0 var(--gutter--small);
        &::-webkit-scrollbar{
            display: none;
        }
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    &__wrap{
        padding-bottom: var(--gutter--small);
    }
    &__list{
        list-style: none;
    }
    &__title{
        padding: 0 var(--gutter--small);
        margin: var(--gutter--medium) 0 0 0;
        height: var(--height--menu-item);
        line-height: var(--height--menu-item);
        font-size: var(--font-size--description);
        color: var(--color--secondary--content);
    }
    &__listitem{
        cursor: pointer;
        font-size: var(--font-size--content);
        padding: 0 var(--gutter--medium);
        height: var(--height--menu-item);
        line-height: var(--height--menu-item);
        color:  var(--color--regular--content);
        &.liactive{
            color:  var(--color--base--content);
            background-color: var(--color--base--background);
            border-radius: var(--radius--medium);
            font-weight: var(--font-weight-bold);
        }
    }
    &__listitem:hover{
        color:  var(--color--base--content);
        background-color: var(--color--base--background);
        border-radius: var(--radius--medium);
        font-weight: var(--font-weight-bold);
    }
    &__tab, &__subtitle{
        height: var(--height--menu-item);
        line-height: var(--height--menu-item);
        padding: 0 var(--gutter--medium);
        margin-top: var(--gutter--medium);
    }
    &__tab{
        border-bottom: var(--border--hr)
    }
    &__tabcontent{
        padding: var(--gutter--medium) var(--gutter--large);
    }
    &__tabbtn{
        display: inline-block;
        cursor: pointer;
        margin-right: var(--gutter--large);
        color: var(--color--regular--content);
        &.tabactive{
            font-weight: var(--font-weight-bold);
            color: var(--color--base--content);
        }
    }
    &__subtitle{
        font-weight: var(--font-weight-bold);
        color: var(--color--base--content);
    }
    &-right{
        background-color: #fff;
        flex: 1;
    }
    &__footer{
        padding: var(--gutter--medium);
        background-color: var(--color--primary-white);
        border-top: var(--border--hr);
        .el-button{
            border-radius: var(--radius--default);
        }
    }
    &__form{
        max-width: 558px;
        max-height: calc(~"100vh - 250px");
        overflow-y: auto;
    }
    &__basicinfo{
      margin-top: -10px;
      margin-bottom: 20px;
    }
    &__button-first{
        margin-top: 10px;
    }
  }
</style>
