<template>
    <el-container class="leader-promission">
        <el-main>
            <el-alert
                title="机构负责人授权后，授权人将仅能查看机构负责人组织结构下的所有团队空间，另外，被授权人在机构负责人组织结构下才能授权成功，为保证数据的安全性请谨慎操作。"
                type="warning"
                show-icon
                style="margin-bottom: 16px;"
            />
            <div class="leader-promission-main">
                <div class="leader-promission-main__table">
                    <div class="leader-promission-main__header border">
                        <div class="leader-promission-left">
                            <el-input
                                placeholder="搜索想要授权的一个工号"
                                style="width: 200px;"
                                size="mini"
                                clearable
                                v-model="condition.keyWord"
                                @keyup.enter.native="getSuggestList"
                            >
                                <i
                                    slot="prefix"
                                    class="el-input__icon el-icon-search"
                                />
                            </el-input>
                            <el-button
                                size="mini"
                                type="primary"
                                style="padding: 7px 10px"
                                @click="getSuggestList"
                            >
                                查询
                            </el-button>
                            <!-- <div class="leader-promission-header"> -->

                            <!-- </div> -->
                        </div>
                        <div class="leader-promission-right">
                            <el-button
                                size="mini"
                                style="float: right;"
                                @click="$router.push({ name: 'teamspaceLeaderView' });"
                            >
                                退出授权
                            </el-button>
                        </div>
                    </div>
                    <div class="leader-promission-main__header">
                        <div class="leader-promission-left">
                            <label style="font-size: 14px;">推荐授权人列表</label>
                        </div>
                    </div>
                    <div class="table-box">
                        <el-table
                            :data="suggestList"
                            border
                            style="width: 100%"
                            height="100%"
                            stripe
                            :row-style="{height: '50px'}"
                        >
                            <el-table-column
                                prop="userErp"
                                label="工号"
                                width="120"
                            />
                            <el-table-column
                                prop="userName"
                                label="姓名"
                                width="120"
                            />
                            <el-table-column
                                prop="orgTierName"
                                label="部门"
                            />
                            <el-table-column
                                width="100"
                                label="操作"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <label
                                        style="color: #999"
                                        v-if="promissionErpList.includes(scope.row.userErp)"
                                    >已授权</label>
                                    <el-button
                                        @click="addPromissionPerson(scope.row)"
                                        type="text"
                                        size="small"
                                        v-else
                                    >
                                        授权
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="leader-promission-main__right">
                    <div class="leader-promission-main__header">
                        <div class="leader-promission-left">
                            <label>已授权人员 {{ promissionList.length }}</label>
                        </div>
                        <div class="leader-promission-right">
                            <el-button
                                type="text"
                                @click="cleanPromissionList"
                            >
                                全部清空
                            </el-button>
                        </div>
                    </div>
                    <ul class="person-list">
                        <li
                            class="person-list-item"
                            v-for="(person, index) in promissionList"
                            :key="person.targetErp"
                        >
                            <img
                                :src="person.targetHeadImage"
                                v-if="person.targetHeadImage"
                            >
                            <img
                                wws
                                src="~@/assets/images/avatar.png"
                                v-else
                            >
                            <span>{{ person.targetName }} ({{ person.targetErp }})</span>
                            <i
                                class="el-icon-circle-close"
                                @click="removePromissionPerson(person.id, index)"
                            />
                        </li>
                    </ul>
                </div>
            </div>
        </el-main>
    </el-container>
</template>

<script type="text/javascript">
import { space as SpaceModel } from '@/models/teamspace';

export default {
    data() {
        return {
            promissionList: [],
            suggestList: [],
            condition: {
                current: 1,
                pageSize: 50,
                keyWord: '',
            },
            totalCount: 0,
        };
    },
    created() {
        this.getSuggestList();
        this.getLeaderPromissionList();
    },
    methods: {
        async getSuggestList() {
            const methed = this.condition.keyWord ? 'getSuggestListByKey' : 'getSuggestList';
            const resData = await SpaceModel[methed](this.condition);
            // map为兼容方案，account与name已不使用，后期删除
            this.suggestList = resData.records.map((item) => {
                const { account, name } = item;
                return Object.assign(item, { userErp: item.userErp || account, userName: item.userName || name });
            });
            this.totalCount = resData.total;
        },
        async getLeaderPromissionList() {
            this.promissionList = await SpaceModel.getLeaderPromissionList();
        },
        // 添加授权用户
        async addPromissionPerson({ userErp, userName }) {
            await SpaceModel.addPromissionPerson({
                targetErp: userErp,
                targetName: userName,
            });
            this.$notify({
                title: '添加成功！',
                type: 'success',
                duration: 2000,
            });
            this.getSuggestList();
            this.getLeaderPromissionList();
        },
        // 删除授权
        async removePromissionPerson(id, index) {
            await SpaceModel.removePromissionPerson(id);
            this.$notify({
                title: '删除成功！',
                type: 'success',
                duration: 2000,
            });
            this.promissionList.splice(index, 1);
        },
        // 清空授权列表
        cleanPromissionList() {
            this.$confirm('此操作将删除全部授权用户, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                SpaceModel.cleanPromissionList().then(() => {
                    this.$notify({
                        title: '删除成功！',
                        type: 'success',
                        duration: 2000,
                    });
                    this.getSuggestList();
                    this.getLeaderPromissionList();
                });
            });
        },
    },
    computed: {
        promissionErpList() {
            const list = [];
            this.promissionList.forEach((item) => {
                list.push(item.targetErp);
            });
            return list;
        },
    },
};
</script>

<style lang="less">
.leader-promission{
    background-color: #F5F5F5;
    position: relative;
    overflow: visible;
    .el-header{
        background-color: #fff;
        border-bottom: 1px solid #F5F5F5;
        padding: 0 16px;
        &>div{
            display: flex;
            height: 100%;
            justify-content: space-between;
            align-items: center;
        }
        label{
            line-height: 28px;
            font-size: 14px;
        }
    }
    &-header{
        // position: absolute;
        top: 85px;
        right: 92px;
        z-index: 1;
    }
    .el-main{
        padding: 24px;
        display: flex;
        flex-direction: column;
    }
    &-left{
        float: left;
        &>*{
            margin-right: 16px;
        }
    }
    &-right{
        float: right;
        &>*{
            margin-left: 16px;
        }
    }
    &-main{
        background-color: #fff;
        width: 100%;
        display: flex;
        flex: 1;
        &__table{
            flex: 1;
            display: flex;
            flex-direction: column;
            & .table-box{
                margin: 0 16px;
                flex: 1;
                overflow: auto;
            }
        }
        &__right{
            width: 360px;
            border-left: 1px solid #F5F5F5;
            & .person-list{
                margin: 16px;
                list-style: none;
            }
            & .person-list-item{
                height: 40px;
                line-height: 40px;
                border-bottom: 1px solid #F5F5F5;
                color: #333333;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
                & i{
                    color: #999;
                    font-size: 16px;
                }
                & img{
                    width: 24px;
                    height: 24px;
                    border-radius: 12px;
                }
                & span{
                    flex: 1;
                    padding-left: 16px;
                    height: 40px;
                    display: inline-block;
                    line-height: 40px;
                }
            }
        }
        &__header{
            height: 48px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            &.border{
                border-bottom: 1px solid #F5F5F5;
            }
            & label{
                font-size: 12px;
                color: #999;
            }
        }
    }
}
</style>
