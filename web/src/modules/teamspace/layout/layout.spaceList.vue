<template>
    <el-row
        class="teamspace-spacelist-root"
    >
        <el-col style="background: #fff; min-width: 216px; max-width: 216px;overflow-x:hidden;">
            <!-- 空间分组 -->
            <local-space-groups
                v-model="currentGroupId"
                :groups="spaceGroups"
                @on-created="getGroups"
                @on-deleted="(group) => {
                    getGroups();
                    if (group.id === currentGroupId) {
                        spaceListType = SpaceViewType.all;
                    }
                }"
            >
                <template slot="default-group">
                    <li
                        class="teamspace-spacelist-group__item"
                        :class="{
                            'teamspace-spacelist-group__item--active': spaceListType === SpaceViewType.all
                        }"
                    >
                        <el-button
                            @click.native="() => setSpaceListType(SpaceViewType.all)"
                            type="text"
                            class="teamspace-spacelist-group__groupname"
                        >
                            <jacp-text>
                                {{ $t('jacp.teamspace.spaceGroup.all') }}
                                ({{ allList.length }})
                            </jacp-text>
                        </el-button>
                    </li>
                    <li
                        class="teamspace-spacelist-group__item"
                        :class="{
                            'teamspace-spacelist-group__item--active': spaceListType === SpaceViewType.archived
                        }"
                    >
                        <el-button
                            @click.native="() => setSpaceListType(SpaceViewType.archived)"
                            type="text"
                            class="teamspace-spacelist-group__groupname"
                        >
                            <jacp-text>
                                {{ $t('jacp.teamspace.spaceGroup.archived') }}
                                ({{ archivedList.length }})
                            </jacp-text>
                        </el-button>
                        <el-tooltip
                            placement="right"
                        >
                            <jacp-icon
                                name="jacp-icon-help"
                                active
                            />
                            <div slot="content">
                                1.团队空间管理员可对空间进行归档<br>
                                2.归档的团队空间会从所有空间成员的“活跃”中消失，进入“归档”
                            </div>
                        </el-tooltip>
                    </li>
                </template>
                <li
                    class="teamspace-spacelist-group__item"
                    :class="{
                        'teamspace-spacelist-group__item--active': spaceListType === SpaceViewType.favo
                    }"
                    slot="prefix-group"
                >
                    <el-button
                        type="text"
                        class="teamspace-spacelist-group__groupname"
                        @click.native="() => setSpaceListType(SpaceViewType.favo)"
                    >
                        <jacp-text>
                            {{ $t('jacp.teamspace.spaceGroup.favorite') }}
                            ({{ favoList.length }})
                        </jacp-text>
                    </el-button>
                </li>
            </local-space-groups>
            <!-- 最近访问 -->
            <local-space-recents />
        </el-col>
        <el-col
            class="teamspace-spacelist__main"
            :class="{
                'teamspace-spacelist__main--large': collapseMenu,
            }"
        >
            <el-row
                class="teamspace-spacelist-header"
                type="flex"
                justify="space-between"
            >
                <el-col :span="12">
                    <el-input
                        v-model="keyword"
                        placeholder="请输入关键字"
                        suffix-icon="el-icon-search"
                        style="width: 200px"
                    />
                    <el-button
                        v-if="showLeaderView"
                        type="warning"
                        style="margin-left: 18px;"
                        @click="$router.push({ name: 'teamspaceLeaderView' })"
                    >
                        进入领域
                    </el-button>
                </el-col>
                <el-col :span="12">
                    <el-button
                        style="float: right;margin-left: 16px;"
                        type="primary"
                        @click="newSpace"
                    >
                        新建团队空间
                    </el-button>
                    <el-dropdown
                        style="float: right;"
                        trigger="click"
                        @command="handleTypeChange"
                    >
                        <el-input
                            readonly
                            v-model="orderDes"
                            class="teamspace-spacelist-spaceorder"
                            suffix-icon="el-icon-arrow-down"
                        />
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                                :disabled="orderType === 'DESC'"
                                command="DESC"
                            >
                                创建日期由近及远
                            </el-dropdown-item>
                            <el-dropdown-item
                                :disabled="orderType === 'ASC'"
                                command="ASC"
                            >
                                创建日期由远及近
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </el-col>
            </el-row>
            <el-row
                class="teamspace-spacelist-content"
                type="flex"
                justify="flex-start"
                v-show="currentList.length"
            >
                <local-space-card
                    v-for="space in currentList"
                    :key="space.id"
                    :space="space"
                    :groups="spaceGroups"
                    @on-copied="getSpaceList"
                >
                    <!-- 移出分组 -->
                    <el-dropdown-item
                        slot="action"
                        v-if="currentGroup && currentGroup.spaceIds.includes(space.id)"
                        @click.native="() => removeSpaceFromGroup(space)"
                    >
                        {{ $t('jacp.teamspace.spaceActions.removeFromGroup') }}
                    </el-dropdown-item>
                </local-space-card>
            </el-row>
            <!-- 个人分组和系统分组给不同的empty提示 -->
            <jacp-empty
                v-if="!currentList.length && (!currentGroupId || keyword)"
                label="暂无空间"
            />
            <jacp-empty
                v-if="!currentList.length && currentGroupId && !keyword"
            >
                <div style="display: flex; flex-direction: column; align-items: center;">
                    <img
                        src="@/assets/images/<EMAIL>"
                        width="136"
                    >
                    <jacp-text
                        size="16"
                        :font-weight="500"
                        class="j-mgt16"
                    >
                        当前分组内没有团队空间
                    </jacp-text>
                    <jacp-text
                        size="14"
                        class="j-mgt8"
                        type="disable"
                    >
                        点击团队空间卡片右上角的更多按钮，可将团队空间加入分组
                    </jacp-text>
                </div>
            </jacp-empty>
        </el-col>
        <jacp-version :lazy-load="false" />
    </el-row>
</template>

<script type="text/javascript">
import { mapGetters } from 'vuex';
import cloneDeep from 'lodash/cloneDeep';
import sortBy from 'lodash/sortBy';
import Dialog from '@/models/dialog';
import { space as SpaceModel } from '@/models/teamspace';
import newSpaceDialog from '$module/dialogs/newSpace';

import SpaceGroup from '$module/models/spaceGroup';
import LocalSpaceCard from '$module/components/spaceCard';
import LocalSpaceGroups from '$module/components/spaceGroups';
import LocalSpaceRecents from '$module/components/spaceRecents';

const SpaceViewType = {
    all: 1,
    favo: 2,
    archived: 3,
};

export default {
    name: 'TeamspaceList',
    components: { LocalSpaceCard, LocalSpaceGroups, LocalSpaceRecents },
    data() {
        const orderType = 'DESC';
        return {
            event: this.$initEvent(),
            spaceList: [],
            searchValue: '',
            spaceListType: localStorage.jacp_spaceListView !== undefined
                ? Number(localStorage.jacp_spaceListView)
                : SpaceViewType.all,
            keyword: '',
            orderType,
            orderDes: '创建日期由近及远',
            SpaceViewType,
            showLeaderView: false,
            spaceGroups: [],
            currentGroupId: localStorage.jacp_spaceListGroupId
                ? Number(localStorage.jacp_spaceListGroupId)
                : null,
        };
    },
    mounted() {
        const { event: $event } = this;
        $event.$on('refresh-space-list', this.getSpaceList);
        this.getSpaceList();
        this.getLeaderViewPermission();
        this.getGroups();
    },
    methods: {
        getGroups() {
            SpaceGroup.getList().then((data = []) => {
                this.spaceGroups = data;
            });
        },
        newSpace() {
            console.warn('主框架');
            return Dialog.confirm({
                title: this.$t('jacp.newTeamspace'),
                confirmBtnText: this.$t('jacp.button.submit'),
                slot: newSpaceDialog,
                customClass: 'dialog-new-space',
                closeOnClickModal: false,
                beforeConfirm: vm => vm.validateData().then(() => SpaceModel.create(vm.data)
                    .then(() => {
                        this.getSpaceList(this.orderType);
                    })),
            });
        },
        handleTypeChange(order) {
            this.orderType = order || 'DESC';
            this.orderDes = order === 'ASC' ? '创建日期由远及近' : '创建日期由近及远';
        },
        getSpaceList() {
            return SpaceModel.getList(this.orderType).then((list) => {
                this.spaceList = cloneDeep(list);
            });
        },
        async getLeaderViewPermission() {
            this.showLeaderView = await SpaceModel.getLeaderViewPermission();
        },
        setSpaceListType(val) {
            this.spaceListType = val;
        },
        removeSpaceFromGroup(space) {
            const { currentGroup } = this;
            SpaceGroup.shift({
                spaceId: space.id,
                groupId: currentGroup.id,
            }).then(() => {
                const index = currentGroup.spaceIds.indexOf(space.id);
                currentGroup.spaceIds.splice(index, 1);
            });
        },
    },
    watch: {
        // FIXME:
        spaceListType(viewType) {
            // 缓存最近一次视图类型
            localStorage.jacp_spaceListView = viewType;
            if (viewType) {
                this.currentGroupId = null;
            }
        },
        currentGroupId: {
            immediate: true,
            handler(groupId) {
                if (groupId) {
                    this.spaceListType = null;
                }
                // 缓存最近一次个人分组
                localStorage.jacp_spaceListGroupId = groupId;
            },
        },
    },
    computed: {
        ...mapGetters('chilldteamspace', ['allList', 'archivedList', 'favoList']),
        currentList() {
            let list;
            const sortFn = () => (a, b) => (this.orderType === 'DESC' ? b.cTime - a.cTime : a.cTime - b.cTime);
            const filterFn = () => space => space.name.toLowerCase().includes(this.keyword.toLowerCase());
            switch (true) {
            case this.spaceListType === SpaceViewType.all && !this.currentGroupId:
                list = this.allList;
                break;
            case this.spaceListType === SpaceViewType.favo && !this.currentGroupId:
                list = this.favoList;
                break;
            case this.spaceListType === SpaceViewType.archived && !this.currentGroupId:
                list = this.archivedList;
                break;
            case !!this.currentGroupId:
                list = this.groupList;
                break;
            default:
                list = this.allList;
            }
            return list.filter(filterFn()).sort(sortFn());
        },
        groupList() {
            const { currentGroupId, spaceList, currentGroup } = this;
            if (!currentGroupId || !currentGroup) {
                return [];
            }

            return sortBy(
                spaceList.filter(space => currentGroup.spaceIds.includes(space.id)),
                space => currentGroup.spaceIds.indexOf(space.id),
            );
        },
        currentGroup() {
            return this.spaceGroups.find(g => g.id === this.currentGroupId);
        },
        collapseMenu: {
            get() {
                return this.$store.state.root.collapsedGlobalMenu;
            },
        },
    },
    filters: {
        mySpace(value) {
            let mySpaceNum = 0;
            value.forEach((space) => {
                if (space.attentionType === 1) {
                    mySpaceNum += 1;
                }
            });
            return mySpaceNum;
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
@searchColor: #DDDFE7;
@searchColorHover: @primaryColor;
.teamspace-spacelist{
    &__main{
        height: 100%;
        overflow-y: auto;
        padding: 24px;
        position: relative;
    }
    &-spaceorder{
        .el-input__inner{
            color: @fontSecendColor;
        }
    }
    &__showmore{
        margin: 0px auto 32px;
        display: block;
    }
    &-root{
        background-color: rgba(241,241,241,1);
        display: flex;
    }
    &__search{
        margin: 40px auto;
        width: 300px;
        font-size: 14px;

        & input{
            translate: all .5s ease-in-out;
            border-radius: 18px;
            border: 1px solid @searchColor;
            &:hover,&:focus{
                border-color: @searchColorHover;
                & + span>i.icon{
                    color: @fadeFontColor;
                }
            }
            & + span>i.icon{
                color: @searchColor;
            }
            &::placeholder{
                color: @searchColor;
            }
        }
    }
    &__list{
        &__add{
            border: 1px solid @borderColor;
            border-radius: 4px;
            background-color: #eee;
            cursor: pointer;
            text-align: center;
            font-size: 12px;
            color: @primaryColor;
            padding-top: 10px;
        }
        &__item{
            background-color: #fff;
        }
        &__add, &__item{
            vertical-align: top;
            margin-bottom: 10px;
            height: 80px;
            &:hover {
                border-color: @primaryColor;
                box-shadow: 0 0 3px 0 @primaryColor;
            }
        }
    }
    &-content{
        display: flex;
        flex-wrap: wrap;
        margin: 0 auto;
        // transform: translateX(12px);
    }
    &-header{
        padding-right: 24px;
        margin: 0 auto;
    }

}

.dialog-new-space{
    width: 768px;
}

.generate-spacelist-width(@n) {
    @width-large: @n * 224;
    @width: (@n - 1 ) * 224;
    .teamspace-spacelist__main--large{
        .teamspace-spacelist-header,
        .teamspace-spacelist-content {
            width: unit(@width-large, px);
        }
    }
    .teamspace-spacelist-header,
    .teamspace-spacelist-content {
        width: unit(@width, px);
    }
}

@media screen and (max-width: 1400px) {
    .generate-spacelist-width(4);
}
@media screen and (min-width: 1400px)  and (max-width: 1800px) {
    .generate-spacelist-width(5);
}
@media screen and (min-width: 1800px)  and (max-width: 2100px) {
    .generate-spacelist-width(7);
}
@media screen and (min-width: 2100px) and (max-width: 2400px){
    .generate-spacelist-width(8);
}
@media screen and (min-width: 2400px) {
    .generate-spacelist-width(10);
}
</style>
