<template>
    <div class="teamspace-general-root">
        <router-view
            :is-no-permission-space="isNoPermissionSpace"
            :space-privilage="spacePrivilage"
        />
        <jacp-empty
            v-if="isNoPermissionSpace"
            :style="{
                marginRight: emptyMarginRight,
            }"
            label="您尚无权限查看其他卡片信息"
        />
    </div>
</template>

<script type="text/javascript">

export default {
    componentName: 'LayoutCardList',
    props: {
        spacePrivilage: {
            type: Object,
            default: () => {},
        },
        isNoPermissionSpace: Boolean,
    },
    data() {
        return {
            emptyMarginRight: 0,
            isCollapse: true,
        };
    },
    computed: {
        currentMenu() {
            const { name } = this.$route;
            return name || 'generalCardList';
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.teamspace-general{
    &-root{
        display: flex;
        flex-direction: column;

        .list-header{
            border-bottom: 1px solid #efefef;
        }
        /* 这里布局和scrum的不一样了 */
        & .teamspace-cards-table-root{
            height: calc(100vh - 50px);
        }
        & .teamspace-cards-table__list{
            height: calc(~"100vh - 160px");
        }
        & .cardslist-root--fullscreen{
            .teamspace-cards-table-root{
                padding-top: 0;
                margin-top: 0;
                background: #fff;
            }
            & .teamspace-cards-table-root{
                height: 100vh;
            }
            & .teamspace-cards-table__list{
                height: calc(~"100vh - 112px");
            }
        }
    }
    &__main{
        display: flex;
        height: calc(~"100vh - 60px");
        width: 100%;
    }
}
</style>
