<template>
    <div class="teamspace-intro-root">
        <el-row
            :gutter="24"
            class="teamspace-intro-row"
        >
            <el-col :span="8">
                <!-- 空间属性 -->
                <el-card
                    shadow="never"
                    class="j-mgb24"
                >
                    <jacp-text
                        slot="header"
                        size="16"
                    >
                        <strong>{{ $t('jacp.teamspace.spaceFields.props') }}</strong>
                    </jacp-text>
                    <div style="display: flex; align-items: center; marin-right: 16px;">
                        <jacp-user-avatar
                            :data="spaceInfo"
                            :size="48"
                            first-name
                            avatar
                        />
                        <div style="margin-left: 16px">
                            <el-row><jacp-text :text="spaceInfo.name" /></el-row>
                            <el-row class="j-mgt4">
                                <jacp-text
                                    type="disable"
                                    size="12"
                                >
                                    {{ spaceInfo.creatorName }}于
                                    <time>{{ spaceInfo.cTime | jacp-local-time('YYYY-MM-DD') }}</time>创建
                                </jacp-text>
                            </el-row>
                        </div>
                    </div>
                    <!-- 基本信息 -->
                    <el-row class="j-mgt24">
                        <jacp-text size="14">
                            <strong>{{ $t('jacp.teamspace.spaceFields.basic') }}</strong>
                        </jacp-text>
                    </el-row>
                    <div
                        class="j-mgt16"
                        style="display: flex;"
                    >
                        <jacp-noticeboard
                            :label="$t('jacp.teamspace.spaceFields.key')"
                            :label-size="14"
                            :value="spaceInfo.key"
                            style="flex: 1"
                            size="14"
                        />
                        <jacp-noticeboard
                            :label="$t('jacp.teamspace.spaceSummary.member')"
                            :label-size="14"
                            :value="allMemberList.length"
                            size="14"
                            style="flex: 1"
                        />
                        <jacp-noticeboard
                            :label="$t('jacp.teamspace.spaceFields.type')"
                            :label-size="14"
                            size="14"
                            style="flex: 1"
                            :value="spaceInfo.mode === 2
                                ? $t('jacp.teamspace.spaceType.scrum')
                                : $t('jacp.teamspace.spaceType.general')"
                        />
                    </div>
                    <!-- 关联项目 -->
                    <el-row class="j-mgt24">
                        <jacp-text size="14">
                            <strong>{{ $t('jacp.teamspace.spaceFields.project') }}</strong>
                        </jacp-text>
                    </el-row>
                    <el-row class="j-mgt8">
                        <jacp-text
                            :type="spaceInfo.pmpProject ? 'default': 'disable'"
                            size="14"
                        >
                            {{ spaceInfo.pmpProject
                                ? spaceInfo.pmpProject.pmpProjectName
                                : `${$t('jacp.empty')}${$t('jacp.teamspace.spaceFields.project')}` }}
                        </jacp-text>
                    </el-row>
                    <template v-if="spaceInfo.product">
                        <el-row class="j-mgt24">
                            <jacp-text size="14">
                                <strong>关联产品</strong>
                            </jacp-text>
                        </el-row>
                        <el-row class="j-mgt8">
                            <jacp-text
                                size="14"
                            >
                                {{ spaceInfo.product.name }}
                            </jacp-text>
                        </el-row>
                    </template>
                </el-card>
                <!-- 空间描述 -->
                <el-card
                    shadow="never"
                    class="j-mgb24"
                >
                    <jacp-text
                        size="16"
                        slot="header"
                    >
                        <strong>{{ $t('jacp.teamspace.spaceFields.description') }}</strong>
                    </jacp-text>
                    <jacp-text
                        type="disable"
                        size="14"
                    >
                        {{ spaceInfo.desc }} || {{ $t('jacp.empty') }}{{ $t('jacp.teamspace.spaceFields.description') }}
                    </jacp-text>
                </el-card>
                <!-- 空间插件 -->
                <el-card
                    shadow="never"
                    class="j-mgb24"
                >
                    <jacp-text
                        size="16"
                        slot="header"
                    >
                        <strong>{{ $t('jacp.teamspace.spaceFields.extensions') }}</strong>
                    </jacp-text>
                    <jacp-text
                        v-if="!vendors.length"
                        type="disable"
                        size="14"
                    >
                        {{ `${$t('jacp.empty')}${$t('jacp.teamspace.spaceFields.extensions')}` }}
                    </jacp-text>
                    <ul class="teamspace-intro-vendors">
                        <li
                            v-for="vendor in vendors"
                            :key="vendor.id"
                            class="teamspace-intro-vendors__item"
                        >
                            <jacp-user-avatar
                                :data="{ name: vendor.vendorName }"
                                :size="24"
                                first-name
                                avatar
                            />
                            <jacp-text
                                size="14"
                                style="margin-left: 8px;"
                            >
                                {{ vendor.vendorName }}
                            </jacp-text>
                        </li>
                    </ul>
                </el-card>
            </el-col>
            <el-col :span="16">
                <el-row class="teamspace-intro-row j-mgb24">
                    <el-card shadow="never">
                        <div slot="header">
                            <jacp-text :size="16">
                                <strong>{{ $t('jacp.teamspace.spaceFields.summary') }}</strong>
                            </jacp-text>
                            <div
                                class="j-mgt24"
                                style="display: flex;justify-content: space-between;"
                            >
                                <el-tooltip
                                    content="代表当前进行中的迭代的数量"
                                >
                                    <jacp-noticeboard
                                        :label="$t('jacp.teamspace.spaceSummary.activeSprint')"
                                        :label-size="14"
                                        :value="summary.unarchivedSprintCount"
                                        no-data-text="0"
                                        size="32"
                                    />
                                </el-tooltip>
                                <el-tooltip
                                    content="代表已经完成的并保存的迭代数量"
                                >
                                    <jacp-noticeboard
                                        :label="$t('jacp.teamspace.spaceSummary.archiveSprint')"
                                        :label-size="14"
                                        :value="summary.archivedSprintCount"
                                        no-data-text="0"
                                        size="32"
                                    />
                                </el-tooltip>
                                <el-tooltip
                                    content="活跃/归档迭代中所有未拖动到完成状态(包括挂起)的卡片数量"
                                >
                                    <jacp-noticeboard
                                        :label="$t('jacp.teamspace.spaceSummary.undoneCards')"
                                        :label-size="14"
                                        :value="summary.unfinishedCardCount"
                                        no-data-text="0"
                                        size="32"
                                    />
                                </el-tooltip>
                                <el-tooltip
                                    content="活跃/归档迭代中所有拖动到完成状态的卡片数量"
                                >
                                    <jacp-noticeboard
                                        :label="$t('jacp.teamspace.spaceSummary.doneCards')"
                                        :label-size="14"
                                        :value="summary.finishedCardCount"
                                        no-data-text="0"
                                        size="32"
                                    />
                                </el-tooltip>
                            </div>
                        </div>
                    </el-card>
                </el-row>
                <el-row class="teamspace-intro-row">
                    <!-- 卡片数量 -->
                    <sprint-card-summary-count
                        style="border-radius: 4px;"
                        class="teamspace-intro-card-summary"
                        :space-key="spaceKey"
                        :title=" $t('jacp.teamspace.spaceSummary.cards')"
                    />
                    <!-- 空间动态 -->
                    <el-card
                        shadow="never"
                        class="j-mgt24 sticky"
                    >
                        <div
                            slot="header"
                            style="position: sticky; top: 0;"
                        >
                            <jacp-text size="16">
                                <strong>{{ $t('jacp.teamspace.spaceFields.feed') }}</strong>
                            </jacp-text>
                            <!-- TODO: 是否通用？还不确定。通用就踢出去 -->
                            <el-popover
                                placement="top-start"
                                title="筛选"
                                width="282"
                                trigger="click"
                                popper-class="badge-filter-popper"
                            >
                                <el-button
                                    style="float: right; padding: 0;"
                                    type="text"
                                    slot="reference"
                                    class="badge-filter"
                                >
                                    <el-badge
                                        :hidden="!hasFilterParams"
                                        is-dot
                                        type="primary"
                                    >
                                        <div position="relative">
                                            <jacp-icon
                                                name="jacp-icon-filter"
                                                active
                                                :size="16"
                                            />
                                            <jacp-icon
                                                v-if="hasFilterParams"
                                                name="el-icon-circle-close"
                                                @click.native.stop="reloadHistory"
                                                avtive
                                            />
                                        </div>
                                    </el-badge>
                                </el-button>
                                <query-space-history-form
                                    v-model="filterParams"
                                    ref="queryFormEl"
                                    :member-list="allMemberList"
                                    @submit="() => {
                                        pager.currentPage = 1;
                                        historys = [];
                                        searchHistory();
                                    }"
                                />
                            </el-popover>
                        </div>
                        <jacp-record-pane
                            :list="historys"
                            split-time
                            class="teamspace-intro-history"
                        >
                            <el-button
                                type="text"
                                slot="pager"
                                style="display: block;width: 100%;text-align: center;"
                                v-if="historys.length && pager.hasMorePages"
                                :disabled="historyLoading"
                                @click="() => {
                                    pager.next();
                                    searchHistory();
                                }"
                            >
                                查看更多
                            </el-button>
                        </jacp-record-pane>
                    </el-card>
                </el-row>
            </el-col>
        </el-row>
    </div>
</template>
<script>
import { mapState } from 'vuex';
import Pager from '@/plugins/pager';
import SpaceModel from '@/models/space';
import SprintCardSummaryCount from '$module/components/sprintReport/sprintCardSummaryCount';
import QuerySpaceHistoryForm from '$module/components/querySpaceHistoryForm';

export default {
    name: 'LayoutSpaceIntro',
    components: { SprintCardSummaryCount, QuerySpaceHistoryForm },
    props: {
        spaceKey: { type: String, default: '', required: true },
    },
    data() {
        return {
            spaceInfo: {},
            summary: {},
            summaryChartData: {},
            vendors: [],
            historys: [],
            filterParams: {},
            pager: new Pager(),
            historyLoading: false,
        };
    },
    watch: {
        spaceKey: {
            immediate: true,
            handler: 'init',
        },
    },
    methods: {
        async init() {
            this.spaceInfo = await SpaceModel.getDetail();
            this.summary = await SpaceModel.getSummary(this.spaceKey);
            this.vendors = await SpaceModel.getSpaceVendorConfig(this.spaceKey);
            this.reloadHistory();
        },
        async searchHistory() {
            this.historyLoading = true;
            const historys = await SpaceModel.getHistory(this.spaceKey, this.filterParams, this.pager);
            this.historys = [...this.historys, ...historys];
            this.historyLoading = false;
        },
        reloadHistory() {
            this.historys = [];
            this.pager.currentPage = 1;
            // eslint-disable-next-line no-unused-expressions
            this.$refs.queryFormEl && this.$refs.queryFormEl.resetForm();
            this.filterParams = {};
            this.searchHistory();
        },
    },
    computed: {
        ...mapState('chilldteamspace', ['keyMap', 'keyMapPlus', 'allMemberList']),
        hasFilterParams() {
            return !!Object.keys(this.filterParams).length;
        },
    },
};
</script>
<style lang="less">
.teamspace-intro-root{
    padding: 24px;
    .el-card{
        border: 1px solid transparent;
    }
    .el-card__header{
        border-bottom: 0;
        padding: 24px 24px 16px 24px;
    }
    .el-card__body{
        padding: 0 24px 24px 24px;
    }
}

.teamspace-intro-vendors{
    display: flex;
    flex-wrap: wrap;
    li{ list-style: none; }
    &__item{
        display: flex;
        align-items: center;
        &+&{
            margin-left: 24px;
        }
    }
}
.teamspace-intro-card-summary{
    .layout-block-title__text{
        font-weight: 500;
    }
    .icon-fullscreen{
        display: none!important;
    }
}

.badge-filter{
    position: relative;
    .el-icon-circle-close{
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(8px, -8px);
        background: #fff;
        border-radius: 50%;
        z-index: 1;
    }
    &:hover{
        .el-icon-circle-close{
            display: inline-block;
        }
    }
    &-popper{
        padding: 16px;
    }
}
.teamspace-intro-history{
    .jacp-record-pane__operator{
        width: 24%;
        max-width: 300px;
        min-width: 200px;
    }
    .jacp-record-pane__remark{
        max-width: 800px;
        margin-right: 64px;
    }
}
</style>
