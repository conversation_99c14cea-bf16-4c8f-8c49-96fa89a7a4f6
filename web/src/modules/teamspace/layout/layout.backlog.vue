<template>
    <!-- FIXME:在sortable里multiDrag开启的时候，点击外侧会将已选择的元素取消掉 ，
    stopPropagation 到document上可以阻止这个行为，https://github.com/SortableJS/Sortable/issues/1612
    方案1: 直接在当前页面禁用这个【点击外侧取消已选择】的行为
    -->
    <div
        :class="scopedClass.mod({
            shrink: shrink
        }).cn"
    >
        <template v-if="!isNoPermissionSpace">
            <div :class="scopedClass.el('header').cn">
                <span>
                    <jacp-text
                        size="16"
                        font-weight="600"
                    >{{ $t('jacp.teamspace.backlog.title') }}</jacp-text>
                    <jacp-text
                        size="12"
                        type="disable"
                    >
                        {{ $t('jacp.teamspace.backlog.desc') }}
                    </jacp-text>
                    <el-button
                        v-if="$refs.tips"
                        style="font-weight: 400;"
                        type="text"
                        @click="() => {
                            $refs.tips.showBacklogTipsAndDialog();
                        }"
                    >观看操作演示</el-button>
                </span>

                <div :class="scopedClass.el('header').el('r').cn">
                    <entity-creator
                        class="j-mgl16"
                        :visible-actions="['createCard']"
                        :handler-parameters="{
                            useDrawer: true,
                            initialFormData: {
                                sprintId: -1,
                            },
                        }"
                    />
                    <jacp-dropdown-settings
                        v-if="$refs.tableEl"
                        label="卡片"
                        module-key="space"
                        setting-type="list"
                        :disabled="['import']"
                        @export="$refs.tableEl.exportCards"
                        @import="$refs.tableEl.importCards"
                        @save-displayFields="() => $refs.tableEl.initColumns().then(() => loadData())"
                    />
                    <el-divider
                        class="j-mgl16 j-mgr16"
                        direction="vertical"
                    />
                    <el-button
                        :class="scopedClass.el('header').el('r').el('button').cn"
                        type="text"
                        @click.native="() =>shrink = !shrink"
                    >
                        <jacp-icon
                            style="transform: translateY(1px);"
                            :name="shrink ? 'jacp-icon-collapsed' : 'jacp-icon-expand'"
                        />
                        <label class="j-mgl4">迭代</label>
                    </el-button>
                </div>
            </div>

            <LocalViewCardListTable
                ref="tableEl"
                :class="scopedClass.el('content').mod({
                    lowhight: tableTipsVisible,
                    overlay: draging,
                }).cn"
                :active-plan="backlogPlan"
                :groupable="false"
                :draggable="true"
                :showSwitch="false"
                @click-card="viewCardDetail"
                @drag-start="draging = true"
                @drag-end="draging = false"
                @drag-abort="draging = false"
                @pull-end="(evt) => {
                    draging = false;
                    handleBatchAddition(evt)
                }"
                @after-sorted="() => loadData()"
            >
                <div slot="header">
                    <!-- 这里是精简版的header -->
                    <JacpCommonQueryFilter
                        filter-personal-key="card-query"
                        dark
                        @query="loadData"
                    />
                    <LocalBacklogTips
                        ref="tips"
                        :tips-visible.sync="tableTipsVisible"
                    />
                </div>
            </LocalViewCardListTable>
            <div :class="scopedClass.el('aside').cn">
                <LocalSprints
                    ref="sprintEl"
                    layout="searcher"
                    :sprint-id="backlogPlan.id"
                    :async-to-store="false"
                    :focused-on-click="false"
                    :highlight="draging"
                    @sprint-click="gotoSprint"
                    @sprints-changed="initPlaceholderSortable"
                >
                    <template v-slot:sprint="{sprint}">
                        <LocalSprintAnimateBlock
                            :ref="`sprint-${sprint.id}`"
                            :sprint="sprint"
                            @after-revert="() => loadData()"
                        />
                    </template>
                </LocalSprints>
            </div>
        </template>
        <jacp-empty
            v-else
            :style="{
                marginRight: cardId ? '880px' : '0px',
            }"
            label="您尚无权限查看其他卡片信息"
        />
    </div>
</template>
<script>
import Sortable from 'sortablejs';
import isPlainObject from 'lodash/isPlainObject';
import { backlogPlan } from '@/modules/sprints/models/sprint';
import LocalSprints from '@/modules/sprints';
import LocalViewCardListTable from '@/modules/card/view.cardsList.table';
import LocalSprintAnimateBlock from '@/modules/sprints/components/sprintAnimateBlock';
import LocalBacklogTips from '$module/components/backlogTips';
import cloneDeep from 'lodash/cloneDeep';
import cardActions from '@/models/cardActions';
// eslint-disable-next-line no-redeclare
import { event } from '@/plugins/event';
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

export default {
    name: 'TeamspaceBacklog',
    components: {
        LocalSprints,
        LocalViewCardListTable,
        LocalSprintAnimateBlock,
        LocalBacklogTips,
    },
    props: {
        spaceKey: { type: String },
        cardId: { type: [String, Number] },
        isNoPermissionSpace: Boolean,
    },
    data() {
        return {
            scopedClass: this.$scopedClass('teamspace-layout'),
            shrink: false,
            backlogPlan,
            event,
            draging: false,
            tableTipsVisible: false,
        };
    },
    created() {
        this.event.$on('refresh-list', () => this.loadData());
        this.event.$on('backlog-add', () => this.loadData());
        this.event.$on('after-action', () => {
            this.loadData();
            if (this.drawer?.hide) {
                this.drawer.hide();
            }
        });
        this.event.$on('sprint:archiveSprint', () => this.loadData());
    },
    mounted() {
        if (this.cardId) {
            this.drawer = cardActions.openDetail({ id: this.cardId });
        }
    },
    methods: {
        loadData(conditions) {
            if (!this.$refs.tableEl) {
                return;
            }
            if (isPlainObject(conditions)) {
                this.$refs.tableEl.manualUpdateData(conditions);
            } else {
                this.$refs.tableEl.updateData();
            }
        },
        // 批量拖动到迭代列表的时候干的事情
        handleBatchAddition({
            related, dragedItems, dragedItem,
        } = {}) {
            // 这个dragedItems数据会被handler立即清除，clone一个出来撤销的时候临时用
            const cards = dragedItems && dragedItems.length
                ? dragedItems
                : [dragedItem];
            if (related) {
                const id = related.dataset?.item;
                const sprintBlockEl = this.$refs[`sprint-${id}`];
                if (sprintBlockEl) {
                    sprintBlockEl.increase(cloneDeep(cards)).then(() => this.loadData());
                }
            }
        },
        initPlaceholderSortable() {
            this.$nextTick(() => {
                const listEl = this.$el.querySelector('.jacp-sprint-list');
                if (!listEl) {
                    return;
                }
                this.sortable = new Sortable(listEl, {
                    group: {
                        name: 'shared',
                        put: true,
                        pull: false,
                    },
                    sort: false,
                    multiDrag: true,
                    // ghostClass: 'sprint-sortable-ghost',
                    animation: 150,
                });
            });
        },
        gotoSprint(sprint = {}) {
            const url = this.$router.resolve({
                name: 'teamspaceCardsList',
                params: {
                    spaceKey: this.$route.params.spaceKey,
                },
                query: {
                    sprintId: sprint.id,
                },
            }) || {};
            window.open(url.href, '_blank');
        },
        viewCardDetail(card) {
            this.drawer = cardActions.openDetail(card);
        },
    },
};
</script>
<style lang="less">
.teamspace-layout{
    background-color: #fff;
    padding: var(--gutter--medium) 0 0 0;
    display: grid;
    grid-template-columns: auto 240px;
    grid-auto-rows: 40px  auto;
    grid-template-areas: "header header"
                       "content aside";
    &--shrink{
         grid-template-columns: auto 0;
    }
    &__header{
        grid-area: header;
        align-items: center;
        justify-content: space-between;
        padding: 0 var(--gutter--large);
        position: relative;
        width: 100%;
        display: flex;
        border-bottom: var(--border--hr);
        &__r__button{
            font-size: var(--font-size--content);
            &:not(:hover) {
                i{
                    color: var(--color--secondary--content);
                }
                label{
                     color: var(--color--base--content);
                }
            }
            label{
                font-weight: normal;
            }
        }
    }
    &__content{
        grid-area: content;
        margin: 0;
        // override 显示tips的时候高度要变化
   .teamspace-cards-table__list{
            height: calc(100% - 48px);
        }

        &--lowhight{
            .teamspace-cards-table__list{
                height: calc(100% - 88px);
            }
        }
        &--overlay{
            .cards-table{
                overflow: hidden;
            }
        }
    }
    &__aside{
        grid-area: aside;
        transition: all 0.15s;
        position: relative;
        overflow:hidden;
        height: 100%;
    }
}
// override 对齐一下左边
.teamspace-layout{
    .query-actions,
    .jacp-common-filter__header{
        padding: 0 var(--gutter--large);
    }
    .teamspace-cards-table-root{
        padding-top: 0;
    }
     .teamspace-cards-table__list{
            margin-top: 0;
            // padding: var(--gutter--large);
        }

    .teamspace-cards-table__list .cards-table{
        // height: calc(100% - 40px);
    }
    .jacp-sprints{
        height: 100%;
        .jacp-sprint-list{
            border-top: var(--border--hr);
        }
    }
}
</style>
