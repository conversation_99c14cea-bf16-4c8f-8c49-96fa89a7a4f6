<template>
    <div class="general-cardslist-root">
        <div
            class="general-cardslist__main"
            :class="{
                'general-cardslist__main--shrink': shrinkPlanlistPanel,
                'cardslist-root--fullscreen': fullScreenState,
            }"
        >
            <template v-if="!isNoPermissionSpace">
                <div
                    class="general-cardslist__data cardslist-root__main"
                    ref="mainEl"
                >
                    <component
                        :is="currentView"
                        :key="$route.params.spaceKey"
                        ref="listIns"
                        :space-privilage="spacePrivilage"
                        :archived="archivedCard"
                        @full-screen="toggleFullscreen"
                        @plan-changed="refreshPlansData()"
                        @click-card="$_showDetail($event)"
                    />
                </div>
            </template>
        </div>
        <jacp-off-canvas
            ref="cardDetailPanelIns"
            @on-open="emptyMarginRight = '880px'"
            @hide="emptyMarginRight = 0"
            :before-hide="$_closeGeneralDetail"
        >
            <router-view
                @after-action="afterDeleteRefreshData(), refreshPlansData()"
                :disabled="!spacePrivilage.updateCard"
                :space-privilage="spacePrivilage"
                :archived="archivedCard"
                @refresh-list="refreshData"
                @refresh-sprints="refreshPlansData"
            />
        </jacp-off-canvas>
    </div>
</template>

<script>
import cardsTable from '@/modules/card/view.general.table';
import cardsBoard from '@/modules/card/view.general.board';
import MixinCardDetail from '@/modules/card/mixins/mixin.cardDetail';
import mixinFullscreen from '@/modules/card/mixins/mixin.fullscreen';
import { getSpaceInfo } from '@/plugins/utils';

export default {
    componentName: 'LayoutCardList',
    mixins: [MixinCardDetail, mixinFullscreen],
    props: {
        spacePrivilage: {
            type: Object,
            default: () => {},
        },
        isNoPermissionSpace: Boolean,
    },
    mounted() {
        this.$on('refresh-list', this.refreshData);
        const routerQuery = this.$route.query || {};
        if (routerQuery.cardId && routerQuery.planId) {
            this.$_showDetail({
                id: +routerQuery.cardId,
                planId: +routerQuery.planId,
                spaceKey: this.$route.params.spaceKey,
            });
        }
        // this.fullScreenElement = this.$refs.mainEl;
    },
    data() {
        return {
            shrinkPlanlistPanel: false,
            activeBurndownChart: false,
            burndownChartData: {},
            emptyMarginRight: 0,
            event: this.$initEvent(),
        };
    },
    created() {
        this.event.$on('create-card', this.createCard);
    },
    methods: {
        showBurndownChart(planData) { // 查看燃尽图
            this.activeBurndownChart = true;
            this.burndownChartData = planData;
        },
        togglePlanlist() {
            this.shrinkPlanlistPanel = !this.shrinkPlanlistPanel;
        },
        refreshPlansData() {
            const { plansIns } = this.$refs;
            if (plansIns) {
                plansIns.updateSprintSummary();
            }
        },
        afterDeleteRefreshData() {
            const { listIns } = this.$refs;
            if (this.currentViewName === 'board') {
                listIns.updateBoard(listIns.currentPlan);
            }
            if (this.currentViewName === 'table') {
                listIns.updateData();
            }
        },
        refreshData() {
            const { listIns } = this.$refs;
            if (this.currentViewName === 'board') {
                listIns.updateBoard(listIns.currentPlan);
            }
            if (this.currentViewName === 'table') {
                listIns.updateData();
            }
        },
        createCard() {
            this.$router.push({
                name: 'generalCardAdd',
                params: {
                    spaceKey: this.$route.params.spaceKey,
                },
            });
        },
        renderOffcanvas(to) {
            const vm = this;
            if (!vm.$refs.cardDetailPanelIns) return;
            if (to.meta && to.meta.showOffCanvas) {
                vm.$refs.cardDetailPanelIns.open();
            } else {
                vm.$refs.cardDetailPanelIns.setOpenStatus(false);
            }
        },
        spaceFilter(space) {
            const { spaceKey } = this.$route.params;
            const curSpace = this.$store.state.chilldteamspace.keyMap[spaceKey];
            return space.mode === curSpace.mode;
        },
    },
    computed: {
        currentViewTip() {
            return {
                table: '切换为看板视图',
                board: '切换为列表视图',
            }[this.currentViewName];
        },
        currentViewName() {
            // 默认进入看板
            return this.$route.query.view
                || (localStorage.defaultSpaceView === 'report' ? 'board' : localStorage.defaultSpaceView)
                || 'board';
        },
        otherViewName() {
            return {
                board: 'table',
                table: 'board',
            }[this.currentViewName];
        },
        otherViewIcon() {
            return {
                board: 'icon-list-unorder',
                table: 'icon-module',
            }[this.currentViewName];
        },
        currentView() {
            return {
                board: cardsBoard,
                table: cardsTable,
            }[this.currentViewName];
        },
        createCardText() {
            return this.$t('jacp.button.createCard');
        },
        archivedCard() {
            const currentPlan = this.$store.state.plan.activePlan;
            return currentPlan && currentPlan.archived === 1;
        },
        spaceMode() {
            const space = getSpaceInfo(this.$route.params.spaceKey);
            return space ? space.mode : 1;
        },

        /* emptyMarginRight() {
            const { cardDetailPanelIns } = this.$refs;
            if (!cardDetailPanelIns || !cardDetailPanelIns.openStatus) {
                return 0;
            }
            // console.log(cardDetailPanelIns.$el.querySelector('.jacp-offcanvas__main'));
            return `${cardDetailPanelIns.$el.style.width}px`;
        }, */
    },
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            vm.renderOffcanvas(to);
        });
    },
    beforeRouteUpdate(to, from, next) {
        this.renderOffcanvas(to);
        next();
    },
    // 开启全局搜索的时候默认收起
    watch: {
        '$store.state.plan.isGlobalMode': {
            deep: true,
            handler(n) {
                this.shrinkPlanlistPanel = Boolean(n);
            },
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.general-cardslist{
    &-root{
        width: 100%;
        background-color:#fff;
    }
    &__main{
        display: flex;

        height: 100%;
        width: 100%;
        position: relative;
        // margin: 0 24px;
        // margin-bottom: 0;
    }
    &__createbtn{
         position: fixed;
        right: 48px;
        top: 86px;
        // transform: translateY(-100%);
        font-size: 12px;
        // height: 32px;
        z-index: 1;
    }
    &__main--shrink &__plans{
        width: 20px;
        overflow: hidden;
    }
    &__main--shrink &__plans__list,
    &__main--shrink &__plans__shrinktip{
        transform: translateX(-230px);
    }
    &__border{
        width: 10px;
        display: flex;
        align-items: center;

        & *{
            color: #fff;
            background-color: #999;
            padding: 10px 0;
            transform: scaleX(0.5);
            transform-origin: left;
            cursor: pointer;
        }
    }
    &__plans{
        transform: translateZ(1);
        transition: width 0.15s;
        &__list, &__shrinktip{
            transform: translateX(0);
            transition: 0.15s;
        }
    }
    &__plans{
        display: flex;
        flex-shrink: 0;
        width: 230px;
        overflow-x: hidden;
        overflow-y: auto;
        height: 100%;
        background-color: #fff;
        border: 1px solid @borderColor;

        &__list{
            width: 230px;
            flex-shrink: 0;
            padding-bottom: 20px;
        }
        &__shrinktip{
            color: #999;
            font-size: 12px;
            align-items: center;
            display: flex;
            padding-left: 3px;
        }
    }
    &__data{
        flex: 1;
        height: 100%;
        // background-color: @mainBgColor;
        // border: 1px solid @borderColor;
        // background-color: #fff;
        overflow: hidden;
        position: relative;
    }
    &__detail{
        width: 920px;
    }
}
</style>
