<template>
    <div class="teamspace-index">
        <!-- header -->
        <div class="teamspace-index__header jacp-root-header">
            <!-- header title -->
            <span
                class="jacp-root-header__title j-whitespace-nowwrap"
                style="margin-right: 8px"
                @click="()=>gotoPage('teamspaceSpaceList')"
            >{{ $route.meta.title || '团队空间' }}</span>
            <!-- Space list -->
            <template v-if="!$route.meta.noSpaceList">
                <el-divider
                    direction="vertical"
                    style="min-width: 1px;"
                />
                <jacp-dropdown
                    class="j-whitespace-nowwrap j-mgl8 j-mgr8"
                    v-if="currentSpaceId"
                    menu-align="top-start"
                    :data="groupedSpaceList"
                    :filter="(space) => space.mode === currentSpace.mode"
                    :value="currentSpaceId"
                    @change="switchSpace"
                    mode="group"
                />
                <div
                    v-else
                    class="teamspace-index__label"
                >
                    {{ (keyMapPlus[spaceKey] || {}).name }}
                </div>
            </template>
            <!-- header menu -->
            <template v-if="!$route.meta.noHeaderMenu">
                <el-divider
                    direction="vertical"
                />
                <local-teamspace-header-menu
                    v-if="!isNoPermissionSpace"
                    :is-general="isGeneral"
                    :space-key="spaceKey"
                    :space-privilage="spacePrivilage"
                    style="margin-left: -8px"
                />
                <!-- FIXME: 右侧这个改成根据路由meta可配? -->
                <router-link
                    v-if="!isNoPermissionSpace"
                    style="position: absolute;right: var(--gutter--large);"
                    :to="{
                        name:! isGeneral ? 'teamspaceSpaceSetting' :'generalSpaceSetting'
                    }"
                >
                    <jacp-text
                        icon="jacp-icon-setting"
                        type="default"
                        active
                        active-color="var(--color--primary--hover)"
                        v-if="jurisdicList['space:view'] === 0"
                    >
                        空间设置
                    </jacp-text>
                </router-link>
            </template>
        </div>
        <!-- content -->
        <jacp-empty
            v-if="noPermissionVisible"
            label="您尚无权限查看其他空间信息"
        />
        <router-view
            v-else
            class="teamspace-index__main"
            :is-no-permission-space="isNoPermissionSpace"
            :space-privilage="spacePrivilage"
        />
    </div>
</template>

<script type="text/javascript">
import { mapActions, mapGetters, mapState } from 'vuex';
import store from '$platform.store';
import LocalTeamspaceHeaderMenu from '@/modules/teamspace/components/extensionPoint/teamspaceHeaderMenu';

export default {
    props: {
        spaceKey: { type: String, default: '' },
    },
    components: { LocalTeamspaceHeaderMenu },
    computed: {
        isNoPermissionSpace() {
            const { spaceKey } = this;
            const { keyMap } = this;
            return !keyMap[spaceKey]
            && !this.spacePrivilage.isGuest;
        },
        // TODO: 为毛没有权限的tab还要显示出来，然后点进去以后再出现没权限？
        noPermissionVisible() {
            return this.$route.meta.permissionRequired && this.isNoPermissionSpace;
        },
        isGeneral() {
            const { currentSpace = {} } = this;
            return currentSpace.mode === 1;
        },
        ...mapState('chilldteamspace', ['keyMap', 'spaceList', 'keyMapPlus', 'currentSpaceKey', 'spacePrivilage']),
        ...mapGetters('chilldteamspace', ['currentSpace', 'currentSpaceId', 'groupedSpaceList']),
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
        }),
    },
    methods: {
        setupSpace(spaceKey) {
            if (spaceKey && !this.isNoPermissionSpace) {
                this.$store.commit('chilldteamspace/updateState', { currentSpaceKey: spaceKey });
                this.$store.dispatch('chilldteamspace/updateSpacePrivilage', spaceKey);
                this.fetchRequiredReview(spaceKey);
                this.fetchWorktime(spaceKey);
                this.fetchAllInfoBySpaceId({
                    spaceKey,
                });
                this.fetchMemberList();
            }
        },
        switchSpace(space) {
            store.commit('chilldteamspace/addRecentSpaceList', space.key);
            this.$utils.replaceUrl({
                spaceKey: space.key,
            });
        },
        gotoPage(name) {
            this.$router.push({ name });
        },
        ...mapActions('chilldteamspace', [
            'fetchAllInfoBySpaceId',
            'fetchRequiredReview',
            'fetchWorktime',
            'fetchMemberList']),
    },
    watch: {
        spaceKey: {
            handler: 'setupSpace',
            immediate: true,
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var.less';

.teamspace-index{
    display: flex;
    flex-direction: column;
    &__main{
        overflow: auto;
        height: calc(~'100vh - 50px');
    }
    &__header{
        position: relative;
        width: 100%;
        background-color:#fff;
        align-items: center;
        display: flex;
        padding-left: var(--gutter--large);
        min-height: var(--height--base);
        border-bottom: 1px solid #f1f1f1;
        .jacp-root-header__title{
            cursor: pointer;
        }
        .jacp-dropdown__value{
            font-weight: 500;
            color: @fontColor;
            font-size: 14px;
        }
    }
}
</style>
