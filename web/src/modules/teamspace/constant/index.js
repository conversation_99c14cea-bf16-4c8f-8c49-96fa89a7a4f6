import createEnum from '@/plugins/enum';


export const stageMap = {
    10: {
        name: '准备',
        color: '#BA68C8',
    },
    30: {
        name: '评审',
        color: '#F4BE00',
    },
    50: {
        name: '就绪',
        color: '#198DEC',
    },
    70: {
        name: '设计',
        color: '#FF8D00',
    },
    90: {
        name: '开发',
        color: '#6AB4FF',
    },
    110: {
        name: '测试',
        color: '#0ED396',
    },
    130: {
        name: '发布',
        color: '#F84E54',
    },
    150: {
        name: '完成',
        color: '#78C913',
    },
    170: {
        name: '挂起',
        color: '#90A3AE',
    },
};
export const generalStageMap = {
    20: {
        name: 'TODO',
        color: '#BA68C8',
    },
    80: {
        name: 'DOING',
        color: '#198DEC',
    },
    150: {
        name: 'DONE',
        color: '#78C913',
    },
    170: {
        name: '挂起',
        color: '#90A3AE',
    },
};

export const spaceHistoryType = [{
    name: '卡片',
    id: 'card',
}, {
    name: '任务',
    id: 'task',
}, {
    name: '迭代',
    id: 'sprint',
}, {
    name: '团队空间',
    id: 'space',
}];

export const spaceHistoryOprationType = [{
    name: '创建',
    id: 'create',
}, {
    name: '状态变更',
    id: 'statusUpdate',
}, {
    name: '内容变更',
    id: 'contentUpdate',
}, {
    name: '删除',
    id: 'delete',
}];

export const listViewType = createEnum([{
    name: '列表',
    value: 'table',
    icon: 'jacp-icon-a-ic-toolbar-list',
}, {
    name: '看板',
    value: 'board',
    icon: 'jacp-icon-a-ic-toolbar-kanban',
}]);

export const listEntities = [
    { value: 'card', name: '卡片', routeName: 'teamspaceCardsList' },
    { value: 'task', name: '任务', routeName: 'teamspaceTaskList' },
];


export const wipStatusOptions = [
    { value: 0, label: '不限制数量' },
    { value: 1, label: '允许超出数量，仅提示' },
];

export const spaceSettingMenuTitle = {
    spaceInfo: '基本信息',
    spacePermission: '人员管理',
    spacePermAssign: '权限配置',
    CustomizedRoleSetting: '角色配置',
    spaceTag: '标签',
    cardOption: '卡片类型',
    pluginManage: '插件集成',
};

// spaceModeCondition用于spaceMode判断
export const spaceSettingMenuList = [
    {
        label: '空间设置',
        item: [
            { label: '基本信息', value: 'spaceInfo', spaceModeCondition: [1, 2] },
            { label: '人员管理', value: 'spacePermission', spaceModeCondition: [1, 2] },
            { label: '权限配置', value: 'spacePermAssign', spaceModeCondition: [1, 2] },
            { label: '角色配置', value: 'CustomizedRoleSetting', spaceModeCondition: [2] },
        ],
    },
    {
        label: '卡片',
        item: [
            {
                label: '看板', value: 'CustomizeCardFields', spaceModeCondition: [1, 2], showTab: 'tb1',
            },
            {
                label: '状态与工作流', value: 'cardStatus', spaceModeCondition: [1, 2], showTab: 'tb2',
            },
            { label: '标签', value: 'spaceTag', spaceModeCondition: [1, 2] },
            { label: '卡片类型', value: 'cardOption', spaceModeCondition: [2] },
        ],
    },
    {
        label: '插件',
        item: [
            { label: '插件集成', value: 'pluginManage', spaceModeCondition: [2] },
        ],
    },
];

export const spaceSettingSubTabMenu = [
    {
        label: '卡片自定义', value: 'CustomizeCardFields', spaceModeCondition: [1, 2], showTab: 'tb1',
    },
    {
        label: '看板显示设置', value: 'boardSetting', spaceModeCondition: [2], showTab: 'tb1',
    },
    {
        label: '卡片状态设置', value: 'cardStatus', spaceModeCondition: [1, 2], showTab: 'tb2',
    },
    {
        label: '流程流转', value: 'processManage', spaceModeCondition: [1, 2], showTab: 'tb2',
    },
    {
        label: '流程约束', value: 'flowOption', spaceModeCondition: [2], showTab: 'tb2',
    },
];
