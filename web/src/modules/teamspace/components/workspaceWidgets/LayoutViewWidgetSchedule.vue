<template>
    <layout-card
        :title="title"
        class="jacp-schedule-box"
    >
        <div
            class="jacp-schedule"
            :class="{'jacp-schedule__fullscreen': fullScreenState}"
        >
            <div class="jacp-schedule-header">
                <el-button
                    type="primary"
                    plain
                    size="mini"
                    @click="changeDate(0)"
                >
                    今天
                </el-button>
                <div class="jacp-schedule-header__date">
                    <i
                        class="el-icon-arrow-left"
                        @click="changeDate(-1)"
                    />
                    <span>{{ currentMonth }}</span>
                    <i
                        class="el-icon-arrow-right"
                        @click="changeDate(1)"
                    />
                </div>
                <el-radio
                    v-model="taskStatus"
                    :label="0"
                >
                    全部任务
                </el-radio>
                <el-radio
                    v-model="taskStatus"
                    :label="1"
                >
                    未完成任务
                </el-radio>
                <el-radio
                    v-model="taskStatus"
                    :label="2"
                >
                    已完成任务
                </el-radio>
                <i
                    class="fullBtn"
                    :title="fullScreenState ? '退出全屏' : '全屏模式'"
                    :class="[fullScreenState ? 'jacp-icon-fullscreen-exit' : 'jacp-icon-fullscreen']"
                    @click="toggleScheduleFullscreen()"
                />
            </div>
            <div class="jacp-schedule-body">
                <template v-for="(day, index) in workDays">
                    <div
                        :key="index"
                        class="jacp-schedule-body__day"
                        :class="{'weekend': new Date(day.date).getDay() === 0
                                     || new Date(day.date).getDay() === 6,
                                 'head-line': index < 7,
                                 'today': today.getTime() === day.date,
                                 'otherMonth': curMonth !== new Date(day.date).getMonth()}"
                    >
                        <div
                            class="week"
                            v-if="index < 7"
                        >
                            {{ weekList[new Date(day.date).getDay()] }}
                        </div>
                        <div class="date">
                            {{ new Date(day.date).getDate() }}
                        </div>
                        <ul class="task-list">
                            <template v-if="currentWorkList(day.tasks).length < 4">
                                <li
                                    v-for="task in currentWorkList(day.tasks)"
                                    :key="task.personHour.id"
                                    :class="{
                                        doing: task.personHour.status === 3,
                                        finish: task.personHour.status === 2,
                                        'un-finish': task.personHour.status === 1 }"
                                    @click="showCardDetail(task)"
                                >
                                    <el-popover
                                        placement="right"
                                        width="264"
                                        :open-delay="300"
                                        popper-class="jacp-schedule-popper"
                                        trigger="hover"
                                    >
                                        <schedule-detail :task="task" />
                                        <span slot="reference">{{ task | displayTitle }}</span>
                                    </el-popover>
                                </li>
                            </template>
                            <template v-else>
                                <li
                                    v-for="task in currentWorkList(day.tasks).slice(0, 2)"
                                    :key="task.personHour.id"
                                    :class="{
                                        doing: task.personHour.status === 3,
                                        finish: task.personHour.status === 2,
                                        'un-finish': task.personHour.status === 1}"
                                    @click="showCardDetail(task)"
                                >
                                    <el-popover
                                        placement="right"
                                        width="264"
                                        :open-delay="300"
                                        popper-class="jacp-schedule-popper"
                                        trigger="hover"
                                    >
                                        <schedule-detail :task="task" />
                                        <span slot="reference">{{ task | displayTitle }}</span>
                                    </el-popover>
                                </li>
                                <li class="more">
                                    <el-popover
                                        placement="right"
                                        width="264"
                                        popper-class="jacp-schedule-popper"
                                        trigger="click"
                                    >
                                        <schedule-more
                                            v-if="visibleIndex === index"
                                            @showDetal="showCardDetail"
                                            :tasks="currentWorkList(day.tasks)"
                                            :date="day.date"
                                        />
                                        <span
                                            @click="delayLoad(index)"
                                            slot="reference"
                                        >+{{ currentWorkList(day.tasks).length - 2 }}任务</span>
                                    </el-popover>
                                </li>
                            </template>
                        </ul>
                    </div>
                </template>
            </div>
        </div>
    </layout-card>
</template>
<script>
import fscreen from 'fscreen';
import { MyZoneModel } from '@/models/myzone';
import ScheduleMore from './scheduleMore';
import ScheduleDetail from './scheduleDetail';
import RouterMixin from '@/mixins/mixin.router';

export default {
    name: 'LayoutViewWidgetSchedule',
    mixins: [RouterMixin],
    components: {
        ScheduleMore,
        ScheduleDetail,
    },
    props: {
        title: { type: String, default: '个人日程表' },
    },
    watch: {
        visibleIndex(newVal, oldVal) {
            if (newVal === oldVal) {
                this.visibleIndex = -1;
            }
        },
    },
    data() {
        return {
            taskStatus: 0,
            // yyyy-mm-dd 00:00:00
            today: new Date((new Date()).toLocaleDateString()),
            curMonth: new Date().getMonth(),
            firstDayOfMonth: new Date((new Date()).getFullYear(), (new Date()).getMonth(), 1),
            currentMonth: '',
            fullScreenState: false,
            workDays: [],
            weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
            // 更多任务点击加载内容index
            visibleIndex: -1,
        };
    },
    created() {
        this.currentMonth = this.getCurrentMonth();
        this.initDate(this.firstDayOfMonth);
        fscreen.addEventListener('fullscreenchange', this.setfullScreenState, false);
    },
    beforeDestroy() {
        fscreen.removeEventListener('fullscreenerror', this.setfullScreenState);
    },
    methods: {
        delayLoad(value) {
            this.visibleIndex = value;
        },
        initDate(firstDayOfMonth) {
            // 当月最后一天
            const lastDayOfMonth = new Date(firstDayOfMonth.getFullYear(),
                firstDayOfMonth.getMonth() + 1, 0);
            // 当月第一天星期几
            const dayOfFirst = firstDayOfMonth.getDay();
            // 当月最后一天星期几
            const dayOfLast = lastDayOfMonth.getDay();
            // 日历起始日期 0点 时间戳
            const startDate = firstDayOfMonth.getTime() - (1000 * 60 * 60 * 24 * dayOfFirst);
            // 日历结束日期 23:59:59 时间戳
            const endDate = lastDayOfMonth.getTime()
                + ((1000 * 60 * 60 * 24 * ((6 - dayOfLast) + 1)) - 1);
            MyZoneModel.getPersonalTask({ startDate, endDate }).then((data) => {
                this.workDays = data.workDays;
                this.currentMonth = this.getCurrentMonth();
            });
        },
        changeDate(flag) {
            switch (flag) {
            case -1:
                this.firstDayOfMonth.setMonth(this.firstDayOfMonth.getMonth() - 1);
                break;
            case 0:
                this.firstDayOfMonth.setFullYear(this.today.getFullYear());
                this.firstDayOfMonth.setMonth(this.today.getMonth());
                break;
            case 1:
                this.firstDayOfMonth.setMonth(this.firstDayOfMonth.getMonth() + 1);
                break;
            default:
                break;
            }
            this.curMonth = this.firstDayOfMonth.getMonth();
            this.initDate(this.firstDayOfMonth);
        },
        getCurrentMonth() {
            const year = this.firstDayOfMonth.getFullYear();
            const month = this.firstDayOfMonth.getMonth() + 1;
            return `${year}年${month > 9 ? month : `0${month}`}月`;
        },
        setfullScreenState() {
            this.fullScreenState = fscreen.fullscreenElement
                && fscreen.fullscreenElement.fullScreenComponent === this.$el;
        },
        toggleScheduleFullscreen() {
            if (!this.fullScreenState) {
                document.body.fullScreenComponent = this.$el;
                fscreen.requestFullscreen(document.body);
            } else {
                fscreen.exitFullscreen();
                document.body.fullScreenComponent = undefined;
            }
        },
        // 顶部任务筛选
        currentWorkList(tasks) {
            return tasks.filter((task) => {
                // 1 未完成 2 已完成 0 全部
                if (this.taskStatus === 1) {
                    return task.personHour.status === 1;
                } if (this.taskStatus === 2) {
                    return task.personHour.status === 2;
                }
                return true;
            });
        },
        // 新标签打开卡片详情
        showCardDetail({
            cardId, sprintId, spaceKey, mode, taskSource, personHour,
        } = {}) {
            // 需求任务
            if (taskSource === 2) {
                this.$_routerOpen({
                    name: 'demandDetail',
                    params: {
                        id: personHour.demandId,
                    },
                });
                return;
            }
            // 项目任务
            if (taskSource === 3) {
                this.$_routerOpen({
                    name: 'jtaskDetail',
                    params: {
                        projectId: personHour.projectId,
                        taskId: personHour.id,
                    },
                });
                return;
            }
            if (!cardId || !spaceKey) {
                return;
            }
            if (mode === 1) {
                this.$_routerOpen({
                    name: 'generalCardDetail',
                    params: {
                        spaceKey,
                        cardId,
                    },
                });
                return;
            }
            this.$_routerOpen({
                name: 'teamspaceCardDetail',
                params: {
                    spaceKey,
                },
                query: {
                    cardId,
                    sprintId,
                },
            });
        },
    },
    filters: {
        displayTitle(task) {
            if (task.taskSource === 2 || task.taskSource === 3) {
                return task.personHour.content;
            }
            return task.cardName || task.personHour.content;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-schedule-box{
    & .jacp-schedule{
        padding: 0;
        &__fullscreen{
            z-index: 999 !important;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: #fff;
    }
        &-header{
            height: 40px;
            &__date{
                font-size: 14px;
                display: inline-block;
                margin: 0 32px;
                & i{
                    cursor: pointer;
                    &:hover{
                        color: #2695F1;
                    }
                }
            }
            & .fullBtn{
                float: right;
                right: 8px;
                top: 8px;
                cursor: pointer;
                color: #e1e1e1;
                &:hover{
                    color: #2695f1;
                }
            }
        }
        &-body{
            display: flex;
            flex-wrap: wrap;
            border-top: 1px solid #c5c5c5;
            border-left: 1px solid #c5c5c5;
            &__day{
                padding: 4px;
                border-bottom: 1px solid #c5c5c5;
                border-right: 1px solid #c5c5c5;
                min-height: 94px;
                min-width: 14%;
                flex: 1;
                font-size: 14px;
                font-family: PingFangSC-Regular, sans-serif;
                &.weekend{
                    background-color: #fafafa;
                }
                &.head-line{
                    height: 114px;
                }
                &.today{
                    box-sizing: border-box;
                    background:rgba(240,251,254,1);
                    border: 1px solid rgba(82,183,255,1);
                    color: #52B7FF;
                    margin: -1px 0 0 -1px;
                }
                & .week{
                    color: #999999;
                    height: 20px;
                    line-height: 20px;
                }
                & .date{
                    color: #666666;
                    height: 20px;
                    line-height: 20px;
                    font-weight: 999;
                }
                &.otherMonth .date{
                    color: #999999;
                    font-weight: 100;
                }
                & .task-list {
                    margin-bottom: 5px;
                    list-style: none;
                    & li{
                        height: 18px;
                        line-height: 18px;
                        font-size:12px;
                        border-radius:2px;
                        margin-top: 5px;
                        padding: 0 5px;
                        cursor: pointer;
                        &:hover{
                            box-shadow:0px 2px 6px 0px rgba(51,51,51,0.3);
                        }
                        &.finish{
                            color: #fff;
                            background: #60CFA8;
                        }
                        &.un-finish{
                            color: #fff;
                            background: #4FB7FF;
                        }
                        &.doing{
                            color: #fff;
                            background: #FF8D00;
                        }
                        & span{
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            display: inline-block;
                            width: 100%;
                        }
                        &.more{
                            padding: 0;
                            &:hover{
                                color: #4FB7FF;
                                border: 1px solid #4FB7FF;
                            }
                        }
                    }
                }
            }
        }
    }
    & .layout-card-content{
        padding: 0px 0px;
    }
}
.jacp-schedule-popper{
    border: none;
    padding: 0;
    margin-left: 6px;
    box-shadow: 0 12px 40px 6px rgba(0,0,0,.2);
}
</style>
