<template>
    <div
        class="jacp-plain-list-item"
        style="width: 100%"
    >
        <div class="jacp-plain-list-item-colunm left j-mgr24">
            <p class="j-text-overflow j-f-large jacp-plain-list-item__link">
                {{ data.processorContent }}
            </p>
        </div>
        <div class="jacp-plain-list-item-colunm right no-shrink light">
            <!-- <div
                class="j-mgr16"
                v-if="data.tags && data.tags.length"
            >
                <div>
                    <custom-tag
                        class="jacp-form-tags__item"
                        v-for="item in data.tags.slice(0,2)"
                        :key="item.id"
                        :color="item.color"
                    >
                        {{ item.name }}
                    </custom-tag>
                    <span v-if="data.tags.length > 2">...</span>
                </div>
            </div> -->
            <div
                class="j-mgr16"
                :title="data.cardName"
            >
                {{ data.cardName | ellipsis(20) }}
            </div>
            <div
                class="j-mgr16 jacp-plain-list-item__link"
                v-if="data.space"
                @click.stop.prevent="$_routerOpen({
                    name: data.space.mode !== 1 ?'teamspaceCardsList' : 'teamspaceGeneral',
                    params: { spaceKey: data.space.key }
                }, data.space.key)"
            >
                {{ data.space.name | ellipsis(12) }}
            </div>
            <div
                class="j-mgr16"
                v-if="data.plannedHours"
            >
                {{ data.plannedHours }} h
            </div>
            <div
                class="j-mgr16"
                v-if="data.startDate"
            >
                {{ data.startDate | jacp-local-time('YYYY-MM-DD') }}
                至 <span class="red-time">{{ data.deadline | jacp-local-time('YYYY-MM-DD') }}</span>
            </div>
            <!-- 编辑任务状态 -->
            <card-task-status
                v-model="data.status"
                @change-status="handleChangeStatus(data, $event)"
            />
        </div>
    </div>
</template>

<script>
import RouterMixin from '@/mixins/mixin.router';

export default {
    name: 'TaskLine',
    mixins: [RouterMixin],
    props: {
        data: Object,
    },
    filters: {
        ellipsis(val, length) {
            if (typeof val === 'undefined') {
                return '';
            }
            let value = val.toString().slice(0, length);
            value = val.length > length ? (value += '...') : value;
            return value;
        },
    },
    methods: {
        handleChangeStatus(data, $event) {
            this.$emit('update-status', data, $event);
        },
    },
};
</script>
