<template>
    <div class="jacp-schedule-detail">
        <div
            class="jacp-schedule-detail__header"
            :class="{'finish': task.personHour && task.personHour.status === 2}"
        >
            <p>{{ task.personHour.content }}</p>
        </div>
        <div class="jacp-schedule-detail__content">
            <div v-if="task.taskSource === 1">
                <i
                    class="el-icon-tickets"
                    title="卡片名称"
                />
                <ellipsis-text
                    :content="task.cardName"
                    :max-width="200"
                />
            </div>
            <div>
                <i
                    class="el-icon-date"
                    title="起止时间"
                />
                <span>
                    {{ task.personHour && task.personHour.startDate | dateFormat }}
                    至
                    {{ task.personHour && task.personHour.deadline | dateFormat }}</span>
            </div>
            <div>
                <i
                    class="icon-timer"
                    title="计划工时"
                /><span>{{ task.personHour && task.personHour.plannedHours }}h</span>
            </div>
        </div>
    </div>
</template>
<script>

export default {
    name: 'ScheduleDetail',
    props: {
        task: {
            type: Object,
            default: () => ({}),
        },
    },
    filters: {
        dateFormat(value) {
            const date = new Date(value);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}-${month > 9 ? month : `0${month}`}-${day > 9 ? day : `0${day}`}`;
        },
    },
};
</script>
<style lang="less">
.jacp-schedule-detail{
    height: 220px;
    &__header{
        height: 88px;
        background:linear-gradient(90deg,rgba(22,114,203,1) 0%,rgba(38,149,241,1) 100%);
        border-radius:4px 4px 0px 0px;
        padding: 24px 16px;
        & p{
            font-size:14px;
            font-weight:400;
            color:rgba(255,255,255,1);
            line-height:20px;
            overflow : hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            height: 40px;
            margin: 0;
        }
        &.finish{
            background:linear-gradient(90deg,rgba(52,180,135,1) 0%,rgba(96,207,168,1) 100%);
        }
    }
    &__content{
        padding: 24px 16px;
        font-size: 14px;
        color: #C5C5C5;
        & div{
            height: 32px;
            & span{
                font-size: 12px;
                padding-left: 10px;
                color: #666666;
            }
        }
    }
}
</style>
