<template>
    <layout-card
        :title="title"
        class="jacp-plain"
    >
        <el-select
            v-model="orderBy"
            placeholder="请选择"
            slot="extra"
            style="width: 150px;margin-right: -9px;"
        >
            <el-option
                v-for="item in orderByOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
        <el-tabs
            v-model="activeTab"
            class="jacp-plain-tabs"
        >
            <el-tab-pane
                :label="tabsLabel['todo'] + `(${data.todoTotal || 0})`"
                name="todo"
            />
            <el-tab-pane
                :label="tabsLabel['done'] + `(${data.doneTotal || 0})`"
                name="done"
            />
        </el-tabs>
        <plain-list
            :data="list"
            :current-page="currentPage"
            :page-size="data.size"
            :total="data.total"
            :height="510"
            @more="handleMoreClick"
            @on-item-click="handleItemClick"
            ref="list"
            v-if="activeTab === 'done'"
        >
            <template slot-scope="scope">
                <task-line
                    :data="scope.row"
                    @update-status="handleUpdate"
                />
            </template>
        </plain-list>
        <div
            class="jacp-plain-list"
            style="height: 510px"
            v-else
        >
            <div
                v-for="group in list"
                :key="group.name"
                v-show="!group.hide"
            >
                <h3>{{ group.name }}</h3>
                <ul>
                    <li
                        v-for="item in group.list"
                        :key="item.id"
                        :class="['jacp-plain-list-item',
                                 {'jacp-plain-list-item__content': group.name === '逾期', 'hide': item.hide}]"
                        @click="handleItemClick(item)"
                    >
                        <task-line
                            :data="item"
                            @update-status="handleUpdate"
                        />
                    </li>
                </ul>
            </div>
        </div>
    </layout-card>
</template>
<script type="text/javascript">
import CardTaskModel from '@/modules/card/models/CardTask';
import { MyZoneModel } from '@/models/myzone';
import mixinsLayoutViewWidgetList from '@/mixins/mixin.LayoutViewWidgetListExtra';
import event from '$platform.event';
import moment from 'moment';
import TaskLine from './taskLine';

export default {
    name: 'LayoutViewWidgetListCards',
    mixins: [mixinsLayoutViewWidgetList],
    components: {
        TaskLine,
    },
    props: {
        title: {
            default: '我的任务',
            type: String,
        },
    },
    data() {
        return {
            data: {
                todoTotal: 0,
                doneTotal: 0,
            },
            list: [],
            tabsLabel: {
                todo: this.$t('jacp.myzone.layoutViewWidgetListCards.todo'),
                done: this.$t('jacp.myzone.layoutViewWidgetListCards.done'),
            },
            searchCondition: {
                status: 1, // 1：处理中；2：完成
            },
            orderBy: 'ascByDeadline', // default
            doneOptions: [{
                value: 'descByStartDate',
                label: '开始日期由近及远',
            }, {
                value: 'ascByStartDate',
                label: '开始日期由远及近',
            }, {
                value: 'descByDeadline',
                label: '截止日期由近及远',
            }, {
                value: 'ascByDeadline',
                label: '截止日期由远及近',
            }],
            todoOptions: [{
                value: 'ascByStartDate',
                label: '开始日期由早至晚',
            }, {
                value: 'ascByDeadline',
                label: '截止日期由早至晚',
            }],
        };
    },
    mounted() {
        this.activeTab = this.searchCondition.status === 1 ? 'todo' : 'done';
    },
    computed: {
        orderByOptions() {
            if (this.activeTab === 'done') {
                return this.doneOptions;
            }
            return this.todoOptions;
        },
    },
    methods: {
        handleUpdate(item, status) {
            const { id, cardId } = item;
            item.status = status;
            let isRemoveable = false;
            switch (this.searchCondition.status) {
            case 1:
                if (status === 2) isRemoveable = true;
                break;
            case 2:
                if (status === 1 || status === 3) isRemoveable = true;
                break;
            default:
                break;
            }
            CardTaskModel.updateStatus({ id, cardId, status })
                .then(isRemoveable ? this.removeItem({ id, status }) : () => {})
                .catch(msg => this.$message.error(msg.join('<br>')));
        },
        handleItemClick({
            id, cardId, sprintId, space,
        }) {
            if (space && space.mode === 1) {
                this.openLink({
                    name: 'generalCardDetail',
                    params: {
                        spaceKey: space.key,
                        cardId,
                    },
                }, id);
                return;
            }
            // 跳转到详情
            // const detailUrl = `/teamspace/cardlist/${item.space.key}?cardId=${item.cardId}&planId=${item.sprintId}`;
            this.openLink({
                name: 'teamspaceCardDetail',
                params: {
                    spaceKey: space.key,
                },
                query: {
                    cardId,
                    sprintId,
                },
            }, id);
        },
        setData(data) {
            this.data = Object.assign({}, this.data, data);
            this.convertData(this.data.records);
            this.data.total = data[`${this.activeTab}Total`];
        },
        convertData(records) {
            if (this.activeTab !== 'todo') {
                this.list = records;
                return;
            }
            const dateTime = new Date(moment(new Date()).format('YYYY-MM-DD')).getTime();
            this.list = [{ name: '逾期', list: [] }, { name: '今天', list: [] },
                { name: '未来一周', list: [] }, { name: '以后', list: [] }];
            records.forEach((item) => {
                const startTime = new Date(moment(item.startDate).format('YYYY-MM-DD')).getTime();
                if (new Date(moment(item.deadline).format('YYYY-MM-DD')).getTime() < dateTime) {
                    this.list[0].list.push(item);
                } else if (startTime <= dateTime) {
                    this.list[1].list.push(item);
                } else if (startTime <= dateTime + 604800000) {
                    this.list[2].list.push(item);
                } else {
                    this.list[3].list.push(item);
                }
            });
            this.updateToDoList();
        },
        updateToDoList() {
            if (this.activeTab !== 'todo') {
                return;
            }
            this.list.forEach((item) => {
                if (item.list.filter(i => i.hide).length === item.list.length) {
                    Object.assign(item, { hide: true });
                }
            });
        },
        updateItem(params = {}) {
            this.data.records.forEach((item, index) => {
                if (item.id === params.id) {
                    // 结束编辑状态
                    // eslint-disable-next-line
                    delete item.editing;
                    this.data.records.splice(index, 1, Object.assign(item, params));
                }
            });
        },
        removeItem({ id, status }) {
            this.data.records.forEach((item, index) => {
                if (item.id === id) {
                    this.data.records.splice(index, 1, Object.assign(item, {
                        hide: true,
                    }));
                }
            });
            this.handleTotalCount(status);
            this.updateToDoList();
        },

        /* 处理一下tab上显示的总数，怪怪的。。 */
        handleTotalCount(status) {
            switch (Number(status)) {
            case 1:
            case 3:
                this.data.todoTotal += 1;
                if (this.data.doneTotal >= 1) {
                    this.data.doneTotal -= 1;
                }
                break;
            case 2:
                this.data.doneTotal += 1;
                if (this.data.todoTotal >= 1) {
                    this.data.todoTotal -= 1;
                }
                break;
            default:
                break;
            }
            event.$emit('on-total-change', {
                todo: this.data.todoTotal,
                done: this.data.doneTotal,
            });
        },
        getList() {
            const statusListParam = this.searchCondition.status === 1
                ? { statusList: [1, 3] }
                : { statusList: [2] };
            const orderField = this.orderBy.endsWith('Deadline') ? 'deadline' : 'startDate';
            const orderType = this.orderBy.startsWith('desc') ? 'desc' : 'asc';
            const params = Object.assign({},
                statusListParam, {
                    currentPage: this.currentPage,
                    orderType,
                    orderField,
                });

            this.list = [];
            return MyZoneModel.getCardsList(params)
                .then(this.setData);
        },
    },
    watch: {
        activeTab: {
            handler(val) {
                this.orderBy = val === 'todo' ? 'ascByDeadline' : 'descByStartDate';
                this.resetList();
                this.$nextTick(() => {
                    this.searchCondition.status = val === 'todo' ? 1 : 2;
                    this.data.total = this.data[`${val}Total`];
                });
            },
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-plain-list{
    &-item{
        &__content .red-time {
            color: @redColor;
        }
    }
}
</style>
