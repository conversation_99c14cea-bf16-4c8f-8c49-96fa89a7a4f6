<template>
    <div class="jacp-schedule-more">
        <div class="jacp-schedule-more__header">
            <p class="date">
                {{ date | dateFormat }}
            </p>
            <p class="week">
                {{ date | week }}
            </p>
        </div>
        <div class="jacp-schedule-more__content">
            <ul>
                <li
                    v-for="task in tasks"
                    :key="task.personHour.id"
                    :class="{'finish': task.personHour.status === 2}"
                    @click="$emit('showDetal', task)"
                >
                    <el-popover
                        placement="right"
                        width="264"
                        :open-delay="300"
                        popper-class="jacp-schedule-popper"
                        trigger="hover"
                    >
                        <ScheduleDetail :task="task" />
                        <span slot="reference">{{ task | displayTitle }}</span>
                    </el-popover>
                </li>
            </ul>
        </div>
    </div>
</template>
<script>
import ScheduleDetail from './scheduleDetail';

export default {
    name: 'ScheduleMore',
    components: {
        ScheduleDetail,
    },
    props: {
        tasks: {
            type: Array,
            default: () => ([]),
        },
        date: {
            type: Number,
        },
    },
    data() {
        return {
        };
    },
    filters: {
        week(value) {
            return ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][new Date(value).getDay()];
        },
        dateFormat(value) {
            const date = new Date(value);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}-${month > 9 ? month : `0${month}`}-${day > 9 ? day : `0${day}`}`;
        },
        displayTitle(task) {
            if (task.taskSource === 2 || task.taskSource === 3) {
                return task.personHour.content;
            }
            return task.cardName || task.personHour.content;
        },
    },
};
</script>
<style lang="less">
.jacp-schedule-more{
    padding: 16px;
    &__header{
        & p{
            font-family: PingFangSC-Medium;
            line-height:20px;
            height: 20px;
            margin: 0;
            &.date{
                font-size: 16px;
                color: #333333;
            }
            &.week{
                font-size: 14px;
                color: #999999;
            }
        }
        &.finish{
            background:linear-gradient(90deg,rgba(52,180,135,1) 0%,rgba(96,207,168,1) 100%);
        }
    }
    &__content{
        margin-top: 8px;
        & ul{
            height: 130px;
            overflow-y: auto;
            list-style: none;
            & li{
                height: 18px;
                line-height: 18px;
                font-size: 12px;
                padding-left: 10px;
                color: #ffffff;
                background-color: #4FB7FF;
                margin-top: 2px;
                border-radius: 2px;
                cursor: pointer;
                &.finish{
                    background-color: #60CFA8;
                }
                & span{
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    display: inline-block;
                    width: 100%;
                }
            }
        }
    }
}
</style>
