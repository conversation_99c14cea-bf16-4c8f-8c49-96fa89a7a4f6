<template>
    <ul class="space-recents teamspace-spacelist-group">
        <li class="teamspace-spacelist-group__title">
            <jacp-text
                type="disable"
                size="12"
            >
                最近访问
            </jacp-text>
        </li>
        <li
            class="teamspace-spacelist-group__item"
            v-for="item in filteredList"
            :key="item.key"
            @click="$router.push({
                name: item.mode === 1 ? 'teamspaceGeneral' :'teamspaceCardsList',
                params: { spaceKey: item.key } })"
        >
            <jacp-user-avatar
                :data="item"
                :size="24"
                first-name
                avatar
            />
            <div class="j-text-overflow">
                <jacp-text
                    class="j-mgl8"
                >
                    {{ item.name }}
                </jacp-text>
            </div>
        </li>
    </ul>
</template>
<script>
import sortBy from 'lodash/sortBy';
import { mapState } from 'vuex';

export default {
    name: 'SpaceRecents',
    props: {
        maxLength: { type: Number, default: 5 },
    },
    computed: {
        ...mapState('chilldteamspace', ['spaceList', 'recentSpaceList']),
        filteredList() {
            const { spaceList = [], recentSpaceList = [] } = this;
            return sortBy(
                spaceList.filter(space => recentSpaceList.includes(space.key)),
                space => recentSpaceList.indexOf(space.key),
            );
        },
    },
};
</script>
<style lang="less">
.space-recents{
  .teamspace-spacelist-group__item{
    justify-content: flex-start;
    cursor: pointer;
    .jacp-user-avatar{
      min-width: 24px;
    }
  }
}
</style>
