
// 团队空间顶部导航和menu的对接层
import {
    MenuItem,
} from '@/modules/root/models/menu';

export const createSpaceAddonId = addon => `teamspaceAddon-${addon.app.appCode}-${addon.meta.type}-${addon.id}`;
export const addonAdapter = (parentRouteName = 'teamspaceAddon') => (addon) => {
    let menuItem;
    switch (addon.meta.type) {
    case 'path':
    case 'route':
        menuItem = new MenuItem({
            id: createSpaceAddonId(addon),
            name: addon.name,
            clickAction: 'jacpClickHandle',
            clickParams: {
                url: addon.meta.value,
                menuData: {},
            },
        });
        break;
    case 'extension':
    case 'iframe':
        menuItem = new MenuItem({
            id: createSpaceAddonId(addon),
            name: addon.name,
            clickAction: 'jacpClickHandle',
            clickParams: {
                url: parentRouteName,
                menuData: addon.app,
            },
        });
        break;
    default: break;
    }
    return menuItem;
};

export function removeAddonsFromMenu(addons = []) {
    addons.forEach((addon) => {
        const targetMenuItem = this.find(createSpaceAddonId(addon));
        if (targetMenuItem) {
            this.remove(targetMenuItem);
        }
    });
}

export function addAddonsToMenu(addons = []) {
    // const filteredAddons = addons.filter(addon => spaceTabExtensionPoint.isEqual(addon.extensionPoint?.id));
    const adapter = addonAdapter('teamspaceAddon');
    if (addons.length) {
        const menuItems = addons.map(adapter);
        this.add(menuItems);
    }
}
