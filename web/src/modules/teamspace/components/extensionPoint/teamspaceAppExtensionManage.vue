<script>

/* 团队空间管理自己挂载点插件的dialog内容, 包裹一下当前挂载点特殊的界面内容 */
import LocalAppExtensionManage from '@/modules/root/components/appExtension/manage';

const tips = '空间管理员有权限「开启/关闭」某应用。此操作会对空间所有成员生效。';
export default {
    inheritAttrs: false,
    props: LocalAppExtensionManage.props,
    render() {
        return <div>
            <el-alert
                title={tips}
                type="warning"></el-alert>
            <LocalAppExtensionManage {...{
                props: { ...this.$props, disabledText: tips },
            }}></LocalAppExtensionManage>
        </div>;
    },
};
</script>
