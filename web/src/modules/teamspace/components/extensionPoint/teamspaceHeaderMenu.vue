<template>
    <el-row
        class="teamspace-header-menu"
        type="flex"
        v-if="menu"
    >
        <LocalTabsMenu
            :menu="menu"
        />
        <el-button
            v-if="!disabledAdd"
            class="teamspace-header-menu__button"
            icon="el-icon-plus"
            circle
            @click="showAppManage"
        />
    </el-row>
</template>
<script>
import Vue from 'vue';
import Dialog from '@/models/dialog';
import LocalTabsMenu from '@/modules/root/components/tabsMenu';
import { openLink } from '@/plugins/utils';
import {
    creatRouteMenu,
    clearMenuItem,
    jacpMenuItemAdapter,
} from '@/modules/root/models/menu';
import {
    loadSpaceAppExtension, loadSpaceAddons, spaceTabExtensionPoint,
    setSpaceAppExtentionState,
} from '@/models/app';
import Manage from './teamspaceAppExtensionManage';
import { removeAddonsFromMenu, addAddonsToMenu } from './addonAdapter';

const defaultMenus = [{
    code: 'teamspaceSpaceIntro',
    label: '空间概览',
}, {
    code: 'teamspaceBacklog',
    label: 'Backlog',
}, {
    code: 'teamspaceCardsList',
    label: '迭代',
}, {
    code: 'teamspaceCalender',
    label: '人力资源日历',
}, /* , {
    code: 'teamspaceSpaceSetting',
    label: '设置',
} */];
const generalMenus = [{
    code: 'generalCardList',
    label: '计划&跟踪',
}, {
    code: 'generalCalender',
    label: '人力资源日历',
}, /* , {
    code: 'generalSpaceSetting',
    label: '设置',
} */];
const teamspaceHeaderMenu = creatRouteMenu();
export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['isGeneral', 'spaceKey', 'spacePrivilage'], //  isGeneral需要逐渐被废弃
    components: { LocalTabsMenu },
    data() {
        return {
            spaceTabExtensionPoint,
            // 初始值
            menu: teamspaceHeaderMenu, //
        };
    },
    computed: {
        extensionPointContextParams() {
            return {
                // TODO: baseURL
                space: Object.freeze(this.currentSpace),
                permission: Object.freeze(this.spacePrivilage),
            };
        },
        disabledAdd() {
            return this.isGeneral;
        },
    },
    methods: {
        initMenu() {
            clearMenuItem.call(this.menu);
            const menus = this.isGeneral ? generalMenus : defaultMenus;
            const initialMenus = menus.map(menu => jacpMenuItemAdapter({
                ...menu,
                url: menu.code,
            }));
            this.menu.add(initialMenus);
            if (!this.disabledAdd) {
                loadSpaceAddons(spaceTabExtensionPoint, this.spaceKey)
                    .then((addons => addAddonsToMenu.call(this.menu, addons)));
            }
        },
        async showAppManage() {
            const { spaceKey, spacePrivilage } = this;
            const appExtensions = await loadSpaceAppExtension(this.spaceKey);
            Dialog.confirm({
                title: '团队空间应用',
                width: '840px',
                slot: Manage,
                confirmDisplay: false,
                cancelBtnText: '关闭',
                slotProps: {
                    data: Vue.observable(appExtensions),
                    // 管理员能操作
                    disabled: !(spacePrivilage && spacePrivilage.isAdmin),
                    onChange: (appExtension = {}, prop, newValue) => setSpaceAppExtentionState(spaceKey, appExtension, newValue)
                        .then(() => {
                            this.$message.success({
                                message: '保存成功！',
                                type: 'success',
                                showClose: true,
                                offset: 48,
                                duration: 2000,
                            });
                            if (newValue) {
                                const filteredAddons = (appExtension.addons || [])
                                    .filter(addon => spaceTabExtensionPoint.isEqual(addon.extensionPoint?.id));
                                addAddonsToMenu.call(this.menu, filteredAddons);
                            } else {
                                removeAddonsFromMenu.call(this.menu, appExtension.addons);
                            }
                        }),
                    onClick: (appExtension) => {
                        this.showAppExtentionDetail(appExtension);
                    },
                },
            });
        },
        showAppExtentionDetail(appExtension = {}) {
            openLink({
                route: {
                    name: 'jacpAppDetail',
                    params: {
                        code: appExtension.appCode,
                    },
                    query: {
                        ex: 1,
                    },
                },
                isPopup: true,
            });
        },
    },
    watch: {
        spaceKey: {
            immediate: true,
            handler: 'initMenu',
        },
    },
};
</script>
<style lang="less">
.teamspace-header-menu{
    position: relative;

    .jacp-tabs-menu--horizontal{
        display:flex;
    }
    &__button.el-button{
        border: none;
        font-size: var(--font-size--subtitle);
        background: transparent;
    }
}

</style>
