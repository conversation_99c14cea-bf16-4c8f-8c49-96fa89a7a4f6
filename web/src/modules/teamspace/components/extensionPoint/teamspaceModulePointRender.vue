<script>
import { mapGetters } from 'vuex';
import {
    loadSpaceAddons, spaceTabExtensionPoint,
} from '@/models/app';
import ModulePointRender from '@/modules/root/components/modulePointRender';
// 团队空间顶部导航的挂载点
export default {
    name: 'JacpTeamspaceModulePointRender',
    inheritAttrs: false,
    components: { ModulePointRender },
    data() {
        return {
            addon: null,
        };
    },
    mounted() {
        this.initAddon(this.$route);
    },
    computed: {
        ...mapGetters('chilldteamspace', ['currentSpace']),
    },
    methods: {
        async initAddon(route) {
            const { query, params } = route;
            const addons = await loadSpaceAddons(spaceTabExtensionPoint, params.spaceKey);
            const targetAddon = query.id
                ? addons.find(addon => addon.id === query.id)
                : addons.find(addon => addon.app.appCode === params.appCode);
            this.addon = targetAddon;
        },
    },
    render() {
        return this.addon ? <ModulePointRender {...{
            props: {
                ...this.$attrs,
                contextParams: spaceTabExtensionPoint.contextGenerator(this.currentSpace),
            },
        }} addon={this.addon} ></ModulePointRender> : <div>加载中...</div>;
    },
    watch: {
        $route: {
            handler(val) {
                this.initAddon(val);
            },
        },
    },
};
</script>
