<template>
    <el-form
        v-bind="$attrs"
        v-on="$listeners"
        label-position="top"
        class="j-form"
        ref="form"
    >
        <el-form-item
            prop="optErp"
            label="人员"
        >
            <user-selector
                v-model="value.optErp"
                :space-members="memberList"
                placeholder-text="请选择人员"
                clearable
            />
        </el-form-item>
        <!-- type: 可选值["card","task","sprint","space"] -->
        <el-form-item
            prop="type"
            label="类型"
        >
            <jacp-input-select
                placeholder="请选择类型"
                v-model="value.type"
                :data="spaceHistoryType"
                clearable
            />
        </el-form-item>
        <el-form-item
            prop="optRange"
            label="搜索范围"
        >
            <jacp-input-select
                placeholder="请选择搜索范围"
                v-model="value.optRange"
                :data="filterdSpaceHistoryOprationType"
                clearable
            />
        </el-form-item>
        <el-form-item
            prop="dateRange"
            label="日期"
        >
            <el-date-picker
                v-model="value.dateRange"
                class="j-datepicker--small"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                value-format="timestamp"
                clearable
            />
        </el-form-item>
        <div style="width: 100%; text-align: right;padding-top: 16px;">
            <el-button
                type="primary"
                @click="$emit('submit', value)"
            >
                确认
            </el-button>
        </div>
    </el-form>
</template>
<script>
import UserSelector from '@/modules/card/components/userSelector';
import { spaceHistoryType, spaceHistoryOprationType } from '$module/constant';

export default {
    inheritAttrs: true,
    name: 'QuerySpaceHistoryForm',
    components: { UserSelector },
    props: {
        memberList: { type: Array, default: () => [] },
        value: { type: Object, default: () => ({}) },
    },
    data() {
        return { spaceHistoryType, spaceHistoryOprationType };
    },
    computed: {
        filterdSpaceHistoryOprationType() {
            if (this.value && this.value.type === 'space') {
                return this.spaceHistoryOprationType.filter(o => o.id !== 'statusUpdate');
            }
            return this.spaceHistoryOprationType;
        },
    },
    methods: {
        filterMethod(keyword) {
            return this.memberList.filter((user = {}) => user.erp.includes(keyword) || user.name.includes(keyword));
        },
        resetForm() {
            this.$refs.form.resetFields();
        },
    },
    watch: {
        'value.type': {
            handler() {
                if (!this.value) {
                    return;
                }
                // fixed: 团队空间类型时  应该没有 状态变更
                if (this.value.type === 'space' && this.value.optRange === 'statusUpdate') {
                    this.value.optRange = '';
                }
            },
        },
    },
};
</script>
<style lang="less">
.j-datepicker--small{
  .el-range-separator{
    min-width: 20px;
  }
}
</style>
