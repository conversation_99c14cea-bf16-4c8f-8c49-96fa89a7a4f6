<template>
    <div class="user-search">
        <input
            type="text"
            v-model="searchStr"
            @input="$emit('filter', searchStr)"
            placeholder="输入工号/姓名"
        >
        <jacp-icon
            v-show="!searchStr"
            class="user-search__searchicon"
            name="icon-search"
            :size="16"
        />
        <jacp-icon
            v-show="searchStr"
            class="user-search__clearicon"
            @click.native="resetSearchString()"
            name="icon-close2"
            :size="16"
        />
    </div>
</template>

<script type="text/javascript">
export default {
    data() {
        return {
            searchStr: '',
        };
    },
    methods: {
        resetSearchString() {
            this.searchStr = '';
            this.$emit('filter', '');
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.user-search{
    display: inline-block;
    width: 120px;
    overflow: hidden;
    position: relative;

    &__clearicon,
    &__searchicon{
        position: absolute;
        right: 0;
        top: 0;
        color: #999;
    }

    &__clearicon{
        cursor: pointer;
    }
}
.user-search input{
    display: inline-block;
    width: 100%;
    outline: none;
    border: none;
    border-bottom: 1px solid #ddd;

    &:hover,
    &:focus{
        border-color: @primaryColor;
    }
}
</style>
