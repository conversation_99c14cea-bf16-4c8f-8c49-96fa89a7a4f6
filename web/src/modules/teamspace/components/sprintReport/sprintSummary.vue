<template>
    <layout-block
        :title="title"
        :tip-content="tipContent"
        :sub-title="subTitle"
        height="23.2"
    >
        <ci-highchart
            :option="options"
            :data="series"
        />
    </layout-block>
</template>

<script>
import moment from 'moment';

const options = {
    chart: {
        type: 'solidgauge',
    },
    title: {
        text: '完成率',
        style: {
            fontSize: '10px',
            color: '#999999',
        },
        verticalAlign: 'middle',
        y: 35,
    },
    exporting: {
        enabled: false,
    },
    pane: {
        startAngle: 0,
        endAngle: 360,
        background: [{
            outerRadius: '100%',
            innerRadius: '90%',
            backgroundColor: '#D0E1FC',
            borderWidth: 0,
        }],
    },
    yAxis: {
        min: 0,
        max: 100,
        lineWidth: 0,
        tickPositions: [],
    },
    plotOptions: {
        solidgauge: {
            borderWidth: '8px',
            dataLabels: {
                borderWidth: 0,
                y: -35,
                style: {
                    fontSize: '35px',
                    color: '#407FE7',
                },
                format: '{y}%',
            },
        },
    },
    credits: {
        enabled: false,
    },
    series: [],
};

const series = [{
    name: '完成率',
    borderColor: '#407FE7',
    data: [{
        radius: '95%',
        innerRadius: '95%',
        y: 0,
        rate: 0,
    }],
}];

export default {
    name: 'SprintSummary',
    props: {
        value: {
            type: Number,
            default: 0,
        },
        sprint: {
            type: Object,
            default: undefined,
        },
    },
    data() {
        return {
            title: '迭代汇总',
            tipContent: ' 已完成的任务数/总的任务数*100%',
            options,
            series,
        };
    },
    methods: {
        summaryData() {
            const seriesCopy = JSON.parse(JSON.stringify(series));
            Object.assign(seriesCopy[0].data[0], { y: this.value });
            return seriesCopy;
        },
    },
    computed: {
        subTitle() {
            if (!this.sprint) {
                return '';
            }
            const startDate = moment(this.sprint.startDate).format('YYYY-MM-DD');
            const endDate = moment(this.sprint.endDate).format('YYYY-MM-DD');
            return `${startDate}至${endDate}`;
        },
    },
    watch: {
        value: {
            immediate: true,
            handler: function refresh() {
                this.series = this.summaryData();
            },
        },
    },
};
</script>
