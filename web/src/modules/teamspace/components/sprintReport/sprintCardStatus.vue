<template>
    <layout-block
        :title="title"
        :tip-content="tipContent"
        :display-fullscreen="false"
        height="26"
    >
        <ci-highchart
            :option="options"
            :data="series"
            v-if="!empty"
        />
        <jacp-empty
            v-else
            position="left"
        />
    </layout-block>
</template>

<script>
const options = {
    chart: {
        type: 'column',
    },
    title: {
        text: '',
    },
    xAxis: {
        type: 'category',
    },
    yAxis: {
        title: '',
    },
    tooltip: {
        headerFormat: '{point.key}：{point.y}',
        pointFormat: '',
    },
    legend: {
        enabled: false,
    },
    exporting: {
        enabled: false,
    },
    series: [],
};

const series = [{
    data: [],
    color: '#50B7FF',
    dataLabels: {
        enabled: true,
    },
}];
export default {
    name: 'SprintCardStatus',
    props: {
        value: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            title: '卡片状态图',
            tipContent: '展示各泳道下的卡片数量（包含父子卡片）',
            options,
            series,
        };
    },
    methods: {
        summaryData() {
            const seriesCopy = JSON.parse(JSON.stringify(series));
            seriesCopy[0].data = this.value;
            return seriesCopy;
        },
    },
    computed: {
        empty() {
            return !this.series[0].data.length;
        },
    },
    watch: {
        value: {
            immediate: true,
            handler: function refresh() {
                this.series = this.summaryData();
            },
        },
    },
};
</script>

<style scoped>

</style>
