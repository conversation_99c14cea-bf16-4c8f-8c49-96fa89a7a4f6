<template>
    <layout-block :title="title">
        <el-table
            :data="value"
            height="600"
            :header-cell-style="{ backgroundColor: '#FAFAFA'}"
            border
        >
            <el-table-column label="成员名称">
                <template slot-scope="scope">
                    <span style="color: #333333">
                        {{ scope.row.memberName }}（{{ scope.row.memberErp }})
                    </span><br>
                    <span style="color: #999999">{{ scope.row.memberOrgName }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="taskUnfinishCount"
                label="未完成任务数"
                align="center"
                sortable
            />
            <el-table-column
                prop="taskFinishCount"
                label="已完成任务数"
                align="center"
                sortable
            />
            <el-table-column
                prop="plannedHourSum"
                label="计划工时"
                align="center"
                sortable
            />
            <el-table-column
                prop="remainingHourSum"
                label="剩余工时"
                align="center"
                sortable
            />
            <el-table-column
                prop="taskFinishRate"
                label="完成率"
                align="center"
                sortable
                :formatter="formatter"
            />
        </el-table>
    </layout-block>
</template>

<script>
export default {
    name: 'SprintMembersData',
    props: {
        value: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            title: '成员卡片任务数&工时数',
        };
    },
    methods: {
        formatter(row) {
            return `${row.taskFinishRate}%`;
        },
    },
};
</script>
