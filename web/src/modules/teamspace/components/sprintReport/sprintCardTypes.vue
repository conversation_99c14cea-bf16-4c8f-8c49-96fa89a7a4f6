<template>
    <layout-block
        :title="title"
        :display-fullscreen="false"
        height="26"
    >
        <ci-highchart
            :option="options"
            :data="series"
            v-if="!empty"
        />
        <jacp-empty
            v-else
            position="left"
        />
    </layout-block>
</template>

<script>

const options = {
    chart: {
        type: 'pie',
    },
    title: {
        text: '',
    },
    exporting: {
        enabled: false,
    },
    plotOptions: {
        pie: {
            dataLabels: {
                enabled: false,
            },
            showInLegend: true,
        },
    },
    legend: {
        layout: 'vertical',
        align: 'right',
        verticalAlign: 'top',
        symbolRadius: 0,
        itemMarginTop: 15,
        x: -90,
    },
    series: [],
};

const colorLine = {
    BUG: '#F8766A',
    任务: '#FEB863',
    产品需求: '#50B7FF',
    研发需求: '#00E4AB',
};

const series = [{
    innerSize: '90%',
    name: '卡片类型',
    data: [],
}];

export default {
    name: 'SprintCardTypes',
    props: {
        value: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            title: '卡片类型',
            options,
            series,
        };
    },
    methods: {
        cardTypeData() {
            const data = [];
            let sum = 0;
            this.value.forEach((e) => {
                data.push({
                    name: e.name, y: e.count, rate: e.rate, color: colorLine[e.name],
                });
                sum += e.count;
            });
            const seriesCopy = JSON.parse(JSON.stringify(series));
            if (sum > 0) { // 排除由于类型设置变动，倒至匹配卡片sum为0的情况
                seriesCopy[0].data = data;
            }
            return seriesCopy;
        },
        optionsData() {
            const optionsCopy = { ...options };
            const legend = {
                labelFormat: '<div style="color: #666666;font-weight: normal">{name}'
                    + '<span style="margin-left: 15px;">{y}个</span><span style="margin-left: 15px">占比&nbsp;{rate}%</span></div>',
                useHTML: true,
            };
            Object.assign(optionsCopy.legend, legend);
            return optionsCopy;
        },
    },
    computed: {
        empty() {
            return !this.series[0].data.length;
        },
    },
    watch: {
        value: {
            immediate: true,
            handler: function refresh() {
                this.options = this.optionsData();
                this.series = this.cardTypeData();
            },
        },
    },
};
</script>

<style scoped>

</style>
