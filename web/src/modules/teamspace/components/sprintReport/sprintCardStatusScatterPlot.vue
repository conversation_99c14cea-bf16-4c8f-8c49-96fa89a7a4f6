<template>
    <layout-block
        :title="title"
        :tip-content="tipContent"
        :display-fullscreen="false"
        height="26"
    >
        <div slot="extra">
            <el-select
                v-model="selectStatus"
                multiple
                collapse-tags
                @change="loadScatterPlot"
                style="position: relative; top: -27px; margin-left: 250px;"
            >
                <!-- 完成7 挂起8 不可选 -->
                <el-option
                    v-for="item in statusList"
                    :key="item.statusCode"
                    :value="item.statusName"
                    :lable="item.statusName"
                    :disabled="item.statusCode === 7 || item.statusCode === 8"
                />
            </el-select>
        </div>
        <ci-highchart
            :option="options"
            :data="burndata"
            v-if="!empty"
        />
        <jacp-empty
            v-else
            position="left"
        />
    </layout-block>
</template>

<script>
import moment from 'moment';
import SprintModel from '@/models/plan';
import { space as SpaceModel } from '@/models/teamspace';

const options = {
    title: {
        text: '',
    },
    tooltip: {
        xDateFormat: '%Y-%m-%d',
        crosshairs: true,
        shared: true,
        useHTML: true,
        formatter() {
            let s = `<span style="font-size: 10px">${moment(this.key || this.x).format('YYYY-MM-DD')}</span><br/>`;
            if (this.points) {
                this.points.forEach((item) => {
                    s += `<span style="color:${item.color}">\u25CF</span> ${item.series.name}: <b>${(item.point.y / 3600000).toFixed(1)}H</b><br/>`;
                });
            }
            if (this.point) {
                s += `<span style="color:${this.point.color}">\u25CF</span> ${this.series.name}: <b>${(this.y / 3600000).toFixed(1)}H</b><br/>`;
            }
            return s;
        },
    },
    credits: {
        enabled: false,
    },
    plotOptions: {
        scatter: {
            marker: {
                radius: 3,
            },
        },
        line: {
            marker: {
                radius: 2,
            },
        },
    },
    exporting: {
        enabled: false,
    },
    yAxis: {
        title: {
            text: null,
        },
        labels: {
            formatter() {
                return `${(this.value / 3600000).toFixed(1)}H`;
            },
        },
        lineWidth: 2,
    },
    xAxis: {
        type: 'datetime',
        tickInterval: 24 * 3600 * 1000,
        labels: {
            format: '{value:%m-%d}',
        },
    },
};

export default {
    name: 'SprintCardStatusScatterPlot',
    props: {
        sprint: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            title: '卡片状态停留时长散点图',
            tipContent: '已完成卡片在所选状态下的停留时长散点图、总平均停留时长、每天平均停留时长',
            statusList: [],
            selectStatus: [],
            options,
            series: [],
        };
    },
    methods: {
        load() {
            if (!this.sprint.id) {
                return;
            }
            SpaceModel.getAvailableStatusList({ spaceId: this.sprint.spaceId }).then((data) => {
                this.statusList = data;
                this.selectStatus = [];
                data.forEach((item) => {
                    if (!(item.statusCode === 7 || item.statusCode === 8)) {
                        this.selectStatus.push(item.statusName);
                    }
                });
                this.loadScatterPlot();
            });
        },
        loadScatterPlot() {
            if (this.selectStatus.length <= 0) {
                return;
            }
            const param = Object.assign({}, { filters: this.buildFilters() });
            SprintModel.getCardStatusScatterPlot(param).then((data = []) => {
                if (data.length === 3) {
                    Object.assign(data[1], { color: '#999999' });
                    Object.assign(data[2], { color: '#00E4AB' });
                    this.series = data;
                }
            });
        },
        buildFilters() {
            return [
                { column: 'card.stayTime.status.stayTime', type: 'nested', value: this.selectStatus },
                { column: 'snapDate', type: 'term', value: moment.utc(new Date().getTime()).format('YYYY-MM-DD') },
                { column: 'card.space.id', type: 'term', value: this.sprint.spaceId },
                { column: 'card.sprint.id', type: 'term', value: this.sprint.id },
            ];
        },
    },
    computed: {
        burndata() {
            return JSON.parse(JSON.stringify(this.series));
        },
        empty() {
            return this.series.length <= 0;
        },
    },
    watch: {
        'sprint.id': {
            handler() {
                this.series = [];
                this.load();
            },
        },
    },
};
</script>
