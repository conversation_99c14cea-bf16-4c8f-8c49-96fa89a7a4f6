<template>
    <layout-block
        :title="title"
        :tip-content="tipContent"
        :display-fullscreen="false"
        height="26"
    >
        <ci-highchart
            :option="options"
            :data="burndata"
            v-if="!empty"
        />
        <jacp-empty
            v-else
            position="left"
        />
    </layout-block>
</template>
<script>
import PlanModel from '@/models/plan';

const options = {
    chart: {
        type: 'spline',
        zoomType: 'x',
    },
    title: {
        text: '',
    },
    tooltip: {
        xDateFormat: '%Y-%m-%d',
        crosshairs: true,
        shared: true,
    },
    credits: {
        enabled: false,
    },
    plotOptions: {
        spline: {
            marker: {
                radius: 2,
                lineColor: '#666666',
                lineWidth: 1,
            },
        },
    },
    exporting: {
        enabled: false,
    },
    yAxis: {
        title: {
            enabled: false,
        },
        labels: {
            formatter() {
                return `${this.value}`;
            },
        },
        lineWidth: 2,
    },
    xAxis: {
        type: 'datetime',
        tickInterval: 24 * 3600 * 1000,
        labels: {
            format: '{value:%m-%d}',
        },
    },
};

const series = [
    {
        name: '完成卡片',
        color: '#00E4AB',
        data: [],
        zoneAxis: 'x',
    },
    {
        name: '新增卡片',
        color: '#50B7FF',
        data: [],
        zoneAxis: 'x',
    },
];
export default {
    props: {
        sprint: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            title: '新增卡片与完成卡片',
            tipContent: '在迭代时间范围内，展示每天新增卡片数量和完成的卡片数量',
            options,
            series,
        };
    },
    created() {
        this.load(this.sprint.id);
    },
    methods: {
        roundDay(timestamp) {
            return 3600 * 24 * Math.round((timestamp / (3600 * 24)));
        },
        load(planId) {
            if (!planId) {
                return;
            }
            PlanModel.getCardBurndown(planId).then((data) => {
                const startDate = this.roundDay(this.sprint.startDate);
                const endDate = this.roundDay(this.sprint.endDate);
                const optionsCopy = { ...options };
                if (data.xAxisData) {
                    const breakSize = 24 * 3600 * 1000;
                    const breaks = [];
                    let tempFrom = this.roundDay(data.xAxisData[0]);
                    for (let i = 1; i < data.xAxisData.length; i += 1) {
                        const tempTo = this.roundDay(data.xAxisData[i]);
                        if (tempTo <= startDate) {
                            breaks.push({ from: tempFrom, to: tempTo - 1, breakSize });
                        } else if (tempTo > endDate) {
                            breaks.push({ from: tempFrom, to: tempTo - 1, breakSize });
                        }
                        tempFrom = tempTo;
                    }
                    Object.assign(optionsCopy.xAxis, {
                        min: this.roundDay(data.xAxisData[0]),
                        max: this.roundDay(data.xAxisData[data.xAxisData.length - 1]),
                        breaks,
                    });
                }

                this.options = optionsCopy;
                const zones = [
                    { value: startDate, dashStyle: 'dot' },
                    { value: endDate },
                    { dashStyle: 'dot' },
                ];
                const seriesCopy = JSON.parse(JSON.stringify(series));
                if (data.finishCardList) {
                    const finish = data.finishCardList.map(e => [this.roundDay(e.date), e.count]);
                    seriesCopy[0].data = finish;
                    seriesCopy[0].zones = zones;
                }
                if (data.createCardList) {
                    const create = data.createCardList.map(e => [this.roundDay(e.date), e.count]);
                    seriesCopy[1].data = create;
                    seriesCopy[1].zones = zones;
                }
                this.series = seriesCopy;
            });
        },
    },
    computed: {
        burndata() {
            return JSON.parse(JSON.stringify(this.series));
        },
        empty() {
            return !(this.series[0].data.length > 0 || this.series[1].data.length > 0);
        },
    },
    watch: {
        'sprint.id': {
            handler(n) {
                this.series = series;
                this.load(n);
            },
        },
    },
};
</script>
