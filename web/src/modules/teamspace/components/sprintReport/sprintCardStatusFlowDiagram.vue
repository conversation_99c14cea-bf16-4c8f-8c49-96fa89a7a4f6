<template>
    <layout-block
        :title="title"
        :tip-content="tipContent"
        :display-fullscreen="false"
        height="26"
    >
        <ci-highchart
            :option="options"
            :data="burndata"
            v-if="!empty"
        />
        <jacp-empty
            v-else
            position="left"
        />
    </layout-block>
</template>

<script>
import moment from 'moment';
import PlanModel from '@/models/plan';

const timeMap = new Map();
const axis = {
    cardStatusPane: {
        x: { column: 'snapDate', orders: [] },
        y: { column: 'card.status.code', func: 'count', orders: [] },
    },
};
const formatter = function getPointFormatter() {
    let s = `<b>${this.x}</b><table><tr><th>状态</th><th>卡片数量</th><th>CycleTime（小时）</th></tr>`;
    this.points.forEach((item, i) => {
        s += `<tr><td><span style="color:${item.color}">\u25CF</span>${item.series.name}: </td>
              <td style="text-align: center">${item.y}</td>
              <td style="text-align: center">${timeMap.get(`${i}-${item.point.x}`)}</td></tr>`;
    });
    s += `<tr><td colspan="3" style="text-align: center">LeadTime（小时）${timeMap.get(`0-${this.points[0].point.x}-lead`)}</td></tr></table>`;
    return s;
};
export default {
    name: 'SprintCardStatusFlowDiagram',
    props: {
        sprint: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            title: '卡片状态图累积流图',
            tipContent: '展示各泳道下的历史卡片数量（包含父子卡片）',
            options: {
                chart: {
                    type: 'streamgraph',
                    zoomType: 'x',
                },
                title: {
                    text: '',
                },
                tooltip: {
                    xDateFormat: '%Y-%m-%d',
                    shared: true,
                    useHTML: true,
                    formatter,
                },
                plotOptions: {
                    streamgraph: {
                        stacking: 'normal',
                        marker: {
                            radius: 2,
                        },
                    },
                },
                exporting: {
                    enabled: false,
                },
                xAxis: {
                    labels: {
                        formatter: function dateFormatter() {
                            return `${this.value.substr(5, 5)}`;
                        },
                    },
                },
                yAxis: {
                    title: {
                        enabled: false,
                    },
                },
                series: [],
            },
            series: [],
        };
    },
    methods: {
        load() {
            if (!this.sprint.id) {
                return;
            }
            const param = Object.assign({}, axis.cardStatusPane, { filters: this.buildFilters() });
            PlanModel.getCardStatusFlowDiagram(param).then((data) => {
                Object.assign(this.options.xAxis, { categories: data.x });
                const tempSeries = [];
                data.z.forEach((val, i) => {
                    tempSeries.push({ name: val, data: data.y[i] });
                });
                this.series = tempSeries;
                this.calTime(data.y);
            });
        },
        buildFilters() {
            return [
                {
                    column: 'snapDate',
                    type: 'range',
                    value: {
                        from: moment(this.sprint.startDate).format('YYYY-MM-DD'),
                        to: moment(this.sprint.endDate).format('YYYY-MM-DD'),
                    },
                },
                { column: 'card.space.id', type: 'term', value: this.sprint.spaceId },
                { column: 'card.sprint.id', type: 'term', value: this.sprint.id },
            ];
        },
        calTime(yAxis) {
            const calMap = new Map();
            if (yAxis.length > 0 && yAxis[0].length > 0) {
                const yIndex = yAxis.length - 1;
                const xIndex = yAxis[0].length - 1;
                for (let x = xIndex; x >= 0; x -= 1) {
                    let sum = 0;
                    for (let y = yIndex; y >= 0; y -= 1) {
                        sum += yAxis[y][x];
                        calMap.set(`${y}-${x}`, sum);
                        if (x === xIndex) {
                            timeMap.set(`${y}-${x}`, 'N/A');
                            timeMap.set(`0-${x}-lead`, 'N/A');
                        } else {
                            timeMap.set(`${y}-${x}`, 'N/A');
                            let z = x + 1;
                            for (; z <= xIndex; z += 1) {
                                const calSum = calMap.get(`${y + 1}-${z}`);
                                if (calSum >= sum) {
                                    const doneNum = calSum - calMap.get(`${y + 1}-${x}`);
                                    timeMap.set(`${y}-${x}`, ((yAxis[y][x] / (doneNum === 0 ? 1 : doneNum)) * (z - x) * 8).toFixed(1));
                                    break;
                                }
                            }
                        }
                        if (y === 0) {
                            timeMap.set(`${y}-${x}-lead`, 'N/A');
                            let z = x + 1;
                            for (; z <= xIndex; z += 1) {
                                const calSum = calMap.get(`${yIndex - 1}-${z}`);
                                if (calSum >= sum) {
                                    const doneNum = calSum - calMap.get(`${yIndex - 1}-${x}`);
                                    timeMap.set(`${y}-${x}-lead`, (((sum - yAxis[yIndex][x]) / (doneNum === 0 ? 1 : doneNum)) * (z - x) * 8).toFixed(1));
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            calMap.clear();
        },
    },
    computed: {
        burndata() {
            return JSON.parse(JSON.stringify(this.series));
        },
        empty() {
            return this.series.length <= 0;
        },
    },
    watch: {
        'sprint.id': {
            handler() {
                this.series = [];
                this.load();
            },
        },
    },
};
</script>
