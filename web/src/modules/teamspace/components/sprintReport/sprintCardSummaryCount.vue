<template>
    <layout-block
        style="position: relative"
        :title="title"
        :display-fullscreen="false"
        height="26"
    >
        <el-radio-group
            style="position: absolute;top: 16px;right: 24px;"
            v-model="type"
        >
            <el-radio-button
                class="jacp-radio-button--plain"
                v-for="item in types"
                :label="item.value"
                :key="item.value"
            >
                {{ item.name }}
            </el-radio-button>
        </el-radio-group>
        <ci-highchart
            :option="options"
            :data="series"
            v-if="!empty"
        />
        <jacp-empty
            v-else
            position="left"
        />
    </layout-block>
</template>

<script>
import PlanModel from '@/models/plan';
import { stageMap } from '@/modules/teamspace/constant';

const options = {
    chart: {
        type: 'column',
    },
    title: {
        text: '',
    },
    xAxis: {
        type: 'category',
        labels: { style: { color: '#999999' } },
    },
    yAxis: {
        title: '',
    },
    tooltip: {
        headerFormat: '{point.key}：{point.y}',
        pointFormat: '',
    },
    legend: {
        enabled: false,
    },
    exporting: {
        enabled: false,
    },
    plotOptions: {
        column: {
            colorByPoint: true,
        },
    },
    series: [],

};
const getStageColor = () => Object.values(stageMap).map((stage = {}) => stage.color);
const series = [{
    data: [],
    colors: getStageColor(),
    dataLabels: {
        enabled: true,
    },
}];
export default {
    name: 'SprintCardSummaryCount',
    props: {
        spaceKey: { type: String, default: '' },
        title: { type: String, default: '' },
    },
    data() {
        return {
            options,
            series,
            data: {},
            type: 'unarchivedSprintCardStageList',
            types: [{
                name: '活跃迭代',
                value: 'unarchivedSprintCardStageList',
            }, {
                name: 'Backlog',
                value: 'backlogCardStageList',
            }],
        };
    },
    methods: {
        async load() {
            this.data = await PlanModel.getCardSummaryCount(this.spaceKey);
        },
        summaryData() {
            const seriesCopy = JSON.parse(JSON.stringify(series));
            seriesCopy[0].data = this.value;
            return seriesCopy;
        },
    },
    computed: {
        empty() {
            return !this.series[0].data.length;
        },
        value() {
            return this.data[this.type] || [];
        },
    },
    watch: {
        spaceKey: {
            immediate: true,
            handler: 'load',
        },
        value: {
            handler() {
                this.series = this.summaryData();
            },
        },
    },
};
</script>
