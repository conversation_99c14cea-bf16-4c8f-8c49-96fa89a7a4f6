<template>
    <div
        class="teamspace-spacecard"
        @click="goto(spaceModesRoutes[space.mode], space)"
    >
        <el-tooltip
            :content="space.mode === 2
                ? $t('jacp.teamspace.spaceType.scrum')
                : $t('jacp.teamspace.spaceType.general')"
            placement="top"
        >
            <div
                class="mode-type"
                :class="'space-type'+space.mode"
            >
                <i class="mode-type-icon" />
                <span class="mode-type-triangle" />
            </div>
        </el-tooltip>
        <el-dropdown
            class="teamspace-spacecard-actions"
            placement="bottom-start"
            trigger="hover"
            @command="spaceOption"
            @click.native.stop
        >
            <jacp-icon
                name="jacp-icon-more"
                active
                @click.native.stop
            />
            <el-dropdown-menu slot="dropdown">
                <!-- 空间概览 -->
                <el-dropdown-item
                    v-if="space.mode !== 1"
                    @click.native="spaceOption('intro', space)"
                >
                    {{ $t('jacp.teamspace.spaceActions.intro') }}
                </el-dropdown-item>
                <!-- 加入分组 -->
                <el-dropdown-item>
                    <el-popover
                        placement="right-start"
                        trigger="hover"
                        popper-class="teamspace-spacelist-space__opration-popper"
                        :visible-arrow="false"
                        :offset="-8"
                        :append-to-body="false"
                    >
                        <span
                            slot="reference"
                            @click.stop
                        >
                            加入分组
                            <i
                                class="el-icon-arrow-right"
                                style="margin:0 -4px 0 4px;"
                            />
                        </span>
                        <ul class="el-dropdown-menu--small teamspace-spacelist-space__opration-groups">
                            <li
                                v-if="!groups.length"
                                class="el-dropdown-menu__item groups-item"
                                style="pointer-events: none;"
                            >
                                <jacp-text
                                    type="disable"
                                    size="12"
                                >
                                    暂无分组
                                </jacp-text>
                            </li>
                            <li
                                v-for="group in groups"
                                class="el-dropdown-menu__item groups-item"
                                style="padding-left: 28px;position: relative;"
                                :class="{
                                    'groups-item--active': group.spaceIds.includes(space.id),
                                }"
                                :key="group.id"
                                @click.stop="spaceOption('group', space, group)"
                            >
                                <i
                                    class="el-icon-check"
                                    style="position: absolute;top: 8px;left: 8px;"
                                    v-show="group.spaceIds.includes(space.id)"
                                />
                                <span>{{ group.groupName }}</span>
                            </li>
                        </ul>
                    </el-popover>
                </el-dropdown-item>
                <!-- 复制空间 -->
                <el-dropdown-item
                    :disabled="space.role !== 'admin'"
                    @click.native="spaceOption('copy', space)"
                >
                    {{ $t('jacp.teamspace.spaceActions.copy') }}
                </el-dropdown-item>
                <!-- 管理员才有权限归档或者取消归档 -->
                <template v-if="space.privilage === 1">
                    <!-- 空间归档 -->
                    <el-dropdown-item
                        v-if="!space.archived"
                        @click.native="spaceOption('archive', space)"
                    >
                        {{ $t('jacp.teamspace.spaceActions.archive') }}
                    </el-dropdown-item>
                    <!-- 空间取消归档 -->
                    <el-dropdown-item
                        v-if="space.archived"
                        @click.native="spaceOption('unarchive', space)"
                    >
                        {{ $t('jacp.teamspace.spaceActions.unarchive') }}
                    </el-dropdown-item>
                </template>
                <slot name="action" />
            </el-dropdown-menu>
        </el-dropdown>
        <jacp-user-avatar
            style="display: block;margin: 0 auto;"
            :data="space"
            :size="48"
            first-name
            avatar
        />
        <div class="teamspace-spacecard__des">
            <p>{{ space.name }}</p>
            <p style="font-size: 0;">
                <span>
                    团队成员<span>{{ space.memberCount > 999 ? '999+' : space.memberCount }}</span>
                </span>
                <span style="margin-left: 4px;">
                    卡片数量<span>{{ space.activeCardCount > 999 ? '999+' : space.activeCardCount }}</span>
                </span>
            </p>
        </div>
        <div
            class="teamspace-spacecard__mark"
            :class="{'marked': space.attentionType === 1}"
            @click.stop="addToAttention(space)"
        >
            <span>{{ space.attentionType === 1 ? '已收藏' : '收藏' }}</span>
        </div>
    </div>
</template>
<script>
import Dialog from '@/models/dialog';
import newSpaceDialog from '$module/dialogs/newSpace';
import { getColorByFirstChar as getBgColor } from '@/plugins/utils.theme';
import { space as SpaceModel } from '@/models/teamspace';
import SpaceGroup from '$module/models/spaceGroup';

export default {
    name: 'SpaceCard',
    props: {
        space: { type: Object, default: () => ({}) },
        groups: { type: Array, default: () => [] },
    },
    data() {
        return {
            spaceModesRoutes: {
                1: 'teamspaceGeneral',
                2: 'teamspaceCardsList',
            },
        };
    },
    methods: {
        copySpace(space) {
            const { mode } = space;
            const content = mode === 1 ? `即将复制来自"${space.name}"空间的人员、卡片状态、卡片标签。`
                : `即将复制来自"${space.name}"空间的人员、卡片类型、卡片状态、角色职责、卡片标签。`;
            return Dialog.confirm({
                title: this.$t('jacp.copyTeamspace'),
                confirmBtnText: this.$t('jacp.button.createAndCopy'),
                content,
                slot: newSpaceDialog,
                slotProps: { baseInfo: space },
                beforeConfirm: vm => vm.validateData().then(() => {
                    SpaceModel.copySpace(vm.data, space.id).then((data) => {
                        this.$emit('on-copied', data);
                    });
                }),
            });
        },
        addToAttention(space) {
            SpaceModel.addToAttention({
                spaceId: space.id,
            }).then(() => {
                this.$set(space, 'attentionType', Number(!space.attentionType));
                this.$message({
                    message: '保存成功！',
                    type: 'success',
                });
            });
        },
        spaceOption(command, space, group) {
            switch (command) {
            case 'copy':
                this.copySpace(space);
                break;
            case 'intro':
                this.goto('teamspaceSpaceIntro', space);
                break;
            case 'group':
                if (group.spaceIds.includes(space.id)) {
                    SpaceGroup.shift({ spaceId: space.id, groupId: group.id })
                        .then(() => {
                            const index = group.spaceIds.indexOf(space.id);
                            group.spaceIds.splice(index, 1);
                        });
                } else {
                    SpaceGroup.add({ spaceId: space.id, groupId: group.id })
                        .then(() => {
                            group.spaceIds.push(space.id);
                        });
                }

                break;
            case 'archive':
                this.$confirm('您可以为所有成员归档不常用的团队空间，归档的空间依然可以正常使用。确定要归档吗？', '提示', {
                    type: 'warning',
                }).then(() => {
                    SpaceModel.archive(space.id).then(() => {
                        Object.assign(space, { archived: 1 });
                    });
                });
                break;
            case 'unarchive':
                SpaceModel.unarchive(space.id).then(() => {
                    Object.assign(space, { archived: 0 });
                });
                break;
            default:
                break;
            }
        },
        goto(rootName, space) {
            this.$router.push({
                name: rootName,
                params: {
                    spaceKey: space.key,
                },
            });
        },
        getBgColor,
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.teamspace-spacelist-space__opration-popper{
    padding: 0;
}
.teamspace-spacecard{
    margin: 24px 24px 0 0;;
    width: 200px;
    height: 214px;
    padding-top: 40px;
    box-shadow: 0px 1px 4px 0px rgba(48,49,51,0.06);
    border-radius: 2px;
    background-color: #fff;
    cursor: pointer;
    transition: all .3s;
    &:hover{
        box-shadow: 0px 8px 16px 0px rgba(48,49,51,0.12);
        .mode-type{
            display: flex;
        }
    }
    position: relative;

    &-actions{
        position: absolute;
        top: 16px;
        right: 16px;
        &__btn{
          color: #c5c5c5;
          &:hover{
              color: #2695F1;
              border: solid 1px #2695F1;
              background-color: transparent;
          }
          &.el-button{
              padding: 2px;
          }
        }
    }
    &.space-admin{
        &:hover{
            & .el-dropdown{
                display: block;
            }
        }
    }
    &__des{
        text-align: center;
        font-family: PingFangSC-Medium;
        font-weight: 600;
        & p{
            width: 160px;
            margin: 8px auto;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            color: @fontColor;
            font-size: 14px;
            height: 20px;
            line-height: 20px;
            & span {
                font-weight: 400;
                font-size: 12px;
                color: @fontThirdColor;
                &:nth-last-child(1) {
                  margin-left: 2px;
                }
            }
        }
    }
    &__mark{
        margin: 0 auto;
        margin-top: 20px;
        width: 88px;
        height: 24px;
        font-size: 12px;
        text-align: center;
        border-radius: 3px;
        border: 1px solid  @primaryColor;
        color: @primaryColor;
        cursor: pointer;
        & span{
            display: inline-block;
            height: 24px;
            font-size: 12px;
            text-align: center;
            line-height: 24px;
        }
        &:hover{
            color: #fff;
            background-color: @primaryColor;
        }
        &.marked{
            color: @fontThirdColor;
            border: none;
            background-color: @mainBgColor;
            &:hover{
                color: #fff;
                background-color: @primaryColor;
                border: 1px solid @primaryColor;
            }
        }
    }
    .mode-type{
        position: absolute;
        width: 24px;
        height: 40px;
        left: 16px;
        top: 0px;
        background-color: #F8D866;
        display: none;
        flex-direction: column;
        transition: height .3s;
        i {
            margin: auto;
            transform: translateY(-4px);
            width: 16px;
            height: 16px;
            background-size: cover;
            background-position-x: 0px;
        }
        &:after{
            content: '';
            position: absolute;
            bottom: 0;
            border: 12px solid transparent;
            border-bottom-color: #fff;
        }
        &:hover{
            height: 48px;
        }
        &.space-type1{
            background-color: #AEDF71;
            .mode-type-icon{
                background-image: url(~@/assets/images/tag_standard.png);
            }
        }
        &.space-type2{
            background-color: #7DBFF7;
            .mode-type-icon{
                background-image: url(~@/assets/images/tag_jaile.png);
            }
        }
    }
}
</style>
