<template>
    <div
        class="teamspace-calendar"
        :class="{'teamspace-calendar__fullscreen': fullScreenState}"
    >
        <el-form
            :inline="true"
            :model="formData"
            class="teamspace-calendar__condition"
            label-position="right"
            label-width="50px"
            ref="conditionForm"
        >
            <!-- 部门树 用于部门视图 -->
            <el-form-item
                label=""
                v-if="viewType === 'org' || viewType === 'workload'"
            >
                <jacp-org-tree
                    size="mini"
                    :root-org-id="topOrgCode"
                    :root-org-name="topOrgName"
                    :default-org-id="orgCode"
                    :default-org-name="orgName"
                    :default-checked-keys="[orgCode]"
                    :default-expanded-keys="defaultExpandedKeys"
                    @check-org="changeOrg"
                />
            </el-form-item>
            <el-form-item
                label="人员"
                prop="members"
            >
                <el-select
                    v-model="formData.members"
                    filterable
                    multiple
                    collapse-tags
                    size="mini"
                    placeholder="请选择"
                    remote
                    :remote-method="loadMembers"
                    @change="getCardCalendarList"
                >
                    <el-option
                        v-for="item in memberOptions"
                        :key="item.userErp"
                        :label="item.userName"
                        :value="item.userErp"
                    >
                        <span>{{ item.userName+'('+item.userErp+')' }}</span>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="关键字"
                prop="keyWord"
                v-if="viewType !== 'workload'"
                label-width="60px"
            >
                <el-input
                    type="text"
                    v-model="formData.keyWord"
                    placeholder="输入卡片关键字"
                    size="mini"
                    @keyup.native="filterChange"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    @click="resetForm('conditionForm')"
                    type="primary"
                    size="mini"
                >
                    重置
                </el-button>
            </el-form-item>
            <el-form-item v-if="viewType === 'workload'">
                <el-button
                    @click="exportWorkload"
                    type="primary"
                    size="mini"
                >
                    导出
                </el-button>
            </el-form-item>
        </el-form>
        <div
            class="calendar"
            ref="calendar"
        >
            <div class="calendar-header">
                <div class="calendar-row">
                    <div class="calendar-header__name">
                        团队成员
                        <span class="calendar-header__btn right">
                            <i class="el-icon-date" />
                            <el-date-picker
                                ref="dateIcon"
                                v-model="firstDate"
                                @change="changeDate"
                            />
                        </span>
                    </div>
                    <div
                        class="date-bar"
                        ref="dateBar"
                    >
                        <div
                            :class="['date-bar__item',
                                     {weekend : item.isHoliday},
                                     {dateRang: item.dateNum >= tmpStartDay
                                         && item.dateNum <= tmpDeadline
                                         && dragFlag
                                         || item.dateNum === defaultDateNum}]"
                            v-for="(item, index) in week"
                            :key="index"
                        >
                            <span
                                class="calendar-header__btn left"
                                v-if="index===0"
                                @click="lastWeek"
                                style="width: 20px;"
                            ><i class="el-icon-arrow-left" /></span>
                            {{ item.dateStr }}
                            <span
                                class="calendar-header__btn right"
                                v-if="index===13"
                                @click="nextWeek"
                                style="width: 20px;"
                            ><i class="el-icon-arrow-right" /></span>
                        </div>
                    </div>
                </div>
            </div>
            <div style="overflow: hidden;">
                <div
                    ref="calendarBody"
                    class="calendar-body"
                    :class="{'calendar-body__personal': viewType==='org',
                             'multiple-line': multipleLine}"
                >
                    <div
                        class="calendar-body__empty"
                        v-if="validUserList.length === 0"
                    >
                        暂无数据
                    </div>
                    <!-- 任务日历 view-type： org -->
                    <template v-if="viewType==='org'">
                        <div
                            class="calendar-row calendar-body__row"
                            v-for="user in validUserList"
                            :key="user.processorErp"
                        >
                            <div class="calendar-body__name">
                                <div class="calendar-body__name__info">
                                    <p
                                        class="user"
                                        :title="user.processorName+'('+ user.processorErp +')'"
                                    >
                                        {{ user.processorName }} ( {{ user.processorErp }} )
                                    </p>
                                    <p
                                        class="org"
                                        v-if="viewType === 'org'"
                                    >
                                        <span class="left">完成任务：{{ user.completedTaskCount }}</span>
                                        <span class="right">未完成：{{ user.unCompletedTaskCount }}</span>
                                    </p>
                                </div>
                            </div>
                            <div
                                class="calendar-body__task-list"
                            >
                                <div
                                    class="calendar-body__task-item"
                                    v-for="cardListItem in user.cardList"
                                    :key="cardListItem.taskId"
                                >
                                    <div
                                        :class="[{ task: task.type===1, notask: task.type===0},
                                                 'day'+task.days,
                                                 {'round-left': task.startDate !== week[0]},
                                                 {'round-right': task.endDate !== week[13]},
                                                 {'sameCard': hightLightCard === cardListItem.cardId && task.type===1},
                                                 {'otherSpace': spaceKey && cardListItem.spaceKey !== spaceKey
                                                     && task.type===1}]"
                                        v-for="(task, index) in cardListItem.workStaus"
                                        :key="index"
                                    >
                                        <label
                                            v-if="task.type === 1 && task.isFirst"
                                            :class="['calendar-body__task-label',
                                                     {'align-right': task.startDate.dateNum
                                                         > week[parseInt(week.length / 2, 10)].dateNum}]"
                                        >
                                            {{ cardListItem.cardName+'[空间：'+(cardListItem.spaceName||'')+']' }}
                                        </label>
                                        <template>
                                            <div
                                                v-if="task.type===1"
                                            >
                                                <span
                                                    :title="cardListItem | calendarTitle"
                                                    :draggable="editable"
                                                    class="dragItem"
                                                    @click.stop="editTaskDetail(user, task, cardListItem)"
                                                >
                                                    {{ cardListItem | calendarTitle }}
                                                </span>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <!-- 饱和度日历 view-type： workload-->
                    <template v-if="viewType==='workload'">
                        <div
                            class="calendar-row calendar-body__row"
                            v-for="user in userList"
                            :key="user.account"
                        >
                            <div class="calendar-body__name">
                                <div class="calendar-body__name__info">
                                    <p
                                        class="user"
                                        :title="user.name+'('+ user.account +')'"
                                    >
                                        {{ user.name }} ( {{ user.account }} )
                                    </p>
                                </div>
                            </div>
                            <div class="calendar-body__task-list work-load">
                                <div
                                    class="calendar-body__load-item"
                                    :class="`workload-level${getWorkloadLevel(task.workLoad, week[index].isHoliday)}`"
                                    v-for="(task, index) in user.tasks"
                                    :key="index"
                                >
                                    <el-popover
                                        v-if="task.workLoad !== 0"
                                        placement="right-end"
                                        width="264"
                                        :open-delay="200"
                                        popper-class="jacp-schedule-popper"
                                        trigger="hover"
                                    >
                                        <Workload-detail
                                            :task="task"
                                            :level="getWorkloadLevel(task.workLoad, week[index].isHoliday)"
                                            @on-card-click="showCardDetailByCode"
                                        />
                                        <span slot="reference">{{ task.workLoad.toFixed(1) }}</span>
                                    </el-popover>
                                    <span v-else>{{ task.workLoad }}</span>
                                </div>
                            </div>
                        </div>
                    </template>
                    <div
                        class="calendar-body__more"
                        v-if="hasMore"
                        @click="loadMore"
                    >
                        查看更多...
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script type="text/javascript">
import fscreen from 'fscreen';
import axios from 'axios';
import { mapState } from 'vuex';
import debounce from 'lodash/debounce';
import UserModel from '@/models/user';
import SpaceModel from '@/models/card';
import OrgModel from '@/models/org';
import { getSpaceId } from '@/plugins/utils';
import WorkloadDetail from './schedule/scheduleWorkloadDetail';
import RouterMixin from '@/mixins/mixin.router';

export default {
    mixins: [RouterMixin],
    components: {
        WorkloadDetail,
    },
    props: {
        currentSpaceId: {
            type: Number,
            default: 0,
        },
        editable: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        const defaultOrgCode = this.$store.state.user.orgCode;
        return {
            spaceKey: this.$route.params.spaceKey,
            spaceId: getSpaceId(this.$route.params.spaceKey),
            week: [],
            currentDate: new Date(new Date(new Date().toLocaleDateString('zh-CN')).getTime()), // 当天零点时间
            firstDate: undefined,
            endDay: undefined,
            userList: [],
            validUserList: [], // 关键字过滤后的 userList
            formData: {
                members: [],
                keyWord: '',
            },
            memberOptions: [],
            personInfo: {},
            currentTask: {},
            card: {},
            dayWidth: 0,
            tmpStartDay: '',
            tmpDeadline: '',
            dragFlag: false,
            hightLightCard: '',
            fullScreenState: false,
            orgCode: defaultOrgCode,
            orgFullName: this.$store.state.user.orgTierName,
            orgName: this.$store.state.user.orgName,
            topOrgCode: '',
            topOrgName: '',
            // customizedConfigRole: [],
            defaultDate: undefined,
            defaultDateNum: 0,
            multipleLine: false,
            defaultExpandedKeys: defaultOrgCode.split('/').filter(o => o),
            hasMore: false,
            current: 1,
            size: 100,
        };
    },
    created() {
        const delay = 500;
        fscreen.addEventListener('fullscreenchange', this.setFullscreenState, false);
        this.loadMembers = debounce(this.loadMembers, delay, { trailing: true });
        this.filterChange = debounce(this.filterChange, delay, { trailing: true });

        this.init();
    },
    beforeDestroy() {
        fscreen.removeEventListener('fullscreenerror', this.setFullscreenState);
    },
    methods: {
        init() {
            this.getWeek();
            this.initFilterData();
        },
        setFullscreenState() {
            this.fullScreenState = fscreen.fullscreenElement
                && fscreen.fullscreenElement.fullScreenComponent === this.$el;
        },
        toggleFullscreen() {
            if (!this.fullScreenState) {
                document.body.fullScreenComponent = this.$el;
                fscreen.requestFullscreen(document.body);
            } else {
                fscreen.exitFullscreen();
                document.body.fullScreenComponent = undefined;
            }
        },
        loadMore() {
            this.current += 1;
            this.getCardCalendarList({ add: true });
        },
        getCardCalendarList({ add = false } = {}) {
            let getCardList = '';
            const param = {};
            param.orgCode = this.orgCode;
            if (this.viewType === 'workload') { // 饱和度视图
                getCardList = 'getMyzoneWorkload';
            } else {
                getCardList = 'getMyzoneCalendarListByOrg'; // 部门视图
            }
            SpaceModel[getCardList](Object.assign(param, {
                startDate: this.firstDate.getTime(),
                endDate: this.endDate.getTime(),
                current: this.current,
                size: this.size,
                userErps: this.formData.members,
            })).then((data) => {
                const userList = data;
                if (this.viewType === 'org') {
                    let workStaus = [];
                    // 合成本周卡片进度
                    userList.forEach((user) => {
                        // 遍历卡片
                        user.cardList.forEach((card) => {
                            const curCard = card;
                            // 检查当前周每天是否在当前卡片某个任务计划内, 将连续的同状态日期合并到单个对象
                            this.week.forEach((day, index) => {
                                // 默认无卡片工作 一周数据样例 eg:【1,1,0,0,0,1,1】
                                workStaus[index] = 0;
                                curCard.workDayList.forEach((task) => {
                                    // 如果当前包含在任何人卡工时区间 设置为
                                    const startDate = new Date(task.startDate).toLocaleDateString();
                                    const endDate = new Date(task.endDate).toLocaleDateString();
                                    if (day.dateNum >= new Date(startDate)
                                        && day.dateNum <= new Date(endDate)) {
                                        workStaus[index] = 1;
                                    }
                                });
                            });

                            /*
                                将数据转换为模板所需数据 type 0：无任务 1：有任务；  days 任务状态持续天数
                                 eg：[{type: 0, days: 2}, {type: 1, days: 5}]周一二无 周三到周日有
                            */
                            let tmp = {};
                            curCard.workStaus = [tmp];
                            workStaus.forEach((n, index) => {
                                if (tmp.type === undefined) {
                                    tmp.type = n;
                                    tmp.days = 1;
                                    tmp.startDate = this.week[index];
                                    tmp.endDate = this.week[index];
                                } else if (tmp.type === n) {
                                    tmp.days += 1;
                                    tmp.endDate = this.week[index];
                                } else {
                                    tmp = {
                                        type: n,
                                        days: 1,
                                        startDate: this.week[index],
                                        endDate: this.week[index],
                                    };
                                    curCard.workStaus.push(tmp);
                                }
                            });
                            // 标记卡片第一个任务
                            const firstTask = curCard.workStaus.find(item => item.type === 1);
                            if (firstTask) {
                                firstTask.isFirst = true;
                            }
                            workStaus = [];
                        });
                    });
                    // 显示并移除克隆的拖拽对象
                    if (this.targetBase && this.target) {
                        this.targetBase.style.visibility = 'visible';
                        this.targetBase.parentNode.removeChild(this.target);
                        this.targetBase = '';
                        this.target = '';
                    }
                }
                // 获取的分页数据追加在已存在的结果集中
                if (add) {
                    this.userList = [
                        ...this.userList,
                        ...data,
                    ];
                } else {
                    this.userList = data;
                }
                // 如果返回的数据小于 size，则隐藏 查看更多
                if (data.length < this.size) {
                    this.hasMore = false;
                } else {
                    this.hasMore = true;
                }
                // 初始化过滤条件
                this.filterChange();
            });
        },
        // 初始化过滤条件下拉数据
        initFilterData() {
            OrgModel.getDeptByLevel({ level: 0 }).then((orgs) => {
                this.topOrgCode = orgs.fullPath;
                this.topOrgName = orgs.fullName;
            });
        },
        exportWorkload() {
            axios({
                method: 'post',
                url: '/devops-api/space-demand/api/v1/spaces/humanCal/workLoad/v2',
                data: {
                    orgCode: this.orgCode,
                    type: 'workloadExport',
                    startDate: this.firstDate.getTime(),
                    endDate: this.endDate.getTime(),
                },
                responseType: 'blob',
                headers: {
                    Authorization: localStorage.getItem('Authorization'),
                },
            }).then((res) => {
                const blob = new Blob([res.data]);
                const elink = document.createElement('a');
                elink.download = '人力资源饱和度.xlsx';
                elink.style.display = 'none';
                elink.href = URL.createObjectURL(blob);
                document.body.appendChild(elink);
                elink.click();
                URL.revokeObjectURL(elink.href);
                document.body.removeChild(elink);
            });
        },
        // 根据日期计算当前周起始终止日期 不传参默认计算当前系统时间所在周
        getWeek(date) {
            if (date) {
                // 设置开始日期 起始时间零点
                this.firstDate = new Date(new Date(date).toLocaleDateString());
            } else {
                const { currentDate } = this;
                const currentDay = currentDate.getDay();
                const days = currentDay === 0 ? 6 : currentDay - 1;
                // 设置开始日期 起始时间零点
                this.firstDate = new Date(currentDate.getTime() - (1000 * 60 * 60 * 24 * days));
            }
            // 设置结束日期 结束时间23点59分59秒999
            this.endDate = new Date(this.firstDate.getTime() + ((1000 * 60 * 60 * 24 * 14) - 1));
            SpaceModel.getHolidayStatus({
                startDate: this.firstDate.getTime(),
                endDate: this.endDate.getTime(),
            }).then((dayList) => {
                let tmp = this.firstDate;
                this.week = [];
                for (let n = 0; n < 14; n += 1) {
                    // 日期毫秒
                    const dateNum = tmp.getTime();
                    // 日期格式化 yyyy-MM-dd
                    const dateStr = `${tmp.getMonth() + 1 > 9 ? tmp.getMonth() + 1 : `0${tmp.getMonth() + 1}`}-${tmp.getDate() > 9 ? tmp.getDate() : `0${tmp.getDate()}`}`;
                    this.week[n] = {
                        dateNum,
                        dateStr,
                        isHoliday: dayList[n] && dayList[n].type !== 3,
                    };
                    // 加一天
                    tmp = new Date(tmp.getTime() + (1000 * 60 * 60 * 24));
                }
                this.targetBase = '';
                this.target = '';
                // 获取列表数据
                this.current = 1;
                this.getCardCalendarList();
            });
        },
        lastWeek() {
            const date = new Date(this.firstDate.getTime() - (1000 * 60 * 60 * 24 * 7));
            this.getWeek(date);
        },
        nextWeek() {
            const date = new Date(this.firstDate.getTime() + (1000 * 60 * 60 * 24 * 7));
            this.getWeek(date);
        },
        changeDate() {
            this.getWeek(this.firstDate);
        },
        loadMembers(query) {
            if (query === '') {
                this.memberOptions = [];
            } else {
                UserModel.searchByOrgName(query, this.orgFullName).then(({ records: data } = {}) => {
                    this.memberOptions = data;
                });
            }
        },
        showCardDetail({
            cardId, sprintId, spaceKey, mode,
        } = {}) {
            if (!cardId || !spaceKey) return;
            if (mode === 1) {
                this.$_routerOpen({
                    name: 'generalCardDetail',
                    params: {
                        spaceKey,
                        cardId,
                    },
                });
                return;
            }
            this.$_routerOpen({
                name: 'teamspaceCardDetail',
                params: {
                    spaceKey,
                },
                query: {
                    cardId,
                    sprintId,
                },
            });
        },
        showCardDetailByCode({
            cardCode,
            taskSource,
            demandId,
            projectId,
            taskId,
        } = {}) {
            // 需求任务
            if (taskSource === 2) {
                this.$_routerOpen({
                    name: 'demandDetail',
                    params: {
                        id: demandId,
                    },
                });
                return;
            }
            // 项目任务
            if (taskSource === 3) {
                this.$_routerOpen({
                    name: 'jtaskDetail',
                    params: {
                        projectId,
                        taskId,
                    },
                });
                return;
            }
            if (!cardCode) return;
            this.$_routerOpen({
                name: 'teamspaceCardDetailByCode',
                params: {
                    code: cardCode,
                },
            });
        },
        editTaskDetail(user, currentTask, card) {
            // 需求任务
            if (card.taskSource === 2) {
                this.$_routerOpen({
                    name: 'demandDetail',
                    params: {
                        id: card.demandId,
                    },
                });
                return;
            }
            // 项目任务
            if (card.taskSource === 3) {
                this.$_routerOpen({
                    name: 'jtaskDetail',
                    params: {
                        projectId: card.projectId,
                        taskId: card.taskId,
                    },
                });
                return;
            }

            // 双击 单机事件分别高亮同卡片 和显示卡片详情
            if (this.timeout) {
                this.hightLightCard = this.hightLightCard === card.cardId ? '' : card.cardId;
                clearTimeout(this.timeout);
                this.timeout = '';
            } else {
                this.timeout = setTimeout(() => {
                    if (!this.editable || card.spaceKey !== this.spaceKey) {
                        this.showCardDetail(card);
                    } else {
                        // 记录当前用户信息
                        Object.assign(this.personInfo, user);
                        this.currentTask = currentTask;
                        this.card = card;
                        // 打开添加任务窗口
                        this.showEditDialog = true;
                    }
                    clearTimeout(this.timeout);
                    this.timeout = '';
                }, 500);
            }
        },
        resetForm(formName) {
            this.current = 1;
            this.$refs[formName].resetFields();
            // 由于回显缓存reset无法清空复杂数据 手动重置
            this.formData.members = [];
            this.formData.keyWord = '';
            this.getCardCalendarList();
            this.filterChange();
            this.scrollToTop();
        },
        scrollToTop() {
            this.$refs.calendarBody.scroll({
                top: 0,
                behavior: 'smooth',
            });
        },
        filterChange() {
            this.validUserList = [];
            if (this.viewType === 'org') {
                this.userList.forEach((user) => {
                    const validCards = [];
                    user.cardList.forEach((card) => {
                        // 根据 关键字 过滤卡片
                        if (this.formData.keyWord === ''
                                || card.cardName.indexOf(this.formData.keyWord) > -1) {
                            validCards.push(card);
                        }
                    });
                    if (this.formData.keyWord === '' || validCards.length) {
                        this.validUserList.push({
                            ...user,
                            cardList: validCards,
                        });
                    }
                });
            } else {
                this.validUserList = this.userList;
            }
        },
        changeOrg(orgId, orgName) {
            this.current = 1;
            this.orgCode = orgId;
            this.orgFullName = orgName;
            this.orgName = orgName.split('-').pop();
            // this.getWeek();
            this.initFilterData();
            this.resetForm('conditionForm');
            // 缓存部门信息
            localStorage.jacp_calandar_ordCode = this.orgCode;
            localStorage.jacp_calandar_orgFullName = this.orgFullName;
            localStorage.jacp_calandar_orgName = this.orgName;
        },

        /* customizedConfigRoleList() {
            SpaceModel.getCustomizedConfigRoleList(this.spaceId).then((config) => {
                this.customizedConfigRole = config;
            });
        }, */
        // 很久饱和度计算等级 设置颜色
        getWorkloadLevel(workload, isHoliday) {
            let level;
            if (workload === 0) {
                level = 0;
            } else if (workload > 0 && workload <= 0.9) {
                level = 1;
            } else if (workload > 0.9 && workload <= 1.1) {
                level = 2;
            } else if (workload > 1.1 && workload <= 1.4) {
                level = 3;
            } else {
                level = 4;
            }
            // 周末
            if (workload !== 0 && isHoliday) {
                level = 5;
            }
            return level;
        },
    },
    computed: {
        ...mapState('chilldteamspace', {
            customizedConfigRole: 'customizedConfigRoleList',
        }),
    },
    filters: {
        calendarTitle(cardItem) {
            if (cardItem.taskSource === 2 || cardItem.taskSource === 3) {
                return cardItem.taskName;
            }
            const sprintInfo = cardItem.sprintName ? `[迭代：${cardItem.sprintName}]` : '';
            return `${cardItem.cardName}${sprintInfo}`;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.teamspace-calendar{
    background-color: #fff;
    .org-selector__value{
        padding-left: 16px;
    }
    &__fullscreen{
        z-index: 999 !important;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        & .calendar div.calendar-body{
            height: calc(~"100vh - 140px");
        }
    }
    &__condition{
        border: 1px solid #e1e1e1;
        padding-top: 12px;
        border-bottom: none;
        // margin-left: 5px;
        position: relative;
        & .fullBtn{
            position: absolute;
            right: 8px;
            top: 12px;
        }
        & .el-select__tags-text{
            display: inline-block;
            max-width: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        & .el-select .el-tag__close.el-icon-close{
            top: -5px;
        }
        & .el-form-item{
            margin-bottom: 12px;
        }
    }
    .calendar{
        color: #666;
        border: 1px solid #e1e1e1;
        &-row{
            text-align: center;
            display: flex;
        }
        & .left{
            float: left;
            left: 0;
            top: 0;
        }
        & .right{
            float: right;
            top: 0;
            right: 0;
        }
        &-header{
            line-height: 32px;
            min-height: 32px;
            border-bottom: 1px solid #f1f1f1;
            background:rgba(250,250,250,1);
            &__name{
                width: 216px;
                border-right: 1px solid #f1f1f1;
                overflow: hidden;
                font-weight: 999;
                text-align: left;
                padding-left: 16px;
            }
            &__btn{
                text-align: center;
                cursor: pointer;
                display: inline-block;
                width: 30px;
                font-family: "SimSun";
                position: relative;
                & .el-date-editor{
                    position: absolute;
                    left: 0;
                    top: 0;
                    opacity: 0;
                }
                & i{
                    color: #c5c5c5;
                }
                &:hover i{
                    color: @primaryColor;
                };
            }
            & .date-bar{
                flex: 1;
                box-sizing: border-box;
                display: flex;
                height: 32px;
                font-size: 12px;
                overflow: hidden;
                position: relative;
                &__item{
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    border-right: 1px solid #f1f1f1;
                    &.weekend{
                        color: red;
                    }
                    &:last-child{
                        border-right: none;
                    }
                    & .left, .right{
                        position: absolute;
                        background: rgba(250, 250, 250, .95);
                    }
                }
            }
        }
        .calendar-body{
            height: calc(~"100vh - 220px");
            width: calc(~"100% + 18px");
            overflow-y: scroll;
            overflow-x: hidden;
            &__personal{
                    height: 560px;
            }
            &__empty{
                text-align: center;
                font-size: 20px;
                line-height: 300px;
                height: 300px;
            }
            &__more {
                text-align: center;
                cursor: pointer;
                height: 32px;
                line-height: 32px;
                color: @primaryColor;
            }
            &__row{
                border-bottom: 1px solid #f1f1f1;
                &:nth-of-type(2n){
                    background:rgba(250,250,250,1);
                }
            }
            &__name{
                width: 216px;
                border-right: 1px solid #f1f1f1;
                display: flex;
                text-align: left;
                padding: 8px 0 8px 16px;
                & p{
                    margin: 2px 0;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                    &.user{
                        font-size: 14px;
                        color: @fontColor;
                        height: 18px;
                        line-height: 16px;
                    }
                    &.erp{
                        font-size: 12px;
                        line-height: 16px;
                        color: @remarkColor;
                    }
                    &.org{
                        font-size: 12px;
                        color: @remarkColor;
                        height: 14px;
                        line-height: 14px;
                    }
                }
                &__info{
                    display: flex;
                    width: 180px;
                    flex-direction: column;
                    justify-content: center;
                }
                &__add{
                    display: flex;
                    width: 40px;
                    align-items: stretch;
                    cursor: pointer;
                    & i{
                        color: #c5c5c5;
                        &:hover{
                            color: @primaryColor;
                        };
                    }
                }
                &__icon{
                    margin: auto;
                }
            }
            &__task-list{
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                padding: 20px 4px 8px 2px;
                overflow: hidden;
                position: relative;
                transition: all .1s;
                transition-delay: .1s;
                &.work-load{
                    flex-direction: row;
                }
                &:hover{
                    & .addBtns{
                        opacity: 1;
                    }
                }
            }
            &__task-item{
                display: flex;
                font-size: 12px;
                color: white;
                line-height: 18px;
                position: relative;
                &.addBtns{
                    position: absolute;
                    left: 0;
                    top: 0;
                    opacity: 0;
                    width: 100%;
                    height: 20px;
                    transition: all .3s;
                    transition-delay: .2s;
                }
                &>div{
                    box-sizing: border-box;
                    background-color: #50B7FF;
                    &.sameCard{
                        background-color: rgba(12,85,166,1);
                    }
                    &.otherSpace{
                        background-color: #c5c5c5;
                    }
                    & div{
                        cursor: pointer;
                        flex: 1;
                        width: 0;
                        text-align: center;
                        display: flex;
                        & span{
                            &:hover{
                                text-decoration: underline;
                            }
                            padding: 0 5px;
                            width: 100%;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            text-align: center;
                        }
                        & i{
                            width: 20px;
                            background: url("./../../../assets/icons/draggable.png") no-repeat;
                            background-size: 4px 8px;
                            background-position: center;
                        }
                    }
                    &.round-left{
                        border-top-left-radius: 12px;
                        border-bottom-left-radius: 12px;
                    }
                    &.round-right{
                        border-top-right-radius: 12px;
                        border-bottom-right-radius: 12px;
                    }
                }
                & .task{
                    height: 18px;
                    margin: 2px 0;
                    display: flex;
                    width: 0;
                    position: relative;
                }

                & .notask{
                    background: transparent;
                    height: 18px;
                    margin: 2px 0;
                }
            }
            &__task-label{
                display: none;
                position: absolute;
                left: 0;
                top: 100%;
                color: #333;
                font-size: 12px;
                white-space: nowrap;
                &.align-right{
                    left: inherit;
                    right: 0;
                    text-align: end;
                }
            }
            &__load-item{
                height: 24px;
                margin: 4px 2px;
                border-radius:3px;
                background-color: gray;
                flex: 1;
                text-align: center;
                line-height: 24px;
                background:rgba(80,183,255,1);
                color: #fff;
                &:hover{
                    box-shadow:0px 2px 6px 0px rgba(51,51,51,0.3);
                }
                & span{
                    display: inline-block;
                    width: 90%;
                }
            }
            &.multiple-line{
                & .calendar-body__task-label{
                    display: block;
                }
                & .task{
                    margin-bottom: 24px;
                }
                & span{
                    color: transparent;
                }
            }
        }
        & .dateRang{
            background:rgba(208,230,248,1);
        }
        &__value{
            display: inline-block;
            width: 120px;
        }
        & .workload-level0{
            color:rgba(38,149,241,1);
            background: rgba(204,239,255,1);
        }
        & .workload-level1{
            color:rgba(38,149,241,1);
            background: rgba(162,223,255,1);
        }
        & .workload-level2{
            color:rgba(255,255,255,1);
            background: rgba(80,183,255,1);
        }
        & .workload-level3{
            color:rgba(255,255,255,1);
            background: rgba(38,149,241,1);
        }
        & .workload-level4{
            color:rgba(255,255,255,1);
            background: rgba(20,112,201,1);
        }
        & .workload-level5{
            color:rgba(255,255,255,1);
            background: rgba(2,58,127,1);
        }
    }
    & .org-selector{
        &__value{
            min-width: 120px;
            height: 26px;
        }
    }
    .fullBtn{
        cursor: pointer;
        color: #e1e1e1;
        &:hover{
            color: #2695f1;
        }
    }
    .set-task-days(@n, @i: 1) when (@i <= @n) {
        .day@{i}{
            flex: @i;
        }
        .set-task-days(@n, @i + 1);
    }
    .set-task-days(14);
}
</style>
