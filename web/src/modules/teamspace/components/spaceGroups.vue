<template>
    <div>
        <ul class="teamspace-spacelist-group">
            <li class="teamspace-spacelist-group__title">
                <jacp-text
                    type="disable"
                    size="12"
                >
                    {{ $t('jacp.teamspace.spaceGroupTitle.default') }}
                </jacp-text>
            </li>
            <!-- 放置一些固定分组 -->
            <slot name="default-group" />
        </ul>
        <ul class="teamspace-spacelist-group">
            <li class="teamspace-spacelist-group__title">
                <jacp-text
                    type="disable"
                    size="12"
                >
                    {{ $t('jacp.teamspace.spaceGroupTitle.group') }}
                </jacp-text>
                <el-button
                    type="text"
                    style="padding: 0px;"
                >
                    <jacp-icon
                        name="el-icon-plus"
                        active
                        @click.native="createGroup"
                    />
                </el-button>
            </li>
            <!-- 放置一些固定分组 -->
            <slot name="prefix-group" />
            <li
                v-for="group in groups"
                :key="group.id"
                class="teamspace-spacelist-group__item"
                :class="{
                    'teamspace-spacelist-group__item--active': value === group.id
                }"
                @click="() => {
                    $emit('input', group.id);
                    $emit('on-select', group);
                }"
            >
                <el-button
                    type="text"
                    class="teamspace-spacelist-group__groupname "
                >
                    <el-tooltip
                        :content="group.groupName"
                        :disabled="group.groupName.replace(/[\u4e00-\u9fa5]/ig, 'aa').length <= 8"
                        placement="top-start"
                    >
                        <div class="j-text-overflow">
                            <jacp-text>
                                {{ group.groupName }}
                            </jacp-text>
                            <jacp-text
                                style="display: inline-block; min-width: 40px"
                            >
                                ({{ group.spaceIds.length }})
                            </jacp-text>
                        </div>
                    </el-tooltip>
                </el-button>
                <el-dropdown
                    placement="bottom-start"
                    trigger="click"
                    :hide-on-click="false"
                >
                    <jacp-icon
                        class="teamspace-spacelist-group__opration-button"
                        name="jacp-icon-more"
                        active
                        @click.native.stop
                    />

                    <el-dropdown-menu
                        slot="dropdown"
                        class="teamspace-spacelist-group__opration-popper"
                    >
                        <el-dropdown-item @click.native="() => renameGroup(group)">
                            <span>{{ $t('jacp.button.rename') }}</span>
                        </el-dropdown-item>
                        <el-dropdown-item>
                            <el-popconfirm
                                :title="$t('jacp.teamspace.spaceGroupActions.removeConfirm', { name: group.groupName })"
                                @confirm="() => group.remove().then(() => $emit('on-deleted', group))"
                            >
                                <span
                                    slot="reference"
                                >
                                    {{ $t('jacp.button.remove') }}
                                </span>
                            </el-popconfirm>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </li>
        </ul>
    </div>
</template>
<script>
import SpaceGroup from '$module/models/spaceGroup';

const lengthPattern = /^.{1,10}$/;
const emojiPattern = /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g;

export default {
    name: 'SpaceGroups',
    props: {
        value: { type: Number },
        groups: { type: Array, default: () => [] },
        maxLength: { type: Number, default: 5 },
    },
    methods: {
        inputValidator(targetGroup, val) {
            if (!lengthPattern.exec(val.trim())) {
                return '请输入1-10个字符';
            }
            if (emojiPattern.exec(val)) {
                return '分组名称不合法';
            }
            if (targetGroup) {
                if (this.groups.find(group => group.groupName === val && group.id !== targetGroup.id)) {
                    return '与现有分组名称重复，请更改';
                }
                return true;
            }
            if (this.groups.find(group => group.groupName === val)) {
                return '与现有分组名称重复，请更改';
            }
            return true;
        },
        createGroup() {
            if (this.groups.length >= this.maxLength) {
                this.$message({
                    showClose: true,
                    message: this.$t('jacp.teamspace.spaceGroupActions.overlength', { size: this.maxLength }),
                    type: 'warning',
                });
                return;
            }
            this.$prompt('', this.$t('jacp.teamspace.spaceGroupActions.create'), {
                inputValidator: this.inputValidator.bind(this, null),
                inputPlaceholder: '请输入分组名称',
            }).then(({ value: groupName }) => {
                SpaceGroup.create({ groupName }).then((group) => {
                    this.$emit('on-created', group);
                });
            });
        },
        renameGroup(group) {
            this.$prompt('', this.$t('jacp.button.rename'), {
                inputValue: group.groupName,
                inputValidator: this.inputValidator.bind(this, group),
                inputPlaceholder: '请输入分组名称',
            }).then(({ value: groupName }) => {
                group.update({ groupName }).then(() => {
                    this.$emit('on-updated', group);
                });
            });
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';

.teamspace-spacelist-group{
    .el-button{
        font-weight: 400;
    }
    &__title{
        padding: 24px 16px 8px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 400;
    }
    &__item{
        height: 40px;
        line-height: 40px;
        padding: 0 8px;
        margin: 0 8px;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &:hover,
        &--active{
            background: @hoverBgColor;
        }
    }
    &__groupname{
        width: calc(~"100% - 48px");
        text-align: left;
        display: flex;
    }
    &__opration-popper{
        &.el-dropdown-menu--small .el-dropdown-menu__item{
            padding: 0;
            text-align: center;
            width: 80px;
            // 扩大一下【删除】的热点区域
            & .el-popover__reference{
                padding: 0 16px;
                display: block;
                line-height: 32px;
                height: 32px;
                width: 100%;
            }
        }
    }
}
</style>
