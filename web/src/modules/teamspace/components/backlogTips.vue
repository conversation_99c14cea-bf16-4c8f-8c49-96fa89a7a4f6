<template>
    <div class="jacp-backlog-tips">
        <el-alert
            v-if="backlogTipsVisible"
            class="jacp-backlog-tips__text"
            title="快速拖动左侧卡片到右侧选择的迭代中；按打开批量操作后，后按住 Shift 键 + 鼠标左键点可选中多个卡片拖动到右侧的迭代中"
            type="warning"
            @close="hideBacklogTips"
            show-icon
        />
        <el-dialog
            title="操作演示"
            :visible.sync="backlogTipsDialogVisible"
            width="648px"
        >
            <img
                src="http://storage.360buyimg.com/jacp.attachment/static/images/backlogtip.gif"
                width="100%"
                height="365"
                style="margin-bottom: 104px;"
                alt="Backlog操作演示"
            >
            <div
                slot="footer"
                style=""
                class="jacp-backlog-tips-dialog__text"
            >
                <blockquote class="jacp-backlog-tips-dialog__text__title">
                    <strong>|</strong>
                    Backlog
                </blockquote>
                <p style="width: 100%">
                    独立的团队空间backlog可以更清晰地让团队成员管理团队待研发的内容。
                    <br>
                    同时支持从团队空间backlog选取卡片拖动到迭代，规划即将开始和进行中迭代的研发内容。
                </p>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: 'BacklogTips',
    data() {
        return {
            backlogTipsVisible: !localStorage.getItem('jacp-BacklogTipsReaded'),
            backlogTipsDialogVisible: !localStorage.getItem('jacp-BacklogTipsDialogReaded'),
        };
    },
    mounted() {
        localStorage.setItem('jacp-BacklogTipsDialogReaded', 1);
    },
    methods: {
        hideBacklogTips() {
            this.backlogTipsVisible = false;
            localStorage.setItem('jacp-BacklogTipsReaded', 1);
        },
        showBacklogTips() {
            this.backlogTipsVisible = true;
            localStorage.removeItem('jacp-BacklogTipsReaded');
        },
        showBacklogTipsAndDialog() {
            this.backlogTipsDialogVisible = true;
            this.showBacklogTips();
        },
    },
    watch: {
        backlogTipsVisible: {
            immediate: true,
            handler(val) {
                this.$emit('update:tipsVisible', val);
            },
        },
    },
};
</script>
<style lang="less">
.jacp-backlog-tips {
    .el-alert__closebtn{
        font-size: var(--font-size--subtitle);
        top: 10px;
    }
    &__text{
        margin-left: 24px;
        width: calc(100% - 48px);
        // transform: translateY(12px);
        z-index:1;
    }
    &-dialog__text{
        width: 100%;
        height: 139px;
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 2;
        background: rgba(38,149,241,0.8);
        font-size: var(--font-size--content);
        font-family: PingFangSC;
        font-weight: normal;
        color: rgba(255,255,255,1);
        text-align:left;
        padding: 0 var(--gutter--medium);
        line-height:20px;
        &__title{
            margin: var(--gutter--large) 0 var(--gutter--mini) 0;
            font-size: 18px;
        }
    }
}

</style>
