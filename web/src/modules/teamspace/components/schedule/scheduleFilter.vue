<template>
    <div class="schedule-filter">
        <div class="schedule-filter__title">
            筛选
        </div>
        <div style="position: absolute;width: 100%;border-bottom: 1px solid #e9e9e9;left: 0;" />
        <template v-if="viewType === 'teamspace'">
            <el-input
                v-model="conditions.keyWord"
                placeholder="输入卡片名称关键字"
                prefix-icon="el-icon-search"
                class="schedule-filter__key"
                clearable
            />
            <template v-if="spaceMode === 2">
                <label class="schedule-filter__label">迭代</label>
                <el-select
                    multiple
                    collapse-tags
                    style="margin-top: 8px;width: 100%;"
                    placeholder="请选择"
                    v-model="conditions.plans"
                    clearable
                    :popper-append-to-body="false"
                    @change="$emit('change', 'plans', conditions.plans)"
                >
                    <el-option
                        v-for="item in planList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </template>
            <label class="schedule-filter__label">部门</label>
            <el-select
                multiple
                collapse-tags
                style="margin-top: 8px;width: 100%;"
                placeholder="请选择"
                v-model="conditions.orgs"
                clearable
                :popper-append-to-body="false"
                @change="$emit('change', 'orgs', conditions.orgs)"
            >
                <el-option
                    v-for="item in deptList"
                    :key="item.memberOrgId"
                    :label="item.memberOrgName.split('-').pop()"
                    :value="item.memberOrgId"
                />
            </el-select>
        </template>
        <template>
            <div v-show="memberList && memberList.length > 0">
                <label
                    class="schedule-filter__label"
                    style="position: relative;"
                >
                    成员
                    <el-button
                        v-if="this.conditions.members && this.conditions.members.length"
                        type="text"
                        style="position: absolute;right:0;top: -8px;"
                        @click="clearMembers"
                    >清除</el-button>
                </label>
                <el-input
                    placeholder="输入工号/姓名"
                    prefix-icon="el-icon-search"
                    v-model="searchUserString"
                    class="schedule-filter__searchuser"
                    clearable
                />
                <div
                    class="schedule-filter__users"
                >
                    <div
                        class="schedule-filter__user"
                        v-for="member in memberList"
                        :key="member.memberErp"
                        v-show="member.memberName.includes(searchUserString)
                            || member.memberErp.includes(searchUserString)"
                    >
                        <div
                            class="schedule-filter__user-pic"
                            :title="member.memberErp"
                            @click="usersChange(member.memberErp)"
                        >
                            <div
                                class="schedule-filter__user-select"
                                v-show="conditions.members.includes(member.memberErp)"
                            />
                            <img
                                v-if="member.headImage"
                                :title="member.memberErp"
                                :src="member.headImage"
                            >
                            <img
                                v-else
                                :title="member.memberErp"
                                src="../../../../assets/images/avatar.png"
                            >
                        </div>
                        <div class="name">
                            {{ member.memberName }}
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script type="text/javascript">
import CardModel from '@/models/card';

export default {
    props: {
        spaceId: {
            type: Number,
            default: 0,
        },
        spaceMode: {
            type: Number,
            default: 1,
        },
        viewType: {
            type: String,
            default: 'teamspace',
        },
        checkedMembers: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            showUsers: true,
            searchUserString: '',
            planList: [],
            memberList: [],
            deptList: [],
            conditions: {
                keyWord: '',
                plans: [],
                members: [],
                orgs: [],
            },
        };
    },
    methods: {
        // 初始化过滤条件下拉数据
        initFilterData() {
            // 人员
            CardModel.getMemberList({
                spaceId: this.spaceId,
            }).then((data) => {
                this.memberList = data || [];
                data.forEach((member) => {
                    const have = this.deptList
                        .some(item => item.memberOrgId === member.memberOrgId);
                    if (!have && member.memberOrgId) {
                        this.deptList.push(member);
                    }
                });
            });
            // 迭代
            CardModel.getPlanList({
                spaceId: this.spaceId,
                // 查询全部迭代
                type: 0,
            }).then((data) => {
                this.planList = data || [];
            });
            // 显示缓存查询条件
            const calendarCondition = sessionStorage.calendarCondition
                ? JSON.parse(sessionStorage.calendarCondition) : {};
            const curSpaceCondition = calendarCondition[this.spaceKey] || {
                orgs: [],
                plans: [],
                members: [],
                keyWord: '',
            };
            Object.assign(this.conditions, curSpaceCondition);
            this.$emit('change', 'all', this.conditions);
        },
        usersChange(erp) {
            const index = this.conditions.members.indexOf(erp);
            if (index > -1) {
                this.conditions.members.splice(index, 1);
            } else {
                this.conditions.members.push(erp);
            }
            this.emitMembersChange();
        },
        clearMembers() {
            this.conditions.members = [];
            this.emitMembersChange();
        },
        emitMembersChange() {
            this.$emit('update:checkedMembers', this.conditions.members);
            this.$emit('change', 'members', this.conditions.members);
        },
    },
    watch: {
        'conditions.keyWord': {
            handler(keyWord) {
                this.$emit('change', 'keyWord', keyWord);
            },
        },
        spaceId: {
            handler() {
                this.spaceKey = this.$route.params.spaceKey;
                this.initFilterData();
            },
            immediate: true,
        },
        checkedMembers(val = []) {
            this.conditions.members = val;
            this.emitMembersChange();
        },
    },
};
</script>

<style lang="less">
.schedule-filter{
    position: relative;
    box-sizing: border-box;
    width: 304px;
    padding: 0 24px;
    position: relative;
    &__title{
        line-height: 48px;
        height: 48px;
        color: #333;
    }
    &__label{
        font-size: 14px;
        display: block;
        margin-top: 24px;
        color: #333333;
    }
    &__key{
        margin: 16px 0 0;
        & .el-input__inner{
            height: 32px;
        }
    }
    &__head{
        overflow: hidden;
        color: #333333;
        margin-top: 24px;
    }
    &__showAll{
        float: right;
        font-size: 12px;
        color: #999999;
        cursor: pointer;
    }
    &__filters{
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        margin-bottom: 24px;
        &.tags{
            max-height: 64px;
            overflow: hidden;
            &.showmore{
                height: auto;
                max-height: inherit;
            }
        }
        & .el-radio__label{
            font-size: 12px;
            padding-left: 6px;
        }
    }
    &__users{
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
        height: auto;
        overflow: hidden;
        transition: all .5s;
        &.showmore{
            height: auto;
        }
    }
    &__user{
        width: 40px;
        height: 50px;
        margin: 8px 12px 0 0;
        position: relative;
        &:nth-of-type(5n){
            margin-right: 0px;
        }
        & img{
            display: block;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #f1f1f1;
            margin: 0 auto;
        }
        &
        &-select{
            position: absolute;
            z-index: 1;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #f1f1f1;
            left: 50%;
            transform: translate(-50%);
            background: url('~@/assets/icons/confirm.png') no-repeat;
            background-size: 100% 100%;
            background-color: #023A7F;
            opacity: .7;
        }
        & .name{
            text-align: center;
            font-size: 12px;
            margin: 4px auto;
            color: #999999;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space:nowrap;
            width: 100%;
        }
    }
    &__searchuser{
        margin-top: 8px;
        & .el-input__inner{
            height: 32px;
        }
    }
}
</style>
