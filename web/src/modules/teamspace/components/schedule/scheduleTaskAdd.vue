<template>
    <el-dialog
        title="新增任务"
        :visible="showDialog"
        :before-close="closeDialog"
        :close-on-click-modal="false"
        class="schedule-task-dialog"
        append-to-body
        width="648px"
        top="10vh"
    >
        <card-task-form
            ref="addTaskForm"
            :card-task-info="formData"
            :members="[personInfo]"
            :space-id="spaceId"
            :default-date="defaultDate"
            :show-add-card-btn="true"
            :remain-hours-disabled="true"
        />
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-button @click="closeDialog">
                取 消
            </el-button>
            <jacp-button
                type="primary"
                :on-click="addTask"
            >
                确 定
            </jacp-button>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import { mapState } from 'vuex';
import CardModel from '@/models/card';
import CardTaskModel from '@/modules/card/models/CardTask';
import mixinCardTask from '@/modules/card/mixins/mixin.cardTask';
import cardTaskForm from '@/modules/_components/cardTaskForm';
// let defaultRoleMap = {};

export default {
    mixins: [mixinCardTask],
    components: {
        cardTaskForm,
    },
    props: {
        showAddDialog: {
            type: Boolean,
            default: false,
        },
        personInfo: {
            type: Object,
            default: () => {},
        },
        defaultDate: {
            type: Date,
            default: () => new Date(),
        },
        spaceId: {
            type: Number,
            default: 0,
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            formData: {
                sprint: -1,
                sprintList: [],
                cardId: '',
                cardList: [],
                content: '',
                erp: '',
                remainingHours: undefined,
                plannedHours: undefined,
                roleCode: '',
                // duration: [],
                startDate: '',
                deadline: '',
            },
            defaultHours: undefined,
            showAddCard: false,
            roleInfo: {},
        };
    },
    created() {
    },
    methods: {
        closeDialog() {
            this.$emit('update:showAddDialog', false);
            this.showAddCard = false;
        },
        async addTask() {
            const { formData } = this.$refs.addTaskForm;
            const valid = await this.$refs.addTaskForm.$refs.cardTaskForm.validate();
            if (valid) {
                // 防重
                return CardTaskModel.create({
                    // cardId: this.formData.cardId,
                    content: formData.content,
                    erp: this.personInfo.processorErp,
                    name: this.personInfo.processorName,
                    remainingHours: formData.remainingHours,
                    plannedHours: formData.plannedHours,
                    deadline: formData.deadline,
                    startDate: formData.startDate,
                    roleCode: formData.roleCode,
                }, formData.cardId).then(() => {
                    this.$notify({
                        title: '保存成功！',
                        type: 'success',
                        duration: 2000,
                    });
                    // 更新人资日历数据
                    this.$emit('refreshlist');
                    this.closeDialog();
                    this.saveDefaultRole(this.personInfo.processorErp, formData.roleCode);
                });
            }
            return Promise.reject();
        },
        getDefaultRole(erp, spaceId, initFlag) {
            CardModel.getRoleCodeDefault(erp, spaceId).then((data) => {
                if (data && data.length > 0) {
                    const defaultRole = data[0];
                    this.roleInfo = defaultRole;
                    if (initFlag) {
                        // 首次加载任务只获取职位id 优先保留原任务数据
                        this.formData.roleCode = this.formData.roleCode || defaultRole[spaceId];
                    } else {
                        // 人员切换导致的二次加载人员职位信息 直接设置查询到的默认职位
                        this.formData.roleCode = defaultRole[spaceId];
                    }
                } else {
                    this.roleInfo = {};
                }
            });
        },
        saveDefaultRole(erp, roleCode) {
            const params = { [this.spaceId]: roleCode };
            if (this.roleInfo.id) {
                CardModel.updateConfigRoleDefault(this.roleInfo.id, erp, params);
            } else {
                CardModel.saveConfigRoleDefault(erp, params);
            }
        },
    },
    watch: {
        showDialog(val) {
            if (val) {
                this.getDefaultRole(this.personInfo.processorErp, this.spaceId);
                // 本应该在 personInfo 变化时，设置 erp 的，但是 watch 不到变化?
                this.formData.erp = this.personInfo.processorErp;
                this.formData.startDate = this.defaultDate;
                this.formData.deadline = this.defaultDate;
                if (this.$refs.addTaskForm) {
                    this.$refs.addTaskForm.clear();
                }
            } else {
                this.$refs.addTaskForm.resetForm();
                // this.$refs.addTaskForm.clear();
            }
        },
    },
    computed: {
        showDialog() {
            return this.showAddDialog;
        },
        ...mapState('chilldteamspace', {
            customizedConfigRole: 'customizedConfigRoleList',
        }),
    },
};
</script>

<style lang="less">
.schedule-task-dialog {
    .el-dialog__header {
        border-bottom: solid 1px #EBEEF5;
        padding: 12px 16px;
        .el-dialog__title {
            font-size: 16px;
            font-weight: 500;
        }
    }
    .el-dialog__footer {
        padding: 16px;
    }
    .el-dialog__body {
        padding: 8px 16px;
    }
    .el-dialog__headerbtn {
        right: 16px;
    }
}
</style>
