<template>
    <el-dialog
        title="人员排序"
        :visible="showSortDialog"
        :before-close="closeDialog"
        class="teamspace-calendar_sort"
        append-to-body
        width="640px"
    >
        <el-table
            v-if="showSortDialog"
            ref="table"
            :data="userList"
            height="400"
            style="width: 100%"
            row-class-name="drag-elements"
        >
            <template v-if="viewType==='teamspace'">
                <el-table-column
                    label="姓名"
                    width="100"
                >
                    <template slot-scope="props">
                        <span
                            class="user-info"
                            :erp="props.row.processorErp"
                        >{{ props.row.processorName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="processorErp"
                    label="工号"
                    width="140"
                />
                <el-table-column
                    prop="processorOrg"
                    label="部门"
                    :show-overflow-tooltip="true"
                />
            </template>
            <template v-if="viewType==='workload'">
                <el-table-column
                    label="姓名"
                    width="100"
                >
                    <template slot-scope="props">
                        <span
                            class="user-info"
                            :erp="props.row.account"
                        >{{ props.row.name }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="account"
                    label="工号"
                    width="140"
                />
                <el-table-column
                    prop="orgTierName"
                    label="部门"
                    :show-overflow-tooltip="true"
                />
            </template>
        </el-table>
        <div
            slot="footer"
            class="dialog-footer"
            height="100"
        >
            <el-button @click="closeDialog">
                取 消
            </el-button>
            <el-button
                v-show="userList.length > 0"
                type="primary"
                @click="saveUserOrder"
            >
                保存
            </el-button>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import Sortable from 'sortablejs';
import { card as CardModel } from '@/models/teamspace';

export default {
    props: {
        showSortDialog: {
            type: Boolean,
            default: false,
        },
        userList: {
            type: Array,
            default: () => [],
        },
        viewType: {
            type: String,
            default: 'teamspace',
        },
    },
    methods: {
        saveUserOrder() {
            const erpList = [];
            const spanEls = this.$refs.table.$el.querySelectorAll('span.user-info');
            [].forEach.call(spanEls, (user) => {
                erpList.push(user.getAttribute('erp'));
            });
            CardModel.saveUserOrder({ erpList }).then(() => {
                this.$notify({
                    title: '保存排序成功！',
                    type: 'success',
                    duration: 2000,
                });
                // 更新人资日历数据
                this.$emit('refreshlist');
                this.closeDialog();
            });
        },
        closeDialog() {
            this.$emit('update:showSortDialog', false);
        },
    },
    watch: {
        showSortDialog(show) {
            if (show) {
                this.$nextTick(() => {
                    const dragElement = this.$refs.table.$el.querySelector('tbody');
                    this.dragObj = new Sortable(dragElement, {
                        sort: true,
                        animation: 150,
                    });
                });
            }
        },
    },
};
</script>
<style lang="less">
.teamspace-calendar_sort{
    & .dialog-footer{
        margin: 0;
    }
    & .drag-elements{
        background-image: url("~@/assets/icons/sortable.svg");
        background-repeat: no-repeat;
        background-size: contain;
        &:hover{
            cursor: grab;
        }
        & span{
            padding-left: 10px;
        }
    }
    & .el-table--enable-row-hover .el-table__body tr:hover>td {
        background-color: transparent;
    }
}
</style>
