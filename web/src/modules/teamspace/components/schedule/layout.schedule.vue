<template>
    <div
        class="jacp-schedule"
        :class="{'jacp-schedule__fullscreen': fullScreenState}"
    >
        <div class="schedule-header">
            <div class="schedule-header-top">
                <div class="schedule-header-top-left">
                    <el-tabs
                        class="jacp-tabs"
                        :value="viewType"
                        @tab-click="changeViewType"
                    >
                        <el-tab-pane
                            name="teamspace"
                            label="人力资源日历"
                        />
                        <el-tab-pane
                            name="workload"
                            label="饱和度日历"
                        />
                    </el-tabs>
                </div>
                <!-- 时间条件 -->
                <div class="schedule-header-top-right">
                    <el-date-picker
                        v-model="dateRange"
                        type="daterange"
                        align="right"
                        unlink-panels
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 240px"
                        :picker-options="pickerOptions"
                        :clearable="false"
                        value-format="timestamp"
                        size="mini"
                        @change="getWeek"
                    />
                    <!-- <jacp-icon
                        style="color: #c5c5c5;"
                        name="icon-help_outline j-mgl16"
                        v-if="spaceMode === 2"
                        :size="16"
                        @click.native="showHelp = true"
                    /> -->
                    <span class="seperate-line" />
                    <jacp-icon
                        name="jacp-icon-icon_fullscreen"
                        active
                        @click.native="toggleFullscreen"
                    />
                </div>
            </div>
            <div class="schedule-header-bottom">
                <div class="schedule-header-top-left">
                    <el-radio-group
                        v-if="viewType === 'teamspace'"
                        v-model="globalMode"
                        size="mini"
                        @change="getCardCalendarList"
                    >
                        <el-radio-button
                            :label="0"
                            class="jacp-radio-button--plain"
                        >
                            当前空间
                        </el-radio-button>
                        <el-radio-button
                            :label="1"
                            class="jacp-radio-button--plain"
                        >
                            跨空间
                        </el-radio-button>
                    </el-radio-group>
                </div>
                <div class="schedule-header-top-right">
                    <!-- 与筛选条件联动 -->
                    <el-switch
                        v-model="selfMode"
                        inactive-color="#e1e1e1"
                        :active-value="1"
                        :inactive-value="0"
                        @change="handleSelfModeChange"
                    />
                    <jacp-text class="j-mgl8 j-mgr8">
                        仅我处理的
                    </jacp-text>
                    <div class="schedule-header-btns">
                        <a
                            class="jacp-button-board jacp-button-filter"
                            :class="{'active': hasFilter}"
                            href="javascript:void(0);"
                            @click="showFilterPanel"
                        >
                            <span>
                                筛选
                            </span>
                        </a>
                    </div>
                    <el-dropdown
                        class="j-mgl8"
                        trigger="click"
                        v-if="viewType=== 'teamspace'"
                    >
                        <i
                            class="el-icon-more"
                        />
                        <el-dropdown-menu
                            slot="dropdown"
                        >
                            <div
                                class="schedule-header__oprations"
                            >
                                <el-form>
                                    <el-form-item label="文字显示于外部（卡片文字）">
                                        <jacp-text slot="label">
                                            文字显示于外部<jacp-text
                                                type="disable"
                                                size="12"
                                            >
                                                （卡片文字）
                                            </jacp-text>
                                        </jacp-text>
                                        <el-switch
                                            v-model="multipleLine"
                                            inactive-color="#e1e1e1"
                                            @change="changeLineType"
                                        />
                                    </el-form-item>
                                    <el-button
                                        type="text"
                                        v-if="spacePrivilage.isAdmin"
                                        @click="showSortDialog = true"
                                    >
                                        设置人员显示顺序
                                        <jacp-text
                                            type="disable"
                                            size="12"
                                        >
                                            （空间管理员可操作）
                                        </jacp-text>
                                    </el-button>
                                </el-form>
                            </div>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
        </div>
        <div class="schedule-table">
            <div class="schedule-main">
                <!-- 左侧固定列 -->
                <div class="schedule-main-left">
                    <div class="schedule-main-head">
                        <div class="schedule-main-row">
                            <div
                                class="freeze-column schedule-main-td column-name"
                            >
                                人员
                            </div>
                        </div>
                    </div>
                    <!-- 只显示固定列  -->
                    <div
                        class="schedule-main-body"
                        ref="freezeColumn"
                        style="overflow: hidden;"
                    >
                        <!-- 人力资源日历人员列 -->
                        <template
                            v-if="viewType === 'teamspace'"
                        >
                            <div
                                class="schedule-main-row"
                                v-for="(user, index) in userList"
                                :key="user.processorErp"
                                :style="{'height': (lineHeight[index] || 40) + 'px'}"
                                v-show="validUsers.includes(user.processorErp)"
                            >
                                <div class="freeze-column  schedule-main-td">
                                    <jacp-erp
                                        :data="{
                                            name: user.processorName,
                                            headImage: user.headImage,
                                            erp: user.processorErp,
                                        }"
                                        :disable-timline="$utils.isCurrentUser(user)"
                                        :first-name="!user.headImage"
                                        :avatar-size="24"
                                        avatar
                                        vertical
                                    />
                                    <i
                                        class="el-icon-plus"
                                        v-if="editable && viewType === 'teamspace'"
                                        @click="addTask(user, firstDate)"
                                    />
                                </div>
                            </div>
                        </template>
                        <!-- workload 饱和度人员列 -->
                        <template
                            v-if="viewType === 'workload'"
                        >
                            <div
                                class="schedule-main-row"
                                v-for="(user, index) in userList"
                                :key="user.account"
                                :style="{'height': (lineHeight[index] || 40) + 'px'}"
                                v-show="validUsers.includes(user.account)"
                            >
                                <div class="freeze-column  schedule-main-td">
                                    <jacp-erp
                                        :data="{
                                            name: user.name,
                                            headImage: user.headImage,
                                            erp: user.account,
                                        }"
                                        :disable-timline="$utils.isCurrentUser(user)"
                                        :first-name="!user.headImage"
                                        :avatar-size="24"
                                        avatar
                                        vertical
                                    />
                                    <!-- <div>
                                        <img
                                            v-if="user.headImage"
                                            :src="user.headImage"
                                        >
                                        <img
                                            v-else
                                            src="../../../../assets/images/avatar.png"
                                        >
                                        <span>{{ user.name }}</span>
                                    </div> -->
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- 右侧日期列表 -->
                <div class="schedule-main-right">
                    <div class="schedule-main-head">
                        <div
                            class="schedule-main-row"
                            ref="dateBar"
                        >
                            <div
                                class="schedule-main-td date"
                                :class="{'weekend': day.isHoliday,
                                         'today': currentDate.getTime() === day.dateNum
                                             || day.dateNum >= tmpStartDay
                                             && day.dateNum <= tmpDeadline
                                             && dragFlag}"
                                v-for="day in week"
                                :key="day.dateNum"
                            >
                                {{ day.dateStr }}
                            </div>
                        </div>
                    </div>
                    <div
                        class="schedule-main-body"
                        :class="{'multiple-line': multipleLine}"
                        @scroll="scrollAction"
                        ref="scrollTable"
                    >
                        <template v-if="viewType === 'teamspace'">
                            <div
                                class="schedule-main-row"
                                :class="{'showRulerX': rowIndex === curRowIndex}"
                                v-for="(user, rowIndex) in userList"
                                :key="user.processorErp"
                                :style="{'height': (lineHeight[rowIndex] || 40) + 'px'}"
                                v-show="validUsers.includes(user.processorErp)"
                            >
                                <div
                                    class="task-list"
                                    @drop="drop(user, $event)"
                                    @dragover="dragover"
                                >
                                    <!-- 任务层 -->
                                    <div
                                        class="task-list-item"
                                        v-for="(cardListItem, idx) in user.cardList"
                                        :key="cardListItem.cardId+'_'+idx"
                                        @dblclick="hightLightCard = cardListItem.cardId"
                                        v-show="validCards.includes(cardListItem.cardId)"
                                    >
                                        <div
                                            :class="[{ task: task.type===1, notask: task.type===0},
                                                     'day'+task.days,
                                                     {'round-left': task.startDate !== week[0]},
                                                     {'round-right': task.endDate !== week[week.length - 1]},
                                                     {'sameCard': hightLightCard === cardListItem.cardId
                                                         && task.type===1},
                                                     `bg-${spaceKey && cardListItem.spaceKey === spaceKey
                                                         ? getStageColor(cardListItem.stageId || 10)
                                                         : 'otherSpace'}`]"
                                            v-for="task in cardListItem.workStatus"
                                            :key="task.id"
                                        >
                                            <label
                                                v-if="task.type === 1 && task.isFirst"
                                                :class="['task-list-label',
                                                         {'align-right': task.startDate.dateNum
                                                             > week[parseInt(week.length / 2, 10)].dateNum}]"
                                            >
                                                <template v-if="spaceMode === 2">
                                                    {{ (globalMode===1 && cardListItem.spaceKey !== spaceKey)
                                                        ? getCardNameWithSpaceAndSprint(cardListItem)
                                                        : getCardNameWithSprint(cardListItem) }}
                                                </template>
                                                <template v-else>
                                                    {{ (globalMode===1 && cardListItem.spaceKey !== spaceKey)
                                                        ? getCardNameWithSpace(cardListItem)
                                                        : cardListItem.cardName }}  </template>
                                            </label>
                                            <!-- 跨空间视图专用 -->
                                            <template v-if="globalMode===1 && cardListItem.spaceKey !== spaceKey">
                                                <div v-if="task.type === 1">
                                                    <span
                                                        :title="getCardNameWithSpaceAndSprint(cardListItem)"
                                                        @click.stop="editTaskDetail(user, task, cardListItem)"
                                                    >
                                                        {{ getCardNameWithSpaceAndSprint(cardListItem) }}
                                                    </span>
                                                </div>
                                            </template>
                                            <template v-else>
                                                <div v-if="task.type === 1">
                                                    <i
                                                        class="leftPort"
                                                        @mousedown.stop.prevent="daragStart(user, task, cardListItem, $event)"
                                                        v-if="task.startDate !== week[0] && editable"
                                                    />
                                                    <span
                                                        :title="spaceMode === 2
                                                            ? getCardNameWithSprint(cardListItem)
                                                            : cardListItem.cardName"
                                                        :draggable="editable"
                                                        class="dragItem"
                                                        @click.stop="editTaskDetail(user, task, cardListItem)"
                                                        @dragstart.stop="move(user, task, cardListItem, $event)"
                                                    >
                                                        {{ spaceMode === 2
                                                            ? getCardNameWithSprint(cardListItem)
                                                            : cardListItem.cardName }}
                                                    </span>
                                                    <i
                                                        class="rightPort"
                                                        @mousedown.stop.prevent="daragStart(user, task, cardListItem, $event)"
                                                        v-if="task.endDate !== week[week.length - 1] && editable"
                                                    />
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                                <!-- 背景栅格纹路 -->
                                <div
                                    class="schedule-main-td"
                                    :class="{'weekend': day.isHoliday,
                                             'today': currentDate.getTime() === day.dateNum,
                                             'showRulerY': columnIndex === curColumnIndex}"
                                    v-for="(day, columnIndex) in week"
                                    :key="day.dateNum"
                                    @mouseenter="showRuler(rowIndex, columnIndex)"
                                    @mouseleave="hideRuler"
                                >
                                    <div
                                        class="td-add-btn"
                                        v-if="editable && viewType === 'teamspace'"
                                        @click="addTask(user, new Date(day.dateNum))"
                                    />
                                </div>
                            </div>
                        </template>
                        <!-- 饱和度日历 view-type： workload-->
                        <template v-if="viewType==='workload'">
                            <div
                                class="schedule-main-row"
                                :class="{'showRulerX': rowIndex === curRowIndex}"
                                v-for="(user, rowIndex) in userList"
                                :key="user.account"
                                v-show="validUsers.includes(user.account)"
                            >
                                <div
                                    class="task-list"
                                >
                                    <div class="task-list-item">
                                        <div
                                            class="task-list-load"
                                            :class="`workload-level${getWorkloadLevel(task, week[index])}`"
                                            v-for="(task, index) in user.tasks"
                                            :key="index"
                                        >
                                            <el-popover
                                                v-if="task.workLoad !== 0"
                                                placement="right-end"
                                                width="264"
                                                :open-delay="200"
                                                popper-class="jacp-schedule-popper"
                                                trigger="hover"
                                            >
                                                <schedule-workload-detail
                                                    :task="task"
                                                    :level="getWorkloadLevel(task, week[index])"
                                                    @on-card-click="showCardDetailByCode"
                                                />
                                                <span slot="reference">{{ task.workLoad.toFixed(1) }}</span>
                                            </el-popover>
                                            <span v-else>{{ task.workLoad }}</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- 背景栅格纹路 -->
                                <div
                                    style="height: 56px;"
                                    class="schedule-main-td"
                                    :class="{'weekend': day.isHoliday,
                                             'today': currentDate.getTime() === day.dateNum,
                                             'showRulerY': columnIndex === curColumnIndex}"
                                    v-for="(day, columnIndex) in week"
                                    :key="day.dateNum"
                                />
                            </div>
                        </template>
                        <!-- <div class="schedule-main-row" /> -->
                    </div>
                </div>
            </div>
        </div>
        <schedule-task-add
            :show-add-dialog.sync="showAddDialog"
            :person-info="personInfo"
            :space-id="spaceId"
            :default-date="defaultDate"
            :space-privilage="spacePrivilage"
            @refreshlist="getCardCalendarList"
        />
        <schedule-task-edite
            :show-edit-dialog.sync="showEditDialog"
            :person-info="personInfo"
            :space-id="spaceId"
            :card="card"
            :current-task="currentTask"
            :member-list="userList"
            @refreshlist="getCardCalendarList"
        />
        <sort-member
            :view-type="viewType"
            :user-list="userList"
            :show-sort-dialog.sync="showSortDialog"
            @refreshlist="getCardCalendarList"
        />
        <jacp-off-canvas
            ref="scheduleFilter"
            :fixed-on-document="false"
        >
            <schedule-filter
                :view-type="viewType"
                :space-id="spaceId"
                :space-mode="spaceMode"
                :checked-members.sync="checkedMembers"
                @change="changeFilter"
            />
        </jacp-off-canvas>
        <!-- <jacp-guide
            :visible.sync="showHelp"
            :list="guideList"
        /> -->
    </div>
</template>
<script type="text/javascript">
import fscreen from 'fscreen';
import { getSpaceId, getSpaceInfo } from '@/plugins/utils';
import { stageMap } from '@/modules/teamspace/constant';
import CardModel from '@/models/card';
import CardTaskModel from '@/modules/card/models/CardTask';
import scheduleTaskEdite from './scheduleTaskEdite';
import scheduleTaskAdd from './scheduleTaskAdd';
import scheduleFilter from './scheduleFilter';
import sortMember from './sortMember';
import scheduleWorkloadDetail from './scheduleWorkloadDetail';
import RouterMixin from '@/mixins/mixin.router';

const guideList = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

const mixinsCardName = {
    methods: {
        getCardNameWithSpace(card = {}) {
            return `${card.cardName}[空间：${card.spaceName || ''}]`;
        },
        getCardNameWithSprint(card = {}) {
            if (!card.sprintName) return card.cardName;
            return `${card.cardName}[迭代：${card.sprintName}]`;
        },
        getCardNameWithSpaceAndSprint(card = {}) {
            const space = `[空间：${card.spaceName || ''}]`;
            const sprint = card.sprintName ? `[迭代：${card.sprintName}]` : '';
            return `${card.cardName}${space}${sprint}`;
        },
    },
};
export default {
    mixins: [RouterMixin, mixinsCardName],

    components: {
        scheduleTaskAdd,
        scheduleTaskEdite,
        sortMember,
        scheduleFilter,
        scheduleWorkloadDetail,
    },
    props: {
        currentSpaceId: {
            type: Number,
            default: 0,
        },
        modify: {
            type: Boolean,
            default: false,
        },
        viewType: {
            type: String,
            default: 'teamspace',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            spaceKey: this.$route.params.spaceKey,
            spaceId: getSpaceId(this.$route.params.spaceKey),
            week: [],
            currentDate: new Date(new Date(new Date().toLocaleDateString()).getTime()), // 当天零点时间
            firstDate: undefined,
            endDay: undefined,
            userList: [],
            dateRange: [],
            lineHeight: [],
            hightLightCard: '',
            stageMap,
            validUsers: [], // 人员过滤
            validCards: [], // 卡片顾虑
            showAddDialog: false,
            showEditDialog: false,
            showSortDialog: false,
            personInfo: {},
            currentTask: {},
            card: {},
            dayWidth: 0,
            tmpStartDay: '',
            tmpDeadline: '',
            dragFlag: false,
            fullScreenState: false,
            orgCode: this.$store.state.user.orgCode,
            orgName: this.$store.state.user.orgName,
            topOrgCode: '',
            topOrgName: '',
            globalMode: 0,
            defaultDate: undefined,
            defaultDateNum: 0,
            multipleLine: false,
            curRowIndex: -1,
            curColumnIndex: -1,
            showHelp: false,
            guideList,
            formData: {
                depts: [],
                orgs: [],
                orgList: [],
                members: [],
                plans: [],
                keyWord: '',
            },
            pickerOptions: {
                shortcuts: [{
                    text: '最近两周',
                    onClick: (picker) => {
                        const currentDay = this.currentDate.getDay();
                        // 当天距离当前周周一的天数
                        const days = currentDay === 0 ? 6 : currentDay - 1;
                        // 设置开始日期 默认当前周周一
                        const start = new Date(this.currentDate.getTime()
                            - (1000 * 60 * 60 * 24 * days));
                        // 默认时间跨度两周
                        const end = new Date(start.getTime()
                            + ((1000 * 60 * 60 * 24 * 14) - 1));
                        picker.$emit('pick', [start, end]);
                    },
                }, {
                    text: '最近一个月',
                    onClick: (picker) => {
                        // 设置开始日期 默认当前周周一
                        const start = new Date(this.currentDate.getTime());
                        start.setDate(1);
                        // 默认时间跨度两周
                        const end = new Date(this.currentDate.getTime());
                        end.setMonth(this.currentDate.getMonth() + 1);
                        end.setDate(0);
                        picker.$emit('pick', [start, end]);
                    },
                }],
                firstDayOfWeek: 1, // 这只第一天为周一
            },
            selfMode: 0, // 仅我处理的
            checkedMembers: [],
        };
    },
    created() {
        const viewAccount = parseInt(localStorage.jacp_schedule_viewAccount || 0, 10);
        if (viewAccount === 0 && this.editable) {
            this.showHelp = true;
            localStorage.jacp_schedule_viewAccount = 1;
        } else {
            localStorage.jacp_schedule_viewAccount = viewAccount + 1;
        }
    },
    mounted() {
        this.setLineHeight();
        fscreen.addEventListener('fullscreenchange', this.setFullscreenState, false);
    },
    beforeDestroy() {
        fscreen.removeEventListener('fullscreenerror', this.setFullscreenState);
    },
    methods: {
        getCardCalendarList() {
            let getCardList = '';
            const param = {};
            // 空间视图
            if (this.viewType === 'teamspace') {
                getCardList = 'getCardCalendarList';
                param.spaceId = this.spaceId;
                // 快空间查询标识 0 非跨空间 1 跨空间
                param.globalMode = this.globalMode;
            } else if (this.viewType === 'workload') { // 饱和度视图
                getCardList = 'getworkLoadBySpaceId';
                param.spaceId = this.spaceId;
            }
            CardModel[getCardList](Object.assign(param, {
                startDate: this.firstDate.getTime(),
                endDate: this.endDate.getTime(),
            })).then((data) => {
                const userList = data;
                if (this.viewType === 'teamspace') {
                    let workStatus = [];
                    // 合成本周卡片进度
                    userList.forEach((user) => {
                        // 遍历卡片
                        user.cardList.forEach((card) => {
                            const curCard = card;
                            // 检查当前周每天是否在当前卡片某个任务计划内, 将连续的同状态日期合并到单个对象
                            this.week.forEach((day, index) => {
                                // 默认无卡片工作 一周数据样例 eg:【1,1,0,0,0,1,1】
                                workStatus[index] = 0;
                                curCard.workDayList.forEach((task) => {
                                    // 如果当前包含在任何人卡工时区间 设置为
                                    const startDate = new Date(task.startDate).toLocaleDateString();
                                    const endDate = new Date(task.endDate).toLocaleDateString();
                                    if (day.dateNum >= new Date(startDate)
                                        && day.dateNum <= new Date(endDate)) {
                                        workStatus[index] = 1;
                                    }
                                });
                            });

                            /*
                                将数据转换为模板所需数据 type 0：无任务 1：有任务；  days 任务状态持续天数
                                 eg：[{type: 0, days: 2}, {type: 1, days: 5}]周一二无 周三到周日有
                            */
                            let tmp = {};
                            curCard.workStatus = [tmp];
                            workStatus.forEach((n, index) => {
                                if (tmp.type === undefined) {
                                    tmp.type = n;
                                    tmp.days = 1;
                                    tmp.startDate = this.week[index];
                                    tmp.endDate = this.week[index];
                                } else if (tmp.type === n) {
                                    tmp.days += 1;
                                    tmp.endDate = this.week[index];
                                } else {
                                    tmp = {
                                        type: n,
                                        days: 1,
                                        startDate: this.week[index],
                                        endDate: this.week[index],
                                    };
                                    curCard.workStatus.push(tmp);
                                }
                            });
                            // 标记卡片第一个任务
                            const firstTask = curCard.workStatus.find(item => item.type === 1);
                            if (firstTask) {
                                firstTask.isFirst = true;
                            }
                            workStatus = [];
                        });
                    });
                    // 显示并移除克隆的拖拽对象
                    if (this.targetBase && this.target) {
                        this.targetBase.style.visibility = 'visible';
                        this.targetBase.parentNode.removeChild(this.target);
                        this.targetBase = '';
                        this.target = '';
                    }
                }
                this.userList = data;
                this.$nextTick(() => {
                    // 重置行高
                    this.setLineHeight();
                });
                this.changeFilter();
            });
        },
        // 根据日期计算当前周起始终止日期 不传参默认计算当前系统时间所在周
        getWeek(dateRange) {
            if (dateRange) {
                // 设置开始日期 起始时间零点
                this.firstDate = new Date(dateRange[0]);
                this.endDate = new Date(dateRange[1]);
            } else {
                const { currentDate } = this;
                const currentDay = currentDate.getDay();
                // 当天距离当前周周一的天数
                const days = currentDay === 0 ? 6 : currentDay - 1;
                // 设置开始日期 默认当前周周一
                this.firstDate = new Date(currentDate.getTime()
                    - (1000 * 60 * 60 * 24 * days));
                // 默认时间跨度两周
                this.endDate = new Date(this.firstDate.getTime()
                    + ((1000 * 60 * 60 * 24 * 14) - 1));
                this.dateRange = [this.firstDate, this.endDate];
            }
            // 设置结束日期 结束时间23点59分59秒999
            // 获取节假日列表
            CardModel.getHolidayStatus({
                startDate: this.firstDate.getTime(),
                endDate: this.endDate.getTime(),
            }).then((dayList) => {
                let tmp = this.firstDate;
                const days = parseInt(Math.abs(this.firstDate.getTime()
                    - this.endDate.getTime()) / 1000 / 60 / 60 / 24 + 1, 10);
                this.week = [];
                for (let n = 0; n < days; n += 1) {
                    // 日期毫秒
                    const dateNum = tmp.getTime();
                    // 日期格式化 yyyy-MM-dd
                    const dateStr = `${tmp.getMonth() + 1 > 9 ? tmp.getMonth() + 1 : `0${tmp.getMonth() + 1}`}-${tmp.getDate() > 9 ? tmp.getDate() : `0${tmp.getDate()}`}`;
                    this.week[n] = {
                        dateNum,
                        dateStr,
                        isHoliday: dayList[n] && dayList[n].type !== 3,
                    };
                    // 加一天
                    tmp = new Date(tmp.getTime() + (1000 * 60 * 60 * 24));
                }
                this.targetBase = '';
                this.target = '';
                // 获取列表数据
                this.getCardCalendarList();
            });
        },
        daragStart(user, task, card, e) {
            // 记录当前用户信息
            Object.assign(this.personInfo, user);
            this.currentTask = task;
            this.card = card;
            // 当前拖拽对象
            this.targetBase = e.target.parentNode.parentNode;
            // 任务当前的宽度
            this.taskWidth = this.targetBase.offsetWidth;
            // 任务当前的LEFT
            this.taskLeft = this.targetBase.offsetLeft;
            // 获取当前单位1天 的宽度
            this.dayWidth = this.taskWidth / task.days;
            // 克隆拖拽对象并隐藏原对象
            this.target = this.targetBase.cloneNode(true);
            // 判断时间类型
            this.dragType = e.target.className === 'rightPort' ? 'dragRight' : 'dragLeft';
            // 选择并保存拖拽函数
            this.fn = this[this.dragType];
            this.x = e.clientX;
            // 调整克隆元素为可拖拽样式
            this.target.style.width = `${this.targetBase.offsetWidth}px`;
            this.target.style.left = `${this.targetBase.offsetLeft}px`;
            this.target.style.position = 'absolute';
            this.targetBase.parentNode.insertBefore(this.target, this.targetBase);
            this.targetBase.style.visibility = 'hidden';
            // 绑定拖拽事件
            document.addEventListener('mousemove', this.fn, false);
            document.addEventListener('mouseup', this.dragEnd, false);
            this.dragFlag = true;
            this.tmpStartDay = this.currentTask.startDate.dateNum;
            this.tmpDeadline = this.currentTask.endDate.dateNum;
        },
        dragLeft(e) {
            const moveX = e.clientX - this.x;
            if (this.taskWidth - moveX > this.dayWidth) {
                this.target.style.left = `${this.taskLeft + moveX}px`;
                this.target.style.width = `${this.taskWidth - moveX}px`;
                const tmpDay = Math.round((this.target.offsetWidth - this.taskWidth)
                    / this.dayWidth);
                const newStartDate = this.currentTask.startDate.dateNum
                    - (tmpDay * (1000 * 60 * 60 * 24));
                // 开始日期 大于或等于当前查询范围开始日期 小于任务截止日期
                this.tmpStartDay = newStartDate < this.week[0].dateNum
                    ? this.week[0].dateNum : newStartDate;
            }
        },
        dragRight(e) {
            const moveX = e.clientX - this.x;
            this.target.style.width = `${(this.taskWidth + moveX) >= this.dayWidth ? (this.taskWidth + moveX) : this.dayWidth}px`;
            const tmpDay = Math.round((this.target.offsetWidth - this.taskWidth) / this.dayWidth);
            const newDeadline = this.currentTask.endDate.dateNum
                + (tmpDay * (1000 * 60 * 60 * 24));
            // 当前最大日期
            const maxDay = this.week[this.week.length - 1];
            // 终止日期 大于或等于任务起始日期 小于当前查询范围截止日期
            this.tmpDeadline = newDeadline > maxDay ? maxDay : newDeadline;
        },
        dragEnd() {
            document.removeEventListener('mousemove', this.fn);
            document.removeEventListener('mouseup', this.dragEnd);
            const tmpDay = Math.round((this.target.offsetWidth - this.taskWidth)
                / this.dayWidth);
            // 当前最小日期
            const minDay = this.week[0];
            // 当前最大日期
            const maxDay = this.week[this.week.length - 1];
            this.dragFlag = false;
            CardTaskModel.getListByDate({
                erp: this.personInfo.processorErp || '',
                cardId: this.card.cardId,
                startDate: this.currentTask.startDate.dateNum || new Date().getTime(),
                endDate: this.currentTask.endDate.dateNum || new Date().getTime(),
            }).then((data) => {
                this.taskList = data || [];
                if (this.taskList.length === 1) {
                    // 设置拖拽边界值
                    if (this.dragType === 'dragRight') {
                        let newDeadline = this.taskList[0].deadline
                            + (tmpDay * (1000 * 60 * 60 * 24));
                        // 终止日期 大于或等于任务起始日期 小于当前查询范围截止日期
                        newDeadline = newDeadline > maxDay.dateNum
                            ? maxDay.dateNum : newDeadline;
                        newDeadline = newDeadline < this.taskList[0].startDate
                            ? this.taskList[0].startDate : newDeadline;
                        this.taskList[0].deadline = newDeadline;
                    } else {
                        let newStartDate = this.taskList[0].startDate
                            - (tmpDay * (1000 * 60 * 60 * 24));
                        // 开始日期 大于或等于当前查询范围开始日期 小于任务截止日期
                        newStartDate = newStartDate < minDay.dateNum
                            ? minDay.dateNum : newStartDate;
                        newStartDate = newStartDate > this.taskList[0].deadline
                            ? this.taskList[0].deadline : newStartDate;
                        this.taskList[0].startDate = newStartDate;
                    }
                    // 检查计划工时与天数合理性
                    const days = ((this.taskList[0].deadline - this.taskList[0].startDate)
                        / (1000 * 60 * 60 * 24)) + 1;
                    if (days * 24 < this.taskList[0].plannedHours) {
                        this.$alert('超过单日24小时计划工时建议修改！', '提示', {
                            confirmButtonText: '确定',
                        });
                        // 获取列表数据
                        this.getCardCalendarList();
                        return;
                    }
                    this.updateCardTask();
                } else if (this.targetBase && this.target) {
                    this.targetBase.style.visibility = 'visible';
                    this.targetBase.parentNode.removeChild(this.target);
                    this.targetBase = '';
                    this.target = '';
                    this.$alert('该任务存在多条任务信息，请点击打开详情编辑！', '操作提示', {
                        confirmButtonText: '确定',
                        type: 'warning',
                    });
                }
            });
        },
        move(user, task, card, e) {
            if (!this.editable || e.target.className.indexOf('dragItem') < 0) return false;
            // 记录当前用户信息
            Object.assign(this.personInfo, user);
            this.currentTask = task;
            this.card = card;
            this.target = e.target;
            // 获取当前单位1天 的宽度
            this.dayWidth = parseInt(this.$refs.dateBar.querySelector('.date').offsetWidth, 10);
            this.x = e.clientX;
            this.dragFlag = true;
            this.tmpStartDay = this.currentTask.startDate.dateNum;
            this.tmpDeadline = this.currentTask.endDate.dateNum;
            return false;
        },
        dragover(e) {
            if (!this.editable) return;
            e.preventDefault();
            let tmpDay = (e.clientX - this.x) / this.dayWidth;
            tmpDay = Math.round(tmpDay);
            const { currentTarget } = e;
            const newStartDate = this.currentTask.startDate.dateNum
                + (tmpDay * (1000 * 60 * 60 * 24));
            const newDeadline = this.currentTask.endDate.dateNum
                + (tmpDay * (1000 * 60 * 60 * 24));
            // 开始日期 大于或等于当前查询范围开始日期 小于任务截止日期
            this.tmpStartDay = newStartDate < this.week[0].dateNum
                ? this.week[0].dateNum : newStartDate;
            // 终止日期 大于或等于任务起始日期 小于当前查询范围截止日期
            this.tmpDeadline = newDeadline > this.week[this.week.length - 1].dateNum
                ? this.week[this.week.length - 1].dateNum : newDeadline;
            this.cleanHightLight();
            // 添加目标行高亮效果
            if (currentTarget.className.indexOf('targetList') === -1) {
                currentTarget.className += ' targetList';
            }
        },
        drop(newUser, e) {
            if (!this.editable) return;
            this.cleanHightLight();
            this.dragFlag = false;
            let tmpDay = (e.clientX - this.x) / this.dayWidth;
            tmpDay = Math.round(tmpDay);
            CardTaskModel.getListByDate({
                erp: this.personInfo.processorErp,
                cardId: this.card.cardId,
                startDate: this.currentTask.startDate.dateNum,
                endDate: this.currentTask.endDate.dateNum,
            }).then((data) => {
                this.taskList = data || [];
                if (this.taskList.length === 1) {
                    const newDeadline = this.taskList[0].deadline
                        + (tmpDay * (1000 * 60 * 60 * 24));
                    this.taskList[0].deadline = newDeadline;
                    const newStartDate = this.taskList[0].startDate
                        + (tmpDay * (1000 * 60 * 60 * 24));
                    this.taskList[0].startDate = newStartDate;
                    this.taskList[0].erp = newUser.processorErp;
                    this.taskList[0].name = newUser.processorName;
                    // this.getPlannedHours(this.taskList[0]).then(() => {
                    this.updateCardTask();
                    // });
                } else if (this.target) {
                    this.target = '';
                    this.$alert('该任务存在多条任务信息，请点击打开详情编辑！', '操作提示', {
                        confirmButtonText: '确定',
                        type: 'warning',
                    });
                }
            });
        },
        cleanHightLight() {
            this.$refs.scrollTable.querySelectorAll('.schedule-main-row .task-list').forEach((item) => {
                const target = item;
                target.className = target.className.replace(/targetList/g, '');
            });
        },
        updateCardTask() {
            CardTaskModel.update(this.taskList[0], this.card.cardId)
                .then(() => {
                    this.$notify({
                        title: '保存成功！',
                        type: 'success',
                        duration: 2000,
                    });
                    this.getCardCalendarList();
                }).catch(this.getCardCalendarList);
        },
        showCardDetail({
            cardId, sprintId, spaceKey, mode,
        } = {}) {
            if (!cardId || !spaceKey) return;
            if (mode === 1) {
                this.$_routerOpen({
                    name: 'generalCardDetail',
                    params: {
                        spaceKey,
                        cardId,
                    },
                });
            } else if (mode === 2) {
                this.$_routerOpen({
                    name: 'teamspaceCardDetail',
                    params: {
                        spaceKey,
                    },
                    query: {
                        cardId,
                        sprintId,
                    },
                });
            }
        },
        showCardDetailByCode({ cardCode } = {}) {
            if (!cardCode) return;
            this.$_routerOpen({
                name: 'teamspaceCardDetailByCode',
                params: {
                    code: cardCode,
                },
            });
        },
        editTaskDetail(user, currentTask, card) {
            // 双击 单机事件分别高亮同卡片 和显示卡片详情
            if (this.timeout) {
                this.hightLightCard = this.hightLightCard === card.cardId ? '' : card.cardId;
                clearTimeout(this.timeout);
                this.timeout = '';
            } else {
                this.timeout = setTimeout(() => {
                    if (!this.editable || card.spaceKey !== this.spaceKey) {
                        this.showCardDetail(card);
                    } else {
                        // 记录当前用户信息
                        Object.assign(this.personInfo, user);
                        this.currentTask = currentTask;
                        this.card = card;
                        // 打开添加任务窗口
                        this.showEditDialog = true;
                    }
                    clearTimeout(this.timeout);
                    this.timeout = '';
                }, 500);
            }
        },
        scrollAction() {
            this.$refs.freezeColumn.scrollTop = this.$refs.scrollTable.scrollTop;
        },
        setLineHeight() {
            this.lineHeight = [];
            const baseRows = this.$refs.scrollTable.querySelectorAll('.task-list');
            // 人资日历模式下增加24像素显示添加按钮
            const tmp = this.viewType === 'teamspace' ? 24 : 0;
            [].forEach.call(baseRows, (row) => {
                this.lineHeight.push(row.offsetHeight + tmp);
            });
        },
        changeLineType() {
            this.$nextTick(() => {
                this.setLineHeight();
            });
        },
        getStageColor(stageId) {
            const colorIndex = Object.keys(this.stageMap).indexOf(`${stageId}`);
            return colorIndex > -1 ? colorIndex + 1 : 1;
        },
        showRuler(rowIndex, columnIndex) {
            this.curRowIndex = rowIndex;
            this.curColumnIndex = columnIndex;
        },
        hideRuler() {
            this.curRowIndex = -1;
            this.curColumnIndex = -1;
        },
        setFullscreenState() {
            this.fullScreenState = fscreen.fullscreenElement
                && fscreen.fullscreenElement.fullScreenComponent === this.$el;
        },
        toggleFullscreen() {
            if (!this.fullScreenState) {
                document.body.fullScreenComponent = this.$el;
                fscreen.requestFullscreen(document.body);
            } else {
                fscreen.exitFullscreen();
                document.body.fullScreenComponent = undefined;
            }
        },
        showFilterPanel() {
            this.$refs.scheduleFilter.open();
        },
        handleSelfModeChange(selfMode) {
            this.checkedMembers = selfMode ? [this.$store.state.user.erp] : [];
            this.changeFilter('members', this.checkedMembers);
        },
        changeFilter(key, filter) {
            if (key) {
                if (key === 'all') {
                    Object.assign(this.formData, filter);
                } else {
                    this.formData[key] = filter;
                }
            }
            this.validUsers = [];
            this.validCards = [];
            this.userList.forEach((user) => {
                if (this.viewType === 'teamspace') {
                    // 根据部门 人员erp 过滤人员
                    if ((this.formData.members.length === 0
                            || this.formData.members.includes(user.processorErp))
                        && (this.formData.orgs.length === 0
                            || this.formData.orgs.includes(user.processorOrgId))) {
                        // 有效erp
                        this.validUsers.push(user.processorErp);
                    }
                    user.cardList.forEach((card) => {
                        // 根据 迭代 和 关键字 过滤卡片
                        if ((this.formData.plans.length === 0
                                || this.formData.plans.includes(card.sprintId))
                            && (this.formData.keyWord === ''
                                || card.cardName.indexOf(this.formData.keyWord) > -1)) {
                            // 有效卡片ID
                            this.validCards.push(card.cardId);
                        }
                    });
                }
                if (this.viewType === 'workload') {
                    // 根据部门 人员erp 过滤人员
                    if ((this.formData.members.length === 0
                            || this.formData.members.includes(user.account))) {
                        // 有效erp
                        this.validUsers.push(user.account);
                    }
                }
            });
            // 缓存查询条件
            const calendarCondition = sessionStorage.calendarCondition
                ? JSON.parse(sessionStorage.calendarCondition) : {};
            calendarCondition[this.spaceKey] = {
                orgs: this.formData.orgs,
                members: this.formData.members,
                plans: this.formData.plans,
                keyWord: this.formData.keyWord,
            };
            // 以空间维度保存查询条件
            sessionStorage.calendarCondition = JSON.stringify(calendarCondition);
            this.$nextTick(() => {
                // 重置行高
                this.setLineHeight();
            });
        },
        addTask(user, date) {
            // 记录当前用户信息
            Object.assign(this.personInfo, user);
            this.defaultDate = date;
            // 打开添加任务窗口
            this.showAddDialog = true;
        },
        changeViewType({ name: viewType }) {
            this.$emit('update:viewType', viewType || 'teamspace');
        }, // 很久饱和度计算等级 设置颜色
        getWorkloadLevel({ workLoad }, { isHoliday }) {
            let level;
            if (workLoad === 0) {
                level = 0;
            } else if (workLoad > 0 && workLoad <= 0.9) {
                level = 1;
            } else if (workLoad > 0.9 && workLoad <= 1.1) {
                level = 2;
            } else if (workLoad > 1.1 && workLoad <= 1.4) {
                level = 3;
            } else {
                level = 4;
            }
            // 周末
            if (workLoad !== 0 && isHoliday) {
                level = 5;
            }
            return level;
        },
    },
    watch: {
        currentSpaceId: {
            immediate: true,
            handler(v) {
                if (v) {
                    this.spaceKey = this.$route.params.spaceKey;
                    this.spaceId = getSpaceId(this.$route.params.spaceKey);
                    this.getWeek();
                }
            },
        },
        viewType() {
            this.getCardCalendarList();
        },
        checkedMembers() {
            if (this.checkedMembers.length === 1 && this.checkedMembers[0] === this.$store.state.user.erp) {
                this.selfMode = 1;
            } else {
                this.selfMode = 0;
            }
        },
    },
    computed: {
        spaceMode() {
            return getSpaceInfo(this.$route.params.spaceKey).mode;
        },
        editable() {
            return this.spacePrivilage.updateCard && this.modify && this.spaceMode === 2;
        },
        hasFilter() {
            return this.formData.orgs.length > 0
            || this.formData.members.length > 0
            || this.formData.plans.length > 0
            || this.formData.keyWord;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
/* 操作收到了点点点里 */
.schedule-header__oprations{
    display: flex;
    flex-direction: column;
    width: 280px;
    padding: var(--gutter--small) var(--gutter--medium) 0 var(--gutter--medium);
    .el-form-item{
        margin-bottom: var(--gutter--small);
        .el-form-item__label{
            font-size: var(--font-size--content);
            text-align: left;
        }
        .el-form-item__content{
            text-align: right;
        }
    }
    .el-button--text{
        font-size: var(--font-size--content);
        font-weight: 400;
    }
}
.jacp-schedule{
    color: var(--color--base--content);
    font-family: PingFang SC;
    position: relative;
    &__fullscreen{
        z-index: 999 !important;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: var(--color--base--background);
        & div.schedule-table{
            height: calc(~"100vh - 148px");
        }
    }
    .column-name{
        display: inline-block;
        height: 40px;
        width: 100%;
        font-size: 14px;
        padding: 10px 16px;
        text-align: left;
        font-weight: 900;
    }
    .schedule-header{
        background-color: #fff;
        margin: var(--gutter--large);
        &-top{
            border-bottom: var(--border--hr);
        }
        &-top,
        &-bottom{
            display: flex;
            justify-content: space-between;
            height: var(--height--base);
            align-items: center;
            padding: 0 var(--gutter--medium);
        }
        &-top-right{
            display: flex;
            align-items: center;
        }
        & .el-range-separator {
            width: 8%;
        }
        &-btns{
          display: flex;
          align-items: center;
        }

    }
    .schedule-table{
        padding: var(--gutter--large);
        padding-top: 0;
        height: calc(~"100vh - 74px");
        background-color: #F5F5F5;
        .schedule-main{
            display: flex;
            width: 100%;
            height: 100%;
            background-color: #fff;
            padding: var(--gutter--medium);
            &-left{
                height: 100%;
                width: 160px;
                border: 1px solid rgba(241,241,241,1);
                border-right: none;
                overflow: hidden;
            }
            &-right{
                height: 100%;
                flex: 1;
                overflow-x: overlay;
                overflow-y: hidden;
                border:1px solid rgba(241,241,241,1);
            }
            &-body{
                overflow-y: overlay;
                overflow-x: hidden;
                min-width: max-content;
                height: calc(~"100% - 56px");
                width: 100%;
                position: relative;
                & .schedule-main-row{
                    &:last-of-type{
                        border-bottom: 1px solid rgba(241,241,241,1);
                    }
                    &.showRulerX{
                        border-bottom: 1px dashed #50B7FF;
                    }
                    & .showRulerY{
                        border-right: 1px dashed #50B7FF;
                    }
                }
                & .weekend{
                    background: url("./../../../../assets/images/<EMAIL>") repeat;
                    background-size: 8px 8px;
                }
                & .today{
                    border-right: 1px dashed #2695F1;
                }
                & .targetList{
                    border: 2px solid #2695F1;
                }
                &.multiple-line{
                    & .task-list-label{
                        display: block;
                    }
                    & .task{
                        margin-bottom: 24px;
                        & span{
                            color: transparent;
                        }
                    }
                }
            }
            &-head{
                height: 40px;
                background:rgba(250,250,250,1);
                min-width: max-content;
                width: 100%;
                .date{
                    font-size: 12px;
                    font-family: PingFangSC-Regular;
                    font-weight: 400;
                    color: #333;
                    line-height: 40px;
                    text-align: center;
                    &.weekend{
                        color: #999;
                    }
                    &.today{
                        color: #2695F1;
                        background-color: #E9F4FD;
                    }
                }
            }
            &-td{
                flex: 1 0 auto;
                border-right: 1px solid rgba(241,241,241,1);
                height: 100%;
                width: 80px;
                position: relative;
                z-index: 0;
                & .td-add-btn{
                    width: 16px;
                    height: 16px;
                    position: absolute;
                    left: 50%;
                    bottom: 0;
                    transform: translateX(-50%);
                    opacity: 0;
                    transition: opacity .3s;
                    background: url("~@/assets/icons/<EMAIL>") no-repeat center;
                    background-size: 16px;
                    &:hover{
                        background: url("~@/assets/icons/<EMAIL>") no-repeat center;
                        background-size: 16px;
                    }
                }
                &:hover{
                    & .td-add-btn{
                        opacity: 1;
                    }
                }
                &:last-of-type{
                  border: none;
                }
            }
            &-row{
                display: flex;
                position: relative;
                min-width: 100%;
                min-height: 56px;
                &:nth-of-type(2n){
                    background: #FAFAFA;
                }
            }
            & .freeze-column{
                width: 160px;
                display: flex;
                padding-left: 24px;
                padding-right: 12px;
                align-items: center;
                justify-content: space-between;
                .el-icon-plus{
                    color: #C5C5C5;
                    font-weight: 900;
                    font-size: 16px;
                    cursor: pointer;
                    height: 24px;
                    line-height: 24px;
                    &:hover{
                        color: #2695F1;
                    }
                }
                .jacp-erp__name{
                    font-weight: 500;
                    transform: translate(4px, -1px);
                }
            }
        }
        .task-list{
            width: 100%;
            min-height: 44px;
            position: absolute;
            left: 0;
            top: 0;
            padding-top: 16px;
            z-index: 1;
            &-item{
                display: flex;
                font-size: 12px;
                color: #333;
                line-height: 18px;
                position: relative;
                &>div{
                    box-sizing: border-box;
                    & div{
                        cursor: pointer;
                        flex: 1;
                        width: 0;
                        text-align: center;
                        display: flex;
                        & span{
                            &:hover{
                                text-decoration: underline;
                            }
                            padding: 0 5px;
                            width: 100%;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            text-align: center;
                            opacity: 1;
                        }
                        & i{
                            cursor: ew-resize;
                            width: 20px;
                            background: url("./../../../../assets/icons/draggable.png") no-repeat;
                            background-size: 4px 8px;
                            background-position: center;
                            line-height: 26px;
                        }
                    }
                    &.round-left{
                        border-top-left-radius: 12px;
                        border-bottom-left-radius: 12px;
                    }
                    &.round-right{
                        border-top-right-radius: 12px;
                        border-bottom-right-radius: 12px;
                    }
                    & .leftPort{
                        float: left;
                    }
                    & .rightPort{
                        float: right;
                    }
                }
                & .task{
                    height: 24px;
                    line-height: 24px;
                    margin: 4px 0;
                    display: flex;
                    width: 0;
                    position: relative;
                    &.bg-otherSpace{
                        // background-color: #c5c5c5;
                        border: 1.5px solid #c5c5c5;
                    }
                }

                & .notask{
                    background: transparent;
                    color: transparent;
                    height: 24px;
                    margin: 4px 0;
                    opacity: 1;
                }
            }
            &-load{
                flex: 1 0 auto;
                width: 80px;
                text-align: center;
                line-height: 24px;
                height: 24px;
                border-right: 1px solid transparent;
                border-radius: 4px;
                background-clip: content-box;
                &:hover{
                    box-shadow:0px 2px 6px 0px rgba(51,51,51,0.3);
                }
                & span{
                    display: inline-block;
                    width: 90%;
                }
            }
            &-label{
                display: none;
                position: absolute;
                left: 0;
                top: 100%;
                color: #333;
                font-size: 12px;
                white-space: nowrap;
                &.align-right{
                    left: inherit;
                    right: 0;
                    text-align: end;
                }
            }
        }
    }
    &-btn{
        border: 1px solid rgba(225,225,225,1);
        border-radius: 4px;
        padding: 4px;
        height: 28px;
        margin: 0 4px;
        & .el-switch__label{
            color: rgba(153,153,153,1);
            &.is-active {
                color: #2695F1;
            }
            & span{
                font-size: 12px;
                font-family: PingFangSC-Regular;
            }
        }
        & .el-switch__core{
            transform: scale(.8);
        }
        & .el-switch__label--right{
            margin-left: 2px;
        }
    }

    .seperate-line{
        height: 16px;
        margin: var(--gutter--medium);
        border-right: 1px solid #f1f1f1;
    }
    .set-task-days(@n, @i: 1) when (@i <= @n) {
        .day@{i}{
            flex: @i;
        }
        .set-task-days(@n, @i + 1);
    };
    .set-task-days(365);

    .setTaskColor(@colors, @i: 1) when (@i <= length(@colors)) {
        @color: extract(@colors, @i);
        .bg-@{i} {
            background-color: fade(@color, 30%);
            color: #333;
            &:hover{
                background-color: @color;
                color: white;
            }
            &.sameCard{
                background-color: @color;
                color: white;
                &:hover{
                    background-color: @color;
                    color: white;
                }
            }
        }
        .setTaskColor(@colors, @i + 1);
    };
    .setTaskColor(@stepColors);
    & .workload-level0{
        color:rgba(38,149,241,1);
        background: rgba(204,239,255,1);
    }
    & .workload-level1{
        color:rgba(38,149,241,1);
        background: rgba(162,223,255,1);
    }
    & .workload-level2{
        color:rgba(255,255,255,1);
        background: rgba(80,183,255,1);
    }
    & .workload-level3{
        color:rgba(255,255,255,1);
        background: rgba(38,149,241,1);
    }
    & .workload-level4{
        color:rgba(255,255,255,1);
        background: rgba(20,112,201,1);
    }
    & .workload-level5{
        color:rgba(255,255,255,1);
        background: rgba(2,58,127,1);
    }
}
</style>
