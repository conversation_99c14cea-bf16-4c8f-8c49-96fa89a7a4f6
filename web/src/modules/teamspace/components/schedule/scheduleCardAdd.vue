<template>
    <div
        class="calendar-card-add"
        v-show="showAddCard"
    >
        <el-form
            ref="cardForm"
            :model="formData"
            :rules="rules"
            :inline-message="true"
        >
            <el-form-item prop="name">
                <el-input
                    ref="cardName"
                    size="mini"
                    placeholder="卡片名称"
                    v-model="formData.name"
                />
            </el-form-item>
            <el-form-item prop="typeId">
                <el-select
                    size="mini"
                    v-model="formData.typeId"
                    placeholder="卡片类型"
                    style="width: 100%;"
                >
                    <el-option
                        v-for="item in typeList"
                        :key="item.name"
                        :label="item.name"
                        :value="item.code"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-col :span="19">
                    <el-form-item>
                        <el-checkbox
                            v-if="$store.state.chilldteamspace.requiredReview === 1"
                            size="mini"
                            v-model="formData.reviewFlag"
                            style="margin-right: 10px;"
                            :true-label="1"
                            :false-label="0"
                        >
                            强制评审
                        </el-checkbox>
                        <el-checkbox
                            size="mini"
                            v-model="formData.confidential"
                            style="margin-right: 10px;"
                        >
                            保密
                        </el-checkbox>
                    </el-form-item>
                </el-col>
                <el-col :span="5">
                    <el-form-item>
                        <el-button
                            type="text"
                            size="mini"
                            @click="saveCard"
                        >
                            <i class="el-icon-check" />
                        </el-button>
                        <el-button
                            type="text"
                            size="mini"
                            @click="$emit('update:showAddCard', false)"
                        >
                            <i class="el-icon-close" />
                        </el-button>
                    </el-form-item>
                </el-col>
            </el-form-item>
        </el-form>
    </div>
</template>

<script type="text/javascript">
import { card as CardModel, space as SpaceModel } from '@/models/teamspace';

export default {
    props: {
        spaceId: {
            type: Number,
        },
        sprintId: {
            type: Number,
        },
        showAddCard: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            formData: {
                name: '',
                typeId: '',
                confidential: false,
                reviewFlag: 0,
                pmpProjectId: (this.pmpProject || {}).pmpProjectId,
                pmpProjectName: (this.pmpProject || {}).pmpProjectName,
            },
            rules: {
                name: [{
                    required: true,
                    message: '卡片名称不能为空',
                    trigger: 'change',
                }, {
                    min: 3,
                    max: 80,
                    message: '长度在3 - 80个字',
                    trigger: 'change',
                }],
                typeId: [{
                    required: true,
                    message: '请选择卡片类型',
                }],
            },
            typeList: [],
            pmpProject: {},
        };
    },
    methods: {
        saveCard() {
            // 设置默认卡片状态
            this.formData.statusId = this.defaultStatus;
            this.formData.sprintId = this.sprintId;
            this.formData.spaceId = this.spaceId;
            this.$refs.cardForm.validate(this.formData).then(() => {
                CardModel.save(this.formData).then(({ id } = {}) => {
                    this.$emit('update:showAddCard', false);
                    // 将新建卡片添加到当前卡片列表
                    this.$emit('newCard', {
                        cardName: this.formData.name,
                        id,
                    });
                    this.$notify({
                        title: '保存成功！',
                        type: 'success',
                        duration: 2000,
                    });
                });
            });
        },
        resetForm() {
            this.$refs.cardForm.resetFields();
            this.formData = {
                name: '',
                typeId: '',
                statusId: this.defaultStatus,
                // planId: this.sprintId || -1,
                reviewFlag: 0,
                confidential: false,
                pmpProjectId: (this.pmpProject || {}).pmpProjectId,
                pmpProjectName: (this.pmpProject || {}).pmpProjectName,
            };
        },
    },
    watch: {
        showAddCard(value) {
            if (value) {
                SpaceModel.getSpaceCardType(this.$route.params.spaceKey).then((types) => {
                    const typeList = types.fields[0].options;
                    this.typeList = typeList.filter(item => item.show);
                    this.formData.typeId = this.typeList[0] ? this.typeList[0].code : '';
                });
                // 查询默认关联项目
                SpaceModel.getDetail().then((data) => {
                    this.pmpProject = data.pmpProject || {};
                    Object.assign(this.formData, this.pmpProject);
                });
                this.$nextTick(() => {
                    this.$refs.cardName.$el.querySelector('.el-input__inner').focus();
                });
            } else {
                this.resetForm();
            }
        },
        sprintId() {
            this.$emit('update:showAddCard', false);
        },
    },
    computed: {
        defaultStatus() {
            const validateStatusList = this.$store.state.chilldteamspace.availableStatusList;
            if (validateStatusList && validateStatusList[0]) {
                return validateStatusList[0].statusCode;
            }
            return '';
        },
    },
};
</script>

<style lang="less">
.calendar-card-add{
    width: 300px;
    & .el-form .el-input,.el-form .el-input__inner{
        width: 100%;
    }
    & .el-form .el-form-item__content{
        line-height: 20px;
        & .el-button{
            padding: 2px 0;
            & i{
                font-size: 16px;
                font-weight: 900;
                padding-left: 5px;
                transition: transform .1s;
                &:hover{
                    transform: scale(1.1);
                }
            }
        }
        & .el-icon-check{
            color: #67C23A;
        }
        & .el-icon-close{
            color: #F56C6C;
        }
        & .el-checkbox__label{
            font-size: 12px;
        }
    }
    & .el-form .el-form-item{
        margin-bottom: 5px;
    }
}
</style>
