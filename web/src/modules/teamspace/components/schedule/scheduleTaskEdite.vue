<template>
    <el-dialog
        title="任务详情"
        :visible="showEditDialog"
        :before-close="closeDialog"
        :close-on-click-modal="false"
        class="schedule-task_Edit schedule-task-dialog"
        width="804px"
        append-to-body
    >
        <div class="schedule-task_Edit__box">
            <div class="schedule-task_Edit__list">
                <ul class="list-box">
                    <li
                        :class="{'active': taskIndex === index}"
                        v-for="(item, index) in taskList"
                        :key="item.id"
                        @click="changeTask(index)"
                    >
                        <span :title="item.content">{{ item.content }}</span>
                        <i
                            class="el-icon-delete"
                            slot="reference"
                            @click="delCardTask(item, index)"
                        />
                    </li>
                </ul>
            </div>
            <div class="schedule-task_Edit__detail">
                <card-task-form
                    ref="editTaskForm"
                    :card-task-info="formData"
                    :members="memberList"
                    :space-id="spaceId"
                />
            </div>
        </div>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-button
                @click="closeDialog"
                size="mini"
            >
                取 消
            </el-button>
            <el-button
                type="primary"
                @click="addTask"
                size="mini"
            >
                保 存
            </el-button>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import { mapState } from 'vuex';
import CardModel from '@/models/card';
import CardTaskModel from '@/modules/card/models/CardTask';
/* eslint-disable */
import mixinCardTask from '@/modules/card/mixins/mixin.cardTask';
import cardTaskForm from '@/modules/_components/cardTaskForm';

export default {
    mixins: [mixinCardTask],
    components: {
        cardTaskForm,
    },
    props: {
        showEditDialog: {
            type: Boolean,
            default: false,
        },
        personInfo: {
            type: Object,
            default: () => {},
        },
        currentTask: {
            type: Object,
            default: () => {},
        },
        card: {
            type: Object,
            default: () => {},
        },
        memberList: {
            type: Array,
            default: () => [],
        },
        spaceId: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            taskList: [],
            taskIndex: 0,
            cardList: [],
            sprintList: [],
            formData: {
                erp: '',
                name: '',
                sprint: '',
                cardId: '',
                content: '',
                remainingHours: undefined,
                plannedHours: undefined,
                // duration: [],
                startDate: '',
                deadline: '',
                roleCode: undefined,
                status: 1,
            },
        };
    },
    created() {
        this.resetCardTasksInfo();
    },
    methods: {
        closeDialog() {
            this.$emit('update:showEditDialog', false);
        },
        // 初始化过滤条件下拉数据
        async getCurrentTaskList() {
            const { cardId } = this.card;
            const { startDate, endDate } = this.currentTask;
            this.taskList = await CardTaskModel.getListByDate({
                erp: this.personInfo.processorErp,
                cardId,
                startDate: startDate.dateNum,
                endDate: endDate.dateNum,
            }) || [];
            if (this.taskList.length) {
                this.changeTask(0);
            }
        },
        addTask() {
            const formData = this.$refs.editTaskForm.formData;
            const {
                cardId,
            } = formData;
            this.$refs.editTaskForm.$refs.cardTaskForm.validate((valid) => {
                if (valid) {
                    CardTaskModel.update(formData, cardId).then(() => {
                        this.$notify({
                            title: '保存成功！',
                            type: 'success',
                            duration: 2000,
                        });
                        this.saveDefaultRole(formData);
                        // 更新人资日历数据
                        this.$emit('refreshlist');
                        this.closeDialog();
                    });
                }
            });
        },
        changeTask(index) {
            const task = this.taskList[index];
            this.taskIndex = index;
            Object.assign(this.formData, task, {
                startDate: task.startDate, 
                deadline: task.deadline,
                roleCode: this.customizedConfigRole.filter(
                    item => item.code === task.roleCode,
                ).length > 0 ? task.roleCode : undefined,
                // 追加一个原始值，用于保存的时候进行对比，如果发生变化了，就去更新默认职责
                originRoleCode: task.originRoleCode || task.roleCode,
            });
            this.getDefaultRole(task.erp, this.spaceId, true);
        },
        changeProcessor(erp) {
            this.memberList.forEach((item) => {
                if (item.processorErp === erp) {
                    this.formData.name = item.processorName;
                }
            });
            this.getDefaultRole(erp, this.spaceId);
        },
        delCardTask({ id }, index) {
            this.$confirm('此操作将永久删除该任务, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                CardModel.delCardTask(id).then(() => {
                    this.$notify({
                        title: '删除成功！',
                        type: 'success',
                        duration: 2000,
                    });
                    this.taskList.splice(index, 1);
                    if (this.taskList.length > 0) {
                        if (this.taskIndex === index) {
                            this.changeTask(0);
                        }
                    } else {
                        this.closeDialog();
                    }
                    this.$emit('refreshlist');
                });
            });
        },
        getDefaultRole(erp, spaceId, initFlag) {
            CardModel.getRoleCodeDefault(erp, spaceId).then((data) => {
                if (data && data.length > 0) {
                    const defaultRole = data[0];
                    this.roleInfo = defaultRole;
                    if (initFlag) {
                        // 首次加载任务只获取职位id 优先保留原任务数据
                        this.formData.roleCode = this.formData.roleCode || defaultRole[spaceId];
                    } else {
                        // 人员切换导致的二次加载人员职位信息 直接设置查询到的默认职位
                        this.formData.roleCode = defaultRole[spaceId];
                    }
                } else {
                    this.roleInfo = {};
                }
            });
        },
        saveDefaultRole({ erp, roleCode }) {
            const params = { [this.spaceId]: roleCode };
            if (this.roleInfo.id) {
                CardModel.updateConfigRoleDefault(this.roleInfo.id, erp, params);
            } else {
                CardModel.saveConfigRoleDefault(erp, params);
            }
        },
        resetCardTasksInfo() {
            this.taskIndex = 0;
            this.formData.sprint = this.card.sprintId;
            this.formData.cardId = this.card.cardId;
            if (Object.keys(this.currentTask).length > 0) {
                this.getCurrentTaskList();
            }
        },
    },
    watch: {
        showEditDialog: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.resetCardTasksInfo();
                } else if (this.$refs.editTaskForm) {
                    this.$refs.editTaskForm.resetForm();
                    this.$refs.editTaskForm.clear();
                }
            },
        },
    },
    computed: {
        showDialog() {
            return this.showEditDialog;
        },
        ...mapState('chilldteamspace', {
            customizedConfigRole: 'customizedConfigRoleList',
        }),
    },
};
</script>
<style lang="less">
.schedule-task_Edit{
    & .el-form-item__content{
        font-size: 12px;
    }
    & .el-input,.el-input__inner{
        width: 300px;
    }
    & .el-form-item{
        margin-bottom: 12px;
    }
    &__box{
        display: flex;
        width: 100%;
        height: 100%;
        margin-top: 5px;
    }
    &__detail{
        flex: 1;
        padding-left: 16px;
        margin-bottom: 8px;
    }
    &__list{
        width: 140px;
        border-right: 1px #dcd9d9 solid;
        & ul{
            height: 400px;
            overflow-y: auto;
            list-style: none;
            font-size: 0px;
            padding: 0;
            & li{
                box-sizing: border-box;
                border-left: 4px solid #fff;
                background-color: #f7f7f7;
                border-left: 4px solid #d5dadc;
                &:hover{
                    background-color: #e3eefa;
                    border-left: 4px solid #39b6e1;
                }
                &.active{
                    border-left: 4px solid #39b6e1;
                    background-color: #e3eefa;
                }
                & span{
                    display: inline-block;
                    max-width: 110px;
                    height: 32px;
                    font-size: 14px;
                    margin-bottom: 2px;
                    line-height: 32px;
                    overflow-x: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    padding-left: 5px;
                }
                & i{
                    float: right;
                    font-size: 16px;
                    margin-top: 8px;
                    margin-right: 6px;
                    cursor: pointer;
                    &:hover{
                        color: #2286d9;
                    }
                }
            }
        }
    }
}
</style>
