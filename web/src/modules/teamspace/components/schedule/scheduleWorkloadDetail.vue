<template>
    <div class="jacp-workload-detail">
        <div
            class="jacp-workload-detail__header"
            :class="`workload-level${level}`"
        >
            <p class="date">
                {{ task.date | dateFormat }}
            </p>
            <p class="workload-info">
                <span>总计划工时：{{ task.totalPlanHour }}h</span>
                <span>饱和度：{{ task.workLoad !== 0 ? task.workLoad.toFixed(1) : task.workLoad }}</span>
            </p>
        </div>
        <div class="jacp-workload-detail__content">
            <ul>
                <li
                    class="jacp-workload-detail__card"
                    v-for="card in task.cards"
                    :key="card.cardId"
                    @click="$emit('on-card-click', card)"
                >
                    <span :title="card | calendarTitle">{{ card | calendarTitle }}</span>
                    <span>计划工时：{{ card.planHour }}h</span>
                </li>
            </ul>
        </div>
    </div>
</template>
<script>
export default {
    name: 'CalenderWorkloadDetail',
    props: {
        task: {
            type: Object,
            default: () => {},
        },
        level: {
            type: Number,
            default: 0,
        },
    },
    filters: {
        dateFormat(value) {
            const curDate = new Date(value);
            const year = curDate.getFullYear();
            const month = curDate.getMonth() + 1;
            const date = curDate.getDate();
            const day = curDate.getDay();
            const weekList = ['日', '一', '二', '三', '四', '五', '六'];
            return `${year}年${month > 9 ? month : `0${month}`}月${date > 9 ? date : `0${date}`}日 星期${weekList[day]}`;
        },
        calendarTitle(cardItem) {
            if (cardItem.taskSource === 2 || cardItem.taskSource === 3) {
                return cardItem.taskName;
            }
            return cardItem.cardName;
        },
    },
};
</script>
<style lang="less">
.jacp-workload-detail{
    &__header{
        height: 88px;
        border-radius:4px 4px 0px 0px;
        padding: 24px 16px;
        & p{
            overflow : hidden;
            margin: 0;
            &.date{
                line-height:20px;
                height: 30px;
                font-size:14px;
                font-weight:400;
            }
            &.workload-info{
                line-height:20px;
                height: 20px;
                font-size:12px;
                & span:first-child{
                    margin-right: 24px;
                }
            }
        }
    }
    &__content{
        padding: 8px 0px;
        font-size: 14px;
        min-height: 80px;
        max-height: 140px;
        overflow-y: auto;
    }
    &__card{
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        height: 32px;
        line-height: 32px;
        cursor: pointer;
        color: #999999;
        padding: 0px 16px;
        &:hover{
            background:rgba(233,244,253,1);
            color: #2695F1;
            & span:first-child{
                color: #2695F1;
            }
        }
        & span:first-child{
            color: #333333;
            width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        & span:last-child{
            text-align: right;
            width: 100px;
        }
    }
    & .workload-level0{
        color:rgba(38,149,241,1);
        background: rgba(204,239,255,1);
    }
    & .workload-level1{
        color:rgba(38,149,241,1);
        background: rgba(162,223,255,1);
    }
    & .workload-level2{
        color:rgba(255,255,255,1);
        background: rgba(80,183,255,1);
    }
    & .workload-level3{
        color:rgba(255,255,255,1);
        background: rgba(38,149,241,1);
    }
    & .workload-level4{
        color:rgba(255,255,255,1);
        background: rgba(20,112,201,1);
    }
    & .workload-level5{
        color:rgba(255,255,255,1);
        background: rgba(2,58,127,1);
    }
}
.jacp-schedule-popper{
    padding: 0;
    margin-left: 6px;
    border: none;
    box-shadow: 0 12px 40px 6px rgba(0,0,0,.2);
}
</style>
