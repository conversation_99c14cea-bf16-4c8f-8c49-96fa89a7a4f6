<template>
    <div>
        <el-table
            ref="multipleTable"
            :row-style="rowClass"
            :data="formData.fields[0] ? formData.fields[0].options : []"
            border
            tooltip-effect="dark"
        >
            style="width: 100%"
            <el-table-column
                prop="name"
                label="卡片类型"
                width="250"
            />
            <el-table-column label="是否显示">
                <template slot-scope="scope">
                    <el-checkbox
                        v-model="scope.row.show"
                        :disabled="scope.row.disabled || read"
                    /><span>&nbsp;显示</span>
                </template>
            </el-table-column>
        </el-table>
        <jacp-button
            v-if="spacePrivilage.updateSpace && read"
            @click="read = !read"
            type="primary"
            class="teamspace-spacesetting__button-first"
        >
            编辑
        </jacp-button>
        <jacp-button
            v-if="spacePrivilage.updateSpace && !read"
            type="primary"
            class="teamspace-spacesetting__button-first"
            @click="saveCardType"
        >
            保存
        </jacp-button>
        <jacp-button
            v-if="spacePrivilage.updateSpace && !read"
            @click="getSpaceCardType"
            class="teamspace-spacesetting__button-first"
        >
            取消
        </jacp-button>
    </div>
</template>

<script type="text/javascript">
// import SpaceModel from '@/models/space';

export default {
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            formData: {
                fields: [],
            },
            read: true,
        };
    },
    created() {
        this.getSpaceCardType();
    },
    methods: {
        async getSpaceCardType() {
            this.read = true;
            this.formData = JSON.parse(JSON.stringify(await this.$store.dispatch('chilldteamspace/fetchTypeListOptions', this.spaceKey)));
        },
        saveCardType() {
            this.$store.dispatch('chilldteamspace/updateTypeListOptions', this.formData)
                .then(() => {
                    this.read = true;
                    this.$message.success('保存成功！');
                });

            /* SpaceModel.saveSpaceCardType(this.formData).then(() => {
                this.getSpaceCardType();
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            }); */
        },
        rowClass(scope) {
            if (scope.row.code === 1) {
                return { display: 'none' };
            }
            return {};
        },
    },
    watch: {
        spaceKey() {
            this.getSpaceCardType();
        },
    },
};
</script>

<style lang="less"></style>
