<template>
    <div class="space-perm-assign">
        <div
            v-for="item in promiseOptions"
            :key="item.resourceCode"
            class="space-perm-assign-table"
        >
            <div>
                <el-row>
                    <el-col :span="6">
                        {{ item.resourceName }}
                    </el-col>
                    <el-col :span="6">
                        管理员
                    </el-col>
                    <el-col :span="6">
                        成员
                    </el-col>
                    <el-col :span="6">
                        游客
                    </el-col>
                </el-row>
                <el-row
                    v-for="info in item.rolePermissionVOList"
                    :key="info.promissionId"
                >
                    <el-col :span="6">
                        {{ info.operationName }}
                    </el-col>
                    <el-col :span="18">
                        <el-checkbox-group
                            v-model="info.roleCodes"
                            :disabled="!editing"
                        >
                            <el-checkbox label="spaceAdmin">
                                {{ '' }}
                            </el-checkbox>
                            <el-checkbox label="spaceMember">
                                {{ '' }}
                            </el-checkbox>
                            <el-checkbox
                                label="spaceGuest"
                                :disabled="item.resourceCode === 'card' && info.permissionCode === 'suspend-restore-card'"
                            >
                                {{ '' }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-col>
                </el-row>
            </div>
        </div>
        <template v-if="spacePrivilage.updateSpace">
            <div
                v-if="!editing"
                class="buttonDiv"
            >
                <el-button
                    @click="editing = true"
                    type="primary"
                    size="small"
                >
                    编辑
                </el-button>
            </div>
            <div
                v-else
                class="buttonDiv"
            >
                <el-button
                    @click="cancelSetting"
                    size="small"
                >
                    取消
                </el-button>
                <el-button
                    @click="saveSetting"
                    type="primary"
                    size="small"
                >
                    保存
                </el-button>
            </div>
        </template>
    </div>
</template>

<script type="text/javascript">
import SpaceModel from '@/models/space';
import SecurityClient from '@/models/security';
import { getSpaceId } from '@/plugins/utils';

export default {
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            editing: false,
            saving: false,
            scopeCode: '',
            promiseOptions: [],
        };
    },
    created() {
        this.init();
    },
    methods: {
        init() {
            this.editing = false;
            SpaceModel.getScopeCode(this.spaceKey).then((data) => {
                this.scopeCode = data;
            });
            this.getPermSetting(getSpaceId(this.spaceKey));
        },
        getPermSetting(spaceId) {
            SecurityClient.getSpacePermSettingOptions(spaceId).then((e) => {
                this.promiseOptions = e || [];
            });
        },
        saveSetting() {
            if (this.saving || !this.scopeCode) {
                return;
            }
            this.saving = true;
            SecurityClient.saveSpacePermSetting(this.scopeCode, this.promiseOptions).then(() => {
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
                this.editing = false;
                this.saving = false;
            });
        },
        cancelSetting() {
            this.editing = false;
            this.getPermSetting(getSpaceId(this.spaceKey));
        },
    },
    watch: {
        spaceKey() {
            this.init();
        },
    },
};
</script>

<style lang="less">
    .space-perm-assign{
        &-table{
            width: 800px;
            .el-checkbox {
                margin-right: 176px;
            }
        }
        font-size: 14px;
        .role-header {
            display: flex;
            justify-content:center;
        }
        .role-content {
            display: flex;
            justify-content:center;
        }
        .buttonDiv {
            margin-top: 20px;
        }
        & .el-row{
          margin-bottom: 20px;
        }
    }
</style>
