<template>
    <div class="space-setting-process">
        <el-alert
            class="j-blockquote j-blockquote--base j-mgb16"
            title="请以开始状态到目标状态的查看方式 ，选择允许流转的步骤，一旦选择并保存成功将在整个团队空间中生效，请谨慎操作。"
            show-icon
        />
        <div class="toolbar">
            <el-radio-group value="卡片">
                <el-radio-button
                    label="卡片"
                    class="jacp-radio-button--plain"
                />
                <el-radio-button
                    disabled
                    label="任务"
                />
            </el-radio-group>
            <el-radio-group style="float: right">
                <el-radio-button label="列表视图" />
                <el-radio-button
                    disabled
                    label="流程视图"
                />
            </el-radio-group>
        </div>
        <el-table
            :data="tableData"
            border
            :header-cell-style="{'text-align':'center'}"
            :cell-style="{'text-align':'center'}"
        >
            <el-table-column
                width="180"
            >
                <template slot-scope="scope">
                    <span
                        :style="{
                            color: scope.row.color,
                        }"
                    >{{ scope.row.statusName }} </span>
                </template>
            </el-table-column>
            <el-table-column
                v-for="status in statusList"
                :key="status.statusCode"
                :prop="status.statusCode+ ''"
            >
                <template
                    slot="header"
                >
                    <span
                        :style="{
                            color: status.color,
                        }"
                    >{{ status.statusName }}</span>
                </template>
                <template slot-scope="scope">
                    <div
                        class="space-setting-process-stepcell"
                        v-if="getStepFromRow(scope)
                            && status.statusCode !== scope.row.statusCode"
                    >
                        <el-checkbox
                            v-model="getStepFromRow(scope).stepStatus"
                            :disabled="disabled"
                            :true-label="1"
                            :false-label="0"
                            @change="($event) => handleStepStatusChange($event, getStepFromRow(scope))"
                        />
                        <jacp-icon
                            name="el-icon-setting"
                            v-if="!disabled || getStepFromRow(scope).on"
                            active
                            :class="{
                                'step-on': getStepFromRow(scope).on
                            }"
                            @click.native="() => editStep(getStepFromRow(scope))"
                        />
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script type="text/javascript">
import { getSpaceId } from '@/plugins/utils';
import { stageMap, generalStageMap } from '@/modules/teamspace/constant';
import http from '@/plugins/http';
import Dialog from '@/models/dialog';
import TransitionStep, { STEP_STATUS } from '$module/models/transitionStep';
import Workitem from '$module/models/workitem';
import EditStep from './editStep';

const getStageColor = (stageId) => {
    const colorMap = { ...stageMap, ...generalStageMap };
    return colorMap[stageId] || {};
};
export default {
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            statusList: [],
            steps: [],
            spaceRoleList: null,
        };
    },
    methods: {
        async fetchSpaceTransitionInfo() {
            this.steps = await TransitionStep.loadSteps(this.spaceId);
        },
        async fetchSpaceRoleList() {
            this.spaceRoleList = await Workitem.getRoles(this.spaceId);
        },
        async fetchSpaceStatusInfo() {
            const { spaceId } = this;
            this.statusList = await http.get('v1/spaces/cardStatuses', {
                params: { spaceId },
            }).then((list = []) => list.map(status => ({
                ...status,
                color: getStageColor(status.stageId).color,
            }))).then((data) => {
                this.fetchSpaceTransitionInfo();
                return data;
            });
        },
        // 整体保存
        saveSpaceTransitionInfo() {
            http.post(`v1/space/${this.spaceId}/transition/step`, this.steps).then(() => {
                this.$message.success('保存成功');
            });
        },

        statusName(idx) {
            return (this.statusList[idx - 1] || {}).statusName;
        },
        findIndex({ sourceStatus, targetStatus } = {}) {
            return `${sourceStatus}_${targetStatus}`;
        },

        getStepFromRow({ row, column } = {}) {
            return row[column.property];
        },
        // 局部保存step
        editStep(step, { beforeCancel = () => {} } = {}) {
            return Dialog.confirm({
                confirmDisplay: !this.disabled,
                title: '步骤设置',
                slot: EditStep,
                slotProps: {
                    step,
                    roles: this.spaceRoleList,
                    status: this.statusList,
                    disabled: this.disabled,
                },
                confirmBtnText: '保存',
                beforeConfirm: vm => step.update(vm.innerValue),
                beforeCancel,
            });
        },
        // 在变更开启step状态的时候进行二次确认
        handleStepStatusChange(newVal, step = {}) {
            const { sourceStatus, targetStatus } = TransitionStep.getStartAndEndStatus(step, this.statusList);
            // 试图关闭曾经设置过步骤的时候
            if (newVal === STEP_STATUS.OFF) {
                const tips = this.$t('jacp.teamspace.transition.stepPermissionConfirm.off', {
                    sourceStatusName: sourceStatus.statusName,
                    targetStatusName: targetStatus.statusName,
                });
                this.$confirm(
                    tips,
                    '提示',
                    {
                        type: 'warning',
                        dangerouslyUseHTMLString: true,
                    },
                ).then(() => {
                    // step的状态变更改调用新的批量变更状态的接口
                    TransitionStep.updateStatusBatch(step.spaceId, [step]);
                    // step.update(step);
                }).catch(() => {
                    step.stepStatus = STEP_STATUS.ON;
                });
            }

            // 从未勾选到勾选的时候
            if (newVal === STEP_STATUS.ON) {
                const tips = this.$t('jacp.teamspace.transition.stepPermissionConfirm.on', {
                    sourceStatusName: sourceStatus.statusName,
                    targetStatusName: targetStatus.statusName,
                });
                this.$confirm(
                    tips,
                    '提示',
                    {
                        type: 'success',
                        confirmButtonText: '仅启用',
                        cancelButtonText: '进入下一步配置后启用',
                        dangerouslyUseHTMLString: true,
                        distinguishCancelAndClose: true,
                    },
                ).then(() => {
                    TransitionStep.updateStatusBatch(step.spaceId, [step]);
                    // step.update(step);
                }).catch((action) => {
                    // 区分取消和关闭
                    if (action === 'cancel') {
                        this.editStep(step, {
                            beforeCancel: () => {
                                step.stepStatus = STEP_STATUS.OFF;
                            },
                        });
                    } else {
                        step.stepStatus = STEP_STATUS.OFF;
                    }
                });
            }
        },
    },
    computed: {
        spaceId() {
            return getSpaceId(this.spaceKey);
        },
        stepsMap() {
            return this.steps.reduce((result, step = {}) => {
                result[this.findIndex(step)] = step;
                return result;
            }, {});
        },
        tableData() {
            const { stepsMap, statusList, findIndex } = this;
            return statusList.map((status) => {
                const sourceStatus = status.statusCode;
                const stepsMapByTargetStatus = statusList.reduce((result, targetStatusObj) => {
                    const key = findIndex({
                        sourceStatus,
                        targetStatus: targetStatusObj.statusCode,
                    });

                    result[targetStatusObj.statusCode] = stepsMap[key];
                    return result;
                }, {});
                return {
                    ...status,
                    ...stepsMapByTargetStatus,
                };
            });
        },
        disabled() {
            return !this.spacePrivilage.updateSpace;
        },
    },
    watch: {
        spaceKey: {
            immediate: true,
            handler() {
                this.fetchSpaceStatusInfo();
                this.fetchSpaceRoleList();
            },
        },
    },
};
</script>

<style lang="less">
.space-setting-process {
    .toolbar {
        margin: 8px 0px 16px;
    }
    .el-table th {
        background: #F7F8F9;
    }
    .el-table tr:first-of-type th:first-of-type:before {
        content: '开始状态';
        position: absolute;
        height: 1px;
        top: 20px;
        left: 0;
        margin-left: 7px;
    }
    .el-table tr:first-of-type th:first-of-type:after {
        content: '目标状态';
        position: absolute;
        top: 7px;
        right: 0;
        margin-right: 7px;
    }
    .el-table tr:first-of-type th:first-of-type .cell {
        position: absolute;
        top: 0;
        left: 0;
        height: 1px;
        background-color: #EBEEF5;
        display: block;
        text-align: center;
        transform: rotate(11.5deg);
        transform-origin: top left;
    }
    .el-button--mini, .el-button--small {
        border-radius: 4px;
    }
    &-stepcell{
        position: relative;
        // 开启步骤后，小齿轮始终显示；没开启的，有编辑权限的用户可以在hover的时候显示
        .el-icon-setting:not(.step-on){
            visibility: hidden;
        }

        .el-icon-setting{
            cursor: pointer;
            position: absolute;
            right: 0px;
            top: 3px;
        }
        &:hover .el-icon-setting{
            visibility: visible;
        }
    }
}
</style>
