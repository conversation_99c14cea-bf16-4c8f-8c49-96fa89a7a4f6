<template>
    <el-form
        class="j-form edit-step-form"
        :disabled="disabled"
    >
        <el-form-item
            label="步骤流程"
        >
            <ul class="edit-step-status j-mgt20">
                <li
                    class="edit-step-status-node"
                    :style="`--border-color: ${pointStatus.sourceStatus.color}`"
                >
                    <span
                        slot="title"
                        :style="{
                            color: pointStatus.sourceStatus.color,
                        }"
                    >{{ pointStatus.sourceStatus.statusName }}</span>
                </li>
                <li class="edit-step-status-line">
                    <i class="el-icon-arrow-right" />
                    <el-form-item
                        label="步骤名称"
                        class="edit-step-stepName"
                    >
                        <el-input v-model="innerValue.stepName" />
                    </el-form-item>
                </li>
                <li
                    class="edit-step-status-node"
                    :style="`--border-color: ${pointStatus.targetStatus.color}`"
                >
                    <span
                        slot="title"
                        :style="{
                            color: pointStatus.targetStatus.color,
                        }"
                    >{{ pointStatus.targetStatus.statusName }}</span>
                </li>
            </ul>
        </el-form-item>
        <el-form-item
            label="步骤权限"
            v-if="innerValue.permission"
        >
            <el-alert
                title="设置当前步骤流程下有权限操作的人员或者角色，点击保存后生效。"
                type="success"
                :closable="false"
            />
            <el-radio-group
                v-model="innerValue.permission.permissionType"
                @change="() => innerValue.changePermission(innerValue.permission)"
                class="j-mgb8 j-mgt16"
            >
                <el-radio
                    :label="item.value"
                    :key="item.value"
                    v-for="item in permissionTypeList"
                >
                    {{ item.label }}
                </el-radio>
            </el-radio-group>
            <template v-if="innerValue.permission.permissionType === PERMISSIONS_TYPE.ROLE && innerValue.permission.roleCodes">
                <el-card shadow="never">
                    <el-checkbox-group
                        v-model="innerValue.permission.roleCodes"
                        style="display: flex;"
                    >
                        <el-checkbox
                            :label="item.roleCode"
                            :key="item.roleCode"
                            v-for="item in roles"
                        >
                            {{ item.roleName }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-card>
            </template>
            <template v-if="innerValue.permission.permissionType === PERMISSIONS_TYPE.USER && innerValue.permission.users">
                <el-card shadow="never">
                    <form-users-group
                        v-model="innerValue.permission.users"
                        :load-data="userFilter($store.state.chilldteamspace.allMemberList)"
                        :disabled="disabled"
                    />
                </el-card>
                <el-button
                    type="text"
                    style="float:right"
                    @click="innerValue.permission.users = [...$store.state.chilldteamspace.allMemberList]"
                >
                    导入团队空间全部成员
                </el-button>
            </template>
        </el-form-item>
        <!--  <el-form-item label="约束字段">
            <el-alert
                title="进入【目标】步骤节点时，配置需要补充的字段"
                type="success"
                :closable="false"
            />
        </el-form-item> -->
    </el-form>
</template>
<script>
import { PERMISSIONS_TYPE } from '$module/models/stepPermission';
import TransitionStep from '$module/models/transitionStep';
import { userFilter } from '@/plugins/utils';

export default {
    name: 'EditStep',
    props: {
        step: {
            type: Object, default: () => ({}),
        },
        roles: {
            type: Array, default: () => ([]),
        },
        status: {
            type: Array, default: () => [],
        },
        disabled: {
            type: Boolean, default: true,
        },
    },
    data() {
        return {
            innerValue: {},
            PERMISSIONS_TYPE,
            permissionTypeList: Object.keys(PERMISSIONS_TYPE).map(s => ({
                label: this.$t(`jacp.teamspace.transition.stepPermission.${s.toLowerCase()}`),
                value: PERMISSIONS_TYPE[s],
            })),
        };
    },
    methods: { userFilter },
    mounted() {
        this.innerValue = new TransitionStep({ ...this.step });
    },
    computed: {
        pointStatus() {
            return TransitionStep.getStartAndEndStatus(this.step, this.status);
        },
    },
};
</script>
<style lang="less">
.edit-step-form{
    .el-alert{
        padding: 0 4px;
    }
    .form-users__wrapper{
        gap: var(--gutter--medium);
        flex-wrap: wrap;
    }
    .edit-step-status{
        display:flex;
        justify-content: space-between;
        align-items: center;
        // padding: 0 8%;
        &ul, li {
            list-style: none;
        }
        &-node{
            border: 1px solid var(--border-color);
            line-height: 24px;
            border-radius: var(--radius--default);
            padding: var(--gutter--mini) var(--gutter--medium);
            min-width: 100px;
            text-align: center;
            z-index: 1;
        }
        &-line{
            flex-basis: 100%;
            height: 1px;
            background-color: var(--color--base--border);
            position: relative;
            .el-icon-arrow-right{
                position: absolute;
                right: 0;
                top: 0;
                transform: translate(6px, -9px);
                color: var(--color--base--border);
                font-size:20px;
            }
            &:before{
                @radius: 8px;
                content: ' ';
                display: block;
                width: @radius;
                height: @radius;
                background-color: var(--color--base--border);
                position: absolute;
                left: 0;
                top: 0;
                transform: translateY(-50%);
                border-radius: 50%;
            }
        }
    }
    .edit-step-stepName{
        position: absolute;
        display: flex;
        align-items: center;
        top: -48px;
        transform: translateX(-50%);
        left: 50%;
    }
}
</style>
