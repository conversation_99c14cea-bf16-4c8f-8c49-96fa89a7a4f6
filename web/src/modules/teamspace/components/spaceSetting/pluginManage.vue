<template>
    <div class="space">
        <div
            class="space-setting-plugin__content"
        >
            <el-alert
                title="根据团队自身情况可选择对应的插件集成到团队空间中"
                type="success"
                :closable="false"
            />
            <h3 class="space-setting-plugin__head">
                测试类(单选)
                <el-button
                    icon="el-icon-refresh"
                    @click="resetTest"
                    size="small"
                    type="text"
                    :disabled="!spacePrivilage.updateSpace"
                />
            </h3>
            <!-- 先硬编码实现，待与插件市场考虑清楚再配置化处理 -->
            <div>
                <el-radio-group
                    v-model.number="testValue"
                    :disabled="!spacePrivilage.updateSpace"
                >
                    <el-radio
                        :label="1"
                        border
                    >
                        VMS
                    </el-radio>
                    <el-radio
                        :label="2"
                        border
                    >
                        穹天
                    </el-radio>
                    <el-radio
                        :label="3"
                        border
                    >
                        测试管理(行云)
                    </el-radio>
                </el-radio-group>
                <div>
                    <div
                        class="plugin-config"
                        v-show="showTenant || showTemplate"
                    >
                        相关配置
                    </div>
                    <el-form
                        label-position="left"
                        inline
                        :disabled="!spacePrivilage.updateSpace"
                    >
                        <el-form-item
                            v-if="showTenant"
                            label="租户信息"
                        >
                            <jacp-form-select
                                v-model="tenantId"
                                :options="tenantList"
                                :filterable="true"
                                :confirm="false"
                                placeholder="请选择"
                            />
                        </el-form-item>
                        <el-form-item
                            v-if="showTemplate"
                            label="缺陷模板"
                        >
                            <jacp-form-select
                                v-model="issueTemplateId"
                                :options="issueTemplateList"
                                :filterable="true"
                                :confirm="false"
                                placeholder="请选择"
                            />
                        </el-form-item>
                        <el-form-item
                            v-if="showTemplate"
                            label="提测模板"
                        >
                            <jacp-form-select
                                v-model="testTemplateId"
                                :options="testTemplateList"
                                :filterable="true"
                                :confirm="false"
                                placeholder="请选择"
                            />
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <h3 class="space-setting-plugin__head">
                项目管理类
            </h3>
            <div>
                <el-checkbox-group
                    v-model="projectVendors"
                    size="small"
                >
                    <el-checkbox
                        :label="5"
                        border
                    >
                        工时管理
                    </el-checkbox>
                </el-checkbox-group>
            </div>
        </div>
        <div class="space-setting-plugin__content__footer">
            <el-button
                class="space-setting-plugin__content__save"
                type="primary"
                size="mini"
                v-show="spacePrivilage.updateSpace"
                @click="saveVendor"
            >
                应用
            </el-button>
        </div>
    </div>
</template>
<script type="text/javascript">
import RouterMixin from '@/mixins/mixin.router';
import SpaceModel from '@/models/space';
import VendorModel from '@/models/vendor';
import TenantModel from '@/models/tenant';
import JbugTemplateModel from '@/models/jbugTemplate';

export default {
    mixins: [RouterMixin],
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            spaceVendors: [], // 可选数据源列表
            vendorData: [],
            testValue: undefined,
            tenantId: '',
            tenantList: [], // 穹天租户信息
            issueTemplateId: undefined, // 选择的缺陷模板ID
            issueTemplateList: [], // 缺陷模板列表
            testTemplateId: undefined, // 选择额提测模板ID
            testTemplateList: [], // 提测模板列表
            projectVendors: [],
        };
    },
    created() {
        this.init();
        VendorModel.getVendorList().then((vendors) => {
            this.spaceVendors = vendors;
        });
    },
    methods: {
        init() {
            this.initTenantList();
            this.initTestConfigList();
            this.getSpaceInfo();
        },
        getSpaceInfo() {
            SpaceModel.getSpaceVendorConfig(this.spaceKey, true).then((data) => {
                if (data.length > 0) {
                    this.vendorData = data;
                    this.projectVendors = [];
                    data.forEach((cf) => {
                        const intVendorId = parseInt(cf.vendorId, 10);
                        if ([1, 2, 3].includes(intVendorId)) {
                            this.testValue = intVendorId;
                        } else {
                            this.projectVendors.push(intVendorId);
                        }
                    });
                } else {
                    this.vendorData = [];
                }
                this.tenantId = undefined;
                this.issueTemplateId = undefined;
                this.testTemplateId = undefined;
                if (this.testValue === 2) {
                    SpaceModel.getTenantryOption({ spaceKey: this.spaceKey }, true).then((dt) => {
                        this.tenantId = dt.tenantryId;
                    });
                } else if (this.testValue === 3) {
                    SpaceModel.getIssueTemplateOption({ spaceKey: this.spaceKey }, true).then((dt) => {
                        this.issueTemplateId = dt.id;
                    });
                    SpaceModel.getTestTemplateOption({ spaceKey: this.spaceKey }, true).then((dt) => {
                        this.testTemplateId = dt.id;
                    });
                }
            });
        },
        initTenantList() {
            if (this.tenantList.length === 0) {
                TenantModel.getTenantsList().then((tenants) => {
                    this.tenantList = tenants;
                });
            }
        },
        initTestConfigList() {
            if (this.issueTemplateList.length === 0) {
                JbugTemplateModel.getIssueTemplateList().then((templates) => {
                    this.issueTemplateList = templates;
                });
            }
            if (this.testTemplateList.length === 0) {
                JbugTemplateModel.getTestTemplateList().then((templates) => {
                    this.testTemplateList = templates;
                });
            }
        },
        async saveVendor() {
            if (this.testValue === 2 && !this.tenantId) {
                this.$notify({
                    title: '请选择穹天系统租户信息！',
                    type: 'error',
                    duration: 2000,
                });
                return;
            }

            if (this.testValue === 3 && (!this.issueTemplateId || !this.testTemplateId)) {
                this.$notify({
                    title: '请选择行云测试系统的模板信息！',
                    type: 'error',
                    duration: 2000,
                });
                return;
            }
            const requestBody = [];
            if (this.testValue) {
                requestBody.push({
                    id: this.testValue,
                    attrs: {
                        tenantryId: this.testValue === 2 ? this.tenantId : undefined,
                        issueTemplateId: this.testValue === 3 ? this.issueTemplateId : undefined,
                        testTemplateId: this.testValue === 3 ? this.testTemplateId : undefined,
                    },
                });
            }
            (this.projectVendors || []).forEach((id) => {
                requestBody.push({ id });
            });
            this.$store.commit('chilldteamspace/setWorktime', this.projectVendors.includes(5));

            await SpaceModel.saveSpaceVendorConfig(this.spaceKey, requestBody).then(() => {
                // 这个接口会对get进行缓存，所以保存完了以后刷一下缓存
                SpaceModel.getSpaceVendorConfig(this.spaceKey, true);
            });
        },
        resetTest() {
            this.testValue = null;
        },
    },
    watch: {
        spaceKey: {
            handler(key) {
                if (key) {
                    this.init();
                }
            },
            immediate: true,
        },
    },
    computed: {
        showTenant() {
            return this.testValue === 2;
        },
        showTemplate() {
            return this.testValue === 3;
        },
    },
};
</script>

<style lang="less">
.space-setting-plugin {
    position: relative;
    padding-left: 10px;
    &__head {
        margin: 0;
        height: 40px;
        color: #666;
        font-size: 16px;
        line-height: 40px;
        margin-bottom: 12px;
        margin-top: 12px;
    }
    &__content {
        overflow-y: auto;
        height: calc(~"100vh - 260px");
        &__footer {
            border-top: 1px solid #c4c4c4;
            & button{
                margin-top: 6px;
            }
        }
        .plugin-config {
            font-size: 12px;
            margin-top: 12px;
            margin-bottom: 12px;
        }
    }
}
</style>
