<template>
    <div class="customize-card-fields__root">
        <div class="customize-card-fields__content">
            <div class="customize-card-fields__left">
                <!-- 选择模板 -->
                <div
                    v-if="spacePrivilage.isAdmin && editMode"
                    class="customize-card-fields__select"
                >
                    <el-select
                        v-model="chosedTemplateId"
                        placeholder="请选择模板"
                        clearable
                    >
                        <el-option
                            v-for="item in validTemplates"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                    <router-link
                        class="customize-card-fields__add"
                        v-if="addVisible"
                        to="/option/extendFormSetting"
                    >
                        <el-button
                            size="small"
                            type="primary"
                            circle
                            icon="el-icon-plus"
                            class="jacp-button--gradient jacp-button--mini"
                        />
                    </router-link>
                </div>
                <!-- 设置说明 -->
                <div class="customize-card-fields__instructions">
                    <!-- eslint-disable -->
                    <span class="customize-card-fields__instructions-title">团队空间扩展字段设置说明</span><br>
                    1.行云部门管理员有权在系统设置-->扩展表单设置里创建模版<br>
                    2.团队空间管理员有权根据模版自定义扩展字段<br>
                    3.保存后扩展字段会在团队空间的卡片内生效<br>
                    4.若删除或变更字段,会将对应的历史记录一并删除或变更<br>
                </div>
            </div>
            <!-- 扩展字段预览 -->
            <div class="customize-card-fields__right">
                <div style="margin-bottom: 8px; font-size: 12px; color: #909399">扩展字段预览</div>
                <dynamic-form
                    class="customize-card-fields__form"
                    v-if="fieldsNotEmpty"
                    v-bind="chosedTemplateData.schema || customizedTemplateSchema"
                    v-model="data"
                    :disabled="true"
                />
                <div
                    v-else
                    class="customize-card-fields__form"
                >
                    <div style="color: #a4a3a3; font-size: 14px; margin-left: -20px;">
                        暂无扩展字段
                    </div>
                </div>
            </div>
        </div>
        <div
            v-if="spacePrivilage.isAdmin"
            class="customize-card-fields__rooter"
        >
            <div v-if="!editMode">
                <el-button
                    size="small"
                    type="primary"
                    @click="editMode=true"
                >编辑</el-button>
            </div>
            <div v-else>
                <el-button
                    size="small"
                    type="primary"
                    :disabled="!chosedTemplateId"
                    @click="save"
                >保存并应用</el-button>
                <el-button
                    size="small"
                    @click="cancel"
                >取消</el-button>
                <el-button
                    size="small"
                    v-show="showResetBtn"
                    @click="resetData"
                >还原</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import CardModel from '@/models/card';
import UserModel from '@/models/user';
import DynamicForm from '@/models/dynamicForm';
import { event } from '@/plugins/event';

export default {
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
        currentSpaceId: {
            type: Number,
        },
    },
    mounted() {
        this.init();
    },
    data() {
        return {
            chosedTemplateData: {},
            chosedTemplateId: '',
            initialTemplateId: '',
            customizedTemplateSchema: undefined,
            validTemplates: [],
            data: {},
            addVisible: false,
            editMode: false,
        };
    },
    methods: {
        async init() {
            this.editMode = false;
            this.chosedTemplateId = '';
            this.initialTemplateId = '';
            await this.loadValidTemplates();
            this.loadCustomizedTemplate();
            this.initPrivilage();
        },
        initPrivilage() {
            UserModel.getMenuAuthor().then((data) => {
                if (data.admin.length > 0) {
                    this.addVisible = true;
                }
            });
        },
        loadValidTemplates() {
            CardModel.getValidTemplates().then((data = []) => {
                this.validTemplates = data;
            });
        },
        loadCustomizedTemplate() {
            DynamicForm.load({
                name: 'card',
                scope: `space:${this.currentSpaceId}`,
            }).then((data = {}) => {
                const { schema = {} } = data;

                /**
                 * 第一次进入卡片自定义设置/应用的是空模板时，获取 schema 为空。
                 * 期望在编辑状态下，选中空模板，给用户友好提示
                 */
                if (!schema.fields || !schema.fields.length) {
                    this.chosedTemplateId = this.validTemplates[0]?.id;
                    this.initialTemplateId = this.chosedTemplateId;
                }
                this.customizedTemplateSchema = schema;
            });
        },
        async loadTemplateData() {
            this.chosedTemplateData = await DynamicForm.load({
                id: this.chosedTemplateId,
                code: 'jacp',
            });
        },
        save() {
            const { appId, schema } = this.chosedTemplateData;
            if (!this.chosedTemplateId) {
                Object.assign(schema, { fields: [] });
            }
            CardModel.saveCustomizedTemplate({
                appId,
                schema,
                scope: this.currentSpaceId,
                name: 'card',
            }).then(() => {
                this.editMode = false;
                this.customizedTemplateSchema = schema;
                this.initialTemplateId = '';
                this.chosedTemplateId = this.initialTemplateId;
                event.$emit('template-changed', {
                    spaceId: this.currentSpaceId,
                    templateId: this.chosedTemplateId,
                });
                this.$message.success('保存成功');
                // 刷一下http缓存
                DynamicForm.load({
                    name: 'card',
                    scope: `space:${this.currentSpaceId}`,
                }, { ignoreCache: true });
            });
        },
        cancel() {
            this.editMode = false;
            this.resetData();
        },
        resetData() {
            this.chosedTemplateId = this.initialTemplateId;
        },
    },
    computed: {
        showResetBtn() {
            return this.chosedTemplateId !== this.initialTemplateId;
        },
        fieldsNotEmpty() {
            return this.chosedTemplateId !== ''
                ? (this.chosedTemplateData.schema && this.chosedTemplateData.schema.fields.length > 0)
                : (this.customizedTemplateSchema && this.customizedTemplateSchema.fields.length > 0);
        },
    },
    watch: {
        chosedTemplateId() {
            if (!this.chosedTemplateId) {
                this.chosedTemplateData = {};
                return;
            }
            this.loadTemplateData();
        },
        spaceKey() {
            this.init();
        },
    },
};
</script>

<style lang="less">
.customize-card-fields {
    &__content {
        height: calc(100vh - 260px);
        display: flex;
        column-gap: 24px;
    }
    &__left {
        width: 40%;
    }
    &__add {
        margin-left: 8px;
    }
    &__select {
        margin-bottom: 16px;
    }
    &__instructions {
        color: #303133;
        font-size: 12px;
        font-weight: 400;
        line-height: 24px;
        &-title {
            font-size: 14px;
            font-weight: 600;
        }
    }
    &__right {
        width: 60%;
    }
    &__form {
        padding: 20px 40px;
        background-color: #f7f7f7;
        border-radius: 4px;
        height: calc(100% - 80px);
        overflow-y: auto;
        overflow: overlay;
    }
    &__rooter {
        // border-top: 1px solid #c4c4c4;
        & .el-button {
            margin-top: 6px;
            border-radius: 4px;
        }
    }
}
</style>
