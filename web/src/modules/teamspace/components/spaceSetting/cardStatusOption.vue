<template>
    <div class="jacp-cardStatus-option">
        <div class="jacp-step-bar">
            <div class="jacp-step-label">
                阶段示意：
            </div>
            <div
                class="jacp-step-item"
                color="#BA68C8"
            >
                准备
            </div>
            <div
                class="jacp-step-item"
                color="#F4BE00"
            >
                评审
            </div>
            <div
                class="jacp-step-item"
                color="#198DEC"
            >
                就绪
            </div>
            <!-- <div class = "jacp-step-item" color="#198DEC">起点</div> -->
            <div
                class="jacp-step-item"
                color="#FF8D00"
            >
                设计
            </div>
            <div
                class="jacp-step-item"
                color="#6AB4FF"
            >
                开发
            </div>
            <div
                class="jacp-step-item"
                color="#0ED396"
            >
                测试
            </div>
            <div
                class="jacp-step-item"
                color="#F84E54"
            >
                发布
            </div>
            <div
                class="jacp-step-item jacp-step-item-nodirection"
                color="#78C913"
            >
                完成
            </div>
            <div
                class="jacp-step-item jacp-step-item-nodirection"
                color="#90A3AE"
            >
                挂起
            </div>
        </div>
        <div class="card-status-table">
            <div class="card-status-table__head">
                <div class="card-status-table__column">
                    序号
                </div>
                <div class="card-status-table__column">
                    状态名
                </div>
                <div
                    class="card-status-table__column"
                >
                    属性
                </div>
                <div class="card-status-table__column">
                    阶段
                </div>
                <div class="card-status-table__column last-column">
                    卡片数量（活跃迭代/归档迭代）
                </div>
                <div class="card-status-table__column">
                    WIP 功能
                </div>
                <div class="card-status-table__column">
                    WIP 数量
                </div>
                <div
                    class="card-status-table__column last-column"
                    v-show="spacePrivilage.updateSpace"
                >
                    操作
                </div>
            </div>
            <div class="card-status-table__body">
                <div
                    class="card-status-table__group"
                    v-for="(list, $index) in statusListMap"
                    :key="$index"
                >
                    <draggable
                        @sort="changeStatusIndex($event, list)"
                        :options="{animation:300, ghostClass: 'card-status-table__item__dragging',
                                   disabled: !spacePrivilage.updateSpace}"
                    >
                        <div
                            class="card-status-table__item"
                            :class="{'drag-handler': spacePrivilage.updateSpace}"
                            v-for="item in list"
                            :key="item.id"
                        >
                            <div class="card-status-table__column">
                                {{ item.tableIndex + 1 }}
                            </div>
                            <div class="card-status-table__column">
                                {{ item.statusName }}
                            </div>
                            <div
                                class="card-status-table__column"
                            >
                                {{ item.attrFlag | attrZH }}
                            </div>
                            <div class="card-status-table__column">
                                <i
                                    class="jacp-card-status"
                                    :color="stageMap[item.stageId].color"
                                />
                                {{ stageMap[item.stageId].name }}
                            </div>
                            <div class="card-status-table__column last-column">
                                {{ cardCountMap[item.statusCode] | cardCount }}
                            </div>
                            <div
                                class="card-status-table__column"
                                :title="wipStatusOptions[item.wipStatus || 0].label"
                            >
                                {{ wipStatusOptions[item.wipStatus || 0].label }}
                            </div>
                            <div class="card-status-table__column">
                                {{ item.wipStatus ? item.wipCount: '' }}
                            </div>
                            <div
                                class="card-status-table__column last-column"
                                v-show="spacePrivilage.updateSpace"
                            >
                                <template v-if="item.stageId !== 150 && item.stageId !== 170">
                                    <el-button
                                        type="text"
                                        v-if="item.disabled === 0"
                                        @click="switchCardStatusVisible(item, list)"
                                    >
                                        隐藏
                                    </el-button>
                                    <el-button
                                        type="text"
                                        v-else
                                        @click="switchCardStatusVisible(item)"
                                    >
                                        显示
                                    </el-button>
                                </template>
                                <el-button
                                    type="text"
                                    v-if="item.stageId !== 170"
                                    @click="changeCardStatusName(item)"
                                >
                                    编辑
                                </el-button>
                                <el-button
                                    v-if="item.stageId !== 150 && item.stageId !== 170"
                                    type="text"
                                    @click="delCardStatus(item, list)"
                                >
                                    删除
                                </el-button>
                            </div>
                        </div>
                    </draggable>
                </div>
            </div>
            <div class="card-status-table__footer">
                <el-button
                    v-show="spacePrivilage.updateSpace"
                    type="primary"
                    size="mini"
                    @click="addCardStatus"
                >
                    新增状态
                </el-button>
            </div>
        </div>
        <el-dialog
            class="card-status-form"
            width="600px"
            :title="formAction === 'addCardStatus' ? '新增状态' : '编辑'"
            :visible.sync="dialogVisible"
            :close-on-click-modal="false"
            @close="closeDialog"
        >
            <el-form
                :model="statusFormData"
                :rules="rules"
                ref="statusForm"
                label-width="120px"
            >
                <el-form-item
                    label="阶段"
                    prop="stageId"
                    v-show="formAction === 'addCardStatus'"
                >
                    <el-select
                        class="jacp-status-selector"
                        :color="stageMap[statusFormData.stageId]
                            ? stageMap[statusFormData.stageId].color : '#fff'"
                        v-model="statusFormData.stageId"
                        placeholder="请选阶段"
                    >
                        <template v-for="(stageInfo, stageId) in stageMap">
                            <el-option
                                v-if="stageId !== '150' && stageId !== '170'"
                                :key="stageId"
                                :label="stageInfo.name"
                                :value="stageId"
                            >
                                <i
                                    class="jacp-card-status"
                                    :color="stageInfo.color"
                                />
                                <span style="padding-left: 6px;">{{ stageInfo.name }}</span>
                            </el-option>
                        </template>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="状态名"
                    prop="statusName"
                >
                    <el-input v-model.trim="statusFormData.statusName" />
                </el-form-item>
                <el-form-item
                    label="属性"
                    prop="attrFlag"
                >
                    <el-select
                        v-model="statusFormData.attrFlag"
                        :disabled="statusFormData.statusCode === 7"
                    >
                        <el-option
                            v-for="item in attrFlagOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <!-- WIP 功能设置 -->
                <el-form-item
                    label="WIP 功能"
                    prop="wipStatus"
                >
                    <el-select
                        v-model="statusFormData.wipStatus"
                    >
                        <el-option
                            v-for="item in wipStatusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="WIP 数量"
                    prop="wipCount"
                    v-if="statusFormData.wipStatus === 1"
                >
                    <el-input
                        type="number"
                        v-model="statusFormData.wipCount"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button
                    type="primary"
                    @click="saveCardStatus"
                >
                    保存
                </el-button>
                <el-button @click="closeDialog">
                    取 消
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script type="text/javascript">
import draggable from 'vuedraggable';
import CardModel from '@/models/card';
import { stageMap, wipStatusOptions } from '@/modules/teamspace/constant';
import SpaceModel from '@/models/space';
import {
    getSpaceId,
} from '@/plugins/utils';

export default {
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        draggable,
    },
    data() {
        const wipCountValidator = (rule, value, callback) => {
            const pattern = /^[1-9]\d*$/;
            if (!value) {
                callback('请输入 WIP 数量');
            } else if (!pattern.test(value)) {
                callback('请输入正整数');
            } else if (+value > 1024) {
                callback('WIP数量最高限制1024个');
            }
            callback();
        };
        return {
            statusList: [],
            statusListMap: {},
            cardCountMap: {},
            cardCountTotalMap: {},
            dialogVisible: false,
            dialogTitle: '新增状态',
            formAction: 'addCardStatus',
            currentEditIndex: -1,
            statusFormData: {
                id: '',
                stageId: '',
                statusName: '',
                attrFlag: 1,
                disabled: 0,
                wipStatus: 0,
                wipCount: undefined,
            },
            attrFlagOptions: [
                { value: 1, label: '活跃' },
                { value: 2, label: '等待' },
                { value: 0, label: '无' },
            ],
            wipStatusOptions,
            stageMap,
            rules: {
                stageId: [{
                    required: true,
                    message: '请选择阶段',
                    trigger: 'change',
                }, {
                    message: '该阶段最多5个状态',
                    trigger: 'change',
                    validator: this.checkStage,
                }],
                statusName: [{
                    required: true,
                    message: '状态名不能为空',
                    trigger: 'blur',
                }, {
                    min: 1,
                    max: 10,
                    message: '状态名不能超过10个字',
                    trigger: 'blur',
                }, {
                    message: '状态名不能重复',
                    trigger: 'change',
                    validator: this.checkStatusName,
                }],
                wipCount: [{
                    validator: wipCountValidator,
                    trigger: 'blur',
                }],
            },
        };
    },
    created() {
        this.getCardStausList();
    },
    methods: {
        getCardStausList() {
            CardModel.getCardStausList(getSpaceId(this.spaceKey)).then((data) => {
                if (data && data.length > 0) {
                    // 查询卡片数量
                    SpaceModel.getRestCardCount(this.spaceKey).then(this.setRestCardCount);
                    this.statusList = data;
                    // 数据按阶段分组
                    let stageIdTmp = data[0].stageId;
                    this.$set(this.statusListMap, stageIdTmp, []);
                    // 状态列表按阶段分组
                    data.forEach((statusInfo, index) => {
                        if (statusInfo.stageId !== stageIdTmp) {
                            stageIdTmp = statusInfo.stageId;
                            this.$set(this.statusListMap, stageIdTmp, []);
                        }
                        this.$set(statusInfo, 'tableIndex', index);
                        this.statusListMap[statusInfo.stageId].push(statusInfo);
                    });
                }
            });
        },
        addCardStatus() {
            this.dialogVisible = true;
            this.formAction = 'addCardStatus';
            this.statusFormData.spaceId = getSpaceId(this.spaceKey);
            this.currentEditIndex = -1;
        },
        changeCardStatusName(statusInfo) {
            this.dialogVisible = true;
            this.formAction = 'modifyCardStatus';
            this.currentEditIndex = statusInfo.tableIndex;
            Object.assign(this.statusFormData, statusInfo);
        },
        saveCardStatus() {
            if (this.statusFormData.wipStatus === 0) {
                this.statusFormData.wipCount = undefined;
            }
            this.$refs.statusForm.validate((valid) => {
                if (valid) {
                    CardModel[this.formAction](this.statusFormData).then(() => {
                        this.getCardStausList();
                        this.closeDialog();
                        this.$notify({
                            title: '保存成功！',
                            type: 'success',
                            duration: 2000,
                        });
                        this.getAvailableStatusList(this.spaceKey);
                    });
                }
            });
        },
        delCardStatus(statusInfo, statusList) {
            let warning;
            if (statusList.length <= 1) {
                warning = '当前阶段至少需要一个状态，无法删除！';
            }
            if (this.cardCountTotalMap[statusInfo.statusCode] > 0) {
                warning = `当前状态下空间中有${this.cardCountTotalMap[statusInfo.statusCode]}张卡片，无法删除，需要改变这些卡片状态后再进行操作！`;
            }
            if (warning) {
                this.$alert(warning, '提示', {
                    confirmButtonText: '确定',
                });
                return;
            }
            this.$confirm(`删除状态将会删除有关该状态的相关历史及记录，会影响数据统计，是否删除状态名为「${statusInfo.statusName}」的状态?`,
                '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                CardModel.delCardStatus(statusInfo.id).then(() => {
                    this.getCardStausList();
                    this.$notify({
                        title: '删除成功！',
                        type: 'success',
                        duration: 2000,
                    });
                    this.getAvailableStatusList(this.spaceKey);
                });
            });
        },
        switchCardStatusVisible({
            id,
            stageId,
            statusName,
            statusCode,
            disabled,
        }, statusList) {
            let warning;
            if (disabled === 0 && this.cardCountTotalMap[statusCode] > 0) {
                warning = `当前状态下空间中有${this.cardCountTotalMap[statusCode]}张卡片，无法隐藏，需要改变这些卡片状态后再进行操作！`;
            }
            if (stageId === 10 && disabled === 0) {
                let visibleCount = 0;
                statusList.forEach((status) => {
                    if (status.disabled === 0) {
                        visibleCount += 1;
                    }
                });
                if (visibleCount <= 1) {
                    warning = '无法隐藏，准备阶段至少保留一种状态为显示！';
                }
            }
            if (warning) {
                this.$alert(warning, '提示', {
                    confirmButtonText: '确定',
                });
                return;
            }
            CardModel.modifyCardStatus({
                id,
                stageId,
                spaceId: getSpaceId(this.spaceKey),
                statusName,
                disabled: disabled === 0 ? 1 : 0,
            }).then(() => {
                this.getCardStausList();
                this.$notify({
                    title: '修改成功！',
                    type: 'success',
                    duration: 2000,
                });
                this.getAvailableStatusList(this.spaceKey);
            });
        },
        changeStatusIndex(e, statusList) {
            const statusListTmp = statusList.slice();
            const dragStatus = statusListTmp[e.oldIndex];
            statusListTmp.splice(e.oldIndex, 1);
            statusListTmp.splice(e.newIndex, 0, dragStatus);
            CardModel.changeCardStatusIndex(statusListTmp).then(() => {
                this.$notify({
                    title: '修改成功！',
                    type: 'success',
                    duration: 2000,
                });
                this.getCardStausList();
                this.getAvailableStatusList(this.spaceKey);
            });
        },
        setRestCardCount(data) {
            this.cardCountMap = Object.assign({}, data);
            Object.keys(this.cardCountMap).forEach((k) => {
                this.cardCountTotalMap[k] = this.cardCountMap[k][0] + this.cardCountMap[k][1];
            });
        },
        closeDialog() {
            this.dialogVisible = false;
            this.$refs.statusForm.resetFields();
            this.statusFormData = {
                id: '',
                stageId: '',
                statusName: '',
                attrFlag: 1,
                disabled: 0,
                wipStatus: 0,
                wipCount: undefined,
            };
        },
        // 同阶段最大状态数5
        checkStage(rule, value, callback) {
            if (this.formAction === 'addCardStatus' && this.statusListMap[value]
              && this.statusListMap[value].length > 4) {
                callback(new Error(rule.message));
                return;
            }
            callback();
        },
        // 检查状态名是否已存在
        checkStatusName(rule, value, callback) {
            let usedName;
            this.statusList.forEach((status) => {
                if (this.currentEditIndex !== status.tableIndex
                  && status.statusName === value) {
                    usedName = true;
                }
            });
            if (usedName) {
                callback(new Error(rule.message));
                return;
            }
            callback();
        },
        getAvailableStatusList(spaceKey) {
            this.$store.dispatch('chilldteamspace/fetchAvailableStatusList', { spaceId: getSpaceId(spaceKey), ignoreCache: true });
        },
    },
    filters: {
        attrZH(input) {
            return ['无', '活跃', '等待'][input];
        },
        cardCount(arr) {
            if (arr !== undefined && arr.length === 2) {
                return `${arr[0]}/${arr[1]}`;
            }
            return '0/0';
        },
    },
    watch: {
        spaceKey(v) {
            if (v) {
                this.getCardStausList();
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-cardStatus-option {
  .jacp-step-bar {
    width: 1200px;
    display: flex;
    overflow: hidden;
    opacity: .8;
  }
  .jacp-step-label {
    width: 80px;
    font-size: 14px;
    color: #2696f1;
    height: 28px;
    line-height: 28px;
  }
  .jacp-step-item {
    flex: 1;
    font-size: 14px;
    color: white;
    line-height: 28px;
    text-align: center;
    position: relative;
    border-radius: 2px;
  }
  .jacp-step-item-nodirection {
    margin-right: 20px;
  }
  & .jacp-step-item.jacp-step-item-nodirection::after,
  .jacp-step-item.jacp-step-item-nodirection::before {
    display: none;
  }
  .jacp-step-item::before {
    content: " ";
    display: block;
    width: 0;
    height: 0;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
    border-left: 15px solid #fff;
    position: absolute;
    top: 50%;
    margin-top: -25px;
    margin-left: 2px;
    left: 100%;
    z-index: 1;
  }
  .jacp-step-item::after {
    content: " ";
    display: block;
    width: 0;
    height: 0;
    border-top: 25px solid transparent;
    border-bottom: 25px solid transparent;
    // border-left: 15px solid #2695f1;
    position: absolute;
    top: 50%;
    margin-top: -25px;
    left: 100%;
    margin-left: -1px;
    z-index: 2;
  }
  .card-status-table {
    margin-top: 20px;
    color: #999;
    &__head {
      font-weight: 600;
      display: flex;
      background-color: #eef1f6;
    }
    &__body {
      height: calc(~"100vh - 340px");
      overflow: auto;
    }
    &__footer {
      height: 30px;
      padding-top: 10px;
    }
    &__group {
      &:nth-of-type(2n) {
        background-color: #f8f8f8;
      }
    }
    &__item {
      display: flex;
      font-size: 14px;
      color: #666;
      &__dragging {
        border: 1px solid #2695F1;
      }
      &.drag-handler{
        cursor: move;
        background-image: url("~@/assets/icons/sortable.svg");
        background-repeat: no-repeat;
        background-size: contain;
      }
    }
    &__column {
      flex: 1;
      height: 36px;
      line-height: 36px;
      padding-left: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.last-column {
        flex: 2;
      }
    }
  }
  .card-status-form {
    & .el-input,
    .el-select {
      width: 300px;
    }
  }
}

</style>
