<template>
    <div class="spacesetting-role">
        <div
            class="spacesetting-role__main"
            v-show="!addFormVisible"
        >
            <el-table
                ref="multipleTable"
                :data="memberList"
                tooltip-effect="dark"
                class="spacesetting-role__users"
            >
                <el-table-column
                    prop="userName"
                    label="姓名"
                    width="340px"
                >
                    <template slot-scope="scope">
                        <label>
                            <jacp-erp
                                :data="{erp: scope.row.memberErp, name: scope.row.memberName}"
                                :display-erp="true"
                                :disable-timline="$utils.isCurrentUser({erp: scope.row.memberErp})"
                            />
                        </label>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="privilage"
                    label="管理员"
                    width="140px"
                    align="center"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <i
                            v-if="scope.row.privilage === 1"
                            class="el-icon-success"
                        />
                    </template>
                </el-table-column>
                <el-table-column
                    prop="privilage"
                    label="成员"
                    width="140px"
                    align="center"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <i
                            v-if="scope.row.privilage === 2"
                            class="el-icon-success"
                        />
                    </template>
                </el-table-column>
                <el-table-column
                    prop="privilage"
                    label="游客"
                    align="center"
                    width="140px"
                >
                    <template slot-scope="scope">
                        <i
                            v-if="scope.row.privilage === 3"
                            class="el-icon-success"
                        />
                    </template>
                </el-table-column>
                <el-table-column />
            </el-table>
        </div>
        <div
            v-show="addFormVisible"
            class="spacesetting-role__edit"
        >
            <div class="spacesetting-role__form">
                <div class="spacesetting-role__header">
                    <el-radio-group
                        v-model="roleType"
                        class="spacesetting-role__roleType"
                    >
                        <el-radio :label="1">
                            管理员
                        </el-radio>
                        <el-radio :label="2">
                            成员
                        </el-radio>
                        <el-radio :label="3">
                            游客
                        </el-radio>
                    </el-radio-group>
                    <el-input
                        class="spacesetting-role__keyworld"
                        placeholder="请输入工号/姓名/邮箱/部门名称"
                        v-model.trim="keyword"
                        size="small"
                        width="200px"
                        clearable
                    >
                        <el-button
                            slot="append"
                            type="primary"
                            icon="el-icon-search"
                            @click="getUserList(true)"
                        />
                    </el-input>
                </div>
                <el-table
                    size="small"
                    stripe
                    :data="userList"
                    style="width: 100%"
                    header-row-class-name="table-head"
                    width="600"
                >
                    <el-table-column width="60">
                        <template slot-scope="scope">
                            <i
                                class="el-icon-circle-plus"
                                @click="addMember(scope.row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="userErp"
                        label="工号"
                        width="120"
                    />
                    <el-table-column
                        prop="userName"
                        label="姓名"
                        width="120"
                    />
                    <el-table-column
                        prop="email"
                        label="邮箱"
                        width="200"
                        :show-overflow-tooltip="true"
                    />
                    <el-table-column
                        prop="orgTierName"
                        label="部门"
                        :show-overflow-tooltip="true"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.orgTierName ? scope.row.orgTierName.split('-').pop() : '-' }}
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 数据index不规则 无法使用通用分页控件-->
                <jacp-pagenation
                    class="demands__pagenation"
                    v-show="userList.length > 0"
                    :total="total"
                    :current-page.sync="current"
                    :page-size.sync="pageSize"
                    @update:currentPage="getUserList"
                />
                <div
                    class="spacesetting-role__page"
                    v-show="userList.length > 0"
                >
                    <div
                        class="left"
                        @click="addAll"
                    >
                        <el-button
                            type="primary"
                            size="mini"
                        >
                            批量添加
                        </el-button>
                    </div>
                </div>
            </div>
            <div class="spacesetting-role__result">
                <div v-show="adminNum > 0">
                    <p class="spacesetting-role__num">
                        已选择管理员
                        <span>{{ adminNum }}</span>人
                        <span
                            class="spacesetting-role__clear"
                            @click="clearAll(1)"
                        >清空</span>
                    </p>
                    <div class="spacesetting-role__addlist">
                        <template v-for="(role, index) in editRoleList">
                            <el-tag
                                size="small"
                                closable
                                @close="delMember(index)"
                                v-if="role.privilage === 1"
                                :key="role.memberErp"
                            >
                                {{ `${role.memberName}(${role.memberErp})` }}
                            </el-tag>
                        </template>
                    </div>
                </div>
                <div v-show="memberNum > 0">
                    <p class="spacesetting-role__num">
                        已选择成员
                        <span>{{ memberNum }}</span>人
                        <span
                            class="spacesetting-role__clear"
                            @click="clearAll(2)"
                        >清空</span>
                    </p>
                    <div class="spacesetting-role__addlist">
                        <template v-for="(role, index) in editRoleList">
                            <el-tag
                                size="small"
                                closable
                                @close="delMember(index)"
                                v-if="role.privilage === 2"
                                :key="role.memberErp"
                            >
                                {{ `${role.memberName}(${role.memberErp})` }}
                            </el-tag>
                        </template>
                    </div>
                </div>
                <div v-show="visitorNum > 0">
                    <p class="spacesetting-role__num">
                        已选择游客
                        <span>{{ visitorNum }}</span>人
                        <span
                            class="spacesetting-role__clear"
                            @click="clearAll(3)"
                        >清空</span>
                    </p>
                    <div class="spacesetting-role__addlist">
                        <template v-for="(role, index) in editRoleList">
                            <el-tag
                                size="small"
                                closable
                                @close="delMember(index)"
                                v-if="role.privilage === 3"
                                :key="role.memberErp"
                            >
                                {{ `${role.memberName}(${role.memberErp})` }}
                            </el-tag>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <template v-if="spacePrivilage.updateSpace">
            <div v-show="!addFormVisible">
                <el-button
                    @click="addFormVisible = true"
                    type="primary"
                    size="small"
                >
                    编辑
                </el-button>
            </div>
            <div
                v-show="addFormVisible"
                class="spacesetting-role__addbtn"
            >
                <el-button
                    @click="addFormVisible = false"
                    size="small"
                >
                    取消
                </el-button>
                <el-button
                    @click="saveMembers"
                    type="primary"
                    size="small"
                >
                    保存
                </el-button>
            </div>
        </template>
    </div>
</template>

<script type="text/javascript">
import { cloneDeep } from 'lodash';
import SpaceModel from '@/models/space';

export default {
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            memberList: [],
            addFormVisible: false,
            roleType: 2,
            keyword: '',
            editRoleList: [],
            total: 0,
            current: 1,
            pageSize: 20,
            userList: [],
            adminNum: 0,
            memberNum: 0,
            visitorNum: 0,
        };
    },
    created() {
        this.getMemberList();
    },
    methods: {
        // 查询空间成员列表
        getMemberList() {
            SpaceModel.getSpaceRoleSetting().then((data) => {
                this.memberList = data;
                this.editRoleList = cloneDeep(data);
            });
        },
        // 查询可添加用户列表
        getUserList(byCondition) {
            if (byCondition === true) {
                this.current = 1;
            }
            SpaceModel.getUserListForAdd({
                keyWord: this.keyword || this.$store.state.user.orgTierName,
                current: this.current,
                pageSize: this.pageSize,
            }).then((data) => {
                // map为兼容方案，account与name已不使用，后期删除
                this.userList = data.records.map((item) => {
                    const { account, name } = item;
                    return Object.assign(item, { userErp: item.userErp || account, userName: item.userName || name });
                });
                this.total = data.total;
            });
        },
        addMember({ userErp, userName }) {
            const member = {
                memberErp: userErp,
                memberName: userName,
                privilage: this.roleType,
            };
            // 查重 已有用户更新/新用户添加
            let checkFlag = true;
            this.editRoleList.forEach((item) => {
                if (item.memberErp === member.memberErp) {
                    Object.assign(item, member);
                    checkFlag = false;
                }
            });
            if (checkFlag) {
                this.editRoleList.push(member);
            }
        },
        addAll() {
            if (this.userList || this.userList.length > 0) {
                this.userList.forEach((userInfo) => {
                    this.addMember(userInfo);
                });
            }
        },
        delMember(index) {
            this.editRoleList.splice(index, 1);
        },
        saveMembers() {
            SpaceModel.updateSpaceRoleSetting('', { privilages: this.editRoleList }).then(() => {
                this.addFormVisible = false;
                this.getMemberList();
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
        clearAll(type) {
            const typeDic = {
                1: '管理员',
                2: '成员',
                3: '游客',
            };
            this.$confirm(`此操作将清空当前选中所有${typeDic[type]}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                const tmp = [];
                this.editRoleList.forEach((item) => {
                    if (item.privilage !== type) {
                        tmp.push(item);
                    }
                });
                this.editRoleList = tmp;
            });
        },
    },
    watch: {
        spaceKey() {
            this.getMemberList();
            this.addFormVisible = false;
        },
        addFormVisible(value) {
            if (value) {
                this.getUserList();
                this.getMemberList();
            } else {
                this.keyword = '';
                this.roleType = 2;
                this.current = 1;
            }
        },
        editRoleList: {
            handler() {
                this.adminNum = 0;
                this.memberNum = 0;
                this.visitorNum = 0;
                this.editRoleList.forEach((item) => {
                    switch (item.privilage) {
                    case 1:
                        this.adminNum += 1;
                        break;
                    case 2:
                        this.memberNum += 1;
                        break;
                    case 3:
                        this.visitorNum += 1;
                        break;
                    default:
                        break;
                    }
                });
            },
            deep: true,
        },
    },
};
</script>

<style lang="less">
    .spacesetting-role{
        &__users{
            margin-bottom: 5px;
            & .el-table__body-wrapper{
                overflow-y: auto;
                height: calc(~"100vh - 250px");
            }
            & i{
                color: #2695F1;
                font-size: 14px;
            }
        }
        &__edit{
            display: flex;
        }
        &__form{
            width: 700px;
            padding: 0 16px;
            border-right: solid 1px #f5f5f5;
            & .el-table__body-wrapper{
                height: calc(~"100vh - 360px");
                overflow-y: auto;
            }
        }
        &__result{
            padding: 16px;
            flex: 1;
        }
        &__delete{
            color: #2695F1;
            cursor: pointer;
        }
        &__header{
            height: 48px;
            overflow: hidden;
        }
        &__keyworld{
            float: right;
            right: 0;
            width: 320px;
            & .el-input-group__append{
                border-top-width: 0px;
                overflow: hidden;
                & button.el-button{
                    color: #fff;
                    background-color: #2695F1;
                    border-color: #2695F1;
                }
            }
        }
        &__roleType{
            float: left;
            left: 0;
            margin-top: 12px;
            width: 320px;
        }
        &__addlist{
            overflow-y: auto;
            margin-bottom: 16px;
            & .el-tag{
                margin-top: 5px;
                margin-right: 5px;
            }
        }
        & .table-head th{
            background-color: #f5f7fa;
        }
        & .el-icon-circle-plus{
            font-size: 16px;
            color: #2695F1;
            cursor: pointer;
        }
        &__page{
            margin-top: 10px;
            font-size: 12px;
            height: 20px;
            line-height: 20px;
            & div{
                display: inline-block;
                cursor: pointer;
                &.left{
                    float: left;
                }
                &:hover{
                    color: #2695F1;
                    text-decoration: underline;
                }
            }
        }
        &__num{
            font-size: 12px;
            margin: 0;
            & span{
                padding: 0 5px;
            }
        }
        &__addbtn{
            float: right;
        }
        &__clear{
            color: red;
            cursor: pointer;
            &:hover{
                text-decoration: underline;
            }
        }
    }
</style>
