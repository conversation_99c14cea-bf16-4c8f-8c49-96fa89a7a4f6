<template>
    <div class="space-setting">
        <div class="space-setting__content">
            <h3 class="space-setting__head">
                评审约束
            </h3>
            <el-radio
                v-model="formData.requiredReview"
                :label="0"
                :disabled="!spacePrivilage.updateSpace"
            >
                默认配置
            </el-radio>
            <p class="space-setting__content__tips">
                *配置此选项后，需求卡片可跳过Backlog评审阶段，或者直接进入迭代中。
            </p>
            <p class="space-setting__content__label">
                关键阶段示意：
            </p>
            <img src="../../../../assets/images/<EMAIL>"><br>
            <el-radio
                v-model="formData.requiredReview"
                :label="1"
                :disabled="!spacePrivilage.updateSpace"
            >
                评审约束
            </el-radio>
            <p class="space-setting__content__tips">
                *配置此选项后，需求来源为业务需求和产品需求的卡片必须经过”在【空间权限管理】中配置了权限的人员“在评审阶段评审后才能进入到后续阶段状态，并可记录详细的评审信息，其他类型的卡片可以根据实际业务选择评审约束。
            </p>
            <p class="space-setting__content__label">
                关键阶段示意：
            </p>
            <img src="../../../../assets/images/<EMAIL>">
            <template v-if="formData.requiredReview === 1">
                <p class="space-setting__content__label">
                    评审信息默认确认人：
                </p>
                <form-users-group
                    style="margin-left: 20px;"
                    v-model="formData.requiredReviewReviewers"
                    placeholder="请选择"
                    storage-key="card_reviewers"
                    :no-timline-users="[$store.state.user]"
                    :load-data="getMemberList"
                    :show-suggestions="false"
                    :disabled="!spacePrivilage.updateSpace"
                /><br>
                <el-checkbox
                    v-model="formData.requiredRdReview"
                    :true-label="1"
                    :false-label="0"
                    style="margin-left: 20px"
                >
                    研发需求强制评审
                </el-checkbox>
            </template><br><br>
            <h3 class="space-setting__head">
                需求变更约束
            </h3>
            <el-radio
                v-model="formData.requiredChange"
                :label="0"
                :disabled="!spacePrivilage.updateSpace"
            >
                默认配置
            </el-radio>
            <p class="space-setting__content__tips">
                *配置此选项后，卡片内容随时修改不受约束。
            </p>
            <el-radio
                v-model="formData.requiredChange"
                :label="1"
                :disabled="!spacePrivilage.updateSpace"
            >
                变更约束
            </el-radio>
            <p class="space-setting__content__tips">
                *配置此选项后，卡片在[准备]或[评审]阶段时，卡片内容随时修改不受约束，非[准备]或[评审]阶段时，若想修改卡片内容则需要提交需求变更内容，提交变更期间卡片被锁定无法编辑。
            </p>
            <h3 class="space-setting__head">
                工时填报约束
            </h3>
            <p class="space-setting__content__title">
                配置填报约束
            </p>
            <p class="space-setting__content__label">
                已开启
                <el-switch
                    v-model="formData.taskActionMqEnabled"
                    active-color="#08a4da"
                    active-value="true"
                    inactive-value="false"
                />
            </p>
            <p class="space-setting__content__tips">
                开启后将可设置允许已完成任务的工时填报有效天数（目前只针对零售集团PMP系统）
            </p>
            <p class="space-setting__content__label">
                有效天数
                <jacp-input-number
                    v-model="formData.taskActionMqDelayDays"
                    controls-position="right"
                    :min="0"
                    :max="30"
                    size="small"
                />
                天
            </p>
            <p class="space-setting__content__tips">
                设置天数后，当研发勾选了已完成的任务，将可在以上天数内填报工时，超期将不能填报，若特殊情况下需要填写工时建议自行取消完成的任务，即可再次填写该任务工时。
            </p>
            <h3 class="space-setting__head">
                关联项目约束
            </h3>
            <el-radio
                v-model="formData.requiredProjectRelation"
                :label="0"
                :disabled="!spacePrivilage.updateSpace"
            >
                默认配置
            </el-radio>
            <p class="space-setting__content__tips">
                *配置此选项后，需求分配到团队空间时，关联项目为非必填。
            </p>
            <el-radio
                v-model="formData.requiredProjectRelation"
                :label="1"
                :disabled="!spacePrivilage.updateSpace"
            >
                项目约束
            </el-radio>
            <p class="space-setting__content__tips">
                *配置此选项后，需求分配到团队空间时，关联项目为必填项，对于严格管控的团队适用。
            </p>
        </div>
        <div class="space-setting__content__footer">
            <el-button
                class="space-setting__content__save"
                type="primary"
                size="mini"
                v-show="spacePrivilage.updateSpace"
                @click="saveFlowOption"
            >
                应用
            </el-button>
        </div>
    </div>
</template>
<script type="text/javascript">
import SpaceModel from '@/models/space';
import CardModel from '@/models/card';
import { getSpaceId } from '@/plugins/utils';

export default {
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            formData: {
                spaceId: '',
                requiredReview: 0,
                requiredRdReview: 0,
                requiredChange: 0,
                requiredProjectRelation: 0,
                requiredReviewReviewers: [],
                taskActionMqDelayDays: '0',
                taskActionMqEnabled: 'false',
            },
            memberList: [],
            baseReviewers: [],
        };
    },
    methods: {
        getFlowOption(spaceKey) {
            SpaceModel.getFlowOption(getSpaceId(spaceKey)).then((data) => {
                this.resetForm(); // 重置表单，因为后端并不会返回表单上的所有数据，最后只会部分覆盖而缺省的数据会显示上一次formData的值，造成数据显示错误。
                Object.assign(this.formData, data, { spaceId: getSpaceId(spaceKey) });
                this.baseReviewers = (data.requiredReviewReviewers || []).slice();
            });
        },
        getTaskActionOption(spaceKey) {
            SpaceModel.getTaskActionOption(getSpaceId(spaceKey)).then((data) => {
                Object.assign(this.formData, data, { spaceId: getSpaceId(spaceKey) });
            });
        },
        saveFlowOption() {
            // 评审约束未开启，研发需求评审则关闭
            if (this.formData.requiredReview !== 1) {
                this.formData.requiredRdReview = 0;
            }
            SpaceModel.saveFlowOption(this.formData).then(() => {
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
                // 更新空间缓存数据
                this.$store.commit('chilldteamspace/setRequiredReview', this.formData);
            });
        },
        getMemberList(keyWord) {
            if (keyWord) {
                return Promise.resolve(this.memberList
                    .filter(member => member.name.includes(keyWord)
                    || member.erp.toLowerCase().includes(keyWord.toLowerCase())));
            }
            return Promise.resolve(this.memberList);
        },
        initSpaceMemberList(spaceId) {
            // 查询空间成员
            CardModel.getMemberList({
                spaceId,
            }).then((data) => {
                data.forEach((member, index) => {
                    Object.assign(data[index], {
                        erp: member.memberErp,
                        name: member.memberName,
                        orgTierCode: member.memberOrgId,
                        orgTierName: member.memberOrgName,
                    });
                });
                this.memberList = data.filter(member => member.privilage !== 3);
            });
        },
        resetForm() {
            this.formData = {
                spaceId: '',
                requiredReview: 0,
                requiredRdReview: 0,
                requiredChange: 0,
                requiredProjectRelation: 0,
                requiredReviewReviewers: [],
                taskActionMqDelayDays: '0',
                taskActionMqEnabled: 'false',
            };
        },
    },
    watch: {
        spaceKey: {
            handler(key) {
                if (key) {
                    this.getFlowOption(key);
                    this.initSpaceMemberList(getSpaceId(key));
                    this.getTaskActionOption(key);
                }
            },
            immediate: true,
        },
        'formData.requiredReview': {
            handler() {
                this.formData.requiredReviewReviewers = this.baseReviewers.slice();
            },
        },
    },
};
</script>

<style lang="less">
.space-setting {
  position: relative;
  &__head {
    margin: 0;
    height: 40px;
    color: #666;
    font-size: 18px;
    line-height: 40px;
    margin-bottom: 12px;
  }
  &__content {
        overflow-y: auto;
        height: calc(~"100vh - 260px");
    &__title {
        color: #2695F1;
        font-size: 14px;
        padding-left: 20px;
    }
    &__label {
        color: #333;
        font-size: 12px;
        padding-left: 20px;
    }
    &__tips {
      color: #999999;
      font-size: 12px;
      padding-left: 20px;
      margin-bottom: 16px;
    }
    &>img {
      width: 280px;
      margin-left: 20px;
      margin-bottom: 16px;
    }
    & .form-users__item{
        margin-right: 12px;
    }
    &__footer {
        border-top: 1px solid #c4c4c4;
      & button{
           margin-top: 6px;
      }
    }
  }
}
</style>
