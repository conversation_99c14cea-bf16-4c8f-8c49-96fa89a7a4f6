<template>
    <div class="customized-setting">
        <el-row
            :gutter="20"
            v-for="item in roleData"
            :key="item.group"
        >
            <el-col><span>{{ item.group }}</span></el-col>
            <el-row :gutter="20">
                <el-checkbox-group v-model="customizedRoleData.configIdList">
                    <el-col
                        :span="3"
                        v-for="el in item.configList"
                        :key="el.id"
                        class="customized-setting_col"
                    >
                        <el-checkbox
                            :label="el.id"
                            :disabled="read"
                        >
                            {{ el.name }}
                        </el-checkbox>
                    </el-col>
                </el-checkbox-group>
            </el-row>
        </el-row>

        <jacp-button
            v-if="spacePrivilage.updateSpace && read"
            @click="read = !read"
            type="primary"
            class="teamspace-spacesetting__button-first"
        >
            编辑
        </jacp-button>
        <jacp-button
            v-if="spacePrivilage.updateSpace && !read"
            type="primary"
            class="teamspace-spacesetting__button-first"
            @click="saveCustomizedRoleConfig"
        >
            保存
        </jacp-button>
        <jacp-button
            v-if="spacePrivilage.updateSpace && !read"
            @click="getRoleConfig"
            class="teamspace-spacesetting__button-first"
        >
            取消
        </jacp-button>
    </div>
</template>

<script>
import SpaceModel from '@/models/space';

export default {
    name: 'CustomizedRoleSetting',
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            customizedRoleData: {},
            roleData: [],
            read: true,
        };
    },
    created() {
        this.getRoleConfig();
    },
    methods: {
        getRoleConfig() {
            this.read = true;
            SpaceModel.getRoleConfig().then((data) => {
                this.roleData = data;
                SpaceModel.getCustomizedRoleConfig(this.spaceKey).then((cData) => {
                    this.customizedRoleData = cData;
                });
            });
        },
        saveCustomizedRoleConfig() {
            SpaceModel.saveCustomizedRoleConfig(this.customizedRoleData, this.spaceKey).then(() => {
                this.getRoleConfig();
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
    },
    watch: {
        spaceKey() {
            this.getRoleConfig();
        },
    },
};
</script>

<style lang="less">
.customized-setting{
    & .el-row {
        margin-bottom: 20px;
        &:last-child {
         margin-bottom: 0;
        }
    }
    & .customized-setting_col {
        margin: 25px 0 0 20px;
    }
}

</style>
