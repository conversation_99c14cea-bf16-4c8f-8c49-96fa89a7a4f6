<template>
    <div class="tag-form">
        <div
            v-if="!spacePrivilage.updateSpace && !tags.length"
            style="color: #666666; "
        >
            暂无标签
        </div>
        <custom-tag-group
            v-model="tags"
            colorful
            :editable="spacePrivilage.updateSpace"
            :tag-editable="spacePrivilage.updateSpace"
            :max-count="200"
            :max-length="20"
            :validator="validateTags"
            @on-delete="deleteTag"
            @on-blur="saveTag"
        />
        <span
            v-if="!spacePrivilage.updateSpace"
            class="tag__tips"
        >您没有权限添加卡片标签，快喊管理员来添加吧！</span>
    </div>
</template>

<script>
import trim from 'lodash/trim';
import SpaceModel from '@/models/space';
import { getSpaceId } from '@/plugins/utils';
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */

export default {
    name: 'SpaceTag',
    mounted() {
        this.load();
    },
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            tags: [],
        };
    },
    computed: {
        hasError() {
            if (this.tags.length) {
                return !this.tags.every(item => !item.errorText);
            }
            return false;
        },
    },
    methods: {
        async load() {
            const { spaceKey } = this.$route.params;
            this.tags = await this.$store.dispatch('chilldteamspace/fetchTagsList', getSpaceId(spaceKey));
        },
        refresh() {
            this.$store.commit('chilldteamspace/updateState', {
                tagsList: this.tags,
            });
        },
        deleteTag(item) {
            const { spaceKey } = this.$route.params;
            SpaceModel.deleteNewTags(spaceKey, item.id)
                .then(this.refresh)
                .catch(this.load);
        },
        validateTags(item, tags = []) {
            const isDuplication = () => tags.filter(tag => tag.name === item.name).length > 1;
            if (isDuplication()) {
                return '标签名重复';
            }
            const re = /[\\*/?？”|~#^$@%&!*()<>:;'"{}【】]/gi;
            let errorText = '';
            switch (true) {
            // case calcLength(trim(item.name)) > 10:
            // errorText = '最多输入5个中文或10个英文';
            // break;
            case trim(item.name).lenth > 20:
                errorText = '最多输入20个字符';
                break;
            case re.test(trim(item.name)):
                errorText = '非法的字符';
                break;
            case trim(item.name) === '':
                errorText = '标签名称不能为空';
                break;
            default:
                break;
            }
            return errorText;
        },
        saveTag(tag) {
            const { spaceKey } = this.$route.params;
            const params = {
                id: tag.id,
                name: trim(tag.name),
                color: tag.color,
            };
            if (tag.id) {
                SpaceModel.updateNewTags(spaceKey, params);
                return;
            }
            SpaceModel.saveNewTags(spaceKey, params)
                .then((_tagId) => {
                    const target = this.tags.find(o => o.name === tag.name);
                    if (target) {
                        this.$set(target, 'id', _tagId);
                    }
                }).then(this.refresh);
        },
    },
    watch: {
        spaceKey() {
            this.load();
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
  .tag-form{
    min-height: 480px;
    overflow-y: auto;
    max-height: calc(100vh - 168px);
    align-content: baseline;
    display: flex;
    flex-wrap: wrap;
    font-size: 12px;
  }
  .tag__tips{
      color: #E1E1E1;
      position: absolute;
      bottom: 10px;
  }
</style>
