<template>
    <div>
        <!-- 自定义卡片状态 -->
        <el-table
            ref="customizeCardStatusTable"
            :data="customizeCardStatusList"
        >
            <el-table-column
                type="index"
                label="序号"
            />
            <el-table-column
                prop="originName"
                label="卡片状态"
                width="180"
            />
            <el-table-column
                prop="name"
                label="别名"
                width="180"
            >
                <template slot-scope="scope">
                    <span v-if="onlyRead">{{ scope.row.name }}</span>
                    <el-input
                        v-else
                        type="text"
                        v-model="scope.row.name"
                        size="mini"
                    />
                </template>
            </el-table-column>
            <el-table-column
                prop="code"
                label="空间卡片数量"
                align="center"
                width="120"
            >
                <template slot-scope="scope">
                    <span>{{ restCardCountMap[scope.row.code] }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="show"
                label="是否开启"
                align="center"
                width="90px"
            >
                <template slot-scope="scope">
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="`该状态下还有${restCardCountMap[scope.row.code]}个卡片，请改变卡片状态后再操作`"
                        placement="top-start"
                        v-if="restCardCountMap[scope.row.code]"
                    >
                        <el-checkbox
                            v-model="scope.row.show"
                            :disabled="true"
                        />
                    </el-tooltip>
                    <el-checkbox
                        v-else
                        v-model="scope.row.show"
                        :disabled="onlyRead || scope.row.disabled"
                    />
                </template>
            </el-table-column>
        </el-table>
        <div class="j-warning">
            <i class="el-icon-warning" />
            <p>
                1、卡片系统状态不可修改，只可更改状态别名。<br>
                2、卡系统状态包括：待开发、开发中、待测试、测试中、待上线、待验证、完成、挂起<br>
                3、状态可选择是否开启，关闭后看板视图中将不显示对应状态列
            </p>
        </div>
        <div
            style="margin-top: 20px;"
            v-if="editAble"
        >
            <el-button
                size="small"
                v-if="!this.editMode"
                @click="editMode = true"
                type="primary"
            >
                编辑
            </el-button>
            <el-button
                size="small"
                v-if="this.editMode"
                @click="saveCustomizeCardStatus"
                type="primary"
            >
                保存
            </el-button>
            <el-button
                size="small"
                v-if="this.editMode"
                @click="cancel"
                type="default"
            >
                取消
            </el-button>
        </div>
    </div>
</template>
<script>
import SpaceModel from '@/models/space';

export default {
    name: 'CustomizeCardStatusSetting',
    data() {
        return {
            editMode: false,
            customizeCardStatus: {},
            restCardCountMap: {},
            customizeCardStatusList: [],
        };
    },
    props: {
        spacePrivilage: {
            type: Object,
        },
        editAble: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        onlyRead() {
            return !this.editAble || !this.editMode;
        },
        spaceKey() {
            return this.$route.params.spaceKey;
        },
    },
    mounted() {
        this.load();
    },
    methods: {
        load() {
            const { spaceKey } = this.$route.params;
            SpaceModel.getCustomizeCardStatus({ spaceKey }).then((data) => {
                this.customizeCardStatus = Object.assign({}, data);
                this.setCustomizeCardStatusList(data);
            });
            SpaceModel.getRestCardCount(spaceKey).then(this.setRestCardCount);
        },
        saveCustomizeCardStatus() {
            const { fields } = this.customizeCardStatus;
            fields.forEach((item) => {
                if (item.fieldKey === 'cardStatus') {
                    // eslint-disable-next-line
                    item.options = this.customizeCardStatusList;
                }
            });
            SpaceModel.saveCustomizeCardStatus({
                spaceKey: this.spaceKey,
                fields,
            }).then((data) => {
                if (data) {
                    this.customizeCardStatus = Object.assign({}, data);
                    this.editMode = false;
                    this.$notify({
                        title: '保存成功！',
                        type: 'success',
                    });
                } else {
                    this.$notify({
                        title: '保存失败！',
                        message: data.message,
                        type: 'error',
                    });
                }
            }).catch(({ response }) => {
                const { data } = response;
                // 如果因为数量问题导致保存异常，需要更新一下当前表单field
                if (data && data.code === 400) {
                    this.load();
                }
            });
        },
        setCustomizeCardStatusList(data) {
            let cardStatusList = [];
            if (data.fields && data.fields.length) {
                cardStatusList = [].concat(data.fields.filter(item => item.fieldKey === 'cardStatus')[0].options);
            }
            // 为了方便恢复原状，创建个副本
            this.customizeCardStatusList = JSON.parse(JSON.stringify(cardStatusList));
        },
        setRestCardCount(data) {
            this.restCardCountMap = Object.assign({}, data);
        },
        cancel() {
            this.setCustomizeCardStatusList(this.customizeCardStatus);
            this.editMode = false;
        },
    },
    watch: {
        '$route.params.spaceKey': {
            handler() {
                this.load();
            },
        },
    },
};
</script>
