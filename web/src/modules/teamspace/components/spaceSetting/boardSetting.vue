<template>
    <div class="board-setting">
        <jacp-left-menu
            mode="horizontal"
            :default-active="activeName"
            :data="listEntities"
        />
        <div class="board-setting__container">
            <div class="board-setting__left">
                <jacp-text
                    size="12"
                    type="disable"
                    class="board-setting__preview-title"
                >
                    预览效果
                </jacp-text>
                <div class="board-setting__preview-wrapper">
                    <component
                        class="board-setting__preview-content"
                        :is="activeBoardItemComponent.component"
                        :fields="currentForm.fields.filter(field => field.required)"
                        v-bind="activeBoardItemComponent.props"
                    >
                        <!-- 扩展字段的预览都用省略号代替 -->
                        <description-item
                            v-for="field in extendedFields"
                            :key="field.fieldKey"
                            value-type="text"
                            :label="field.fieldName"
                            value="..."
                        />
                    </component>
                </div>
            </div>
            <div class="board-setting__right">
                <blockquote class="j-blockquote">
                    <i
                        class="el-icon-warning j-mgr8"
                        style="font-size: 14px;color: #2695F1"
                    /><jacp-text size="12">只有团队空间管理员，才能对【看板显示设置】进行操作</jacp-text>
                </blockquote>
                <!-- <div
                    style="background: #E9F4FD;line-height:32px;border-radius: 4px;"
                    class="j-mgb16"
                > -->

                <!-- </div> -->
                <jacp-grid-checkbox
                    class="j-mgt16"
                    title="系统字段"
                    :data="fields.filter(field => !field.extended)"
                    label-key="fieldName"
                    item-width="134px"
                >
                    <template v-slot:item="{item}">
                        <ellipsis-text
                            :content="item.fieldName"
                            :max-width="!tips[item.fieldKey] ? 134 : 98"
                        />
                        <el-tooltip
                            v-if="tips[item.fieldKey]"
                            style="transform: translateY(-4px);"
                        >
                            <div
                                style="max-width: 350px"
                                slot="content"
                            >
                                {{ tips[item.fieldKey] }}
                            </div>
                            <jacp-icon
                                name="jacp-icon-help"
                                active
                            />
                        </el-tooltip>
                    </template>
                </jacp-grid-checkbox>
                <jacp-grid-checkbox
                    v-if="fields.some(field => field.extended)"
                    title="扩展字段"
                    :data="fields.filter(field => field.extended)"
                    label-key="fieldName"
                    item-width="134px"
                >
                    <template v-slot:item="{item}">
                        <ellipsis-text
                            :content="item.fieldName"
                            :max-width="!tips[item.fieldKey] ? 134 : 98"
                        />
                        <el-tooltip
                            v-if="tips[item.fieldKey]"
                            style="transform: translateY(-4px);"
                        >
                            <div
                                style="max-width: 350px"
                                slot="content"
                            >
                                {{ tips[item.fieldKey] }}
                            </div>
                            <jacp-icon
                                name="jacp-icon-help"
                                active
                            />
                        </el-tooltip>
                    </template>
                </jacp-grid-checkbox>
            </div>
        </div>
        <div
            class="teamspace-spacesetting__footer"
        >
            <el-button
                type="primary"
                @click="save"
                :disabled="!spacePrivilage.updateSpace"
            >
                应用
            </el-button>
            <el-button
                @click="reset"
                :disabled="!spacePrivilage.updateSpace || !currentForm.id"
            >
                恢复默认
            </el-button>
        </div>
    </div>
</template>
<script>
import { FieldSetting } from '@/modules/teamspace/models/BoardSetting';
// import { listEntities } from '@/modules/teamspace/constant';
import { mockBoardCard } from '@/modules/card/models/BoardCard';
import CardsBoardItem from '@/modules/card/components/cardsBoardItem';
import Task from '@/modules/task/models/task';
import TaskBoardItem from '@/modules/task/components/taskBoardItem';
import { event } from '@/plugins/event';

const boardItemCompMap = {
    card: {
        component: 'CardsBoardItem',
        props: {
            data: mockBoardCard,
            style: 'width: 263px',
        },
    },
    task: {
        component: 'TaskBoardItem',
        props: {
            task: new Task({
                code: 'T2020110203809',
                content: '这是一个任务',
            }),
            style: 'width: 168px',
            class: 'kanban-block__inner',
        },
    },
};

export default {
    name: 'BoardSetting',
    components: {
        CardsBoardItem,
        TaskBoardItem,
    },
    data() {
        return {
            activeName: 'card',
            listEntities: [{ id: 'card', name: '卡片' }],
            boardFieldsMap: {},
            metaformMap: {},
            tips: {
                stayTimeText: '记录卡片在当前状态下的停留时长，在卡片中以数值方式显示，一天代表24小时，不足8小时的不显示。卡片拖动到其他状态后停留时长重新计算。',
                stayTimeDots: '记录卡片在当前状态下的停留时长，在卡片底部以小圆点方式展示，一个圆点代表一天（24小时），不足8小时的不显示。卡片拖动到其他状态后停留时长重新计算。',
                locked: '当开启了评审约束后，一旦卡片经历了评审阶段就会在卡片上显示“评审”标识图标',
                reviewFlag: '当开启了需求变更约束后，一旦卡片发生了内容变更就会在卡片上显示“变更”标识图标',
            },
        };
    },
    created() {
        event.$on('template-changed', ({ spaceId }) => {
            if (spaceId === this.spaceId) {
                this.currentForm.cleanCache();
                this.load();
            }
        });
    },
    computed: {
        activeBoardItemComponent() {
            return boardItemCompMap[this.activeName];
        },
        spacePrivilage() {
            return this.$store.state.chilldteamspace.spacePrivilage;
        },
        spaceId() {
            return this.$store.getters['chilldteamspace/currentSpaceId'];
        },
        formInfo() {
            return {
                formKey: FieldSetting.FormKeyMap[this.activeName],
                orgId: this.spaceId,
            };
        },
        fields() {
            if (!this.currentForm) {
                return [];
            }
            if (!this.spacePrivilage.updateSpace) {
                return this.currentForm.fields.map(f => ({ ...f, disabled: true }));
            }
            return this.currentForm.fields || [];
        },
        extendedFields() {
            return this.fields.filter(f => f.extended && f.required);
        },
        currentForm() {
            return this.metaformMap[this.activeName];
        },
    },
    methods: {
        // TODO: 是否和store的合并一下？
        initMetaform() {
            this.$set(this.metaformMap, this.activeName, new FieldSetting(this.formInfo, {
                cacheConfig: JSON.stringify(this.formInfo),
            }));
        },
        load() {
            if (!this.currentForm) {
                this.initMetaform();
            }
            if (this.loading) {
                return;
            }
            this.loading = true;
            this.currentForm.load().then(() => {
                this.loading = false;
            });
        },
        save() {
            this.currentForm.save()
                .then(() => {
                    if (!this.currentForm.id) {
                        this.currentForm.load({}, true);
                    }

                    this.$message.success({
                        duration: 3500,
                        message: (
                            <div style="font-size: 12px;">
                                <span>字段显示已生效，前往</span>
                                <el-button
                                    style="padding: 0;"
                                    type="text"
                                    onClick={this.gotoCardList}>卡片看板</el-button></div>),
                    });
                });
        },
        reset() {
            this.$confirm('是否恢复默认?').then(() => {
                this.currentForm.resetDefault().then(() => {
                    this.currentForm.cleanCache();
                    this.load();
                });
            });
        },
        gotoCardList() {
            this.$router.push({
                name: 'teamspaceCardsList',
                params: {
                    spaceKey: this.$router.currentRoute.params.spaceKey,
                },
                query: {
                    view: 'board',
                },
            });
        },
    },
    watch: {
        activeName: {
            handler() {
                this.load();
            },
        },
        spaceId: {
            immediate: true,
            handler() {
                this.initMetaform();
                // delete this.metaformMap[this.activeName];
                this.load();
            },
        },
    },
};
</script>
<style lang="less">

.test-css-var{
  background-color: var(--color--base--mask);
  font-weight: var(--font-weight-bold);
  border: var(--border--hr);
  padding: var(--gutter--base);
  font-size: var(--text-height);
}
.board-setting{
    display: grid;
    grid-template-rows: 48px calc(100% - 112px) 64px;
    &__container{
        display: flex;
        flex-basis: 100%;
        background-color: var(--color--base--background);
        border-top: var(--border--hr);
    }
    &__left{
        height: 100%;
    }
    &__right{
        flex-basis: 100%;
        background-color: var(--color--primary-white);
        padding: var(--gutter--medium);
        overflow:auto;
    }
    &__preview{
        &-title{
            display: block;
            margin-top: var(--gutter--medium);
            margin-left: var(--gutter--large);
        }
        &-wrapper{
            height: calc(~"100% - 48px");
            margin: var(--gutter--small) var(--gutter--large);
            overflow-y: auto;
        }


    }
}
</style>
