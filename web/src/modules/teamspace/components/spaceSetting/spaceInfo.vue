<template>
    <div>
        <jacp-infos
            class="j-block-data teamspace-spacesetting__basicinfo"
            :label-width="80"
            v-if="read"
        >
            <jacp-info-item
                label="空间标识"
                :value="formData.key"
            />
            <jacp-info-item
                label="空间名称"
                :value="formData.name"
            />
            <jacp-info-item
                label="空间类型"
                :value="typeMap[formData.mode]"
            />
            <jacp-info-item
                label="空间描述"
                :value="formData.desc"
            />
            <jacp-info-item
                label="默认项目"
                :value="formData.defaultProjectName"
            />
            <jacp-info-item
                v-if="prdmAvailable"
                label="关联产品"
                :value="formData.product && formData.product.name ? formData.product.name : '暂无'"
            />
        </jacp-infos>
        <el-form
            class="teamspace-spacesetting__form"
            label-suffix="："
            label-width="80px"
            v-if="read === false"
        >
            <el-form-item label="空间标识">
                <span class="form-item-readonly">
                    {{ formData.key }}
                </span>
            </el-form-item>
            <el-form-item label="空间名称">
                <el-input
                    v-model="formData.name"
                    :maxlength="20"
                    placeholder="请输入"
                />
            </el-form-item>
            <el-form-item label="空间类型">
                <span class="form-item-readonly">
                    {{ typeMap[formData.mode] }}
                </span>
            </el-form-item>
            <el-form-item label="空间描述">
                <el-input
                    v-model="formData.desc"
                    :maxlength="150"
                    type="textarea"
                    placeholder="请输入"
                />
            </el-form-item>
            <el-form-item label="默认项目">
                <jacp-input-select
                    v-model="formData.defaultProjectId"
                    :data="usableProjectList"
                    clearable
                    :filterable="true"
                    placeholder="请选择"
                />
            </el-form-item>
            <el-form-item
                label="关联产品"
                v-if="prdmAvailable"
            >
                <jacp-prdm-productline-tree
                    v-model="formData.product"
                    :popper-append-to-body="true"
                />
            </el-form-item>
        </el-form>
        <jacp-button
            v-if="spacePrivilage.updateSpace && read"
            @click="read = !read"
            type="primary"
            class="teamspace-spacesetting__button-first"
        >
            编辑
        </jacp-button>
        <jacp-button
            v-if="spacePrivilage.updateSpace && !read"
            type="primary"
            @click="saveSpaceInfo"
            class="teamspace-spacesetting__button-first"
        >
            保存
        </jacp-button>
        <jacp-button
            v-if="spacePrivilage.updateSpace && !read"
            @click="cancel(true)"
            class="teamspace-spacesetting__button-first"
        >
            取消
        </jacp-button>
        <jacp-button
            v-if="read"
            type="danger"
            :on-click="quitSpace"
        >
            退出
        </jacp-button>
        <jacp-button
            type="danger"
            v-if="spacePrivilage.updateSpace && read"
            :on-click="removeSpace"
        >
            删除空间
        </jacp-button>
    </div>
</template>

<script type="text/javascript">
import { mapState } from 'vuex';
import SpaceModel from '@/models/space';
import { clearRepeatInArray } from '@/plugins/utils';
import event from '$platform.event';

export default {
    components: {
        // prdmProductlineTree: AsyncComp,
    },
    props: {
        spaceKey: {
            type: String,
            default: '',
        },
        spacePrivilage: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            read: true,
            formData: {
                key: '',
                name: '',
                desc: '',
                mode: '',
                defaultProjectId: '',
                defaultProjectName: '',
                product: '',
            },
            attachProjectList: [], // 可选附加项目列表
            typeMap: {
                2: '敏捷类型',
                1: '通用类型',
            },
            productEditing: false,
        };
    },
    created() {
        this.getSpaceInfo();
    },
    methods: {
        getSpaceInfo() {
            // 重置页面编辑状态
            this.read = true;
            SpaceModel.getDetail().then((data) => {
                this.formData.key = data.key;
                this.formData.name = data.name;
                this.formData.desc = data.desc;
                this.formData.mode = data.mode;
                this.formData.defaultProjectId = (data.pmpProject || {}).pmpProjectId;
                this.formData.defaultProjectName = (data.pmpProject || {}).pmpProjectName;

                this.formData.product = data.product;
            });
        },
        saveSpaceInfo() {
            const params = { ...this.formData };
            if (this.prdmAvailable && this.formData.product) {
                const { product } = this.formData;
                if (product) {
                    const { id, name, type } = product;
                    Object.assign(params, {
                        product: {
                            id, name, type,
                        },
                    });
                }
            }
            if (!this.prdmAvailable) {
                delete params.product;
            }
            SpaceModel.updateSpaceBasicInfo('', params).then(() => {
                // 重置页面编辑状态
                const { defaultProjectId } = this.formData;
                this.formData.defaultProjectName = this.curProjectName(defaultProjectId);
                this.formData.product = params.product;
                this.read = true;
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
        cancel() {
            this.getSpaceInfo();
        },
        // 删完空间，要跳转到空间列表
        removeSpace() {
            const message = this.formData.mode === 1 ? 'jacp.delGeneralHints' : 'jacp.delhints';
            this.$confirm(this.$t(message), this.$t('jacp.deltitle'), {
                confirmButtonText: this.$t('jacp.button.remove'),
                cancelButtonText: this.$t('jacp.button.cancel'),
                type: 'warning',
            }).then(() => {
                SpaceModel.removeSpace().then(() => {
                    this.$router.push({
                        name: 'teamspaceSpaceList',
                    });
                    event.$emit('refresh-space-list');
                });
            });
        },
        async quitSpace() {
            if (this.spacePrivilage.updateSpace) {
                // for admin
                const adminCount = await SpaceModel.countSpaceAdmin(this.spaceKey);
                if (adminCount <= 1) {
                    this.$message({
                        message: this.$t('jacp.quitSpaceNotAllowed'),
                        type: 'warning',
                    });
                    return;
                }
            }
            const ok = await this.$confirm(this.$t('jacp.quitSpace'), this.$t('jacp.quitSpaceTitle'), {
                confirmButtonText: this.$t('jacp.button.quit'),
                cancelButtonText: this.$t('jacp.button.cancel'),
                type: 'warning',
            });
            if (ok) {
                SpaceModel.quitSpace(this.spaceKey).then(() => {
                    this.$router.push({
                        name: 'teamspaceSpaceList',
                    });
                    event.$emit('refresh-space-list');
                });
            }
        },
        curProjectName(id) {
            if (id) {
                const p = this.projectList.filter(item => item.id === id).pop();
                if (p) {
                    return p.name;
                }
            }
            return null;
        },
        curVendorName(id) {
            if (id) {
                const v = this.spaceVendors.filter(item => item.id === id).pop();
                if (v) {
                    return v.name;
                }
            }
            return null;
        },
    },
    watch: {
        spaceKey() {
            this.getSpaceInfo();
        },
    },
    computed: {
        ...mapState({
            projectList: state => state.chilldteamspace.projectList || [],
        }),
        // 检查是否有产品管理的权限
        prdmAvailable() {
            return this.$store.getters['root/getActiveModuleByCode']('prdm');
        },
        usableProjectList() {
            const allList = this.projectList.concat(this.attachProjectList);
            return clearRepeatInArray(allList);
        },
    },
};
</script>

<style lang="less">
    .teamspace-spacesetting__basicinfo {
        width: 1000px;
        &__line {
            display: flex;
            &-item {
                width: 200px;
            }
        }
    }
    .teamspace-spacesetting__form {
        .form-item-readonly {
            font-size: 12px;
        }
        .el-form-item__label{
            text-align: left;
        }
    }
    .teamspace-spacesetting__prodtree-icon{
        padding-right: 4px;
        .el-icon-circle-close{
            display: none;
        }
        &:hover{
            .el-icon-edit{
                display:none;
            }
            .el-icon-circle-close{
                display:inline-block;
                cursor: pointer;
            }
        }
    }
</style>
