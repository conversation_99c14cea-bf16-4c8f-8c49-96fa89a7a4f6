import { Message } from 'element-ui';
import http from '@/plugins/http';
import { pick } from 'lodash';
import StepPermission from './stepPermission';

// 步骤状态： 0=关闭，1=启用
export const STEP_STATUS = {
    OFF: 0,
    ON: 1,
};

export default class TransitionStep {
    constructor(props = {}) {
        const {
            id,
            stepName = '',
            workitemType = 'card',
            stepStatus,
            spaceId,
            sourceStatus, targetStatus,
            permission, requireFields,
        } = props;
        this.id = id;
        this.stepStatus = stepStatus;
        this.stepName = stepName;
        this.workitemType = workitemType;
        this.spaceId = spaceId;
        // 起始状态 / 目标状态
        this.sourceStatus = sourceStatus;
        this.targetStatus = targetStatus;

        // 步骤限制
        this.requireFields = requireFields; // TODO:
        this.permission = StepPermission.createInstance(permission);
    }

    get on() {
        return this.stepStatus === STEP_STATUS.ON && this.permission.on;
    }

    changePermission(permission) {
        this.permission = StepPermission.createInstance(permission);
    }

    update(params = {}) {
        return TransitionStep.update({ ...this, ...params }).then(() => {
            Object.assign(this, params);
            return this;
        });
    }

    static update(step = {}) {
        return http.put(`v1/space/${step.spaceId}/transition/step/${step.id}`, step)
            .then(() => {
                Message.success('保存成功');
            });
    }

    static updateStatusBatch(spaceId, steps = []) {
        return http.put(`v1/space/${spaceId}/transition/step/status`, steps.map(step => pick(step, ['id', 'stepStatus'])));
    }

    static loadSteps(spaceId, workitemType = 'card') {
        return http.get(`v1/space/${spaceId}/transition/step`, {
            params: {
                workitemType,
            },
        }).then((data = []) => data.map(step => new TransitionStep(step)));
    }

    static getStartAndEndStatus(step, status = []) {
        const { sourceStatus: sourceStatusCode, targetStatus: targetStatusCode } = step;
        const sourceStatus = status.find(s => s.statusCode === sourceStatusCode) || {};
        const targetStatus = status.find(s => s.statusCode === targetStatusCode) || {};
        return { sourceStatus, targetStatus };
    }
}
