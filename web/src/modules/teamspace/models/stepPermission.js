/* eslint-disable no-use-before-define */
export const PERMISSIONS_TYPE = {
    NONE: 0,
    ROLE: 1,
    USER: 2,
};
export const PERMISSIONS_TYPE_KEY = {
    1: 'roleCodes',
    2: 'users',
};

export default class StepPermission {
    constructor(type, list = []) {
        this.permissionType = type;
        if (type in PERMISSIONS_TYPE_KEY) {
            this[PERMISSIONS_TYPE_KEY[type]] = list;
        }
    }

    get on() {
        return this.list && this.list.length;
    }

    get list() {
        return this[PERMISSIONS_TYPE_KEY[this.permissionType]];
    }

    set list(val) {
        this[PERMISSIONS_TYPE_KEY[this.permissionType]] = val;
    }

    static createInstance(permission = {}) {
        const {
            permissionType,
        } = permission;
        const list = permission[PERMISSIONS_TYPE_KEY[permissionType]];
        return new StepPermission(permissionType, list);
    }
}
