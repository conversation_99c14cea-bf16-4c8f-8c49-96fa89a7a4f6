import http from '@/plugins/http';

export default class SpaceGroup {
    constructor({
        id,
        groupName,
        ownerErp,
        spaceIds = [],
    }) {
        this.id = id;
        this.groupName = groupName;
        this.ownerErp = ownerErp;
        this.spaceIds = spaceIds;
    }

    update(params = {}) {
        const { id, groupName, ownerErp } = this;
        return http.put(`v1/space/group/${id}`, {
            id, groupName, ownerErp, ...params,
        }).then(() => Object.assign(this, params));
    }

    remove() {
        return http.delete(`v1/space/group/${this.id}`);
    }

    static create(group = {}) {
        return http.post('v1/space/group', group).then(data => new SpaceGroup(data));
    }

    static getList() {
        return http.get('v1/space/group')
            .then((data = []) => data.map(g => new SpaceGroup(g)));
    }

    static add({ spaceId, groupId }) {
        return http.post('v1/space/group/relation', { spaceId, groupId });
    }

    static shift({ spaceId, groupId }) {
        return http.delete(`v1/space/group/relation/${spaceId}/${groupId}`);
    }
}
