import { beforeEnterWrapper } from '$module/utils/validateAuth';
import store from '$platform.store';
import { space as SpaceModel } from '@/models/teamspace';
import { generalCard, scrumCard, teamspaceCardDetailByCode } from '@/modules/card/routes';
import modulePointRender from '@/modules/teamspace/components/extensionPoint/teamspaceModulePointRender';
import layout from '@/modules/teamspace/layout';
import Index from '@/modules/teamspace/layout.index';

console.log('-arch-teamspace-routes------');

const addRecentSpaceList = (spaceKey) => {
    if (!spaceKey) return;
    store.commit('chilldteamspace/addRecentSpaceList', spaceKey);
};
const redirectToBacklog = (to, from, next) => {
    next({
        name: 'teamspaceBacklog',
        params: { ...to.params },
        query: { ...to.query },
    });
};
// 做一下详情页的跳转的重定向
const redirectToCardDetail = (to, from, next) => {
    next({
        name: to.query.sprintId && +to.query.sprintId === -1 ? 'teamspaceBacklog' : 'teamspaceCardDetail',
        params: { ...to.params },
        query: { ...to.query },
    });
};
const routers = [{
    name: 'teamspace',
    path: 'teamspace',
    component: Index,
    props: route => ({ spaceKey: route.params.spaceKey }),
    redirect: {
        name: 'teamspaceSpaceList',
    },
    beforeEnter: (to, from, next) => SpaceModel.getList().then(next),
    children: [...teamspaceCardDetailByCode, {
        path: 'backlog/:spaceKey',
        name: 'teamspaceBacklog',
        component: layout.backlog,
        props: route => ({
            spaceKey: route.params.spaceKey,
            cardId: route.query.cardId,
        }),
        beforeEnter: beforeEnterWrapper.validateSpaceAuth,
    }, {
        path: 'spacelist',
        name: 'teamspaceSpaceList',
        component: layout.spaceList,
        meta: {
            noHeaderMenu: true,
            noSpaceList: true,
        },
    }, {
        path: 'spaceintro/:spaceKey',
        name: 'teamspaceSpaceIntro',
        component: layout.spaceIntro,
        props: route => ({ spaceKey: route.params.spaceKey }),
        beforeEnter: beforeEnterWrapper.validateSpaceAuth,
        meta: {
            permissionRequired: true,
        },
    }, {
        path: 'spacesetting/:spaceKey/:menu?',
        name: 'teamspaceSpaceSetting',
        component: layout.spaceSetting,
        beforeEnter: beforeEnterWrapper.validateSpaceAuth,
        props: route => ({ spaceKey: route.params.spaceKey, menu: route.params.menu }),
        meta: {
            permissionRequired: true,
        },

    }, {
        path: 'calendar/:spaceKey/',
        name: 'teamspaceCalender',
        component: layout.calendar,
        beforeEnter: beforeEnterWrapper.validateSpaceAuth,
        meta: {
            permissionRequired: true,
        },
    }, {
        path: 'footmark/:spaceKey/',
        name: 'teamspaceFootmark',
        component: layout.footmark,
        beforeEnter: beforeEnterWrapper.validateSpaceAuth,
        props: route => ({ spaceKey: route.params.spaceKey }),
        meta: {
            permissionRequired: true,
        },
    }, {
        path: 'leaderView',
        name: 'teamspaceLeaderView',
        component: layout.leaderView,
        meta: {
            title: '管理者的空间',
            noHeaderMenu: true,
            noSpaceList: true,
        },
    }, {
        path: 'leaderViewPromise',
        name: 'teamspaceLeaderViewPromise',
        component: layout.leaderViewPromise,
        meta: {
            title: '领域授权',
            noHeaderMenu: true,
            noSpaceList: true,
        },
    }, {
        path: 'codeRepo/:spaceKey',
        name: 'teamspaceCodeRepo',
        component: layout.codeRepo,
        meta: {
            permissionRequired: true,
        },
    }, {
        path: 'sprintReport/:spaceKey',
        name: 'teamspaceSprintReport',
        component: layout.scrum,
        meta: {
            permissionRequired: true,
        },
    }, {
        path: 'cardlist/:spaceKey',
        name: 'teamspaceCardsList',
        component: layout.scrum,
        props: route => ({
            sprintId: route.query.sprintId || route.query.planId,
        }),
        beforeEnter: (to, from, next) => {
            // TODO: 做旧卡片路由的兼容
            // http://jagile.jd.com/teamspace/cardlist/ptss?cardId=102785&planId=6407
            // 进入空间时，将空间加入最近访问
            addRecentSpaceList(to.params.spaceKey);
            // 判断一下backlog的进入backlog页面
            if (to.query.sprintId && +to.query.sprintId === -1) {
                redirectToBacklog(to, from, next);
            } else if (to.query.cardId && to.name !== 'teamspaceCardDetail') {
                redirectToCardDetail(to, from, next);
            } else {
                beforeEnterWrapper.validateSpaceAuth(to, from, next);
            }
        },
        children: scrumCard,
    },
    {
        path: 'tasklist/:spaceKey',
        name: 'teamspaceTaskList',
        component: layout.scrum,
        // TODO: 是否和上面的beforeEnter融合一下
        beforeEnter: (to, from, next) => {
            // TODO: 做旧卡片路由的兼容
            // http://jagile.jd.com/teamspace/cardlist/ptss?cardId=102785&planId=6407
            // 进入空间时，将空间加入最近访问
            addRecentSpaceList(to.params.spaceKey);
            // 判断一下backlog的进入backlog页面
            if (to.query.sprintId && +to.query.sprintId === -1) {
                redirectToBacklog(to, from, next);
            } else if (to.query.cardId && to.name !== 'teamspaceCardDetail') {
                redirectToCardDetail(to, from, next);
            } else {
                beforeEnterWrapper.validateSpaceAuth(to, from, next);
            }
        },
    },
    {
        path: 'general/:spaceKey',
        name: 'teamspaceGeneral',
        component: layout.general,
        redirect: {
            name: 'generalCardList',
        },
        beforeEnter: (to, from, next) => {
            addRecentSpaceList(to.params.spaceKey);
            beforeEnterWrapper.validateSpaceAuth(to, from, next);
        },
        children: [{
            path: 'cardList',
            name: 'generalCardList',
            component: layout.generalCardList,
            children: generalCard,
        }, {
            path: 'spacesetting',
            name: 'generalSpaceSetting',
            component: layout.spaceSetting,
            beforeEnter: beforeEnterWrapper.validateSpaceAuth,
            props: route => ({ spaceKey: route.params.spaceKey }),
            meta: {
                permissionRequired: true,
            },
        }, {
            path: 'calendar',
            name: 'generalCalender',
            component: layout.calendar,
            beforeEnter: beforeEnterWrapper.validateSpaceAuth,
            meta: {
                permissionRequired: true,
            },
        }],
    }, {

        /* 匹配
        http://local.jd.com:8084/teamspace/addon/jacp2021/joyspace/addon
        http://local.jd.com:8084/teamspace/addon/jacp2021/joyspace
        */
        path: 'addon/:spaceKey/:appCode/(.*)?',
        name: 'teamspaceAddon',
        component: modulePointRender,
        beforeEnter: beforeEnterWrapper.validateSpaceAuth,
        meta: {
            permissionRequired: true,
        },
    },
    ],
}];

export default routers;
