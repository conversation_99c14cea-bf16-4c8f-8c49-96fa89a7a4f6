
import { space as SpaceModel, card as CardModel } from '@/models/teamspace';
import { getSpaceId } from '@/plugins/utils';

export function validateSpaceAuth(spaceKey) {
    return getSpaceId(spaceKey) ? Promise.resolve() : SpaceModel.getDetail(undefined, spaceKey);
}
export function validateCardAuth(cardId) {
    /* eslint-disable no-confusing-arrow */
    // TODO: 预期情况是，在没有权限的情况下应该返回没有权限，纯游客，到ghost页面去
    return new Promise((resolve, reject) => CardModel.validateCardPrivilage(cardId).then(
        hasAuth => hasAuth ? resolve() : reject(),
    ));
}
export const beforeEnterWrapper = {
    validateSpaceAuth(to, from, next) {
        // console.log('---');
        validateSpaceAuth(to.params.spaceKey).then(() => {
            next();
        }).catch(
            () => next({ name: 'teamspaceSpaceList' }),
        );
    },
    validateCardAuth(to, from, next) {
        validateCardAuth(to.query.cardId).then(next).catch(
            () => next({ name: 'teamspaceSpaceList' }),
        );
    },
};
