<template>
    <div class="teamspace-newspace-root">
        <el-form
            ref="formIns"
            :model="data"
            :rules="rules"
            label-position="top"
        >
            <template
                v-if="!baseInfo.id"
            >
                <el-form-item
                    prop="mode"
                    label-width="0"
                    label="空间类型"
                >
                    <div class="teamspace-newspace-root__modes">
                        <template v-if="modeList.find(mode => mode.modeCode === 2)">
                            <div
                                class="radio-item"
                                :class="{'check': data.mode === 2}"
                                @click="data.mode = 2"
                            >
                                <i class="j-jagile" />
                                <div>
                                    <p class="radio-item-title">
                                        {{ $t('jacp.teamspace.spaceType.scrum') }}
                                    </p>
                                    <p>一种产品与研发紧密协作，快速迭代交付产品价值的研发模式，一般适用于频繁交付产品或应对快速变化的需求的中小型研发团队。</p>
                                </div>
                            </div>
                        </template>
                        <template v-if="modeList.find(mode => mode.modeCode === 1)">
                            <div
                                class="radio-item"
                                :class="{'check': data.mode === 1}"
                                @click="data.mode = 1"
                            >
                                <i class="j-general" />
                                <div>
                                    <p class="radio-item-title">
                                        {{ $t('jacp.teamspace.spaceType.general') }}
                                    </p>
                                    <p>一种按照TODO、DOING、DONE的三步工作模式，支持简单的流程流转和控制，面向更多需要流程控制的事务型团队。</p>
                                </div>
                            </div>
                        </template>
                    </div>
                </el-form-item>
            </template>
            <el-form-item
                :label="fieldText.name"
                prop="name"
            >
                <el-input
                    v-model="data.name"
                    placeholder="可输入5-20个字"
                    :maxlength="20"
                    :minlength="5"
                />
            </el-form-item>
            <el-form-item
                :label="fieldText.key"
                prop="key"
            >
                <el-input
                    :disabled="mode==='edit'"
                    v-model="data.key"
                    placeholder="限英文字符、下划线和数字, 4-20字符"
                    :maxlength="20"
                    :minlength="4"
                />
            </el-form-item>
            <el-form-item
                :label="fieldText.description"
                prop="desc"
            >
                <el-input
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 3 }"
                    v-model="data.desc"
                    placeholder=""
                />
            </el-form-item>
        </el-form>
    </div>
</template>

<script type="text/javascript">
import { space as SpaceModel } from '@/models/teamspace';

// 编辑功能OK，但未测试，暂不需要此功能
export default {
    props: {
        spaceId: [Number, String],
        baseInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            mode: this.spaceId ? 'edit' : 'create',
            rules: {
                name: [{
                    required: true,
                    message: '请输入空间名称',
                    trigger: 'change',
                }, {
                    min: 5,
                    max: 20,
                    message: '限5 - 20个字',
                    trigger: 'change',
                }],
                key: [{
                    required: true,
                    message: '请输入空间标识',
                    trigger: 'change',
                }, {
                    pattern: /^[\w\d]{4,20}$/,
                    message: '限4 - 20个字符，且为字母数字下划线',
                    trigger: 'change',
                }, {
                    message: '该空间标识已存在',
                    validator: this.validateKey,
                    trigger: 'change',
                }],
                desc: [{
                    max: 150,
                    message: '限150个字',
                    trigger: 'change',
                }],
                mode: [{
                    required: true,
                    message: '请选择协作方式',
                    trigger: 'change',
                }],
            },
            data: {
                name: '',
                key: '',
                desc: '',
                mode: '',
            },
            filteredKey: '',
            modeList: [],
        };
    },
    created() {
        this.getSpaceModes();
        // 复制空间设置默认模式
        this.data.mode = this.baseInfo.mode || '';
    },
    mounted() {
        if (this.mode === 'edit') {
            SpaceModel.getDetail(this.spaceId).then((data) => {
                this.data = data;
                this.filteredKey = data.key;
            });
        }
    },
    methods: {
        validateData() {
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject(new Error('校验失败'));
                    }
                });
            });
        },
        getSpaceModes() {
            SpaceModel.getSpaceModes().then((data) => {
                this.modeList = data || [];
                if (this.modeList.length === 1) {
                    this.data.mode = this.modeList[0].modeCode;
                }
            });
        },
        validateKey(rule, value, callback) {
            if (value === this.filteredKey) {
                callback();
                return;
            }
            SpaceModel.validateKey(value)
                .then(callback)
                .catch(() => {
                    callback(new Error(rule.message));
                });
        },
    },
    computed: {
        fieldText() {
            return {
                name: this.$t('jacp.teamspace.spaceFields.name'),
                key: this.$t('jacp.teamspace.spaceFields.key'),
                description: this.$t('jacp.teamspace.spaceFields.description'),
            };
        },
    },
};
</script>

<style lang="less">
.teamspace-newspace-root{
    margin: 0;
    margin-top: 6px;
    height: 410px;
    &__modes{
        display: flex;
        justify-content: space-between;
        .radio-item{
            max-width: 352px;
            flex: 1;
            position: relative;
            height: 124px;
            border: 1px solid #e5e5e5;
            display: flex;
            font-weight: 700;
            padding: 16px;
            border-radius: 4px;
            &:nth-of-type(2n) {
                margin-left: 16px;
            }
            cursor: pointer;
            &-title{
                color: #333333;
                margin-bottom: 2px;
            }
            &:hover{
                i, div{
                    color: #66b1ff;
                }
                border-color: #66b1ff;
                background-color: #E9F4FD;
            }
            &.check{
                i, div{
                    color: #66b1ff;
                }
                border-color: #66b1ff;
                background-color: #E9F4FD;
            }
            i{
                width: 32px;
                height: 32px;
                flex: 0 0 auto;
                border-radius: 16px;
                margin-right: 16px;
                display: inline-block;
            }
        }
        p{
            font-size: 12px;
            text-align: left;
            line-height: 1.5;
            padding: 0px;
            margin: 2px 0 12px;
            color: #999999;
            font-weight: 200;
        }
        label{
            font-weight: 500;
            font-size: 14px;
        }
    }
    .el-form-item__label{
        font-size: 14px;
        line-height: 16px;
        font-weight: 500;
        color: #333333;
    }
    .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
        content: '';
    }
    .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:after,
    .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:after {
        content: '*';
        color: #F56C6C;
        margin-left: 4px;
    }
}
</style>
