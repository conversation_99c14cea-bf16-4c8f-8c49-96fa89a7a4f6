export const recentsKey = 'jacp-space_recents';
export const recentsMaxLength = 5;
export const getRecents = (key = recentsKey) => {
    let result = localStorage.getItem(key);
    // TODO: jscrypto
    try {
        result = result ? JSON.parse(result) : [];
    } catch (e) {
        result = [];
    }
    return result;
};
// TODO: remove. replaced by src/utils/localStorage.js
export const addToRecents = (newItem, targetArray = getRecents(recentsKey)) => {
    let index = -1;
    // 存在么？
    targetArray.some((o, i) => {
        if (o === newItem) {
            index = i;
            return false;
        }
        return true;
    });
    if (index !== -1) {
        targetArray.splice(index, 1);// 存在？删掉
    }
    if (targetArray.length >= recentsMaxLength) {
        // eslint-disable-next-line no-param-reassign
        targetArray.length -= 1;
    }
    targetArray.splice(0, 0, newItem);// 强势插入
    localStorage.setItem(recentsKey, JSON.stringify(targetArray));
    return targetArray;
};
