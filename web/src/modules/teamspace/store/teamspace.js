/* eslint-disable prefer-destructuring */
import isPlainObject from 'lodash/isPlainObject';
import sortBy from 'lodash/sortBy';
import omit from 'lodash/omit';
import {
    card, space, pmp, plan,
} from '@/models/teamspace';
import {
    getSpaceId,
} from '@/plugins/utils';
import { getRecents, addToRecents } from './recentSpaceStorage';

export default {
    namespaced: true,
    state: {
        currentSpaceKey: -1,
        spaceList: [],
        recentSpaceList: getRecents(),
        keyMap: {},
        keyMapPlus: {},
        mineCard: false, // 仅我的卡片
        // 看板过滤条件
        boardFilter: {
            keyWord: '',
            types: [],
            priority: [],
            tags: [],
            users: [],
            demandSource: [],
        },
        availableStatusList: [],
        // 各种select的config
        priorityList: [],
        demandSourceConfig: [],
        typeList: [],
        typeListOptions: {
            fields: [],
        },
        appList: [],
        tagsList: [],
        projectList: [],
        allMemberList: [],
        planList: [],
        customizedConfigRoleList: [],
        // memberList: [],
        // guestMemberList: [],
        requiredReview: 0,
        requiredChange: 0,
        requiredReviewReviewers: [],
        spacePrivilage: {}, // 这个privilege拼错了
        basicInfoLoaded: false,
        worktime: false,
    },
    mutations: {
        clearKeymap(state) {
            Object.assign(state, {
                keyMap: {},
            });
        },
        updateKeymap(state, data = {}) {
            state.keyMap = Object.assign(omit(state.keyMap, Object.keys(data)), data);
        },
        updateState(state, payload = {}) {
            Object.assign(state, payload);
        },
        setMineCard(state, data) {
            state.mineCard = data;
        },
        setBoardFilter(state, data) {
            Object.assign(state, {
                boardFilter: data,
            });
        },
        setStatusList(state, data) {
            Object.assign(state, {
                availableStatusList: data,
            });
        },
        setWorktime(state, data) {
            Object.assign(state, {
                worktime: data,
            });
        },
        setRequiredReview(state, {
            requiredReview, requiredRdReview, requiredChange, requiredReviewReviewers,
        }) {
            Object.assign(state, {
                requiredReview: requiredReview || requiredRdReview,
                requiredChange,
                requiredReviewReviewers: requiredReviewReviewers.slice(),
            });
        },
        extendKeymapPlus({ keyMapPlus }, list = []) {
            const keyMap = {};
            list.forEach((o) => {
                keyMap[o.key] = o;
            });
            Object.assign(keyMapPlus, keyMap);
        },
        // 空间加入最近访问
        addRecentSpaceList(state, spaceId) {
            state.recentSpaceList = addToRecents(spaceId, state.recentSpaceList);
        },
    },
    actions: {
        createKeymap({ commit }, spaceList = []) {
            const keyMap = spaceList.reduce((result, o) => {
                result[o.key] = o;
                return result;
            }, {});
            commit('updateState', { spaceList });
            commit('updateKeymap', keyMap);
            return Promise.resolve(spaceList);
        },
        setStatusList({ commit }, data) {
            commit('setStatusList', data);
            return Promise.resolve(data);
        },
        // TODO: 这个应该怎么优化呢?
        /* 详情或者新建的时候，都要求要获取最新的数据，保持数据的最新状态，
        但是如果数据没有更新的时候，却要频繁请求同样的数据，又没有什么必要。
        除非和服务器保持联通，只有在有数据发生变化的时候准确的接收推送后再拉取数据 */
        fetchAllInfoBySpaceId({ dispatch, commit }, {
            keys = [
                'priorityList',
                'demandSourceConfig',
                'typeListOptions',
                'appList',
                'projectList',
                'planList',
                'tagsList',
                'customizedConfigRoleList',
                'availableStatusList',
            ],
            spaceKey,
            spaceId = getSpaceId(spaceKey),
        } = {}) {
            return Promise.all(keys.map((key) => {
                const actionsName = `fetch${key.replace(/(\s|^)[a-z]/g,
                    c => c.toUpperCase())}`;

                return dispatch(actionsName, spaceId);
            })).finally(() => {
                commit('updateState', {
                    basicInfoLoaded: true,
                });
            });
        },
        fetchAllInfoBySpaceIdForGeneral({ dispatch }, {
            keys = ['priorityList', 'demandSourceConfig', 'typeListOptions', 'appList', 'projectList', 'tagsList', 'customizedConfigRoleList', 'availableStatusList'],
            spaceKey,
            spaceId = getSpaceId(spaceKey),
        } = {}) {
            return Promise.all(keys.map((key) => {
                const actionsName = `fetch${key.replace(/(\s|^)[a-z]/g,
                    c => c.toUpperCase())}`;
                return dispatch(actionsName, spaceId);
            }));
        },
        async fetchPriorityList({ commit, state }) {
            // if (state.priorityList.length) return Promise.resolve(state.priorityList);
            commit('updateState', {
                priorityList: await card.getPriorityConfig(),
            });
            return state.priorityList;
        },
        async fetchDemandSourceConfig({ commit }) {
            // if (state.demandSourceConfig.length) return Promise.resolve(state.demandSourceConfig);
            commit('updateState', {
                demandSourceConfig: await card.getDemandSourceConfig(),
            });
        },
        // TODO: 这个卡片类型可以自定义，结构也不是array，待完善
        async fetchTypeListOptions({ state, commit }, spaceId) {
            // if (getters.typeList.length) return Promise.resolve(state.typeListOptions);
            const typeListOptions = await space.getSpaceCardType(spaceId);
            commit('updateState', {
                typeListOptions,
                // typeList: typeList.fields[0].options || [],
            });
            return state.typeListOptions;
        },
        async updateTypeListOptions({ commit }, data) {
            const result = await space.saveSpaceCardType(data);
            if (result) {
                commit('updateState', {
                    typeListOptions: data,
                });
            }
            return result;
        },
        async fetchAppList({ commit, state }, spaceId) {
            commit('updateState', {
                appList: await card.getAppList(spaceId),
            });
            return state.appList;
        },
        // PmpModel.getProjectList()
        async fetchProjectList({ commit, state }) {
            // if (state.projectList.length) return Promise.resolve(state.projectList);
            commit('updateState', {
                projectList: await pmp.getProjectList(),
            });
            return state.projectList;
        },
        async fetchTagsList({ commit, state }, spaceId) {
            commit('updateState', {
                tagsList: await space.getSpaceNewTagsBySpaceId(spaceId),
            });
            return state.tagsList;
        },
        async fetchRequiredReview({ commit }, spaceKey) {
            commit('setRequiredReview', await space.getFlowOption(getSpaceId(spaceKey)));
        },
        async fetchWorktime({ commit }, spaceKey) {
            space.getSpaceVendorConfig(spaceKey, true).then((data) => {
                commit('setWorktime', false);
                if (data.length > 0) {
                    data.forEach((cf) => {
                        const intVendorId = parseInt(cf.vendorId, 10);
                        if ([5].includes(intVendorId)) {
                            commit('setWorktime', true);
                        }
                    });
                }
            });
        },
        async fetchMemberList({ commit }, spaceId) {
            commit('updateState', {
                allMemberList: await space.getMembers(spaceId),
            });
        },
        async fetchPlanList({ commit }, spaceId) {
            let list = await plan.getCurrents(spaceId);
            list = list.concat([{
                id: -1,
                name: 'Backlog',
            }]);
            commit('updateState', {
                planList: list,
            });
            return list;
        },
        async fetchCustomizedConfigRoleList({ commit }, spaceId) {
            commit('updateState', {
                customizedConfigRoleList: await card.getCustomizedConfigRoleList(spaceId),
            });
        },
        async fetchAvailableStatusList({ commit, state }, params) {
            let spaceId = params;
            let ignoreCache;
            if (isPlainObject(params)) {
                spaceId = params.spaceId;
                ignoreCache = params.ignoreCache;
            }
            const availableStatusList = await space.getAvailableStatusList({ spaceId }, ignoreCache);
            commit('updateState', { availableStatusList });
            return state.availableStatusList;
        },
        updateSpacePrivilage({ commit }, spaceKey) {
            // 主框架的请求
            console.log('--主--');
            space.getCurrentUserPrivilage(getSpaceId(spaceKey))
                .then((spacePrivilage) => {
                    commit('updateState', { spacePrivilage });
                });
        },
    },
    getters: {
        priorityListMap({ priorityList }) {
            const priorityListMap = {};
            priorityList.forEach(({ code, name }) => {
                priorityListMap[code] = name;
            });
            return priorityListMap;
        },
        typeList({ typeListOptions }) {
            if (!typeListOptions.fields || !typeListOptions.fields.length) {
                return [];
            }
            const { options } = typeListOptions.fields[0];
            return options || [];
        },
        // 卡片的类型需要过滤掉‘业务需求’，并且，可以设置显示与不显示（show）.
        // item里的disabled是代表的是否可以编辑。所以在调用的时候把disabled
        cardTypeList(state, { typeList }) {
            function removeDisabled(sum, item) {
                if (item.code !== 1 && item.show) {
                    sum.push(Object.assign({}, item, {
                        disabled: undefined,
                    }));
                }
                return sum;
            }
            return typeList.reduce(removeDisabled, []);
        },
        memberList({ allMemberList }) {
            return allMemberList.filter(user => user.role !== 'guest');
        },
        guestMemberList({ allMemberList }) {
            return allMemberList.filter(user => user.role === 'guest');
        },
        // 是否开启评审流程
        enableForceReview({ requiredReview }) {
            return +requiredReview === 1;
        },
        currentSpace({ keyMap, currentSpaceKey }) {
            return keyMap[currentSpaceKey] || {};
        },
        currentSpaceId(state, getters) {
            return getters.currentSpace.id;
        },
        // 没有backlog的纯迭代列表
        activeSprintList(state) {
            return state.planList.filter(s => s.id !== -1 && s.archived === 0);
        },
        // 我的活跃空间列表
        allList({ spaceList }) {
            return spaceList.filter(item => !item.archived);
        },
        // 我的归档空间列表
        archivedList({ spaceList }) {
            return spaceList.filter(item => item.archived);
        },
        // 我的收藏空间列表
        favoList({ spaceList }) {
            return spaceList.filter(item => item.attentionType === 1);
        },
        // 我的收藏空间列表
        recentList({ spaceList, recentSpaceList }) {
            return sortBy(
                spaceList.filter(item => recentSpaceList.includes(item.key)),
                item => recentSpaceList.indexOf(item.key),
            );
        },
        // 分组的空间列表
        groupedSpaceList(state, getters) {
            return [
                { id: 1, name: '最近访问', children: getters.recentList },
                { id: 2, name: '我的收藏', children: getters.favoList },
                { id: 3, name: '我的空间', children: getters.allList },
            ];
        },
    },
};
