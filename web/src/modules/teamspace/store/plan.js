export default {
    namespaced: true,
    state: {
        // TODO: 把sprint记录activePlan到localstorage的部分挪到这里来实现，把teamspace里为维护迭代列表的部分也挪到这里
        activePlan: {},
        isGlobalMode: false,
    },
    mutations: {
        plan_update(state, data) {
            Object.assign(state, data);
        },
        isGlobalMode_change(state, v) {
            // eslint-disable-next-line
            state.isGlobalMode = v;
        },
    },
    actions: {
        plan_update({ commit }, data) {
            commit('plan_update', data);
        },
    },
};
