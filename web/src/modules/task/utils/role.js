import { card as CardModel } from '@/models/teamspace';
import { getSpaceIdFromRouter } from '@/plugins/utils';

const roleMapCache = {};

export const getDefaultRole = async (erp) => {
    if (!erp) {
        return null;
    }
    let roleJson = roleMapCache[erp];
    const spaceId = getSpaceIdFromRouter();
    if (roleJson) {
        return roleJson[spaceId];
    }
    [roleJson] = await CardModel.getRoleCodeDefault(erp, spaceId);
    roleMapCache[erp] = roleJson || {};
    return roleMapCache[erp][spaceId];
};

export const updateRole = async (erp, roleCode) => {
    const spaceId = getSpaceIdFromRouter();
    const roleJson = roleMapCache[erp];
    const params = {
        [spaceId]: roleCode || '',
    };
    if (roleJson && roleJson.id) {
        if (roleJson[spaceId] === roleCode) {
            return;
        }
        await CardModel.updateConfigRoleDefault(roleJson.id, erp, params);
        Object.assign(roleMapCache[erp], {
            [spaceId]: roleCode,
        });
    } else {
        const roleId = await CardModel.saveConfigRoleDefault(erp, params);
        roleMapCache[erp] = {
            [spaceId]: roleCode,
            id: roleId,
        };
    }
};
// TODO: 没啥用可以删了？这个值不能随便清除
export const cleanCache = () => {
    // console.log(roleMapCache);
    // roleMapCache = Object.create(null);
};
