import moment from 'moment';
import store from '$platform.store';
import { userFilter } from '@/plugins/utils';
import { plannedHourRule } from '@/modules/card/models/cardValidateRules';
import {
    handleDateChange, startDateOpt, deadlineOpt, handleProcessorChange,
} from '$module/models/taskColumns';

export const Deadline = ({ props }) => (<jacp-text
    type={props.task.deadline ? 'default' : 'disable'}
    size="12"
    class="task-board-item-value">{
        props.task.deadline
            ? moment(props.task.deadline).format('YYYY-MM-DD')
            : '无数据'}</jacp-text>);
export const Processor = ({ props }) => ((props.task.processor && props.task.processor.erp)
    ? <jacp-erp
        avatar
        vertical
        data={props.task.processor}
        display-name={true}
        avatarSize={24}
        disable-timline={store.state.user.erp === props.task.processor.erp}
    ></jacp-erp>
    : <i class="jacp-icon-Avatar" style="color: #909399; font-size: 24px;"></i>);
export const StartDate = ({ props }) => (<jacp-text
    type={props.task.startDate ? 'default' : 'disable'}
    size="12"
    class="task-board-item-value">{
        props.task.startDate
            ? moment(props.task.startDate).format('YYYY-MM-DD')
            : '无数据'}</jacp-text>);

export function getFieldRenderMapEditable(vm, task) {
    return {
        processor: {
            render() {
                return (<div on-click={e => e.stopPropagation()}>
                    <single-user
                        v-model={task.processor}
                        placeholder="请选择"
                        size={24}
                        max-count="1"
                        no-timline-users={[vm.$store.state.user]}
                        vertical={true}
                        show-suggestions={false}
                        load-data={userFilter(vm.$store.state.chilldteamspace.allMemberList)}
                        storage-key="card_processor"
                        disabled={true}
                        scopedSlots={{
                            reference: () => <i
                                class="el-icon-plus" style="fontSize: 16px;width: 24px; height: 24px; line-height: 23px;"
                            />,
                        }}
                        onOn-add={() => handleProcessorChange(task)}
                        onOn-delete={() => handleProcessorChange(task)}
                    >
                    </single-user>
                    <jacp-text type="error" size="12">
                        { !task.erp ? '不能为空' : null }</jacp-text>
                    { !task.erp && vm.disabled ? <i
                        class="jacp-icon-Avatar"
                        style="font-size: 24px;"
                    /> : null }
                </div>);
            },
        },
        startDate: {
            render() {
                return (<jacp-date
                    placeholder="请选择"
                    style="width: 110px"
                    v-model={task.startDate}
                    disabled={true}
                    rules={plannedHourRule.startDate}
                    on-change={$event => handleDateChange('startDate', $event, task)}
                    clearable={false}
                    picker-options={startDateOpt(task)}
                ></jacp-date>);
            },
        },
        deadline: {
            render() {
                return (<jacp-date
                    placeholder="请选择"
                    style="width: 110px"
                    v-model={task.deadline}
                    disabled={true}
                    rules={plannedHourRule.deadline}
                    on-change={$event => handleDateChange('deadline', $event, task)}
                    clearable={false}
                    picker-options={deadlineOpt(task)}
                ></jacp-date>);
            },
        },
    };
}
