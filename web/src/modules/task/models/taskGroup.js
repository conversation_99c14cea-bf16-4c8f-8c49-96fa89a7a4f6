import { spaceHttp } from '@/plugins/http';
import ListGroup from '@/models/listGroup';
import { taskListGroups, taskStatus } from '@/modules/task/constant';

export const getGroupFilterConditions = (groupKey, group = {}) => {
    let conditions = {};
    switch (groupKey) {
    case taskListGroups.status:
        conditions = { status: group.id };
        break;
    case taskListGroups.processor:
        conditions = { processorErp: group.erp };
        break;
    case taskListGroups.rootCard:
        conditions = { rootCardId: group.id };
        break;
    default: break;
    }
    return conditions;
};
// FIXME: 怎么对齐不同group返回的不同结构？
export const getGroupInfo = (groupKey, group) => {
    let groupInfo = {};
    switch (groupKey) {
    case taskListGroups.status:
        groupInfo = { id: group.id, name: taskStatus[group.id].name };
        break;
    case taskListGroups.processor:
        groupInfo = { id: group.erp, name: `${group.name}${group.erp === '-1' ? '' : `（${group.erp}）`}` };
        break;
    case taskListGroups.rootCard:
    default:
        groupInfo = group;
        break;
    }
    return groupInfo;
};

/* 从任务所有数据里筛选对应到分组的方法 */
export const getGroupFilterFn = (groupKey, group = {}) => {
    let filterFn;
    switch (groupKey) {
    case taskListGroups.processor:
        if (group.id === '-1') {
            filterFn = task => !task.processor;
        } else {
            filterFn = task => task.processor && task.processor.erp === group.id;
        }
        break;
    case taskListGroups.rootCard:
        filterFn = task => +task.cardId === +group.id;
        break;
    default: break;
    }
    return filterFn;
};
export default class TaskGroup extends ListGroup {
    constructor({ data = [], ...props }) {
        super(props);
        this.data = data;
    }

    static getComponent(group = {}) {
        if (group.groupKey === taskListGroups.processor) {
            return ListGroup.TEMPLATE.Erp;
        }
        return null;
    }

    // params是高级查询的条件
    static getGroups(groupName, params = {}) {
        if (!groupName) return Promise.reject(new Error('分组类型不能为空'));
        return spaceHttp.post(`v1/bizSpaceCard/personHours/group/${groupName}`, params);
    }
}
