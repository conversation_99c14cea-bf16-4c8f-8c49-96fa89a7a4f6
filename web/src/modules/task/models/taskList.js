import Pager from '@/plugins/pager';
import Task from './task';


export default class TaskList {
    constructor({
        data = [], pager,
        pageSize = 10,
    } = {}) {
        this.data = data;
        this.pager = pager || new Pager({ pageSize });
    }

    load(params = {}) {
        if (!params.sprintId) {
            return Promise.reject();
        }
        return Task.getList(params, this.pager)
            .then((data) => {
                this.data = data;
                return data;
            });
    }
}

export class TaskBoardList extends TaskList {
    load(params) {
        if (!params.sprintId) {
            return Promise.reject();
        }
        return Task.getListNoPaging(params)
            .then((data) => {
                this.data = data;
                return data;
            });
    }
}
