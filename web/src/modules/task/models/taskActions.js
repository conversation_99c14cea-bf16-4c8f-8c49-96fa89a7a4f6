
import Vue from 'vue';
import TaskAdd from '$module/components/taskAdd';
import i18n from '$platform.i18n';
import store from '$platform.store';
import router from '$platform.router';

const TaskAddVM = Vue.extend(TaskAdd);
let taskAddIns = null;
const createTaskAddIns = (options) => {
    if (taskAddIns) {
        taskAddIns.close();
    }
    taskAddIns = new TaskAddVM({
        i18n,
        store,
        router,
        ...options,
    });
    taskAddIns.$mount();
    document.body.appendChild(taskAddIns.$el);
};
const handleDialogClose = () => {
    if (!taskAddIns) {
        return;
    }
    document.body.removeChild(taskAddIns.$el);
    taskAddIns.$destroy();
    taskAddIns = null;
};
export default {
    create({
        onTaskAdded = () => {},
    } = {}) {
        createTaskAddIns({
            propsData: {
                onAdded: onTaskAdded,
                onClose: handleDialogClose,
            },
        });
    },
    edit({
        task = {},
        onTaskUpdated = () => {},
        onTaskRemoved = () => {},
    }) {
        createTaskAddIns({
            propsData: {
                task,
                title: '查看任务',
                onUpdate: onTaskUpdated,
                onClose: handleDialogClose,
                onRemove: onTaskRemoved,
            },
        });
    },
};
