import Vue from 'vue';
import router from '$platform.router';
import store from '$platform.store';
// FIXME: 任务的字段校验rules
import { plannedHourRule } from '@/modules/card/models/cardValidateRules';
import { getDefaultRole, updateRole } from '@/modules/task/utils/role';
import CardTaskStatus from '@/modules/_components/CardTaskStatus';
import { event } from '@/plugins/event';
import { userFilter } from '@/plugins/utils';

// 修改计划时间的时候需要有一个不阻塞提交但是要给warning的提示
const plannedHoursWarningState = Vue.observable({ msg: '超过单日24小时计划工时建议修改', taskId: '' });
const stopPropagation = e => e.stopPropagation();

export const handleProcessorChange = async (task = {}) => {
    const { processor = {} } = task;
    const { erp, name, headImage } = processor || {};
    Object.assign(task, { erp, name, headImage });
    if (!task.processor) {
        return;
    }
    // 没有默认的角色职责的时候就填一个默认值
    const code = await Promise.resolve(getDefaultRole(processor.erp));
    task.roleCode = code;
    task.save('processor', task.processor).then(() => {
        updateRole(processor.erp, code);
        event.$emit('refresh-list');
    });
};
// 列表里选择的处理人应该为当前空间的member

export const handleDateChange = (prop, value, task = {}) => {
    const { startDate, deadline } = task;
    task.save({
        [prop]: value,
        // duration: [startDate, deadline],
        startDate,
        deadline,
    });
};

export const startDateOpt = row => ({
    disabledDate: time => (row.deadline ? time.getTime() > row.deadline : false),
});

export const deadlineOpt = row => ({
    disabledDate: time => (row.startDate ? time.getTime() < row.startDate : false),
});

// 这里用到了多语言包，需要在merge成功以后才能使
export const columns = [
    {
        prop: 'content',
        className: 'cell-fill',
        minWidth: 150,
        cellRender(h, { row, column }) {
            return <jacp-input
                disabled={column.disabled}
                rules={plannedHourRule.content}
                value={row.content}
                on-change={row.save.bind(row, 'content')}
                on-input={(value) => {
                    row.content = value;
                }}></jacp-input>;
        },
    },
    {
        prop: 'erp',
        width: 120,
        cellRender(h, { row }) {
            return (<div>
                <single-user
                    v-model={row.processor}
                    placeholder="请选择"
                    size={24}
                    max-count="1"
                    no-timline-users={[store.state.user]}
                    vertical={true}
                    show-suggestions={false}
                    load-data={userFilter(store.state.chilldteamspace.allMemberList)}
                    storage-key="card_processor"
                    scopedSlots={{
                        reference: () => <i class="el-icon-plus" style="fontSize: 16px;width: 24px; height: 24px; line-height: 23px;" />,
                    }}
                    onOn-add={() => handleProcessorChange(row)}
                    onOn-delete={() => handleProcessorChange(row)}
                >
                </single-user>
                <jacp-text type="error" size="12">
                    { !row.erp ? '不能为空' : null }</jacp-text>
            </div>);
        },
    },
    {
        prop: 'plannedHours',
        width: 94,
        cellRender(h, { row, column }) {
            // FIXME: 此处性能问题需要优化
            return <div>
                <jacp-input
                    style="width: 70px"
                    type="number"
                    rules={plannedHourRule.plannedHours.concat([{
                        trigger: ['change', 'blur'],
                        validator(rule, plannedHours, callback = () => {}) {
                            const { startDate, deadline } = row;
                            if (!startDateOpt || !deadline) {
                                plannedHoursWarningState.taskId = null;
                                return callback();
                            }
                            let errorMsg = '';
                            const days = ((deadline - startDate) / (1000 * 60 * 60 * 24)) + 1;

                            if (+plannedHours > days * 24) {
                                errorMsg = '超过单日24小时计划工时建议修改';
                                plannedHoursWarningState.taskId = null;
                                return callback(new Error(errorMsg));
                            }
                            return callback();
                        },
                    }])}
                    disabled={column.disabled}
                    v-model={row.plannedHours}
                    on-change={$event => row.save('plannedHours', +$event).then(() => {
                        plannedHoursWarningState.taskId = null;
                    })}
                ></jacp-input>
                <jacp-text type="warning" size="12" style="display: block;width: 70px">
                    { plannedHoursWarningState.taskId === row.id
                        ? plannedHoursWarningState.msg : null }
                </jacp-text>

            </div>;
        },
    }, {
        prop: 'remainingHours',
        width: 94,
        cellRender(h, { row, column }) {
            return <div>
                <jacp-input
                    style="width: 70px"
                    type="number"
                    rules={plannedHourRule.remainingHours}
                    disabled={column.disabled}
                    v-model={row.remainingHours}
                    on-change={row.save.bind(row, 'remainingHours')}
                ></jacp-input>
            </div>;
        },
    },
    {
        prop: 'startDate',
        width: 156,
        // TODO: 等接口改好了以后调整成custom
        // sortable: 'custom',
        sortable: true,
        cellRender(h, { row, column }) {
            return <div>
                <jacp-date
                    style="width: 110px"
                    placeholder="请选择"
                    v-model={row.startDate}
                    disabled={column.disabled}
                    rules={plannedHourRule.startDate}
                    on-change={$event => handleDateChange(column.prop, $event, row)}
                    clearable={!column.disabled}
                    picker-options={startDateOpt(row)}
                ></jacp-date>
            </div>;
        },
    }, {
        prop: 'deadline',
        width: 156,
        // sortable: 'custom',
        sortable: true,
        cellRender(h, { row, column }) {
            return <div>
                <jacp-date
                    style="width: 110px"
                    placeholder="请选择"
                    v-model={row.deadline}
                    disabled={column.disabled}
                    rules={plannedHourRule.deadline}
                    on-change={$event => handleDateChange(column.prop, $event, row)}
                    clearable={!column.disabled}
                    picker-options={deadlineOpt(row)}
                ></jacp-date>
            </div>;
        },
    }, {
        prop: 'cardId',
        className: 'cell-fill',
        minWidth: 150,
        cellRender(h, { row }) {
            const { cardId } = row;
            return <router-link target="_blank" nativeOnClick={stopPropagation} to={{
                name: 'teamspaceCardDetail',
                params: {
                    ...router.currentRoute.params,
                },
                query: {
                    cardId,
                    sprintId: (store.state.plan.activePlan || {}).id,
                },
            }}>
                <el-tooltip>
                    <div slot="content" style="max-width: 280px">{row.cardName}</div>
                    <span style="display: block;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">{row.cardName}</span>
                </el-tooltip>
            </router-link>;
        },
        // slot直接写，scopeSlots放props写
    },
    // TODO: 状态的需要单独写一个下拉，颜色和状态需要映射一下
    {
        prop: 'status',
        width: 130,
        cellRender(h, { row }) {
            return h(CardTaskStatus, {
                props: {
                    status: row.status,
                },
                on: {
                    command: (val) => {
                        row.status = val;
                    },
                    'change-status': val => row.updateStatus(val).then(() => event.$emit('refresh-list')),
                },
            });
        },
    }, {
        prop: 'roleCode',
        width: 125,
        cellRender(h, { row }) {
            const options = [...store.state.chilldteamspace.customizedConfigRoleList];
            if (row.roleCode && !options.map(o => o.code).includes(row.roleCode)) {
                // TODO: 如果有已关闭的角色职责，先显示为暂无。后面进行优化
                options.push({
                    code: row.roleCode, name: '暂无', disabled: true, id: undefined,
                });
            }
            return <jacp-select
                style="width: 108px"
                placeholder="请选择"
                option-value-key="code"
                v-model={row.roleCode}
                data={options}
                on-change={() => {
                    row.save('roleCode', row.roleCode)
                        .then(() => updateRole(row.processor.erp, row.roleCode));
                }}
            ></jacp-select>;
        },
    },
];

const columnsMap = columns.reduce((map, column) => {
    map[column.prop] = column;
    return map;
}, {});
export default columnsMap;
