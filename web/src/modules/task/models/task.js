import pick from 'lodash/pick';
import isPlainObject from 'lodash/isPlainObject';
import { Message } from 'element-ui';
import { spaceHttp } from '@/plugins/http';
import { initProperties } from '@/plugins/utils';
import { date2Number, timestamp2Date } from '@/plugins/utils.transfer';
import { User } from '@/models/user';
import { taskStatus } from '@/modules/task/constant';

const DEFAULT = [
    ['id'],
    ['index'],
    ['content', ''],
    ['erp'],
    ['name'],
    ['headImage'],
    ['startDate'], ['deadline'],
    ['plannedHours'], ['remainingHours'],
    ['roleCode'],
    ['cardId'],
    ['cardName'],
    ['code'],
    ['status', taskStatus.undo.code], // 1: 未完成 2:完成
];
// eslint-disable-next-line no-use-before-define
const createTasks = (data = []) => data.map(task => new Task(task));
export default class Task {
    static #cache = new Map();

    static #beforeUpdateCache = new Map();

    #temp = '';

    #processor = {};

    constructor(values = {}) {
        // console.log(values.id, Task.#cache.has(values.id));
        const self = Task.#cache.has(values.id) ? Task.#cache.get(values.id) : this;
        // console.log(Task.#cache.get(values.id), self);
        initProperties(self, DEFAULT, values);
        // 起止日期
        self.duration = [
            timestamp2Date(self.startDate) || '',
            timestamp2Date(self.deadline) || ''];
        // TODO: 原来的结构先保留，逐步改成processor
        if (values.erp) {
            const { erp, name, headImage } = values;
            self.processor = new User({ erp, name, headImage });
        }
        Task.#beforeUpdateCache.set(values.id, { ...self });
        if (values.id) {
            Task.#cache.set(values.id, self);
        }
        return self;
    }

    get temp() {
        return this.#temp;
    }

    set temp(val) {
        this.#temp = val;
    }

    get processor() {
        return this.#processor;
    }

    set processor(val) {
        this.#processor = val;
        const { erp, name, headImage } = val || {};
        Object.assign(this, { erp, name, headImage });
    }


    updateStatus(status = this.status) {
        const { cardId, id } = this;
        if (!id) {
            return Promise.resolve(this);
        }
        return this.constructor.updateStatus({ cardId, status, id }).then(() => {
            Object.assign(this, { status });
            return this;
        });
    }

    // 全量更新
    save(params = {}) {
        // 取更新前数据，只替换需要更新的数据
        // 以后使用单个字段更新接口
        let targetParams;
        // 如果是array的话，就作为key列表进行更新
        if (Array.isArray(params)) {
            targetParams = pick(this, params);
        } else if (isPlainObject(params)) {
            targetParams = params;
        } else {
            // 只传一个字符串的话就作为key
            targetParams = { [params]: this[params] };
        }
        const task = {
            ...Task.#beforeUpdateCache.get(this.id),
            ...targetParams,
        };
        // console.log(task.deadline);
        return Task.update(task, task.cardId || this.cardId)
            .then(() => {
                Message.success({ message: '保存成功！' });
                Object.assign(this, task);
                // 更新缓存
                Task.#beforeUpdateCache.set(this.id, { ...this });
                return this;
            })
            .catch(() => {
                // 更新异常的时候回填原始值
                Object.assign(this, Task.#beforeUpdateCache.get(this.id));
            });
    }

    static toServerData(params = {}) {
        const data = Object.create(null);
        Object.keys(params).forEach((key) => {
            // FIXME: 如果startDate单独发生变化的时候duration却没有一起变化
            // if (key === 'duration') {
            //     [data.startDate, data.deadline] = params[key] || [];
            //     return;
            // }
            if (key === 'processor') {
                const { erp, name, headImage } = params.processor || {};
                Object.assign(data, { erp, name, headImage });
                return;
            }
            data[key] = params[key];
        });
        return Object.assign(data, {
            startDate: date2Number(data.startDate),
            deadline: date2Number(data.deadline),
        });
    }

    static status = {
        unfinished: taskStatus.undo.code,
        finished: taskStatus.done.code,
    };


    // @override
    static create(params = {}, cardId) {
        return spaceHttp.post('v1/bizSpaceCard/personHours', {
            ...Task.toServerData(params),
            cardId,
        });
    }

    // @override
    static update(params = {}, cardId) {
        return spaceHttp.put('v1/bizSpaceCard/personHours', {
            ...Task.toServerData(params),
            cardId,
        });
    }

    // @override
    static delete({ id }) {
        return spaceHttp.delete('v1/bizSpaceCard/personHours', {
            params: { id },
        });
    }

    static updateIndex(params = {}) {
        return spaceHttp.put('v1/bizSpaceCard/personHours/changeIndex', params);
    }

    static updateStatus({ id, status, cardId } = {}) {
        return spaceHttp.post('v1/bizSpaceCard/personHours/updateStatus', { id, status, cardId });
    }

    static getList(params = {}, pager = {}) {
        return spaceHttp.post('v1/bizSpaceCard/personHours/advanced/query', { ...params, ...pager })
            .then(({ total, records = [] } = {}) => {
                // eslint-disable-next-line no-param-reassign
                pager.total = total;
                return createTasks(records);
            });
    }

    static getListNoPaging(params = {}) {
        return spaceHttp.post('v1/bizSpaceCard/personHours/board/query', params)
            .then(createTasks);
    }

    static getForWorktime(taskId) {
        return spaceHttp.get(`v1/bizSpaceCard/personHours/${taskId}/worktime`);
    }

    static export(params) {
        return spaceHttp.post('v1/bizSpaceCard/personHours/export', params, {
            responseType: 'blob',
        }).then((res) => {
            const blob = new Blob([res.data]);
            const elink = document.createElement('a');
            const fileName = '团队空间-按任务.xlsx';
            elink.download = fileName;
            elink.style.display = 'none';
            elink.href = URL.createObjectURL(blob);
            document.body.appendChild(elink);
            elink.click();
            URL.revokeObjectURL(elink.href);
            document.body.removeChild(elink);
        });
    }
}
