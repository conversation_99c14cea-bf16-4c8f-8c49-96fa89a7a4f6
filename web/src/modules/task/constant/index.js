import omit from 'lodash/omit';
import { Enum } from '@/plugins/enum';

export const taskListGroups = {
    ungrouped: 'ungrouped',
    status: 'status',
    processor: 'processor',
    rootCard: 'rootCard',
};
// UNDO(1, "未处理"), DONE(2, "已完成"), DOING(3, "处理中"),
//    PAUSED(4, "暂停"), CANCELED(5, "取消");
// FIXME: 此处与项目管理的枚举是重复的
export const taskStatus = new Enum([
    {
        code: 1, value: 'undo', name: '未开始', color: '#2695F1', icon: 'jacp-icon-todo',
    },
    {
        code: 3, value: 'doing', name: '进行中', color: '#FF8D00', icon: 'jacp-icon-doing',
    },
    {
        code: 2, value: 'done', name: '已完成', color: '#0ED396', icon: 'jacp-icon-done',
    },
    {
        code: 4, value: 'paused', name: '暂停',
    },
    {
        code: 5, value: 'canceled', name: '取消',
    },
]);
// 过滤掉取消和暂停状态
export const availableStatus = taskStatus.list.reduce((result, status) => {
    if (status.code === taskStatus.undo.code || status.code === taskStatus.done.code || status.code === taskStatus.doing.code) {
        result.push({ ...status, id: status.code });
    }
    return result;
}, []);

// 与工时的关系 1:大于等于; 2:小于等于
export const relationType = new Enum([{
    code: 1, value: 'ge', name: '&ge;',
}, {
    code: 2, value: 'le', name: '&le;',
}]);

// 卡片看板分组方式
export const taskBoardGroups = omit(taskListGroups, 'status');

export const taskFilterCategories = [{
    value: 'basic',
    label: '基础条件',
    data: [
        'processorErp',
    ],
}, {
    value: 'date',
    label: '日期/时间',
    data: [
        'startDate',
        'endDate',
        'combinedPlannedHours',
        'combinedRemainingHours',
    ],
}, {
    value: 'other',
    label: '其他',
    data: [
        'status',
        'roleCode',
    ],
}];
