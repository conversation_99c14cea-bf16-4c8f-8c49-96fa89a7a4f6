<template>
    <div
        class="task-board"
    >
        <!-- 不能加.j-scrollbar--gray：overflow-y: overlay!important; 会使autoscroll失效 -->
        <kanban-board
            ref="boardIns"
            v-bind="$attrs"
            :stages="availableStatus"
            :blocks="data"
            :enable-masonry="true"
            :active-block-id="active.id"
            :disabled="disabled"
            :draggable-option="{
                filter: '.jacp-erp__timline',
                animation: data.length > 800 ? 0 : 200,
                ...draggableOption,
            }"
            :header-visible="headerVisible"
            @initialized="$emit('update:loading', false)"
            @start="onStart"
            @end="onEnd"
            @sort="sort"
            @change="updateStatus"
        >
            <template
                v-if="headerVisible"
                v-slot:column-header="{stage, count}"
            >
                <!-- 此处需要计算总数 -->
                <jacp-text
                    size="14"
                    :style="{color: stage.color}"
                >
                    {{ stage.name }}（{{ count }}）
                </jacp-text>
                <el-button
                    class="task-board__add-button"
                    type="text"
                    v-if="stage.value === 'undo' && !disabled"
                    @click="$emit('add', stage)"
                >
                    +新增
                </el-button>
            </template>
            <template v-slot:block="{block: task}">
                <div
                    class="task-board-item-root kanban-block__inner"
                    @click="() => setActive(task)"
                >
                    <ul>
                        <!-- code 和 content 是固定显示的字段 -->
                        <li class="task-board-field__code">
                            <i class="jacp-icon-task1 j-mgr4" />
                            <div
                                class="j-text-overflow task-board-item-label"
                            >
                                {{ task.code }}
                            </div>
                        </li>
                        <li class="task-board-field__content">
                            <el-tooltip
                                v-if="task.content.replace(/[\u4e00-\u9fa5]/ig, 'aa').length > 16"
                                :disabled="dragging"
                                :content="task.content"
                                placement="top-start"
                            >
                                <div
                                    style="max-width: 216px;"
                                    slot="content"
                                >
                                    {{ task.content }}
                                </div>
                                <div class="j-content-overflow-2line j-mgt8 j-mgb8">{{ task.content }}</div>
                            </el-tooltip>
                            <div
                                v-else
                                class="j-content-overflow-2line j-mgt8 j-mgb8"
                            >
                                {{ task.content }}
                            </div>
                        </li>

                        <li class="task-board-field__processor">
                            <label
                                class="task-board-item-label"
                            >
                                {{ $t(`jacp.task.field.processor`) }}
                            </label>
                            <jacp-erp
                                v-if="task.processor.erp"
                                avatar
                                vertical
                                :data="task.processor"
                                :display-name="true"
                                :avatar-size="24"
                                :disable-timline="$store.state.user.erp === task.processor.erp"
                            />
                            <i
                                v-else
                                class="jacp-icon-Avatar"
                                style="color: #909399; font-size: 24px;"
                            />
                        </li>
                        <li class="task-board-field__startDate">
                            <label
                                class="task-board-item-label"
                            >
                                {{ $t(`jacp.task.field.startDate`) }}
                            </label>
                            <div
                                :class="{disable: task.startDate}"
                                class="task-board-item-value"
                            >
                                {{ task.startDate | jacp-local-time('YYYY-MM-DD') }}
                            </div>
                        </li>
                        <li class="task-board-field__deadline">
                            <label
                                class="task-board-item-label"
                            >
                                {{ $t(`jacp.task.field.deadline`) }}
                            </label>
                            <div
                                :class="{disable: task.deadline}"
                                class="task-board-item-value"
                            >
                                {{ task.deadline | jacp-local-time('YYYY-MM-DD') }}
                            </div>
                        </li>
                        <li class="task-board-field__cardId">
                            <label
                                class="task-board-item-label"
                            >
                                {{ $t(`jacp.task.field.cardId`) }}
                            </label>
                            <router-link
                                class="task-board-field__value"
                                target="_blank"
                                @click.stop.native
                                :to="{
                                    name: 'teamspaceCardDetail',
                                    params: {
                                        ...$router.currentRoute.params,
                                    },
                                    query: {
                                        cardId: task.cardId,
                                        sprintId: ($store.state.plan.activePlan || {}).id,
                                    },
                                }"
                            >
                                <el-tooltip>
                                    <div
                                        slot="content"
                                        style="max-width: 280px"
                                    >
                                        {{ task.cardName }}
                                    </div>
                                    <span
                                        class="j-content-overflow-2line"
                                    >{{ task.cardName }}</span>
                                </el-tooltip>
                            </router-link>
                        </li>
                    </ul>
                </div>
                <!-- FIXME: 多了这一步会有性能问题 -->
                <!-- TODO: 如果是新建的话，要用新建的组件代替？目前新建是弹框，还不需要 -->
                <!-- <task-board-item
                    class="kanban-block__inner"
                    v-if="block.id"
                    :task="block"
                    :disabled="disabled"
                    :dragging="dragging"
                    @click.native="() => setActive(block)"
                /> -->
            </template>
        </kanban-board>
    </div>
</template>
<script>
import { availableStatus } from '$module/constant';
import Task from '$module/models/task';
// import TaskBoardItem from '$module/components/taskBoardItem';
export default {
    name: 'TaskBoard',
    props: {
        data: { type: Array, default: () => [] },
        disabled: { type: Boolean },
        loading: { type: Boolean },
        headerVisible: { type: Boolean, default: true },
        draggableOption: { type: Object, default: () => ({}) },
    },
    data() {
        return {
            availableStatus,
            active: {},
            dragging: false,
        };
    },
    methods: {
        onStart() {
            this.dragging = true;
        },
        onEnd() {
            this.dragging = false;
        },
        updateStatus({ added, stage = {} }) {
            setTimeout(() => {
                if (added) {
                    const { element: task } = added;
                    // console.log({ added, task, stage });
                    const { id } = task;
                    const status = stage.id;
                    Task.updateStatus({
                        id,
                        status,
                    }).then(() => {
                        if (task) {
                            task.status = status;
                        }
                    });
                }
            }, 0);
        },
        sort(evt, { prevBlock, nextBlock, block = {} }) {
            setTimeout(() => {
                // 0:目标id之前，1:目标id之后
                if (prevBlock || nextBlock) {
                    const position = prevBlock ? 1 : 0;
                    const targetId = position === 1 ? prevBlock.id : nextBlock.id;
                    Task.updateIndex({
                        position,
                        targetId,
                        id: block.id,
                    }).then(() => {
                        this.$emit('sorted', block.id);
                    });
                }
            }, 0);
        },
        setActive(task = {}) {
            this.active = task;
            this.$emit('on-active', task);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
// 任务看板：每个甬道最多显示两列，甬道的最小宽度为两列时的宽度.同时要设置看板为瀑布模式
@column-count: 1;
@kanban-block-width: 168px;
@kanban-block-margin: 12px;
@column-min-width: @kanban-block-margin * (@column-count + 1) + @kanban-block-width * @column-count;
.task-board{
    // 用原来横向滚动条的样式？
  .kanban-column{
      min-width: @column-min-width; // 不能小于两列的宽度
  }
  .kanban-block{
    //   background-color:#fff;
  }
  .task-board__add-button{
      color: @primaryColor;
      font-weight: 400;
      font-size: 12px;
      padding-left: 12px;
      text-align: left;
      padding: 2px 0;
      &:hover,
      &:active {
          color: @secendColor;
      }
  }
}

.task-board-item-root{
    font-size: 12px;
    padding: 16px;
    background-color:#fff;
    .jacp-form__value{
        color: @fontColor;
    }
    .task-board-field__processor{
        .jacp-form__value{
            padding: 2px 0;
        }
    }
    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
    }

    .task-board-item-label{
        white-space: nowrap;
        margin-right: 12px;
        min-width: 50px;
        display: inline-block;
        color: @fontThirdColor;
    }
    .task-board-item-value{
        line-height: 24px;
         color: @fontColor;
    }
    li[class^='task-board-field']{
        // line-height: 20px;
        display:flex;
        align-items: center;
    }
    .task-board-field__processor,
    .task-board-field__startDate,
    .task-board-field__deadline {
        height: 28px;
    }
    .task-board-field__content{
        font-size: 14px;
        color: @fontColor;
        line-height: 20px;
    }
    .icon-task1{
        font-size: 14px;
        color: #33D296;
    }
    .task-board-field__cardId{
        align-items: flex-start!important;
        margin-top: var(--gutter--mini);
        .task-board-field__value{
            color: var(--color--base--content);
            &:hover{
                color: var(--color--primary--hover);
            }
            // line-height:18px;
        }
    }
}
</style>
