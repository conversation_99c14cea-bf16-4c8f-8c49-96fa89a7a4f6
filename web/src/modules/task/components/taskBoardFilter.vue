<template>
    <div class="task-board-filter-root">
        <jacp-text
            size="16"
            class="task-board-filter__title"
        >
            筛选条件
        </jacp-text>
        <div style="position: absolute;width: 100%;border-bottom: 1px solid #e9e9e9;left: 0;" />
        <el-input
            class="j-mgt12"
            v-model="innerValue.keyword"
            prefix-icon="el-icon-search"
            placeholder="搜索标题/编号，回车确认"
            clearable
        />
        <jacp-text
            tag="label"
            class="j-mgt24"
            style="display:inline-block;"
        >
            范围
        </jacp-text>
        <div class="task-board-filter__filters j-mgt12">
            <el-radio-group
                v-model="innerValue.relationType"
            >
                <el-radio
                    :label="0"
                >
                    全部
                </el-radio>
                <el-radio
                    :label="1"
                >
                    仅我参与
                </el-radio>
            </el-radio-group>
        </div>
        <user-filter
            v-model="innerValue.processors"
            :data="processors"
        />
    </div>
</template>
<script>
export default {
    name: 'TaskBoardFilter',
    props: {
        value: { type: Object, required: true },
        processors: { type: Array, default: () => [] },
    },
    computed: {
        innerValue: {
            set(val) {
                this.$emit('input', val);
            },
            get() {
                return this.value;
            },
        },
    },
};
</script>
<style lang="less">
.task-board-filter{
    &-root{
      width: 304px;
      padding: 0 24px;
      position: relative;
      box-sizing: border-box;
    }

    &__title{
        line-height: 48px;
        height: 48px;
        // color: #333;
    }
}
</style>
