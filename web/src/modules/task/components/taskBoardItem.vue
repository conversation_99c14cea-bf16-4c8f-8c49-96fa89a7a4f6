<template>
    <div class="task-board-item-root">
        <ul>
            <!-- code 和 content 是固定显示的字段 -->
            <li class="task-board-field__code">
                <i class="jacp-icon-task1 j-mgr4" />
                <jacp-text
                    type="disable"
                    class="j-text-overflow"
                    tag="div"
                    :size="12"
                >
                    {{ task.code }}
                </jacp-text>
            </li>
            <!-- TODO: content需要最大高度 -->
            <li class="task-board-field__content">
                <jacp-input
                    v-if="false"
                    v-model="task.content"
                    @change="() => task.save('content')"
                />
                <el-tooltip
                    v-else
                    :disabled="dragging || !task.content || task.content.replace(/[\u4e00-\u9fa5]/ig, 'aa').length < 16"
                    :content="task.content"
                    placement="top-start"
                >
                    <div
                        style="max-width: 216px;"
                        slot="content"
                    >
                        {{ task.content }}
                    </div>
                    <div class="j-content-overflow-2line j-mgt8 j-mgb8">{{ task.content }}</div>
                </el-tooltip>
            </li>
            <!--  TODO: 点击任务查看详情的时候，和编辑某个任务字段的交互有些冲突 -->
            <li
                v-for="field in fields"
                :key="`task-${field.prop}`"
                :class="`task-board-field__${field.prop}`"
            >
                <jacp-text
                    class="task-board-item-label"
                    type="disable"
                    :size="12"
                >
                    {{ field.label }}
                </jacp-text>
                <template v-if="compMap[field.prop]">
                    <!--  以后任务显示的组件会动态的根据配置来决定是否显示某个属性，同时还需要支持编辑，所以用components:is来渲染属性。 -->
                    <component
                        :is="compMap[field.prop]"
                        :disabled="disabled"
                    />
                </template>
                <span v-else>
                    {{ task[field.prop] }}
                </span>
            </li>
        </ul>
    </div>
</template>
<script>
import moment from 'moment';


import { userFilter } from '@/plugins/utils';
import { plannedHourRule } from '@/modules/card/models/cardValidateRules';
import {
    handleDateChange, startDateOpt, deadlineOpt, handleProcessorChange,
} from '$module/models/taskColumns';

function getFieldRenderMapEditable(vm, task) {
    return {
        processor: {
            render() {
                return (<div on-click={e => e.stopPropagation()}>
                    <single-user
                        v-model={task.processor}
                        placeholder="请选择"
                        size={24}
                        max-count="1"
                        no-timline-users={[vm.$store.state.user]}
                        vertical={true}
                        show-suggestions={false}
                        load-data={userFilter(vm.$store.state.chilldteamspace.allMemberList)}
                        storage-key="card_processor"
                        disabled={true}
                        scopedSlots={{
                            reference: () => <i
                                class="el-icon-plus" style="fontSize: 16px;width: 24px; height: 24px; line-height: 23px;"
                            />,
                        }}
                        onOn-add={() => handleProcessorChange(task)}
                        onOn-delete={() => handleProcessorChange(task)}
                    >
                    </single-user>
                    <jacp-text type="error" size="12">
                        { !task.erp ? '不能为空' : null }</jacp-text>
                    { !task.erp && vm.disabled ? <i
                        class="jacp-icon-Avatar"
                        style="font-size: 24px;"
                    /> : null }
                </div>);
            },
        },
        startDate: {
            render() {
                return (<jacp-date
                    placeholder="请选择"
                    style="width: 110px"
                    v-model={task.startDate}
                    disabled={true}
                    rules={plannedHourRule.startDate}
                    on-change={$event => handleDateChange('startDate', $event, task)}
                    clearable={false}
                    picker-options={startDateOpt(task)}
                ></jacp-date>);
            },
        },
        deadline: {
            render() {
                return (<jacp-date
                    placeholder="请选择"
                    style="width: 110px"
                    v-model={task.deadline}
                    disabled={true}
                    rules={plannedHourRule.deadline}
                    on-change={$event => handleDateChange('deadline', $event, task)}
                    clearable={false}
                    picker-options={deadlineOpt(task)}
                ></jacp-date>);
            },
        },
    };
}
const flag = true;
export default {
    name: 'TaskBoardItem',
    props: {
        task: {
            type: Object,
            default: () => ({}),
        },
        visibleProperties: {
            type: Array,
            default: () => ['processor', 'startDate', 'deadline'],
        },
        // TODO: 目前不支持编辑，支持编辑的话需要调整一下编辑的组件样式和容器宽度
        // disabled: { type: Boolean, default: true },
        dragging: { type: Boolean, default: false },
    },
    data() {
        return { disabled: true };
    },
    computed: {
        fields() {
            return this.visibleProperties.map(prop => ({
                prop,
                label: this.$t(`jacp.task.field.${prop}`) || prop,
            }));
        },
        compMap() {
            return this.getFieldRenderMap(this.task);
        },
    },
    methods: {
        getFieldRenderMap(task) {
            const vm = this;
            if (vm.disabled) {
                return {
                    processor: {
                        render() {
                            return ((task.processor && task.processor.erp && flag)
                                ? <jacp-erp
                                    avatar
                                    vertical
                                    data={task.processor}
                                    display-name={true}
                                    avatarSize={24}
                                    disable-timline={vm.$store.state.user.erp === task.processor.erp}
                                ></jacp-erp>
                                : <i class="jacp-icon-Avatar" style="color: #909399; font-size: 24px;"></i>);
                        },
                    },
                    deadline: {
                        render() {
                            return (<jacp-text
                                type={task.deadline ? 'default' : 'disable'}
                                size="12"
                                class="task-board-item-value">{
                                    task.deadline
                                        ? moment(task.deadline).format('YYYY-MM-DD')
                                        : '无数据'}</jacp-text>);
                        },
                    },
                    startDate: {
                        render() {
                            return (<jacp-text
                                type={task.startDate ? 'default' : 'disable'}
                                size="12"
                                class="task-board-item-value">{
                                    task.startDate
                                        ? moment(task.startDate).format('YYYY-MM-DD')
                                        : '无数据'}</jacp-text>);
                        },
                    },
                };
            }
            return getFieldRenderMapEditable(vm, task);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.task-board-item-root{
    font-size: 12px;
    padding: 16px;
    .jacp-form__value{
        color: @fontColor;
    }
    .task-board-field__processor{
        .jacp-form__value{
            padding: 2px 0;
        }
    }
    ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
    }

    .task-board-item-label{
        white-space: nowrap;
        margin-right: 12px;
        min-width: 50px;
        display: inline-block;
    }
    .task-board-item-value{
        line-height: 24px;
    }
    li[class^='task-board-field']{
        // line-height: 20px;
        display:flex;
        align-items: center;
        &:not(.task-board-field__content) {
            height: 28px;
        }
        // margin: 4px 0px;
    }
    .task-board-field__content{
        font-size: 14px;
        color: @fontColor;
        line-height: 20px;
    }
    .icon-task1{
        font-size: 14px;
        color: #33D296;
    }
}
</style>
