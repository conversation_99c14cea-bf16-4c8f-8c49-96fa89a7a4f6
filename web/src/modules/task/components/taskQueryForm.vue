<template>
    <div class="query-conditions-form task-query-form">
        <div :class="{ 'query-conditions-form-wrapper': Object.keys(showedFields).length}">
            <dynamic-form
                :key="filter.id || ''"
                :fields="showedFields"
                v-model="data"
            />
            <el-row class="query-conditions-form__bottom">
                <el-popover
                    trigger="click"
                    placement="bottom-start"
                >
                    <jacp-query-conditions-selector
                        class="demands-conditions-selector"
                        ref="queryConditionsSelectorEl"
                        :fields="schema.fields"
                        :init-check-list="showedFieldKeys"
                        :categories="taskFilterCategories"
                        @change="changeShowedFields"
                    />
                    <jacp-button
                        class="query-edit-button"
                        size="mini"
                        slot="reference"
                        plain
                    >
                        编辑筛选条件<i class="el-icon-caret-bottom" />
                    </jacp-button>
                </el-popover>
                <slot />
            </el-row>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import pick from 'lodash/pick';
import QueryManager from '@/components/queryFilter/model/queryManager';
import DynamicForm from '@/models/dynamicForm';
// import QueryConditionsSelector from '@/modules/card/components/queryConditionsSelector';
import { availableStatus, taskFilterCategories } from '@/modules/task/constant';
import { fixupDateDefaultTime } from '@/plugins/dyfield';

export default {
    components: {
        // QueryConditionsSelector,
    },
    props: {
        filter: { type: Object },
    },
    data() {
        return {
            taskFilterCategories,
            data: {}, // 表单数据
            schema: {},
            showedFields: [], // 动态表单需要的 schema fields
            showedFieldKeys: [], // 编辑筛选条件需要回显哪些项选中了
            queryManager: new QueryManager('task-query'),
        };
    },
    computed: {
        ...mapGetters('chilldteamspace', ['memberList']),
        ...mapState('chilldteamspace', ['customizedConfigRoleList']),
        combineFields() {
            // 将 fields 数组内元素转换成 key(fieldName): value(field) 的形式，方便使用
            const result = {};
            (this.schema.fields || []).forEach((field) => {
                result[field.name] = field;
            });
            return result;
        },
        formData() {
            // 从 data 里过滤出勾选的条件
            const { data, showedFieldKeys } = this;
            const formattedData = pick(data, showedFieldKeys);
            return formattedData;
        },
    },
    watch: {
        filter() {
            this.initForm();
        },
    },
    created() {
        this.queryManager.getQuerySchema().then((data = {}) => {
            const formated = DynamicForm.toLocal(data);
            const { schema } = formated;
            (schema.fields || []).forEach(fixupDateDefaultTime);
            this.dynamicFormData = new DynamicForm(formated);
            this.schema = this.dynamicFormData.schema;
            this.initProps();
        });
    },
    methods: {
        initProps() {
            (this.schema.fields || []).forEach((field, index) => {
                switch (field.name) {
                case 'processorErp':
                    this.schema.fields[index].options = (this.memberList || []).map(item => ({
                        label: `${item.name} (${item.erp})`,
                        value: item.erp,
                    }));
                    break;
                case 'status':
                    this.schema.fields[index].options = (availableStatus || []).map(item => ({
                        label: item.name,
                        value: item.code,
                    }));
                    break;
                case 'roleCode':
                    this.schema.fields[index].options = (this.customizedConfigRoleList || []).map(item => ({
                        label: item.name,
                        value: item.code,
                    }));
                    break;
                default:
                    break;
                }
            });
        },
        initForm() {
            this.data = Object.assign({}, this.filter.value);
            this.showedFieldKeys = Object.keys(this.data);
            this.showedFields = Object.values(pick(this.combineFields, this.showedFieldKeys));
        },
        changeShowedFields(showedFields, checkList) {
            this.showedFields = showedFields;
            this.showedFieldKeys = checkList;
        },
    },
};
</script>

<style lang="less">
.task-query-form {
    & .is-nested {
        & .dynamic-form-item__nested {
            display: flex;
            width: 260px;
            & > .el-form-item {
                & > .el-form-item__content {
                    // border: 1px solid #E1E1E1;
                    height: 32px;
                }
            }
            & > .dynamic-form-item {
                margin-bottom: 0;
            }
            & [name='plannedHoursRelation'],
            & [name='remainingHoursRelation']{
                width: 130px;
            }

            & [name='plannedHours'],
            & [name='remainingHours'] {
                width: 100%;
            }
        }
    }
}
</style>
