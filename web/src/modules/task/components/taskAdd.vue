<template>
    <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="648px"
        class="card-task-dialog"
        :close-on-click-modal="false"
    >
        <card-task-form
            ref="addTaskForm"
            v-bind="$attrs"
            :remain-hours-disabled="!task.id"
            :card-task-info="task"
        />
        <span
            slot="footer"
            class="dialog-footer"
        >
            <el-button
                v-if="task.id"
                type="danger"
                plain
                style="float: left;"
                @click="deleteTask"
            >删 除</el-button>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <jacp-button
                type="primary"
                :on-click="confirm"
            >确 定</jacp-button>
        </span>
    </el-dialog>
</template>

<script>
import cardTaskForm from '@/modules/_components/cardTaskForm';
import Task from '$module/models/task';

export default {
    props: {
        title: { type: String, default: '新增任务' },
        task: { type: Object, default: () => ({}) },
        onAdded: { type: Function, default: () => {} },
        onClose: { type: Function, default: () => {} },
        onUpdate: { type: Function, default: () => {} },
        onRemove: { type: Function, default: () => {} },
    },
    data() {
        return {
            dialogVisible: false,
        };
    },
    mounted() {
        this.dialogVisible = true;
    },
    watch: {
        dialogVisible(val) {
            if (val) {
                this.$nextTick(() => {
                    this.$refs.addTaskForm.clear();
                });
            } else {
                this.onClose();
            }
        },
    },
    components: {
        cardTaskForm,
    },
    methods: {
        close() {
            this.dialogVisible = false;
        },
        confirm() {
            if (this.task.id) {
                return this.updateTask();
            }
            return this.addTask();
        },
        async addTask() {
            const { formData } = this.$refs.addTaskForm;
            const valid = await this.$refs.addTaskForm.$refs.cardTaskForm.validate();
            if (valid) {
                // 防重
                return Task.create({
                    // cardId: this.formData.cardId,
                    content: formData.content,
                    erp: formData.erp,
                    name: formData.name,
                    remainingHours: formData.remainingHours,
                    plannedHours: formData.plannedHours,
                    deadline: formData.deadline,
                    startDate: formData.startDate,
                    roleCode: formData.roleCode,
                }, formData.cardId).then((data) => {
                    this.onAdded(data);
                    this.dialogVisible = false;
                    this.$notify({
                        title: '保存成功！',
                        type: 'success',
                        duration: 2000,
                    });
                });
            }
            return Promise.reject(valid);
        },
        updateTask() {
            const { task } = this;
            const { formData } = this.$refs.addTaskForm;
            const done = (data) => {
                this.onUpdate(data);
                this.dialogVisible = false;
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            };
            if (task && task.id && task.save) {
                return task.save(formData).then(done);
            }
            return Task.update({ ...task, ...formData }, formData.cardId).then(done);
        },
        deleteTask() {
            const { task } = this;
            // if (task.)
            const message = '删除该条任务后将同步清除PMP中对应任务，是否确认？';
            this.$confirm(message, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                this.dialogVisible = false;
                Task.delete(task).then(() => this.onRemove(task));
            });
        },
    },
};
</script>

<style lang="less">
.card-task-dialog {
    .el-dialog__header {
        border-bottom: solid 1px #EBEEF5;
        padding: 12px 16px;
        .el-dialog__title {
            font-size: 16px;
            font-weight: 500;
        }
    }
    .el-dialog__footer {
        padding: 16px;
        .el-button--small {
            border-radius: 6px;
        }
    }
    .el-dialog__body {
        padding: 16px 16px 0 16px;
    }
    .el-dialog__headerbtn {
        right: 16px;
    }
}
</style>
