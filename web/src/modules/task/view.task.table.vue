<template>
    <div class="task-table-root">
        <!-- 通用筛选器 -->
        <JacpCommonQueryFilter
            ref="filter"
            filter-personal-key="task-query"
            v-model="currentQueryFilter"
            :initial-conditions="{
                keyword: '',
                processorErp: '',
                startDate: [],
                endDate: [],
            }"
            @query="tempQuery"
        >
            <div
                slot="suffix"
                class="task-table__page-header"
                style="margin-left: 48px"
            >
                <!-- 切换视图 -->
                <view-switcher />
                <el-divider
                    direction="vertical"
                />
                <!-- 列表分组 -->
                <el-dropdown
                    class="jacp-view-switcher j-mgr4"
                    trigger="click"
                >
                    <span class="el-dropdown-link">
                        {{ $t(`jacp.task.groupBy.${taskGroups[currentGroupKey]}`) }}
                        <jacp-icon
                            name="el-icon-caret-bottom"
                            active
                            size="14"
                            style="margin-left: -4px;"
                        />
                    </span>
                    <el-dropdown-menu
                        slot="dropdown"
                    >
                        <el-dropdown-item
                            v-for="(item) in taskGroups"
                            :key="item"
                            @click.native="setCurrentGroupKey(item)"
                        >
                            <span style="white-space: nowrap">{{ $t(`jacp.task.groupBy.${taskGroups[item]}`) }}</span>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <!-- TODO: 目前只做敏捷空间的任务列表 -->
                <jacp-dropdown-settings
                    label="任务"
                    module-key="task"
                    setting-type="list"
                    :space-privilage="spacePrivilage"
                    @save-displayFields="handleColumnsChange"
                    @export="exportTasks"
                />
            </div>
        </JacpCommonQueryFilter>

        <!-- 列表 -->
        <div class="task-table-content">
            <basic-table
                v-if="ungroupedListElHeight"
                class="j-table"
                :height="ungroupedListElHeight"
                v-show="ungrouped"
                :data="ungroupedList.data"
                :columns="taskColumns"
                @sort-change="($event) => {
                    handleSortChange($event);
                    loadData();
                }"
            >
                <el-table-column
                    :label="$t('jacp.task.field.operations')"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="handleDelete(scope.row, scope.$index)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>

                <!-- 分页 -->
                <el-pagination
                    class="j-pagination"
                    slot="pager"
                    background
                    style="margin-top: 16px; text-align: right;"
                    :current-page.sync="ungroupedList.pager.currentPage"
                    :page-size.sync="ungroupedList.pager.pageSize"
                    :total="ungroupedList.pager.total"
                    @prev-click="loadUngroupedTasks"
                    @next-click="loadUngroupedTasks"
                    @current-change="loadUngroupedTasks"
                    @size-change="loadUngroupedTasks"
                    layout="prev, pager, next, total, sizes, jumper"
                />
            </basic-table>
            <!-- 分组列表 -->
            <el-collapse
                v-show="!ungrouped"
                class="jacp-collapse"
                v-model="activeGroupIds"
            >
                <jacp-collapse-item
                    v-for="group in pageGroups"
                    :key="group.id"
                    :title="group.name"
                    :name="group.id"
                    border
                >
                    <template slot="title">
                        <component
                            :is="group.constructor.getComponent(group)"
                            :data="group.groupInfo"
                            v-if="group.constructor.getComponent(group) !== null"
                        />
                        <span v-else>{{ group.name }}</span>
                        <jacp-text
                            type="disable"
                            size="12"
                            class="j-mgl8"
                            font-weight="400"
                        >
                            共计<span class="j-mgl4">{{ group.groupInfo.total || group.list.pager.total }}</span>
                        </jacp-text>
                    </template>
                    <basic-table
                        class="j-table"
                        :data="group.list.data"
                        :columns="taskColumns"
                        @sort-change="($event) => {
                            handleSortChange($event);
                            group.load(conditions);
                        }"
                    >
                        <el-table-column
                            :label="$t('jacp.task.field.operations')"
                        >
                            <template slot-scope="scope">
                                <el-button
                                    type="text"
                                    @click="handleDelete(scope.row, scope.$index, group)"
                                >
                                    删除
                                </el-button>
                            </template>
                        </el-table-column>

                        <!-- 分页 -->
                        <el-pagination
                            class="j-pagination"
                            slot="pager"
                            background
                            style="margin-top: 16px; text-align: right;"
                            :current-page.sync="group.list.pager.currentPage"
                            :page-size.sync="group.list.pager.pageSize"
                            :total="group.list.pager.total"
                            @prev-click="() => group.load(conditions)"
                            @next-click="() => group.load(conditions)"
                            @current-change="() => group.load(conditions)"
                            @size-change="() => group.load(conditions)"
                            layout="prev, pager, next, total, sizes, jumper"
                        />
                    </basic-table>
                </jacp-collapse-item>
            </el-collapse>
            <jacp-empty
                v-if="!ungrouped && !groups.length"
                label="暂无分组"
                style="height: 180px;"
            />
        </div>
    </div>
</template>
<script>
import isUndefined from 'lodash/isUndefined';
import fscreen from 'fscreen';
// const
import { onElementScrollToBottom, getSpaceIdFromRouter, flattenObject } from '@/plugins/utils';
import { listEntities } from '@/modules/teamspace/constant';
import { taskListGroups } from '@/modules/task/constant';
// local components
// import TaskFilter from '@/modules/task/components/taskFilter';
import TaskQueryForm from '@/modules/task/components/taskQueryForm';
// TODO: remove 后面会改成统一的复合查询，不需要单独引用
// import QueryManager from '@/components/queryFilter/model/queryManager';
// models
import Task from '@/modules/task/models/task';
import TaskList from '@/modules/task/models/taskList';
import taskColumnsMap from '@/modules/task/models/taskColumns';
import TaskGroup, { getGroupFilterConditions, getGroupInfo } from '@/modules/task/models/taskGroup';
// FIXME: 此处等改造drodownSetting的时候一起修改
import PersonalModel from '@/models/personal';
import sortBy from 'lodash/sortBy';
import throttle from 'lodash/throttle';
import cloneDeep from 'lodash/cloneDeep';
import LazyLoader from '@/plugins/lazyLoader';


export default {
    name: 'ViewTaskList',
    props: {
        spacePrivilage: {
            type: Object,
            default: () => {},
        },
        disabled: { type: Boolean },
    },
    components: {
        // TaskFilter,
        TaskQueryForm,
    },
    data() {
        return {
            entities: listEntities.slice(),
            taskGroups: taskListGroups,
            currentGroupKey: localStorage.getItem('cardTaskGroupKey') || taskListGroups.ungrouped,
            activeGroupIds: '',
            sortedColumns: [],
            groups: [],
            pageGroups: [],
            lazyLoader: undefined,
            // TODO: 此处有个table columns
            ungroupedList: new TaskList(),
            advanceQueryFilter: {},
            currentQueryFilter: {}, // TODO: 代替上面的
            queryFilterConditions: {},
            sortConditions: {},
            ungroupedListElHeight: undefined,
            event: this.$initEvent(),
            showForm: false,
            // queryManager: new QueryManager('task-query'),
        };
    },
    created() {
        this.calcContentHeight = throttle(this.calcContentHeight.bind(this), 200);
        fscreen.addEventListener('fullscreenchange', this.calcContentHeight, false);
        window.addEventListener('resize', this.calcContentHeight);

        const { event: $event } = this;
        $event.$on('refresh-list', () => this.loadData());
    },
    activated() {
        this.calcContentHeight();
    },
    beforeDestroy() {
        this.rootContainer.removeEventListener('scroll', this.loadMore);
        this.rootContainer = null;
    },
    destroyed() {
        window.removeEventListener('resize', this.calcContentHeight);
    },
    mounted() {
        // this.loadUngroupedTasks();
        this.initColumns();
        this.calcContentHeight();
        this.rootContainer = this.$el;
        this.initListener();
    },
    computed: {
        ungrouped() { return this.currentGroupKey === taskListGroups.ungrouped; },
        conditions() {
            return {
                spaceId: getSpaceIdFromRouter(),
                sprintId: Number(this.sprintId),
                ...this.queryFilterConditions,
                ...this.sortConditions,
            };
        },
        currentPlan() {
            return this.$store.state.plan.activePlan || {};
        },
        sprintId() {
            return (this.currentPlan).id || this.$route.query.sprintId;
        },
        taskColumns() {
            const { sortedColumns, disabled } = this;
            return sortedColumns.map(({ label, name }) => {
                const columnInfo = taskColumnsMap[name];
                if (columnInfo) {
                    return {
                        disabled,
                        ...columnInfo,
                        label,
                    };
                }
                return {
                    prop: name, label, width: 150, disabled,
                };
            });
        },
        activeGroups() {
            const { groups, activeGroupIds } = this;
            return groups.filter(group => activeGroupIds.includes(group.id));
        },
        showSave() {
            return this.advanceQueryFilter.type === 2;
        },
    },
    watch: {
        currentGroupKey: {
            // immediate: true,
            handler: 'loadGroups',
        },
        activeGroupIds: {
            immediate: true,
            handler: 'loadActiveGroupTasks',
        },
        'currentPlan.id': {
            // immediate: false,
            handler: 'loadData',
        },
    },
    methods: {
        initColumns() {
            PersonalModel.getListDisplayFields('task').then((data) => {
                this.handleColumnsChange(sortBy(data.filter(d => d.show), 'index'));
                setTimeout(() => {
                    this.ungroupedList.columns = this.taskColumns;
                }, 0);
            });
        },
        // FIXME: 合二为一
        handleColumnsChange(sortedColumns = []) {
            this.sortedColumns = sortedColumns;
        },
        loadData() {
            // FIXME: 这里判断是否合理
            if (!this.sprintId) return;
            if (!this.currentQueryFilter.id) return;
            if (this.ungrouped) {
                this.loadUngroupedTasks();
            } else {
                if (!this.ungroupedList.data.length) {
                    this.loadUngroupedTasks();
                }
                this.loadGroups();
            }
        },
        // 临时查询，使用筛选器表单的内容查询
        tempQuery(conditions) {
            if (!isUndefined(conditions)) {
                this.queryFilterConditions = this.toServerData(conditions);
            }
            this.loadData();
        },
        handleSortChange({ order, prop } = {}) {
            this.sortConditions = order ? {
                orderField: prop,
                orderType: order.includes('desc') ? 'desc' : 'asc',
            } : {};
        },
        handleDelete(row, index, group) {
            const message = '删除该条任务后将同步清除PMP中对应任务，是否确认？';
            this.$confirm(message, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => Task.delete(row)).then(() => {
                if (group) {
                    group.load(this.conditions);
                    group.groupInfo.total -= 1;
                } else {
                    this.loadUngroupedTasks();
                }
            });
        },
        loadUngroupedTasks() {
            this.ungroupedList.load(this.conditions);
        },
        loadGroups() {
            if (this.ungrouped) return;
            TaskGroup.getGroups(this.currentGroupKey, this.conditions)
                .then((groups = []) => {
                    this.groups = groups.map(group => new TaskGroup({
                        groupKey: this.currentGroupKey,
                        conditions: getGroupFilterConditions(this.currentGroupKey, group),
                        list: new TaskList(),
                        // TODO: 不同的group里返回的结构都不一样，怎么对齐到id和name上？
                        ...getGroupInfo(this.currentGroupKey, group),
                        groupInfo: group, // 存一下原始的值？
                    }));
                    this.lazyLoader = new LazyLoader({ originData: this.groups, pageSize: 20 });
                    this.loadMore();
                })
                .then(this.loadActiveGroupTasks);
        },
        loadActiveGroupTasks() {
            if (!this.sprintId) return;
            const { activeGroups, conditions } = this;
            activeGroups.forEach((group) => {
                if (!group.loaded) {
                    group.load(conditions);
                }
            });
        },
        // 导出
        exportTasks() {
            // const condition = this.$refs.filter.getCondition() || {};
            return Task.export(this.conditions);
        },
        // 临时处理成动态高度，以后可能要改成整个右侧滚动
        calcContentHeight() {
            setTimeout(() => {
                const screenHeight = document.body.scrollHeight;
                // FIXME:
                let marginHeight = 50 + 48 + 86 + 24; // margin
                if (fscreen.fullscreenElement) {
                    marginHeight -= 48;
                }
                this.ungroupedListElHeight = screenHeight - marginHeight; // pager
            }, 500);
        },
        setCurrentGroupKey(val) {
            this.currentGroupKey = val;
            localStorage.setItem('cardTaskGroupKey', val);
        },
        toServerData(conditions) {
            return flattenObject(conditions, false);
        },
        resetForm() {
            this.$refs.queryForm.data = cloneDeep(this.$refs.queryForm.filter.value);
            this.keyword = this.$refs.queryForm.data.keyword;
            this.tempQuery();
        },
        initListener() {
            onElementScrollToBottom({
                el: this.rootContainer,
                distance: 100,
                callback: this.loadMore,
            });
        },
        loadMore() {
            this.lazyLoader.load();
            this.pageGroups = this.lazyLoader.loadedData;

            /** 检测没有出现 滚动条 */
            this.$nextTick(() => {
                if (this.pageGroups.length !== this.groups.length
                    && this.rootContainer.scrollHeight <= this.rootContainer.clientHeight) {
                    this.loadMore();
                }
            });
        },
    },
};
</script>
<style lang="less">
.cardslist-root--fullscreen{
    .task-table-root{
        padding-top: 0;
        height: 100vh;
    }
}
.task-table-root{
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .el-table th>.cell{
        padding-left: 24px;
    }
    .j-table tbody td{
        overflow: hidden;
    }
    td.cell-fill .cell {
        width: 100%;
    }
    .task-table__page-header {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
.task-table-content{
    padding: 0 24px;
    flex-basis: 100%;
    position: relative;
    &>.j-table,
    & .jacp-collapse{
        transition: height 0.3s;
        .el-collapse-item__content{
            padding: 0 16px 16px 16px;
        }
    }
    & .j-table{
        background: #fff;
        // padding: 16px;
        & /deep/ .el-table__body-wrapper {
            height: calc(100% - 50px) !important;
        }
    }
}

.teamspace-task-table__form {
    max-height: 0;
    // overflow: hidden;
    opacity: 0;
    transition: all .3s cubic-bezier(.2,0,.8,1);
    will-change: transform;
    &--show {
        opacity: 1;
        max-height: 300px;
    }
}
</style>
