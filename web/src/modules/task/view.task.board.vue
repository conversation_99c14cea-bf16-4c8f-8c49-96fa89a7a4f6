<template>
    <div
        class="task-board-root"
        :class="{
            'task-board-root--ungrouped': ungrouped,
        }"
    >
        <div
            class="list-filter__root"
            style="border-top:var(--border--hr)"
        >
            <span style="flex-basis: 100%;" />
            <template>
                <!-- 切换视图 -->
                <view-switcher :disabled-views="['report']" />
                <jacp-text
                    class="j-whitespace-nowwrap j-mgl16"
                    icon="jacp-icon-a-ic-toolbar-fliter"
                    type="secend"
                    active
                    @click.native="() => $refs.filterPanelIns.open()"
                >
                    筛选
                </jacp-text>
                <!-- <span
                    class="jacp-button-board jacp-button-filter"
                    style="margin-right: 0px"
                    @click="() => $refs.filterPanelIns.open()"
                >
                    筛选
                </span> -->
                <el-divider
                    class="j-mgl16 j-mgr16"
                    direction="vertical"
                />
                <!-- 列表分组 -->
                <el-dropdown
                    class="jacp-view-switcher"
                    trigger="click"
                >
                    <span class="el-dropdown-link">
                        {{ $t(`jacp.task.groupBy.${taskGroups[currentGroupKey]}`) }}
                        <jacp-icon
                            name="el-icon-caret-bottom"
                            active
                            style="margin-left: -4px;"
                        />
                    </span>
                    <el-dropdown-menu
                        slot="dropdown"
                        :append-to-body="false"
                    >
                        <el-dropdown-item
                            v-for="(item) in taskGroups"
                            :key="item"
                            @click.native="setCurrentGroupKey(item)"
                        >
                            <span style="white-space: nowrap">{{ $t(`jacp.task.groupBy.${taskGroups[item]}`) }}</span>
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </template>
        </div>
        <task-board
            v-show="ungrouped"
            v-loading="loading"
            element-loading-background="rgba(255, 255, 255, 0.2)"
            class="task-board-container"
            ref="boardEl"
            :loading.sync="loading"
            :data="filteredData"
            :disabled="disabled"
            @on-active="showTask"
            @sorted="load"
            @add="createTask"
        />
        <!-- 分组看板 -->
        <group-wrapper
            ref="cardsBoardGroupIns"
            v-if="!ungrouped"
            v-model="activeGroupIds"
            :groups="filteredGroups"
            :title="$t(`jacp.task.groupBy.${taskGroups[currentGroupKey]}`)"
        >
            <template v-slot:header>
                <!-- flex 1保持三等分 -->
                <div
                    v-for="(status, index) in availableStatus"
                    :key="status.value"
                    class="cards-board__group__label"
                    :style="{
                        borderLeft: index !== 0 ? `1px solid #DCDFE6` : '',
                        flex: 1,
                        margin: 0,
                    }"
                >
                    <span
                        class="jacp-status-label cards-board__group__label__status"
                        :style="{
                            color: status.color,
                        }"
                    >{{ status.name + `（${(filteredStatusData[status.code] || []).length}）` }}</span>
                </div>
            </template>
            <!-- 分组的title -->
            <template v-slot:title="{group, isActive}">
                <div
                    style="display: inline-flex;"
                >
                    <component
                        :is="group.constructor.getComponent(group)"
                        :data="group.groupInfo"
                        v-if="group.constructor.getComponent(group) !== null"
                    />
                    <ellipsis-text
                        v-else
                        :content="group.name"
                        :max-width="isActive ? 108 : 450"
                    />
                    <!-- <span v-else>{{ group.name }}</span> -->
                    <jacp-text
                        type="disable"
                        size="12"
                        font-weight="400"
                        class="jacp-collapse__header-right j-mgl8"
                        :style="{
                            position: isActive ? 'absolute' : 'static',
                            top: !isActive ? 0 : '36px',
                            left: isActive ? '32px' : 0,
                            whiteSpace: 'nowrap'
                        }"
                    >
                        {{ isActive ? '任务总数:': '共计' }}
                        <span>
                            {{ group.data.length }}</span>
                    </jacp-text>
                </div>
            </template>
            <!-- 分组的看板内容, 不允许跨组拖动 -->
            <template v-slot:content="{group}">
                <task-board
                    v-loading="loading"
                    element-loading-background="rgba(255, 255, 255, 0.2)"
                    class="task-board-container"
                    ref="boardEl"
                    :draggable-option="{
                        group: `${group.groupKey}-${group.id}-task`
                    }"
                    :loading.sync="loading"
                    :data="group.data"
                    :disabled="disabled"
                    :header-visible="false"
                    :need-lazy="false"
                    @on-active="showTask"
                    @sorted="load"
                    @add="createTask"
                />
            </template>
        </group-wrapper>
        <jacp-empty
            v-if="!ungrouped && !groups.length"
            label="暂无分组"
        />
        <jacp-off-canvas
            ref="filterPanelIns"
            :fixed-on-document="false"
        >
            <task-board-filter
                v-model="filterConditions"
                :processors="allProcessors"
            />
        </jacp-off-canvas>
    </div>
</template>

<script>
import debounce from 'lodash/debounce';
// other components
// import CardsBoardInfo from '@/modules/card/components/cardsBoardInfo';
// const
import { listEntities } from '@/modules/teamspace/constant';
import { taskBoardGroups, availableStatus } from '@/modules/task/constant';
import TaskBoard from '$module/components/taskBoard';
// local components
import TaskBoardFilter from '@/modules/task/components/taskBoardFilter';
import { space as SpaceModel } from '@/models/teamspace';
import { TaskBoardList } from '$module/models/taskList';
import TaskActions from '$module/models/taskActions';
import TaskGroup, { getGroupInfo, getGroupFilterFn } from '@/modules/task/models/taskGroup';
import { getSpaceIdFromRouter } from '@/plugins/utils';

const keywordChecker = keyword => task => (task.code.includes(keyword)
|| task.content.includes(keyword)
|| `${task.id}`.includes(keyword));
const relationTypeChecker = erp => task => task.erp === erp;
const processorsChecker = (processors = []) => task => processors.includes(task.erp);
export default {
    name: 'ViewTaskBoard',
    props: {
        disabled: { type: Boolean },
    },
    components: {
        TaskBoard,
        TaskBoardFilter,
        // CardsBoardInfo,
    },
    created() {
        this.handleConditionChange = debounce(this.handleConditionChange.bind(this), 200);
    },
    computed: {
        ungrouped() {
            return this.currentGroupKey === taskBoardGroups.ungrouped;
        },
        conditions() {
            return {
                spaceId: getSpaceIdFromRouter(),
                sprintId: Number(this.sprintId),
            };
        },
        sprintId() {
            return (this.$store.state.plan.activePlan || {}).id;
        },
        currentUserErp() {
            return this.$store.state.user.erp;
        },
        filteredGroups() {
            const { groups, filteredData } = this;
            return groups.map((group) => {
                const groupIns = new TaskGroup({
                    groupKey: this.currentGroupKey,
                    ...getGroupInfo(this.currentGroupKey, group),
                    groupInfo: group, // 存一下原始的值？
                });
                const filterFn = getGroupFilterFn(this.currentGroupKey, groupIns);
                // 所有task分装的每个分组里
                groupIns.data = filteredData.filter(filterFn);
                // console.log(group, groupIns.data);
                return groupIns;
            });
        },
        filteredStatusData() {
            const { filteredData } = this;
            const statusMap = {};
            return filteredData.reduce((map, task) => {
                if (!map[task.status]) {
                    map[task.status] = [];
                }
                map[task.status].push(task);
                return map;
            }, statusMap);
        },
    },
    data() {
        const currentGroupKey = localStorage.getItem('cardTaskGroupKey');
        return {
            entities: listEntities.slice(),
            allProcessors: [],
            filterConditions: {
                keyword: '',
                relationType: 0,
                processors: [],
            },
            boardFilterVisible: true,
            list: new TaskBoardList(),
            filteredData: [],
            loading: false,
            // 分组看板
            taskGroups: taskBoardGroups,
            currentGroupKey: (currentGroupKey && Object.keys(taskBoardGroups).includes(currentGroupKey))
                ? currentGroupKey
                : taskBoardGroups.ungrouped,
            activeGroupIds: '',
            groups: [],
            availableStatus,
        };
    },
    methods: {
        load() {
            this.loading = true;
            return this.list.load(this.conditions).then(() => {
                this.handleConditionChange();
            });
        },
        async loadAllProcessors() {
            this.allProcessors = await SpaceModel.getProsessorsInSprint(this.conditions.spaceId, {
                modeTypeId: this.conditions.sprintId,
            });
        },
        // 看板筛选
        handleConditionChange() {
            const { currentUserErp } = this;
            const {
                keyword,
                relationType,
                processors,
            } = this.filterConditions;
            const { data = [] } = this.list;
            const checker = {
                keywordExist: keywordChecker(keyword),
                isMine: relationTypeChecker(currentUserErp),
                processorsChecker: processorsChecker(processors),
            };
            this.filteredData = data.filter((task) => {
                if (keyword && !checker.keywordExist(task)) {
                    return false;
                }
                if (relationType === 1 && !checker.isMine(task)) {
                    return false;
                }
                if (processors.length && !checker.processorsChecker(task)) {
                    return false;
                }
                return true;
            });
        },
        createTask() {
            TaskActions.create({
                onTaskAdded: () => this.load(), // FIXME: 不刷新可以么？下同
            });
        },
        showTask(task) {
            TaskActions.edit({
                task,
                onTaskUpdated: () => this.load(),
                onTaskRemoved: () => this.load(),
            });
        },
        loadGroups() {
            if (this.ungrouped || !this.conditions.sprintId) {
                return;
            }
            TaskGroup.getGroups(this.currentGroupKey, this.conditions)
                .then((groups = []) => {
                    this.groups = groups;
                });
        },
        setCurrentGroupKey(val) {
            this.currentGroupKey = val;
            localStorage.setItem('cardTaskGroupKey', val);
        },
    },
    watch: {
        conditions: {
            immediate: true,
            handler() {
                // 刷新看板数据
                this.load().then(() => {
                    // 未分组的，更新看板滚动位置
                    if (this.ungrouped) {
                        if (this.$refs.boardEl && this.$refs.boardEl?.$refs?.boardIns) {
                            this.$refs.boardEl.$refs.boardIns.scrollToTop();
                        }
                    } else {
                        this.activeGroupIds = [];
                        this.loadGroups();
                    }
                });
            },
        },
        sprintId: {
            immediate: true,
            handler(val) {
                if (!val) {
                    return;
                }
                this.loadAllProcessors();
            },
        },
        filterConditions: {
            deep: true,
            handler: 'handleConditionChange',
        },
        boardFilterVisible(val) {
            if (val) {
                this.$refs.filterPanelIns.open();
            }
        },
        currentGroupKey: {
            handler: 'loadGroups',
        },
    },
};
</script>
<style lang="less">
@board-height:  calc(~"100vh - 196px");
@board-height--fullscreen: calc(~"100vh - 124px");

.task-board-root{
    overflow: auto;
    .task-board-container{
       padding: var(--gutter--medium) var(--gutter--large);
       background-color: #fafbfc;
    }
    .group-wrapper{
        .task-board{
            padding-top: 0;
        }
        .kanban-column:first-child{
            border-left: none;
        }
        .jacp-collapse-item .el-collapse-item__wrap{
            flex-basis: 100%;
        }
        .kanban-inner-list{
            height: calc(~"100% - 24px");
        }
        .jacp-collapse-item .el-collapse-item__content,
        .jacp-collapse-item .el-collapse-item__wrap{
            background-color: #F0F2F5;
        }
    }
    .kanban-root{
        min-height: 100px;
        overflow: hidden;
    }
    .kanban-inner-list{
        height: calc(~"100% - 24px");
    }
    &--ungrouped{
        .kanban-root{
            height: @board-height;
            overflow: auto;
        }
    }

}

.cardslist-root--fullscreen{
    .task-board-root{
        padding-top: 0;
        height: 100vh;
    }
    .task-board-root--ungrouped{
        .kanban-root{
            height: @board-height--fullscreen;
        }
    }
}
</style>
