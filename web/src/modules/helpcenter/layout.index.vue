<template>
    <div class="jacp-helpcenter">
        <div
            class="jacp-helpcenter-header"
            @click="linkHandler('jagile', 'index')"
        />
        <div>
            <section>
                <div>
                    <p class="jacp-helpcenter-title">
                        用户指南
                    </p>
                    <p class="jacp-helpcenter-tips">
                        快速开始，五分钟上手行云
                    </p>
                    <div
                        class="jacp-helpcenter-item1"
                        @click="linkHandler('guide', 'createDemand')"
                    >
                        <div class="demand" />
                        <p>创建需求</p>
                    </div>
                    <div
                        class="jacp-helpcenter-item1"
                        @click="linkHandler('guide', 'createSprint')"
                    >
                        <div class="sprint" />
                        <p>创建迭代</p>
                    </div>
                    <div
                        class="jacp-helpcenter-item1"
                        @click="linkHandler('guide', 'createCard')"
                    >
                        <div class="card" />
                        <p>创建卡片</p>
                    </div>
                    <div
                        class="jacp-helpcenter-item1"
                        @click="linkHandler('guide', 'createSpace')"
                    >
                        <div class="space" />
                        <p>创建团队空间</p>
                    </div>
                    <div
                        class="jacp-helpcenter-item1"
                        @click="linkHandler('guide', 'workspace')"
                    >
                        <div class="workspace" />
                        <p>个人工作台</p>
                    </div>
                    <div
                        class="jacp-helpcenter-item1"
                        @click="linkHandler('guide', 'project')"
                    >
                        <div class="report" />
                        <p>项目管理</p>
                    </div>
                </div>
            </section>
            <section>
                <div>
                    <p class="jacp-helpcenter-title">
                        使用手册
                    </p>
                    <p class="jacp-helpcenter-tips">
                        快速找到所需文档
                    </p>
                    <div class="jacp-helpcenter-box">
                        <div
                            class="jacp-helpcenter-item2"
                            @click="linkHandler('document', 'demand')"
                        >
                            <div class="demand-management" />
                            <p class="jacp-helpcenter-title2">
                                需求管理
                            </p>
                            <p class="jacp-helpcenter-tips2">
                                规范流程，追踪进度的需求神器
                            </p>
                        </div>
                        <div
                            class="jacp-helpcenter-item2"
                            @click="linkHandler('document', 'teamspace')"
                        >
                            <div class="teamspace" />
                            <p class="jacp-helpcenter-title2">
                                团队空间
                            </p>
                            <p class="jacp-helpcenter-tips2">
                                规范团队，践行迭代的敏捷神器
                            </p>
                        </div>
                        <div
                            class="jacp-helpcenter-item2"
                            @click="linkHandler('document', 'datacenter')"
                        >
                            <div class="datacenter" />
                            <p class="jacp-helpcenter-title2">
                                数据中心
                            </p>
                            <p class="jacp-helpcenter-tips2">
                                心中有数，决胜千里的度量神器
                            </p>
                        </div>
                    </div>
                    <div class="jacp-helpcenter-more">
                        <a @click="linkHandler('document', 'more')">
                            查看更多
                            <i class="j-more" />
                        </a>
                    </div>
                </div>
            </section>
            <section>
                <div>
                    <p class="jacp-helpcenter-title">
                        新手入门&FAQ
                    </p>
                    <p class="jacp-helpcenter-tips">
                        视频引导，答疑解惑，轻松上手
                    </p>
                    <div class="jacp-helpcenter-box">
                        <div
                            class="jacp-helpcenter-item3"
                        >
                            <div>
                                <div class="demand" />
                                <p class="jacp-helpcenter-tips3">
                                    【需求管理】需求的新建、管理和编辑
                                </p>
                            </div>
                            <p
                                class="jacp-helpcenter-tips3"
                                @click="linkHandler('FAQ', 'q4')"
                            >
                                【需求】需求拆分的规则是怎样的？
                            </p>
                            <p
                                class="jacp-helpcenter-tips3"
                                @click="linkHandler('FAQ', 'q3')"
                            >
                                【需求】如果人员异动/离职后，相应需求怎么办？
                            </p>
                        </div>
                        <div
                            class="jacp-helpcenter-item3"
                        >
                            <div>
                                <div class="space" />
                                <p class="jacp-helpcenter-tips3">
                                    【团队空间】团队空间的新建、管理和便捷拷贝
                                </p>
                            </div>
                            <p
                                class="jacp-helpcenter-tips3"
                                @click="linkHandler('FAQ', 'q1')"
                            >
                                【用户权限】团队空间、卡片、迭代由谁来创建？
                            </p>
                            <p
                                class="jacp-helpcenter-tips3"
                                @click="linkHandler('FAQ', 'q2')"
                            >
                                【团队空间】团队空间以什么维度来建立？
                            </p>
                        </div>
                        <div
                            class="jacp-helpcenter-item3"
                            @click="linkHandler('FAQ', 'org')"
                        >
                            <div>
                                <div class="org" />
                                <p class="jacp-helpcenter-tips3">
                                    【部门个性设置】部门便捷、标签管理
                                </p>
                            </div>
                            <p
                                class="jacp-helpcenter-tips3"
                                @click="linkHandler('FAQ', 'q5')"
                            >
                                【个性设置】怎么自定义看板状态？
                            </p>
                            <p
                                class="jacp-helpcenter-tips3"
                                @click="linkHandler('FAQ', 'q6')"
                            >
                                【个性设置】怎么设置空间人员与权限？
                            </p>
                        </div>
                    </div>
                    <div class="jacp-helpcenter-more">
                        <a @click="linkHandler('FAQ', 'more')">
                            查看更多
                            <i class="j-more" />
                        </a>
                    </div>
                </div>
            </section>
            <section>
                <div>
                    <p class="jacp-helpcenter-title">
                        最近发布
                    </p>
                    <p class="jacp-helpcenter-tips">
                        版本说明，功能更新
                    </p>
                    <div class="jacp-helpcenter-box">
                        <div
                            class="jacp-helpcenter-item4"
                            @click="linkHandler('version', 'v1')"
                        >
                            <p class="jacp-helpcenter-title">
                                行云v1.7.0
                            </p>
                            <p class="jacp-helpcenter-tips">
                                2019.6.27
                            </p>
                        </div>
                        <div
                            class="jacp-helpcenter-item4"
                            @click="linkHandler('version', 'v2')"
                        >
                            <p class="jacp-helpcenter-title">
                                行云v1.6.5
                            </p>
                            <p class="jacp-helpcenter-tips">
                                2019.5.27
                            </p>
                        </div>
                        <div
                            class="jacp-helpcenter-item4"
                            @click="linkHandler('version', 'v3')"
                        >
                            <p class="jacp-helpcenter-title">
                                行云v1.6.0
                            </p>
                            <p class="jacp-helpcenter-tips">
                                2019.5.16
                            </p>
                        </div>
                    </div>
                    <div class="jacp-helpcenter-more">
                        <a @click="linkHandler('version', 'more')">
                            查看更多
                            <i class="j-more" />
                        </a>
                    </div>
                </div>
            </section>
        </div>
        <div class="jacp-helpcenter-footer">
            <div class="jacp-helpcenter-footer__top">
                <div>
                    <p class="jacp-helpcenter-title">
                        开启高效敏捷协同之旅
                    </p>
                    <p class="jacp-helpcenter-tips">
                        欢迎体验行云，它将让您不再为团队间沟通协作烦恼，一切需求和任务都将变得如流水般丝滑顺畅，如有需要可与我们的敏捷专家沟通，我们将根据您团队的实际情况推荐合适的敏捷实践方案
                    </p>
                    <a @click="linkHandler('jagile', 'index')">立即开始</a>
                </div>
            </div>
            <div class="jacp-helpcenter-footer__bottom">
                <div class="jacp-helpcenter-help">
                    <div>
                        <img src="../../assets/images/<EMAIL>">
                        <p class="jacp-helpcenter-tips3 foot-font2">
                            公司级敏捷研发协作平台<br>全方位解决业务侧与产研侧协作沟通问题
                        </p>
                    </div>
                    <div>
                        <p class="jacp-helpcenter-title2 foot-font">
                            使用反馈
                        </p>
                        <p class="jacp-helpcenter-tips3 foot-font2">
                            <span>反馈邮箱：</span>
                            <span><EMAIL></span>
                        </p>
                    </div>
                    <div>
                        <p class="jacp-helpcenter-title2 foot-font">
                            对接&合作
                        </p>
                        <p class="jacp-helpcenter-tips3 foot-font2">
                            <!-- <span>何思键：</span>
                            <span><EMAIL></span> -->
                            <span>京东行云</span>
                        </p>
                        <!-- <p class="jacp-helpcenter-tips3 foot-font2">
                            <span>朱越：</span>
                            <span><EMAIL></span>
                        </p> -->
                    </div>
                </div>
                <div class="jacp-helpcenter-copy">
                    <span>{{ copyright }}</span>
                    <span>{{ powered }}</span>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import GlobalSetting from '@/models/globalSetting';

export default {
    name: 'LayoutHelpcenterIndex',
    data() {
        return {
            copyright: process.env.VUE_APP_COPYRIGHT,
            powered: process.env.VUE_APP_POWERED,
            supportGroup: GlobalSetting.getSupportGroup(),
            jagile: {
                index: '/myzone',
            },
            guide: {
                createDemand: '/#/modules/demand?id=创建一条需求',
                createSprint: '/#/modules/space?id=创建一条迭代',
                createCard: '/#/modules/space?id=为迭代添加卡片',
                createSpace: '/#/modules/space?id=创建团队空间',
                workspace: '/#/modules/dashboard',
                createReport: '/#/modules/labor_hour',
                project: '/#/modules/project_management',
            },
            document: {
                demand: '/#/modules/demand',
                teamspace: '/#/modules/space',
                datacenter: '/#/modules/datacenter',
                more: '/#/about',
            },
            FAQ: {
                q1: '/#/faq?id=faq1',
                q2: '/#/faq?id=faq2',
                q3: '/#/faq?id=faq3',
                q4: '/#/faq?id=faq4',
                q5: '/#/modules/space?id=设置团队空间',
                q6: '/#/modules/space?id=设置团队空间',
                more: '/#/faq',
            },
            version: {
                v1: '/#/release?id=v170',
                v2: '/#/release?id=v165',
                v3: '/#/release?id=v160',
                more: '/#/release',
            },
        };
    },
    methods: {
        linkHandler(type, target) {
            const url = this[type][target];
            if (url) {
                if (type === 'document' && target === 'coding') {
                    window.open(`${this.$store.state.url.coding}${url}`, '_blank');
                } else if (type === 'document' && target === 'pipeline') {
                    window.open(`${this.$store.state.url.pipeline}${url}`, '_blank');
                } else if (url.indexOf('http') > -1) {
                    window.open(`${url}`, '_blank');
                } else if (type === 'jagile') {
                    window.open(`${this.$store.state.url.devops}${url}`, '_blank');
                } else {
                    window.open(`${this.$store.state.url.help}${url}`, '_blank');
                }
            }
        },
    },
};
</script>
<style lang="less">
.jacp-helpcenter{
  &-header{
    height: 360px;
    background-image:
      url(../../assets/images/helpcenter-logo.png),
      url(../../assets/images/helpcenter-banner.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 720px 128px, 2560px 643px;
    cursor: pointer;
  }
  & section{
    font-size: 0px;
    &:nth-of-type(2n){
      background-color: #F7F9FA;
    }
    &:nth-of-type(1) {
      height: 500px;
    }
    &:nth-of-type(2) {
      height: 460px;
    }
    &:nth-of-type(3) {
      height: 480px;
    }
    &:nth-of-type(4) {
      height: 400px;
    }
    &>div{
      width: 1112px;
      margin: 0 auto;
      padding: 32px 0 16px;
    }
  }
  &-title{
    margin: 0;
    margin-bottom: 4px;
    font-size: 24px;
    color: #333;
    font-weight: 999;
    line-height: 40px;
    text-align: center;
  }
  &-tips{
    margin: 0;
    color: #999;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 32px;
    text-align: center;
  }
  &-title2{
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: 999;
    line-height: 26px;
    text-align: center;
  }
  &-tips2{
    margin: 0;
    color: #999;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
  }
  &-tips3{
    margin: 0;
    color: #333;
    font-size: 14px;
    line-height: 24px;
    text-align: left;
  }
  &-box{
    display: flex;
    justify-content: space-between;
  }
  &-item2{
    width: 360px;
    height: 240px;
    border-radius: 8px;
    box-shadow: 0px 12px 24px 0px rgba(0,91,166,0.1);
    background-color: #fff;
    cursor: pointer;
    &:hover{
      box-shadow: 0px 8px 16px 5px rgba(38,149,241,0.3);
    }
    & div{
      height: 172px;
      background-position: center;
      background-repeat: no-repeat;
      background-size: 160px 120px;
      &.demand-management{
        background-image: url(../../assets/images/<EMAIL>);
      }
      &.teamspace{
        background-image: url(../../assets/images/<EMAIL>);
      }
      &.datacenter{
        background-image: url(../../assets/images/<EMAIL>);
      }
    }
  }
  &-item3{
    width: 360px;
    height: 276px;
    &>div{
      overflow: hidden;
      cursor: pointer;
      height: 204px;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0px 12px 24px 0px rgba(0,91,166,0.1);
      &:hover{
        box-shadow: 0px 8px 16px 5px rgba(38,149,241,0.3);
      }
      &>div{
        height: 160px;
        background-position: center;
        background-repeat: no-repeat;
        background-size: 360px 160px;
        &.demand{
          background-image: url(../../assets/images/<EMAIL>);
        }
        &.space{
          background-image: url(../../assets/images/<EMAIL>);
        }
        &.org{
          background-image: url(../../assets/images/<EMAIL>);
        }
      }
      &>p{
        padding: 8px 16px;
      }
    }
    & .jacp-helpcenter-tips3{
      cursor: pointer;
    }
  }
  &-item4{
    width: 360px;
    height: 200px;
    padding: 70px 0;
    border-radius: 8px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url(../../assets/images/<EMAIL>);
    box-shadow: 0px 12px 24px 0px rgba(0,91,166,0.1);
    cursor: pointer;
    &:hover{
      box-shadow: 0px 8px 16px 5px rgba(38,149,241,0.3);
    }
    & .jacp-helpcenter-title {
      color: white;
    }
    & .jacp-helpcenter-tips {
      color: white;
    }
  }
  &-item1{
    width: 33%;
    height: 160px;
    display: inline-block;
    margin-bottom: 16px;
    cursor: pointer;
    & div{
      margin: 0 auto;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      box-shadow: 0px 8px 16px 5px rgba(38,149,241,0.1);
      background-position: center;
      background-repeat: no-repeat;
      background-size: 120px 120px;
      &:hover{
        box-shadow: 0px 8px 16px 5px rgba(38,149,241,0.3);
      }
      &.card{
        background-image: url(../../assets/images/<EMAIL>);
        &:hover{
          background-image: url(../../assets/images/<EMAIL>);
        }
      }
      &.demand{
        background-image: url(../../assets/images/<EMAIL>);
        &:hover{
          background-image: url(../../assets/images/<EMAIL>);
        }
      }
      &.sprint{
        background-image: url(../../assets/images/<EMAIL>);
        &:hover{
          background-image: url(../../assets/images/<EMAIL>);
        }
      }
      &.space{
        background-image: url(../../assets/images/<EMAIL>);
        &:hover{
          background-image: url(../../assets/images/<EMAIL>);
        }
      }
      &.report{
        background-image: url(../../assets/images/<EMAIL>);
        &:hover{
          background-image: url(../../assets/images/<EMAIL>);
        }
      }
      &.workspace{
        background-image: url(../../assets/images/<EMAIL>);
        &:hover{
          background-image: url(../../assets/images/<EMAIL>);
        }
      }
    }
    & p{
      margin: 12px 0 0;
      font-size: 16px;
      color: #333;
      line-height: 22px;
      text-align: center;
    }
  }
  &-more{
    margin: 0;
    margin-top: 32px;
    display: flex;
    width: 100%;
    cursor: pointer;
    & a{
      font-size: 16px;
      height: 22px;
      margin: auto;
      color: #2695F1;
      display: flex;
      align-items: center;
      &:hover{
        color: #05b7ff;
      }
      & i{
        margin-left: 4px;
      }
    }
  }
  &-footer{
    height: 480px;
    &__top{
      height: 250px;
      background: url(../../assets/images/footer-bg.png) no-repeat;
      background-position: center;
      background-color: #5472FF;
      background-size: 2560px 700px;
      padding-top: 50px;
      & div{
        height: 150px;
        width: 650px;
        margin: 0 auto;
        & .jacp-helpcenter-title {
          color: white;
          margin-bottom: 12px;
        }
        & .jacp-helpcenter-tips {
          color: white;
          font-size: 14px;
          margin-bottom: 12px;
        }
        & a{
          display: inline-block;
          margin-left: 250px;
          margin-top: 16px;
          width: 150px;
          height: 40px;
          text-align: center;
          line-height: 40px;
          color: #fff;
          background: rgba(249,100,0,1);
          border-radius: 8px;
          cursor: pointer;
          font-weight: 999;
          &:hover{
            background: rgba(255,164,34,1);
          };
        }
      }
    }
    &__bottom{
      height: 230px;
      background-color: #333B53;
    }
    & .foot-font{
      color: white;
      margin-bottom: 16px;
      text-align: left;
      opacity: .9;
    }
    & .foot-font2{
      color: white;
      opacity: .5;
      & span:first-child{
        display: inline-block;
        width: 70px;
      }
    }
  }
  &-help{
    height: 180px;
    width: 1128px;
    margin: 0 auto;
    padding: 48px 0;
    display: flex;
    justify-content: space-between;
    & div{
      height: 84px;
      width: 190px;
      &:first-child{
        width: 500px;
      }
      & img{
        opacity: .9;
        width: 90px;
        height: 22px;
        margin-bottom: 16px;
      }
    }
  }
  &-copy{
    height: 48px;
    width: 1128px;
    margin: 0 auto;
    border-top: 1px solid #fff;
    opacity: 0.2;
    span {
        line-height: 48px;
        font-size: 12px;
        color: #fff;
        float: right;
        &:first-child{
          float: left;
        }
    }
  }
}
</style>
