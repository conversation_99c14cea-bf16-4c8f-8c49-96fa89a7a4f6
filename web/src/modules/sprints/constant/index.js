import { Enum } from '@/plugins/enum';

// 0：全部 1:进行中 2:已归档
export const SprintTypeEnum = new Enum([
    // i18n
    { code: 0, value: 'all', name: '全部' },
    { code: 1, value: 'unarchived', name: '活跃迭代' },
    { code: 2, value: 'archived', name: '已归档' },
]);
export const SprintType = SprintTypeEnum.kvMap;

// 10=未开始，20=进行中，30=已归档
/*
export const SprintStatus = {
    unstarted: 10,
    active: 20,
    archived: 30,
};
 */
// TODO: COLor
export const SprintStatusEnum = new Enum([
    {
        code: 10, value: 'unstarted', name: '未开始', icon: 'jacp-icon-todo', color: '#2695F1',
    },
    {
        code: 20, value: 'active', name: '进行中', icon: 'jacp-icon-doing', color: '#FF8D00',
    },
    {
        code: 30, value: 'archived', name: '已归档', icon: 'jacp-icon-done', color: '#0ED396',
    },
]);


export const SprintStatus = SprintStatusEnum.kvMap;
