import http, { spaceHttp } from '@/plugins/http';
import moment from 'moment';

import { initProperties } from '@/plugins/utils';
import { SprintTypeEnum, SprintType, SprintStatus } from '$module/constant';
import generateActions, { sprintActions } from '$module/models/sprintAction';

const DateFormat = 'MM月DD日';
const DEFAULT = [
    ['id'],
    ['name', ''],
    ['spaceId'],
    ['archived', 0],
    ['cTime', null],
    ['uTime', null],
    ['startDate', null],
    ['endDate', null],
    ['deleted', 0],
    ['desc', ''],
    ['status', SprintStatus.active],
];
export class Sprint {
    constructor(values = {}) {
        const self = this;
        initProperties(self, DEFAULT, values);
        return self;
    }

    get startDateString() {
        return moment(this.startDate).format(DateFormat);
    }

    get endDateString() {
        return moment(this.endDate).format(DateFormat);
    }
}

export default class SpaceSprint extends Sprint {
    #actions = [];

    constructor({ cardsCount = 0, finishedCardsCount = 0, ...props }) {
        super(props);
        this.cardsCount = +cardsCount;
        this.finishedCardsCount = +finishedCardsCount;
    }

    // 卡片完成进度
    get progress() {
        if (this.cardsCount === 0 || this.finishedCardsCount === 0) return 0;
        return (this.finishedCardsCount / this.cardsCount) * 100;
    }

    get unFinishedCardsCount() {
        return this.cardsCount - this.finishedCardsCount;
    }

    set actions(val) {
        this.#actions = val;
    }

    get actions() {
        return this.#actions;
    }

    get sprintType() {
        return this.archived ? SprintTypeEnum.archived.value
            : SprintTypeEnum.unarchived.value;
    }

    update(params = {}, spaceId) {
        return http.useSpaceId({ spaceId }).put('v1/spaces/sprints', {
            ...this,
            ...params,
        }).then(() => {
            Object.assign(this, params);
            return this;
        });
    }

    archive(type) {
        return http.put('v1/spaces/sprints/archive', {
            sprintId: this.id,
            type,
        }).then(() => {
            this.archived = SprintType.archived;
            return this;
        });
    }

    remove(type) {
        return http.delete('v1/spaces/sprints', {
            data: {
                sprintId: this.id,
                type,
            },
        }).then(() => {
            this.deleted = 1;
            return this;
        });
    }

    generateActions(privilage = {}) {
        // TODO: remove ？
        if (this.id === -1) {
            this.actions = [];
            return;
        }
        const avaliActions = sprintActions[this.sprintType];
        this.actions = generateActions(privilage, avaliActions, this);
        // return this.actions;
    }

    batchAddition(cardIdList) {
        return spaceHttp.put('v1/bizSpaceCard/batch/sprint', {
            cardIdList,
            spaceId: this.spaceId,
            sprintId: this.id,
        }).then((count = 0) => {
            this.cardsCount += count;
            return count;
        });
        // this.cardsCount += count;
        // TODO: 如果批量里包括了已完成的卡片，那已完成的数量就会不对
    }

    revertBatchAddition(cards = [], originSprintId = -1) {
        return spaceHttp.put('v1/bizSpaceCard/batch/rollback/sprint', {
            sprintId: originSprintId,
            spaceId: this.spaceId,
            cards: cards.map(card => ({
                id: card.id,
                index: card.index,
            })),
        }).then((count = 0) => {
            this.cardsCount -= cards.length;
            return count;
        });
    }

    // 带query的接口有聚合了卡片的参数，不带query的只有迭代信息
    static loadSprints({ queryType = SprintType.unarchived } = {}, spaceId) {
        return http.useSpaceId({ spaceId }).get('v1/spaces/sprints/query', {
            params: {
                queryType,
            },
        }).then((data = []) => data
            .map(sprint => new SpaceSprint(sprint)));
    }

    static create(data, spaceId) {
        return http.useSpaceId({ spaceId }).post('v1/spaces/sprints', data);
    }

    static getSprintDetail(sprintId) {
        return http.get(`v1/spaces/sprints/sprint/${sprintId}`).then(data => new SpaceSprint(data));
    }
}

export const backlogPlan = new SpaceSprint({
    id: -1,
    name: 'Backlog',
});
