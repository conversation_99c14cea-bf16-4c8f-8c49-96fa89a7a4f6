import i18n from '$platform.i18n';
import Dialog from '@/models/dialog';
import router from '$platform.router';
import { event } from '@/plugins/event';
import SpaceSprint from '$module/models/sprint';
// 燃尽图
import BurndownChart from '@/modules/sprints/components/burndownChart';
import newPlanDialog from '$module/components/dialog/dialog.newPlan';
import movePlan from '$module/components/dialog/dialog.movePlan';
import delPlan from '$module/components/dialog/dialog.delPlan';

export const sprintActions = {
    unarchived: ['updateSprint', 'archiveSprint', 'burndownChart', 'deleteSprint'],
    archived: [],
};
const emitEvent = cmd => (...arg) => event.$emit(`sprint:${cmd}`, ...arg);
export const sprintActionDefine = {
    createSprint: (cmd) => {
        const label = i18n.t('jacp.newPlan');
        const clickHandler = () => Dialog.confirm({
            width: '464px',
            title: i18n.t('jacp.newPlan'),
            confirmBtnText: i18n.t('jacp.button.submit'),
            slot: newPlanDialog,
            slotProps: {
                spaceKey: router.currentRoute.params.spaceKey,
            },
            beforeConfirm: vm => vm.validateData()
                .then(() => SpaceSprint.create(vm.data)).then(emitEvent(cmd)),
        });
        return {
            label,
            clickHandler,
        };
    },
    updateSprint: (cmd, sprint = {}) => ({
        label: i18n.t('jacp.button.edit'),
        clickHandler() {
            return Dialog.confirm({
                width: '464px',
                title: i18n.t('jacp.editPlan'),
                confirmBtnText: i18n.t('jacp.button.submit'),
                slot: newPlanDialog,
                slotProps: {
                    originData: sprint,
                    spaceKey: router.currentRoute.params.spaceKey,
                },
                beforeConfirm: vm => vm.validateData()
                    .then(() => sprint.update(vm.data)).then(emitEvent(cmd)),
            });
        },
    }),
    archiveSprint: (cmd, sprint = {}) => ({
        label: i18n.t('jacp.button.archive'),
        clickHandler() {
            return Dialog.confirm({
                title: `${i18n.t('jacp.archivePlan')} ${sprint.name}`,
                confirmBtnText: i18n.t('jacp.button.archive'),
                slot: movePlan,
                slotProps: {
                    originData: {
                        planId: sprint.id,
                        progressing: sprint.unFinishedCardsCount,
                        actionCode: 1,
                    },
                },
                beforeConfirm: vm => sprint.archive(+vm.data.type)
                    .then(() => {
                        emitEvent(cmd)({ sprint });
                    }),
            });
        },
    }),
    deleteSprint: (cmd, sprint = {}) => ({
        label: i18n.t('jacp.button.remove'),
        clickHandler() {
            return Dialog.confirm({
                title: `${i18n.t('jacp.delPlan')}`,
                confirmBtnText: '确认删除',
                slot: delPlan,
                slotProps: {
                    originData: {
                        planId: sprint.id,
                        progressing: sprint.unFinishedCardsCount,
                        sprintName: sprint.name,
                        actionCode: 2,
                    },
                },
                beforeConfirm: vm => sprint.remove(+vm.data.type)
                    .then(() => {
                        emitEvent(cmd)({ sprint });
                    }),
            });
        },
    }),
    burndownChart: (cmd, sprint = {}) => ({
        label: i18n.t(
            'jacp.sprint.burndownChartTitle',
            { sprintName: sprint.name },
        ),
        clickHandler() {
            return Dialog.alert({
                title: i18n.t('jacp.sprint.burndownChart', { sprintName: sprint.name }),
                slot: BurndownChart,
                slotProps: {
                    sprint,
                },
                showIcon: false,
            });
        },
    }),
};
export default function generateActions(privilage, availableAction = [], sprint) {
    return availableAction
        .filter(cmd => cmd in sprintActionDefine)
        .map((cmd) => {
            const disabled = cmd in privilage ? !privilage[cmd] : false;
            const action = sprintActionDefine[cmd](cmd, sprint);
            return {
                disabled,
                cmd,
                ...action,
            };
        });
}
