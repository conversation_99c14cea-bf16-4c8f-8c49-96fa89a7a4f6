<script>
import Vue from 'vue';
import Sidebar, { SidebarManager } from '@jacpbiz/sidebar';
import '@jacpbiz/sidebar/dist/sidebar.css';
import isFunction from 'lodash/isFunction';
import generateActions from '$module/models/sprintAction';
import { SprintType, SprintTypeEnum } from '$module/constant';
import Sprint from '$module/models/sprint';
import LocalSprintTab from '$module/components/sprintTab';

Vue.use(Sidebar, { prefix: 'Jacpbiz' });
function getLastSprintId(spaceKey) {
    let lastPlan;
    try {
        lastPlan = JSON.parse(localStorage.jacp_lastPlan);
    } catch (e) {
        lastPlan = {};
    }
    return lastPlan[spaceKey];
}
function setLastSprintId(spaceKey, sprintId) {
    const lastPlan = JSON.parse(localStorage.jacp_lastPlan || '{}');
    lastPlan[spaceKey] = sprintId;
    localStorage.jacp_lastPlan = JSON.stringify(lastPlan);
}
function filterDeleted() {
    return s => !s.deleted;
}
function filterType(sprintType) {
    return s => s?.sprintType === sprintType;
}
function filterKeyword(keyword = '') {
    return s => s?.name.toLowerCase().includes(keyword.toLowerCase());
}

const sprintStore = new Map();
const selectedSprintStore = new Map();
const backlogPlan = new Sprint({
    id: -1,
    name: 'Backlog',
});
export default {
    name: 'JacpSprints',
    components: {
        LocalSprintTab,
    },
    props: {
        theme: { type: String, default: 'gray' },
        title: { type: String, default: '迭代' },
        subTitle: { type: String, default: '' },
        layout: {
            type: String,
            default: 'searcher, tab',
        },
        // TODO: 用于搜索所有类型的时候
        searchScope: { type: String, default: SprintType.unarchive },
        sprintId: { type: [String, Number], default: '' },
        onChange: { type: Function, default: () => {} },
        onTabChange: { type: Function, default: () => {} },
        appendBacklog: { type: Boolean, default: false },
        asyncToStore: { type: Boolean, default: true },
        focusedOnClick: { type: Boolean, default: true },
        highlight: { type: Boolean, default: false },
    },
    data() {
        return {
            keyword: '',
            sprintManager: {},
            onCreate: () => {},
            searcherVisible: false,
            sprints: [],
            activeTab: SprintTypeEnum.unarchived.value,
            event: this.$initEvent(),
        };
    },
    created() {
        const updateIndex = ({ sprint = {} }) => {
            if (sprint.id === this.activedIndex) {
                const [firstSprint] = this.filteredSprints;
                if (firstSprint) {
                    this.activedIndex = firstSprint.id;
                } else {
                    this.activedIndex = '';
                }
            }
        };
        // 新建迭代后仅刷新
        this.event.$on('sprint:createSprint', async (sprintId) => {
            if (this.activeTab === SprintTypeEnum.unarchived.value) {
                this.reload().then(() => {
                    this.activedIndex = sprintId;
                    this.focus(sprintStore.get(sprintId), true);
                });
            }
        });
        // 列表中归档/删除该迭代 + 如果是当前选中迭代的话，同时还要更新卡片 +没有消息通知
        this.event.$on('sprint:archiveSprint', updateIndex);
        this.event.$on('sprint:deleteSprint', updateIndex);
        this.event.$on('sprint:update', (fn, sprintId) => this.updateSprint(fn, sprintId));
        this.event.$on('sprint:reload', this.reload);
    },
    methods: {
        init() {
            this.activeTab = SprintTypeEnum.unarchived.value;
            selectedSprintStore.clear();
            if (this.sprintId) {
                this.initActiveTab().then(this.reload);
            } else {
                this.reload();
            }
        },
        initActiveTab() {
            if (this.sprintId) {
                const lastSprint = sprintStore.get(+this.sprintId);
                // 如果url上的sprintId是归档的，要默认到归档的tab，并且高亮该迭代
                if (!lastSprint) {
                    return Sprint.getSprintDetail(this.sprintId).then((sprint = {}) => {
                        this.activeTab = sprint.sprintType;
                    });
                }
            }
            return Promise.resolve();
        },
        setActiveIndex() {
            if (this.sprintId) {
                return;
            }
            const defaultFirst = () => {
                if (this.sprints.length) {
                    const [firstSprint] = this.sprints;
                    this.activedIndex = +firstSprint.id;
                }
            };
            const lastSprintId = getLastSprintId(this.spaceKey);
            if (selectedSprintStore.has(this.activeTab)) {
                this.activedIndex = selectedSprintStore.get(this.activeTab).id;
                return;
            }
            if (lastSprintId) {
                // 取出本地缓存，与当前所处的tab进行对比
                const lastSprint = sprintStore.get(lastSprintId);
                if (!lastSprint || (lastSprint && lastSprint.sprintType !== this.activeTab)) {
                    defaultFirst();
                    return;
                }

                this.activedIndex = +lastSprintId;
                return;
            }
            defaultFirst();
        },
        reload() {
            const queryType = SprintType[this.activeTab];
            return Sprint.loadSprints({ queryType }).then((data) => {
                this.sprints = this.appendBacklog ? data.concat(backlogPlan) : data;
                if (this.asyncToStore) {
                    this.$store.commit('teamspace/updateState', {
                        planList: [...this.sprints],
                    });
                }

                this.$nextTick(() => {
                    this.setActiveIndex();
                    // setTimeout(() => {
                    this.scrollToSprint(this.activedIndex);
                    // }, 1);
                });
            });
        },
        restoreSprints() {
            this.sprints.forEach((sprint) => {
                sprint.generateActions(this.privilage);
                sprintStore.set(sprint.id, sprint);
            });
        },
        focus(sprint = {}, scroll = false) {
            // this.activedIndex = sprint.id;
            if (sprint.sprintType === SprintTypeEnum.unarchived.value) {
                // 点击活跃迭代的时候才会计入本地缓存
                setLastSprintId(this.spaceKey, sprint.id);
            }
            selectedSprintStore.set(this.activeTab, sprint);
            if (scroll) {
                this.scrollToSprint(sprint.id);
            }
        },
        scrollToSprint(sprintId) {
            this.$nextTick(function scrollIntoView() {
                const curEl = this.$el.querySelector(`div[data-item='${sprintId}']`);
                if (curEl) {
                    curEl.scrollIntoView({
                        behavior: 'instant',
                        block: 'nearest', // 不能用center，center会使页面整体上移
                    });
                }
            });
        },
        handleTabClick() {
            const onTabChange = isFunction(this.onTabChange) ? this.onTabChange : () => {};
            Promise.resolve(onTabChange(this.activeTab)).then(() => {
                this.reload();
                this.$emit('tab-click', this.activeTab);
            });
        },
        handleSprintClick(ev, sprint = {}) {
            this.$emit('sprint-click', sprint);
            if (this.focusedOnClick) {
                this.focus(sprint);
            } else {
                ev.stopPropagation();
            }
        },
        // public method
        updateSprint(updateFn = () => {}, sprintId) {
            const targetSprintId = sprintId || this.activedIndex;
            if (isFunction(updateFn)) {
                const oldSprint = sprintStore.get(targetSprintId);
                const newSprint = updateFn(sprintStore.get(targetSprintId));
                if (oldSprint && newSprint) {
                    sprintStore.set(targetSprintId, Object.assign(oldSprint, newSprint));
                }

                if (targetSprintId === this.activedIndex && this.asyncToStore) {
                    this.$nextTick(() => {
                        this.$store.dispatch('plan/plan_update', {
                            activePlan: { ...this.activeSprint },
                        });
                    });
                }
            }
        },
    },

    computed: {
        activedIndex: {
            get() {
                return SidebarManager.activedIndex;
            },
            set(val) {
                SidebarManager.activedIndex = val;
            },
        },
        spaceKey() {
            return this.$route.params.spaceKey;
        },
        privilage() {
            return this.$store.state.chilldteamspace.spacePrivilage;
        },
        currentLayout() {
            return this.layout.split(',').map(item => item.trim());
        },
        activeSprint() {
            return this.sprints.find(s => s.id === this.activedIndex);
        },
        sprintCache() {
            this.sprints.forEach((sprint = {}) => {
                sprintStore.set(sprint.id, sprint);
            });
            return sprintStore;
        },
        filteredSprints() {
            return this.sprints.filter((s) => {
                if (!filterDeleted()(s)) return false;
                if (!filterKeyword(this.keyword)(s)) return false;
                if (!filterType(this.activeTab)(s)) return false;
                return true;
            });
        },
    },
    render() {
        const {
            theme,
            currentLayout, filteredSprints,
            title, subTitle, privilage, onCreate, activedIndex,
        } = this;
        const childTemplate = {
            // 搜索
            searcher: () => <el-input
                clearable
                size="mini"
                placeholder="请输入迭代名称"
                style={{
                    margin: '8px 24px',
                    width: 'calc(100% - 48px)',
                }}
                class="j-input--background"
                vModel={this.keyword}
            >
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>,
            // tab
            tab: () => <local-sprint-tab vModel={this.activeTab} onTabClick={this.handleTabClick}></local-sprint-tab>,
            // 进度条
            progress: sprint => (sprint.id !== -1 ? <el-tooltip
                open-delay={300}
                content={this.$t('jacp.sprint.progressTips', {
                    finished: sprint.finishedCardsCount,
                    total: sprint.cardsCount,
                    progress: Math.floor(sprint.progress),
                })}

                placement="bottom">
                <el-progress
                    class="j-mgt12 j-mgb4"
                    stroke-width={4}
                    show-text={false}
                    percentage={sprint.progress}
                ></el-progress></el-tooltip> : null),
        };
        const childrenComponents = [];
        currentLayout.forEach(com => childrenComponents.push(childTemplate[com]()));
        const contentStyle = {
            display: 'grid',
            'grid-template-rows': '40px auto',
            height: '100%',
        };
        return <div class="jacp-sprints">
            <JacpbizSidebar activedIndex={activedIndex} theme={theme}>
                {
                    <JacpbizSidebarGroup
                        style={contentStyle}
                        title={title}
                        canCollapsed={false}>
                        <el-tooltip
                            slot="extra"
                            content={this.$t('jacp.noPermission')}
                            placement="bottom"
                            disabled={privilage.createSprint}>
                            <i class="el-icon-plus"
                                role="button"
                                onClick={privilage.createSprint
                                    ? () => onCreate.clickHandler()
                                    : () => {}}></i></el-tooltip>
                        {subTitle ? <JacpbizSidebarBlock
                            disabled={true}
                        >{subTitle}</JacpbizSidebarBlock> : null}
                        { childrenComponents }
                        {
                            filteredSprints.length
                                ? <div class={`jacp-sprint-list ${this.highlight ? 'jacp-sprint-list--highlight' : ''}`}>
                                    { filteredSprints.map(sprint => <JacpbizSidebarBlock
                                        attrs={{
                                            'data-item': sprint.id,
                                        }}
                                        index={sprint.id}
                                        title={sprint.name}
                                        actions={sprint.actions}
                                        nativeOnClick={ev => this.handleSprintClick(ev, sprint)}
                                    >
                                        <div class="jacp-sprint-item" >
                                            { sprint.startDate ? <div style="line-height:18px;margin-top: 2px;">{ sprint.startDateString }- { sprint.endDateString }</div>
                                                : null}
                                            {
                                                this.$scopedSlots.sprint ? this.$scopedSlots.sprint({
                                                    sprint,
                                                }) : childTemplate.progress(sprint)
                                            }
                                        </div>
                                    </JacpbizSidebarBlock>) }

                                </div> : <jacp-empty
                                    placeholderImg={false}
                                    label="暂无迭代"
                                />
                        }
                    </JacpbizSidebarGroup>
                }
            </JacpbizSidebar>
        </div>;
    },
    watch: {
        privilage: {
            immediate: true,
            deep: true,
            handler() {
                [this.onCreate] = generateActions(this.privilage, ['createSprint']);
            },
        },
        sprints: {
            handler: 'restoreSprints',
        },
        activeSprint: {
            handler(val) {
                if (this.asyncToStore) {
                    this.$store.dispatch('plan/plan_update', {
                        activePlan: val ? { ...val } : {},
                    });
                }
            },
        },
        activedIndex: {
            immediate: true,
            handler(val, oldVal) {
                if (val !== oldVal && isFunction(this.onChange)) {
                    this.onChange(this.activeSprint);
                }
            },
        },
        spaceKey: {
            immediate: true,
            handler() {
                this.init();
            },
        },
        sprintId: {
            immediate: true,
            handler() {
                if (this.sprintId) {
                    this.activedIndex = +this.sprintId;
                }
            },
        },
        filteredSprints: {
            immediate: true,
            handler(val) {
                this.$emit('sprints-changed', val);
            },
        },
    },
    beforeDestroy() {
        sprintStore.clear();
        selectedSprintStore.clear();
    },
};
</script>
<style lang="less">
.teamspace-scrum__plans .jacpbiz-sidebar{
    background: transparent;
}
.jacp-sprints{
    padding-bottom: 0!important;
    .jacpbiz-sidebar-group-content{
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    .jacpbiz-sidebar-group-title{
        [role="button"] {
            font-size: 16px;
        }
    }
    .jacpbiz-sidebar-group-title__label{
        font-weight: 600;
    }
}
.jacp-sprint-list{
    margin-bottom: var(--gutter--large);
    overflow-y: auto;
    .jacpbiz-sidebar-block{
        border: 1px dashed transparent;
        margin-top: 2px;
        margin-bottom: 2px;
    }
    &--highlight{
         .jacpbiz-sidebar-block{
            border-bottom: 1px dashed var(--color--primary);
        }
    }
    // flex: 1;
    .jacpbiz-sidebar-block{
        padding: var(--gutter--medium) 0;
        position: relative;
        border-radius: var(--radius--medium);
        &:after{
            position: absolute;
            bottom: 0;
            content: ' ';
            display: block;
            height: 1px;
            background-color: var(--color--base--hr);
            width: calc(~'100% - 32px');
            left: var(--gutter--medium);
        }
    }
}
.jacp-sprint-item{
    .el-progress{
        cursor: pointer;
    }
    .el-progress .el-progress-bar__inner{
        opacity: 1;
        background-color: var(--color--primary--hover);
    }
    .el-progress .el-progress-bar__outer{
        background-color: var(--color--base--hr);
    }
}
.jacp-sprint-list{
    z-index: 1;
    padding-top: 2px;
    tr, td{
        display: none;
    }

}
.sprint-sortable-ghost{
    position: relative;
    background-color: var(--color--row--hover);
    cursor: copy!important;
}
</style>
