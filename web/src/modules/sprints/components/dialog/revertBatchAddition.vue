<template>
    <div class="revertBatchAddition">
        <span>{{ tips }}</span>
        <div>
            <el-button
                type="text"
                @click="revert"
            >
                <jacp-text
                    type="error"
                >
                    撤销({{ counter }})
                </jacp-text>
            </el-button>
            <el-button
                type="text"
                @click="clear"
            >
                我知道了
            </el-button>
        </div>
    </div>
</template>
<script>
import Sprint from '$module/models/sprint';
import i18n from '$platform.i18n';

export default {
    name: 'RevertBatchAddition',
    props: {
        sprint: {
            type: Sprint, default: () => ({}),
        },
        cards: {
            type: Array, default: () => [],
        },
    },
    data() {
        return {
            counter: 5, // 倒计时
        };
    },
    mounted() {
        this.timer = setInterval(() => {
            this.counter -= 1;
            if (this.counter <= 0) {
                this.clear();
            }
        }, 1000);
    },
    computed: {
        tips() {
            return i18n.t('jacp.sprint.revertTips', {
                name: this.sprint.name,
                n: this.cardcount,
            });
        },
        cardcount() {
            return this.cards.length;
        },
    },
    methods: {
        clear() {
            clearInterval(this.timer);
            this.$emit('close');
        },
        revert() {
            clearInterval(this.timer);
            if (this.defer) {
                return;
            }
            this.defer = this.sprint.revertBatchAddition(this.cards)
                .then(() => {
                    this.$emit('revert', {
                        sprint: this.sprint,
                        cards: this.cards,
                    });
                    this.$emit('close');
                    this.$message.success(i18n.t('jacp.sprint.revertSuccess'));
                });
        },
    },
    beforeDestroy() {
        clearInterval(this.timer);
        this.defer = undefined;
    },
};
</script>
