<template>
    <el-form
        ref="formIns"
        :model="data"
        :rules="rules"
        label-width="100px"
        label-position="top"
        class="plan-form-root j-form"
    >
        <el-form-item
            :label="fieldText.name"
            prop="name"
        >
            <el-input
                v-model="data.name"
                placeholder="可输入5-20个字"
                :maxlength="20"
                :minlength="5"
            />
        </el-form-item>
        <el-row
            type="flex"
            align="space-between"
        >
            <el-form-item
                :label="fieldText.startDate"
                prop="startDate"
                style="flex: 1; margin-right:16px;"
            >
                <el-date-picker
                    v-model="data.startDate"
                    type="date"
                    placeholder="选择日期"
                    value-format="timestamp"
                    :picker-options="pickerOptions.startDate"
                />
            </el-form-item>
            <el-form-item
                :label="fieldText.endDate"
                prop="endDate"
                style="flex: 1"
            >
                <el-date-picker
                    v-model="data.endDate"
                    type="date"
                    placeholder="选择日期"
                    value-format="timestamp"
                    :picker-options="pickerOptions.endDate"
                />
            </el-form-item>
        </el-row>
        <el-form-item
            :label="fieldText.status"
            prop="cardInitVersion"
        >
            <jacp-form-select
                v-model="data.status"
                option-value-key="code"
                :confirm="false"
                :options="sprintStatus"
            />
        </el-form-item>
        <el-form-item
            :label="fieldText.cardInitVersion"
            prop="cardInitVersion"
            v-if="hasSpaceVendor"
        >
            <jacp-input-select
                v-model="data.cardInitVersion"
                :data="versions"
                clearable
                :filterable="true"
                placeholder="请选择"
            />
        </el-form-item>
        <el-form-item
            :label="fieldText.description"
            prop="desc"
        >
            <el-input
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 6 }"
                v-model="data.desc"
                placeholder=""
            />
        </el-form-item>
    </el-form>
</template>

<script type="text/javascript">
import SpaceModel from '@/models/space';
import VendorModel from '@/models/vendor';
import { SprintStatusEnum, SprintStatus } from '@/modules/sprints/constant';
// import SpaceSprint from '$module/models/sprint';

function dateValidator(rule, value, callback) {
    if (!value) {
        callback(new Error(rule.message));
    } else {
        callback();
    }
}

export default {
    props: {
        originData: {
            type: Object,
            default: () => ({}),
        },
        spaceKey: {
            type: String,
        },
    },
    data() {
        return {
            mode: this.originData.id ? 'edit' : 'create',
            data: {
                id: undefined,
                name: '',
                startDate: undefined,
                endDate: undefined,
                desc: '',
                cardInitVersion: '',
                status: SprintStatus.active,
            },
            pickerOptions: {
                startDate: {
                    disabledDate: cur => this.data.endDate && cur > this.data.endDate,
                },
                endDate: {
                    disabledDate: cur => this.data.startDate && cur < this.data.startDate,
                },
            },
            rules: {
                name: [{
                    required: true,
                    message: '名称不能为空',
                    trigger: 'change',
                }, {
                    min: 5,
                    max: 20,
                    message: '长度在5 - 20个字',
                    trigger: 'change',
                }],
                startDate: [{
                    required: true,
                    message: '请设置开始日期',
                    validator: dateValidator,
                    trigger: 'change',
                }],
                endDate: [{
                    required: true,
                    message: '请设置结束日期',
                    validator: dateValidator,
                    trigger: 'change',
                }],
                desc: [{
                    max: 150,
                    message: '限150个字',
                    trigger: 'change',
                }],
            },
            hasSpaceVendor: false,
            versions: [],
            sprintStatus: SprintStatusEnum.list.filter(status => status.code !== SprintStatus.archived),
        };
    },
    created() {
        Object.assign(this.data, this.originData);
        /* eslint-disable */
        SpaceModel.getSpaceVendorConfig(this.spaceKey).then((data) => {
            if (data.length > 0 && data[0].vendorName === 'vms') {
                this.hasSpaceVendor = true;
            }
            if (this.hasSpaceVendor) {
                VendorModel.getVendorVersions(data[0].vendorName).then((versions) => {
                    this.versions = versions;
                });
            }
        });
    },
    methods: {
        validateData() {
            this.data['cardInitVersion'] = this.getVersionName(this.data.cardInitVersion);
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject();
                    }
                });
            });
        },
        getVersionName(versionId) {
            const v = this.versions.filter(item => item.id === versionId).pop();
            if (v) {
                return v.name;
            }
            return null;
        },
    },
    computed: {
        fieldText() {
            return {
                name: this.$t('jacp.sprint.props.name'),
                description: this.$t('jacp.sprint.props.description'),
                cardInitVersion: this.$t('jacp.sprint.props.cardInitVersion'),
                startDate: this.$t('jacp.startDate'),
                endDate: this.$t('jacp.endDate'),
                status: this.$t('jacp.sprint.props.status')
            };
        },
    },
};
</script>
