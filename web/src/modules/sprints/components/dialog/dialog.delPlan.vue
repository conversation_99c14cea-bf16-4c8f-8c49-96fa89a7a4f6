<template>
    <div class="plan-del-root">
        <p class="dlg-title">
            确认删除迭代 <span class="dlg-title-sprint">{{ originData.sprintName }}</span> 吗？
        </p>
        <p>
            迭代删除后不可恢复，迭代内的所有卡片将被转移至backlog中。
        </p>
    </div>
</template>

<script type="text/javascript">

export default {
    props: {
        originData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            data: {
                type: '1',
            },
        };
    },
};
</script>


<style lang="less">
  .plan-del-root{
    font-size: 14px;
    &-radio {
      margin-left: 0px;
      display:block;
      font-size: 12px;
    }
    &-row {
      margin-top: 10px;
    }
    .dlg-title {
        font-size: 20px;
        font-weight: bold;
        &-sprint {
            color: royalblue;
        }
    }
  }
</style>
