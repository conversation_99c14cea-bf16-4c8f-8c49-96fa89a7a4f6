<template>
    <div class="plan-move-root">
        <p>{{ dynamicHints }}</p>
        <el-radio-group
            v-model="data.type"
            v-show="remainingCount && remainingCount>0"
        >
            <el-radio
                class="plan-move-root-radio"
                size="small"
                label="2"
            >
                {{ this.fieldText.ignoreIntoHistory }}
            </el-radio>
            <el-row class="plan-move-root-row" />
            <el-radio
                class="plan-move-root-radio"
                size="small"
                label="1"
            >
                {{ this.fieldText.moveToBacklog }}
            </el-radio>
        </el-radio-group>
    </div>
</template>

<script type="text/javascript">
import { plan as PlanModel } from '@/models/teamspace';

export default {
    props: {
        originData: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            remainingCount: '',
            data: {
                type: '0',
            },
        };
    },
    created() {
        this.remainingCount = this.originData.progressing;
        this.$$dialog = this.$parent.$parent;
        this.$$dialog.disableOk = true; // 禁用弹框的确认按钮
        if (this.remainingCount < 1) {
            // 无数据默认勾选删除
            this.data.type = '1';
        }
    },
    methods: {
        updateData(action, data) {
            PlanModel.update(action, data);
        },
    },
    computed: {
        fieldText() {
            return {
                moveToBacklog: this.$t('jacp.sprint.moveToSprint'),
                ignoreIntoHistory: this.$t('jacp.sprint.ignoreIntoHistory'),
                ignoreIntoDel: this.$t('jacp.sprint.ignoreIntoDel'),
            };
        },
        dynamicHints() {
            let hintTemplate;
            // 根据数据是否为空 返回不同提示信息
            if (this.remainingCount && this.remainingCount > 0) {
                hintTemplate = this.$t('jacp.sprint.hints', {
                    nItem: this.remainingCount,
                });
            } else {
                hintTemplate = this.$t('jacp.sprint.hintsForNull');
            }
            return hintTemplate;
        },
        isValid() {
            return this.data.type === '1' || this.data.type === '2';
        },
    },
    watch: {
        isValid() {
            this.$$dialog.disableOk = !this.isValid;
        },
    },
};
</script>


<style lang="less">
  .plan-move-root{
    font-size: 14px;
    &-radio {
      margin-left: 0px;
      display:block;
      font-size: 12px;
    }
    &-row {
      margin-top: 10px;
    }
  }
</style>
