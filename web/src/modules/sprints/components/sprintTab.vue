<template>
    <el-tabs
        v-bind="$attrs"
        v-on="$listeners"
        @tab-click="(ev) => $listeners.tabClick
            ? $listeners.tabClick(ev)
            : () => {}"
        class="jacp-tabs jacp-tabs--gray jacp-sprint-tab"
    >
        <el-tab-pane
            v-for="tab in tabs"
            :key="tab.key"
            :label="tab.label"
            :name="tab.key"
            :ref="tab.key"
        />
    </el-tabs>
</template>
<script>
import { SprintTypeEnum } from '$module/constant';
// TODO: 以后还要变
export default {
    name: 'JacpSprintTab',
    inheritAttrs: false,
    data() {
        return {
            tabs: [{
                key: SprintTypeEnum.unarchived.value,
                label: this.$t('jacp.sprint.activeSprintTab'),

            }, {
                key: SprintTypeEnum.archived.value,
                label: this.$t('jacp.sprint.historySprintTab'),
            }],
        };
    },
};
</script>
<style lang="less">
.jacp-sprint-tab{
  padding-left: var(--gutter--large);
  &:after{
    display: block;
    content: " ";
    height: 1px;
    width: calc(~'100% + 48px');
    background-color: var(--color--base--hr);
    transform: translateX(-24px);
  }
}
</style>
