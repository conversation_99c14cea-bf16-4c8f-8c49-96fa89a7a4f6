<template>
    <div :class="scopedClass.cn">
        <div :class="scopedClass.el('status').cn">
            <i
                :style="`color: ${getSprintStatus('color')}`"
                :class="getSprintStatus('icon')"
            />
            <jacp-text
                class="j-mgl4"
                type="default"
                size="12"
            >
                {{ getSprintStatus('name') }}
            </jacp-text>
        </div>
        <span :class="scopedClass.el('cardsCount').cn">
            <jacp-text
                type="disable"
                size="12"
            >卡片数量</jacp-text>
            <jacp-text
                class="j-mgl4"
                type="default"
                size="12"
            >{{ sprint.cardsCount }}</jacp-text>
        </span>
        <transition
            name="jacp-bounce"
            @after-enter="afterEnter"
            @after-leave="afterLeave"
        >
            <div
                :class="scopedClass.el('counter').cn"
                v-show="show"
            >
                +{{ counter }}
            </div>
        </transition>
    </div>
</template>
<script>
import Sprint from '$module/models/sprint';
import { SprintStatusEnum } from '@/modules/sprints/constant';
import { getEnumByProp } from '@/plugins/enum';
import RevertBatchAddition from '$module/components/dialog/revertBatchAddition';

const getSprintStatus = getEnumByProp(SprintStatusEnum);

export default {
    name: 'JacpSprintBlockInner',
    props: {
        sprint: { type: Sprint, default: () => ({}) },
        canRevert: { type: Boolean, default: true },
    },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-sprint-block-inner'),
            counter: 0,
            show: false,
        };
    },
    methods: {
        getSprintStatus(key) {
            return getSprintStatus(this.sprint.status, key);
        },
        increase(cards = []) {
            // if (!cards.length) return;

            this.counter = cards.length;
            this.show = true;
            return this.sprint.batchAddition(cards.map(card => card.id)).then(() => {
                if (this.canRevert) {
                    this.showRevert(cards);
                }
                // console.log('increase done', cards);
            });
        },
        afterEnter() {
            this.show = false;
        },
        afterLeave() {
            this.counter = 0;
        },
        afterRevert($event) {
            this.$emit('after-revert', $event);
        },
        showRevert(cards) {
            const notify = this.$notify({
                title: '已成功！',
                type: 'success',
                dangerouslyUseHTMLString: true,
                message: <RevertBatchAddition
                    sprint={this.sprint}
                    cards={cards}
                    onRevert={$event => this.afterRevert($event)}
                    onClose={() => notify.close()}></RevertBatchAddition>,
                duration: 0,
            });
        },
    },
};
</script>
<style lang="less">
.jacp-sprint-block-inner{
  margin-top: var(--gutter--small);
  color: var(--color--regular--content);
  &, &__status{
      display: flex;
      justify-content: flex-start;
      align-items: center;
  }
  &__cardsCount, &__counter{
    margin-left: var(--gutter--medium);
    font-size: var(--font-size--description);
  }
  &__counter{
    color: var(--color--regular--content);
    font-weight: bold;
  }
}


</style>
