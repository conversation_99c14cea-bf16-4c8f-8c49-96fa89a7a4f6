<script>
import Sprint from '$module/models/sprint';
import { SprintStatusEnum } from '@/modules/sprints/constant';
import { getEnumByProp } from '@/plugins/enum';

const getSprintStatus = getEnumByProp(SprintStatusEnum);
export default {
    name: 'JacpSprintInlineItem',
    props: {
        sprint: { type: [Sprint, Object], default: () => ({}) },
        layout: { type: Array, default: () => ['status', 'name', 'time'] },
    },
    data() {
        return {
            scopedClass: this.$scopedClass('jacp-sprint-inline-item'),
        };
    },
    methods: {
        getSprintStatus(key) {
            return getSprintStatus(this.sprint.status, key);
        },
    },
    render() {
        const timeFormat = this.$options.filters['jacp-local-time'];
        const { sprint, layout } = this;
        const childTemplate = {
            status: () => <el-tag
                size="mini"
                effect="plain"
                style={{
                    color: this.getSprintStatus('color'),
                    borderColor: this.getSprintStatus('color'),
                }}>{this.getSprintStatus('name')}
            </el-tag>,
            name: () => <jacp-text style="flex: 1">{sprint.name}</jacp-text>,
            time: () => <jacp-text size="12" type="disable">{
                `${timeFormat(sprint.startDate, 'YYYY-MM-DD')} - ${timeFormat(sprint.endDate, 'YYYY-MM-DD')}`}
            </jacp-text>,
        };
        return <div class={this.scopedClass.cn}>
            {layout.map(prop => childTemplate[prop]())}
        </div>;
    },
};
</script>
<style lang="less">
.jacp-sprint-inline-item{
  display:flex;
  align-items:center;
  justify-content: space-between;
  gap: var(--gutter--small);
}
</style>
