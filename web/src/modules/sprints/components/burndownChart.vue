<template>
    <layout-block
        :title="title"
        :height="height"
        :display-fullscreen="displayFullscreen"
    >
        <template v-if="!empty">
            <div slot="extra">
                <el-radio-group
                    v-model="type"
                    v-show="checkType === 'radio'"
                    style="margin-bottom: 20px"
                >
                    <el-radio
                        v-for="item in burnDataType"
                        :key="item.id"
                        :label="item.id"
                    >
                        {{ item.name }}
                    </el-radio>
                </el-radio-group>
                <jacp-dropdown
                    :data="burnDataType"
                    v-model="type"
                    v-show="checkType === 'dropdown'"
                    style="position: relative; top: -27px; margin-left: 60px;"
                />
                <el-switch
                    v-model="includeHoliday"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                    active-text="含节假日"
                    style="margin-left: 60px; margin-top: -20px"
                />
            </div>
            <ci-highchart
                :option="options"
                :data="burndownData"
            />
        </template>
        <template v-else>
            <jacp-empty position="left" />
        </template>
    </layout-block>
</template>
<script>
import PlanModel from '@/models/plan';
import CardModel from '@/models/card';

const burnDataType = [
    {
        id: 'errortsData',
        name: '按剩余工时',
    },
    {
        id: 'storysData',
        name: '按故事点',
    },
    {
        id: 'tasksData',
        name: '按剩余任务数',
    },
];

const standardConfig = {
    name: '参考线',
    color: '#50B7FF',
    marker: {
        radius: 1,
        symbol: 'circle',
    },
};
const options = {
    chart: {
        type: 'spline',
        zoomType: 'x',
    },
    title: {
        text: '',
    },
    tooltip: {
        xDateFormat: '%Y-%m-%d',
        crosshairs: true,
        shared: true,
    },
    credits: {
        enabled: false,
    },
    plotOptions: {
        spline: {
            marker: {
                radius: 2,
                lineColor: '#666666',
                lineWidth: 1,
            },
        },
    },
    series: [],
    lang: {
        contextButtonTitle: '图表导出菜单',
        downloadJPEG: '下载 JPEG 图片',
        downloadPDF: '下载 PDF 文件',
        downloadPNG: '下载 PNG 文件',
        downloadSVG: '下载 SVG 文件',
        printChart: '打印图表',
    },
    exporting: {
        enabled: true,
        buttons: {
            contextButton: {
                menuItems: ['printChart',
                    'separator',
                    'downloadPNG',
                    'downloadJPEG',
                    'downloadSVG'],
            },
        },
    },
};
export default {
    props: {
        title: {
            type: String,
            default: '',
        },
        displayFullscreen: {
            type: Boolean,
            default: true,
        },
        height: Number,
        sprint: {
            type: Object,
            default: () => ({}),
        },
        checkType: {
            type: String,
            default: 'radio',
        },
        exporting: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            type: 'errortsData',
            options,
            result: [],
            xAxis: {},
            burnDataType,
            includeHoliday: true,
            workDays: [],
        };
    },
    mounted() {
        if (!this.sprint.startDate || !this.sprint.endDate) {
            return;
        }
        CardModel.getHolidayStatus({
            startDate: this.sprint.startDate,
            endDate: this.sprint.endDate,
        }).then((rs) => {
            this.workDays = rs.filter(item => item.workDay)
                .map(item => `${item.month}-${item.day > 9 ? item.day : `0${item.day}`}`);
        });
    },
    methods: {
        roundDay(timestamp) {
            return 3600 * 24 * Math.round((timestamp / (3600 * 24)));
        },
        load(sprintId) {
            if (!sprintId) {
                return;
            }
            PlanModel.getBurndownChart(sprintId)
                .then(({ burnoutData }) => {
                    this.result = burnoutData;
                    this.xAxis = {
                        categories: this.calcXAxis(),
                    };
                    this.options = this.errortsOptions;
                });
        },
        getLine(key) {
            const currentLine = JSON.parse(JSON.stringify(this.result))
                .filter(item => this.includeHoliday || this.workDays.some(v => v === this.formatDate(item.date)))
                .map(item => [this.calcIndex(item.date), item[key]]);
            const baseLine = [
                [0, this.result[0][key]],
                [this.includeHoliday ? this.calcIndex(this.sprint.endDate) : this.workDays.length - 1, 0],
            ];
            return {
                currentLine,
                baseLine,
            };
        },
        formatDate(value) {
            const date = new Date(value);
            const day = date.getDate();
            return `${date.getMonth() + 1}-${day > 9 ? day : `0${day}`}`;
        },
        calcXAxis() {
            const data = [];
            for (let i = this.sprint.startDate; i <= this.sprint.endDate; i += 3600000 * 24) {
                if (!this.includeHoliday) {
                    if (this.workDays.some(v => v === this.formatDate(i))) {
                        data.push(this.formatDate(i));
                    }
                } else {
                    data.push(this.formatDate(i));
                }
            }
            return data;
        },
        calcIndex(timestamp) {
            if (!this.includeHoliday) {
                return this.workDays.findIndex(item => item === this.formatDate(timestamp));
            }
            return Math.round(timestamp / (3600000 * 24)) - Math.round(this.sprint.startDate / (3600000 * 24));
        },
        calcOptions() {
            if (this.type.includes('storys')) {
                this.options = this.storyOptions;
            } else if (this.type.includes('tasks')) {
                this.options = this.taskOptions;
            } else {
                this.options = this.errortsOptions;
            }
        },
    },
    computed: {
        empty() {
            return !this.burndownData.length;
        },
        fieldText() {
            return {
                noBurnData: '暂无燃尽图数据',
            };
        },
        errortsData() {
            if (!this.result.length) {
                return [];
            }
            const line = this.getLine('remainingHours');
            return [{
                ...standardConfig,
                data: line.baseLine,
            }, {
                name: '剩余工时',
                color: '#09DBA7',
                data: line.currentLine,
            }];
        },
        storysData() {
            if (!this.result.length) {
                return [];
            }
            const line = this.getLine('storySum');
            return [{
                ...standardConfig,
                data: line.baseLine,
            }, {
                name: '故事点',
                color: '#09DBA7',
                data: line.currentLine,
            }];
        },
        tasksData() {
            if (!this.result.length) {
                return [];
            }
            const line = this.getLine('unfinishedTaskCount');
            return [{
                ...standardConfig,
                data: line.baseLine,
            }, {
                name: '剩余任务数',
                color: '#09DBA7',
                data: line.currentLine,
            }];
        },
        storyOptions() {
            return Object.assign(JSON.parse(JSON.stringify(options)), {
                yAxis: {
                    title: {
                        enabled: true,
                        text: '故事点',
                    },
                    labels: {
                        formatter() {
                            return `${this.value}`;
                        },
                    },
                    lineWidth: 2,
                },
                exporting: {
                    enabled: this.exporting,
                },
                xAxis: this.xAxis,
            });
        },
        errortsOptions() {
            return Object.assign(JSON.parse(JSON.stringify(options)), {
                yAxis: {
                    title: {
                        enabled: true,
                        text: '剩余工时',
                    },
                    labels: {
                        formatter() {
                            return `${this.value}h`;
                        },
                    },
                    lineWidth: 2,
                },
                exporting: {
                    enabled: this.exporting,
                },
                xAxis: this.xAxis,
            });
        },
        taskOptions() {
            return Object.assign(JSON.parse(JSON.stringify(options)), {
                yAxis: {
                    title: {
                        enabled: true,
                        text: '剩余任务数',
                    },
                    labels: {
                        formatter() {
                            return `${this.value}`;
                        },
                    },
                    lineWidth: 2,
                },
                exporting: {
                    enabled: this.exporting,
                },
                xAxis: this.xAxis,
            });
        },
        burndownData() {
            // series.setData的时候会直接覆盖掉原始引用的值，导致异常，所以每次传给chart的值都是副本，省的被改掉
            return JSON.parse(JSON.stringify(this[this.type]));
        },
    },
    watch: {
        sprint: {
            deep: true,
            handler(n) {
                if (n.id && n.id !== -1) {
                    this.type = 'errortsData';
                    this.load(n.id);
                }
            },
            immediate: true,
        },
        type: {
            immediate: true,
            handler() {
                this.calcOptions();
            },
        },
        includeHoliday: {
            immediate: true,
            handler() {
                this.xAxis = { categories: this.calcXAxis() };
                this.calcOptions();
            },
        },
    },
};
</script>
