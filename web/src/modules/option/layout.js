import index from './layout.index';
import formOption from './formOption.main';

/* import adminOption from './adminOption.list';
import auxiliaryOption from './auxiliaryOption.list';
import whiteList from './whiteList.main';
import demandFlowOption from './demandFlowOption.main';
import moduleOption from './moduleOption';
import moduleManagment from './moduleManagment';
import globalSetting from './globalSetting';  */
import extendFormSettingEdit from './extendFormSettingEdit';

export default {
    index,
    formOption, // 这里可以共享一些样式。。。
    // formOption: () => import('./formOption.main'),
    adminOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./adminOption.list'),
    auxiliaryOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./auxiliaryOption.list'),
    whiteList: () => import(/* webpackChunkName: "chunck-layout-o" */'./whiteList.main'),
    orgSetting: () => import(/* webpackChunkName: "chunck-layout-o" */'./orgSetting'),
    // 这一个没有使用
    // demandFlowOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./demandFlowOption.main'),
    appSetting: () => import(/* webpackChunkName: "chunck-layout-o" */'./appSetting'),
    globalSetting: () => import(/* webpackChunkName: "chunck-layout-o" */'./globalSetting'),
    // 这一个没有使用
    // dynamicFormSetting: () => import(/* webpackChunkName: "chunck-layout-o" */'./dynamicForm.setting'),
    deptSpecial: () => import(/* webpackChunkName: "chunck-layout-o" */'./deptSpecial'),
    demandPriorityOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./demandPriorityOption.main'),
    extendFormSetting: () => import(/* webpackChunkName: "chunck-layout-o" */'./extendFormSetting'),
    // extendFormSettingEdit: () => import(/* webpackChunkName: "chunck-layout-o" */'./extendFormSettingEdit'),
    extendFormSettingEdit,
    taskCenter: () => import(/* webpackChunkName: "chunck-layout-o" */'./taskCenter/taskCenter'),
    operateLog: () => import(/* webpackChunkName: "chunck-layout-o" */'./operateLog'),
    orgAdminSetting: () => import(/* webpackChunkName: "chunck-layout-o" */'./orgAdminSetting'),
    appAdminSetting: () => import(/* webpackChunkName: "chunck-layout-o" */'./appAdminSetting'),
    aasNewRoleOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./security/roleNewManage'),
    // aasRoleOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./security/roleManage'),
    // aasGroupOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./security/groupManage'),
    // aasPermOption: () => import(/* webpackChunkName: "chunck-layout-o" */'./security/resourceManage'),
    noticeCenter: () => import(/* webpackChunkName: "chunck-layout-o" */'./noticeCenter'),
};
