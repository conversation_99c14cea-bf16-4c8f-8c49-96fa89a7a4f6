<template>
    <div class="operateLog" v-if="jurisdicList['archOptRecord:view'] === 0">
        <div class="header">
            <h4>操作记录</h4>
        </div>
        <div class="search">
            <el-form
                :inline="true"
                :model="searchData"
                class="demo-form-inline"
            >
                <el-form-item label="操作人">
                    <jacp-input-users
                        class="userorgname"
                        v-model="searchData.createUser"
                        placeholder="请输入操作人"
                        prefix-icon="el-icon-search"
                    />
                </el-form-item>
                <el-form-item label="操作模块">
                    <el-select
                        v-model="searchData.moduleCodes"
                        multiple
                        placeholder="请选择"
                    >
                        <el-option
                            label="全部"
                            value=""
                            @click.stop.native="selectAll"
                        />
                        <el-option
                            v-for="item in operateOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作内容">
                    <el-input
                        v-model="searchData.operationContent"
                        placeholder="请输入内容~"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="操作时间">
                    <el-date-picker
                        v-model="searchData.opTime"
                        type="daterange"
                        range-separator="-"
                        value-format="yyyy-MM-dd"
                        @change="opTimeChange"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        @click="onSubmit"
                        v-if="jurisdicList['archOptRecord:search'] === 0"
                    >
                        查询
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archOptRecord:search'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            查询
                        </el-button>
                    </el-tooltip>
                </el-form-item>
            </el-form>
        </div>
        <div class="table">
            <el-table
                :data="tableData"
                style="width: 100%"
                tooltip-effect="dark"
            >
                <el-table-column
                    prop="opUserName"
                    label="操作人"
                    show-overflow-tooltip
                    width="180"
                >
                    <template slot-scope="scope">
                        <person-info
                            :name="scope.row.opUserName"
                            :erp="scope.row.opUserErp"
                        />
                    </template>
                </el-table-column>
                <el-table-column
                    prop="moduleCodes"
                    label="操作模块"
                    show-overflow-tooltip
                    width="180"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.moduleName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="operationContent"
                    label="操作内容"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="opTime"
                    label="操作时间"
                    show-overflow-tooltip
                    width="180"
                />
            </el-table>
        </div>
        <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="init"
                @current-change="init"
                :page-size.sync="pagesize"
                :total="total"
                :current-page.sync="currentPage"
                layout="prev, pager, next, total, sizes, jumper"
            />
        </div>
    </div>
</template>

<script>
import Setting from '@/modules/option/model/setting';
import { mapState } from 'vuex';

export default {

    data() {
        return {
            searchData: {
                appCode: 'arch',
                createUser: [],
                createUserErps: [],
                moduleCodes: [],
                operationContent: '',
                opStartDate: '',
                opEndDate: '',
                opTime: [],
                edit: false,
            },
            operateOption: [
                { name: '权限管理', code: 'archAuth' },
                { name: '部门管理员设置', code: 'archOrgAdmin' },
                { name: '应用管理员设置', code: 'archAppAdmin' },
            ],
            tableData: [],
            currentPage: 1,
            pagesize: 100,
            total: 0,
            isSelectedAll: false,
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    methods: {
        onSubmit() {
            this.init();
        },
        init() {
            this.searchData.createUserErps = [];
            this.searchData.createUser.forEach((item) => {
                this.searchData.createUserErps.push(item.erp);
            });
            this.searchData.page = {
                size: this.pagesize,
                index: this.currentPage,
            };
            Setting.getAppList(this.searchData).then((res) => {
                this.tableData = res.records;
                this.total = res.total;
            });
        },
        opTimeChange() {
            if (this.searchData.opTime && this.searchData.opTime.length) {
                this.searchData.opStartDate = this.searchData.opTime[0];
                this.searchData.opEndDate = this.searchData.opTime[1];
            } else {
                this.searchData.opStartDate = [];
                this.searchData.opEndDate = [];
            }
        },
        // moduleCodesChange() {
        //     console.log(this.searchData.moduleCodes, 'this.searchData.moduleCodes');
        //     if (this.searchData.moduleCodes.length === 3 && this.searchData.moduleCodes.indexOf('') === -1) {
        //         this.searchData.moduleCodes.push('');
        //     } else if (this.searchData.moduleCodes.length === 3 && this.searchData.moduleCodes.indexOf('') !== -1) {
        //         this.searchData.moduleCodes = this.searchData.moduleCodes.filter(item => item !== '');
        //     }
        // },
        // removeTag() {
        //     console.log(this.searchData.moduleCodes, 'this.searchData.moduleCodes1');
        //     if (this.searchData.moduleCodes.length === 4) {
        //         // console.log(1111111);
        //         this.searchData.moduleCodes = this.searchData.moduleCodes.filter(item => item !== '');
        //     }
        // },
        // moduleCodesAll() {
        //     if (this.isSelectedAll) {
        //         this.searchData.moduleCodes = [];
        //         this.searchData.moduleCodes.push('');
        //         this.operateOption.forEach((res) => {
        //             this.searchData.moduleCodes.push(res.code);
        //         });
        //     } else {
        //         this.searchData.moduleCodes = [];
        //     }
        //     this.isSelectedAll = !this.isSelectedAll;
        // },
        selectAll() {
            this.searchData.moduleCodes = [];
            this.operateOption.forEach((res) => {
                this.searchData.moduleCodes.push(res.code);
            });
        },
    },
    watch: {
        isSelectedAll: {
            handler(val) {
                if (val) {
                    this.searchData.moduleCodes = [];
                    this.searchData.moduleCodes.push('');
                    this.operateOption.forEach((res) => {
                        this.searchData.moduleCodes.push(res.code);
                    });
                } else {
                    this.searchData.moduleCodes = [];
                }
            },
        },
    },
};
</script>

<style lang="less" scoped>
.operateLog {
    position: relative;
    margin-left: 24px;
    margin-top: 24px;
    .table {
        flex: 1;
        overflow: auto;
    }
    .footer {
        padding: 16px 24px;
        .j-pagination {
            text-align: right;
        }
    }
}
</style>
