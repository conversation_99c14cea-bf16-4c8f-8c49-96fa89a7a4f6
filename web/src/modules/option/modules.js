import {
    apiGateway,
} from '@/plugins/http';
import store from '$platform.store';

const getWay = {
    modules: '',
    appCode: 'arch',
};
// 根据用户获取前端资源列表
export function getPermissionUicomponents(params = getWay) {
    return apiGateway
        .get('uaos/api/v1/auth/permission/uicomponents', { params })
        .then((res) => {
            // 0:展示，1:置灰，2:隐藏
            store.commit('optionUpdate', res);
            return res;
        });
}
