<template>
    <el-container>
        <el-main class="moduleOption__main">
            <div class="moduleOption__main__header">
                <el-button
                    class="moduleOption__main__header__btn"
                    type="primary"
                    size="small"
                    @click="newModule"
                >
                    注册应用
                </el-button>
                <el-input
                    class="search"
                    placeholder="请输入应用名称关键字"
                    prefix-icon="el-icon-search"
                    :clearable="true"
                    v-model="queryCondition"
                />
            </div>
            <el-table
                :data="moduleList"
                style="width: 100%"
                class="option-admin__list"
                :row-style="rowFilter"
            >
                <el-table-column
                    prop="index"
                    label="序号"
                    width="80"
                />
                <el-table-column
                    prop="name"
                    label="应用名称"
                    width="220"
                />
                <el-table-column
                    prop="code"
                    label="应用标识"
                    width="180"
                />
                <el-table-column
                    prop="register.url"
                    label="应用地址url"
                />
                <el-table-column
                    prop="register.type"
                    label="应用类型"
                    width="100"
                />
                <el-table-column
                    prop="permissionCode"
                    label="权限码"
                    width="180"
                />
                <el-table-column
                    prop="topMenuMounted"
                    label="是否挂载一级菜单"
                />
                <el-table-column
                    prop="register.parentRouterName"
                    label="上级应用"
                    width="180"
                />
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            v-if="scope.row.status == 'inactive'"
                            size="small"
                            @click="changeStatus(scope.row, 'active')"
                        >
                            启用
                        </el-button>
                        <el-button
                            type="text"
                            v-if="scope.row.status == 'active'"
                            size="small"
                            @click="changeStatus(scope.row, 'frozen')"
                        >
                            冻结
                        </el-button>
                        <el-button
                            type="text"
                            v-if="scope.row.status == 'frozen'"
                            size="small"
                            @click="changeStatus(scope.row, 'active')"
                        >
                            解冻
                        </el-button>
                        <el-button
                            type="text"
                            v-if="scope.row.status == 'frozen'"
                            size="small"
                            @click="changeStatus(scope.row, 'dead')"
                        >
                            删除
                        </el-button>
                        <el-button
                            type="text"
                            v-if="scope.row.status !== 'active'"
                            size="small"
                            @click="editModule(scope.row.id, scope.row.appId)"
                        >
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <add-module
                :show-add-module.sync="showAddModule"
                :app-list="appList"
                @refresh-list="getModuleList"
            />
            <edit-module
                :show-edit-module.sync="showEditModule"
                :module-id="currentModuleId"
                :app-id="appId"
                :app-list="appList"
                @refresh-list="getModuleList"
            />
        </el-main>
    </el-container>
</template>
<script type="text/javascript">
import ModuleOption from '@/models/moduleOption';
import SecurityClient from '@/models/security';
import addModule from './components/addModule';
import editModule from './components/editModule';

export default {
    components: {
        addModule, editModule,
    },
    data() {
        return {
            curRow: {},
            moduleList: [],
            showConfirm: false,
            showAddModule: false,
            showEditModule: false,
            currentModuleId: null,
            appId: null,
            queryCondition: '',
            appList: [],
            timer: null,
        };
    },
    mounted() {
        this.getModuleList();
        this.getAppList();
    },
    methods: {
        getModuleList() {
            ModuleOption.getModuleList({
                // status: 'inactive',
            }).then((data) => {
                this.moduleList = data || [];
            });
        },
        newModule() {
            this.showAddModule = true;
        },
        // changeStatus(id, status) {
        //     ModuleOption.updateModuleStatus({ id, status }).then(() => {
        //         // 加载列数据
        //         this.getModuleList();
        //         this.$notify({
        //             title: '保存成功',
        //             type: 'success',
        //             duration: 2000,
        //         });
        //     });
        // },
        changeStatus(row, status) {
            // Promise.all([ModuleOption.updateModuleStatus({ id: row.id, appId: row.appId, status }), this.getModuleList()]).then(() => {
            //     this.$notify({
            //         title: '保存成功',
            //         type: 'success',
            //         duration: 2000,
            //     });
            // });
            // if (!this.flag) {
            //     this.flag = true;
            //     ModuleOption.updateModuleStatus({ id: row.id, appId: row.appId, status }).then(() => {
            //         console.log(this.flag, 'ppppppppp');
            //         // 加载列数据
            //         this.getModuleList(true);
            //         this.$notify({
            //             title: '保存成功',
            //             type: 'success',
            //             duration: 2000,
            //         });
            //     });
            // } else {
            //     this.$message.error('操作过于频繁，请稍后再试');
            // }
            if (!this.timer) {
                this.timer = setTimeout(() => {
                    ModuleOption.updateModuleStatus({ id: row.id, appId: row.appId, status }).then(() => {
                        // 加载列数据
                        this.getModuleList(true);
                        this.$notify({
                            title: '保存成功',
                            type: 'success',
                            duration: 2000,
                        });
                        this.timer = null;
                    });
                }, 2000);
            } else {
                this.$message.error('操作过于频繁，请稍后再试');
            }
        },
        doDelete() {
            ModuleOption.delModule(this.curRow.id).then(() => {
                // 加载列数据
                this.getModuleList();
                this.$notify({
                    title: '删除成功',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
        rowFilter(scope) {
            if (scope.row.status === 'dead') {
                return { display: 'none' };
            }
            if (this.queryCondition !== '' && scope.row.name.indexOf(this.queryCondition) < 0) {
                return { display: 'none' };
            }
            return {};
        },
        editModule(currentModuleId, appId) {
            this.showEditModule = true;
            this.currentModuleId = currentModuleId;
            this.appId = appId;
        },
        getAppList() {
            SecurityClient.getAppList().then((data) => {
                this.appList = data;
            });
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
    .moduleOption__main{
        overflow-y: auto;
        &__header{
            border-bottom: 3px solid @primaryColor;
            height: 40px;
            overflow: hidden;
            & .search {
                float: right;
                width: 300px;
                right: 10px;
            }
        }
    }
</style>
