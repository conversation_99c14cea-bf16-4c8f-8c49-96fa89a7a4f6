<template>
    <!-- 用了formOption.main.vue里的样式，临时用，后期改造设置页面的时候再改 -->
    <div class="dynamicform-setting">
        <div
            class="dynamicform-setting__header form-option-org"
        >
            <span class="form-option-org__label">部门</span>
            <el-select
                v-model="userOrgId"
                placeholder="请选择"
                :filterable="true"
            >
                <el-option
                    v-for="item in orgList"
                    :key="item.id"
                    :value="item.userOrgId"
                    :label="item.userOrgName"
                >
                    <span style="float: left">{{ item.userOrgName }}</span>
                    <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                </el-option>
            </el-select>
            <el-tooltip placement="top">
                <div slot="content">
                    注意：只有一级部门管理员有权限新建扩展字段哦！
                    <br><br>
                    1.请<strong>选择一个一级部门</strong>
                    <br><br>
                    2.<strong>编辑表单</strong>和控件属性，点击<strong>保存</strong>
                    <br><br>
                    3.到<strong>模版字段中查看</strong>,并为不同类型需求<strong>设置是否显示或必填</strong>
                    <br>
                </div>
                <i
                    class="el-icon-question"
                    style="color: #E1E1E1;cursor: pointer;"
                    @click="showGuide = true"
                />
            </el-tooltip>
        </div>
        <!-- 以下是自定义表单编辑器 -->
        <dynamic-form-editor
            ref="dynamicFormEditorEl"
            :schema.sync="dynamicFormData.schema"
            :before-remove="handleRemove"
            :available-types="[
                'text',
                'integer',
                'boolean',
                'date',
                'enum',
                'select_user',
                'select_org',
            ]"
            :max-count="5"
            class="dynamicform-setting__body"
        >
            <div
                class="dynamicform-tips"
                slot="list"
            >
                <el-divider />
                <h5>扩展字段设置说明</h5>
                <ul class="dynamicform-tips__list">
                    <li>1. 一级部门管理员有权限新建扩展字段</li>
                    <li>2. 扩展字段会将应用到需求模板字段设置中</li>
                    <li>3. 扩展字段保存后会应用到子部门</li>
                    <li>4. 最多可新建 5 条扩展字段</li>
                    <li>5. 若删除字段，会将对应的历史记录一并删除</li>
                </ul>
            </div>
            <p
                style="margin-left: 16px;"
                slot="preview"
            >
                <el-button
                    type="primary"
                    v-show="dynamicFormData.schema"
                    :disabled="!userOrgId"
                    @click="handleSaveSchema"
                >
                    保存扩展字段
                </el-button>
            </p>
        </dynamic-form-editor>
        <jacp-guide
            :visible.sync="showGuide"
            :list="guideList"
        />
    </div>
</template>
<script>

import AdminOption from '@/models/adminOption';
import DynamicForm from '@/models/dynamicForm';

// 自定义表单新手引导
const guideList = ['<EMAIL>', '<EMAIL>'];
const initGuide = (localStorageKey) => {
    const exist = localStorage.getItem(`jacp-guide-${localStorageKey}`);
    if (!exist) {
        localStorage.setItem(`jacp-guide-${localStorageKey}`, 1);
    }
    return !exist;
};
// 混入自定义表单的操作，方便迁移
const mixinDynamicForm = {
    data() {
        return {
            userOrgId: undefined,
            dynamicFormData: {}, // 自定义表单的数据
            guideList,
            showGuide: initGuide(this.$options.name),
        };
    },
    created() {
        if (typeof this.userOrgId !== 'undefined') {
            this.$watch('userOrgId', {
                immediate: true,
                handler: this.loadDynamicForm,
            });
        }
    },
    methods: {
        async loadDynamicForm(userOrgId, oldOrgId) {
            if (!userOrgId) {
                return;
            }

            this.dynamicFormData = await DynamicForm.load({
                name: 'demand',
                scope: userOrgId,
            });
            if (userOrgId !== oldOrgId && !this.dynamicFormData.schema?.field?.length) {
                this.$refs.dynamicFormEditorEl.resetCurrentField();
            }
        },
        async saveDynamicForm(beforeSave) {
            if (!this.dynamicFormData.save) {
                return Promise.reject(new Error(`Wrong Function: ${this.dynamicFormData.save}`));
            }
            const save = () => this.dynamicFormData.save()
                .then(() => {
                    this.$notify.success('保存成功');
                    // this.$emit('dynamicForm-fields:change');
                })
                .catch(err => this.$notify.error(`保存失败:${err}`));
            if (beforeSave) {
                return Promise.resolve(beforeSave)
                    .then(result => (result ? save() : null));
            }
            return save();
        },
        handleSaveSchema() {
            const done = () => this.saveDynamicForm(this.$confirm('保存后将应用到当前部门及其各个子部门的【模板字段设置】中, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }));
            const { schema } = this.dynamicFormData;
            const errorField = schema.fields.find(o => o.hasError);
            if (errorField) {
                this.$message.error(`【${errorField.label}】字段的控件属性有不合法字段`);
            } else {
                done();
            }
        },
        handleRemove(field) {
            const done = () => this.$confirm('删除该字段，将联动子部门的对应字段一并删除，这将可能影响数据的完整性，请您确认是否需要继续删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            });
            // 删除远端的field才需要提示文案
            if (field.remote) {
                return done();
            }
            return true;
        },
    },
};
export default {
    name: 'DynamicFormSetting',
    mixins: [mixinDynamicForm],
    data() {
        return { orgList: [], userOrgId: '' };
    },
    created() {
        const { erp: userErp } = this.$store.state.user;
        // 查当前这个人所能管理的所有一级部门
        AdminOption.getAdminList({ userErp }).then((data) => {
            this.orgList = data.records;
            if (this.orgList.length) {
                const [first] = this.orgList;
                this.userOrgId = first.userOrgId;
            }
        });
    },
};
</script>
<style lang="less">
.dynamicform-setting{
    padding: 24px;
}
.dynamicform-setting__header,
.dynamicform-setting__body{
    background: #fff;
}
.dynamicform-setting__header {
    padding: 16px 24px;
    position: relative;
    &:after {
        content: " ";
        display: block;
        height: 1px;
        width: 100%;
        background-color: #f1f1f1;
        position: absolute;
        bottom: 0;
        transform: translateX(-24px);
    }
    &.form-option-org{
        margin-bottom: 0;
    }
}
.dynamicform-setting__body{
    height: calc(~"100vh - 160px");
    padding: 0 24px;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
}
.dynamicform-tips{
    color: #333;
    &__list{
        font-size: 12px;
        > li{
            // margin: 8px 0;
            list-style: none;
            line-height: 24px;
        }
    }
}
</style>
