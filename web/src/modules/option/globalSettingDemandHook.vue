<template>
    <div
        class="global-setting-demand-hook"
    >
        <div class="form-option-demand__head">
            <span>启用模板</span>
            <el-switch v-model="disabled" />
        </div>
        <div
            class="checkbox-info"
        >
            <el-checkbox v-model="setting.lazyDemandAutoAcceptance">
                需求超期⾃动验收通过
            </el-checkbox>
        </div>
        <div
            class="warn-info"
        >
            说明：<br>
            ① 受理部⻔为所选部⻔，且已开启此规则，提交需求时则会出现⾃动验收通过时效输⼊框，提交验收后则进⾏倒计时，到达该天数时限后，需求⾃动验收通过。<br>
            ② ⾃动验收通过时效上限为21天，默认值7天，下限为1天。<br>
            ③ 若开启该功能后，⾃动验收通过时效为必填项。<br>
        </div>
        <div
            class="checkbox-info"
        >
            <el-checkbox v-model="setting.rdDemandAutoAcceptance">
                研发需求⾃动验收通过
            </el-checkbox>
        </div>
        <div
            class="warn-info"
        >
            说明：<br>
            ① 若需求来源为「研发需求」，且需求⼈及受理⼈为同⼀⼈，且为当前规则所处部⻔，则该需求成功发起验收后，⾃动验收通过。<br>
            ② 若满⾜此条件，则优先⽣效。<br>
        </div>
        <el-button
            type="primary"
            size="small"
            @click="save"
            style="margin-top: 20px"
        >
            保存
        </el-button>
    </div>
</template>
<script type="text/javascript">
import GlobalSetting from '@/models/globalSetting';

export default {
    props: {
        orgId: {
            type: String,
        },
    },
    data() {
        return {
            setting: {},
            disabled: false,
        };
    },
    mounted() {
        this.loadSetting();
    },
    methods: {
        loadSetting() {
            if (this.orgId) {
                GlobalSetting.getSettingOrg('demand', this.orgId).then((data) => {
                    this.setting = data.jsonValue;
                    this.disabled = data.disabled === 0;
                });
            }
        },
        save() {
            GlobalSetting.updateSettingOrg('demand', {
                orgId: this.orgId,
                value: this.setting,
                disabled: this.disabled ? 0 : 1,
            }).then(() => {
                this.$message('保存成功。');
            });
        },
    },
    watch: {
        orgId() {
            this.loadSetting();
        },
    },
};
</script>
<style lang="less">
.global-setting-demand-hook {
    .warn-info {
        font-size: 12px;
        color: #BBB;
        margin-top: 10px;
        line-height: normal;
    }
    .checkbox-info {
        color: #000000;
        margin: 20px 0 10px 0;
    }
}
</style>
