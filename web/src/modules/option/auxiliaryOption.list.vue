<template>
    <div class="option-auxiliary">
        <div class="option-auxiliary__tittle">
            查询条件
        </div>
        <el-form
            :inline="true"
            class="option-auxiliary__condition"
            label-position="left"
            label-width="80px"
        >
            <el-form-item
                label="部门"
                prop="userOrgId"
            >
                <el-select
                    v-model="userOrgId"
                    placeholder="请选择"
                    filterable
                    :default-first-option="true"
                    @change="changeOrg"
                >
                    <el-option
                        v-for="item in orgList"
                        :key="item.orgId"
                        :value="item.orgId"
                        :label="item.orgName"
                    >
                        <span style="float: left">{{ item.orgName }}</span>
                        <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="姓名">
                <jacp-input-users
                    v-model="userInfo"
                    placeholder="姓名"
                    :max-count="1"
                />
            </el-form-item>
            <el-form-item label="添加人员">
                <jacp-input-users
                    v-model="optInfo"
                    placeholder="添加人员"
                    :max-count="1"
                />
            </el-form-item>
            <el-form-item label="添加时间">
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    placeholder="选择日期范围"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    @click="queryList"
                >
                    查询
                </el-button>
                <el-button
                    type="primary"
                    size="small"
                    @click="newAuxiliary"
                >
                    添加
                </el-button>
                <el-button
                    type="danger"
                    size="small"
                    @click="deleteMultiple"
                >
                    批量删除
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="auxiliaryList"
            style="width: 100%"
            class="option-auxiliary__list"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="55"
                :selectable="isAdmin"
            />
            <el-table-column
                prop="userOrgName"
                label="负责部门"
                width="320"
            >
                <template slot-scope="scope">
                    {{ scope.row.userOrgName }}
                    <span style="float: right; color: red;">{{ scope.row.invalid ? '无效' : '' }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="userName"
                label="姓名"
                width="220"
            >
                <template slot-scope="scope">
                    <span>{{ scope.row.userName+'('+ scope.row.userErp +')' }}</span>
                    <i
                        class="el-icon-star-on"
                        v-if="scope.row.groupId === 2"
                    />
                </template>
            </el-table-column>
            <el-table-column
                prop="optName"
                label="添加人员"
                width="220"
            >
                <template slot-scope="scope">
                    {{ scope.row.optName+'('+ scope.row.optErp +')' }}
                </template>
            </el-table-column>
            <el-table-column
                prop="cTime"
                label="添加时间"
                width="180"
                :formatter="dateFormatter"
            />
            <el-table-column
                prop="address"
                label="操作"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        v-if="scope.row.groupId === 3"
                        size="small"
                        @click="deleteAuxiliary([scope.row.id])"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script type="text/javascript">
import Dialog from '@/models/dialog';
import AuxiliaryOption from '@/models/auxiliaryOption';
import addAuxiliary from './components/addAuxiliary';

export default {
    data() {
        return {
            auxiliaryList: [],
            orgList: [],
            userInfo: [],
            optInfo: [],
            dateRange: [],
            delRowList: [],
            userOrgId: '',
        };
    },
    mounted() {
        // 加载一级部门数据
        this.loadOrglist();
    },
    methods: {
        // 查询现有管理员列表
        queryList() {
            const params = {
                startDate: this.dateRange[0] ? this.dateRange[0].getTime() : '',
                endDate: this.dateRange[1] ? this.dateRange[1].getTime() : '',
                userOrgId: this.userOrgId, // 一级部门编码
            };
            params.userErp = this.userInfo.length > 0 ? this.userInfo[0].erp : ''; // 辅助管理员erp
            params.optErp = this.optInfo.length > 0 ? this.optInfo[0].erp : ''; // 添加人erp
            // 查询列表数据
            AuxiliaryOption.getAuxiliaryList(params).then((data) => {
                this.auxiliaryList = data.records;
            });
        },
        // 加载部门列表
        loadOrglist() {
            AuxiliaryOption.getOrgList().then((data) => {
                this.orgList = data;
                if (this.orgList.length > 0) {
                    this.userOrgId = this.orgList[0].orgId;
                    // 加载列数据
                    this.queryList();
                }
            });
        },
        // 删除管理员
        deleteAuxiliary(rowIds) {
            this.$confirm('确定删除该管理员信息？', this.$t('jacp.deltitle'), {
                confirmButtonText: this.$t('jacp.button.remove'),
                cancelButtonText: this.$t('jacp.button.cancel'),
                type: 'warning',
            }).then(() => {
                AuxiliaryOption.deleteAuxiliary(rowIds).then(() => {
                    Dialog.alert({
                        title: '操作提示',
                        content: '删除成功',
                        type: 'success',
                        timeout: 2000,
                    });
                    this.queryList();
                });
            });
        },
        // 添加管理员
        newAuxiliary() {
            return Dialog.confirm({
                title: '添加管理员',
                confirmBtnText: '提交',
                slot: addAuxiliary,
                beforeConfirm: vm => vm.validateData().then(() => {
                    const param = vm.data;
                    param.userErp = param.userInfo[0].erp;
                    param.userName = param.userInfo[0].name;
                    delete param.userInfo;
                    AuxiliaryOption.addAuxiliary(param).then(() => {
                        // 加载列数据
                        this.queryList();
                        this.$notify({
                            title: '保存成功',
                            type: 'success',
                            duration: 2000,
                        });
                        // 将新部门添加进orgList
                        let flag = true;
                        this.orgList.every((item) => {
                            if (item.orgId === param.userOrgId) {
                                flag = false;
                                return false;
                            }
                            return true;
                        });
                        if (flag) {
                            this.orgList.push({
                                orgId: param.userOrgId,
                                orgName: param.userOrgName,
                            });
                        }
                    });
                }),
                slotProps: { orgList: this.orgList, userOrgId: this.userOrgId },
            });
        },
        validateData() {
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject();
                    }
                });
            });
        },
        deleteMultiple() {
            if (this.delRowList.length === 0) {
                Dialog.alert({
                    title: '操作提示',
                    content: '请至少选择一条要删除的数据！',
                    type: 'success',
                    timeout: 2000,
                });
                return;
            }
            const rowList = [];
            this.delRowList.forEach((item) => {
                rowList.push(item.id);
            });
            this.deleteAuxiliary(rowList);
        },
        handleSelectionChange(selection) {
            this.delRowList = selection;
        },
        dateFormatter(row) {
            const date = new Date(row.cTime);
            const y = date.getFullYear();
            const m = date.getMonth() + 1 > 9 ? `-${date.getMonth() + 1}` : `-0${date.getMonth() + 1}`;
            const d = date.getDate() > 9 ? `-${date.getDate()}` : `-0${date.getDate()}`;
            return y + m + d;
        },
        isAdmin(row) {
            return row.groupId !== 2;
        },
        changeOrg() {
            // 重新加载列数据
            this.queryList();
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.option-auxiliary{
    padding: 10px 20px;
    overflow-y: auto;
    &__condition{
        border-bottom: 3px solid @primaryColor;
        & .el-input{
            width: 350px;
        }
    }
    &__list{
        margin-top: 10px;
        height: calc(~"100vh - 320px");
        overflow-y: auto;
    }
    & .el-form-item{
        width: 40%;
        margin-right: 0;
    }
    &__tittle{
        border-left: 5px solid @primaryColor;
        font-size: 14px;
        padding-left: 10px;
        margin-bottom: 10px;
        background-color: #eef3f6;
        height: 24px;
        line-height: 24px;
    }
}
</style>
