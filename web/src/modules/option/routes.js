import option from '@/modules/option/layout';

const routers = [{
    path: 'option',
    name: 'option',
    component: option.index,
    redirect: { name: 'archOrg' },
    children: [{
        path: 'adminOption',
        name: 'adminOption',
        component: option.adminOption,
    }, {
        path: 'archOrg',
        name: 'archOrg',
        component: option.orgSetting,
    }, {
        path: 'archAuth',
        name: 'archAuth',
        component: option.aasNewRoleOption,
        // redirect: { name: 'aasNewRole' },
    },
    // {
    //     path: 'aas/newrole',
    //     name: 'aasNewRole',
    //     component: option.aasNewRoleOption,
    // },
    // {
    //     path: 'aas/role',
    //     name: 'aasRole',
    //     component: option.aasRoleOption,
    // }, {
    //     path: 'aas/group',
    //     name: 'aasGroup',
    //     component: option.aasGroupOption,
    // }, {
    //     path: 'aas/perm',
    //     name: 'aasPerm',
    //     component: option.aasPermOption,
    // },
    // {
    //     path: 'archConfig',
    //     name: 'archConfig',
    //     component: option.auxiliaryOption,
    // },
    {
        path: 'archTaskCenter',
        name: 'archTaskCenter',
        component: option.taskCenter,
    }, {
        path: 'archNoticeCenter',
        name: 'archNoticeCenter',
        component: option.noticeCenter,
    }, {
        path: 'archConfig',
        name: 'archConfig',
        component: option.globalSetting,
    },
    {
        path: 'archDeptSpecial',
        name: 'archDeptSpecial',
        component: option.deptSpecial,
    },
    // {
    //     path: 'archDeptSpecial',
    //     name: 'archDeptSpecial',
    //     component: option.demandPriorityOption,
    // },
    {
        path: 'archForm',
        name: 'archForm',
        component: option.extendFormSetting,
    }, {
        path: 'archOptRecord',
        name: 'archOptRecord',
        component: option.operateLog,
    }, {
        path: 'archOrgAdmin',
        name: 'archOrgAdmin',
        // component: option.orgAdminSetting,
        component: option.adminOption,
    }, {
        path: 'archAppAdmin',
        name: 'archAppAdmin',
        component: option.appAdminSetting,
    }, {
        path: 'extendFormSettingEdit',
        name: 'extendFormSettingEdit',
        component: option.extendFormSettingEdit,
    }, {
        path: 'archRuleCompose',
        name: 'archRuleCompose',
        component: option.archRuleCompose,
    }],
}];

export default routers;
