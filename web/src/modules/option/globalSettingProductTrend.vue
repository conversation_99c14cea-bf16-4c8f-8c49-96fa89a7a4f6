<template>
    <div class="global-setting-product-trend">
        <el-form
            :model="productTrend"
            :rules="rules"
            ref="productTrendForm"
            label-width="100px"
            class="form-info"
        >
            <el-form-item
                label="标题"
                prop="name"
            >
                <el-input
                    v-model="productTrend.name"
                    placeholder="请输入标题"
                />
            </el-form-item>
            <el-form-item
                label="发布日期"
                prop="releaseDate"
            >
                <el-date-picker
                    type="datetime"
                    placeholder="选择发布日期"
                    format="yyyy-MM-dd HH:mm"
                    v-model="productTrend.releaseDate"
                    value-format="timestamp"
                />
            </el-form-item>
            <el-form-item
                label="内容"
                prop="content"
            >
                <quill-editor
                    v-model="productTrend.content"
                    ref="richEditorEl"
                    placeholder="请输入产品动向"
                    :height="350"
                >
                    <div
                        v-text="productTrend.content"
                        slot="full-title"
                        class="ql-editor-plus__fulltitle"
                    />
                </quill-editor>
            </el-form-item>
        </el-form>
        <div class="footer">
            <div class="warn-info warning-text">
                主页显示当前时刻之前的最后一条动向信息。
            </div>
            <el-button
                type="primary"
                @click="onSubmit"
                v-if="jurisdicList['archConfig:product:add'] === 0"
            >
                提交
            </el-button>
            <el-tooltip
                v-else-if="jurisdicList['archConfig:product:add'] === 1"
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    type="primary"
                    style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                >
                    提交
                </el-button>
            </el-tooltip>

            <el-button
                @click="onPreView"
                v-if="jurisdicList['archConfig:product:look'] === 0"
            >
                预览
            </el-button>
            <el-tooltip
                v-else-if="jurisdicList['archConfig:product:look'] === 1"
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    style="color: #c0c4cc;
                        cursor: not-allowed;
                        background-image: none;
                        background-color: #fff;
                        border-color: #ebeef5;"
                >
                    预览
                </el-button>
            </el-tooltip>
        </div>
        <jacp-product-trend ref="dlgPreview" />
    </div>
</template>

<script>
import GlobalSetting from '@/models/globalSetting';
import { mapState } from 'vuex';

export default {
    data() {
        return {
            productTrend: {
                name: '',
                releaseDate: new Date(),
                content: '',
            },
            rules: {
                name: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    {
                        min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur',
                    },
                ],
                releaseDate: [
                    {
                        type: 'date', required: true, message: '请选择发布日期', trigger: 'change',
                    },
                ],
                content: [
                    { required: true, message: '请输入内容', trigger: 'blur' },
                ],
            },
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    created() {
        this.getLatestProductTrend();
    },
    methods: {
        getLatestProductTrend() {
            GlobalSetting.getLatestProductTrend().then((data) => {
                if (data) {
                    this.productTrend = data;
                }
            });
        },
        onSubmit() {
            this.$refs.productTrendForm.validate((valid) => {
                if (valid) {
                    GlobalSetting.addProductTrend(this.productTrend).then(() => {
                        this.$notify({
                            title: '保存成功！',
                            type: 'success',
                            duration: 2000,
                        });
                        this.$refs.productTrendForm.resetFields();
                    }).catch(() => {
                        this.$notify({
                            title: '保存失败！',
                            type: 'error',
                            duration: 2000,
                        });
                    });
                }
            });
        },
        onPreView() {
            // this.$refs.dlgPreview.showProductTrend(this.productTrend);
        },
    },
};
</script>

<style lang="less">
    .global-setting-product-trend {
        margin-left: 40px;

        & .form-info {
            overflow: auto;
            height: 586px;
        }

        & .footer {
            margin-left: 100px;

            & .warn-info {
                font-size: 14px;
                color: #666;
                margin-bottom: 24px;
            }
        }
        & .warning-text{
            background-color: #fdf6ec;
            color: #e6a23c !important;
            padding: 8px 16px;
            box-sizing: border-box;
            border-radius: 4px;
        }
    }
</style>
