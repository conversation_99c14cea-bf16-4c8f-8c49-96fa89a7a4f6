<template>
    <div class="taskManage">
        <!--第一行，条件搜索栏-->
        <el-row
            :gutter="20"
        >
            <!-- 左侧搜索栏-->
            <el-col :span="24">
                <el-form
                    :inline="true"
                    :model="noticeQueryContent"
                    class="el-form--inline"
                >
                    <el-form-item label="应用">
                        <el-select
                            clearable
                            v-model="noticeQueryContent.appCode"
                            :placeholder="$t('jacp.message.jobSelect')"
                        >
                            <el-option
                                v-for="item in appKeyList"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select
                            clearable
                            v-model="noticeQueryContent.status"
                            placeholder="选择状态"
                        >
                            <el-option
                                v-for="item in sendTypeList"
                                :key="item.val"
                                :label="item.labelName"
                                :value="item.val"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="消息类型">
                        <el-input
                            clearable
                            v-model="noticeQueryContent.eventType"
                            placeholder="请输入消息类型"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="getPushList(0)"
                        >
                            {{ $t('jacp.message.query') }}
                        </el-button>
                        <el-button
                            type="cancel"
                            @click="onClickReset(0)"
                        >
                            {{ $t('jacp.message.reset') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>

        <div class="container">
            <el-table
                :data="noticeInfoPageResult.records"
                style="width: 100%"
            >
                <!-- <el-table-column
                    prop="id"
                    label="序号"
                    width="50"
                /> -->
                <el-table-column
                    prop="mailSubject"
                    label="消息类型"
                    :show-overflow-tooltip="true"
                />
                <el-table-column
                    label="渠道"
                    width="100"
                >
                    <template slot-scope="scope">
                        {{ scope.row.sendMsgType }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="收件人"
                    :show-overflow-tooltip="true"
                    prop="mailReceviers"
                />
                <el-table-column
                    label="操作人"
                    prop="createUser"
                />
                <el-table-column
                    label="状态"
                    width="80px"
                >
                    <template slot-scope="scope">
                        {{ scope.row.mailSendStatus===1?'成功':'失败' }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="发送时间"
                    width="150px"
                >
                    <template slot-scope="scope">
                        {{ scope.row.cTime | formatedDate }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 第三行，分页插件 -->
        <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="handleSizeChange"
                @current-change="onClickChangePage"
                :page-size="this.noticeInfoPageResult.size"
                :total="this.noticeInfoPageResult.total"
                :current-page.sync="current"
                layout="prev, pager, next, total, sizes, jumper"
            />
        </div>
    </div>
</template>

<script>
import { Task } from '@/models/taskManage';
import SecurityClient from '@/models/security';
import moment from 'moment';

export default {
    name: 'TaskManage',
    data() {
        return {
            AppOptions: [],
            // 任务查询请求对象
            noticeQueryContent: {
                appCode: undefined, // 应用
                status: undefined, // 状态
                eventType: undefined, // 输入名称查询
                current: 0,
                size: 10,
            },
            // 任务列表（查询结果）
            noticeInfoPageResult: {
                size: 10, // 每页条数
                total: 0,
                data: [],
            },
            current: 1, // 当前页
            appKeyList: [], // 应用数据
            sendTypeList: [
                { val: 0, labelName: '全部' },
                { val: 1, labelName: '发送成功' },
                { val: 2, labelName: '发送失败' },
            ], // 状态数据
        };
    },
    filters: {
        formatedDate: item => (item
            ? `${moment(item).format('YYYY-MM-DD HH:mm:ss')}`
            : '-'),
    },
    mounted() {
        this.getSelectList();
        this.getPushList();
        Task.appInfoList().then((res) => {
            this.AppOptions = res.data.data;
        });
        // 加载用户信息
        Task.userList().then((res) => {
            this.userList = res.data.data;
        });
    },
    methods: {
        getSelectList() {
            // 应用下拉
            SecurityClient.queryVal().then((data) => {
                this.appKeyList = data;
            });
            // const typeObj = ['appKey', 'msgSendType']; // 应用参数，状态参数
            // typeObj.forEach((item) => {
            //     SecurityClient.queryVal(item).then((data) => {
            //         if (item === 'appKey') { this.appKeyList = data; } else { this.sendTypeList = data; }
            //     });
            // });
        },
        getPushList(i) {
            if (i === 0) {
                this.noticeQueryContent.current = 0;
                this.current = 1;
            }
            // 获取列表数据  搜索
            SecurityClient.queryMsgDetail(this.noticeQueryContent).then((res) => {
                this.noticeInfoPageResult = res;
            });
        },
        // 分页
        onClickChangePage(index) {
            this.noticeQueryContent.current = index - 1;
            this.current = index;
            this.getPushList();
        },
        handleSizeChange(size) {
            this.noticeQueryContent.size = size;
            this.getPushList();
        },
        // 重置
        onClickReset(i) {
            if (i === 0) {
                this.noticeQueryContent.current = 0;
                this.current = 1;
            }
            this.noticeQueryContent.appCode = undefined;
            this.noticeQueryContent.eventType = undefined;
            this.noticeQueryContent.status = undefined;
            this.getPushList();
        },

    },
};
</script>

<style lang="less" scoped>
.taskManage {
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  .footer {
    background: #fff;
    height: 64px;
    position: relative;
    .j-pagination {
        position: absolute;
        top: 50%;
        right: 16px;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        }
  }
}
</style>
