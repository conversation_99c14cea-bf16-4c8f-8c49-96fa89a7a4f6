<template>
    <div class="demand-flow__main">
        <el-form
            :inline="true"
            class="demand-flow__main__org"
            label-position="left"
        >
            <el-form-item
                label="部门"
                prop="userOrgId"
            >
                <el-select
                    v-model="userOrgId"
                    placeholder="请选择"
                    filterable
                    :default-first-option="true"
                    @change="changeUserOrg"
                >
                    <el-option
                        v-for="item in orgList"
                        :key="item.orgId"
                        :value="item.orgId"
                        :label="item.orgName"
                    >
                        <span style="float: left">{{ item.orgName }}</span>
                        <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <el-tabs
            v-model="formName"
            type="border-card"
        >
            <el-tab-pane
                label="提交子流程"
                name="demand"
            >
                <submit-flow :user-org-id="userOrgId" />
            </el-tab-pane>
            <el-tab-pane
                label="验收子流程"
                name="demandMore"
            >
                <acceptance-flow :user-org-id="userOrgId" />
            </el-tab-pane>
            <el-tab-pane
                label="成本分摊确认"
                name="apportion"
            >
                <apportion-config :user-org-id="userOrgId" />
            </el-tab-pane>
            <el-tab-pane
                label="流程操作约束"
                name="demandTransitionRule"
            >
                <demand-transition-rule
                    :user-org-id="userOrgId"
                    :user-org-name="userOrgName"
                />
            </el-tab-pane>
            <el-tab-pane
                label="预估工时审批"
                name="hours"
                v-if="false"
            >
                <apportion-hours
                    :user-org-id="userOrgId"
                    :user-org-name="userOrgName"
                />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import DemandFlow from '@/models/demandFlow';
import submitFlow from './components/submitFlow';
import acceptanceFlow from './components/acceptanceFlow';
import apportionConfig from './components/apportionConfig';
import apportionHours from './components/apportionHours';
import demandTransitionRule from './components/demandTransitionRule';

export default {
    components: {
        submitFlow,
        acceptanceFlow,
        apportionConfig,
        apportionHours,
        demandTransitionRule,
    },
    data() {
        return {
            formName: 'demand',
            orgList: [],
            userOrgId: '',
            userOrgName: '',
        };
    },
    mounted() {
        this.getAdminOrgList();
    },
    methods: {
        // 加载部门列表
        getAdminOrgList() {
            DemandFlow.getOrgList().then((data) => {
                this.orgList = data;
                if (this.orgList.length > 0) {
                    this.userOrgId = this.orgList[0].orgId;
                }
            });
        },
        changeUserOrg(userOrgId) {
            this.orgList.forEach((item) => {
                if (item.orgId === userOrgId) {
                    this.userOrgName = item.orgName;
                }
            });
        },
    },
};
</script>

<style lang="less">
    .demand-flow__main{
        padding: 20px;
        overflow-y: auto;
        &__org{
            & .el-input{
                width: 400px;
            }
        }
    }
</style>
