<template>
    <div class="member-management" v-if="jurisdicList['archOrgAdmin:member:view'] === 0">
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="负责部门">
                <el-autocomplete
                    v-model="formInline.userOrgName"
                    :fetch-suggestions="loadOrglist"
                    placeholder="请输入部门关键字"
                    :trigger-on-focus="false"
                    @select="handleSelect"
                    value-key="fullName"
                    label="fullName"
                    clearable
                />
            </el-form-item>
            <el-form-item label="管理员">
                <jacp-input-users
                    :maxCount="1"
                    v-model="formInline.user"
                    placeholder="请输入姓名"
                    prefix-icon="el-icon-search"
                />
                <!-- <i class="el-icon-search" style="float:left"/> -->
            </el-form-item>
            <el-form-item label="操作人">
                <jacp-input-users
                    :maxCount="1"
                    v-model="formInline.quser"
                    placeholder="请输入操作人"
                    prefix-icon="el-icon-search"
                />
            </el-form-item>
            <el-form-item label="操作时间">
                <el-date-picker
                    v-model="formInline.time"
                    type="daterange"
                    placeholder="请选择开始日期～结束日期"
                />
            </el-form-item>
            <el-form-item>
                <el-button @click="reset">重置</el-button>

                <el-button @click="search" v-if="jurisdicList['archOrgAdmin:member:search'] === 0">查询</el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archOrgAdmin:member:search'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="primary"
                        style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                    >
                        查询
                    </el-button>
                </el-tooltip>
                <el-button type="primary" @click="addmember" v-if="jurisdicList['archOrgAdmin:member:add'] === 0">新增</el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archOrgAdmin:member:add'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="primary"
                        style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                    >
                        新增
                    </el-button>
                </el-tooltip>
                <el-button
                    type="danger"
                    plain @click="del"
                    v-if="jurisdicList['archOrgAdmin:member:del'] === 0">批量删除</el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archOrgAdmin:member:del'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="primary"
                        style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                    >
                        批量删除
                    </el-button>
                </el-tooltip>
            </el-form-item>
        </el-form>
        <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            :row-class-name="tableRowClassName"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="50"
                :selectable="selectable"
            />
            <el-table-column
                label="负责部门"
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    {{ scope.row.ownedOrgName }}
                </template>
            </el-table-column>
            <el-table-column
                label="管理员"
            >
                <template slot-scope="scope">
                    {{ scope.row.userName }}({{ scope.row.userErp }})
                </template>
            </el-table-column>
            <el-table-column
                label="所属部门"
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    {{ scope.row.userOrgName }}
                </template>
            </el-table-column>
            <!-- <el-table-column
                label="负责部门"
                v-if="activeItem.code !== 'superAdmin'"
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    {{ scope.row.ownedOrgName }}
                </template>
            </el-table-column> -->
            <el-table-column
                label="操作人"
            >
                <template slot-scope="scope">
                    {{ scope.row.opUserName }}({{ scope.row.opUserErp }})
                </template>
            </el-table-column>
            <el-table-column
                label="操作时间"
                width="160"
            >
                <template slot-scope="scope">
                    {{ scope.row.opTime }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 第三行，分页插件 -->
        <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="handleSizeChange"
                @current-change="onClickChangePage"
                :page-size="this.pageSize"
                :total="this.total"
                :current-page.sync="currentPage"
                layout="total, sizes, prev, pager, next"
            />
            <!--  sizes, -->
        </div>
        <!-- 新增成员管理 -->
        <el-dialog
            :close-on-click-modal="false"
            title="添加部门管理员"
            :visible.sync="visible"
            @close="close"
            width="464px"
            class="menber-managment-dialog"
        >
            <el-form ref="addFormRef" :model="add" :rules="rules" label-width="200">
                <el-form-item label="姓名" prop="user">
                    <jacp-input-users
                        :maxCount="1"
                        v-model="add.user"
                        placeholder="请输入姓名~"
                        prefix-icon="el-icon-search"
                    />
                </el-form-item>
                <el-form-item label="所属部门">
                    <el-input disabled :value="add.org" placeholder="暂无数据，请输入管理员姓名～" />
                </el-form-item>
                <el-form-item label="负责部门" prop="ownedOrgName">
                    <el-autocomplete
                        v-model="add.ownedOrgName"
                        style="width: 100%"
                        :fetch-suggestions="loadOrglist"
                        placeholder="请输入部门关键字"
                        :trigger-on-focus="false"
                        @select="handleSelectOrg"
                        value-key="fullName"
                        label="fullName"
                    />
                </el-form-item>
            </el-form>
            <div style=" display: flex;justify-content: space-between;">
                <div>
                    <el-checkbox v-model="checked">继续创建</el-checkbox>
                    <!-- <el-button type="primary" @click="save(1)">保存并继续</el-button> -->
                </div>
                <div>
                    <el-button @click="close">取消</el-button>
                    <el-button type="primary" @click="save(false)">保存</el-button>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            :close-on-click-modal="false"
            title="删除部门管理员"
            :visible.sync="delvisible"
            @close="close"
            width="464px"
            class="menber-managment-dialog"
        >
            <el-form>
                <el-form-item>
                    <span>确认删除部门管理员</span>
                    <span :key="i.id" v-for="(i, index ) in multipleSelection">
                        <span style="color:#2695F1;">
                            {{ i.userName }}({{ i.userErp }})
                        </span>
                        <span v-if="index != multipleSelection.length -1">、</span>
                    </span>
                    <span>吗？</span>
                </el-form-item>
                <el-form-item style="text-align: right; margin-bottom:0;">
                    <el-button @click="close(1)">取消</el-button>
                    <el-button type="primary" @click="delEnter">确认</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';
import AdminOption from '@/models/adminOption';
import {
    getOrgListMe,
    queryAppCodeByRoleMember,
    addOrgAdmin,
    queryOrgAdmin,
    deleteOrgAdmin,
} from './security/uaos';
import { mapState } from 'vuex';

export default {
    name: 'adminOption',
    props: {
        roleList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            visible: false,
            userOrgId: '',
            formInline: {
                user: [],
                region: '',
                quser: [],
                time: [],
            },
            tableData: [],
            multipleSelection: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            add: {
                user: [],
                org: '',
                userOrgName: '',
                userOrgId: '',
                checkedCities: [],
            },
            rules: {
                user: [
                    { required: true, message: '请输入姓名', trigger: 'change' },
                ],
                ownedOrgName: [
                    { required: true, message: '请选择负责部门', trigger: 'change' },
                ],
            },
            delvisible: false, // 删除弹出框
            checked: false,
            radio: 'yes',
            appList: [],
            checkAll: false,
            isIndeterminate: true,
            appDataShow: false,
        };
    },
    watch: {
        'add.user': {
            handler(newval) {
                if (newval.length) {
                    this.add.org = newval[0].orgTierName;
                    this.getAppByAdmin();
                    this.appDataShow = true;
                } else {
                    this.add.org = '';
                    this.appDataShow = false;
                }
            },
        },
        'formInline.userOrgName': {
            handler(val) {
                if (val === '') {
                    this.userOrgId = '';
                }
            },
        },
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    methods: {
        tableRowClassName({ row }) {
            if (row.builtIn) {
                return 'info-row';
            }
            return '';
        },
        selectable(row) {
            return !row.builtIn;
        },
        getKeyObject() {
            return {
                superAdmin: 'sa',
                orgAdmin: 'oa',
                childrenOrgAdmin: 'coa',
            };
        },
        // 删除按钮
        del() {
            if (this.multipleSelection.length === 0) {
                this.$message({
                    type: 'warning',
                    message: '请选择要删除的部门管理员',
                });
            } else {
                this.delvisible = true;
            }
        },
        // 确认删除
        async delEnter() {
            // const roleCode = this.activeItem.code;
            // const roleType = this.getKeyObject()[roleCode] || 'cust';
            const ids = this.multipleSelection.map(item => item.id);
            // console.log('=====del=ids====', ids);
            let data = null;
            data = await deleteOrgAdmin({ ids });
            if (data) {
                this.$message({
                    type: 'success',
                    message: '删除成功',
                });
                this.delvisible = false;
                this.search();
            }
        },
        // 重置
        reset() {
            this.formInline = {
                user: [],
                region: '',
                quser: [],
                time: [],
            };
            this.userOrgId = '';
            if (this.$refs.multipleTable) {
                this.$refs.multipleTable.clearSelection();
            }
            this.search();
        },
        clearTable() {
            this.tableData = [];
        },
        // 搜索
        search() {
            const adminErp = this.formInline.user.length ? this.formInline.user[0].erp : undefined;
            const userOrgCode = this.userOrgId ? this.userOrgId : undefined;
            const opEndDate = this.formInline.time && this.formInline.time.length ? moment(this.formInline.time[1]).format('YYYY-MM-DD') : undefined;
            const opStartDate = this.formInline.time && this.formInline.time.length ? moment(this.formInline.time[0]).format('YYYY-MM-DD') : undefined;
            const userErps = this.formInline.quser.length ? this.formInline.quser[0].erp : undefined;
            const page = {
                index: this.currentPage, // 当前页
                size: this.pageSize, // 每页多少
            };
            this.getroleList({
                opUserErp: userErps, // 用户Erp
                ownedOrgCode: userOrgCode, // 用户组织编码
                opEndDate, // 操作结束时间
                opStartDate, // 操作开始时间
                adminErp, // 操作用户Erp
                page, // 分页
            });
        },
        // onSubmit() {},
        save() {
            // const roleCode = this.activeItem.code;
            // const roleType = this.getKeyObject()[roleCode] || 'cust';
            this.$refs.addFormRef.validate((valid) => {
                if (valid) {
                    const userErp = this.add.user[0].erp;
                    addOrgAdmin({
                        ownedOrgCode: this.add.ownedOrgCode,
                        userErp,
                    }).then(() => {
                        this.$message({
                            type: 'success',
                            message: '添加成功',
                        });
                        this.search();
                        // 保持并继续
                        if (this.checked) {
                            this.clearVal();
                        } else {
                            this.close();
                        }
                    });
                }
            });
        },
        // 清除弹出层数据
        clearVal() {
            this.add = {
                user: [],
                org: '',
                userOrgName: '',
                userOrgId: '',
                checkedCities: [],
            };
            this.checkAll = false;
        },
        // 添加人员addMember
        addmember() {
            // console.log(addMember);
            AdminOption.queryAppList().then((res) => {
                this.appList = res;
                this.visible = true;
            });
        },
        getAppByAdmin() {
            const userErp = this.add.user[0].erp;
            queryAppCodeByRoleMember({ userErp, roleCode: 'admin' }).then((res) => {
                const data = [];
                res.forEach((item) => {
                    data.push(item.appCode);
                });
                this.appList.forEach((item) => {
                    if (data.includes(item.code)) {
                        item.disabled = true;
                    } else {
                        item.disabled = false;
                    }
                });
                console.log(this.appList, 'appList');
            });
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.search();
        },
        close(type) {
            if (type === 1) {
                this.delvisible = false;
            } else {
                this.visible = false;
                this.clearVal();
            }
        },
        onClickChangePage(index) {
            this.currentPage = index;
            this.search();
        },
        // getOrgListMe
        loadOrglistOwne(orgInfo, callback) {
            if (orgInfo) {
                getOrgListMe({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data);
                });
            } else {
                // 清空原部门信息
                this.add.userOrgName = '';
                this.add.userOrgId = '';
            }
        },
        // 加载部门列表
        loadOrglist(orgInfo, callback) {
            if (orgInfo) {
                AdminOption.getOrgList({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data.records);
                });
            } else {
                // 清空原部门信息
                this.userOrgId = '';
                this.formInline.userOrgName = '';
            }
        },
        handleSelect(data) {
            console.log('==========handleSelect===========', data);
            this.userOrgId = data.id;
            this.formInline.userOrgName = data.fullName;
        },
        handleSelectOrg(data) {
            console.log('==========handleSelect===========', data);
            this.add.ownedOrgCode = data.id;
            // this.formInline.userOrgName = data.fullName;
        },
        // handleSelectOrgN(data) {
        //     console.log('==========handleSelect===========', data);
        //     this.formInline.ownedOrgCode = data.id;
        //     // this.formInline.userOrgName = data.fullName;
        // },
        handleSelectMe(data) {
            console.log('==========handleSelectMe===========', data);
            this.add.userOrgId = data.code;
            this.add.userOrgName = data.fullName;
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        getroleList(code) {
            const {
                ownedOrgCode, // 角色编码
                opUserErp, // 用户Erp
                opEndDate, // 操作结束时间
                opStartDate, // 操作开始时间
                adminErp, // 操作用户Erp
                page = {
                    index: this.currentPage, // 当前页
                    size: this.pageSize, // 每页多少
                },
            } = code;
            queryOrgAdmin({
                ownedOrgCode,
                opUserErp,
                opEndDate,
                opStartDate,
                adminErp,
                page,
            }).then((res) => {
                console.log('==res==', res.records);
                this.tableData = res.records;
                this.total = res.total;
            });
        },
        handleCheckAllChange(val) {
            const data = [];
            this.appList.forEach((item) => {
                data.push(item.code);
            });
            this.add.checkedCities = val ? data : [];
            // if (val) {
            //     this.$set(this.add, 'checkedCities', data);
            // } else {
            //     this.$set(this.add, 'checkedCities', []);
            // }
            this.isIndeterminate = false;
        },
        handleCheckedCitiesChange(value) {
            const data = [];
            this.appList.forEach((item) => {
                data.push(item.code);
            });
            const checkedCount = value.length;
            this.checkAll = checkedCount === data.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < data.length;
        },
    },
    mounted() {
        // console.log(this.activeItem, '====activeItem====');
        this.search();
    },
};
</script>
<style lang="less">
.member-management{
    margin: 24px;
    .menber-managment-dialog{
        .el-dialog__header{
            border-bottom: 1px solid #EBEEF5;
        }
    }
    .info-row{
        background-color: #f0f2f5;
    }
}
</style>
<style lang="less" scoped>
.member-management{
    display: flex;
    flex-direction: column;
    .search{

    }
    .el-form-item__content{
        width: 300px;
    }
    .footer{
        height: 30px;
        margin-top: 20px;
        text-align: right;
    }
}
</style>
