<template>
    <div class="global-setting" v-if="jurisdicList['archDeptSpecial:handover:view'] === 0">
        <el-form
            :inline="true"
            class="white-list__org"
            label-position="left"
            style="border-bottom:1px solid rgb(235, 238, 245)"
        >
            <el-form-item
                label="部门"
                prop="userOrgId"
                style="padding:20px 0 0 20px;"
            >
                <!-- <el-select
                    v-model="userOrgId"
                    placeholder="请选择"
                    filterable
                    :default-first-option="true"
                >
                    <el-option
                        v-for="item in orgList"
                        :key="item.orgId"
                        :value="item.orgId"
                        :label="item.orgName"
                    >
                        <span style="float: left">{{ item.orgName }}</span>
                        <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                    </el-option>
                </el-select> -->
                <el-autocomplete
                    class="userorgname"
                    v-model="userName"
                    :fetch-suggestions="loadOrglist"
                    placeholder="请输入部门关键字"
                    :trigger-on-focus="false"
                    @select="handleSelect"
                    value-key="fullName"
                    label="fullName"
                    clearable
                />
            </el-form-item>
        </el-form>
        <el-tabs
            v-model="activeName"
            type="border-card"
            style="border:none;box-shadow: unset;"
        >
            <!-- <el-tab-pane
                label="自动验收通过"
                name="autoDemandPass"
            >
                <global-setting-demand-hook
                    :org-id="userOrgId"
                />
            </el-tab-pane> -->
            <el-tab-pane
                label="权限转移"
                name="handover"
                lazy
                v-if="jurisdicList['archDeptSpecial:handover:view'] === 0"
            >
                <global-setting-handover
                    :org-id="userOrgId"
                    :org-name="userOrgName"
                />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import globalSettingHandover from './globalSettingHandover';
// import globalSettingDemandHook from './globalSettingDemandHook';
import FormOption from '@/models/formOption';
import { mapState } from 'vuex';
import AdminOption from '@/models/adminOption';

export default {
    components: {
        globalSettingHandover,
        // globalSettingDemandHook,
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            // tips: state => state.option.tips,
        }),
    },
    data() {
        return {
            activeName: 'handover',
            orgList: [],
            userOrgId: '',
            userOrgName: '',
            userName: '',
        };
    },
    mounted() {
        this.getAdminOrgList();
    },
    methods: {
        // 加载部门列表
        loadOrglist(orgInfo, callback) {
            if (orgInfo) {
                AdminOption.getOrgList({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data.records);
                    this.orgList = data.records;
                });
            } else {
                // 清空原部门信息
                this.userOrgId = '';
                this.userName = '';
            }
        },
        // 加载部门列表
        getAdminOrgList() {
            FormOption.getAdminOrgList().then((data) => {
                this.orgList = data;
                if (this.orgList.length > 0) {
                    this.userOrgId = this.orgList[0].orgId;
                }
            });
        },
        handleSelect(data) {
            console.log('==========handleSelect===========', data);
            this.userOrgId = data.parentIds;
            this.userName = data.fullName;
        },
    },
    watch: {
        userOrgId(n) {
            this.orgList.forEach((item) => {
                if (item.parentIds === n) {
                    this.userOrgName = item.fullName;
                }
            });
        },
    },
};
</script>
<style lang="less">
    .global-setting {
        height: calc(~"100% - 50px");
        // padding: 20px;
    }
    .white-list__org{
        & .el-input{
            width: 400px;
        }
    }
    .userorgname{
        width: 400px;
        .el-autocomplete{
            width: 100%;
        }
    }
</style>
