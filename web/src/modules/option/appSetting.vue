<template>
    <div class="app-setting">
        <el-tabs
            v-model="activeName"
            type="border-card"
            style="height: 100%"
        >
            <el-tab-pane
                label="应用注册"
                name="register"
                :lazy="true"
                v-if="jurisdicList['archConfig:version:view'] === 0"
            >
                <module-option />
            </el-tab-pane>
            <!-- <el-tab-pane
                label="应用菜单"
                name="menus"
                :lazy="true"
            >
                <app-menus />
            </el-tab-pane> -->
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import moduleOption from './moduleOption';
// import appMenus from './appMenus';
import { mapState } from 'vuex';

export default {
    components: {
        moduleOption,
        // appMenus,
    },
    data() {
        return {
            activeName: 'register',
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
        }),
    },
};
</script>
<style lang="less">
.app-setting {
    height: calc(~"100% - 24px");
    margin: 24px 24px 0 16px;
    & .el-tabs__content {
        overflow-y: auto;
        height: 100%;
    }
}
</style>
