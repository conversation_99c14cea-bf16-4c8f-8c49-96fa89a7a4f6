<template>
    <div class="appadmin" v-if="jurisdicList['archAppAdmin:member:view'] === 0">
        <div class="header">
            <h4>应用管理员设置</h4>
        </div>
        <!-- <div class="search">
            <el-button
                type="primary"
                @click="onSubmit"
                v-if="jurisdicList['archAppAdmin:member:add'] === 0 && !edit"
            >
                编辑
            </el-button>
            <el-tooltip
                v-else-if="jurisdicList['archAppAdmin:member:add'] === 1"
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    type="primary"
                    style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                >
                    编辑
                </el-button>
            </el-tooltip>
            <el-button
                v-if="edit"
                @click="edit = !edit"
            >
                取消
            </el-button>
            <el-button
                v-if="edit"
                type="primary"
                @click="onSave"
            >
                保存
            </el-button>
        </div> -->
        <div class="table">
            <el-table
                :data="tableData"
                style="width: 100%"
                tooltip-effect="dark"
            >
                <el-table-column
                    prop="name"
                    label="应用"
                    show-overflow-tooltip
                    width="200"
                >
                    <template slot-scope="scope">
                        <div class="table_name">
                            <img :src="scope.row.icon" alt="">
                            <span>{{ scope.row.name }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="admin"
                    label="管理员"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <form-users-group
                            class="latest-detail-followers__editor"
                            v-model="scope.row.user"
                            :no-timline-users="[$store.state.user]"
                            storage-key="demand_followers"
                            placeholder="请选择"
                            :max-visible-item-length="10"
                            :expand-all="false"
                            :disabled="jurisdicList['archAppAdmin:member:add'] !== 0"
                            :margin-left="8"
                            @on-add="handleAdd"
                            @on-delete="handleDel"
                            @input="handleInput(scope.row)"
                            style="float: left;"
                        />
                        <span
                            v-if="scope.row.user && scope.row.user.length > 10"
                            class="adminSpan"
                        >
                            <el-popover
                                placement="bottom"
                                trigger="hover"
                            >
                                <form-users-group
                                    class="latest-detail-followers__editor"
                                    v-model="scope.row.userHover"
                                    :no-timline-users="[$store.state.user]"
                                    :add-reference="false"
                                    storage-key="demand_followers"
                                    placeholder="请选择"
                                    :disabled="jurisdicList['archAppAdmin:member:add'] !== 0"
                                    @on-add="handleAdd"
                                    @on-delete="handleDel"
                                    @input="handleInput(scope.row)"
                                />
                                <span slot="reference">+{{ scope.row.user.length - 10 }}</span>
                            </el-popover>
                        </span>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    prop="operator"
                    label="最后操作人"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope" v-if="scope.row.operator.name">
                        <span>{{ scope.row.operator.name }}({{ scope.row.operator.erp }})</span>
                    </template>
                </el-table-column> -->
            </el-table>
        </div>
        <!-- <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="init"
                @current-change="init"
                :page-size.sync="pagesize"
                :total="total"
                :current-page.sync="currentPage"
                layout="prev, pager, next, total, sizes, jumper"
            />
        </div> -->
    </div>
</template>

<script>
import {
    queryRoleMemberAppAdmin, addRoleMemberAppAdmin,
    deleteMemberAppAdmin,
} from './security/uaos';
import { mapState } from 'vuex';

export default {

    data() {
        return {
            searchData: {

            },
            tableData: [],
            edit: false,
            changeCode: '',
            changeName: '',
            // currentPage: 1,
            // pagesize: 5,
            // total: 0,
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    methods: {
        onSubmit() {
            this.edit = true;
        },
        init() {
            this.tableData = [];
            queryRoleMemberAppAdmin().then((res) => {
                res.forEach((item) => {
                    let userHover = [];
                    if (item.userList.length > 10) {
                        userHover = item.userList.slice(10);
                    }
                    this.tableData.push({
                        code: item.code,
                        name: item.name,
                        icon: item.icon,
                        user: item.userList,
                        userHover,
                        operator: {
                            name: item.opUserName,
                            erp: item.opUserErp,
                        },
                    });
                });
            });
        },
        handleAdd(value) {
            addRoleMemberAppAdmin({
                roleCode: 'admin',
                appCode: this.changeCode,
                userErp: value.erp,
                appName: this.changeName,
            }).then(() => {
                this.$message.success('添加成功！');
                this.init();
            });
        },
        handleDel(value) {
            deleteMemberAppAdmin({
                roleCode: 'admin',
                userErp: value.erp,
                appCode: this.changeCode,
                appName: this.changeName,
            }).then(() => {
                this.$message.success('删除成功！');
                this.init();
            });
        },
        handleInput(value) {
            this.changeCode = value.code;
            this.changeName = value.name;
        },
    },
};
</script>

<style lang="less" scoped>
.appadmin {
    margin: 24px;
    .search {
        text-align: right;
        margin-right: 30px;
    }
    .footer {
        padding: 24px;
        .j-pagination {
            text-align: right;
        }
    }
    img {
        width: 40px;
        height: 40px;
        vertical-align: middle;
        border-radius: 5px;
        margin-right: 10px;
        margin-left: 3px;
    }
    .adminSpan {
        display: inline-block;
        width: 32px;
        height: 32px;
        background: #dcdfe6;
        border-radius: 50%;
        line-height: 32px;
        font-size: 14px;
        text-align: center;
        margin: 4px;
    }
    .table_name {
        display: flex;
        flex-direction: column;
    }
}
</style>
