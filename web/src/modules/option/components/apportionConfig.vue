<template>
    <div class="apportion__main">
        <el-form
            class="white-list__org"
            label-position="left"
            :model="formData"
            label-width="120px"
        >
            <el-form-item
                label="确认人"
                prop="approvers"
            >
                <div><span style="color:red;">*</span>当同一部门配置了多个确认人时，任何一人确认则该成本分摊信息将被确认</div>
                <jacp-input-users
                    placeholder="确认人姓名"
                    v-model="formData.approvers"
                    style="width: fit-content"
                    :max-count="3"
                    @add="checkUser"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    @click="saveDemandApportion"
                >
                    保存设置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script type="text/javascript">
import DemandApportion from '@/models/demandApportion';

export default {
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            formData: {
                approvers: [],
            },
        };
    },
    methods: {
        getDemandApportionConfig() {
            DemandApportion.getDemandApportion({
                typeId: 3,
                orgId: this.userOrgId,
            }).then((data) => {
                this.formData.approvers = data.approvers;
                this.id = data.id || '';
            });
        },
        saveDemandApportion() {
            DemandApportion.saveDemandApportion({
                id: this.id,
                ownerOrgId: this.userOrgId,
                processType: 3,
                executionType: 2,
                approvers: this.formData.approvers.map((item, index) => Object.assign(item, {
                    id: index + 1,
                })),
                disabled: 0,
            }).then(() => {
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
                this.getDemandApportionConfig();
            });
        },
        checkUser(user) {
            // const { approvers } = this.formData;
            // 不能重复添加同一确认人
            if (user && user.orgTierCode && !user.orgTierCode.startsWith(this.userOrgId)) {
                this.$message.warning({
                    title: '操作提示',
                    message: '当前所配置的确认人不在该部门',
                });
                // this.formData.approvers.splice(approvers.length - 1, 1);
            }
        },
    },
    watch: {
        // 根据当前部门信息查询流程信息
        userOrgId(userOrgId) {
            if (userOrgId) {
                this.getDemandApportionConfig();
            }
        },
    },
};
</script>

<style lang="less">
    .apportion__main{
        & .el-table__body-wrapper{
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
    .apportion__table{
        border-collapse: collapse;
        border: 1px solid #ebf1f3;
        & thead{
            background-color: #eef3f6;
            & th{
                width: 200px;
            }
        }
        & td{
            height: 40px;
            line-height: 40px;
            text-align: center;
        }
    }
</style>
