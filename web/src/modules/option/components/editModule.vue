<template>
    <el-dialog
        title="应用注册"
        :visible.sync="showEditModule"
        width="60%"
        @close="$emit('update:showEditModule', false)"
    >
        <el-form
            label-position="right"
            label-width="220px"
            class="add-admin-pop"
            :model="formData"
            ref="formIns"
            :rules="rules"
        >
            <el-form-item
                label="应用名称"
                prop="name"
            >
                <el-input
                    placeholder="请输入内容"
                    v-model="formData.name"
                />
            </el-form-item>
            <el-form-item
                label="应用标识"
                prop="code"
            >
                <el-input
                    v-model="formData.code"
                    placeholder="请输入内容"
                    readonly
                />
            </el-form-item>
            <el-form-item
                label="应用域名url"
                prop="url"
            >
                <el-input
                    v-model="formData.url"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="应用类型"
                prop="type"
            >
                <el-input
                    v-model="formData.type"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="上级应用"
                prop="parentRouterName"
            >
                <el-input
                    v-model="formData.parentRouterName"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="入口路由"
                prop="topRooter"
            >
                <el-input
                    v-model="formData.topRooter"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="权限码"
                prop="permissionCode"
            >
                <el-input
                    v-model="formData.permissionCode"
                    placeholder="应用可见权限码，没有请在权限系统申请"
                />
            </el-form-item>
            <el-form-item
                label="是否挂载一级菜单"
                prop="topMenuMounted"
            >
                <el-checkbox
                    v-model="formData.topMenuMounted"
                    :true-label="1"
                    :false-label="0"
                />
            </el-form-item>
            <el-form-item
                label="关联应用"
                prop="appId"
            >
                <el-select
                    v-model="formData.appId"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in appList"
                        :key="item.id"
                        :value="item.id"
                        :label="item.name"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="ICON"
                prop="icon"
            >
                <el-input
                    v-model="formData.icon"
                    placeholder="icon class，没有请提前设计"
                />
            </el-form-item>
            <el-form-item
                label="排序"
                prop="index"
            >
                <el-input
                    v-model="formData.index"
                    placeholder="排序index"
                />
            </el-form-item><br>
        </el-form>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button
                    type="primary"
                    @click="saveData"
                >确 定</el-button>
                <el-button @click="$emit('update:showEditModule', false)">取 消</el-button>
            </span>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import ModuleOption from '@/models/moduleOption';

export default {
    props: {
        showEditModule: {
            type: Boolean,
            default: false,
        },
        moduleId: {
            type: Number,
        },
        appList: {
            type: Array,
            default: () => [],
        },
        appId: {
            type: String,
        },
    },
    data() {
        return {
            formData: {
                name: '',
                code: '',
                url: '',
                type: 'page',
                parentRouterName: 'root',
                icon: '',
                topRooter: '',
                appId: '',
                index: 99,
                permissionCode: '',
                topMenuMounted: 1,
            },
            rules: {
                name: [{
                    required: true,
                    message: '应用名称不能为空',
                    trigger: 'blur',
                }],
                code: [{
                    required: true,
                    message: '应用标识不能为空',
                    trigger: 'blur',
                }],
                url: [{
                    required: true,
                    message: 'url不能为空',
                    trigger: 'blur',
                }],
                type: [{
                    required: true,
                    message: '类型不能为空',
                    trigger: 'blur',
                }],
                parentRouterName: [{
                    required: true,
                    message: '上级应用不能为空',
                    trigger: 'blur',
                }],
                icon: [{
                    required: true,
                    message: 'ICON不能为空',
                    trigger: 'blur',
                }],
                topRooter: [{
                    required: true,
                    message: '入口路由不能为空',
                    trigger: 'blur',
                }],
                appId: [{
                    required: true,
                    message: '关联应用不能为空',
                    trigger: 'blur',
                }],
            },
        };
    },
    methods: {
        saveData() {
            this.$refs.formIns.validate((valid) => {
                if (valid) {
                    const param = {
                        id: this.formData.id,
                        code: this.formData.code,
                        name: this.formData.name,
                        desc: '',
                        index: this.formData.index,
                        appId: this.formData.appId,
                        permissionCode: this.formData.permissionCode,
                        topMenuMounted: this.formData.topMenuMounted,
                        register: {
                            key: this.formData.code,
                            name: this.formData.name,
                            url: this.formData.url,
                            topRooter: this.formData.topRooter,
                            type: this.formData.type,
                            parentRouterName: this.formData.parentRouterName,
                            icon: this.formData.icon,
                        },
                    };
                    ModuleOption.editModule(param).then(() => {
                        this.$emit('update:showEditModule', false);
                        // 刷新列表
                        this.$emit('refresh-list');
                        this.$notify({
                            title: '保存成功',
                            type: 'success',
                            duration: 2000,
                        });
                    });
                } else {
                    throw new Error(valid);
                }
            });
        },
    },
    watch: {
        showEditModule(show) {
            const isShow = show;
            if (isShow) {
                ModuleOption.getModuleDetail(this.moduleId).then((data) => {
                    Object.assign(this.formData, {
                        id: data.id,
                        name: data.name,
                        code: data.code,
                        url: data.register.url,
                        topRooter: data.register.topRooter,
                        type: data.register.type,
                        parentRouterName: data.register.parentRouterName,
                        appId: data.appId,
                        icon: data.register.icon,
                        index: data.index,
                        permissionCode: data.permissionCode,
                        topMenuMounted: data.topMenuMounted,
                    });
                });
            }
        },
    },
};
</script>
<style lang="less">
 .add-admin-pop{
    & datacenter-chart.el-input{
        width: 400px;
    }
 }
 .option-admin-auto{
    & li{
        white-space: inherit;
        line-height: 26px;
        font-size: 12px;
        border-bottom: 1px solid #dcdbdb;
        &:nth-of-type(2n){
            background-color: #f9f9f9;
        }
        &.highlighted{
            background-color: #08a4da;
            color: #fff;
        }
    }
}
</style>
