<template>
    <div class="acceptance-flow__main">
        <el-form
            class="white-list__org"
            label-position="left"
            :model="formData"
            label-width="120px"
        >
            <el-form-item
                label="启用审批流程"
                prop="enabled"
            >
                <el-switch
                    v-model="formData.enabled"
                    active-color="#08a4da"
                />
            </el-form-item>
            <el-form-item
                label="审批类型"
                prop="executionType"
            >
                <el-select
                    placeholder="流程类型"
                    v-model="formData.executionType"
                >
                    <el-option
                        v-for="item in approvetypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="审批人"
                prop="approvers"
            >
                <table class="acceptance-flow__table">
                    <thead>
                        <tr><th>审批人</th><th>操作</th></tr>
                    </thead>
                    <tbody>
                        <tr
                            v-for="(row, index) in formData.approvers"
                            :key="index"
                        >
                            <td>
                                <jacp-input-users
                                    placeholder="审批人姓名"
                                    v-model="formData.approvers[index].users"
                                    :max-count="1"
                                    @change="checkUser"
                                />
                            </td>
                            <td>
                                <el-button
                                    type="text"
                                    v-if="formData.approvers.length < 3"
                                    size="small"
                                    @click="formData.approvers.splice(index + 1, 0, {users: []})"
                                >
                                    添加
                                </el-button>
                                <el-button
                                    type="text"
                                    v-if="formData.approvers.length > 1"
                                    size="small"
                                    @click="formData.approvers.splice(index, 1)"
                                >
                                    删除
                                </el-button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    @click="saveDemandFlow"
                >
                    保存设置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script type="text/javascript">
import DemandFlow from '@/models/demandFlow';
import { approvetypes } from '@/models/config';
import Dialog from '@/models/dialog';

export default {
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            formData: {
                enabled: false,
                approvers: [{ users: [] }],
                executionType: 1,
            },
            approvetypeList: approvetypes,
        };
    },
    mounted() {
    },
    methods: {
        getDemandAcceptanceFlow() {
            DemandFlow.getDemandFlow({
                typeId: 2,
                orgId: this.userOrgId,
            }).then((data) => {
                this.formData.enabled = data.disabled === 0;
                this.formData.executionType = data.executionType || 1;
                if (data.approvers.length > 0) {
                    this.formData.approvers = data.approvers.map((item) => {
                        const users = {
                            users: [item],
                        };
                        return users;
                    });
                } else {
                    this.formData.approvers = [{ users: [] }];
                }
                this.id = data.id || '';
            });
        },
        saveDemandFlow() {
            let reviewerId = 1;
            const approversList = [];
            // 去掉中间层数组结构 按顺序添加等级标识
            this.formData.approvers.forEach((item) => {
                const approver = item.users[0];
                if (approver) {
                    approver.id = `reviewer${reviewerId}`;
                    approversList.push(approver);
                    reviewerId += 1;
                }
            });
            // 审批人不能为空
            if (approversList.length === 0) {
                Dialog.alert({
                    title: '操作提示',
                    content: '请选择至少一位审批人',
                    type: 'success',
                });
                return;
            }
            DemandFlow.saveDemandFlow({
                id: this.id,
                ownerOrgId: this.userOrgId,
                processType: 2,
                executionType: this.formData.executionType,
                approvers: approversList,
                disabled: this.formData.enabled ? 0 : 1,
            }).then(() => {
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
                this.getDemandAcceptanceFlow();
            });
        },
        checkUser(user) {
            if (user[0] && user[0].orgTierCode && !user[0].orgTierCode.startsWith(this.userOrgId)) {
                Dialog.alert({
                    title: '操作提示',
                    content: '请选择当前部门下属人员',
                    type: 'success',
                });
                this.formData.approvers.forEach((item, index, arr) => {
                    const approvers = arr;
                    if (user === item) {
                        approvers[index] = { users: [] };
                    }
                });
            }
        },
    },
    watch: {
        // 根据当前部门信息查询流程信息
        userOrgId(userOrgId) {
            if (userOrgId) {
                this.getDemandAcceptanceFlow();
            }
        },
    },
};
</script>

<style lang="less">
    .acceptance-flow__main{
        & .el-table__body-wrapper{
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
    .acceptance-flow__table{
        border-collapse: collapse;
        border: 1px solid #ebf1f3;
        & thead{
            background-color: #eef3f6;
            & th{
                width: 200px;
            }
        }
        & td{
            height: 40px;
            line-height: 40px;
            text-align: center;
        }
    }
</style>
