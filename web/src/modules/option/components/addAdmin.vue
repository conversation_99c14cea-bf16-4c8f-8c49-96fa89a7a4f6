<template>
    <el-form
        label-position="right"
        label-width="120px"
        :rules="rules"
        class="add-admin-pop"
        :model="data"
        ref="formIns"
    >
        <el-form-item
            label="负责部门"
            prop="userOrgName"
        >
            <el-autocomplete
                v-model="data.userOrgName"
                :fetch-suggestions="loadOrglist"
                placeholder="请输入部门关键字"
                :trigger-on-focus="false"
                @select="handleSelect"
                value-key="fullName"
                label="fullName"
                :value="data.userOrgName"
                :select-when-unmatched="true"
            />
        </el-form-item>
        <el-form-item
            label="姓名"
            prop="userInfo"
        >
            <jacp-input-users
                v-model="data.userInfo"
                placeholder="姓名"
                :max-count="1"
            />
        </el-form-item><br>
    </el-form>
</template>
<script type="text/javascript">
import AdminOption from '@/models/adminOption';

export default {
    props: {
        rootOrgList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            data: {
                userOrgId: '',
                userOrgName: '',
                userInfo: [],
            },
            rules: {
                userOrgName: [{
                    required: true,
                    message: '部门不能为空',
                    trigger: 'blur',
                    validator: this.validator,
                }],
                userInfo: [{
                    required: true,
                    message: '管理员姓名不能为空',
                    trigger: 'blur',
                    validator: this.validator,
                }],
            },
        };
    },
    methods: {
        validator(rule, value, callback) {
            const min = rule.required ? 1 : 0;
            if (!value || value.length < min) {
                callback(new Error(rule.message));
            } else {
                callback();
            }
        },
        validateData() {
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject();
                    }
                });
            });
        },
        handleSelect(data) {
            this.data.userOrgId = data.parentIds;
        },
        // 加载部门列表
        loadOrglist(orgInfo, callback) {
            AdminOption.getOrgList({
                keyWord: orgInfo.trim().replace(/\s+/g, ','),
            }).then((data) => {
                callback(data.records);
            });
        },
    },
    watch: {
        userInfo(newValue) {
            this.data.userErp = newValue.length > 0 ? newValue[0].erp : '';
            this.data.userName = newValue.length > 0 ? newValue[0].name : '';
        },
    },
};
</script>
<style lang="less">
 .add-admin-pop{
    & .el-input{
        width: 400px;
    }
 }
 .option-admin-auto{
    & li{
        white-space: inherit;
        line-height: 26px;
        font-size: 12px;
        border-bottom: 1px solid #dcdbdb;
        &:nth-of-type(2n){
            background-color: #f9f9f9;
        }
        &.highlighted{
            background-color: #08a4da;
            color: #fff;
        }
    }
}
</style>
