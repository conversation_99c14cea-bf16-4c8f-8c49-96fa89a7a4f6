<template>
    <div class="acceptance-flow__main">
        <el-form
            class="white-list__org"
            label-position="left"
            :model="formData"
        >
            <h4>需求处理时</h4>
            <el-form-item
                label="分配团队空间或者直接处理时，关联项目为必填项。"
                prop="demandAssignProjectRequire"
            >
                <el-switch
                    v-model="formData.demandAssignProjectRequire"
                    active-color="#08a4da"
                />
            </el-form-item>
            <el-form-item
                label="分配团队空间或者直接处理时，显示关联产品并必填。"
                prop="demandAssignProductRequire"
            >
                <el-switch
                    v-model="formData.demandAssignProductRequire"
                    active-color="#08a4da"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    @click="saveDemandTransitionRule"
                >
                    保存设置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script type="text/javascript">
import GlobalSetting from '@/models/globalSetting';

export default {
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            setting: {},
            formData: {
                demandAssignProjectRequire: false,
                demandAssignProductRequire: false,
            },
        };
    },
    methods: {
        getDemandTransitionRule() {
            GlobalSetting.getSettingOrg('demand-assign-project', this.userOrgId).then((data) => {
                if (data) {
                    this.setting = data.jsonValue;
                    this.formData.demandAssignProjectRequire = data.jsonValue.demandAssignProjectRequire;
                    this.formData.demandAssignProductRequire = data.jsonValue.demandAssignProductRequire;
                }
            });
        },
        saveDemandTransitionRule() {
            this.setting.demandAssignProjectRequire = this.formData.demandAssignProjectRequire;
            this.setting.demandAssignProductRequire = this.formData.demandAssignProductRequire;
            GlobalSetting.updateSettingOrg('demand-assign-project', {
                orgId: this.userOrgId,
                value: this.setting,
                disabled: 0,
            }).then(() => {
                this.$message.success('保存成功。');
            });
        },
    },
    watch: {
        // 根据当前部门信息查询流程信息
        userOrgId(userOrgId) {
            if (userOrgId) {
                this.getDemandTransitionRule();
            }
        },
    },
};
</script>

<style lang="less">
    .acceptance-flow__main{
        & .el-table__body-wrapper{
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
    .acceptance-flow__table{
        border-collapse: collapse;
        border: 1px solid #ebf1f3;
        & thead{
            background-color: #eef3f6;
            & th{
                width: 200px;
            }
        }
        & td{
            height: 40px;
            line-height: 40px;
            text-align: center;
        }
    }
</style>
