<template>
    <div>
        <el-form
            :model="form"
            :rules="rules"
            ref="form"
            label-width="100px"
        >
            <el-form-item
                label="应用"
                prop="appId"
            >
                <el-select
                    v-model="form.appId"
                    style="width: 80%"
                    :disabled="isDisabled"
                    @change="appChange"
                >
                    <el-option
                        v-for="item in appItems"
                        :key="item.appId"
                        :label="item.name"
                        :value="item.appId"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="类型"
                v-if="isJACP"
                prop="type"
            >
                <el-select
                    v-model="form.type"
                    style="width: 80%"
                >
                    <el-option
                        v-for="item in typeList"
                        :key="item.key"
                        :label="item.name"
                        :value="item.key"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="组织范围"
                v-if="isJACP"
                prop="scope"
            >
                <!-- <el-select
                    v-model="form.scope"
                    style="width: 80%"
                    filterable
                    clearable
                >
                    <el-option
                        v-for="item in orgList"
                        :key="item.orgId"
                        :value="item.orgId"
                        :label="item.orgName"
                    >
                        <span style="float: left">{{ item.orgName }}</span>
                        <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                    </el-option>
                </el-select> -->
                <el-autocomplete
                    class="userorgname"
                    v-model="form.scope"
                    :fetch-suggestions="loadOrglist"
                    placeholder="请输入部门关键字"
                    :trigger-on-focus="false"
                    @select="handleSelect"
                    value-key="fullName"
                    label="fullName"
                    clearable
                />
            </el-form-item>
            <el-form-item
                label="扩展表单名"
                prop="schemaName"
            >
                <el-input
                    v-model="form.schemaName"
                    placeholder="请输入"
                    style="width: 80%"
                />
            </el-form-item>
        </el-form>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-button
                type="primary"
                @click="submit('form')"
            >
                确 定
            </el-button>
            <el-button
                @click="resetForm('form')"
            >
                取 消
            </el-button>
        </div>
    </div>
</template>
<script type="text/javascript">
import DynamicFormModule from '@/models/dynamicForm';
import AdminOption from '@/models/adminOption';

const JACP_APP_CODE = 'teamspace';

export default {
    props: {
        schema: { type: Object, default() {} },
        isCreate: { type: Boolean, default: true },
        isDisabled: { type: Boolean, default: false },
        appItems: { type: Array, default() {} },
        orgList: { type: Array, default() {} },
    },
    data() {
        const checkSpecialKey = (str) => {
            const pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]");
            if (pattern.test(str)) {
                return false;
            }
            return true;
        };
        const checkEmpty = fieldName => (rule, value, callback) => {
            if (!value) {
                return callback(new Error(`${fieldName}不能为空`));
            }
            return callback();
        };
        const checkSchemaName = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('表单名不能为空！'));
            }
            if (value.length > 40) {
                return callback(new Error('表单名不能超过40！'));
            }
            if (!checkSpecialKey(value)) {
                return callback(new Error('表单名不能包含特殊字符'));
            }
            return callback();
        };
        return {
            // 类型暂时写死。类型是必填项吗？
            typeList: [
                { key: 'card', name: '卡片' },
                // { key: 'task', name: '任务' },
            ],
            form: {
                appId: '',
                schemaName: '',
                type: '',
                scope: '',
            },
            isJACP: false,
            userOrgId: '',
            rules: {
                appId: [
                    { required: true, validator: checkEmpty('项目'), trigger: ['blur', 'change'] },
                ],
                schemaName: [
                    { required: true, validator: checkSchemaName, trigger: ['blur', 'change'] },
                ],
                type: [
                    { validator: checkEmpty('类型'), trigger: ['blur', 'change'] },
                ],
                scope: [
                    { validator: checkEmpty('组织范围'), trigger: ['blur', 'change'] },
                ],
            },
        };
    },
    methods: {
        handleSelect(data) {
            this.userOrgId = data.parentIds;
            this.form.scope = data.fullName;
        },
        // 加载部门列表
        loadOrglist(orgInfo, callback) {
            if (orgInfo) {
                AdminOption.getOrgList({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data.records);
                });
            } else {
                // 清空原部门信息
                this.userOrgId = '';
                this.form.scope = '';
            }
        },
        closeDialog() {
            this.$emit('closeDialog');
        },
        getFormList() {
            this.$emit('reRender');
        },
        submit(formName) {
            // this.isCreate 为true则是新建，为false则是复制
            if (this.isCreate) {
                this.submitCreate(formName);
            } else {
                this.submitCopy(formName);
            }
        },
        submitCreate(formName) {
            const {
                appId, schemaName, type,
            } = this.form;
            this.$refs[formName].validate((valid) => {
                if (!valid) {
                    return false;
                }
                DynamicFormModule.saveForm({
                    name: schemaName,
                    appId,
                    type,
                    scope: this.userOrgId,
                }).then(() => {
                    this.getFormList();
                    this.closeDialog();
                });
                this.$refs[formName].resetFields();
                return true;
            });
        },
        submitCopy(formName) {
            const { id: schemaId } = this.schema;
            const { schemaName: name, type, scope } = this.form;
            this.$refs[formName].validate((valid) => {
                if (!valid) {
                    return false;
                }
                DynamicFormModule.copyForm({
                    schemaId, name, type, scope,
                }).then(() => {
                    this.getFormList();
                    this.resetForm(formName);
                    this.closeDialog();
                });
                // this.$emit('reRender');
                return true;
            });
        },
        resetForm(formName) {
            if (this.$refs[formName]) {
                this.$refs[formName].resetFields();
                this.$refs[formName].clearValidate();
                this.$refs[formName].clearValidate('appId');
            }
            this.$emit('reset');
            this.closeDialog();
        },
        appChange(id) {
            const code = this.appItems.find(item => item.appId === id)?.appCode;
            if (code === JACP_APP_CODE) {
                this.isJACP = true;
            } else {
                this.isJACP = false;
            }
            this.form.scope = undefined;
            this.form.type = undefined;
        },
    },
    watch: {
        schema: {
            handler() {
                this.form.appId = this.schema.appId;
            },
            immediate: true,
            deep: true,
        },
    },
};
</script>

<style lang="less">
.userorgname{
    width: 290px;
    .el-autocomplete{
        width: 100%;
    }
}
</style>
