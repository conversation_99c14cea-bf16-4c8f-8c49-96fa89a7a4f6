<template>
    <div class="jacp-app-menus">
        <div class="jacp-app-menus__header">
            <span style="padding: 0 8px">
                同级菜单之间可以拖拽排序，
                <span style="color: red">修改确认无误后，请点击保存按钮</span>
            </span>
            <el-button
                size="mini"
                type="primary"
                @click="save"
            >
                保存
            </el-button>
        </div>
        <el-tree
            ref="appMenusTreeRef"
            :data="menuList"
            :props="defaultProps"
            :expand-on-click-node="false"
            :check-strictly="true"
            :allow-drag="allowDrag"
            :allow-drop="allowDrop"
            node-key="code"
            draggable
            show-checkbox
            highlight-current
            default-expand-all
        >
            <span
                class="jacp-app-menus__tree-node"
                slot-scope="{ node, data }"
            >
                <span>{{ node.label }}</span>
                <span>
                    <el-button
                        v-if="node.level < 3"
                        type="text"
                        size="mini"
                        @click="() => showAppendDialog(data)"
                    >
                        Append
                    </el-button>
                    <el-button
                        v-if="!data.disabled"
                        type="text"
                        size="mini"
                        @click="() => editDialog(data)"
                    >
                        Edit
                    </el-button>
                    <el-button
                        v-if="!data.disabled"
                        type="text"
                        size="mini"
                        @click="() => remove(node, data)"
                    >
                        Delete
                    </el-button>
                </span>
            </span>
        </el-tree>
        <el-dialog
            title="追加新菜单"
            :visible.sync="dialogVisible"
            width="30%"
        >
            <el-form
                :model="child"
                ref="appendChildRef"
            >
                <el-form-item
                    prop="name"
                    label="菜单名称"
                    :rules="[{ required: true, message: '请输入菜单名称', trigger: ['blur', 'change'] }]"
                >
                    <el-input v-model="child.name" />
                </el-form-item>
                <el-form-item
                    prop="code"
                    label="菜单code码"
                    :rules="[{ required: true, message: '请输入code码', trigger: ['blur', 'change'] }]"
                >
                    <el-input
                        placeholder="菜单code码，为应路由所在的层级字符"
                        v-model="child.code"
                    />
                </el-form-item>
                <el-form-item
                    prop="routerName"
                    label="菜单路由名"
                    :rules="[{ required: true, message: '请输入菜单路由名', trigger: ['blur', 'change'] }]"
                >
                    <el-input
                        placeholder="菜单路由名，为应路由的唯一name"
                        v-model="child.routerName"
                    />
                </el-form-item>
                <el-form-item
                    prop="permissionCode"
                    label="权限码"
                >
                    <el-input
                        placeholder="权限码"
                        v-model="child.permissionCode"
                    />
                </el-form-item>
            </el-form>
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="dialogVisible = false">Cancel</el-button>
                <el-button
                    type="primary"
                    @click="append"
                >{{ isEdit ? 'Edit' : 'Append' }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script type="text/javascript">
import ModuleOption from '@/models/moduleOption';

const nodeChild = {
    code: null,
    name: null,
    parentId: -1,
    routerName: null,
    permissionCode: null,
};

export default {
    props: {
        value: Number,
    },
    data() {
        return {
            menuList: [],
            currentData: null,
            dialogVisible: false,
            isEdit: false,
            defaultProps: {
                children: 'children',
                label: 'name',
                disabled: data => data.disabled,
            },
            child: { ...nodeChild },
        };
    },
    mounted() {
        if (this.value) {
            ModuleOption.getAppWebMenuList(this.value).then((data) => {
                this.menuList.push(data);

                // 加载默认勾选
                const checkedKeys = [data.code];
                function findCheckedKeys(menus) {
                    if (menus) {
                        menus.forEach((value) => {
                            findCheckedKeys(value.children);
                            if (value.checked) {
                                checkedKeys.push(value.code);
                            }
                        });
                    }
                }
                findCheckedKeys(data.children);
                this.$nextTick(() => {
                    this.$refs.appMenusTreeRef.setCheckedKeys(checkedKeys);
                });
            });
        }
    },
    methods: {
        allowDrag(draggingNode) {
            return !draggingNode.data.disabled;
        },
        allowDrop(draggingNode, dropNode, type) {
            if (dropNode.data.disabled) {
                return type === 'inner';
            }
            if (dropNode.level === draggingNode.level) {
                return type !== 'inner';
            }
            return false;
        },
        editDialog(data) {
            this.child = data;
            this.currentData = data;
            this.isEdit = true;
            this.dialogVisible = true;
        },
        showAppendDialog(data) {
            const { appWebId, id: parentId } = data;
            this.child = Object.assign({ ...nodeChild }, { appWebId, parentId });
            this.currentData = data;
            this.isEdit = false;
            this.dialogVisible = true;
        },
        append() {
            this.$refs.appendChildRef.validate((valid) => {
                if (valid) {
                    if (!this.isEdit) {
                        if (!this.currentData.children) {
                            this.$set(this.currentData, 'children', []);
                        }
                        this.currentData.children.push({ ...this.child });
                    } else {
                        this.currentData = { ...this.child };
                    }
                    this.dialogVisible = false;
                }
            });
        },
        remove(node, data) {
            const tempParent = node.parent;
            const children = tempParent.data.children || tempParent.data;
            const index = children.findIndex(d => d.id === data.id);
            children.splice(index, 1);
        },
        save() {
            function getServerData(nodes) {
                if (nodes.childNodes) {
                    nodes.childNodes.forEach((value, index) => {
                        Object.assign(value.data, { index, status: value.checked ? 1 : 0 });
                        if (value.childNodes.length > 0) {
                            getServerData(value);
                        }
                    });
                }
                return nodes.data.children;
            }
            if (this.menuList.length > 0) {
                const rootNode = this.$refs.appMenusTreeRef.getNode(this.menuList[0].code);
                let serverData = [];
                if (rootNode.childNodes.length > 0) {
                    serverData = getServerData(rootNode);
                }
                ModuleOption.addOrUpdateAppWebMenu(rootNode.data.appWebId, serverData).then((data) => {
                    if (data) {
                        this.$message.success('保存成功');
                    } else {
                        this.$message.error('保存失败，请check数据或刷新重试');
                    }
                });
            }
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-app-menus {

    padding: 24px;

    &__header {
        margin: 10px 0 20px 0;
        font-size: 12px;
        align-items: center;
    }
    & .el-tree-node__content {
        height: 28px;
    }
    &__tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        padding-right: 16px;
    }
}
</style>
