<template>
    <el-form
        label-position="right"
        class="show-roiBase-form"
        :rules="rules"
        ref="formIns"
        :model="data"
    >
        <el-row>
            <el-col :span="12">
                <el-form-item
                    label="指标名称"
                    prop="factorName"
                    required
                >
                    <br>
                    <el-input
                        v-model="data.factorName"
                        maxlength="100"
                        style="width:190px"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item
                    label="指标占比(%)"
                    prop="factorWeight"
                    required
                >
                    <br>
                    <el-input
                        v-model="data.factorWeight"
                        placeholder="请输入0-100之间的数字"
                        style="width:190px"
                    />
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="12">
                <el-form-item
                    label="指标定义"
                    prop="factorDefine"
                >
                    <br>
                    <el-input
                        type="textarea"
                        v-model="data.factorDefine"
                        placeholder="请输入内容"
                        maxlength="300"
                        show-word-limit
                        style="width: 280px"
                        :autosize="{ minRows: 4, maxRows: 8,}"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item
                    label="受理人是否可编辑"
                    prop="ifEdit"
                    required
                >
                    <br>
                    <el-radio
                        v-model="data.ifEdit"
                        label="1"
                        :disabled="globalReadOnly"
                    >
                        是
                    </el-radio>
                    <el-radio
                        v-model="data.ifEdit"
                        label="0"
                        :disabled="globalReadOnly"
                    >
                        否
                    </el-radio>
                </el-form-item>
            </el-col>
        </el-row>
        <div class="line-left-right" />
        <h4>指标枚举值及评分配置</h4>
        <br>
        <el-form-item
            prop="itemsList"
        >
            <table class="submit-factor__table">
                <thead>
                    <tr>
                        <th width="30px">
                            排序号
                        </th>
                        <th width="50px">
                            值
                        </th>
                        <th width="50px">
                            得分
                        </th>
                        <th
                            width="90px"
                            v-if="data.factorSpecial !== 2"
                        >
                            设为默认值
                        </th>
                        <th width="100px">
                            操作
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(row, index) in data.itemsList"
                        :key="index"
                    >
                        <td>
                            <jacp-input-number
                                style="width:130px"
                                v-model="data.itemsList[index].sortNum"
                                controls-position="right"
                                :min="1"
                                :precision="0"
                            />
                        </td>
                        <td>
                            <el-input
                                v-model="data.itemsList[index].itemName"
                                :max-count="1"
                                :readonly="globalReadOnly"
                            />
                        </td>
                        <td>
                            <el-input
                                placeholder="请输入不大于10的整数"
                                v-model="data.itemsList[index].itemValue"
                                :max-count="1"
                                style="width:170px"
                            />
                        </td>
                        <td
                            v-if="data.factorSpecial !== 2"
                        >
                            <el-switch
                                v-model="defaultSwitch[index]"
                                active-color="#08a4da"
                                @change="(s) => switchDefault(s,index)"
                            />
                        </td>
                        <td>
                            <el-button
                                type="text"
                                size="small"
                                icon="el-icon-plus"
                                @click="data.itemsList.splice(index + 1, 0, { items: [], defaultFlag: 0 })"
                                :disabled="globalReadOnly"
                            />
                            <el-button
                                type="text"
                                v-if="data.itemsList.length > 1"
                                size="small"
                                icon="el-icon-close"
                                @click="data.itemsList.splice(index, 1)"
                                :disabled="globalReadOnly"
                            />
                        </td>
                    </tr>
                </tbody>
            </table>
        </el-form-item>
    </el-form>
</template>

<script type="text/javascript">
import PriorityRate from '@/models/priorityRate';

export default {
    props: {
        templateId: {
            type: Number,
            default: 0,
        },
        userOrgCode: {
            type: String,
            default: '',
        },
        factorId: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            defaultSwitch: [],
            data: {
                id: '',
                factorName: '',
                factorWeight: '',
                factorDefine: '',
                tempId: '',
                departmentCode: '',
                ifEdit: '1',
                sortNum: 1,
                factorSpecial: 0,
                created: '',
                itemsList: [{ items: [], defaultFlag: 0 }],
            },
            rules: {
                factorName: [{
                    required: true,
                    message: '指标名称不能为空',
                    trigger: 'blur',
                }],
                factorWeight: [{
                    required: true,
                    trigger: 'blur',
                    validator: (rule, value, callback) => {
                        const pattern = /^\d+(\.\d{1,2})?$/;
                        const min = rule.required ? 1 : 0;
                        if (!value || value.length < min) {
                            callback(new Error('指标占比不能为空'));
                        }
                        if (!pattern.test(value)) {
                            callback(new Error('只能输入两位小数'));
                        }
                        if (value < 0 || value > 100) {
                            callback(new Error('请输入0-100之间的数字'));
                        }
                        callback();
                    },
                }],
                itemsList: [{
                    validator: (rule, value, callback) => {
                        if (!this.data.itemsList.length) {
                            callback(new Error('请配置指标枚举值及评分'));
                        }
                        this.data.itemsList.forEach((items) => {
                            if (!items.itemValue && items.itemValue !== 0 && items.itemValue !== '0') {
                                callback(new Error('评分配置得分项不能为空'));
                            }
                            const pattern = /^-?\d+$/;
                            if (!pattern.test(items.itemValue)) {
                                callback(new Error('请输入整数'));
                            }
                            if (items.itemValue > 10) {
                                callback(new Error('请输入不大于10的整数'));
                            }
                            if (!items.itemName) {
                                callback(new Error('指标枚举值不能为空'));
                            }
                        });
                        callback();
                    },
                }],
            },
            globalReadOnly: false,
        };
    },
    mounted() {
        // 初始化数据
        this.data.tempId = this.templateId;
        this.data.departmentCode = this.userOrgCode;
        this.showFactor(this.factorId);
    },
    methods: {
        switchDefault(s, index) {
            this.data.itemsList.forEach((items) => {
                const count = this.data.itemsList.indexOf(items);
                if (count === index) {
                    if (s) {
                        this.defaultSwitch[index] = true;
                        this.data.itemsList[index].defaultFlag = 1;
                    } else {
                        this.defaultSwitch[index] = false;
                        this.data.itemsList[count].defaultFlag = 0;
                    }
                } else {
                    this.defaultSwitch[count] = false;
                    this.data.itemsList[count].defaultFlag = 0;
                }
            });
        },
        showFactor(factorId) {
            if (factorId !== 0) {
                return PriorityRate.showFactor(factorId).then((resultData) => {
                    if (resultData) {
                        this.data.id = resultData.id;
                        this.data.tempId = resultData.tempId;
                        this.data.departmentCode = resultData.departmentCode;
                        this.data.factorName = resultData.factorName;
                        this.data.factorWeight = resultData.factorWeight;
                        this.data.factorDefine = resultData.factorDefine;
                        this.data.ifEdit = resultData.ifEdit === 0 ? '0' : '1';
                        this.data.sortNum = resultData.sortNum;
                        this.data.factorSpecial = resultData.factorSpecial;
                        this.globalReadOnly = resultData.factorSpecial !== 0;
                        this.data.created = resultData.created;
                        this.data.itemsList = resultData.itemsList;
                        this.globalReadOnly = resultData.factorSpecial === 1 || resultData.factorSpecial === 2;
                        if (resultData.itemsList.length === 0) {
                            this.data.itemsList = [{ items: [] }];
                        } else {
                            resultData.itemsList.forEach((items) => {
                                const index = resultData.itemsList.indexOf(items);
                                if (items.defaultFlag === 1) {
                                    this.defaultSwitch[index] = true;
                                } else {
                                    this.defaultSwitch[index] = false;
                                }
                            });
                        }
                    }
                });
            }
            return null;
        },
        validator(rule, value, callback) {
            const min = rule.required ? 1 : 0;
            if (!value || value.length < min) {
                callback(new Error(rule.message));
            } else {
                callback();
            }
        },
        validateData() {
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject();
                    }
                });
            });
        },
    },
};
</script>
<style lang="less">
    .add-Factor-form{
        & .el-input{
            width: 180px;
        }
    }
    .submit-factor__main{
        & .el-table__body-wrapper{
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
    .submit-factor__table{
        width: 100%;
        border-collapse: collapse;
        & thead{
            & th{
                text-align: left;
            }
        }
        & td{
            border-bottom: 1px solid #ebf1f3;
            height: 40px;
            line-height: 40px;
            text-align: left;
        }
    }
    .line-left-right {
        padding: 0 0px 0px;
        margin: 15px 0px;
        line-height: 1px;
        border-left: 720px solid rgb(221, 221, 221);
        /*border-right: 150px solid rgb(221, 221, 221);*/
        text-align: center;
        height: 2px;
    }
</style>
