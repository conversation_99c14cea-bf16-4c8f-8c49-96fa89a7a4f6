<template>
    <el-form
        label-position="right"
        class="show-roiBase-form"
        :rules="rules"
        ref="formIns"
        :model="data"
    >
        <el-row>
            <el-col :span="12">
                <el-form-item
                    label="平均达成率(%)"
                    prop="avgRate"
                    required
                >
                    <el-input
                        v-model="data.avgRate"
                        placeholder="请输入0-100之间的数字"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item
                    label="计算系数"
                    prop="avgCoefficient"
                    required
                >
                    <el-input
                        v-model="data.avgCoefficient"
                        placeholder="请输入0-1之间的数字"
                    />
                </el-form-item>
            </el-col>
        </el-row>
        <el-form-item
            prop="roiRates"
        >
            <table class="submit-roiRate__table">
                <thead>
                    <tr>
                        <th
                            width="30px"
                            style="text-align: center"
                        >
                            编号
                        </th>
                        <th width="50px">
                            部门名称
                        </th>
                        <th width="50px">
                            达成率(%)
                        </th>
                        <th width="100px">
                            操作
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(row, index) in data.rateList"
                        :key="index"
                    >
                        <td style="text-align: center">
                            {{ index + 1 }}
                        </td>
                        <td>
                            <!-- 部门树 -->
                            <jacp-orgs-selector-tree
                                v-model="data.rateList[index].departmentMap"
                            />
                        </td>
                        <td>
                            <el-input
                                v-model="data.rateList[index].rate"
                                placeholder="请输入1-100之间的数字"
                                style="width:165px;margin: auto"
                            />
                        </td>
                        <td>
                            <el-button
                                type="text"
                                size="small"
                                @click="data.rateList.splice(index+1,0,{rates: []})"
                            >
                                添加
                            </el-button>
                            <el-button
                                type="text"
                                v-if="data.rateList.length > 1"
                                size="small"
                                @click="data.rateList.splice(index, 1)"
                            >
                                删除
                            </el-button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </el-form-item>
    </el-form>
</template>

<script type="text/javascript">
import PriorityRate from '@/models/priorityRate';

export default {
    data() {
        return {
            data: {
                id: '',
                avgRate: '',
                avgCoefficient: '',
                created: '',
                rateList: [],
            },
            formData: {
                enabled: false,
                roiRates: [{ rates: [] }],
                executionType: 1,
            },
            rules: {
                avgRate: [{
                    required: true,
                    trigger: 'blur',
                    validator: (rule, value, callback) => {
                        const pattern = /^\d+(\.\d{1,2})?$/;
                        const min = rule.required ? 1 : 0;
                        if (!value || value.length < min) {
                            callback(new Error('平均达成率不能为空'));
                        }
                        if (!pattern.test(value)) {
                            callback(new Error('只能输入两位小数'));
                        }
                        if (value < 0 || value > 100) {
                            callback(new Error('请输入0-100之间的数字'));
                        }
                        callback();
                    },
                }],
                avgCoefficient: [{
                    required: true,
                    trigger: 'blur',
                    validator: (rule, value, callback) => {
                        const pattern = /^\d+(\.\d{1,2})?$/;
                        const min = rule.required ? 1 : 0;
                        if (!value || value.length < min) {
                            callback(new Error('计算系数不能为空'));
                        }
                        if (!pattern.test(value)) {
                            callback(new Error('只能输入两位小数'));
                        }
                        if (value < 0 || value > 1) {
                            callback(new Error('请输入0-1之间的数字'));
                        }
                        callback();
                    },
                }],
                roiRates: [{
                    validator: (rule, value, callback) => {
                        const pattern = /^\d+(\.\d{1,2})?$/;
                        if (!this.data.rateList.length) {
                            callback(new Error('请配置部门达成率'));
                        }
                        this.data.rateList.forEach((items) => {
                            if (!items.rate && items.rate !== 0 && items.rate !== '0') {
                                callback(new Error('部门达成率不能为空'));
                            }
                            if (!pattern.test(items.rate)) {
                                callback(new Error('只能输入两位小数'));
                            }
                            if (!items.departmentMap) {
                                callback(new Error('请选择一个部门'));
                            }
                        });
                        callback();
                    },
                }],
            },
        };
    },
    mounted() {
        // 初始化数据
        this.showRoiBase();
    },
    methods: {
        showRoiBase() {
            return PriorityRate.showRoiBase().then((resultData) => {
                if (resultData) {
                    this.data.id = resultData.id;
                    this.data.avgRate = resultData.avgRate;
                    this.data.avgCoefficient = resultData.avgCoefficient;
                    this.data.created = resultData.created;
                    this.data.rateList = resultData.rateList;
                    if (resultData.rateList.length === 0) {
                        this.data.rateList = [{ rates: [] }];
                    }
                }
            });
        },
        validator(rule, value, callback) {
            const min = rule.required ? 1 : 0;
            if (!value || value.length < min) {
                callback(new Error(rule.message));
            } else {
                callback();
            }
        },
        validateData() {
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject();
                    }
                });
            });
        },
    },
};
</script>
<style lang="less">
    .show-roiBase-form{
        & .el-input{
            width: 130px;
        }
    }
    .submit-roiRate__main{
        & .el-table__body-wrapper{
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
    .submit-roiRate__table{
        width: 100%;
        border-collapse: collapse;
        & thead{
            & th{
                text-align: left;
            }
        }
        & td{
            border-bottom: 1px solid #ebf1f3;
            height: 40px;
            line-height: 40px;
            text-align: left;
        }
    }
</style>
