<template>
    <div class="form-option-demand">
        <div class="form-option-demand__head">
            <span>启用模板</span>
            <el-switch v-model="formData.disabled" />
        </div>
        <el-table
            ref="multipleTable"
            :data="formData.fields"
            :row-key="formData.fields.fieldKey"
            border
            tooltip-effect="dark"
            style="width: 100%"
        >
            <el-table-column
                prop="fieldName"
                label="字段名称"
                width="250"
            >
                <span slot-scope="scope">
                    {{ scope.row.fieldName }}
                    <el-tag
                        size="mini"
                        v-if="scope.row.extended"
                    >扩展</el-tag>
                </span>
            </el-table-column>
            <el-table-column label="是否必填">
                <template slot-scope="scope">
                    <el-checkbox
                        v-model="scope.row.required"
                        :disabled="scope.row.disabled"
                    /> <span>&nbsp;必填&nbsp;</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="display"
                label="显示"
                width="250"
            >
                <!-- TODO: 字段还没定 -->
                <span slot-scope="scope">
                    <el-checkbox
                        v-show="scope.row.extended"
                        v-model="scope.row.display"
                        :disabled="scope.row.disabled"
                    >显示</el-checkbox></span>
            </el-table-column>
            <el-table-column
                prop="type"
                label="类型"
                width="180"
            >
                <span slot-scope="scope">
                    <!-- TODO: 字段还没定 -->
                    {{ $t(`jacp.fields.type.${scope.row.dataType || scope.row.type}`) }}
                </span>
            </el-table-column>
        </el-table>
        <el-button
            type="primary"
            size="small"
            class="form-option__save"
            @click="saveFormOption"
        >
            保存
        </el-button>
    </div>
</template>
<script type="text/javascript">
import FormOption from '@/models/formOption';

// import addExceptionList from './addExceptionList';

export default {
    components: {
        // addExceptionList,
    },
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
        demandType: {
            type: String,
            default: '',
        },
        unshowFieldList: Array,
    },
    data() {
        return {
            formData: { fields: [] }, // 字段列表
            userOrgName: '',
            currentIndex: 0,
        };
    },
    methods: {
        // 查询需求字段列表
        queryDemandFieldList() {
            FormOption.getFieldList({
                formKey: this.demandType,
                orgId: this.userOrgId,
            }).then((data) => {
                this.filterDemandField(data);
                // 0 1 转换为布尔值
                this.formData.disabled = data.disabled === 0;
            });
        },
        saveFormOption() {
            // 布尔值 还原为 0 1
            const tmp = {
                disabled: this.formData.disabled ? 0 : 1,
                formKey: this.demandType,
            };
            FormOption.saveFormOption(Object.assign(this.formData, tmp)).then(() => {
                // 刷新数据
                this.queryDemandFieldList();
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
        filterDemandField(data) {
            if (!this.unshowFieldList || this.unshowFieldList.length === 0) {
                this.formData = data;
                return;
            }
            const fields = data.fields.filter(field => !this.unshowFieldList.includes(field.fieldKey));
            this.formData = Object.assign({}, data, { fields });
        },
    },
    watch: {
        userOrgId() {
            this.queryDemandFieldList();
        },
        unshowFieldList() {
            this.filterDemandField(this.formData);
        },
    },
};
</script>

<style lang="less">
    .form-option-demand{
        height: calc(~"100vh - 236px");
        overflow-y: auto;
        & .el-table__body-wrapper{
            // height: calc(~"100vh - 320px");
            overflow-y: auto;
            overflow-x: hidden;
        }
        & .form-option__save{
            margin-top: 10px;
        }
        &__link{
            color: #08A4DA;
            cursor: pointer;
        }
        &__head{
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }
    }
    .demand-excludes-form{
        width: 668px;
        & .el-input{
            width: 600px;
        }
    }
</style>
