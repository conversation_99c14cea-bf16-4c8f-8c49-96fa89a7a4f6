<template>
    <div
        class="form-option-demand"
        v-if="dicData"
    >
        <el-table
            :data="dicData.fields[0] && dicData.fields[0].options"
            style="width: 100%;height: calc(100vh - 280px)"
        >
            <el-table-column
                prop="code"
                label="优先级编号"
                width="180"
            />
            <el-table-column
                prop="name"
                label="优先级描述"
                width="180"
            >
                <template slot-scope="scope">
                    <el-input v-model="scope.row.name" />
                </template>
            </el-table-column>
            <el-table-column
                prop="show"
                label="是否显示"
            >
                <template slot-scope="scope">
                    <el-checkbox v-model="scope.row.show" /><span>&nbsp;显示</span>
                </template>
            </el-table-column>
        </el-table>
        <el-button
            type="primary"
            size="small"
            class="form-option__save"
            @click="saveFormOption"
        >
            保存
        </el-button>
    </div>
</template>
<script type="text/javascript">
// import FormOption from '@/models/formOption';
import { FieldDictMetadata } from '@/models/metadata';
// import addExceptionList from './addExceptionList';

export default {
    components: {
        // addExceptionList,
    },
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
        demandType: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            dicData: null,
        };
    },
    methods: {
        // 查询需求字段列表
        queryDemandFieldList() {
            this.dicData.load({
                owner: this.dicData.owner,
                fieldKey: this.dicData.fieldKey,
                orgId: undefined, // FIXME: 希望可以统一一下，都使用owner或者都使用orgId？都是代表同一个字段
            });
        },
        saveFormOption() {
            // 布尔值 还原为 0 1
            this.dicData.save().then(() => {
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
    },
    watch: {
        userOrgId: {
            immediate: true,
            handler(val) {
                if (!this.dicData) {
                    this.dicData = new FieldDictMetadata({
                        api: 'v1/bizMetadataForm/field-dict',
                        formKey: 'demand',
                        orgId: val,
                        fieldKey: 'priorityId',
                    });
                }
                this.dicData.orgId = val;
                this.queryDemandFieldList();
            },
        },
    },
};
</script>

<style lang="less">
    .form-option-demand{
        & .el-table__body-wrapper{
            // height: calc(~"100vh - 320px");
            overflow-y: auto;
            overflow-x: hidden;
        }
        & .form-option__save{
            margin-top: 10px;
        }
        &__link{
            color: #08A4DA;
            cursor: pointer;
        }
        &__head{
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }
    }
    .demand-excludes-form{
        width: 668px;
        & .el-input{
            width: 600px;
        }
    }
</style>
