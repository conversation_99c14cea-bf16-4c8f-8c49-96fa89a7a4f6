<template>
    <el-form
        label-position="right"
        label-width="120px"
        :rules="rules"
        ref="formIns"
        class="add-auxiliary-form"
        :model="data"
    >
        <el-form-item
            label="所属部门"
            prop="userTopOrgId"
        >
            <el-select
                v-model="userTopOrgId"
                placeholder="请选择"
                filterable
                @change="getUerOrgInfo"
            >
                <el-option
                    v-for="item in orgList"
                    :key="item.orgId"
                    :value="item.orgId"
                    :label="item.orgName"
                >
                    <span style="float: left">{{ item.orgName }}</span>
                    <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item
            label="负责部门"
            prop="userOrgId"
        >
            <jacp-org-tree
                :root-org-id="userTopOrgId"
                :root-org-name="userTopName"
                @check-org="checkOrg"
            >
                <template slot-scope="scope">
                    <div
                        v-if="scope.data"
                        class="demands-filter__depname"
                    >
                        {{ scope.data.name }}
                    </div>
                    <div v-else>
                        请选择
                    </div>
                </template>
            </jacp-org-tree>
        </el-form-item>
        <el-form-item
            label="姓名"
            prop="userInfo"
        >
            <jacp-input-users
                v-model="data.userInfo"
                placeholder="姓名"
                :max-count="1"
            />
        </el-form-item><br>
    </el-form>
</template>

<script type="text/javascript">
export default {
    props: {
        orgList: {
            type: Array,
            default: () => ([]),
        },
        userOrgId: {
            type: String,
        },
    },
    data() {
        return {
            userTopOrgId: '',
            userTopName: '',
            data: {
                userOrgName: '',
                userOrgId: '',
                userInfo: [],
            },
            rules: {
                userOrgId: [{
                    required: true,
                    message: '部门不能为空',
                    trigger: 'blur',
                }],
                userInfo: [{
                    required: true,
                    message: '管理员姓名不能为空',
                    trigger: 'blur',
                    validator: this.validator,
                }],
            },
        };
    },
    mounted() {
        // 初始化部门数据
        this.userTopOrgId = this.userOrgId;
        this.data.userOrgId = this.userOrgId;
        this.getUerOrgInfo(this.data.userOrgId);
    },
    methods: {
        validator(rule, value, callback) {
            const min = rule.required ? 1 : 0;
            if (!value || value.length < min) {
                callback(new Error(rule.message));
            } else {
                callback();
            }
        },
        validateData() {
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject();
                    }
                });
            });
        },
        getUerOrgInfo(newValue) {
            this.orgList.forEach((item) => {
                if (item.orgId === newValue) {
                    this.userTopName = item.orgName;
                    // 更新部门树 选中顶级节点
                    this.data.userOrgName = item.orgName;
                    this.data.userOrgId = item.orgId;
                }
            });
        },
        checkOrg(orgId, orgName) {
            this.data.userOrgId = orgId;
            this.data.userOrgName = orgName;
        },
    },
};
</script>
<style lang="less">
.add-auxiliary-form{
    & .el-input{
        width: 350px;
    }
}
</style>
