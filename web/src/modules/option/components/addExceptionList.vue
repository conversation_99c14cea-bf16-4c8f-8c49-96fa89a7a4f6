<template>
    <el-dialog
        class="demand-excludes-dialog"
        title="添加例外部门名单"
        :visible="dialogFormVisible"
        :before-close="closeDialog"
    >
        <el-form class="demand-excludes-form">
            <el-form-item>
                <el-autocomplete
                    v-model="userOrgName"
                    :fetch-suggestions="loadOrglist"
                    placeholder="请输入例外部门名称关键字"
                    :trigger-on-focus="false"
                    :props="{value: 'fullName', label: 'fullName'}"
                    @select="handleSelect"
                />
                <el-button
                    @click="addException"
                    type="primary"
                    size="small"
                >
                    添加
                </el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="excludesList"
            style="width: 100%"
        >
            <el-table-column
                prop="orgId"
                label="部门编号"
            />
            <el-table-column
                prop="orgName"
                label="部门名称"
            />
            <el-table-column
                label="删除"
                width="100"
            >
                <template slot-scope="scope">
                    <span
                        class="form-option-demand__link"
                        @click="delException(scope)"
                    >删除</span>
                </template>
            </el-table-column>
        </el-table>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <el-button @click="closeDialog">
                返回
            </el-button>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import FormOption from '@/models/formOption';
import Dialog from '@/models/dialog';

export default {
    props: {
        fields: {
            type: Array,
            default: () => [],
        },
        dialogFormVisible: {
            type: Boolean,
            default: false,
        },
        // 当前字段下标
        index: {
            type: Number,
            default: 0,
        },
        topOrgId: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            userOrgId: '',
            userOrgName: '',
        };
    },
    methods: {
        // 加载部门列表
        loadOrglist(orgInfo, callback) {
            if (orgInfo) {
                FormOption.getOrgList({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data.records);
                });
            } else {
                // 清空原部门信息
                this.userOrgId = '';
                this.userOrgName = '';
            }
        },
        // 页面添加数据
        addException() {
            if (this.userOrgId && this.userOrgName && this.checkOrg()) {
                this.excludesList.push({
                    orgId: this.userOrgId,
                    orgName: this.userOrgName,
                });
                // 清空原部门信息
                this.userOrgId = '';
                this.userOrgName = '';
                this.$emit('save');
            }
        },
        checkOrg() {
            let flag = true;
            if (!this.userOrgId.startsWith(this.topOrgId)) {
                Dialog.alert({
                    title: '操作提示',
                    content: '请在当前部门的下级部门中选择例外部门',
                    type: 'success',
                });
                // 清空原部门信息
                this.userOrgId = '';
                this.userOrgName = '';
                flag = false;
                return flag;
            }

            // 查重
            this.excludesList.forEach((item) => {
                if (item.orgId === this.userOrgId) {
                    Dialog.alert({
                        title: '操作提示',
                        content: '该部门已在例外名单中',
                        type: 'success',
                    });
                    // 清空原部门信息
                    this.userOrgId = '';
                    this.userOrgName = '';
                    flag = false;
                }
            });
            return flag;
        },
        // 页面删除部门数据
        delException(scope) {
            this.excludesList.splice(scope.$index, 1);
            this.$emit('save');
        },
        handleSelect(item) {
            this.userOrgId = item.parentIds;
        },
        closeDialog() {
            this.$emit('update:dialogFormVisible', false);
        },
    },
    computed: {
        // 白名单部门列表
        excludesList() {
            if (this.fields && this.fields.length > 0) {
                return this.fields[this.index].excludes;
            }
            return [];
        },
    },
};
</script>

<style lang="less">
    .demand-excludes-dialog{
        & .form-option__save{
            margin-top: 10px;
        }
        &__link{
            color: #08A4DA;
            cursor: pointer;
        }

        & .el-dialog__wrapper{
            z-index: 2;
            & .el-table__body-wrapper{
            height: calc(~"100vh - 440px");
            overflow-y: auto;
            }
        }
    }
    .demand-excludes-form{
        width: 668px;
        & .el-input{
            width: 450px;
        }
    }
</style>
