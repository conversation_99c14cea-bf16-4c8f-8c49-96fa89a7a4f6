<template>
    <el-dialog
        title="应用注册"
        :visible.sync="showAddModule"
        width="60%"
        @close="$emit('update:showAddModule', false)"
    >
        <el-form
            label-position="right"
            label-width="220px"
            class="add-admin-pop"
            :model="formData"
            ref="formIns"
            :rules="rules"
        >
            <el-form-item
                label="应用名称"
                prop="name"
            >
                <el-input
                    placeholder="请输入内容"
                    v-model="formData.name"
                />
            </el-form-item>
            <el-form-item
                label="应用标识"
                prop="code"
            >
                <el-input
                    v-model="formData.code"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="应用域名url"
                prop="url"
            >
                <el-input
                    v-model="formData.url"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="应用类型"
                prop="type"
            >
                <el-input
                    v-model="formData.type"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="上级应用"
                prop="parentRouterName"
            >
                <el-input
                    v-model="formData.parentRouterName"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="入口路由"
                prop="topRooter"
            >
                <el-input
                    v-model="formData.topRooter"
                    placeholder="请输入内容"
                />
            </el-form-item>
            <el-form-item
                label="权限码"
                prop="permissionCode"
            >
                <el-input
                    v-model="formData.permissionCode"
                    placeholder="应用可见权限码，没有请在权限系统申请"
                />
            </el-form-item>
            <el-form-item
                label="是否挂载一级菜单"
                prop="topMenuMounted"
            >
                <el-checkbox
                    v-model="formData.topMenuMounted"
                    :true-label="1"
                    :false-label="0"
                />
            </el-form-item>
            <el-form-item
                label="关联应用"
                prop="appId"
            >
                <el-select
                    v-model="formData.appId"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in appList"
                        :key="item.id"
                        :value="item.id"
                        :label="item.name"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="ICON"
                prop="icon"
            >
                <el-input
                    v-model="formData.icon"
                    placeholder="icon class，没有请提前设计"
                />
            </el-form-item><br>
        </el-form>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button
                    type="primary"
                    @click="saveData"
                >确 定</el-button>
                <el-button @click="$emit('update:showAddModule', false)">取 消</el-button>
            </span>
        </div>
    </el-dialog>
</template>
<script type="text/javascript">
import ModuleOption from '@/models/moduleOption';

export default {
    props: {
        showAddModule: {
            type: Boolean,
            default: false,
        },
        appList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            formData: {
                name: '',
                code: '',
                url: '',
                type: 'page',
                parentRouterName: 'root',
                icon: '',
                topRooter: '',
                appId: '',
                permissionCode: '',
                topMenuMounted: 1,
            },
            rules: {
                name: [{
                    required: true,
                    message: '应用名称不能为空',
                    trigger: 'blur',
                }],
                code: [{
                    required: true,
                    message: '应用标识不能为空',
                    trigger: 'blur',
                }],
                url: [{
                    required: true,
                    message: 'url不能为空',
                    trigger: 'blur',
                }],
                type: [{
                    required: true,
                    message: '类型不能为空',
                    trigger: 'blur',
                }],
                parentRouterName: [{
                    required: true,
                    message: '上级应用不能为空',
                    trigger: 'blur',
                }],
                icon: [{
                    required: true,
                    message: 'ICON不能为空',
                    trigger: 'blur',
                }],
                topRooter: [{
                    required: true,
                    message: '入口路由不能为空',
                    trigger: 'blur',
                }],
                appId: [{
                    required: true,
                    message: '关联应用不能为空',
                    trigger: 'blur',
                }],
            },
        };
    },
    methods: {
        saveData() {
            this.$refs.formIns.validate((valid) => {
                if (valid) {
                    const param = {
                        code: this.formData.code,
                        name: this.formData.name,
                        desc: '',
                        appId: this.formData.appId,
                        permissionCode: this.formData.permissionCode,
                        topMenuMounted: this.topMenuMounted,
                        register: {
                            key: this.formData.code,
                            name: this.formData.name,
                            url: this.formData.url,
                            topRooter: this.formData.topRooter,
                            type: this.formData.type,
                            parentRouterName: this.formData.parentRouterName,
                            icon: this.formData.icon,
                        },
                    };
                    ModuleOption.addModule(param).then(() => {
                        this.$emit('update:showAddModule', false);
                        // 刷新列表
                        this.$emit('refresh-list');
                        this.$notify({
                            title: '保存成功',
                            type: 'success',
                            duration: 2000,
                        });
                    });
                } else {
                    throw new Error(valid);
                }
            });
        },
    },
    watch: {
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
 .add-admin-pop{
    & .el-input{
        width: 400px;
    }
 }
 .option-admin-auto{
    & li{
        white-space: inherit;
        line-height: 26px;
        font-size: 12px;
        border-bottom: 1px solid #dcdbdb;
        &:nth-of-type(2n){
            background-color: #f9f9f9;
        }
        &.highlighted{
            background-color: @primaryColor;
            color: #fff;
        }
    }
}
</style>
