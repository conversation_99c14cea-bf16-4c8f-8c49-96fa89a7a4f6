<template>
    <div class="apportion-hours">
        <div class="apportion-hours__tips">
            <i class="el-icon-warning" />
            <p>
                一旦开启后将在「需求受理」时，必须填写「预估工时」，行云将数据传递到公司「流程中心」，
                业务方可在PC或者京ME端审批该需求的预估工时，可进行确认或驳回操作，若确认后才能进行后续的「需求处理」操作。
            </p>
        </div>
        <label style="margin: 24px 0 12px;display: block;">
            示意图：
        </label>
        <div class="apportion-hours__pic">
            <img src="../../../assets/images/hours-flow.png">
        </div>
        <label style="margin: 24px 0 12px;display: block;">启用审批流程</label>
        <el-switch
            v-model="approvalFlag"
            :active-value="false"
            :inactive-value="true"
            @change="setHoursReviewInfo"
        />
        <div>
            <label style="margin: 24px 0 12px;display: block;">工时范围（人/天）</label>
            <el-input-number
                v-model="lowerBound"
                controls-position="right"
                :min="0"
                :controls="false"
                :disabled="approvalFlag"
                @change="setHoursReviewInfo"
            />
            <span>-</span>
            <el-input-number
                v-model="upperBound"
                controls-position="right"
                :min="0"
                :controls="false"
                :disabled="approvalFlag"
                @change="setHoursReviewInfo"
            />
        </div>
        <label style="margin: 24px 0 12px;display: block;">
            例外部门
        </label>
        <el-table
            style="width: 100%"
            border
            :data="exceptionList"
        >
            <el-table-column
                label="部门名称"
                prop="orgName"
            />
            <el-table-column
                label="操作"
                align="center"
                width="100"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        :disabled="approvalFlag"
                        @click="delExceptionOrg(scope.row.id)"
                    >
                        移除
                    </el-button>
                </template>
            </el-table-column>
        </el-table><br>
        <el-button
            type="primary"
            size="mini"
            :disabled="approvalFlag"
            @click="dialogFormVisible = true"
        >
            添加例外部门
        </el-button>
        <el-dialog
            class="apportion-hours-dialog"
            title="添加例外部门名单"
            :visible.sync="dialogFormVisible"
            :before-close="closeDialog"
            width="640px"
        >
            <el-form class="apportion-hours-form">
                <el-form-item>
                    <el-autocomplete
                        v-model="orgNameKey"
                        :fetch-suggestions="loadOrglist"
                        placeholder="请输入例外部门名称关键字"
                        :trigger-on-focus="false"
                        value-key="fullName"
                        label="fullName"
                        @select="handleSelect"
                        style="width: 100%;"
                    />
                </el-form-item>
            </el-form>
            <div
                slot="footer"
                class="dialog-footer"
            >
                <el-button
                    @click="addExceptionOrg"
                    type="primary"
                    size="mini"
                >
                    添加
                </el-button>
                <el-button
                    size="mini"
                    @click="closeDialog"
                >
                    返回
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script type="text/javascript">
import FormOption from '@/models/formOption';
import DemandFlow from '@/models/demandFlow';

export default {
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
        userOrgName: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            approvalFlag: !!1, // 0开启 1 禁用
            approvalId: undefined,
            dialogFormVisible: false,
            orgNameKey: '',
            orgId: '',
            orgName: '',
            lowerBound: 0,
            upperBound: 29,
            exceptionList: [],
        };
    },
    methods: {
        getHoursReviewInfo(orgId) {
            DemandFlow.getHoursReviewInfo({ orgId }).then((data) => {
                this.approvalId = data.id;
                this.approvalFlag = !!data.disabled;
                this.upperBound = data.upperBound || 29;
                this.lowerBound = data.lowerBound || 0;
                if (this.approvalId) {
                    this.getExceptionOrgList(this.approvalId);
                } else {
                    this.exceptionList = [];
                }
            });
        },
        setHoursReviewInfo() {
            if (this.lowerBound >= this.upperBound) {
                this.$alert('工时上限不能小于等于工时下限！', '操作提示', { type: 'warning' });
                return;
            }
            DemandFlow.setHoursReviewInfo({
                orgId: this.userOrgId,
                orgName: this.userOrgName,
                upperBound: this.upperBound,
                lowerBound: this.lowerBound,
                disabled: Number(this.approvalFlag),
                id: this.approvalId,
            }).then((data) => {
                this.approvalId = data.id;
                this.$notify({
                    title: '保存成功',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
        getExceptionOrgList(approvalId) {
            DemandFlow.getExceptionOrgList({ approvalId }).then((data) => {
                this.exceptionList = data;
            });
        },
        // 加载部门列表
        loadOrglist(orgInfo, callback) {
            if (orgInfo) {
                FormOption.getOrgList({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data.records);
                });
            } else {
                // 清空原部门信息
                this.orgId = '';
                this.orgName = '';
            }
        },
        handleSelect({ fullName, parentIds }) {
            this.orgId = parentIds;
            this.orgName = fullName;
        },
        addExceptionOrg() {
            if (!this.orgId || !this.orgName) {
                this.$alert('请选择例外部门', '操作提示', { type: 'warning' });
                return;
            }
            DemandFlow.addExceptionOrg({
                orgId: this.orgId,
                orgName: this.orgName,
                approvalId: this.approvalId,
            }).then(() => {
                this.$notify({
                    title: '添加成功',
                    type: 'success',
                    duration: 2000,
                });
                this.getExceptionOrgList(this.approvalId);
                this.closeDialog();
            });
        },
        delExceptionOrg(ruleId) {
            DemandFlow.delExceptionOrg(ruleId).then(() => {
                this.$notify({
                    title: '移除成功',
                    type: 'success',
                    duration: 2000,
                });
                this.getExceptionOrgList(this.approvalId);
            });
        },
        closeDialog() {
            this.dialogFormVisible = false;
            this.orgId = '';
            this.orgName = '';
            this.orgNameKey = '';
            this.lowerBound = 0;
            this.upperBound = 29;
        },
    },
    watch: {
        // 根据当前部门信息查询流程信息
        userOrgId(userOrgId) {
            if (userOrgId) {
                this.getHoursReviewInfo(userOrgId);
            }
        },
    },
};
</script>

<style lang="less">
.apportion-hours{
  & h5, label{
    color: #333;
    font-size: 14px;
  }
  .dialog-footer{
    margin: 0;
  }
  &__tips{
    color: #FEA73E;
    background:rgba(254,167,62, .2);
    padding: 8px 16px 8px 14px;
    margin-bottom: 12px;
    vertical-align: super;
    line-height: 16px;
    display: flex;
    & p{
      font-size: 12px;
      display: inline-block;
      margin: 0;
      flex: 1;
    }
    i{
      width: 24px;
      height: 100%;
      display: inline-block;
    }
  }
  &__pic img{
    width: 504px;
    height: 72px;
  }
}
</style>
