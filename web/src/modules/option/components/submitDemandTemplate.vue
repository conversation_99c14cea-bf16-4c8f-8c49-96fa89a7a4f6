<template>
    <div class="submit-template__main">
        <el-form
            class="submit-template__table"
            label-position="left"
            :model="formData"
            :rules="rules"
            ref="formIns"
        >
            <el-form-item
                label="启用模板"
                prop="enabled"
            >
                <el-switch
                    v-model="formData.enabled"
                    active-color="#08a4da"
                    @change="switchTemplate"
                />
                <el-tooltip
                    class="tip"
                    effect="dark"
                    content="模板开启后指标将不可编辑"
                    placement="right"
                >
                    <div class="el-icon-info" />
                </el-tooltip>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    @click="newFactor"
                    :disabled="blockEdit"
                >
                    新增指标
                </el-button>
            </el-form-item>
            <el-form-item
                prop="factors"
            >
                <el-table
                    :data="formData.factorList"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="sortNum"
                        label="排序号"
                        width="160"
                    >
                        <template slot-scope="scope">
                            <jacp-input-number
                                style="width:140px"
                                v-model="scope.row.sortNum"
                                controls-position="right"
                                :min="1"
                                :precision="0"
                                :readonly="blockEdit"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="factorName"
                        label="指标名称"
                        width="120"
                    />
                    <el-table-column
                        prop="factorDefine"
                        :show-overflow-tooltip="true"
                        label="指标定义"
                        width="260"
                    />
                    <el-table-column
                        prop="factorSpecial"
                        label="类型"
                        width="120"
                    >
                        <template slot-scope="scope">
                            <span v-if="scope.row.factorSpecial === 1 || scope.row.factorSpecial === 2">系统默认</span>
                            <span v-else>非系统默认</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="factorWeight"
                        label="指标占比(%)"
                        width="220"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.factorWeight"
                                maxlength="6"
                                placeholder="请输入0-100之间的数字"
                                :readonly="blockEdit"
                                style="width: 180px"
                            />
                        </template>
                    </el-table-column>

                    <el-table-column
                        label="操作"
                    >
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                size="small"
                                @click="editFactor(scope.row.id)"
                                :disabled="blockEdit"
                            >
                                编辑
                            </el-button>
                            <el-button
                                type="text"
                                size="small"
                                @click="deleteFactor(scope.row.id)"
                                :disabled="scope.row.factorSpecial === 1 || scope.row.factorSpecial === 2 || blockEdit"
                            >
                                删除
                            </el-button>
                            &nbsp;&nbsp;
                            <span v-if="scope.row.factorSpecial === 2">
                                <el-button
                                    type="text"
                                    size="small"
                                    @click="editRoiBase"
                                >
                                    数据录入
                                </el-button>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    @click="saveTemplate"
                    :disabled="blockEdit"
                >
                    保存设置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script type="text/javascript">
import Dialog from '@/models/dialog';
import PriorityRate from '@/models/priorityRate';
import editRoiBase from './editRoiBase';
import addFactor from './addFactor';

export default {
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
        factorArray: {
            type: Array,
            default: () => [],
        },
        disableEdit: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            blockEdit: false,
            formData: {
                enabled: false,
                factorList: [],
                tempId: '',
                id: 0,
            },
            rules: {
                factors: [{
                    validator: (rule, value, callback) => {
                        this.formData.factorList.forEach((items) => {
                            if (!items.factorWeight && items.factorWeight !== 0 && items.factorWeight !== '0') {
                                callback(new Error('指标占比不能为空'));
                            }
                            const pattern = /^\d+(\.\d{1,2})?$/;
                            if (!pattern.test(items.factorWeight)) {
                                callback(new Error('只能输入两位小数'));
                            }
                            if (items.factorWeight < 0 || items.factorWeight > 100) {
                                callback(new Error('请输入小于0-100之间的数字'));
                            }
                        });
                        callback();
                    },
                }],
            },
        };
    },
    mounted() {
        this.blockEdit = this.disableEdit;
        // 加载对象数据
        this.templateByOrgCode();
    },
    methods: {
        // 查询当前部门下的模板
        templateByOrgCode() {
            PriorityRate.getTemplateByOrgCode({
                fullOrgPath: this.userOrgId,
            }).then((data) => {
                if (data) {
                    this.formData.tempId = data.id;
                    this.formData.id = data.id;
                    this.formData.factorList = data.factors;
                    if (data.ifOpen === 1) {
                        this.blockEdit = true;
                        this.formData.enabled = true;
                    } else {
                        this.blockEdit = false;
                        this.formData.enabled = false;
                    }
                }
            });
        },
        // 新增指标
        newFactor() {
            return Dialog.confirm({
                title: '新增指标',
                confirmBtnText: '提交',
                slot: addFactor,
                slotProps: {
                    templateId: this.formData.tempId,
                    userOrgCode: this.userOrgId,
                },
                beforeConfirm: vm => vm.validateData().then(() => {
                    const param = vm.data;
                    PriorityRate.createNormalFactor(param).then(() => {
                        this.$notify({
                            title: '保存成功',
                            type: 'success',
                            duration: 2000,
                        });
                        this.templateByOrgCode();
                    });
                }),
            });
        },
        // 编辑指标
        editFactor(id) {
            return Dialog.confirm({
                title: '编辑指标',
                confirmBtnText: '提交',
                slot: addFactor,
                slotProps: {
                    templateId: this.formData.tempId,
                    userOrgCode: this.userOrgId,
                    factorId: id,
                },
                beforeConfirm: vm => vm.validateData().then(() => {
                    const param = vm.data;
                    PriorityRate.updateFactor(param).then(() => {
                        this.$notify({
                            title: '保存成功',
                            type: 'success',
                            duration: 2000,
                        });
                        this.templateByOrgCode();
                    });
                }),
            });
        },
        // 删除指标
        deleteFactor(id) {
            this.$confirm('确定删除该条指标？', this.$t('jacp.deltitle'), {
                confirmButtonText: this.$t('jacp.button.remove'),
                cancelButtonText: this.$t('jacp.button.cancel'),
                type: 'warning',
            }).then(() => {
                PriorityRate.deleteFactor(id).then(() => {
                    Dialog.alert({
                        title: '操作提示',
                        content: '删除成功',
                        type: 'success',
                        timeout: 2000,
                    });
                    this.templateByOrgCode();
                });
            });
        },
        // 保存模板
        saveTemplate() {
            this.validateData().then(() => {
                const param = {
                    id: 0,
                    factors: [],
                };
                param.id = this.formData.id;
                param.factors = this.formData.factorList.slice(0);
                PriorityRate.saveTemplate(param).then(() => {
                    this.$notify({
                        title: '保存成功',
                        type: 'success',
                        duration: 2000,
                    });
                    this.templateByOrgCode();
                });
            });
        },
        // 开关模板
        switchTemplate() {
            let successMessage = '';
            if (this.formData.enabled) {
                this.blockEdit = true;
                successMessage = '模板开启成功';
            } else {
                this.blockEdit = false;
                successMessage = '模板关闭成功';
            }
            PriorityRate.switchTemplate(this.formData.id).then(() => {
                this.$notify({
                    title: successMessage,
                    type: 'success',
                    duration: 2000,
                });
                this.templateByOrgCode();
            }).catch(() => {
                this.formData.enabled = false;
                this.blockEdit = false;
            });
        },
        // 编辑Roi基础
        editRoiBase() {
            return Dialog.confirm({
                title: '各部门达成率',
                confirmBtnText: '保存',
                slot: editRoiBase,
                beforeConfirm: vm => vm.validateData().then(() => {
                    const param = vm.data;
                    PriorityRate.saveRoiBase(param).then(() => {
                        this.$notify({
                            title: '保存成功',
                            type: 'success',
                            duration: 2000,
                        });
                    }).catch();
                }),
            });
        },
        validateData() {
            return new Promise((resolve, reject) => {
                this.$refs.formIns.validate((res) => {
                    if (res) {
                        resolve();
                    } else {
                        reject();
                    }
                });
            });
        },
    },
    watch: {
        // 根据当前部门信息查询流程信息
        userOrgId(userOrgId) {
            if (userOrgId) {
                this.templateByOrgCode();
            }
        },
        factorArray(factorArray) {
            if (factorArray.length > 0) {
                this.templateByOrgCode();
            }
        },
    },
};
</script>

<style lang="less">
    .submit-template__main{
        & .el-table__body-wrapper{
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
    .submit-template__table{
        & .el-input{
            width: 140px;
        }
    }
    .tip {
        margin: 4px;
    }
</style>
