<template>
    <div class="submit-list__main">
        <el-form
            :inline="true"
            class="white-list__org"
            label-position="left"
        >
            <el-form-item
                label="姓名"
                prop="userInfo"
            >
                <jacp-input-users
                    v-model="userInfo"
                    placeholder="姓名"
                    :max-count="1"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    @click="saveWhiteList('save')"
                >
                    添加
                </el-button>
            </el-form-item>
            <el-form-item label="启用白名单">
                <el-switch
                    v-model="isDisabled"
                    active-color="#08a4da"
                    @change="saveWhiteList('switch')"
                />
                <span class="white-list__main__warning">*注意：开启后，仅白名单人员允许提报需求。</span>
            </el-form-item>
        </el-form>
        <el-table
            :data="submitData?submitData.members: []"
            style="width: 100%"
        >
            <el-table-column
                prop="memberName"
                label="姓名"
                width="180"
            />
            <el-table-column
                prop="memberKey"
                label="工号"
            />
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        size="small"
                        @click="deleteUser(scope)"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script type="text/javascript">
import WhiteList from '@/models/whiteList';
import Dialog from '@/models/dialog';

export default {
    props: {
        userOrgId: {
            type: String,
            default: '',
        },
        userOrgName: {
            type: String,
            default: '',
        },
        submitData: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            userInfo: [],
        };
    },
    methods: {
        saveWhiteList(type) {
            if (type === 'save') {
                if (!this.validateUser()) {
                    return;
                }
                this.submitData.members.push({
                    memberTypeId: 1,
                    memberName: this.userInfo[0].name,
                    memberKey: this.userInfo[0].erp,
                });
            }
            if (type === 'switch') {
                this.$set(this.submitData, 'disabled', this.isDisabled ? 1 : 0);
            }
            WhiteList.saveWhiteList(this.submitData).then(() => {
                this.$notify({
                    title: '保存成功！',
                    type: 'success',
                    duration: 2000,
                });
                this.userInfo = [];
                this.$emit('refreshList');
            });
        },
        validateUser() {
            if (this.userInfo.length > 0) {
                if (this.userOrgId.startsWith(this.userInfo[0].orgTierCode)
                || this.userInfo[0].orgTierCode.startsWith(this.userOrgId)) {
                    return true;
                }
                Dialog.alert({
                    title: '操作提示',
                    content: '该员工不属于当前一级部门',
                    type: 'success',
                    timeout: 4000,
                });
                return false;
            }
            Dialog.alert({
                title: '操作提示',
                content: '人员姓名不能为空',
                type: 'success',
                timeout: 4000,
            });
            return false;
        },
        deleteUser(scope) {
            this.submitData.members.splice(scope.$index, 1);
            WhiteList.saveWhiteList(this.submitData).then(() => {
                this.$notify({
                    title: '删除成功！',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
    },
    computed: {
        isDisabled() {
            return this.submitData.disabled === 0;
        },
    },
};
</script>

<style lang="less">
    .submit-list__main{
        height: calc(~"100vh - 210px");
        overflow-y: auto;
    }
</style>
