<template>
    <div class="arch-rule-compose-management" v-if="jurisdicList['archRuleCompose:view'] === 0">
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="规则编码">
                <el-input
                    v-model="formInline.ruleCode"
                    placeholder="请输入规则编码"
                    clearable
                />
            </el-form-item>
            <el-form-item label="规则名称">
                <el-input
                    v-model="formInline.ruleName"
                    placeholder="请输入规则名称"
                    clearable
                />
            </el-form-item>
            <el-form-item label="触发器类型">
                <el-select v-model="formInline.triggerType" placeholder="请选择触发器类型" clearable>
                    <el-option label="事项触发" :value="1"></el-option>
                    <el-option label="定时触发" :value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="formInline.state" placeholder="请选择状态" clearable>
                    <el-option label="禁用" :value="0"></el-option>
                    <el-option label="启用" :value="1"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button @click="reset">重置</el-button>
                <el-button @click="search" v-if="jurisdicList['archRuleCompose:view'] === 0">查询</el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archRuleCompose:view'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="primary"
                        disabled>查询</el-button>
                </el-tooltip>
            </el-form-item>
        </el-form>

        <div class="table-header">
            <div class="table-header-left">
                <el-button
                    type="primary"
                    @click="showAddDialog"
                    v-if="jurisdicList['archRuleCompose:add'] === 0">
                    新增
                </el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archRuleCompose:add'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="primary"
                        disabled>新增</el-button>
                </el-tooltip>

                <el-button
                    type="danger"
                    @click="batchDelete"
                    :disabled="multipleSelection.length === 0"
                    v-if="jurisdicList['archRuleCompose:delete'] === 0">
                    批量删除
                </el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archRuleCompose:delete'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="danger"
                        disabled>批量删除</el-button>
                </el-tooltip>

                <el-button
                    type="success"
                    @click="exportData"
                    v-if="jurisdicList['archRuleCompose:export'] === 0">
                    导出
                </el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archRuleCompose:export'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="success"
                        disabled>导出</el-button>
                </el-tooltip>

                <el-button
                    type="warning"
                    @click="showImportDialog"
                    v-if="jurisdicList['archRuleCompose:import'] === 0">
                    导入
                </el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archRuleCompose:import'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="warning"
                        disabled>导入</el-button>
                </el-tooltip>
            </div>
        </div>

        <el-table
            :data="tableData"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            v-loading="loading">
            <el-table-column
                type="selection"
                width="55">
            </el-table-column>
            <el-table-column
                prop="ruleCode"
                label="规则编码"
                width="150">
            </el-table-column>
            <el-table-column
                prop="ruleName"
                label="规则名称"
                width="200">
            </el-table-column>
            <el-table-column
                prop="description"
                label="规则描述"
                width="250"
                show-overflow-tooltip>
            </el-table-column>
            <el-table-column
                prop="triggerType"
                label="触发器类型"
                width="120">
                <template slot-scope="scope">
                    <span v-if="scope.row.triggerType === 1">事项触发</span>
                    <span v-else-if="scope.row.triggerType === 2">定时触发</span>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="processElementCode"
                label="流程要素编码"
                width="150">
            </el-table-column>
            <el-table-column
                prop="state"
                label="状态"
                width="80">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">
                        {{ scope.row.state === 1 ? '启用' : '禁用' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="createErp"
                label="创建者"
                width="120">
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="创建时间"
                width="180">
                <template slot-scope="scope">
                    {{ formatDate(scope.row.createTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="300"
                fixed="right">
                <template slot-scope="scope">
                    <el-button
                        size="mini"
                        @click="showDetailDialog(scope.row)"
                        v-if="jurisdicList['archRuleCompose:view'] === 0">
                        详情
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archRuleCompose:view'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            size="mini"
                            disabled>详情</el-button>
                    </el-tooltip>

                    <el-button
                        size="mini"
                        type="primary"
                        @click="showEditDialog(scope.row)"
                        v-if="jurisdicList['archRuleCompose:edit'] === 0">
                        编辑
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archRuleCompose:edit'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            size="mini"
                            type="primary"
                            disabled>编辑</el-button>
                    </el-tooltip>

                    <el-button
                        size="mini"
                        :type="scope.row.state === 1 ? 'warning' : 'success'"
                        @click="updateState(scope.row)"
                        v-if="jurisdicList['archRuleCompose:edit'] === 0">
                        {{ scope.row.state === 1 ? '禁用' : '启用' }}
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archRuleCompose:edit'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            size="mini"
                            disabled>{{ scope.row.state === 1 ? '禁用' : '启用' }}</el-button>
                    </el-tooltip>

                    <el-button
                        size="mini"
                        type="danger"
                        @click="deleteRow(scope.row)"
                        v-if="jurisdicList['archRuleCompose:delete'] === 0">
                        删除
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archRuleCompose:delete'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            size="mini"
                            type="danger"
                            disabled>删除</el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>

        <!-- 新增/编辑对话框 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="dialogVisible"
            width="80%"
            :close-on-click-modal="false">
            <el-form
                :model="formData"
                :rules="rules"
                ref="formRef"
                label-width="180px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="规则编码" prop="ruleCode">
                            <el-input v-model="formData.ruleCode" placeholder="请输入规则编码"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规则名称" prop="ruleName">
                            <el-input v-model="formData.ruleName" placeholder="请输入规则名称"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="规则描述" prop="description">
                    <el-input
                        type="textarea"
                        :rows="3"
                        v-model="formData.description"
                        placeholder="请输入规则描述">
                    </el-input>
                </el-form-item>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="触发器类型" prop="triggerType">
                            <el-select v-model="formData.triggerType" placeholder="请选择触发器类型">
                                <el-option label="事项触发" :value="1"></el-option>
                                <el-option label="定时触发" :value="2"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="触发器动作" prop="triggerTypeAction">
                            <el-select v-model="formData.triggerTypeAction" placeholder="请选择触发器动作">
                                <el-option label="事项状态改变或属性改变" :value="1"></el-option>
                                <el-option label="事项状态改变" :value="2"></el-option>
                                <el-option label="事项属性改变" :value="3"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="流程要素编码" prop="processElementCode">
                            <el-input v-model="formData.processElementCode" placeholder="请输入流程要素编码"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态" prop="state">
                            <el-select v-model="formData.state" placeholder="请选择状态">
                                <el-option label="禁用" :value="0"></el-option>
                                <el-option label="启用" :value="1"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </div>
        </el-dialog>

        <!-- 详情对话框 -->
        <el-dialog
            title="自动化规则详情"
            :visible.sync="detailDialogVisible"
            width="80%">
            <el-descriptions :column="2" border>
                <el-descriptions-item label="规则编码">{{ detailData.ruleCode }}</el-descriptions-item>
                <el-descriptions-item label="规则名称">{{ detailData.ruleName }}</el-descriptions-item>
                <el-descriptions-item label="规则描述" :span="2">{{ detailData.description }}</el-descriptions-item>
                <el-descriptions-item label="触发器类型">
                    <span v-if="detailData.triggerType === 1">事项触发</span>
                    <span v-else-if="detailData.triggerType === 2">定时触发</span>
                    <span v-else>-</span>
                </el-descriptions-item>
                <el-descriptions-item label="流程要素编码">{{ detailData.processElementCode }}</el-descriptions-item>
                <el-descriptions-item label="状态">
                    <el-tag :type="detailData.state === 1 ? 'success' : 'danger'">
                        {{ detailData.state === 1 ? '启用' : '禁用' }}
                    </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="创建者">{{ detailData.createErp }}</el-descriptions-item>
                <el-descriptions-item label="创建时间" :span="2">{{ formatDate(detailData.createTime) }}</el-descriptions-item>
            </el-descriptions>

            <!-- JSON数据展示 -->
            <div v-if="detailData.oaData" style="margin-top: 20px;">
                <h4>OA数据：</h4>
                <pre>{{ formatJson(detailData.oaData) }}</pre>
            </div>
            <div v-if="detailData.msgData" style="margin-top: 20px;">
                <h4>消息数据：</h4>
                <pre>{{ formatJson(detailData.msgData) }}</pre>
            </div>
            <div v-if="detailData.actions" style="margin-top: 20px;">
                <h4>执行动作：</h4>
                <pre>{{ detailData.actions }}</pre>
            </div>
        </el-dialog>

        <!-- 导入对话框 -->
        <el-dialog
            title="导入自动化规则"
            :visible.sync="importDialogVisible"
            width="50%">
            <el-upload
                ref="upload"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :on-success="handleImportSuccess"
                :on-error="handleImportError"
                :before-upload="beforeUpload"
                :auto-upload="false"
                accept=".xlsx,.xls">
                <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                <el-button
                    style="margin-left: 10px;"
                    size="small"
                    type="success"
                    @click="downloadTemplate">
                    下载模板
                </el-button>
                <div slot="tip" class="el-upload__tip">只能上传xlsx/xls文件，且不超过10MB</div>
            </el-upload>
            <div slot="footer" class="dialog-footer">
                <el-button @click="importDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitUpload">确定导入</el-button>
            </div>
        </el-dialog>
    </div>
    <div v-else class="no-permission">
        <el-result
            icon="warning"
            title="权限不足"
            sub-title="您没有访问此页面的权限">
        </el-result>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import axios from 'axios';

export default {
    name: 'ArchRuleCompose',
    data() {
        return {
            // 查询表单
            formInline: {
                ruleCode: '',
                ruleName: '',
                triggerType: null,
                state: null
            },
            // 表格数据
            tableData: [],
            loading: false,
            multipleSelection: [],
            // 分页
            currentPage: 1,
            pageSize: 10,
            total: 0,
            // 对话框
            dialogVisible: false,
            dialogTitle: '',
            detailDialogVisible: false,
            importDialogVisible: false,
            // 表单数据
            formData: {
                id: null,
                ruleCode: '',
                ruleName: '',
                description: '',
                triggerType: null,
                triggerTypeAction: null,
                processElementCode: '',
                state: 1
            },
            detailData: {},
            // 表单验证规则
            rules: {
                ruleCode: [
                    { required: true, message: '请输入规则编码', trigger: 'blur' },
                    { max: 50, message: '规则编码长度不能超过50个字符', trigger: 'blur' }
                ],
                ruleName: [
                    { required: true, message: '请输入规则名称', trigger: 'blur' },
                    { max: 50, message: '规则名称长度不能超过50个字符', trigger: 'blur' }
                ],
                description: [
                    { required: true, message: '请输入规则描述', trigger: 'blur' },
                    { max: 100, message: '规则描述长度不能超过100个字符', trigger: 'blur' }
                ],
                triggerType: [
                    { required: true, message: '请选择触发器类型', trigger: 'change' }
                ],
                processElementCode: [
                    { required: true, message: '请输入流程要素编码', trigger: 'blur' },
                    { max: 32, message: '流程要素编码长度不能超过32个字符', trigger: 'blur' }
                ],
                state: [
                    { required: true, message: '请选择状态', trigger: 'change' }
                ]
            },
            // 上传相关
            uploadUrl: process.env.VUE_APP_BASE_API + '/arch/rule/compose/import',
            uploadHeaders: {
                'Authorization': 'Bearer ' + this.$store.getters.token
            },
            tips: '您没有该操作权限'
        };
    },
    computed: {
        ...mapState('option', ['jurisdicList'])
    },
    mounted() {
        this.loadData();
    },
    methods: {
        // 加载数据
        loadData() {
            this.loading = true;
            const params = {
                ...this.formInline,
                pageNum: this.currentPage,
                pageSize: this.pageSize
            };

            axios.post('/arch/rule/compose/list', params).then(response => {
                if (response.data.code === 200) {
                    this.tableData = response.data.data.records;
                    this.total = response.data.data.total;
                } else {
                    this.$message.error(response.data.msg || '查询失败');
                }
            }).catch(error => {
                console.error('查询失败:', error);
                this.$message.error('查询失败');
            }).finally(() => {
                this.loading = false;
            });
        },

        // 查询
        search() {
            this.currentPage = 1;
            this.loadData();
        },

        // 重置
        reset() {
            this.formInline = {
                ruleCode: '',
                ruleName: '',
                triggerType: null,
                state: null
            };
            this.search();
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val;
            this.loadData();
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val;
            this.loadData();
        },

        // 多选改变
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },

        // 显示新增对话框
        showAddDialog() {
            this.dialogTitle = '新增自动化规则';
            this.dialogVisible = true;
            this.formData = {
                id: null,
                ruleCode: '',
                ruleName: '',
                description: '',
                triggerType: null,
                triggerTypeAction: null,
                processElementCode: '',
                state: 1
            };
            this.$nextTick(() => {
                this.$refs.formRef.clearValidate();
            });
        },

        // 显示编辑对话框
        showEditDialog(row) {
            this.dialogTitle = '编辑自动化规则';
            this.dialogVisible = true;
            this.formData = { ...row };
            this.$nextTick(() => {
                this.$refs.formRef.clearValidate();
            });
        },

        // 显示详情对话框
        showDetailDialog(row) {
            this.detailData = { ...row };
            this.detailDialogVisible = true;
        },

        // 提交表单
        submitForm() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    const url = this.formData.id ? '/arch/rule/compose/edit' : '/arch/rule/compose/add';
                    axios.post(url, this.formData).then(response => {
                        if (response.data.code === 200) {
                            this.$message.success(response.data.data || '操作成功');
                            this.dialogVisible = false;
                            this.loadData();
                        } else {
                            this.$message.error(response.data.msg || '操作失败');
                        }
                    }).catch(error => {
                        console.error('操作失败:', error);
                        this.$message.error('操作失败');
                    });
                }
            });
        },

        // 删除行
        deleteRow(row) {
            this.$confirm('确认删除该自动化规则吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/arch/rule/compose/delete/${row.id}`).then(response => {
                    if (response.data.code === 200) {
                        this.$message.success('删除成功');
                        this.loadData();
                    } else {
                        this.$message.error(response.data.msg || '删除失败');
                    }
                }).catch(error => {
                    console.error('删除失败:', error);
                    this.$message.error('删除失败');
                });
            });
        },

        // 批量删除
        batchDelete() {
            if (this.multipleSelection.length === 0) {
                this.$message.warning('请选择要删除的数据');
                return;
            }

            this.$confirm('确认删除选中的自动化规则吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const ids = this.multipleSelection.map(item => item.id);
                axios.post('/arch/rule/compose/batchDelete', ids).then(response => {
                    if (response.data.code === 200) {
                        this.$message.success('批量删除成功');
                        this.loadData();
                    } else {
                        this.$message.error(response.data.msg || '批量删除失败');
                    }
                }).catch(error => {
                    console.error('批量删除失败:', error);
                    this.$message.error('批量删除失败');
                });
            });
        },

        // 更新状态
        updateState(row) {
            const newState = row.state === 1 ? 0 : 1;
            const action = newState === 1 ? '启用' : '禁用';

            this.$confirm(`确认${action}该自动化规则吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post('/arch/rule/compose/updateState', null, {
                    params: {
                        id: row.id,
                        state: newState
                    }
                }).then(response => {
                    if (response.data.code === 200) {
                        this.$message.success(`${action}成功`);
                        this.loadData();
                    } else {
                        this.$message.error(response.data.msg || `${action}失败`);
                    }
                }).catch(error => {
                    console.error(`${action}失败:`, error);
                    this.$message.error(`${action}失败`);
                });
            });
        },

        // 导出数据
        exportData() {
            const params = { ...this.formInline };
            axios.post('/arch/rule/compose/export', params, {
                responseType: 'blob'
            }).then(response => {
                const blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = '自动化规则数据.xlsx';
                link.click();
                window.URL.revokeObjectURL(url);
                this.$message.success('导出成功');
            }).catch(error => {
                console.error('导出失败:', error);
                this.$message.error('导出失败');
            });
        },

        // 显示导入对话框
        showImportDialog() {
            this.importDialogVisible = true;
        },

        // 下载模板
        downloadTemplate() {
            axios.get('/arch/rule/compose/downloadTemplate', {
                responseType: 'blob'
            }).then(response => {
                const blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = '自动化规则导入模板.xlsx';
                link.click();
                window.URL.revokeObjectURL(url);
                this.$message.success('模板下载成功');
            }).catch(error => {
                console.error('模板下载失败:', error);
                this.$message.error('模板下载失败');
            });
        },

        // 上传前检查
        beforeUpload(file) {
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                           file.type === 'application/vnd.ms-excel';
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isExcel) {
                this.$message.error('上传文件只能是 Excel 格式!');
            }
            if (!isLt10M) {
                this.$message.error('上传文件大小不能超过 10MB!');
            }
            return isExcel && isLt10M;
        },

        // 提交上传
        submitUpload() {
            this.$refs.upload.submit();
        },

        // 导入成功
        handleImportSuccess(response) {
            if (response.code === 200) {
                this.$message.success(response.data || '导入成功');
                this.importDialogVisible = false;
                this.loadData();
            } else {
                this.$message.error(response.msg || '导入失败');
            }
        },

        // 导入失败
        handleImportError(error) {
            console.error('导入失败:', error);
            this.$message.error('导入失败');
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '-';
            return new Date(date).toLocaleString('zh-CN');
        },

        // 格式化JSON
        formatJson(jsonStr) {
            if (!jsonStr) return '';
            try {
                return JSON.stringify(JSON.parse(jsonStr), null, 2);
            } catch (e) {
                return jsonStr;
            }
        }
    }
};
</script>

<style scoped>
.arch-rule-compose-management {
    padding: 20px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header-left {
    display: flex;
    gap: 10px;
}

.no-permission {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

pre {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
}
</style>
