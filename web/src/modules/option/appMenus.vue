<template>
    <div class="app-setting-menus">
        <el-tabs
            tab-position="left"
            style="height: 100%"
        >
            <el-tab-pane
                v-for="item in tabList"
                :key="item.code"
                :label="item.name"
                :name="item.code"
                :lazy="true"
            >
                <app-menus-setting v-model="item.id" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import ModuleOption from '@/models/moduleOption';
import appMenusSetting from './components/appMenusSetting';

export default {
    components: {
        appMenusSetting,
    },
    data() {
        return {
            tabList: [],
        };
    },
    mounted() {
        ModuleOption.getModuleList({
            // status: 'inactive',
        }).then((data) => {
            this.tabList = data || [];
        });
    },
    methods: {
    },
};
</script>
<style lang="less">
.app-setting-menus {
    height: calc(~"100vh - 160px");
    overflow-y: auto;
}
</style>
