<template>
    <div class="global-setting-version">
        <el-form
            :model="version"
            :rules="rules"
            ref="versionForm"
            label-width="100px"
            class="form-info"
        >
            <el-form-item
                label="版本号"
                prop="name"
            >
                <el-input
                    v-model="version.name"
                    placeholder="请输入版本号"
                />
            </el-form-item>
            <el-form-item
                label="发布日期"
                prop="releaseDate"
            >
                <el-date-picker
                    type="datetime"
                    placeholder="选择发布日期"
                    format="yyyy-MM-dd HH:mm"
                    v-model="version.releaseDate"
                    value-format="timestamp"
                />
            </el-form-item>
            <el-form-item
                label="详情链接"
                prop="detailUrl"
            >
                <el-input
                    placeholder="http(s)://"
                    v-model="version.detailUrl"
                />
            </el-form-item>
            <el-form-item
                label="发布内容"
                prop="content"
            >
                <el-input
                    type="textarea"
                    :rows="20"
                    placeholder="请输入发布内容"
                    v-model="version.content"
                />
            </el-form-item>
        </el-form>
        <div class="footer">
            <div class="warn-info warning-text">
                版本说明每次提交均会按照发布时间直接生效，请谨慎操作。
            </div>
            <el-button
                type="primary"
                @click="onSubmit"
                v-if="jurisdicList['archConfig:version:add'] === 0"
            >
                提交
            </el-button>
            <el-tooltip
                v-else-if="jurisdicList['archConfig:version:add'] === 1"
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    type="primary"
                    style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                >
                    提交
                </el-button>
            </el-tooltip>

            <el-button
                v-if="jurisdicList['archConfig:version:look'] === 0"
                @click="onPreView"
            >
                预览
            </el-button>
            <el-tooltip
                v-else-if="jurisdicList['archConfig:version:look'] === 1"
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    style="color: #c0c4cc;
                        cursor: not-allowed;
                        background-image: none;
                        background-color: #fff;
                        border-color: #ebeef5;"
                >
                    预览
                </el-button>
            </el-tooltip>
        </div>
        <jacp-version ref="version" />
    </div>
</template>

<script>
import GlobalSetting from '@/models/globalSetting';
import { mapState } from 'vuex';

export default {
    data() {
        return {
            version: {
                name: '',
                releaseDate: new Date(),
                detailUrl: '',
                content: '',
            },
            showVersion: false,
            rules: {
                name: [
                    { required: true, message: '请输入版本号', trigger: 'blur' },
                    {
                        min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur',
                    },
                ],
                releaseDate: [
                    {
                        type: 'date', required: true, message: '请选择发布日期', trigger: 'change',
                    },
                ],
                content: [
                    { required: true, message: '请输入发布内容', trigger: 'blur' },
                ],
            },
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    created() {
        this.getLastVersionInfo();
    },
    methods: {
        getLastVersionInfo() {
            GlobalSetting.getVersionInfo().then((data) => {
                if (data) {
                    this.version = data;
                }
            });
        },
        onSubmit() {
            this.$refs.versionForm.validate((valid) => {
                if (valid) {
                    GlobalSetting.addVersionRelease(this.version).then(() => {
                        this.$notify({
                            title: '保存成功！',
                            type: 'success',
                            duration: 2000,
                        });
                        this.$refs.versionForm.resetFields();
                    }).catch(() => {
                        this.$notify({
                            title: '保存失败！',
                            type: 'error',
                            duration: 2000,
                        });
                    });
                }
            });
        },
        onPreView() {
            this.$refs.version.showVersion(this.version);
        },
    },
};
</script>

<style lang="less">
    .global-setting-version {
        margin-left: 40px;

        & .form-info {
            overflow: auto;
            height: 586px;
        }

        & .footer {
            margin-left: 100px;

            & .warn-info {
                font-size: 14px;
                color: #666;
                margin-bottom: 24px;
            }
        }
        & .warning-text{
            background-color: #fdf6ec;
            color: #e6a23c !important;
            padding: 8px 16px;
            box-sizing: border-box;
            border-radius: 4px;
        }
    }
</style>
