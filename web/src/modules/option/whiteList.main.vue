<template>
    <div class="white-list__main">
        <el-form
            :inline="true"
            class="white-list__org"
            label-position="left"
        >
            <el-form-item
                label="部门"
                prop="userOrgId"
            >
                <el-select
                    v-model="userOrgId"
                    placeholder="请选择"
                    filterable
                    :default-first-option="true"
                    @change="changeUserOrg"
                >
                    <el-option
                        v-for="item in orgList"
                        :key="item.orgId"
                        :value="item.orgId"
                        :label="item.orgName"
                    >
                        <span style="float: left">{{ item.orgName }}</span>
                        <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                    </el-option>
                </el-select>
                <!-- <span class="white-list__main__warning">*注意：所有当前页的设置均仅影响此一级部门。</span> -->
            </el-form-item>
        </el-form>
        <el-tabs
            v-model="formName"
            type="border-card"
        >
            <el-tab-pane
                label="提交白名单"
                name="submitList"
            >
                <submit-white-list
                    :user-org-id="userOrgId"
                    :user-org-name="userOrgName"
                    :submit-data="submitData"
                    @refreshList="getSubmitWhiteList"
                />
            </el-tab-pane>
            <el-tab-pane
                label="受理白名单"
                name="resave"
            >
                <receive-white-list
                    :user-org-id="userOrgId"
                    :user-org-name="userOrgName"
                    :receive-data="receiveData"
                    @refreshList="getReceiveWhiteList"
                />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import WhiteList from '@/models/whiteList';
import submitWhiteList from './components/submitWhiteList';
import receiveWhiteList from './components/receiveWhiteList';

export default {
    components: {
        receiveWhiteList,
        submitWhiteList,
    },
    data() {
        return {
            formName: 'submitList',
            userOrgId: '',
            userOrgName: '',
            orgList: [],
            submitData: {},
            receiveData: {},
        };
    },
    mounted() {
        // 加载一级部门数据
        this.loadOrglist();
    },
    methods: {
        // 加载部门列表
        loadOrglist() {
            WhiteList.getOrgList().then((data) => {
                this.orgList = data;
                if (this.orgList.length > 0) {
                    this.userOrgId = this.orgList[0].orgId;
                }
                // 加载白名单数据
                this.getSubmitWhiteList();
                this.getReceiveWhiteList();
            });
        },
        getSubmitWhiteList() {
            WhiteList.getWhiteList({
                orgId: this.userOrgId || '',
                typeId: 1,
            }).then((data) => {
                this.submitData = data;
                // 暂时未返回部门id字段 手动添加
                this.submitData.orgId = this.userOrgId;
            });
        },
        getReceiveWhiteList() {
            WhiteList.getWhiteList({
                orgId: this.userOrgId || '',
                typeId: 2,
            }).then((data) => {
                this.receiveData = data;
                // 暂时未返回部门id字段 手动添加
                this.receiveData.orgId = this.userOrgId;
            });
        },
        changeUserOrg(userOrgId) {
            this.orgList.forEach((item) => {
                if (item.orgId === userOrgId) {
                    this.userOrgName = item.orgName;
                }
            });

            // 根据部门查询对应报名单列表
            this.getSubmitWhiteList();
            this.getReceiveWhiteList();
        },
    },
};
</script>

<style lang="less">
    .white-list__main{
        padding: 20px;
        &__warning{
            color: #ff4949;
            font-size: 12px;
        }
    }

    .white-list__org{
        & .el-input{
            width: 400px;
        }
    }
</style>
