<template>
    <div
        class="form-option__main"
        style="overflow-y: auto;"
    >
        <div
            class="form-option-org"
        >
            <span class="form-option-org__label">部门</span>
            <el-select
                v-model="userOrgId"
                placeholder="请选择"
                filterable
                :default-first-option="true"
                @change="changeUserOrg"
                class="form-list"
            >
                <el-option
                    v-for="item in orgList"
                    :key="item.orgId"
                    :value="item.orgId"
                    :label="item.orgName"
                >
                    <span style="float: left">{{ item.orgName }}</span>
                    <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                </el-option>
            </el-select>
        </div>
        <!-- 不同业务类型的Tabs -->
        <el-tabs
            v-model="formName"
            type="border-card"
        >
            <el-tab-pane
                label="部门受理业务需求"
                name="demand"
                lazy
            >
                <demand-form
                    ref="demand"
                    :user-org-id="userOrgId"
                    demand-type="demand"
                    :unshow-field-list="unshowFieldList"
                />
            </el-tab-pane>
            <el-tab-pane
                label="部门受理产品需求"
                name="prd-demand"
            >
                <demand-form
                    ref="prd-demand"
                    :user-org-id="userOrgId"
                    demand-type="prd-demand"
                    :unshow-field-list="unshowFieldList"
                />
            </el-tab-pane>
            <el-tab-pane
                label="部门受理研发需求"
                name="dev-demand"
            >
                <demand-form
                    ref="dev-demand"
                    :user-org-id="userOrgId"
                    demand-type="dev-demand"
                    :unshow-field-list="unshowFieldList"
                />
            </el-tab-pane>
            <el-tab-pane
                label="业务优先级"
                name="priority"
            >
                <priority-dic :user-org-id="userOrgId" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import FormOption from '@/models/formOption';
import demandForm from './components/demandForm';
import priorityDic from './components/priorityDic';

export default {
    components: {
        demandForm,
        priorityDic,
    },
    data() {
        return {
            formName: 'demand',
            orgList: [],
            userOrgId: '',
            userOrgName: '',
            unshowFieldList: [],
        };
    },
    mounted() {
        this.getAdminOrgList();
    },
    methods: {
        // 加载部门列表
        getAdminOrgList() {
            FormOption.getAdminOrgList().then((data) => {
                this.orgList = data;
                if (this.orgList.length > 0) {
                    this.userOrgId = this.orgList[0].orgId;
                }
            });
        },
        changeUserOrg(userOrgId) {
            this.orgList.forEach((item) => {
                if (item.orgId === userOrgId) {
                    this.userOrgName = item.orgName;
                }
            });
        },
    },
};
</script>

<style lang="less">

    .form-option__main{
        padding: 20px;
    }
    .form-option-org{
        margin-bottom: 16px;
        & .el-input{
            width: 400px;
            margin-right:16px;
        }
        &__label{
            font-size: 12px;
            color: #696969;
            margin-right: 8px;
        }
    }
    .form-option-dynamicForm{
        width: 100%;
        padding: 24px;
        margin-bottom: 32px;
        background-color: #f3f5f6;
        border-radius: 4px;
    // padding: 4px 8px;
        // height: calc(~"100vh - 78px");
        overflow: auto;
    }
</style>
