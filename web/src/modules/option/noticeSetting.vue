<template>
    <el-collapse
        accordion
        v-model="activeNames"
        @change="handleChange"
    >
        <el-collapse-item
            name="1"
            :style="activeNames==1?'padding-bottom:20px':'padding-bottom:0px'"
        >
            <span
                class="collapse-title"
                slot="title"
            > <span>邮箱</span>
                <div
                    @click="stopProp"
                    v-if="editPower['message:notify:enable']!=2"
                >
                    <el-switch
                        v-model="emailSwitch"
                        @change="openDialog"
                        :disabled="this.noticeForm.id==null || editPower['message:notify:enable'] == 1"
                    /></div></span>
            <div class="notice-tips">
                <p class="tip">配置邮箱服务器后，可通过邮件推送消息给到相关人员</p>
                <el-form
                    ref="noticeForm"
                    :model="noticeForm"
                    :rules="rules"
                    :label-position="'top'"
                    class="notice-form"
                >
                    <el-row>
                        <el-col :span="8">
                            <el-form-item
                                prop="mailHost"
                                label="邮箱服务器地址"
                            >
                                <el-input
                                    :disabled="!emailSwitch || !edit"
                                    v-model="noticeForm.mailHost"
                                    placeholder="请输入地址，如域名或主机端口，如smtp.jd.com、***********:8080~"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                prop="mailFromAddr"
                                label="邮箱发送账号"
                            >
                                <el-input
                                    :disabled="!emailSwitch || !edit"
                                    v-model="noticeForm.mailFromAddr"
                                    placeholder="请输入发送账号，如*******************"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col
                            :span="8"
                            v-show="edit && emailSwitch"
                        >
                            <el-form-item
                                label="邮箱密码"
                            >
                                <el-input
                                    :disabled="!emailSwitch || !edit"
                                    v-model="noticeForm.mailUserPassword"
                                    type="password"
                                    placeholder="请输入密码~"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <!-- 配置邮箱服务器后，可通过邮件推送消息给到相关人员<br>
                *邮箱服务器地址:请输入地址，如域名或主机端口，如smtp.jd.com或***********:8080～ <br>
                *邮箱发送账号:请输入发送账号，如******************* <br>
                邮件账号密码:请输入账号密码～ -->
            </div>
            <p v-show="emailSwitch">
                <el-button
                    type="primary"
                    @click="openDialog('test')"
                    :disabled="editPower['message:notify:connect'] == 1"
                    v-if="editPower['message:notify:connect']!==2"
                >
                    测试连通性
                </el-button>
                <el-button
                    v-show="!edit"
                    @click="edit=true"
                    :disabled="editPower['message:notify:save'] == 1"
                    v-if="editPower['message:notify:save']!==2"
                >
                    编辑
                </el-button>
                <el-button
                    v-show="edit"
                    type="primary"
                    @click="openDialog('save')"
                >
                    保存
                </el-button>
                <el-button
                    v-show="edit"
                    @click="queryMailDetail"
                >
                    取消
                </el-button>
            </p>
        </el-collapse-item>
    </el-collapse>
</template>
<script type="text/javascript">
import SecurityClient from '@/models/security';
import store from '$platform.store';

export default {
    data() {
        const regMail = /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/;

        const validateEmail = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入正确的邮箱地址~'));
            } else {
                if (value !== '') {
                    const reg = regMail;
                    if (!reg.test(value)) {
                        callback(new Error('请输入正确的邮箱地址~'));
                    }
                }
                callback();
            }
        };
        return {
            edit: false,
            emailSwitch: false,
            activeNames: ['1'],
            noticeForm: {},
            regMail,
            rules: {
                mailHost: [
                    { required: true, message: '请输入地址，如域名或主机端口，如smtp.jd.com、***********:8080~', trigger: 'blur' },
                ],
                mailFromAddr: [
                    { validator: validateEmail },
                    { required: true, message: '请输入发送账号，如*******************', trigger: 'blur' },
                ],
                mailUserPassword: [
                    { required: true, message: '请输入账号密码~', trigger: 'blur' },
                ],
            },
        };
    },
    props: {
        editPower: {
            type: Object,
            default: () => ({}),
        },
    },
    mounted() {
        this.queryMailDetail();
    },
    methods: {
        // 获取页面详情
        queryMailDetail() {
            this.edit = false;
            // this.resetForm();
            SecurityClient.bizMailConfig().then((res) => {
                this.noticeForm = res;
                this.emailSwitch = res.mailEnable; // switch开启与关闭
                if (res.mailFromAddr === '' && res.mailHost === '') {
                    this.resetForm();
                }
            });
        },
        // 查询是否开启邮箱配置
        // queryEnableMsg() {
        //     SecurityClient.queryEnableMsg().then((res) => {
        //         this.emailSwitch = res;
        //     });
        // },
        stopProp(e) {
            e.stopPropagation();
        },
        handleChange(val) {
            console.log(val);
        },
        resetForm() {
            this.$refs.noticeForm.resetFields();
        },
        openDialog(val) {
            const tipObj = {
                title: '提示',
            };
            switch (val) {
            case 'test':
                tipObj.content = '测试配置的邮箱渠道信息是否正常发送邮件，邮箱收件地址:请输入邮箱地址，如*********~';
                this.$refs.noticeForm.validate((valid) => {
                    if (valid) {
                        const h = this.$createElement;
                        this.$prompt('测试连通性', {
                            title: '测试连通性',
                            message: h('div', null, [
                                h('p', null, '测试配置的邮箱渠道信息是否正常发送邮件 '),
                                h('p', null, '邮箱收件地址:'),
                            ]),
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            inputPlaceholder: '请输入邮箱地址,如*******************',
                            inputPattern: this.regMail,
                            inputErrorMessage: '请输入正确的邮箱地址~',
                        }).then(({ value }) => {
                            this.noticeForm.toEmails = value.split();
                            this.testConnectConfig();
                        }).catch(() => {
                            this.$message({
                                type: 'info',
                                message: '取消输入',
                            });
                        });
                    }
                });
                break;
            case 'save':
                tipObj.content = '保存配置信息后，系统的操作将读取最新配置信息进行推送，确认保存吗？';
                // eslint-disable-next-line consistent-return
                this.$refs.noticeForm.validate((valid) => {
                    if (valid) {
                        this.commonDialog(tipObj, val);
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
                break;
            case true:
                tipObj.content = '开启后，系统的操作将通过邮件发送提醒，确认开启吗?';
                this.commonDialog(tipObj, 'open');
                break;
            case false:
                this.edit = false;
                tipObj.content = '关闭后，系统的操作将不会通过邮件发送提醒，确认关闭吗？';
                this.commonDialog(tipObj, 'close');
                break;
            default:
            }
        },
        commonDialog(tipObj, type) {
            this.$confirm(tipObj.content, tipObj.title, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                switch (type) {
                case 'open':
                    this.updateMailConfig();
                    break;
                case 'close':
                    // this.queryMailDetail();
                    this.updateMailConfig();
                    // this.resetForm();
                    break;
                case 'save':
                    this.edit = false;
                    this.updateMailConfig();
                    break;
                default:
                }
            }).catch(() => {
                if (type === 'open' || type === 'close') {
                    this.emailSwitch = !this.emailSwitch;
                }
                this.$message({
                    type: 'info',
                    message: '已取消',
                });
            });
        },
        // 保存
        updateMailConfig() {
            this.noticeForm.mailEnable = this.mailEnable;
            const params = Object.assign(this.noticeForm, { optErp: store.state.user.erp });
            if (this.noticeForm.id == null) {
                this.addBizMailConfig(params);
            } else {
                this.saveBizMailConfig(params);
            }
        },
        // 编辑保存
        saveBizMailConfig(params) {
            SecurityClient.saveBizMailConfig(params).then(() => {
                this.$message.success('保存成功');
            });
        },
        // 新增保存
        addBizMailConfig(params) {
            SecurityClient.addBizMailConfig(params).then(() => {
                this.queryMailDetail();
                this.$message.success('保存成功');
            });
        },
        // 测试连通性
        testConnectConfig() {
            const {
                mailFromAddr, mailHost, mailUserPassword, toEmails,
            } = this.noticeForm;
            const params = {
                mailFromAddr, mailHost, mailUserPassword, toEmails,
            };
            SecurityClient.testConnectConfig(params).then(() => {
                this.$message({
                    type: 'success',
                    message: '测试连通性成功',
                });
            });
        },
    },
};
</script>
<style lang="less">
.collapse-title {
    flex: 1 0 90%;
    order: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.el-collapse-item {
    background: #F7F8F9;
    // padding-bottom: 10px;
    border-radius: 6px
}
.el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
    background: #F7F8F9;
    padding: 0 14px;
}
.el-collapse-item__wrap {
    margin: 0 10px;
    padding: 0 16px;
    border-radius: 6px;
}
.notice-tips {
    .tip {
        color: #909399;
        font-size: 12px;
    }
}
.notice-form {
    .el-row>.el-col:nth-of-type(2){
        padding: 0 16px;
    }
}
</style>
