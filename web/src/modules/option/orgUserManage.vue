<template>
    <el-container class="org-user-manage">
        <el-aside width="300px">
            <el-tree
                :data="orgData"
                node-key="code"
                default-expand-all
                :expand-on-click-node="true"
                ref="tree"
                :props="defaultProps"
                @current-change="selectOrg"
            >
                <span slot-scope="{ node }">
                    <span>{{ node.label }}</span>
                </span>
            </el-tree>
        </el-aside>
        <el-main>
            <el-button
                @click="showUserAddDlg"
                size="small"
                type="primary"
                v-if="jurisdicList['archOrg:per:add'] === 0"
            >
                添加人员
            </el-button>
            <el-tooltip
                v-else-if="jurisdicList['archOrg:per:add'] === 1"
                effect="dark"
                :content="tips"
                placement="top"
            >
                <el-button
                    size="mini"
                    style="color: #c0c4cc;
                        cursor: not-allowed;
                        background-image: none;
                        background-color: #fff;
                        border-color: #ebeef5;"
                >
                    添加人员
                </el-button>
            </el-tooltip>

            <el-table :data="userData">
                <el-table-column
                    label="工号"
                    prop="erp"
                />
                <el-table-column
                    label="姓名"
                    prop="realName"
                />
                <el-table-column
                    label="email"
                    prop="email"
                />
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            @click="showUserUpdateDlg(scope.row)"
                            v-if="jurisdicList['archOrg:per:update'] === 0"
                        >
                            编辑
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archOrg:per:update'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                size="mini"
                                style="color: #c0c4cc;
                                    cursor: not-allowed;
                                    background-image: none;
                                    background-color: #fff;
                                    border-color: #ebeef5;"
                            >
                                编辑
                            </el-button>
                        </el-tooltip>
                        <el-button
                            size="mini"
                            type="danger"
                            @click="removeUser(scope.row)"
                            v-if="jurisdicList['archOrg:per:del'] === 0"
                        >
                            删除
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archOrg:per:del'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                size="mini"
                                style="color: #c0c4cc;
                                    cursor: not-allowed;
                                    background-image: none;
                                    background-color: #fff;
                                    border-color: #ebeef5;"
                            >
                                删除
                            </el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <el-dialog
                title="添加人员"
                :close-on-click-modal="false"
                :visible.sync="userAddDlgVisible"
            >
                <el-form
                    :model="userAddForm"
                    :rules="userAddRules"
                    ref="userAddFormRef"
                >
                    <!-- <el-form-item
                        prop="erp"
                        label="工号"
                    >
                        <el-input v-model="userAddForm.erp" />
                    </el-form-item> -->
                    <el-form-item
                        prop="erp"
                        label="用户"
                    >
                        <!-- <el-input v-model="userAddForm.realName" /> -->
                        <el-select
                            v-model="userAddForm.erp"
                            placeholder="请选择用户"
                            style="width: 100%;"
                            filterable
                            remote
                            :remote-method="getUserList"
                            @change="userChange"
                        >
                            <el-option
                                v-for="item in UserOptions"
                                :key="item.userName"
                                :label="item.userName"
                                :value="item.userName"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        prop="realName"
                        label="姓名"
                    >
                        <el-input
                            v-model="userAddForm.realName"
                            placeholder="请输入姓名"
                        />
                    </el-form-item>
                    <el-form-item label="部门编码">
                        <el-input
                            v-model="userAddForm.organizationCode"
                            disabled
                        />
                    </el-form-item>
                    <!-- <el-form-item
                        prop="password"
                        label="密码"
                    >
                        <el-input v-model="userAddForm.password" />
                    </el-form-item> -->
                </el-form>
                <div
                    slot="footer"
                    class="dialog-footer"
                >
                    <el-button @click="userAddDlgVisible = false">取 消</el-button>
                    <el-button
                        type="primary"
                        @click="addUser"
                    >
                        确 定
                    </el-button>
                </div>
            </el-dialog>
            <el-dialog
                title="修改人员信息"
                :visible.sync="userUpdateDlgVisible"
            >
                <el-form
                    :model="userUpdateForm"
                    :rules="userUpdateRules"
                    ref="userUpdateFormRef"
                >
                    <el-form-item label="用户">
                        <el-input
                            v-model="userUpdateForm.erp"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item
                        label="姓名"
                        prop="realName"
                    >
                        <el-input
                            v-model="userUpdateForm.realName"
                        />
                    </el-form-item>
                    <!-- <el-form-item label="邮箱">
                        <el-input v-model="userUpdateForm.email" />
                    </el-form-item> -->
                    <el-form-item label="部门编码">
                        <el-input
                            v-model="userUpdateForm.organizationCode"
                            disabled
                        />
                    </el-form-item>
                    <!-- <el-form-item
                        label="密码"
                        prop="password"
                    >
                        <el-input v-model="userUpdateForm.password" />
                    </el-form-item> -->
                </el-form>
                <div
                    slot="footer"
                    class="dialog-footer"
                >
                    <el-button @click="userUpdateDlgVisible = false">取 消</el-button>
                    <el-button
                        type="primary"
                        @click="updateUser"
                    >
                        确 定
                    </el-button>
                </div>
            </el-dialog>
        </el-main>
    </el-container>
</template>
<script type="text/javascript">
import { apiGateway } from '@/plugins/http';
import { mapState } from 'vuex';
import { User } from '@/models/user';

function userForm() {
    return {
        erp: '',
        realName: '',
        email: '',
        password: '',
        organizationCode: '',
    };
}

/**
 *   archOrg:per:view 人员管理-查看
 *   archOrg:per:add 人员管理-新增
 *   archOrg:per:update 人员管理-编辑
 *   archOrg:per:del 人员管理-删除
 */

export default {
    // components: {},
    data() {
        // const validatePas = (rule, value, callback) => {
        //     // eslint-disable-next-line no-plusplus
        //     let data = this.userAddForm.erp.toString().toLowerCase();
        //     const Pwd = this.userAddForm.password.toString().toLowerCase();
        //     // eslint-disable-next-line no-plusplus
        //     for (let i = 0; i < data.length - 2; i++) {
        //         if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
        //             callback(new Error('密码不可包含与用户特征相关的信息，不允许提交'));
        //         }
        //     }
        //     data = this.userAddForm.realName.toString().toLowerCase();
        //     // eslint-disable-next-line no-plusplus
        //     for (let i = 0; i < data.length - 2; i++) {
        //         if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
        //             callback(new Error('密码不可包含与用户特征相关的信息，不允许提交'));
        //         }
        //     }
        //     data = this.userAddForm.email.toString().toLowerCase();
        //     // eslint-disable-next-line no-plusplus
        //     for (let i = 0; i < data.length - 2; i++) {
        //         if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
        //             callback(new Error('密码不可包含与用户特征相关的信息，不允许提交'));
        //         }
        //     }
        //     callback();
        // };
        // const validatePasEdit = (rule, value, callback) => {
        //     // eslint-disable-next-line no-plusplus
        //     let data = this.userUpdateForm.erp.toString().toLowerCase();
        //     const Pwd = this.userUpdateForm.password.toString().toLowerCase();
        //     // eslint-disable-next-line no-plusplus
        //     for (let i = 0; i < data.length - 2; i++) {
        //         if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
        //             callback(new Error('密码不可包含与用户特征相关的信息，不允许提交'));
        //         }
        //     }
        //     data = this.userUpdateForm.realName.toString().toLowerCase();
        //     // eslint-disable-next-line no-plusplus
        //     for (let i = 0; i < data.length - 2; i++) {
        //         if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
        //             callback(new Error('密码不可包含与用户特征相关的信息，不允许提交'));
        //         }
        //     }
        //     data = this.userUpdateForm.email.toString().toLowerCase();
        //     // eslint-disable-next-line no-plusplus
        //     for (let i = 0; i < data.length - 2; i++) {
        //         if (Pwd.indexOf(data.substring(i, i + 3)) !== -1) {
        //             callback(new Error('密码不可包含与用户特征相关的信息，不允许提交'));
        //         }
        //     }
        //     callback();
        // };
        return {
            curOrgNode: {},
            orgData: [],
            userData: [],
            userAddDlgVisible: false,
            userUpdateDlgVisible: false,
            userAddForm: userForm(),
            userUpdateForm: userForm(),
            defaultProps: {
                children: 'children',
                label: 'name',
            },
            userAddRules: {
                erp: [{ required: true, message: '请选择用户', trigger: 'blur' }],
                realName: [
                    { required: true, message: '请输入姓名', trigger: 'blur' },
                    { max: 6, message: '姓名最长6个字符', trigger: 'blur' },
                ],
                // email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
                // password: [
                //     { required: true, message: '请输入密码', trigger: 'blur' },
                //     {
                //         pattern: /^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/,
                //         trigger: ['blur', 'change'],
                //         message: '密码必须包含大小写字母,特殊字符和数字，且长度不低于8位。',
                //     },
                //     { required: true, trigger: ['blur', 'change'], validator: validatePas },
                // ],
            },
            userUpdateRules: {
                // password: [
                //     { required: true, message: '请输入密码', trigger: 'blur' },
                //     {
                //         pattern: /^(?![A-Za-z0-9]+$)(?![a-z0-9\W]+$)(?![A-Za-z\W]+$)(?![A-Z0-9\W]+$)[a-zA-Z0-9\W]{8,}$/,
                //         trigger: ['blur', 'change'],
                //         message: '密码必须包含大小写字母,特殊字符和数字，且长度不低于8位。',
                //     },
                //     { required: true, trigger: ['blur', 'change'], validator: validatePasEdit },
                // ],
                realName: [
                    { max: 6, message: '姓名最长6个字符', trigger: 'blur' },
                ],
            },
            UserOptions: [],
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    mounted() {
        apiGateway
            .get('uaos/api/v1/org/level/0')
            .then((data) => {
                this.orgData = this.listToTreeList(data);
            });
        // this.getUserList();
    },
    methods: {
        listToTreeList(data) {
            const map = {};
            data.forEach((item) => {
                map[item.code] = item;
            });
            const val = [];
            data.forEach((item) => {
                const pnode = map[item.parentCode];
                if (pnode) {
                    (pnode.children || (pnode.children = [])).push(item);
                } else {
                    val.push(item);
                }
            });
            return val;
        },
        currentNode() {
            return this.$refs.tree.getCurrentNode();
        },
        refreshUserList() {
            this.selectOrg(this.curOrgNode);
        },
        selectOrg(node) {
            this.curOrgNode = node;
            apiGateway
                .get(`uaos/api/v1/org/children?code=${node.code}`)
                .then((data) => {
                    this.$set(node, 'children', data);
                });

            apiGateway
                .get(`uaos/api/v1/user/members?orgCode=${node.code}`)
                .then((rd) => {
                    const users = [];
                    (rd || []).forEach((data) => {
                        users.push({
                            erp: data.userName,
                            realName: data.realName,
                            email: data.email,
                            password: data.password,
                            organizationCode: data.organizationCode,
                        });
                    });
                    this.userData = users;
                });
        },
        showUserAddDlg() {
            if (!this.curOrgNode.code) {
                this.$message.error('请选择组织结构');
                return;
            }
            this.userAddDlgVisible = true;
            this.userAddForm.organizationCode = this.currentNode().code;
        },
        showUserUpdateDlg(node) {
            this.userUpdateDlgVisible = true;
            this.userUpdateForm = Object.assign({}, node);
        },
        removeUser(node) {
            this.$confirm('是否需要删除?').then(() => {
                apiGateway
                    .delete('uaos/api/v1/user', {
                        data: {
                            erp: node.erp,
                        },
                    })
                    .then(() => {
                        this.$message('删除成功。');
                        this.refreshUserList();
                    });
            });
        },
        addUser() {
            this.$refs.userAddFormRef.validate((valid) => {
                if (valid) {
                    this.userAddDlgVisible = false;
                    apiGateway
                        .post('uaos/api/v1/user', this.userAddForm)
                        .then(() => {
                            this.userAddForm = userForm();
                            this.$message.success('添加成功');
                            this.refreshUserList();
                        });
                } else {
                    this.$message.warning('请将信息填写完整!');
                }
            });
        },
        updateUser() {
            this.$refs.userUpdateFormRef.validate((valid) => {
                if (valid) {
                    this.userUpdateDlgVisible = false;
                    apiGateway
                        .patch('uaos/api/v1/user', this.userUpdateForm)
                        .then(() => {
                            this.$message.success('修改成功');
                            this.userUpdateDlgVisible = false;
                            this.refreshUserList();
                        });
                } else {
                    this.$message.warning('请将信息填写完整!');
                }
            });
        },
        getUserList(keyword = '') {
            User.getOrgUserList({ keyword }).then((res) => {
                this.UserOptions = res;
            });
        },
        userChange(key) {
            const data = this.UserOptions.find(item => item.userName === key);
            this.userAddForm.email = data.email;
            this.userAddForm.erp = data.userName;
        },
    },
};
</script>
<style lang="less">
.org-user-manage {
    height: calc(~'100vh - 140px');
    .el-tree {
        .el-tree-node .el-tree-node__content {
            height: 40px;
            background: rgba(235, 235, 235);
            margin: 1px 0;
            color: #000;
        }
        .is-current > .el-tree-node__content {
            background: rgb(146, 220, 221);
        }
        .el-tree-node__children .el-tree-node__content {
            color: #000;
        }
    }
}
</style>
