<template>
    <div class="extendform-setting-edit">
        <div class="extendform-setting-edit__header">
            <span class="label">应用: {{ appName }}</span>
            <span class="label">表单名: {{ schemaName }}</span>
            <div class="extendform-setting-edit__header__opset">
                <el-button
                    type="text"
                    @click="showDictionary"
                >
                    查看字典
                </el-button>
                <el-button
                    type="primary"
                    @click="handleSaveSchema"
                    :disabled="disabled"
                >
                    保存
                </el-button>
            </div>
        </div>
        <el-dialog
            class="dicDialog"
            title="查看字典"
            :visible.sync="dialogFormVisible"
            :show-close="false"
        >
            <el-input
                class="searchInput"
                placeholder="请输入字典名称、URL"
                prefix-icon="el-icon-search"
                :clearable="true"
                style="width: 50%"
                v-model="queryCondition"
            />
            <el-table
                class="dicTable"
                :data="dicList"
                ref="singleTable"
                stripe
                border
                :default-sort="{prop: 'date', order: 'descending'}"
                :header-cell-style="{ background: '#F7F8F9', color: '#303133'}"
                highlight-current-row
                @current-change="handleCurrentTableItem"
                style="width: 100%"
            >
                <el-table-column
                    property="name"
                    min-width="40"
                    label="字典名称"
                >
                    <template slot-scope="scope">
                        <el-popover
                            placement="left"
                            trigger="click"
                        >
                            <el-table
                                class="dicOptionList"
                                :data="dicOptionList"
                            >
                                <el-table-column
                                    property="value"
                                    label="表单选项"
                                    align="center"
                                />
                            </el-table>
                            <el-button
                                slot="reference"
                                type="text"
                                size="mini"
                                @click="handleExactItem(scope.row)"
                            >
                                {{ scope.row.name }}
                            </el-button>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column
                    property="url"
                    label="URL"
                />
                <el-table-column
                    property="createErp"
                    min-width="50"
                    label="创建人"
                />
                <el-table-column
                    property="createTime"
                    sortable
                    label="创建时间"
                    min-width="50"
                />
            </el-table>
            <el-pagination
                class="j-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-size.sync="pagesize"
                :total="total"
                :current-page.sync="currentPage"
                layout="prev, pager, next"
            />
            <div class="footer-button">
                <el-button
                    @click="handleCancel"
                >
                    关 闭
                </el-button>
                <el-button
                    type="primary"
                    :disabled="isDiabled"
                    @click="handleConfirm"
                >
                    确 认
                </el-button>
            </div>
        </el-dialog>
        <dynamic-form-editor
            ref="dynamicFormEditorEl"
            :schema.sync="dynamicFormData.schema"
            :before-remove="handleRemove"
            :available-types="[
                'text',
                'integer',
                'boolean',
                'date',
                'enum',
                'select_user',
                'select_org',
            ]"
            class="extendform-setting-edit__body"
            @field-update="handleFieldUpdate"
            @field-chosen="handleFieldChosen"
        />
    </div>
</template>

<script>
import DynamicForm from '@/models/dynamicForm';
import DictionaryForm from '@/models/dictionaryForm';

// 混入自定义表单的操作，方便迁移
const mixinDynamicForm = {
    data() {
        return {
            dynamicFormData: {}, // 自定义表单的数据
        };
    },
    created() {
        this.loadDynamicForm();
    },
    methods: {
        async loadDynamicForm() {
            // this.code = 'pmp';
            // 因数据问题，暂时这样写.
            this.dynamicFormData = await DynamicForm.load({
                id: this.schemaId,
                code: this.appCode,
            });
            this.schemaName = this.dynamicFormData.name;
            this.appId = this.dynamicFormData.appId;
        },
        async saveDynamicForm(beforeSave) {
            const {
                id,
                name,
                appId,
                schema,
            } = this.dynamicFormData;
            const save = (paramId, paramName, paramAppId, paramSchema) => DynamicForm.saveForm({
                id: paramId,
                name: paramName,
                appId: paramAppId,
                schema: paramSchema,
            }).then(() => {
                this.$notify.success('保存成功');
            }).catch(err => this.$notify.error(`保存失败:${err}`));
            if (beforeSave) {
                return Promise.resolve(beforeSave)
                    .then(result => (result ? save(id, name, appId, schema) : null));
            }
            return save(id, name, appId, schema);
        },
        handleSaveSchema() {
            const done = () => this.saveDynamicForm();
            const { schema } = this.dynamicFormData;
            const errorField = schema.fields.find(o => o.hasError);
            if (errorField) {
                this.$message.error(`【${errorField.label}】字段的控件属性有不合法字段`);
            } else {
                done();
            }
        },
        handleRemove(field) {
            const done = () => this.$confirm('删除该字段，已生效的表单中的对应字段一并删除，这将可能影响数据的完整性，请您确认是否需要继续删除?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            });
            // 删除远端的field才需要提示文案
            if (field.remote) {
                return done();
            }
            return true;
        },
    },
};
export default {
    mixins: [mixinDynamicForm],
    data() {
        return {
            dicList: [],
            dialogFormVisible: false,
            total: 0,
            currentPage: 1,
            pagesize: 5,
            queryCondition: '',
            currentRow: null,
            isDiabled: true,
            schemaId: this.$route.query.schemaId,
            appId: 0,
            appName: this.$route.query.appName,
            appCode: this.$route.query.appCode,
            schemaName: '',
            dicOptionList: [],
            disabled: true,
        };
    },
    watch: {
        currentPage: {
            handler() {
                this.loadDicList();
            },
            immediate: true,
        },
        pagesize: {
            handler() {
                this.loadDicList();
            },
        },
        queryCondition: {
            handler() {
                this.loadDicList();
            },
        },
        'dynamicFormData.schema.fields': {
            handler(val) {
                if (Array.isArray(val) && val.length !== 0) {
                    this.disabled = false;
                } else {
                    this.disabled = true;
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        loadDicList() {
            if (this.queryCondition.length === 0) {
                DictionaryForm.loadFormDicList({
                    current: this.currentPage,
                    size: this.pagesize,
                    appId: this.appId,
                }).then((data) => {
                    this.dicList = data.records;
                    this.total = data.total;
                });
            } else {
                DictionaryForm.loadFormDicList({
                    current: this.currentPage,
                    size: this.pagesize,
                    appId: this.appId,
                    name: this.queryCondition,
                }).then((data) => {
                    this.dicList = data.records;
                    this.total = data.total;
                });
            }
        },
        getDicOptionList(url) {
            DictionaryForm.loadDicOptionList({ url }).then((data) => {
                this.dicOptionList = data;
            });
        },
        handleSizeChange(size) {
            this.pagesize = size;
        },
        handleCurrentChange(currentPage) {
            this.currentPage = currentPage;
        },
        handleCurrentTableItem(row) {
            this.currentRow = row;
            this.isDiabled = false;
        },
        setCurrent(row) {
            this.$refs.singleTable.setCurrentRow(row);
        },
        handleConfirm() {
            // 在这里预留出操作。
            if (this.currentField && this.currentField.remoteOptionSource) {
                Object.assign(this.currentField.remoteOptionSource, this.currentRow);
                this.currentField.options = this.dicOptionList;
            }
            this.setCurrent();
            this.closeDialog();
        },
        handleCancel() {
            this.setCurrent();
            this.closeDialog();
        },
        closeDialog() {
            this.dialogFormVisible = false;
            this.isDiabled = true;
        },
        handleExactItem(row) {
            this.getDicOptionList(row.url);
        },
        showDictionary() {
            this.loadDicList();
            this.dialogFormVisible = true;
        },

        handleFieldUpdate({ field, params = {} } = {}) {
            if (params.prop && params.prop === 'enableRemoteOption') {
                if (field.enableRemoteOption) {
                    this.showDictionary();
                } else {
                    this.currentField.options = [];
                }
            }
        },
        handleFieldChosen(field) {
            this.currentField = field;
        },
    },
};
</script>

<style lang="less">
.extendform-setting-edit {
    background:rgba(245,245,245,1);
    padding: 24px;
    &__header {
        background-color: #fff;
        padding: 16px 24px;
        position: relative;
        &:after {
            content: " ";
            display: block;
            height: 1px;
            width: 100%;
            background-color: #f1f1f1;
            position: absolute;
            bottom: 0;
            transform: translateX(-24px);
        }
        .label {
            font-weight: 500;
            color: #333;
            margin-right: 8px;
            font-size: 14px;
        }
        &__opset {
            position: absolute;
            right: 24px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    &__body {
        background: #fff;
        height: calc(~"100vh - 160px");
        padding: 0 24px;
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
    }
    .extendform-tips{
        color: #333;
        &__list{
            font-size: 12px;
            > li{
                margin: 8px 0;
                list-style: none;
                line-height: 24px;
            }
        }
    }
    .dicDialog {
        .searchInput {
            float: right;
        }
        .dicTable {
            margin-top: 40px;
            th, td, tr {
                height: 65px;
            }
            &::before {
                height: 0;
                width: 0;
            }
            &::after {
                width: 0;
                height: 0;
            }
        }
        .j-pagination {
            margin-top: 10px;
            text-align: center;
        }
        .footer-button {
            margin-top: 20px;
            text-align: center;
        }
    }
}
.dicOptionList {
    overflow-y: auto;
    &::before {
        width: 0;
        height: 0;
    }
}
.dynamic-form-editor__title{
    font-size: 14px;
}
</style>
