<template>
    <el-container
        class="option-index"
        :class="{'option-index--shrink': isCollapse}"
    >
        <el-header
            class="jacp-root-header"
            height="48px"
        >
            <span class="jacp-root-header__title">设置</span>
        </el-header>

        <el-container>
            <el-aside style="width: min-content; border-right:1px solid #EBEEF5">
                <jacp-left-menu
                    class="option-index__menu"
                    :is-collapse.sync="isCollapse"
                    :default-active="$route.name"
                    :data="validMenus"
                    @select="gotoPage"
                />
            </el-aside>
            <el-main class="option-index__main">
                <router-view />
            </el-main>
        </el-container>
    </el-container>
</template>

<script type="text/javascript">
import SecurityModel from '@/models/security';
import { MessageBox } from 'element-ui';
import { getPermissionUicomponents } from './modules';

const validMenu = [];
export default {
    data() {
        return {
            isCollapse: false,
            validMenus: [
                {
                    id: 'appSeting',
                    name: '平台设置',
                    icon: 'icon-module',
                    router: '',
                    children: [],
                },
            ],
        };
    },
    beforeRouteEnter(to, from, next) {
        SecurityModel.getSettingMenu().then((menus = []) => {
            if (menus) {
                menus.forEach((item) => {
                    validMenu.push(item.code);
                    // 暂时先支持两级, 貌似够了
                    if (item.children) {
                        item.children.forEach((citem) => {
                            validMenu.push(citem.code);
                        });
                    }
                });
                if (!validMenu.some(item => item === to.name)) {
                    if (validMenu.length > 0) {
                        next({ name: menus[0].code });
                    } else {
                        MessageBox({
                            title: '提示',
                            message: '您没有权限，如有诉求，请联系管理员',
                            type: 'error',
                        });
                        next(from.fullPath);
                    }
                } else {
                    next();
                }
            }
        });
    },
    beforeRouteUpdate(to, from, next) {
        if (validMenu.findIndex(m => to.name.startsWith(m)) > -1 || to.name === 'extendFormSettingEdit') {
            next();
        } else {
            MessageBox({
                title: '提示',
                message: '您没有权限，如有诉求，请联系管理员',
                type: 'error',
            });
            next(from.fullPath);
        }
    },
    mounted() {
        this.initSettingMenu();
        this.initJurisdiction();
    },
    computed: {
    },
    methods: {
        initSettingMenu() {
            this.validMenus = [
                {
                    id: 'appSeting',
                    name: '平台设置',
                    icon: 'icon-module',
                    router: '',
                    children: [],
                },
            ];
            const menuChild = this.validMenus[0].children;
            SecurityModel.getSettingMenu().then((menus = []) => {
                if (menus) {
                    menus.forEach((item) => {
                        const menu = this.menuFormatter(item);
                        menuChild.push(menu);
                        // 暂时先支持两级, 貌似够了
                        if (item.children) {
                            item.children.forEach((citem) => {
                                menu.children.push(this.menuFormatter(citem));
                            });
                        }
                    });
                }
            });
        },
        menuFormatter(item = {}) {
            return {
                id: item.code,
                name: item.name,
                icon: item.icon,
                router: item.url,
                children: [],
                appCode: item.appCode,
            };
        },
        async gotoPage(routerName) {
            // await this.initSettingMenu();
            await this.initJurisdiction();
            SecurityModel.getSettingMenu().then((menu) => {
                if (menu.length === 0) {
                    MessageBox({
                        title: '提示',
                        message: '您没有权限，如有诉求，请联系管理员',
                        type: 'error',
                    });
                    this.$router.push({path: '/'});
                } else {
                    this.$router.push({
                        name: routerName,
                    });
                }
            });
        },
        // 权限查询
        initJurisdiction() {
            getPermissionUicomponents();
        },
    },
};
</script>

<style lang="less">
.option-index {
    display: flex;
    &__menu {
        min-width: 200px;
        max-width: 200px;
        flex: 0;
    }
    &__main {
        flex: 1;
        overflow: hidden;
        // background-color: #fff;
    }
    &__menu,
    &__main {
        height: calc(~"100vh - 50px");
    }
    .option-index__main {
        padding: 0;
        overflow-y: auto;
        background-color: #fff;
    }
    &--shrink &__menu {
        min-width: 64px;
        max-width: 64px;
    }
}
</style>
