<template>
    <div class="demand-priority__main">
        <el-form
            :inline="true"
            class="demand-priority__main__org"
            label-position="left"
        >
            <el-form-item
                label="需求来源部门"
                prop="userOrgId"
            >
                <el-select
                    v-model="userOrgId"
                    placeholder="请选择"
                    filterable
                    :default-first-option="true"
                    @change="changeUserOrg"
                >
                    <el-option
                        v-for="item in orgList"
                        :key="item.orgId"
                        :value="item.orgId"
                        :label="item.orgName"
                    >
                        <span style="float: left">{{ item.orgName }}</span>
                        <span style="float: right; color: red;">{{ item.invalid ? '无效' : '' }}</span>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    size="small"
                    :disabled="disableCreate"
                    @click="createTemplate"
                >
                    添加模板
                </el-button>
            </el-form-item>
        </el-form>
        <el-tabs
            v-model="formName"
            type="border-card"
            v-show="showTabs"
        >
            <el-tab-pane
                label="模板配置"
                name="demand"
            >
                <submit-demand-template
                    v-if="userOrgId"
                    :user-org-id="userOrgId"
                    :factor-array="factorList"
                    :disable-edit="disabledEdit"
                />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import DemandFlow from '@/models/demandFlow';
import submitDemandTemplate from './components/submitDemandTemplate';
import PriorityRate from '@/models/priorityRate';


export default {
    components: {
        submitDemandTemplate,
    },
    data() {
        return {
            formName: 'demand',
            orgList: [],
            userOrgId: '',
            userOrgName: '',
            disableCreate: false,
            showTabs: false,
            factorList: [],
            disabledEdit: false,
        };
    },
    mounted() {
        // 获取默认模板
        this.getDefaultTemplate();
    },
    methods: {
        getDefaultTemplate() {
            // 获取部门列表
            DemandFlow.getOrgList().then((data) => {
                this.orgList = data;
                if (this.orgList.length > 0) {
                    this.userOrgId = this.orgList[0].orgId;
                    this.userOrgName = this.orgList[0].orgName;
                }
                // 获取当前部门的模板
                this.getTemplateByOrgCode(this.userOrgId).then((result) => {
                    if (result) {
                        this.disableCreate = true;
                        this.showTabs = true;
                        if (result.ifOpen === 1) {
                            this.disabledEdit = true;
                        }
                    } else {
                        this.disableCreate = false;
                        this.showTabs = false;
                    }
                });
            });
        },
        changeUserOrg(userOrgId) {
            this.orgList.forEach((item) => {
                if (item.orgId === userOrgId) {
                    this.userOrgName = item.orgName;
                    this.userOrgId = item.orgId;
                    // 获取当前部门的模板
                    this.getTemplateByOrgCode(this.userOrgId).then((result) => {
                        if (result) {
                            this.disableCreate = true;
                            this.showTabs = true;
                            if (result.ifOpen === 1) {
                                this.disabledEdit = true;
                            }
                        } else {
                            this.disableCreate = false;
                            this.showTabs = false;
                        }
                    });
                }
            });
        },
        // 查询当前部门下的模板
        getTemplateByOrgCode(userOrgId) {
            const params = {
                fullOrgPath: userOrgId,
            };
            return PriorityRate.getTemplateByOrgCode(params);
        },
        // 创建模板
        createTemplate() {
            const param = {};
            param.departmentCode = this.userOrgId;
            param.departmentName = this.userOrgName;
            return PriorityRate.createTemplate(param).then((data) => {
                if (data) {
                    this.disableCreate = true;
                    this.showTabs = true;
                    this.factorList = data.factors;
                    if (data.ifOpen === 1) {
                        this.disabledEdit = true;
                    }
                } else {
                    this.disableCreate = false;
                    this.showTabs = false;
                }
            });
        },
    },
};
</script>

<style lang="less">
    .demand-priority__main{
        padding: 20px;
        overflow-y: auto;
        &__org{
            & .el-input{
                width: 400px;
            }
        }
    }
</style>
