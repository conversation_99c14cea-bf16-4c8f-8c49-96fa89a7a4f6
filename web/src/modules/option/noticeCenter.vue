<template>
    <el-tabs
        v-if="editPower['message:notify:view']===0"
        v-model="activeName"
        type="border-card"
        style="height: 100%;border:none;"
    >
        <el-tab-pane
            label="渠道设置"
            name="setting"
        >
            <notice-setting :edit-power="editPower" />
        </el-tab-pane>
        <el-tab-pane
            label="推送记录"
            name="record"
        >
            <notice-push />
        </el-tab-pane>
    </el-tabs>
</template>
<script type="text/javascript">
import { MessageBox } from 'element-ui';
import noticeSetting from './noticeSetting.vue';
import noticePush from './noticePush.vue';

import { getPermissionUicomponents } from '@/modules/option/modules';

export default {
    components: {
        noticeSetting,
        noticePush,
    },
    data() {
        return {
            activeName: 'setting',
            editPower: {}, // 页面按钮权限 0:展示，1:置灰，2:隐藏
        };
    },
    mounted() {
        this.getPermission();
    },
    methods: {
        async getPermission() {
            await getPermissionUicomponents().then((res) => {
                this.editPower = res;
                if (res['message:notify:view'] !== 0) {
                    MessageBox({
                        title: '提示',
                        message: '您没有权限，如有诉求，请联系管理员',
                        type: 'error',
                    }).then(() => {
                        this.$router.push({ name: 'option' });
                    }).catch(() => {
                        this.$router.push({ name: 'option' });
                    });
                }
            });
        },
    },
};
</script>
<style lang="less">

</style>
