<template>
    <div class="taskJournal">
        <!-- 第一行，搜索区 -->
        <el-row class="header">
            <el-col :span="22">
                <el-form
                    :inline="true"
                    :model="instanceQueryContent"
                    class="el-form--inline"
                    ref="journalForm"
                >
                    <el-form-item
                        label="应用"
                        prop="appId"
                    >
                        <el-select
                            v-model="instanceQueryContent.appId"
                            placeholder="请选择应用"
                            clearable
                        >
                            <el-option
                                v-for="item in AppOptions"
                                :key="item.id"
                                :label="item.applyName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('jacp.message.jobId')">
                        <el-input
                            clearable
                            v-model="instanceQueryContent.jobId"
                            maxlength="18"
                            onkeyup="value=value.replace(/[^\d]/g,'')"
                            :placeholder="'请输入'+$t('jacp.message.jobId')"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('jacp.message.instanceId')">
                        <el-input
                            clearable
                            v-model="instanceQueryContent.instanceId"
                            maxlength="18"
                            onkeyup="value=value.replace(/[^\d]/g,'')"
                            :placeholder="'请输入'+$t('jacp.message.instanceId')"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('jacp.message.status')">
                        <el-select
                            clearable
                            v-model="instanceQueryContent.status"
                            :placeholder="$t('jacp.message.status')"
                        >
                            <el-option
                                v-for="item in instanceStatusOptions"
                                :key="item.key"
                                :label="item.label"
                                :value="item.key"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="类型">
                        <el-select
                            clearable
                            v-model="instanceQueryContent.jobType"
                            placeholder="请选择类型"
                        >
                            <el-option
                                v-for="item in jobTypeOptions"
                                :key="item.id"
                                :label="item.typeName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item :label="$t('jacp.message.keyword')">
                        <el-input
                            clearable
                            v-model="instanceQueryContent.jobName"
                            :placeholder="'请输入任务名称的'+$t('jacp.message.keyword')"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="getList(0)"
                            v-if="jurisdicList['archTaskCenter:task:instance:search'] === 0"
                        >
                            {{ $t('jacp.message.query') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:instance:search'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="primary"
                                style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;">
                                {{ $t('jacp.message.query') }}
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="cancel"
                            @click="resetForm('journalForm',0)"
                        >
                            {{ $t('jacp.message.reset') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :span="2">
                <div style="float:right;padding-right:10px">
                    <el-button
                        type="primary"
                        @click="getList"
                        v-if="jurisdicList['archTaskCenter:task:instance:refresh'] === 0"
                    >
                        {{ $t('jacp.message.refresh') }}
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archTaskCenter:task:instance:refresh'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;">
                            {{ $t('jacp.message.refresh') }}
                        </el-button>
                    </el-tooltip>
                </div>
            </el-col>
        </el-row>
        <div class="container">
            <el-table
                :data="instancePageResult.data"
                style="width: 100%"
                :row-class-name="instanceTableRowClassName"
            >
                <el-table-column
                    :show-overflow-tooltip="true"
                    prop="jobId"
                    label="任务ID"
                    width="70px"
                />
                <el-table-column
                    show-overflow-tooltip
                    prop="jobName"
                    label="任务名称"
                />
                <el-table-column
                    prop="jobType"
                    label="任务类型"
                    width="80px"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.jobType==1">修复数据</span>
                        <span v-else-if="scope.row.jobType==2">业务功能</span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="applyName"
                    label="所属应用"
                    width="80px"
                />
                <el-table-column
                    prop="instanceId"
                    label="任务实例ID"
                />
                <el-table-column
                    prop="status"
                    label="状态"
                    width="60px"
                >
                    <template slot-scope="scope">{{ fetchStatus(scope.row.status) }}</template>
                </el-table-column>
                <el-table-column
                    prop="actualTriggerTime"
                    label="触发时间"
                />
                <el-table-column
                    prop="finishedTime"
                    label="结束时间"
                />
                <el-table-column
                    label="操作"
                    width="300"
                    header-align="center"
                >
                    <template slot-scope="scope">
                        <el-button
                            @click="onClickShowDetail(scope.row)"
                            type="primary"
                            size="small"
                            v-if="jurisdicList['archTaskCenter:task:instance:detail'] === 0"
                        >
                            详情
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:instance:detail'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="primary"
                                size="small"
                                style="color: #fff;cursor: not-allowed;
                                    background-color: #93caf8;
                                    border-color: #93caf8;"
                            >详情</el-button>
                        </el-tooltip>
                        <el-button
                            type="primary"
                            size="small"
                            v-if="jurisdicList['archTaskCenter:task:instance:onlineLog'] === 0"
                            @click="onClickShowLog(scope.row)"
                        >
                            日志
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:instance:onlineLog'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="primary"
                                size="small"
                                style="color: #fff;cursor: not-allowed;
                                    background-color: #93caf8;
                                    border-color: #93caf8;"
                            >日志</el-button>
                        </el-tooltip>
                        <el-button
                            type="primary"
                            size="small"
                            @click="onClickRetryJob(scope.row)"
                            v-if="jurisdicList['archTaskCenter:task:instance:retry'] === 0"
                        >
                            重试
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:instance:retry'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="primary"
                                size="small"
                                style="color: #fff;cursor: not-allowed;
                                    background-color: #93caf8;
                                    border-color: #93caf8;"
                            >重试</el-button>
                        </el-tooltip>
                        <el-button
                            type="danger"
                            size="small"
                            @click="onClickStop(scope.row)"
                            v-if="jurisdicList['archTaskCenter:task:instance:stop'] === 0"
                        >
                            停止
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:instance:stop'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="danger"
                                size="small"
                                style="cursor: not-allowed;background-image: none;
                                color: #fff;
                                background-color: #faaaa2;
                                border-color: #faaaa2;"
                            >停止</el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                layout="prev, pager, next, total, sizes, jumper"
                @size-change="handleSizeChange"
                @current-change="onClickChangeInstancePage"
                :total="this.instancePageResult.totalItems"
                :page-size="this.instancePageResult.pageSize"
                :current-page.sync="currentPage"
            />
        </div>

        <!--  任务实例详情弹出框 -->
        <el-dialog
            :visible.sync="instanceDetailVisible"
            v-if="instanceDetailVisible"
            width="80%"
        >
            <div class="power-instance-detail-log">
                <InstanceDetail
                    :instance-id="currentInstanceId"
                    :result-all="true"
                />
            </div>
        </el-dialog>

        <!-- 任务运行日志弹出框 -->
        <el-dialog
            :visible.sync="instanceLogVisible"
            width="80%"
        >
            <el-row>
                <el-col
                    :span="24"
                    class="power-instance-log-download"
                    style="margin-bottom:20px"
                >
                    <el-button
                        type="primary"
                        size="mini"
                        @click="onclickDownloadLog()"
                        icon="el-icon-download"
                    >
                        {{ $t('jacp.message.download') }}
                    </el-button>
                </el-col>
            </el-row>
            <div class="power-instance-log-dialog">
                <el-row>
                    <el-col :span="24">
                        <h4 style="white-space: pre-line;">{{ this.paginableInstanceLog.data }}</h4>
                    </el-col>
                </el-row>
            </div>
            <el-row>
                <el-col :span="24">
                    <el-pagination
                        :page-count="paginableInstanceLog.totalPages"
                        @current-change="onClickChangeLogPage"
                        layout="prev, pager, next"
                    />
                </el-col>
            </el-row>
        </el-dialog>
    </div>
</template>

<script>
import InstanceDetail from './common/InstanceDetail';
import { Task } from '@/models/taskManage';
import { mapState } from 'vuex';

export default {
    name: 'TaskManage',
    components: {
        InstanceDetail,
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    data() {
        return {
            currentPage: 1,
            // 实例查询对象
            instanceQueryContent: {
                appId: '', // 应用
                index: 0,
                pageSize: 10,
                instanceId: undefined, // 任务实例 ID
                status: '', // 状态
                jobId: undefined, // 任务ID
                type: 'NORMAL',
                jobType: '', // 类型
                jobName: '', // 关键字
            },
            jobTypeOptions: [
                { id: '', typeName: '全部' },
                { id: 1, typeName: '修复数据' },
                { id: 2, typeName: '业务功能' },
            ],
            // 实例查询结果
            instancePageResult: {
                pageSize: 10,
                totalItems: 0,
                data: [],
            },
            AppOptions: [], // 应用选择
            // 任务实例状态选择
            instanceStatusOptions: [
                { key: '', label: this.$t('jacp.message.all') },
                { key: 'WAITING_DISPATCH', label: this.$t('jacp.message.waitingDispatch') },
                {
                    key: 'WAITING_WORKER_RECEIVE',
                    label: this.$t('jacp.message.waitingWorkerReceive'),
                },
                { key: 'RUNNING', label: this.$t('jacp.message.running') },
                { key: 'FAILED', label: this.$t('jacp.message.failed') },
                { key: 'SUCCEED', label: this.$t('jacp.message.success') },
                { key: 'CANCELED', label: this.$t('jacp.message.canceled') },
                { key: 'STOPPED', label: this.$t('jacp.message.stopped') },
            ],
            // 详细信息弹出框是否可见
            instanceDetailVisible: false,
            // 日志弹出框是否可见
            instanceLogVisible: false,
            currentInstanceId: undefined,
            // 日志查询对象
            logQueryContent: {
                instanceId: undefined,
                index: 0,
            },
            // 日志对象
            paginableInstanceLog: {
                index: 0,
                totalPages: 0,
                data: '',
            },
            logAppId: '',
        };
    },
    mounted() {
        this.getList();
        Task.appInfoList().then((res) => {
            this.AppOptions = res.data.data;
        });
    },
    methods: {
        // 查询任务实例信息
        getList(i) {
            if (i === 0) {
                this.instanceQueryContent.index = 0;
                this.currentPage = 1;
            }
            Task.instanceList(this.instanceQueryContent).then((res) => {
                this.instancePageResult = res.data.data;
            });
        },
        // 点击重置按钮
        resetForm(formName, i) {
            if (i === 0) {
                this.instanceQueryContent.index = 0;
                this.currentPage = 1;
            }
            this.$refs[formName].resetFields();
            this.instanceQueryContent.instanceId = undefined; // 任务实例 ID
            this.instanceQueryContent.status = ''; // 任务实例 ID
            this.instanceQueryContent.jobId = undefined; // 任务实例 ID
            this.instanceQueryContent.jobType = ''; // 类型
            this.instanceQueryContent.jobName = ''; // 关键字
            this.getList();
        },
        // 分页
        onClickChangeInstancePage(index) {
            this.instanceQueryContent.index = index - 1;
            this.currentPage = index;
            this.getList();
        },
        handleSizeChange(size) {
            this.instanceQueryContent.pageSize = size;
            this.getList();
        },
        // 获取状态
        fetchStatus(s) {
            return this.common.translateInstanceStatus(s);
        },
        // 点击查询详情
        onClickShowDetail(data) {
            this.instanceDetailVisible = true;
            this.currentInstanceId = data.instanceId;
        },
        // 点击重跑
        onClickRetryJob(data) {
            const params = {
                instanceId: data.instanceId,
                appId: data.appId,
            };
            Task.instanceRetry(params).then((res) => {
                if (!res.data.success) {
                    this.$message.error(res.data.message);
                    return;
                }
                this.$message.success(`重试${this.$t('jacp.message.success')}`);
                this.getList();
            });
        },
        // 点击停止实例
        onClickStop(data) {
            const params = {
                instanceId: data.instanceId,
                appId: data.appId,
            };
            this.$confirm(`确定要停止 ${data.jobName}  任务吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                Task.instanceStop(params).then((res) => {
                    if (res.data.success) {
                        this.$message.success(`已停止  ${data.jobName}  任务`);
                        // 重新加载列表
                        this.getList();
                    } else {
                        this.$message({
                            type: 'error',
                            message: '该任务已完成，无法停止',
                        });
                    }
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消停止',
                });
            });
        },
        // eslint-disable-next-line consistent-return
        instanceTableRowClassName({ row }) {
            switch (row.status) {
            // 失败
            case 4: return 'error-row';
                // 成功
            case 5: return 'success-row';
            case 9:
            case 10: return 'warning-row';
            default:
            }
        },
        // 查看日志
        queryLog(appId) {
            const params = {
                instanceId: this.logQueryContent.instanceId,
                index: this.logQueryContent.index,
                appId,
            };
            Task.queryLog(params).then((res) => {
                this.paginableInstanceLog = res.data.data;
                this.logAppId = appId;
                this.instanceLogVisible = true;
            });
        },
        // 查看在线日志
        onClickShowLog(data) {
            this.logQueryContent.instanceId = data.instanceId;
            this.logQueryContent.index = 0;
            this.queryLog(data.appId);
        },
        // 查看其它页的在线日志
        onClickChangeLogPage(index) {
            this.logQueryContent.index = index - 1;
            this.queryLog(this.logAppId);
        },
        // 下载日志
        onclickDownloadLog() {
            const params = {
                instanceId: this.logQueryContent.instanceId,
                appId: this.logAppId,
            };
            Task.downloadLog(params).then((res) => {
                window.open(res.data.data);
            });
        },
    },
};
</script>

<style lang="less" scoped>
.taskJournal {
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  .container {
    .el-table {
        .el-button {
            font-weight: 500;
        }
        // .el-button--success {
        //     background-color: #67C23A;
        //     border-color: #67C23A;
        // }
        // .el-button--warning {
        //     background-color: #E6A23C;
        //     border-color: #E6A23C;
        // }
        // .el-button--danger {
        //     background-color: #F56C6C;
        //     border-color: #F56C6C;
        // }
    }
  }
  .footer {
    background: #fff;
    height: 64px;
    position: relative;
    .j-pagination {
        position: absolute;
        top: 50%;
        right: 16px;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        }
  }
  .title {
  display: inline-block;
  margin: 5px 0;
  font-size: 16px;
  font-weight: bold;
}
.power-instance-log-download {
  display: flex;
  justify-content: flex-end;
}
.power-instance-log-dialog {
  max-height: 400px;
  overflow-y: scroll;
}
.power-instance-detail-log {
  max-height: 500px;
  overflow-y: scroll;
}
}
</style>
