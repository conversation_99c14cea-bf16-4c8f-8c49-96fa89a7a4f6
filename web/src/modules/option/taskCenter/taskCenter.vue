<template>
    <el-tabs
        v-model="activeName"
        type="border-card"
        style="height: 100%;border:none;"
        @tab-click="handleClick"
    >
        <el-tab-pane
            label="任务管理"
            name="task"
            v-if="jurisdicList['archTaskCenter:task:manage:view'] === 0"
        >
            <task-manage @skipLog="skipLog" />
        </el-tab-pane>
        <el-tab-pane
            label="调度日志"
            name="journal"
            v-if="jurisdicList['archTaskCenter:task:instance:view'] === 0"
        >
            <task-journal ref="taskJournal" />
        </el-tab-pane>
        <el-tab-pane
            label="报表"
            name="report"
            v-if="jurisdicList['archTaskCenter:system:homepage:view'] === 0"
        >
            <task-report />
        </el-tab-pane>
    </el-tabs>
</template>
<script type="text/javascript">
import TaskManage from './taskManage';
import TaskJournal from './taskJournal';
import TaskReport from './taskReport';
import { mapState } from 'vuex';

export default {
    components: {
        TaskManage,
        TaskJournal,
        TaskReport,
    },
    data() {
        return {
            activeName: 'task',
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            // tips: state => state.option.tips,
        }),
    },
    methods: {
        // 切换到调度日志时刷新页面
        handleClick(e) {
            if (e.index === '1') {
                this.$refs.taskJournal.getList();
            }
        },
        skipLog() {
            this.activeName = 'journal';
            this.$refs.taskJournal.getList();
        },
    },
};
</script>
<style lang="less">
.org-setting {
    height: calc(~'100% - 24px');
    margin: 24px 24px 0 16px;
}
.el-tabs--border-card>.el-tabs__content {
    background-color: #fff;
}
</style>
