<template>
    <div>
        <el-dialog
            class="add-task"
            title="新增任务"
            :close-on-click-modal="false"
            :visible.sync="formVisible"
            :show-close="false"
            width="60%"
        >
            <el-form
                :model="modifiedJobForm"
                :label-width="formLabelWidth"
            >
                <el-form-item :label="$t('jacp.message.jobSelect')">
                    <el-select
                        v-model="modifiedJobForm.appName"
                        :placeholder="$t('jacp.message.jobSelect')"
                        style="width:100%"
                    >
                        <el-option
                            v-for="item in jobSelectList"
                            :key="item.id"
                            :label="item.appName"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('jacp.message.jobName')">
                    <el-input v-model="modifiedJobForm.jobName" />
                </el-form-item>
                <el-form-item :label="$t('jacp.message.jobDescription')">
                    <el-input v-model="modifiedJobForm.jobDescription" />
                </el-form-item>
                <el-form-item :label="$t('jacp.message.jobParams')">
                    <el-input
                        v-model="modifiedJobForm.jobParams"
                        autocomplete="off"
                    />
                </el-form-item>
                <el-form-item :label="$t('jacp.message.scheduleInfo')">
                    <el-row>
                        <el-col :span="8">
                            <el-select
                                v-model="modifiedJobForm.timeExpressionType"
                                :placeholder="$t('jacp.message.timeExpressionType')"
                            >
                                <el-option
                                    v-for="item in timeExpressionTypeOptions"
                                    :key="item.key"
                                    :label="item.label"
                                    :value="item.key"
                                />
                            </el-select>
                        </el-col>
                        <el-col :span="12">
                            <el-input
                                v-model="modifiedJobForm.timeExpression"
                                :placeholder="$t('jacp.message.timeExpressionPlaceHolder')"
                            />
                        </el-col>
                        <el-col :span="4">
                            <el-button
                                type="text"
                                @click="onClickValidateTimeExpression"
                                style="padding-left: 10px"
                            >
                                {{ $t('jacp.message.validateTimeExpression') }}
                            </el-button>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item :label="$t('jacp.message.executeConfig')">
                    <el-row>
                        <el-col :span="5">
                            <el-select
                                v-model="modifiedJobForm.executeType"
                                :placeholder="$t('jacp.message.executeType')"
                            >
                                <el-option
                                    v-for="item in executeTypeOptions"
                                    :key="item.key"
                                    :label="item.label"
                                    :value="item.key"
                                />
                            </el-select>
                        </el-col>

                        <el-col :span="6">
                            <el-select
                                v-model="modifiedJobForm.processorType"
                                :placeholder="$t('jacp.message.processorType')"
                            >
                                <el-option
                                    v-for="item in processorTypeOptions"
                                    :key="item.key"
                                    :label="item.label"
                                    :value="item.key"
                                />
                            </el-select>
                        </el-col>
                        <el-col :span="13">
                            <el-input
                                v-model="modifiedJobForm.processorInfo"
                                :placeholder="verifyPlaceholder(modifiedJobForm.processorType)"
                            />
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item :label="$t('jacp.message.runtimeConfig')">
                    <el-row>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.maxInstanceNum')"
                                v-model="modifiedJobForm.maxInstanceNum"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.maxInstanceNum') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.threadConcurrency')"
                                v-model="modifiedJobForm.concurrency"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.threadConcurrency') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.timeout')"
                                v-model="modifiedJobForm.instanceTimeLimit"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.timeout') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item :label="$t('jacp.message.retryConfig')">
                    <el-row>
                        <el-col :span="12">
                            <el-input
                                :placeholder="$t('jacp.message.taskRetryTimes')"
                                v-model="modifiedJobForm.instanceRetryNum"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.taskRetryTimes') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="12">
                            <el-input
                                :placeholder="$t('jacp.message.subTaskRetryTimes')"
                                v-model="modifiedJobForm.taskRetryNum"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.subTaskRetryTimes') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item :label="$t('jacp.message.workerConfig')">
                    <el-row>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.minCPU')"
                                v-model="modifiedJobForm.minCpuCores"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.minCPU') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.minMemory')"
                                v-model="modifiedJobForm.minMemorySpace"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.minMemory') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.minDisk')"
                                v-model="modifiedJobForm.minDiskSpace"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.minDisk') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item :label="$t('jacp.message.clusterConfig')">
                    <el-row>
                        <el-col :span="16">
                            <el-input
                                :placeholder="$t('jacp.message.designatedWorkerAddressPLH')"
                                v-model="modifiedJobForm.designatedWorkers"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.designatedWorkerAddress') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.maxWorkerNumPLH')"
                                v-model="modifiedJobForm.maxWorkerCount"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.maxWorkerNum') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item :label="$t('jacp.message.alarmConfig')">
                    <el-select
                        v-model="modifiedJobForm.notifyUserIds"
                        multiple
                        filterable
                        :placeholder="$t('jacp.message.alarmSelectorPLH')"
                    >
                        <el-option
                            v-for="user in userList"
                            :key="user.id"
                            :label="user.username"
                            :value="user.id"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            <div
                slot="footer"
                class="dialog-footer"
            >
                <el-button
                    type="primary"
                    @click="saveTask('save')"
                >
                    {{ $t('jacp.button.save') }}
                </el-button>
                <el-button
                    @click="saveTask('cancel')"
                >
                    {{ $t('jacp.button.cancel') }}
                </el-button>
            </div>
        </el-dialog>
        <el-dialog
            :close-on-click-modal="false"
            :visible.sync="timeExpressionValidatorVisible"
            v-if="timeExpressionValidatorVisible"
        >
            <TimeExpressionValidator
                :time-expression="modifiedJobForm.timeExpression"
                :time-expression-type="modifiedJobForm.timeExpressionType"
            />
        </el-dialog>
    </div>
</template>
<script>
import TimeExpressionValidator from './TimeExpressionValidator';
import { Task } from '@/models/taskManage';

export default {
    name: 'AddTaskDialog',
    components: { TimeExpressionValidator },
    props: {
        addFormVisible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            timeExpressionValidatorVisible: false,
            formVisible: false,
            jobSelectList: [],
            timeExpressionTypeOptions: [{ key: 'API', label: 'API' }, { key: 'CRON', label: 'CRON' }, { key: 'FIXED_RATE', label: this.$t('jacp.message.fixRate') }, { key: 'FIXED_DELAY', label: this.$t('jacp.message.fixDelay') }, { key: 'WORKFLOW', label: this.$t('jacp.message.workflow') }],
            executeTypeOptions: [{ key: 'STANDALONE', label: this.$t('jacp.message.standalone') }, { key: 'BROADCAST', label: this.$t('jacp.message.broadcast') }, { key: 'MAP', label: this.$t('jacp.message.map') }, { key: 'MAP_REDUCE', label: this.$t('jacp.message.mapReduce') }],
            processorTypeOptions: [{ key: 'BUILT_IN', label: this.$t('jacp.message.builtIn') }, { key: 'EXTERNAL', label: this.$t('jacp.message.external') }],
            userList: [],
            modifiedJobForm: {
                appName: '',
                id: undefined,
                jobName: '',
                jobDescription: '',
                appId: '',
                jobParams: '',
                timeExpressionType: '',
                timeExpression: '',
                executeType: '',
                processorType: '',
                processorInfo: '',
                maxInstanceNum: 0,
                concurrency: 5,
                instanceTimeLimit: 0,
                instanceRetryNum: 0,
                taskRetryNum: 1,

                minCpuCores: 0,
                minMemorySpace: 0,
                minDiskSpace: 0,

                enable: true,
                designatedWorkers: '',
                maxWorkerCount: 0,
                notifyUserIds: [],

            },
            formLabelWidth: '120px',
        };
    },
    watch: {
        addFormVisible(val) {
            this.formVisible = val;
        },
    },
    methods: {
        saveTask(type) {
            this.$emit('saveTask', type, this.modifiedJobForm);
        },
        onClickValidateTimeExpression() {
            this.timeExpressionValidatorVisible = true;
        },
        verifyPlaceholder(processorType) {
            let res;
            switch (processorType) {
            case 'BUILT_IN': res = this.$t('message.javaProcessorInfoPLH'); break;
            case 'EXTERNAL': res = this.$t('message.containerProcessorInfoPLH'); break;
            case 'SHELL': res = this.$t('message.shellProcessorInfoPLH'); break;
            case 'PYTHON': res = this.$t('message.pythonProcessorInfoPLH'); break;
            default: res = '';
            }
            return res;
        },
    },
    mounted() {
        // 加载用户信息
        Task.userList().then((res) => {
            this.userList = res.data.data;
        });
        Task.appInfoList().then((res) => {
            this.jobSelectList = res.data.data;
        });
        // that.axios.get('/user/list').then(res => that.userList = res);
        // 加载任务信息
        // this.listJobInfos();
    },
};
</script>
<style lang='less' scoped>
    .add-task {
        /deep/ .el-form-item__label {
            text-align: right;
            vertical-align: middle;
            float: left;
            font-size: 14px;
            color: #606266;
            line-height: 40px;
            padding: 0 12px 0 0;
            box-sizing: border-box;
        }
        .el-button--text {
        border-color: transparent;
        color: teal;
        background: transparent;
        padding-left: 0;
        padding-right: 0;
        font-weight: 500;
        }
        .fl {
            display: flex;
        }
        .fl1 {
            flex: 1;
        }
        .fl2 {
            flex: 2;
        }
        .fl3 {
            flex: 3;
        }
    }
</style>
