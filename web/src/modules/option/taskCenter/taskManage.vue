<template>
    <div class="taskManage">
        <!--第一行，条件搜索栏-->
        <el-row
            :gutter="20"
        >
            <!-- 左侧搜索栏-->
            <el-col :span="20">
                <el-form
                    :inline="true"
                    :model="jobQueryContent"
                    class="el-form--inline"
                >
                    <el-form-item :label="$t('jacp.message.application')">
                        <el-select
                            clearable
                            v-model="jobQueryContent.appId"
                            :placeholder="$t('jacp.message.jobSelect')"
                        >
                            <el-option
                                v-for="item in AppOptions"
                                :key="item.id"
                                :label="item.applyName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item><el-form-item label="类型">
                        <el-select
                            clearable
                            v-model="jobQueryContent.jobType"
                            placeholder="请选择类型"
                        >
                            <el-option
                                v-for="item in jobTypeOptions"
                                :key="item.id"
                                :label="item.typeName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('jacp.message.jobId')">
                        <el-input
                            clearable
                            v-model="jobQueryContent.jobId"
                            maxlength="18"
                            onkeyup="value=value.replace(/[^\d]/g,'')"
                            :placeholder="'请输入'+$t('jacp.message.jobId')"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('jacp.message.keyword')">
                        <el-input
                            clearable
                            v-model="jobQueryContent.keyword"
                            :placeholder="'请输入任务名称的'+$t('jacp.message.keyword')"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="listJobInfos(0)"
                            v-if="jurisdicList['archTaskCenter:task:manage:search'] === 0"
                        >
                            {{ $t('jacp.message.query') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:manage:search'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                type="primary"
                                style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                            >
                                {{ $t('jacp.message.query') }}
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="cancel"
                            @click="onClickReset(0)"
                        >
                            {{ $t('jacp.message.reset') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-col>

            <!-- 右侧新增任务按钮 -->
            <el-col :span="4">
                <div style="float:right;padding-right:10px">
                    <el-button
                        type="primary"
                        @click="onClickNewJob"
                        v-if="jurisdicList['archTaskCenter:task:manage:add'] === 0"
                    >
                        {{ $t('jacp.message.newJob') }}
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archTaskCenter:task:manage:add'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top"
                    >
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            {{ $t('jacp.message.newJob') }}
                        </el-button>
                    </el-tooltip>
                </div>
            </el-col>
        </el-row>

        <div class="container">
            <el-table
                :data="jobInfoPageResult.data"
                style="width: 100%"
            >
                <el-table-column
                    prop="id"
                    label="任务ID"
                    width="60"
                />
                <el-table-column
                    prop="jobName"
                    :label="$t('jacp.message.jobName')"
                >
                    <template slot-scope="scope">
                        <span
                            style="color:#2695f1;cursor:pointer"
                            @click="openDesc(scope.row)"
                        >{{ scope.row.jobName }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    prop="jobDescription"
                    show-overflow-tooltip
                    label="任务描述"
                    width="130px"
                /> -->
                <el-table-column
                    prop="jobType"
                    label="任务类型"
                    width="120px"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.jobType==1">修复数据</span>
                        <span v-else-if="scope.row.jobType==2">业务功能</span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="applyName"
                    label="所属应用"
                    width="120px"
                />
                <el-table-column
                    :label="$t('jacp.message.scheduleInfo')+'描述'"
                    prop="timeExpressionDescription"
                    width="100px"
                />
                <el-table-column label="Cron表达式">
                    <template slot-scope="scope">
                        {{ scope.row.timeExpressionType }}  {{ scope.row.timeExpression }}
                    </template>
                </el-table-column>
                <!-- <el-table-column :label="$t('jacp.message.executeType')">
                    <template slot-scope="scope">
                        {{ translateExecuteType(scope.row.executeType) }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('jacp.message.processorType')">
                    <template slot-scope="scope">
                        {{ translateProcessorType(scope.row.processorType) }}
                    </template>
                </el-table-column> -->
                <el-table-column
                    :label="$t('jacp.message.operation')"
                    width="300"
                    header-align="center"
                >
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            type="text"
                            @click="onClickRun(scope.row)"
                            v-if="jurisdicList['archTaskCenter:task:manage:run'] === 0"
                        >
                            {{ $t('jacp.message.run') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:manage:run'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                type="text"
                                size="mini"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >
                                {{ $t('jacp.message.run') }}
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="text"
                            size="small"
                            @click="onClickShowLog"
                            v-if="jurisdicList['archTaskCenter:task:manage:lookLog'] === 0"
                        >
                            {{ $t('jacp.message.checkLog') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:manage:lookLog'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                type="text"
                                size="small"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >
                                {{ $t('jacp.message.checkLog') }}
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="text"
                            size="small"
                            v-if="jurisdicList['archTaskCenter:task:manage:copy'] === 0"
                            @click="onClickCopyJob(scope.row)"
                        >
                            {{ $t('jacp.message.copy') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:manage:copy'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                type="text"
                                size="small"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >
                                {{ $t('jacp.message.copy') }}
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="text"
                            size="small"
                            v-if="jurisdicList['archTaskCenter:task:manage:update'] === 0"
                            @click="onClickModify(scope.row)"
                        >
                            {{ $t('jacp.button.edit') }}
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:manage:update'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                type="text"
                                size="small"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >
                                {{ $t('jacp.button.edit') }}
                            </el-button>
                        </el-tooltip>
                        <el-button
                            type="text"
                            size="small"
                            @click="changeJobStatus(scope.row)"
                            v-if="jurisdicList['archTaskCenter:task:manage:able'] === 0&&scope.row.jobType != 2"
                        >
                            {{ scope.row.enable?'停用':'启用' }}
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:manage:able'] === 1&&scope.row.jobType != 2"
                            effect="dark"
                            :content="tips"
                            placement="top"
                        >
                            <el-button
                                type="text"
                                size="small"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >
                                {{ scope.row.enable?'停用':'启用' }}
                            </el-button>
                        </el-tooltip>
                        <!-- <el-button
                            type="text"
                            size="small"
                            v-if="jurisdicList['archTaskCenter:task:manage:del'] === 0"
                            @click="onClickDeleteJob(scope.row)"
                        >
                            删除
                        </el-button>
                        <el-tooltip
                            v-else-if="jurisdicList['archTaskCenter:task:manage:del'] === 1"
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="text"
                                size="small"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >删除</el-button>
                        </el-tooltip> -->
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 第三行，分页插件 -->
        <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="handleSizeChange"
                @current-change="onClickChangePage"
                :page-size="this.jobInfoPageResult.pageSize"
                :total="this.jobInfoPageResult.totalItems"
                :current-page.sync="currentPage"
                layout="prev, pager, next, total, sizes, jumper"
            />
        </div>
        <!-- <el-row style="position:absolute;right:50px">
            <el-pagination
                layout="prev, pager, next"
                :total="this.jobInfoPageResult.totalItems"
                :page-size="this.jobInfoPageResult.pageSize"
                @current-change="onClickChangePage"
                :hide-on-single-page="true"
            />
        </el-row> -->
        <!-- 新增任务弹框 -->
        <el-dialog
            :close-on-click-modal="false"
            :title="showDetail?'任务详情':(copy ? '复制任务': modifiedJobForm.id ? '编辑任务':'新建任务')"
            :visible.sync="modifiedJobFormVisible"
            @close="cancelJob('modifiedJobForm')"
            width="60%"
        >
            <el-form
                :model="modifiedJobForm"
                label-width="120px"
                :rules="rules"
                ref="modifiedJobForm"
            >
                <el-form-item
                    :label="$t('jacp.message.jobSelect')"
                    prop="appId"
                >
                    <el-select
                        v-model="modifiedJobForm.appId"
                        :placeholder="$t('jacp.message.jobSelect')"
                        :disabled="modifiedJobForm.id"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in AppOptions"
                            :key="item.id"
                            :label="item.applyName"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    prop="jobName"
                    :label="$t('jacp.message.jobName')"
                >
                    <el-input
                        v-model="modifiedJobForm.jobName"
                        :disabled="showDetail"
                    />
                </el-form-item>
                <el-form-item :label="$t('jacp.message.jobDescription')">
                    <el-input
                        type="textarea"
                        :disabled="showDetail"
                        :autosize="{ minRows: 1, maxRows: 5}"
                        v-model="modifiedJobForm.jobDescription"
                    />
                </el-form-item>
                <!-- 任务参数 -->
                <!-- <el-form-item :label="$t('jacp.message.jobParams')">
                    <el-input v-model="modifiedJobForm.jobParams" />
                </el-form-item> -->
                <el-form-item
                    :label="$t('jacp.message.scheduleInfo')"
                    style="margin-bottom: 0px"
                >
                    <el-row>
                        <el-col :span="8">
                            <el-form-item
                                prop="timeExpressionType"
                            >
                                <el-select
                                    v-model="modifiedJobForm.timeExpressionType"
                                    :placeholder="$t('jacp.message.timeExpressionType')"
                                    :disabled="showDetail"
                                >
                                    <el-option
                                        v-for="item in timeExpressionTypeOptions"
                                        :key="item.key"
                                        :label="item.label"
                                        :value="item.key"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item
                                prop="timeExpression"
                            >
                                <el-input
                                    v-if="modifiedJobForm.timeExpressionType"
                                    v-model="modifiedJobForm.timeExpression"
                                    :disabled="showDetail"
                                    :placeholder="$t('jacp.message.timeExpressionPlaceHolder')"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col
                            :span="4"
                            v-show="!showDetail"
                        >
                            <el-button
                                v-if="modifiedJobForm.timeExpressionType && modifiedJobForm.timeExpression"
                                type="text"
                                @click="onClickValidateTimeExpression"
                                style="padding-left: 10px"
                            >
                                {{ $t('jacp.message.validateTimeExpression') }}
                            </el-button>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item
                    :label="$t('jacp.message.executeConfig')"
                    style="margin-bottom: 0px"
                >
                    <el-row>
                        <el-col :span="5">
                            <el-form-item
                                prop="executeType"
                            >
                                <el-select
                                    v-model="modifiedJobForm.executeType"
                                    :placeholder="$t('jacp.message.executeType')"
                                    :disabled="modifiedJobForm.id"
                                >
                                    <el-option
                                        v-for="item in executeTypeOptions"
                                        :key="item.key"
                                        :label="item.label"
                                        :value="item.key"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                prop="processorType"
                            >
                                <el-select
                                    v-model="modifiedJobForm.processorType"
                                    :placeholder="$t('jacp.message.processorType')"
                                    :disabled="modifiedJobForm.id"
                                >
                                    <el-option
                                        v-for="item in processorTypeOptions"
                                        :key="item.key"
                                        :label="item.label"
                                        :value="item.key"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="13">
                            <el-form-item
                                prop="processorInfo"
                            >
                                <el-input
                                    v-model="modifiedJobForm.processorInfo"
                                    :disabled="modifiedJobForm.id"
                                    :placeholder="verifyPlaceholder(modifiedJobForm.processorType)"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form-item>
                <!-- 运行时配置 -->
                <!-- <el-form-item :label="$t('jacp.message.runtimeConfig')">
                    <el-row>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.maxInstanceNum')"
                                v-model="modifiedJobForm.maxInstanceNum"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.maxInstanceNum') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.threadConcurrency')"
                                v-model="modifiedJobForm.concurrency"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.threadConcurrency') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.timeout')"
                                v-model="modifiedJobForm.instanceTimeLimit"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.timeout') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item> -->
                <!-- 重试配置 -->
                <!-- <el-form-item :label="$t('jacp.message.retryConfig')">
                    <el-row>
                        <el-col :span="12">
                            <el-input
                                :placeholder="$t('jacp.message.taskRetryTimes')"
                                v-model="modifiedJobForm.instanceRetryNum"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.taskRetryTimes') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="12">
                            <el-input
                                :placeholder="$t('jacp.message.subTaskRetryTimes')"
                                v-model="modifiedJobForm.taskRetryNum"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.subTaskRetryTimes') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item> -->
                <!-- 机器配置 -->
                <!-- <el-form-item :label="$t('jacp.message.workerConfig')">
                    <el-row>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.minCPU')"
                                v-model="modifiedJobForm.minCpuCores"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.minCPU') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.minMemory')"
                                v-model="modifiedJobForm.minMemorySpace"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.minMemory') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.minDisk')"
                                v-model="modifiedJobForm.minDiskSpace"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.minDisk') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item> -->
                <!-- 集群配置 -->
                <!-- <el-form-item :label="$t('jacp.message.clusterConfig')">
                    <el-row>
                        <el-col :span="16">
                            <el-input
                                :placeholder="$t('jacp.message.designatedWorkerAddressPLH')"
                                v-model="modifiedJobForm.designatedWorkers"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.designatedWorkerAddress') }}</template>
                            </el-input>
                        </el-col>
                        <el-col :span="8">
                            <el-input
                                :placeholder="$t('jacp.message.maxWorkerNumPLH')"
                                v-model="modifiedJobForm.maxWorkerCount"
                                oninput="value=Number(value.replace(/[^\d]/g,''))"
                                class="ruleContent"
                            >
                                <template slot="prepend">{{ $t('jacp.message.maxWorkerNum') }}</template>
                            </el-input>
                        </el-col>
                    </el-row>
                </el-form-item> -->
                <el-form-item :label="$t('jacp.message.alarmConfig')">
                    <!-- <el-select
                        v-model="modifiedJobForm.notifyUserIds"
                        multiple
                        filterable
                        :placeholder="$t('jacp.message.alarmSelectorPLH')"
                    >
                        <el-option
                            v-for="user in userList"
                            :key="user.id"
                            :label="user.username"
                            :value="user.id"
                        />
                    </el-select> -->
                    <jacp-input-users
                        v-model="inputUsers"
                        placeholder="姓名"
                        :disabled="showDetail"
                        :class="showDetail?'show-detail':''"
                    />
                </el-form-item>
            </el-form>
            <div class="dialog-footer" v-show="!showDetail">
                <el-button
                    type="primary"
                    @click="saveJob('modifiedJobForm')"
                >
                    {{ $t('jacp.button.save') }}
                </el-button>
                <el-button @click="cancelJob('modifiedJobForm')">{{ $t('jacp.button.cancel') }}</el-button>
            </div>
        </el-dialog>

        <el-dialog
            :close-on-click-modal="false"
            :visible.sync="timeExpressionValidatorVisible"
            v-if="timeExpressionValidatorVisible"
        >
            <TimeExpressionValidator
                :time-expression="modifiedJobForm.timeExpression"
                :time-expression-type="modifiedJobForm.timeExpressionType"
            />
        </el-dialog>
    </div>
</template>

<script>
import { Task } from '@/models/taskManage';
import TimeExpressionValidator from './TimeExpressionValidator';
import { mapState } from 'vuex';

export default {
    name: 'TaskManage',
    data() {
        return {
            showDetail: false,
            copy: false,
            copyId: '',
            inputUsers: [],
            enable: false,
            // 新建任务是否显示
            modifiedJobFormVisible: false,
            // 新建任务对象
            modifiedJobForm: {
                id: undefined,
                jobName: '',
                jobDescription: '',
                appId: '',
                jobParams: '',
                timeExpressionType: '',
                timeExpression: '',
                executeType: '',
                processorType: '',
                processorInfo: '',
                maxInstanceNum: 0,
                concurrency: 5,
                instanceTimeLimit: 0,
                instanceRetryNum: 0,
                taskRetryNum: 1,

                minCpuCores: 0,
                minMemorySpace: 0,
                minDiskSpace: 0,

                enable: true,
                designatedWorkers: '',
                maxWorkerCount: 0,
                notifyUserIds: [],

            },
            AppOptions: [], // 应用
            // 任务查询请求对象
            jobQueryContent: {
                appId: null, // 应用
                jobType: '', // 类型
                jobId: undefined, // 任务ID
                keyWord: undefined, // 关键字
                index: 0,
                pageSize: 10,
            },
            jobTypeOptions: [
                { id: '', typeName: '全部' },
                { id: 1, typeName: '修复数据' },
                { id: 2, typeName: '业务功能' },
            ],
            // 任务列表（查询结果）
            jobInfoPageResult: {
                pageSize: 10,
                totalItems: 0,
                data: [],
                index: 0,
            },
            currentPage: 1,
            // 时间表达式选择类型
            timeExpressionTypeOptions: [{ key: 'API', label: 'API' }, { key: 'CRON', label: 'CRON' }, { key: 'FIXED_RATE', label: this.$t('jacp.message.fixRate') }, { key: 'FIXED_DELAY', label: this.$t('jacp.message.fixDelay') }, { key: 'WORKFLOW', label: this.$t('jacp.message.workflow') }],
            // 处理器类型
            processorTypeOptions: [{ key: 'BUILT_IN', label: this.$t('jacp.message.builtIn') }, { key: 'EXTERNAL', label: this.$t('jacp.message.external') }], // {key: "SHELL", label: "SHELL"}, {key: "PYTHON", label: "PYTHON"}
            // 执行方式类型
            executeTypeOptions: [{ key: 'STANDALONE', label: this.$t('jacp.message.standalone') }, { key: 'BROADCAST', label: this.$t('jacp.message.broadcast') }, { key: 'MAP', label: this.$t('jacp.message.map') }, { key: 'MAP_REDUCE', label: this.$t('jacp.message.mapReduce') }],
            // 用户列表
            userList: [],
            // 时间表达式校验窗口
            timeExpressionValidatorVisible: false,
            rules: {
                appId: [
                    { required: true, message: '请选择应用', trigger: 'change' },
                ],
                jobName: [
                    { required: true, message: '请输入任务名称', trigger: 'blur' },
                ],
                timeExpressionType: [
                    { required: true, message: '请选择时间表达式类型', trigger: 'change' },
                ],
                timeExpression: [
                    { required: true, message: '请填写CRON表达式', trigger: 'blur' },
                ],
                executeType: [
                    { required: true, message: '请选择执行类型', trigger: 'change' },
                ],
                processorType: [
                    { required: true, message: '请选择处理器类型', trigger: 'change' },
                ],
                processorInfo: [
                    { required: true, message: '请填写执行配置', trigger: 'blur' },
                ],
            },
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    components: {
        TimeExpressionValidator,
        // AddTaskDialog,
    },
    mounted() {
        this.listJobInfos();
        Task.appInfoList().then((res) => {
            this.AppOptions = res.data.data;
        });
        // 加载用户信息
        Task.userList().then((res) => {
            this.userList = res.data.data;
        });
    },
    methods: {
        openDesc(data) {
            this.inputUsers = data.notifyUserList.map(item => ({
                erp: item.userErp,
                name: item.userName,
                orgTierName: item.orgTierName,
                orgTierCode: item.orgTierCode,
            }));
            this.modifiedJobForm = JSON.parse(JSON.stringify(data));
            this.showDetail = true;
            this.modifiedJobFormVisible = true;
        },
        saveJobFun(flag) {
            this.modifiedJobForm.notifyUserIds = this.inputUsers.map(item => (item.erp));
            Task.saveJob(this.modifiedJobForm).then(() => {
                this.$message.success(this.$t('jacp.message.success'));
                if (this.enable) {
                    setTimeout(() => {
                        this.listJobInfos();
                    }, 2000);
                } else {
                    this.listJobInfos();
                }
                this.enable = false;
                this.inputUsers = [];
                if (flag) {
                    this.cancelJob('modifiedJobForm');
                }
            });
        },
        // 保存任务
        saveJob(formName) {
            // eslint-disable-next-line consistent-return
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.copy) {
                        Task.copyJob({ id: this.copyId }).then((res) => {
                            this.copy = false;
                            const resData = res.data.data;
                            if (resData) {
                                this.modifiedJobForm.id = resData.id;
                                this.modifiedJobForm.gmtCreate = resData.gmtCreate;
                                this.modifiedJobForm.gmtModified = resData.gmtModified;
                                this.saveJobFun(true);
                            }
                        });
                    } else {
                        this.saveJobFun(true);
                    }
                } else {
                    return false;
                }
            });
        },
        cancelJob(formName) {
            this.$refs[formName].resetFields();
            this.modifiedJobFormVisible = false;
            this.copy = false;
            this.showDetail = false;
        },
        // 搜索
        listJobInfos(i) {
            if (i === 0) {
                this.jobQueryContent.index = 0;
                this.currentPage = 1;
            }
            Task.searchTaskList(this.jobQueryContent).then((res) => {
                this.jobInfoPageResult = res.data.data;
            });
        },
        // 重置
        onClickReset(i) {
            if (i === 0) {
                this.jobQueryContent.index = 0;
                this.currentPage = 1;
            }
            this.jobQueryContent.appId = undefined;
            this.jobQueryContent.jobType = '';
            this.jobQueryContent.keyword = undefined;
            this.jobQueryContent.jobId = undefined;
            this.listJobInfos();
        },
        // 新增任务
        onClickNewJob() {
            this.modifiedJobForm.id = undefined;
            this.modifiedJobForm.appId = undefined;
            this.modifiedJobForm.jobName = undefined;
            this.modifiedJobForm.jobDescription = undefined;
            this.modifiedJobForm.jobParams = undefined;
            this.modifiedJobForm.timeExpression = undefined;
            this.modifiedJobForm.timeExpressionType = undefined;
            this.modifiedJobForm.processorInfo = undefined;
            this.modifiedJobForm.processorType = undefined;
            this.modifiedJobForm.executeType = undefined;
            this.modifiedJobForm.notifyUserIds = [];
            this.modifiedJobForm.maxInstanceNum = 0;
            this.modifiedJobForm.concurrency = 5;
            this.modifiedJobForm.instanceTimeLimit = 0;
            this.modifiedJobForm.instanceRetryNum = 0;
            this.modifiedJobForm.taskRetryNum = 1;
            this.modifiedJobForm.minCpuCores = 0;
            this.modifiedJobForm.minMemorySpace = 0;
            this.modifiedJobForm.minDiskSpace = 0;
            this.modifiedJobForm.maxWorkerCount = 0;

            this.enable = false;
            this.inputUsers = [];
            this.modifiedJobFormVisible = true;
        },
        verifyPlaceholder(processorType) {
            let res;
            switch (processorType) {
            case 'BUILT_IN': res = this.$t('jacp.message.javaProcessorInfoPLH'); break;
            case 'EXTERNAL': res = this.$t('jacp.message.containerProcessorInfoPLH'); break;
            case 'SHELL': res = this.$t('jacp.message.shellProcessorInfoPLH'); break;
            case 'PYTHON': res = this.$t('jacp.message.pythonProcessorInfoPLH'); break;
            default:
            }
            return res;
        },
        // 翻译执行类型
        translateExecuteType(executeType) {
            switch (executeType) {
            case 'STANDALONE': return this.$t('jacp.message.standalone');
            case 'BROADCAST': return this.$t('jacp.message.broadcast');
            case 'MAP_REDUCE': return this.$t('jacp.message.mapReduce');
            case 'MAP': return this.$t('jacp.message.map');
            default: return 'UNKNOWN';
            }
        },
        // 翻译处理器类型
        translateProcessorType(processorType) {
            if (processorType === 'EXTERNAL') {
                return this.$t('jacp.message.external');
            }
            return this.$t('jacp.message.builtIn');
        },
        // 点击 立即运行按钮
        onClickRun(data) {
            const params = {
                id: data.id,
                appId: data.appId,
            };
            Task.taskRun(params).then(() => {
                this.$message.success(this.$t('任务执行成功'));
            });
        },
        // 查看日志 跳转到调度日志
        onClickShowLog() {
            this.$emit('skipLog');
        },
        // 点击停止实例
        changeJobStatus(data) {
            if (data.enable) {
                this.$confirm(`停用任务后  ${data.jobName}  会失效，确认要停用该任务吗？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    Task.disableJob({ id: data.id }).then(() => {
                        this.$message.success(`已停用  ${data.jobName}  任务`);
                        // 重新加载列表
                        this.onClickReset();
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消停用',
                    });
                });
            } else {
                // 启用，则发起正常的保存操作
                data.enable = true;
                this.enable = true;
                this.modifiedJobForm = data;

                this.jobQueryContent.appId = undefined;
                this.jobQueryContent.jobType = '';
                this.jobQueryContent.keyword = undefined;
                this.jobQueryContent.jobId = undefined;
                this.saveJobFun();
                // this.onClickReset();
            }
        },
        // 点击 删除任务
        onClickDeleteJob(data) {
            this.$confirm(`删除任务后  ${data.jobName}  会失效，确定要执行吗?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                Task.deleteJob({ id: data.id }).then(() => {
                    this.$message({
                        type: 'success',
                        message: '删除成功!',
                    });
                    this.listJobInfos();
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除',
                });
            });
        },
        // 点击 编辑按钮
        onClickModify(data) {
            // 修复点击编辑后再点击新增 行数据被清空 的问题
            // [{"erp":"admin","name":"admin","orgTierCode":"/00000001/00000002/00000003","orgTierName":"虚拟集团-虚拟子集团-虚拟一级组织"}]
            this.inputUsers = data.notifyUserList.map(item => ({
                erp: item.userErp,
                name: item.userName,
                orgTierName: item.orgTierName,
                orgTierCode: item.orgTierCode,
            }));
            this.modifiedJobForm = JSON.parse(JSON.stringify(data));
            this.modifiedJobFormVisible = true;
        },
        // 点击 复制任务
        onClickCopyJob(data) {
            this.copy = true;
            this.copyId = data.id;
            this.inputUsers = data.notifyUserList.map(item => ({
                erp: item.userErp,
                name: item.userName,
                orgTierName: item.orgTierName,
                orgTierCode: item.orgTierCode,
            }));
            this.modifiedJobForm = JSON.parse(JSON.stringify(data));
            this.modifiedJobForm.jobName = `${this.modifiedJobForm.jobName}_COPY`;
            this.modifiedJobFormVisible = true;
        },
        // 分页
        onClickChangePage(index) {
            this.jobQueryContent.index = index - 1;
            this.currentPage = index;
            this.listJobInfos();
        },
        handleSizeChange(size) {
            this.jobQueryContent.pageSize = size;
            this.listJobInfos();
        },
        // 点击校验
        onClickValidateTimeExpression() {
            this.timeExpressionValidatorVisible = true;
        },
    },
};
</script>

<style lang="less" scoped>
  .show-detail{
       /deep/.el-input.is-disabled .el-input__inner,
       /deep/.jacp-users-input__multi__disabled {
            background-color: #f0f2f5;
            border-color: #e4e7ed;
            color: #c0c4cc;
        }
  }
.taskManage {
  box-sizing: border-box;
  padding: 20px;
  position: relative;
  .footer {
    background: #fff;
    height: 64px;
    position: relative;
    .j-pagination {
        position: absolute;
        top: 50%;
        right: 16px;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        }
  }
  .jacp-users-input__root {
      width: 30%;
  }
  .el-button--text:hover, .el-button--text:focus, .el-button--text {
    border-color: transparent;
    background-color: transparent;
    font-size: 14px;
    font-weight: 500;
    }
}
</style>
