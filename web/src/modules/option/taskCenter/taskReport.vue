
<template>
    <div class="taskReport">
        <!-- 第0行，显示时间信息 -->
        <div class="header">
            <span>选择应用</span>
            <el-select
                v-model="appId"
                placeholder="请选择应用"
                @change="getList()"
            >
                <el-option
                    v-for="item in AppOptions"
                    :key="item.id"
                    :label="item.applyName"
                    :value="item.id"
                />
            </el-select>
        </div>
        <el-row :gutter="24">
            <el-col :span="12">
                <el-card
                    shadow="always"
                    style="text-align:center"
                >
                    <div>{{ $t('jacp.message.appName') }}</div>
                    <div>{{ applyName }}</div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card shadow="always">
                    <div>
                        {{ $t('jacp.message.omsServerTimezone') }}：{{ systemInfo.timezone }}
                    </div>
                    <div>
                        {{ $t('jacp.message.omsServerTime') }}：{{ systemInfo.serverTime }}
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card shadow="always">
                    <div>
                        {{ $t('jacp.message.localBrowserTimezone') }}：
                        {{ Intl.DateTimeFormat().resolvedOptions().timeZone }}
                    </div>
                    <div>
                        {{ $t('jacp.message.localBrowserTime') }}：
                        {{ this.common.timestamp2Str(new Date().getTime()) }}
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <!-- 第一行，显示概览 overview -->
        <el-row :gutter="24">
            <el-col :span="6">
                <div class="wrap">
                    <div class="grid-content bg-purple">
                        <div class="text mTitle">{{ $t('jacp.message.totalJobNum') }}</div>
                        <div class="text mText">{{ systemInfo.jobCount }}</div>
                    </div>
                    <i class="el-icon-orange" />
                </div>
            </el-col>
            <el-col :span="6">
                <div class="wrap">
                    <div class="grid-content bg-purple">
                        <div class="text mTitle">{{ $t('jacp.message.runningInstanceNum') }}</div>
                        <div class="text mText">{{ systemInfo.runningInstanceCount }}</div>
                    </div>
                    <i class="el-icon-timer" />
                </div>
            </el-col>
            <el-col :span="6">
                <div class="wrap">
                    <div class="grid-content bg-purple">
                        <div class="text mTitle">{{ $t('jacp.message.recentFailedInstanceNum') }}</div>
                        <div class="text mText">{{ systemInfo.failedInstanceCount }}</div>
                    </div>
                    <i class="el-icon-bell" />
                </div>
            </el-col>
            <el-col :span="6">
                <div class="wrap">
                    <div class="grid-content bg-purple">
                        <div class="text mTitle">{{ $t('jacp.message.workerNum') }}</div>
                        <div class="text mText">{{ activeWorkerCount }}</div>
                    </div>
                    <i class="el-icon-cpu" />
                </div>
            </el-col>
        </el-row>
        <!-- 第二行，显示在线机器，包个容器方便日后更改 -->
        <el-row>
            <el-col :span="24">
                <!-- 只要在el-table元素中定义了height属性，即可实现固定表头的表格，而不需要额外的代码 -->
                <!-- 可以通过指定 Table 组件的 row-class-name 属性来为 Table 中的某一行添加 class，表明该行处于某种状 -->
                <el-table
                    :data="workerList"
                    style="width: 100%"
                    height="400px"
                    :row-class-name="workerTableRowClassName"
                >
                    <el-table-column
                        prop="address"
                        :label="$t('jacp.message.workerAddress')"
                    />
                    <el-table-column
                        prop="cpuLoad"
                        :label="$t('jacp.message.cpuLoad')"
                    />
                    <el-table-column
                        prop="memoryLoad"
                        :label="$t('jacp.message.memoryLoad')"
                    />
                    <el-table-column
                        prop="diskLoad"
                        :label="$t('jacp.message.diskLoad')"
                    />
                    <!-- <el-table-column
                        prop="tag"
                        label="tag"
                    /> -->
                    <el-table-column
                        prop="lastActiveTime"
                        :label="$t('jacp.message.lastActiveTime')"
                    />
                </el-table>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import { Task } from '@/models/taskManage';

export default {
    name: 'TaskManage',
    data() {
        return {
            appId: '',
            applyName: '',
            systemInfo: {
                jobCount: 'N/A',
                runningInstanceCount: 'N/A',
                failedInstanceCount: 'N/A',
                serverTime: 'UNKNOWN',
                timezone: 'UNKNOWN',
            },
            activeWorkerCount: 'N/A',
            workerList: [],
            AppOptions: [], // 应用选择
        };
    },
    mounted() {
        Task.appInfoList().then((res) => {
            this.AppOptions = res.data.data;
            this.appId = res.data.data[0].id;
            this.applyName = res.data.data[0].applyName;
            if (res) {
                this.getList();
            }
        });
    },
    methods: {
        getList() {
            this.AppOptions.forEach((item) => {
                if (item.id === this.appId) {
                    this.applyName = item.applyName;
                }
            });
            const params = { appId: this.appId };
            Task.listWorker(params).then((res) => {
                res.data.data.sort((a, b) => a.status - b.status);
                this.workerList = res.data.data;
                let num = 0;
                this.workerList.forEach((w) => {
                    if (w.status !== 9999) {
                        // eslint-disable-next-line no-plusplus
                        num++;
                    }
                });
                this.activeWorkerCount = num;
            });
            Task.overview(params).then((res) => {
                this.systemInfo = res.data.data;
            });
        },
        workerTableRowClassName({ row }) {
            switch (row.status) {
            case 1: return 'success-row';
            case 2: return 'warning-row';
            case 9999: return 'offline-row';
            default: return 'error-row';
            }
        },
    },
};
</script>

<style lang="less" scoped>
.taskReport {
    .el-card {
        min-height: 114px;
    }
   /* 头部信息 */
   .header {
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        span {
            margin-right: 10px;
        }
   }
    .wrap {
        background: #fff;
        display: flex;
        text-align: center;
        justify-content: space-around;
        align-items: center;
        margin: 10px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
        font-size: 1.5rem;
        font-weight: bolder;
        height: 131px;
    }
    .mTitle{
        font-size: 16px;
        color:#0f0f0fad;
        margin-bottom: 8px;
    }
    .mText{
      font-size: 18px;
      color:#0f0f0fff;
      margin-bottom: 8px;
    }

    .el-card {
        margin: 10px;
    }
}
</style>
<!-- 全局属性，解决 element-ui 的 row-class-name 不生效问题 -->
<style>
    .el-table .warning-row {
        color: darkgoldenrod;
    }

    .el-table .success-row {
        color: green;
    }

    .el-table .error-row {
        color: red;
    }

    .el-table .offline-row {
        color: darkgray;
    }
</style>
