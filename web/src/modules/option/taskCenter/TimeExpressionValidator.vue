<template>
    <div>
        <el-card class="box-card">
            <div
                v-for="res in nextNTriggerTime"
                :key="res"
                class="text item"
            >
                {{ res }}
            </div>
        </el-card>
    </div>
</template>

<script>
import { Task } from '@/models/taskManage';

export default {
    name: 'TimeExpressionValidator',
    // 数据传递
    props: ['timeExpressionType', 'timeExpression'],
    data() {
        return {
            nextNTriggerTime: [],
        };
    },
    methods: {
        checkTimeExpression() {
            const jobParams = {
                timeExpressionType: this.timeExpressionType,
                timeExpression: this.timeExpression,
            };
            Task.checkTimeExpression(jobParams).then((res) => {
                this.nextNTriggerTime = res.data.data;
            });
            // const that = this;
            // const url = `/validate/timeExpression?timeExpressionType=${this.timeExpressionType}&timeExpression=${this.timeExpression}`;
            // this.axios.get(url).then(res => that.nextNTriggerTime = res);
        },
    },
    mounted() {
        this.checkTimeExpression();
    },
};
</script>

<style scoped>

</style>
