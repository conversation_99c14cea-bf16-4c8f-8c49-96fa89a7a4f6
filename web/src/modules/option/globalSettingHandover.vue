<template>
    <div class="global-setting-handover">
        <el-radio-group
            style="margin: 10px 0;"
            v-model="handoverType"
            @change="cancleHandover()"
        >
            <!-- <el-radio label="1">对某部门离职人员的事项进行交接</el-radio> -->
            <el-radio label="2">对某具体人员事项进行交接（在职人员、离职人员）</el-radio>
        </el-radio-group>
        <el-form
            :inline="true"
            @submit.native.prevent
        >
            <el-form-item label="人员工号">
                <el-input
                    v-model="condition.handoverErp"
                    placeholder="人员工号"
                />
            </el-form-item>
            <el-form-item
                label="离职人所属部门"
                v-if="handoverType === '1'"
            >
                <jacp-org-tree
                    :root-org-id="orgId"
                    :root-org-name="orgName"
                    @check-org="changeOrg"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    @click="query"
                    v-if="jurisdicList['archDeptSpecial:handover:search'] === 0"
                >
                    {{ handoverType === '1' ? '查询部门下离职人员' : '查询' }}
                </el-button>
                <el-tooltip
                    v-else-if="jurisdicList['archDeptSpecial:handover:search'] === 1"
                    effect="dark"
                    :content="tips"
                    placement="top">
                    <el-button
                        type="primary"
                        style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;">
                        {{ handoverType === '1' ? '查询部门下离职人员' : '查询' }}
                    </el-button>
                </el-tooltip>
            </el-form-item>
        </el-form>
        <el-table
            :data="data"
            :max-height="500"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" />
            <el-table-column
                prop="typeDesc"
                label="待交接项"
                width="150"
                :filters="transferTypeList"
                :filter-method="filterHandler"
            />
            <el-table-column
                prop="erp"
                width="120"
                label="人员工号"
            />
            <el-table-column
                prop="content"
                label="待交接项相关信息"
            >
                <template slot-scope="scope">
                    <a
                        :href="scope.row.contentUrl"
                        target="_blank"
                    >{{ `${scope.row.contentId}-${scope.row.content}` }}</a>
                </template>
            </el-table-column>∂
            <el-table-column
                prop="orgName"
                label="所属部门"
            />
            <el-table-column
                prop="leader"
                width="120"
                label="直接上级"
            />
        </el-table>
        <el-dropdown
            v-if="jurisdicList['archDeptSpecial:handover:del'] === 0"
            @command="handleCommand"
            class="global-setting-handover__submit_button"
        >
            <el-button type="primary">
                交接（{{ this.handover.transferId.length }}）<i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handover">交接给指定接收人</el-dropdown-item>
                <el-dropdown-item command="handoverToLeader">交接直属上级</el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown
            v-else-if="jurisdicList['archDeptSpecial:handover:del'] === 1"
            @command="handleCommand"
            class="global-setting-handover__submit_button"
        >
            <el-tooltip
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    type="primary"
                    style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;">
                    交接（{{ this.handover.transferId.length }}）<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
            </el-tooltip>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item disabled command="handover">交接给指定接收人</el-dropdown-item>
                <el-dropdown-item disabled command="handoverToLeader">交接直属上级</el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
        <el-dialog
            title="交接给指定接收人"
            :visible.sync="showHandoverDialog"
        >
            <div>交接后，所选交接项都会交接给同一位接手人，系统会向接手人发送邮件通知</div>
            <jacp-input-users
                v-model="userInfo"
                placeholder="输入接手人姓名/工号"
                :max-count="1"
                style="margin-top: 16px"
            />
            <div
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="cancleHandover()">取 消</el-button>
                <el-button
                    type="primary"
                    @click="submitHandover"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>

        <el-dialog
            title="交接给指定接收人"
            :visible.sync="showHandoverToLeaderDialog"
        >
            <div>交接后，所选交接项都会交接给离职人员各自的直属上级，系统会向每位直属上级发送邮件通知</div>
            <div
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="cancleHandover()">取 消</el-button>
                <el-button
                    type="primary"
                    @click="submitHandover('toLeader')"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script type="text/javascript">
import GlobalSetting from '@/models/globalSetting';
import { mapState } from 'vuex';

export default {
    props: {
        orgId: {
            type: String,
        },
        orgName: {
            type: String,
        },
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    data() {
        return {
            handoverType: '2',
            condition: {
                adminOrgId: this.orgId,
                handoverErp: undefined,
            },
            handover: {
                adminOrgId: this.orgId,
                transferId: [],
                handoverErp: undefined,
                status: undefined,
                targetErp: undefined,
                toLeader: false,
            },
            transferTypeList: [
                { value: 'admin', text: '团队空间管理员' },
                { value: 'deptAdmin', text: '部门管理员' },
                { value: 'proposer', text: '需求提出人' },
                { value: 'receiver', text: '需求受理人' },
                { value: 'processor', text: '需求当前操作人' },
                { value: 'finishDemand', text: '收益验证人' },
            ],
            data: [],
            userInfo: [],
            showHandoverDialog: false,
            showHandoverToLeaderDialog: false,
        };
    },
    methods: {
        filterHandler(value, row) {
            return row.type === value;
        },
        changeOrg(orgId) {
            this.handover.adminOrgId = orgId;
            this.condition.adminOrgId = orgId;
        },
        query() {
            if (this.handoverType === '1') {
                GlobalSetting.getHandoverInfo(this.condition).then((data) => {
                    this.data = data || [];
                    this.handover.status = 1;
                });
            } else if (this.condition.handoverErp) {
                GlobalSetting.getHandoverInfoByPersonal(this.condition.handoverErp).then((data) => {
                    this.data = data || [];
                    if (this.data.length > 0) {
                        this.handover.status = this.data[0].status;
                    }
                });
            }
        },
        handleSelectionChange(val) {
            this.handover.transferId = val.map(v => v.id);
        },
        handleCommand(command) {
            if (!this.handover.transferId || this.handover.transferId.length === 0) {
                return this.$notify.error({
                    title: '错误',
                    message: '请选择交接项',
                });
            }
            this.showHandoverDialog = command === 'handover';
            this.showHandoverToLeaderDialog = command === 'handoverToLeader';
        },
        cancleHandover() {
            this.data = [];
            this.userInfo = [];
            this.showHandoverDialog = false;
            this.showHandoverToLeaderDialog = false;
            this.handover.targetErp = undefined;
            this.handover.toLeader = false;
            this.handover.adminOrgId = this.condition.adminOrgId;
            this.handover.status = undefined;
            this.handover.handoverErp = undefined;
            this.handover.toLeader = false;
        },
        submitHandover(param) {
            this.handover.toLeader = param === 'toLeader';
            if (this.userInfo && this.userInfo.length > 0) {
                this.handover.targetErp = this.userInfo[0].erp;
            }
            if (!this.handover.toLeader && !this.handover.targetErp) {
                return this.$notify.error({
                    title: '错误',
                    message: '请确认接手人工号或指定给直属上级',
                });
            }
            if (this.handoverType !== '1') {
                this.handover.adminOrgId = undefined;
                this.handover.handoverErp = this.condition.handoverErp;
            }
            GlobalSetting.submitHandoverInfo(this.handover).then(() => {
                this.cancleHandover();
                this.query();
            });
        },
    },
    watch: {
        orgId: {
            handler(n) {
                this.handover.adminOrgId = n;
                this.condition.adminOrgId = n;
                this.query();
            },
            immediate: true,
        },
    },
};
</script>
<style lang="less">
.global-setting-handover {
    min-height: 600px;

    &__submit_button {
        float: right;
        margin: 24px 0;
    }
}
</style>
