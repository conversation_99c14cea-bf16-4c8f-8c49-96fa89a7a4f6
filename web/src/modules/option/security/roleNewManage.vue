<template>
    <div class="new-role-setting" v-if="roleList.length">
        <div class="left">
            <div class="heard">
                <h2>角色管理<i /></h2>
                <div class="subh">
                    平台
                </div>
                <ul class="list">
                    <li
                        v-for="item in roleList.filter(item => item.roleType === 'plat')"
                        :key="item.code"
                        @click="getRight(item)"
                        :class="{
                            active: item.code === activeItem.code
                        }"
                    >
                        {{ item.name }}
                        <el-tooltip
                            v-if="item.description"
                            class="item"
                            effect="dark"
                            :content="item.description"
                            placement="top">
                            <i
                                class="jacp-icon-help"
                            />
                        </el-tooltip>
                    </li>
                </ul>
                <div class="subh">
                    应用商店
                </div>
                <ul class="list">
                    <li
                        v-for="item in roleList.filter(item => item.roleType === 'appStore')"
                        :key="item.code"
                        @click="getRight(item)"
                        :class="{
                            active: item.code === activeItem.code
                        }"
                    >
                        {{ item.name }}
                        <el-tooltip
                            v-if="item.description"
                            class="item"
                            effect="dark"
                            :content="item.description"
                            placement="top">
                            <i
                                class="jacp-icon-help"
                            />
                        </el-tooltip>
                    </li>
                </ul>
            </div>
        </div>
        <div class="right">
            <div class="title">{{ activeItem.name }}</div>
            <el-tabs
                v-model="activeTpye"
            >
                <el-tab-pane
                    v-if="showMemberManagement"
                    label="成员管理"
                    key="t-memberManagement-t"
                    name="memberManagement"
                >
                    <memberManagement
                        :activeItem="activeItem"
                        :roleList="roleList"
                        ref="memberManagement"
                    />
                </el-tab-pane>
                <el-tab-pane
                    label="权限配置"
                    name="permissionConfiguration"
                >
                    <permissionConfiguration
                        :activeItem="activeItem"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import memberManagement from './comPage/memberManagement.vue';
import permissionConfiguration from './comPage/permissionConfiguration.vue';
import { roleQuery } from './uaos';
import { getPermissionUicomponents } from '../modules';

export default {
    components: {
        memberManagement,
        permissionConfiguration,
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
        showMemberManagement() {
            return this.activeItem.code !== 'employee' && this.activeItem.code !== 'user';
        },
    },
    data() {
        return {
            activeTpye: 'memberManagement',
            activeItem: {},
            roleList: [
                // {
                //     appCode: 'code1', // 应用编码
                //     builtIn: false, // 是否内置
                //     code: 'superAdmin', // 角色编码
                //     description: 'tips', // 描述
                //     name: '系统管理员', // 角色名称
                // },
            ],
        };
    },
    mounted() {
        this.init();
    },
    watch: {
        activeTpye(n, o) {
            if (n !== o) {
                if (this.$refs.memberManagement) {
                    this.$refs.memberManagement.reset();
                }
            }
        },
        showMemberManagement(n) {
            this.$nextTick(() => {
                if (n === true) {
                    if (this.$refs.memberManagement) {
                        this.$refs.memberManagement.search();
                    }
                }
            });
        },
    },
    methods: {
        getRight(item) {
            if (item.disable === true) {
                return;
            }
            this.activeItem = item;
            if (item.code === 'employee' || item.code === 'user') {
                this.activeTpye = 'permissionConfiguration';
            } else {
                this.activeTpye = 'memberManagement';
            }
            console.log('===item====', item.code);
        },
        async init() {
            await getPermissionUicomponents();
            this.roleQuerylist();
        },
        roleQuerylist() {
            roleQuery({}).then((res) => {
                const newRes = [];
                const obj = {
                    archadmin: 'archAuth:admin:perm:config:view',
                    archuser: 'archAuth:user:perm:config:view',
                    archstoreSelfAdmin: 'archAuth:storeSelfAdmin:perm:config:view',
                    archthirdPartyAdmin: 'archAuth:thirdPartyAdmin:perm:config:view',
                };
                res.forEach((el) => {
                    const data = `${el.appCode}${el.code}`;
                    if (obj[data]) {
                        //
                        if (this.jurisdicList[obj[data]] === 0) {
                            newRes.push(el);
                        }
                        if (this.jurisdicList[obj[data]] === 1) {
                            el.disable = true;
                            newRes.push(el);
                        }
                    }
                    // newRes.push(el);
                });
                if (newRes.length) {
                    setTimeout(() => {
                        this.activeItem = newRes[0];
                    }, 0);
                }
                this.roleList = newRes;
            // console.log(res);
            });
        },
    },
};
</script>

<style lang="less" scoped>
.new-role-setting {
    display: flex;
    background-color: #fff;
    height: 100%;
    .left{
        // border-left: 1px solid #EBEEF5;
        border-right: 1px solid #EBEEF5;
        width: 168px;
        .heard{
            h2 {
                padding-left: 24px;
                padding-top: 5px;
                font-size: 16px;
                font-family: PingFang SC;
                font-weight: 600;
                color: rgba(48,49,51,1);
            }
            >i{
                float: right;
                display: none;
                width: 14px;
                height: 14px;
                background-color: orchid;
                cursor: pointer;
            }
            .subh{
                margin-left: 16px;
                font-size: 15px;
                font-family: PingFang SC;
                font-weight: normal;
                color: rgba(144,147,153,1);
                height: 40px;
                line-height: 40px;
            }
        }
        .list{
            padding: 0;
            margin: 0;
            li{
                padding: 0;
                margin: 0;
                margin: 0 6px;
                line-height: 40px;
                font-size: 14px;
                font-family: PingFang SC;
                font-weight: normal;
                color: rgba(96,98,102,1);
                padding-left: 24px;
                cursor: pointer;
            }
            .active{
                margin: 0 6px;
                height: 40px;
                background: rgba(240,242,245,1);
                border-radius: 12px;
            }
        }
    }
    .right{
        flex: 1;
        padding: 24px;
        overflow: auto;
        .title{
            margin-bottom: 19px;
        }
    }
}
</style>>
