<template>
    <div
        class="member-management"
        v-if="isShow === 0"
    >
        <el-form
            :inline="true"
            :model="formInline"
            class="demo-form-inline"
        >
            <el-form-item label="成员">
                <jacp-input-users
                    class="userorgname"
                    :max-count="1"
                    v-model="formInline.user"
                    placeholder="请输入姓名"
                    prefix-icon="el-icon-search"
                />
                <!-- <i class="el-icon-search" style="float:left"/> -->
            </el-form-item>
            <el-form-item label="所属部门">
                <el-autocomplete
                    class="userorgname"
                    v-model="formInline.userOrgName"
                    :fetch-suggestions="loadOrglist"
                    placeholder="请输入部门关键字"
                    :trigger-on-focus="false"
                    @select="handleSelect"
                    value-key="fullName"
                    label="fullName"
                    clearable
                />
            </el-form-item>
            <el-form-item label="操作人">
                <jacp-input-users
                    class="userorgname"
                    :max-count="1"
                    v-model="formInline.quser"
                    placeholder="请输入操作人"
                    prefix-icon="el-icon-search"
                />
            </el-form-item>
            <el-form-item label="操作时间段">
                <el-date-picker
                    v-model="formInline.time"
                    type="daterange"
                    placeholder="请选择开始日期～结束日期"
                />
            </el-form-item>
            <el-form-item>
                <el-button @click="reset">重置</el-button>

                <el-button
                    v-if="isShowSearch === 0"
                    @click="search"
                >
                    查询
                </el-button>
                <el-tooltip
                    v-else-if="isShowSearch === 1"
                    effect="dark"
                    :content="tips"
                    placement="top"
                >
                    <el-button
                        style="color: #c0c4cc;
                        cursor: not-allowed;
                        background-image: none;
                        background-color: #fff;
                        border-color: #ebeef5;"
                    >
                        查询
                    </el-button>
                </el-tooltip>
                <el-button
                    v-if="isShowAdd === 0"
                    type="primary"
                    @click="addmember"
                >
                    新增
                </el-button>
                <el-tooltip
                    v-else-if="isShowAdd === 1"
                    effect="dark"
                    :content="tips"
                    placement="top"
                >
                    <el-button
                        type="primary"
                        style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                    >
                        新增
                    </el-button>
                </el-tooltip>

                <el-button
                    v-if="isShowDel === 0"
                    type="danger"
                    plain
                    @click="del"
                >
                    批量删除
                </el-button>
                <el-tooltip
                    v-else-if="isShowDel === 1"
                    effect="dark"
                    :content="tips"
                    placement="top"
                >
                    <el-button
                        style="cursor: not-allowed;color: #f9988f;background-color: #feeeec;border-color: #fdddda;"
                        type="danger"
                        plain
                    >
                        批量删除
                    </el-button>
                </el-tooltip>
            </el-form-item>
        </el-form>
        <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            :row-class-name="tableRowClassName"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="50"
                :selectable="selectable"
            />
            <el-table-column
                label="成员姓名"
            >
                <template slot-scope="scope">
                    {{ scope.row.userName }}({{ scope.row.userErp }})
                </template>
            </el-table-column>
            <el-table-column
                label="所属部门"
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    {{ scope.row.userOrgName }}
                </template>
            </el-table-column>
            <!-- <el-table-column
                label="负责部门"
                v-if="activeItem.code !== 'superAdmin'"
                :show-overflow-tooltip="true"
            >
                <template slot-scope="scope">
                    {{ scope.row.ownedOrgName }}
                </template>
            </el-table-column> -->
            <el-table-column
                label="操作人"
            >
                <template slot-scope="scope">
                    {{ scope.row.opUserName }}({{ scope.row.opUserErp }})
                </template>
            </el-table-column>
            <el-table-column
                label="操作时间"
                width="160"
            >
                <template slot-scope="scope">
                    {{ scope.row.opTime }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 第三行，分页插件 -->
        <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="handleSizeChange"
                @current-change="onClickChangePage"
                :page-size="this.pageSize"
                :total="this.total"
                :current-page.sync="currentPage"
                layout="total, sizes, prev, pager, next"
            />
            <!--  sizes, -->
        </div>
        <!-- 新增成员管理 -->
        <el-dialog
            :close-on-click-modal="false"
            :title="title"
            :visible.sync="visible"
            @close="close"
            width="464px"
            class="menber-managment-dialog"
        >
            <el-form>
                <el-form-item
                    label-width="200"
                >
                    <span
                        style="font-size:12px;color: #606265"
                    >姓名<span style="color: #f55445;padding-left:5px">*</span></span>
                    <jacp-input-users
                        :max-count="1"
                        v-model="add.user"
                        placeholder="请输入姓名~"
                        prefix-icon="el-icon-search"
                    />
                </el-form-item>
                <el-form-item label="所属部门">
                    <el-input
                        disabled
                        :value="add.org"
                        placeholder="暂无数据，请输入管理员姓名～"
                    />
                </el-form-item>
                <!-- <el-form-item
                    v-if="activeItem.code !=='superAdmin'">
                    <template>
                        <span style="font-size:12px;color: #606265;padding-right:5px">
                            负责部门
                            <span style="color: #f55445;padding-right:5px">*</span>
                        </span>
                        <el-tooltip
                            effect="dark"
                            content="获取当前操作人所负责的部门及子部门"
                            placement="top">
                            <i
                                class="jacp-icon-help"
                            />
                        </el-tooltip>
                        <el-autocomplete
                            v-model="add.userOrgName"
                            :fetch-suggestions="loadOrglistOwne"
                            placeholder="请输入部门关键字"
                            :trigger-on-focus="false"
                            @select="handleSelectMe"
                            value-key="fullName"
                            label="fullName"
                            style="display:block;"
                        />
                    </template>
                </el-form-item> -->
                <el-form-item
                    label="关联应用"
                    v-if="activeItem.code === 'admin' && appDataShow"
                >
                    <el-radio
                        v-model="radio"
                        label="yes"
                    >
                        是
                    </el-radio>
                    <el-radio
                        v-model="radio"
                        label="no"
                    >
                        否
                    </el-radio>
                </el-form-item>
                <el-form-item v-if="radio === 'yes' && activeItem.code === 'admin' && appDataShow">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        v-model="checkAll"
                        @change="handleCheckAllChange"
                    >
                        全选
                    </el-checkbox>
                    <div
                        style="margin: 15px 0;"
                    />
                    <el-checkbox-group
                        v-model="add.checkedCities"
                        @change="handleCheckedCitiesChange"
                    >
                        <el-checkbox
                            v-for="city in appList"
                            :label="city.code"
                            :key="city.id"
                            :disabled="city.disabled"
                        >
                            {{ city.name }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item style="text-align: right; margin-bottom:0;">
                    <div style=" display: flex;justify-content: space-between;">
                        <div>
                            <el-checkbox v-model="checked">继续创建</el-checkbox>
                            <!-- <el-button type="primary" @click="save(1)">保存并继续</el-button> -->
                        </div>
                        <div>
                            <el-button @click="close">取消</el-button>
                            <el-button
                                type="primary"
                                @click="save(true)"
                                v-if="activeItem.code === 'admin'"
                            >
                                保存
                            </el-button>
                            <el-button
                                type="primary"
                                @click="save(false)"
                                v-else
                            >
                                保存
                            </el-button>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </el-dialog>
        <el-dialog
            :close-on-click-modal="false"
            :title="deltitle"
            :visible.sync="delvisible"
            @close="close"
            width="464px"
            class="menber-managment-dialog"
        >
            <el-form>
                <el-form-item>
                    <span>确认{{ deltitle }}</span>
                    <span
                        :key="i.id"
                        v-for="(i, index ) in multipleSelection"
                    >
                        <span style="color:#2695F1;">
                            {{ i.userName }}({{ i.userErp }})
                        </span>
                        <span v-if="index != multipleSelection.length -1">、</span>
                    </span>
                    <span>吗？</span>
                </el-form-item>
                <el-form-item style="text-align: right; margin-bottom:0;">
                    <el-button @click="close(1)">取消</el-button>
                    <el-button
                        type="primary"
                        @click="delEnter"
                    >
                        确认
                    </el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';
import AdminOption from '@/models/adminOption';
import {
    getroleListByroleCode,
    addStoreSelfAdmin,
    addThirdPartyAdmin,
    deleteMemberPlatAdmin,
    deleteStoreSelfAdmin,
    deleteThirdPartyAdmin,
    getOrgListMe,
    addMemberPlatAdmin,
    queryAppCodeByRoleMember,
} from '../uaos';
import { mapState } from 'vuex';

export default {
    name: 'MemberManagement',
    props: {
        activeItem: {
            type: Object,
            default: () => {},
        },
        roleList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            visible: false,
            userOrgId: '',
            formInline: {
                user: [],
                region: '',
                quser: [],
                time: [],
            },
            tableData: [
                // {
                //     userErp: 'liukun118', // 用户
                //     userName: '小小坤', // 用户名称
                //     userOrgCode: '', // 组织编码
                //     userOrgName: '', // 组织名称
                //     // sub: '所属部门',
                //     // ss: '负责部门',
                //     opUserErp: 'caozuoren', // 操作用户
                //     opUserName: '操作人', // 操作用户名
                //     opTime: '2022-11-29',
                // },
            ],
            multipleSelection: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            add: {
                user: [],
                org: '',
                userOrgName: '',
                userOrgId: '',
                checkedCities: [],
            },
            delvisible: false, // 删除弹出框
            checked: false,
            radio: 'yes',
            appList: [],
            checkAll: false,
            isIndeterminate: true,
            appDataShow: false,
        };
    },
    watch: {
        activeItem() {
            this.clearTable();
            this.reset();
            this.search();
        },
        'add.user': {
            handler(newval) {
                if (newval.length) {
                    this.add.org = newval[0].orgTierName;
                    this.getAppByAdmin();
                    this.appDataShow = true;
                } else {
                    this.add.org = '';
                    this.appDataShow = false;
                }
            },
        },
        'formInline.userOrgName': {
            handler(val) {
                if (val === '') {
                    this.userOrgId = '';
                }
            },
        },
    },
    computed: {
        title() {
            return `添加${this.activeItem.name}`;
        },
        deltitle() {
            return `删除${this.activeItem.name}`;
        },
        isShowAdd() {
            const roleCode = `${this.activeItem.appCode}${this.activeItem.code}`;
            const objects = {
                archadmin: 'archAuth:admin:member:add',
                archstoreSelfAdmin: 'archAuth:storeSelfAdmin:member:add',
                archthirdPartyAdmin: 'archAuth:thirdPartyAdmin:member:add',
            };
            const key = objects[roleCode];
            return key ? this.jurisdicList[key] : 3;
            // 'archAuth:superAdmin:member:add'
            // 'archAuth:orgAdmin:member:add'
            // 'archAuth:childrenOrgAdmin:member:add'
        },
        isShowSearch() {
            const roleCode = `${this.activeItem.appCode}${this.activeItem.code}`;
            const objects = {
                archadmin: 'archAuth:admin:member:search',
                archstoreSelfAdmin: 'archAuth:storeSelfAdmin:member:search',
                archthirdPartyAdmin: 'archAuth:thirdPartyAdmin:member:search',
            };
            const key = objects[roleCode];
            return key ? this.jurisdicList[key] : 3;
        },
        isShowDel() {
            const roleCode = `${this.activeItem.appCode}${this.activeItem.code}`;
            const objects = {
                archadmin: 'archAuth:admin:member:del',
                archstoreSelfAdmin: 'archAuth:storeSelfAdmin:member:del',
                archthirdPartyAdmin: 'archAuth:thirdPartyAdmin:member:del',
            };
            const key = objects[roleCode];
            return key ? this.jurisdicList[key] : 3;
        },
        isShow() {
            const roleCode = `${this.activeItem.appCode}${this.activeItem.code}`;
            const objects = {
                archadmin: 'archAuth:admin:member:view',
                archstoreSelfAdmin: 'archAuth:storeSelfAdmin:member:view',
                archthirdPartyAdmin: 'archAuth:thirdPartyAdmin:member:view',
            };
            const key = objects[roleCode];
            return key ? this.jurisdicList[key] : 3;
        },
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    methods: {
        tableRowClassName({ row }) {
            if (row.builtIn) {
                return 'info-row';
            }
            return '';
        },
        selectable(row) {
            return !row.builtIn;
        },
        getKeyObject() {
            return {
                superAdmin: 'sa',
                orgAdmin: 'oa',
                childrenOrgAdmin: 'coa',
            };
        },
        // 删除按钮
        del() {
            if (this.multipleSelection.length === 0) {
                let name = '成员';
                if (this.activeItem.code === 'admin') {
                    name = '平台管理员';
                } else if (this.activeItem.code === 'user') {
                    name = '成员';
                } else if (this.activeItem.code === 'storeSelfAdmin') {
                    name = '自研应用管理员';
                } else if (this.activeItem.code === 'thirdPartyAdmin') {
                    name = '外采应用管理员';
                }
                this.$message({
                    type: 'warning',
                    message: `请选择要删除的${name}`,
                });
            } else {
                this.delvisible = true;
            }
        },
        // 确认删除
        async delEnter() {
            // const roleCode = this.activeItem.code;
            // const roleType = this.getKeyObject()[roleCode] || 'cust';
            const ids = this.multipleSelection.map(item => item.id);
            // console.log('=====del=ids====', ids);
            let data = null;
            if (this.activeItem.code === 'admin') {
                data = await deleteMemberPlatAdmin({ ids });
            } else if (this.activeItem.code === 'storeSelfAdmin') {
                data = await deleteStoreSelfAdmin({ ids });
            } else {
                data = await deleteThirdPartyAdmin({ ids });
            }
            if (data) {
                this.$message({
                    type: 'success',
                    message: '删除成功',
                });
                this.delvisible = false;
                this.search();
            }
        },
        // 重置
        reset() {
            this.formInline = {
                user: [],
                region: '',
                quser: [],
                time: [],
            };
            this.userOrgId = '';
            if (this.$refs.multipleTable) {
                this.$refs.multipleTable.clearSelection();
            }
            this.search();
        },
        clearTable() {
            this.tableData = [];
        },
        // 搜索
        search() {
            if (this.isShowSearch !== 0) {
                return;
            }
            const roleCode = this.activeItem.code;
            const userErps = this.formInline.user.length ? [this.formInline.user[0].erp] : undefined;
            const userOrgCode = this.userOrgId ? this.userOrgId : undefined;
            const opEndDate = this.formInline.time && this.formInline.time.length ? moment(this.formInline.time[1]).format('YYYY-MM-DD') : undefined;
            const opStartDate = this.formInline.time && this.formInline.time.length ? moment(this.formInline.time[0]).format('YYYY-MM-DD') : undefined;
            const opUserErps = this.formInline.quser.length ? [this.formInline.quser[0].erp] : undefined;
            const page = {
                index: this.currentPage, // 当前页
                size: this.pageSize, // 每页多少
            };
            this.getroleList({
                roleCode, // 角色编码
                userErps, // 用户Erp
                userOrgCode, // 用户组织编码
                opEndDate, // 操作结束时间
                opStartDate, // 操作开始时间
                opUserErps, // 操作用户Erp
                page, // 分页
            });
        },
        // onSubmit() {},
        save(data) {
            // const roleCode = this.activeItem.code;
            // const roleType = this.getKeyObject()[roleCode] || 'cust';
            if (this.add.user.length === 0) {
                this.$message({
                    type: 'warning',
                    message: '请输入姓名',
                });
                return;
            }
            // const orgCode = this.add.userOrgId;
            // const orgCode = this.add.userOrgId;
            const userErp = this.add.user[0].erp;
            if (data && this.activeItem.code === 'admin') {
                if (data && this.radio === 'yes' && this.add.checkedCities && this.add.checkedCities.length <= 0) {
                    this.$message({
                        type: 'warning',
                        message: '请选择应用',
                    });
                    return;
                }
                if (this.radio === 'no') {
                    this.add.checkedCities = [];
                }
                const appRoles = [];
                this.add.checkedCities.forEach((item) => {
                    appRoles.push({
                        appCode: item,
                        roleCode: 'admin',
                    });
                });
                addMemberPlatAdmin({
                    userErp,
                    appRoles,
                }).then(() => {
                    this.$message({
                        type: 'success',
                        message: '添加成功',
                    });
                    this.search();
                    // 保持并继续
                    if (this.checked) {
                        this.clearVal();
                    } else {
                        this.close();
                    }
                });
            } else if (!data && this.activeItem.code === 'storeSelfAdmin') {
                addStoreSelfAdmin({
                    roleCode: 'storeSelfAdmin',
                    appCode: 'arch',
                    userErp,
                }).then(() => {
                    this.$message({
                        type: 'success',
                        message: '添加成功',
                    });
                    this.search();
                    // 保持并继续
                    if (this.checked) {
                        this.clearVal();
                    } else {
                        this.close();
                    }
                });
            } else {
                addThirdPartyAdmin({
                    roleCode: 'thirdPartyAdmin',
                    appCode: 'arch',
                    userErp,
                }).then(() => {
                    this.$message({
                        type: 'success',
                        message: '添加成功',
                    });
                    this.search();
                    // 保持并继续
                    if (this.checked) {
                        this.clearVal();
                    } else {
                        this.close();
                    }
                });
            }
        },
        // 清除弹出层数据
        clearVal() {
            this.add = {
                user: [],
                org: '',
                userOrgName: '',
                userOrgId: '',
                checkedCities: [],
            };
            this.checkAll = false;
        },
        // 添加人员addMember
        addmember() {
            // console.log(addMember);
            this.visible = true;
        },
        getAppByAdmin() {
            AdminOption.queryAppList().then((response) => {
                const userErp = this.add.user[0].erp;
                this.add.checkedCities = [];
                queryAppCodeByRoleMember({ userErp, roleCode: 'admin' }).then((res) => {
                    const data = [];
                    res.forEach((item) => {
                        data.push(item.appCode);
                    });
                    this.appList = response;
                    this.appList.forEach((item) => {
                        if (data.includes(item.code)) {
                            // item.checked = true;
                            this.add.checkedCities.push(item.code);
                        } else {
                            // item.checked = false;
                        }
                    });
                });
            });
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.search();
        },
        close(type) {
            if (type === 1) {
                this.delvisible = false;
            } else {
                this.visible = false;
                this.clearVal();
            }
        },
        onClickChangePage(index) {
            this.currentPage = index;
            this.search();
        },
        // getOrgListMe
        loadOrglistOwne(orgInfo, callback) {
            if (orgInfo) {
                getOrgListMe({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data);
                });
            } else {
                // 清空原部门信息
                this.add.userOrgName = '';
                this.add.userOrgId = '';
            }
        },
        // 加载部门列表
        loadOrglist(orgInfo, callback) {
            if (orgInfo) {
                AdminOption.getOrgList({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data.records);
                });
            } else {
                // 清空原部门信息
                this.userOrgId = '';
                this.formInline.userOrgName = '';
            }
        },
        handleSelect(data) {
            console.log('==========handleSelect===========', data);
            this.userOrgId = data.id;
            this.formInline.userOrgName = data.fullName;
        },
        handleSelectMe(data) {
            console.log('==========handleSelectMe===========', data);
            this.add.userOrgId = data.code;
            this.add.userOrgName = data.fullName;
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        getroleList(code) {
            const {
                roleCode, // 角色编码
                userErps, // 用户Erp
                userOrgCode, // 用户组织编码
                opEndDate, // 操作结束时间
                opStartDate, // 操作开始时间
                opUserErps, // 操作用户Erp
                page = {
                    index: this.currentPage, // 当前页
                    size: this.pageSize, // 每页多少
                },
            } = code;
            getroleListByroleCode({
                roleCode,
                userErps,
                userOrgCode,
                opEndDate,
                opStartDate,
                opUserErps,
                page,
                appCode: 'arch',
            }).then((res) => {
                this.tableData = res.records;
                this.total = res.total;
            });
        },
        handleCheckAllChange(val) {
            const data = [];
            this.appList.forEach((item) => {
                if (!item.disabled) {
                    data.push(item.code);
                }
            });
            this.add.checkedCities = val ? data : [];
            // if (val) {
            //     this.$set(this.add, 'checkedCities', data);
            // } else {
            //     this.$set(this.add, 'checkedCities', []);
            // }
            this.isIndeterminate = false;
        },
        handleCheckedCitiesChange(value) {
            const data = [];
            this.appList.forEach((item) => {
                data.push(item.code);
            });
            const checkedCount = value.length;
            this.checkAll = checkedCount === data.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < data.length;
        },
    },
    mounted() {
        // console.log(this.activeItem, '====activeItem====');
    },
};
</script>
<style lang="less">
.member-management{
    .menber-managment-dialog{
        .el-dialog__header{
            border-bottom: 1px solid #EBEEF5;
        }
    }
    .info-row{
        background-color: #f0f2f5;
    }
}
</style>
<style lang="less" scoped>
.member-management{
    display: flex;
    flex-direction: column;
    .search{

    }
    .el-form-item__content{
        width: 300px;
    }
    .userorgname{
        width: 248px;
        .el-autocomplete{
            width: 100%;
        }
    }
    .footer{
        height: 30px;
        margin-top: 20px;
        text-align: right;
    }
}
</style>
