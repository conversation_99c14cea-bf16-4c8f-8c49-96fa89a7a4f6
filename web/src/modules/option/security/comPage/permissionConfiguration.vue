<template>
    <div class="permission-configuration">
        <el-form>
            <el-form-item>
                <el-row>
                    <el-col :span="4">
                        <span>模块</span>
                    </el-col>
                    <el-col :span="4">
                        <span>访问权限</span>
                    </el-col>
                    <el-col :span="16">
                        <span>操作权限</span>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item v-if="flat">
                <span>平台</span>
            </el-form-item>
            <el-form-item
                :key="item.moduleName"
                v-for="item in list.filter(item => item.category === 'plat')"
            >
                <el-row>
                    <el-col :span="4">
                        <span class="titles">{{ item.moduleName }}</span>
                    </el-col>
                    <el-col :span="4">
                        <el-checkbox-group
                            @change="itemChange"
                            v-model="item.checkList"
                        >
                            <el-checkbox
                                :label="items.code"
                                :key="items.code"
                                v-for="items in item.view"
                                :disabled="islook"
                            >
                                <template>
                                    {{ items.name }}
                                    <el-tooltip
                                        v-if="items.description"
                                        class="item"
                                        effect="dark"
                                        :content="items.description"
                                        placement="top">
                                        <i
                                            class="jacp-icon-help"
                                        />
                                    </el-tooltip>
                                </template>
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-col>
                    <el-col :span="16">
                        <el-checkbox-group
                            @change="itemChange1"
                            v-model="item.checkList"
                        >
                            <el-checkbox
                                :label="items.code"
                                :key="items.code"
                                v-for="items in item.noView"
                                :disabled="islook"
                            >
                                <template>
                                    {{ items.name }}
                                    <el-tooltip
                                        v-if="items.description"
                                        class="item"
                                        effect="dark"
                                        :content="items.description"
                                        placement="top">
                                        <i
                                            class="jacp-icon-help"
                                        />
                                    </el-tooltip>
                                </template>
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item v-if="appStore">
                <span>应用商店</span>
            </el-form-item>
            <el-form-item
                :key="item.moduleName"
                v-for="item in list.filter(item => item.category === 'appStore')"
            >
                <el-row>
                    <el-col :span="4">
                        <span class="titles">{{ item.moduleName }}</span>
                    </el-col>
                    <el-col :span="4">
                        <el-checkbox-group
                            @change="itemChange"
                            v-model="item.checkList"
                        >
                            <el-checkbox
                                :label="items.code"
                                :key="items.code"
                                v-for="items in item.view"
                                :disabled="islook"
                            >
                                <template>
                                    {{ items.name }}
                                    <el-tooltip
                                        v-if="items.description"
                                        class="item"
                                        effect="dark"
                                        :content="items.description"
                                        placement="top">
                                        <i
                                            class="jacp-icon-help"
                                        />
                                    </el-tooltip>
                                </template>
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-col>
                    <el-col :span="16">
                        <el-checkbox-group
                            @change="itemChange1"
                            v-model="item.checkList"
                        >
                            <el-checkbox
                                :label="items.code"
                                :key="items.code"
                                v-for="items in item.noView"
                                :disabled="islook"
                            >
                                <template>
                                    {{ items.name }}
                                    <el-tooltip
                                        v-if="items.description"
                                        class="item"
                                        effect="dark"
                                        :content="items.description"
                                        placement="top">
                                        <i
                                            class="jacp-icon-help"
                                        />
                                    </el-tooltip>
                                </template>
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-col>
                </el-row>
            </el-form-item>
        </el-form>
        <div class="footer" v-if="isShowEdit === 0">
            <el-button type="primary" v-if="islook && !isAdmin" @click="islook = false">编辑</el-button>
            <el-button v-if="!islook" @click="cancel">取消</el-button>
            <el-button type="primary" v-if="!islook" @click="save">保存</el-button>
        </div>
        <div class="footer" v-else-if="isShowEdit === 1">
            <el-tooltip
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    type="primary"
                    style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;">编辑</el-button>
            </el-tooltip>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { getRoleQueryList, postRole } from '../uaos';

export default {
    name: 'permissionConfiguration',
    props: {
        activeItem: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            list: [],
            islook: true,
            flat: false,
            appStore: false,
            isAdmin: false,
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
        isShowEdit() {
            const roleCode = `${this.activeItem.appCode}${this.activeItem.code}`;
            const objects = {
                // archAuth:superAdmin:perm:config:update
                archadmin: 'archAuth:admin:perm:config:update',
                archuser: 'archAuth:user:perm:config:update',
                archstoreSelfAdmin: 'archAuth:storeSelfAdmin:perm:config:update',
                archthirdPartyAdmin: 'archAuth:thirdPartyAdmin:perm:config:update',
            };
            const key = objects[roleCode];
            return key ? this.jurisdicList[key] : 3;
            // 'archAuth:superAdmin:member:add'
            // 'archAuth:orgAdmin:member:add'
            // 'archAuth:childrenOrgAdmin:member:add'
        },
    },
    watch: {
        activeItem(val) {
            this.search();
            this.islook = true;
            if (val.code === 'admin') {
                this.isAdmin = true;
            } else {
                this.isAdmin = false;
            }
        },
    },
    methods: {
        search() {
            const roleCode = this.activeItem.code;
            getRoleQueryList({
                roleCode,
            }).then((res) => {
                res.forEach((item) => {
                    if (item.moduleName === '通用配置管理') {
                        item.permissions = item.permissions.filter(i => !(i.name === '管理产品动向' || i.name === '管理版本说明'));
                    }
                    item.checkList = [];
                    item.permissions.forEach((i) => {
                        if (i.checked) {
                            item.checkList.push(i.code);
                        }
                    });
                    item.view = [];
                    item.noView = [];
                    item.permissions.forEach((response) => {
                        const data = response.code.split(':');
                        if (data[data.length - 1] === 'view') {
                            item.view.push(response);
                        } else {
                            item.noView.push(response);
                        }
                    });
                    // checked
                });
                const { message, license } = this.$store.state.app.serviveConfig;
                if (!message) {
                    res = res.filter(i => i.moduleName !== '消息管理');
                }
                if (!license) {
                    res = res.filter(i => i.moduleName !== '授权管理');
                }
                res = res.filter(i => i.moduleName !== '任务中心');
                this.list = res;
                this.flat = this.list.some(item => item.category === 'plat');
                this.appStore = this.list.some(item => item.category === 'appStore');
            });
        },
        itemChange(selection) {
            // if (selection.length) {
            //     if (selection.toString().indexOf(':view') === -1) {
            //         selection.push(`${selection[0].split(':')[0]}:view`);
            //     }
            // }
            if (selection.length) {
                if (selection.toString().indexOf(':view') === -1) {
                    // selection.push(`${selection[0].split(':')[0]}:view`);
                    selection.splice(0, selection.length);
                }
            }
        },
        itemChange1(selection) {
            // if (selection.length) {
            //     if (selection.toString().indexOf(':view') === -1) {
            //         selection.push(`${selection[0].split(':')[0]}:view`);
            //     }
            // }
            if (selection.length === 1) {
                if (selection.toString().indexOf(':view') === -1) {
                    selection.push(`${selection[0].split(':')[0]}:view`);
                    // selection.pop();
                }
            }
        },
        save() {
            const roleCode = this.activeItem.code;
            const permissions = [];
            this.list.forEach((item) => {
                permissions.push(...item.checkList);
            });
            // if (permissions.length === 0) {
            //     this.$message({
            //         type: 'warning',
            //         message: '请选择你要设置的权限',
            //     });
            //     return;
            // }
            this.$confirm('保存后，系统将获取最新的规则进行权限控制，确定保存吗？', '提示').then(() => {
                postRole({
                    permissions,
                    roleCode,
                }).then(() => {
                    this.islook = true;
                    this.search();
                });
            });
        },
        cancel() {
            this.$confirm('取消后，您所编辑的内容都会清空（非重置），确定取消吗？', '提示').then(() => {
                this.islook = true;
                this.search();
            });
        },
    },
};
</script>

<style lang="less" scoped>
.permission-configuration{
    .titles{
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: normal;
        color: rgba(144,147,153,1);
    }
    .footer{
        margin-top: 150px;
    }
}
</style>
