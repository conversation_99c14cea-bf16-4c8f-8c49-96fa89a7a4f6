import {
    apiGateway,
} from '@/plugins/http';

const roleQuery = params => apiGateway.post('uaos/api/v1/auth/role/query', params);
const getroleListByroleCode = params => apiGateway.post('uaos/api/v1/auth/role/member/query', params);
const addMember = params => apiGateway.post('uaos/api/v1/auth/role/member/addMember', params);
const addMemberPlatAdmin = params => apiGateway.post('uaos/api/v1/auth/role/member/addMemberPlatAdmin', params);
const queryAppCodeByRoleMember = params => apiGateway.get('uaos/api/v1/auth/role/member/queryAppCodeByRoleMember', { params });
const addStoreSelfAdmin = params => apiGateway.post('uaos/api/v1/auth/role/member/addStoreSelfAdmin', params);
const addThirdPartyAdmin = params => apiGateway.post('uaos/api/v1/auth/role/member/addThirdPartyAdmin', params);
const delMemberByIds = params => apiGateway.delete('uaos/api/v1/auth/role/member/deleteRoleMember', { data: params });
const deleteMemberPlatAdmin = params => apiGateway.delete('uaos/api/v1/auth/role/member/deleteMemberPlatAdmin', { data: params });
const deleteStoreSelfAdmin = params => apiGateway.delete('uaos/api/v1/auth/role/member/deleteStoreSelfAdmin', { data: params });
const deleteThirdPartyAdmin = params => apiGateway.delete('uaos/api/v1/auth/role/member/deleteThirdPartyAdmin', { data: params });
const deleteMemberAppAdmin = params => apiGateway.delete('uaos/api/v1/auth/role/member/deleteMemberAppAdmin', { data: params });
const addRoleMemberAppAdmin = params => apiGateway.post('uaos/api/v1/auth/role/member/addRoleMemberAppAdmin', params);
// 根据角色获取权限项列表
const getRoleQueryList = params => apiGateway.get('uaos/api/v1/auth/role/permission/matrix', { params });
// 设置角色权限
const postRole = params => apiGateway.post('uaos/api/v1/auth/role/permission/reset', params);
// 模糊查询部门信息
const getOrgListMe = where => apiGateway.get('uaos/api/v1/org/autoComplete/owned', { params: where });
// 获取应用管理员设置页面数据
const getRoleMemberByAppAdmin = params => apiGateway.get('uaos/api/v1/auth/role/member/queryRoleMemberByAppAdmin', params);
const queryRoleMemberAppAdmin = params => apiGateway.get('uaos/api/v1/auth/role/member/queryRoleMemberAppAdmin', params);
// 删除应用管理员

// 新增部门管理员
const addOrgAdmin = params => apiGateway.post('uaos/api/v1/auth/role/member/addOrgAdmin', params);

// 查询部门管理员
const queryOrgAdmin = params => apiGateway.post('uaos/api/v1/auth/role/member/queryOrgAdmin', params);
// 删除部门管理员
const deleteOrgAdmin = params => apiGateway.delete('uaos/api/v1/auth/role/member/deleteOrgAdmin', { data: params });

export {
    roleQuery,
    getroleListByroleCode,
    addMember,
    addMemberPlatAdmin,
    addStoreSelfAdmin,
    addThirdPartyAdmin,
    delMemberByIds,
    getRoleQueryList,
    postRole,
    getOrgListMe,
    getRoleMemberByAppAdmin,
    deleteMemberAppAdmin,
    queryAppCodeByRoleMember,
    deleteMemberPlatAdmin,
    deleteStoreSelfAdmin,
    deleteThirdPartyAdmin,
    addRoleMemberAppAdmin,
    queryRoleMemberAppAdmin,
    addOrgAdmin,
    queryOrgAdmin,
    deleteOrgAdmin,
};
