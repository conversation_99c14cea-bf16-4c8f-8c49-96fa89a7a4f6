<template>
    <el-container class="org-manage" >
        <el-aside width="300px">
            <el-tree
                :data="orgData"
                node-key="code"
                default-expand-all
                :expand-on-click-node="true"
                @current-change="selectOrg"
                ref="tree"
                :props="defaultProps"
            >
                <span slot-scope="{ node }">
                    <span>{{ node.label }}</span>
                </span>
            </el-tree>
        </el-aside>
        <el-main>
            <el-dialog
                title="添加子部门"
                :close-on-click-modal="false"
                :visible.sync="orgAddDlgVisible"
            >
                <el-form
                    :model="orgAddForm"
                    ref="orgAddForm"
                >
                    <el-form-item
                        label="部门名称"
                        prop="name"
                        :rules="[
                            { pattern: /^((?![\\-]).)*$/, message: '部门名称不能含有-'}
                        ]"
                    >
                        <el-input
                            v-model="orgAddForm.name"
                        />
                    </el-form-item>
                    <el-form-item label="父部门编码">
                        <el-input
                            v-model="orgAddForm.parentCode"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="机构负责人">
                        <el-input v-model="orgAddForm.leaderErp" />
                    </el-form-item>
                </el-form>
                <div
                    slot="footer"
                    class="dialog-footer"
                >
                    <el-button @click="orgAddDlgVisible = false">取 消</el-button>
                    <el-button
                        type="primary"
                        @click="addOrg"
                        :loading="isLoading"
                    >
                        确 定
                    </el-button>
                </div>
            </el-dialog>
            <div v-show="orgUpdateFromVisible">
                <el-row>
                    <el-button
                        type="primary"
                        @click="updateOrg"
                        v-if="jurisdicList['archOrg:org:add'] === 0"
                    >
                        保存
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archOrg:org:add'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top"
                    >
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            保存
                        </el-button>
                    </el-tooltip>
                    <el-button
                        @click="removeOrg"
                        :disabled="orgUpdateForm.level <= 1"
                        v-if="jurisdicList['archOrg:org:del'] === 0">
                        删除
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archOrg:org:del'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top"
                    >
                        <el-button
                            style="color: #c0c4cc;
                            cursor: not-allowed;
                            background-image: none;
                            background-color: #fff;
                            border-color: #ebeef5;"
                        >
                            删除
                        </el-button>
                    </el-tooltip>
                    <el-button
                        @click="showOrgAddDlg"
                        v-if="jurisdicList['archOrg:org:addChildOrg'] === 0">
                        添加子部门
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archOrg:org:addChildOrg'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top"
                    >
                        <el-button
                            style="color: #c0c4cc;
                            cursor: not-allowed;
                            background-image: none;
                            background-color: #fff;
                            border-color: #ebeef5;"
                        >
                            添加子部门
                        </el-button>
                    </el-tooltip>
                    <!-- <el-button disabled>
                        导入
                    </el-button>
                    <el-button disabled>
                        导出
                    </el-button> -->
                </el-row>
                <el-form :model="orgUpdateForm">
                    <el-form-item label="部门编码">
                        <el-input
                            v-model="orgUpdateForm.code"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="部门名称">
                        <el-input
                            v-model="orgUpdateForm.name"
                        />
                    </el-form-item>
                    <el-form-item label="层级">
                        <el-input
                            v-model="orgUpdateForm.level"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="全路径编码">
                        <el-input
                            v-model="orgUpdateForm.fullPath"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="全路径名称">
                        <el-input
                            v-model="orgUpdateForm.fullName"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="父部门编码">
                        <el-input
                            v-model="orgUpdateForm.parentCode"
                            disabled
                        />
                    </el-form-item>
                    <el-form-item label="代码库分组">
                        <el-input
                            v-model="orgUpdateForm.codeGroup"
                            placeholder="请输入代码库分组名称(数字、字母组成)..."
                        />
                    </el-form-item>
                    <el-form-item label="">
                        <div style="color: #606265;font-size: 12px;">
                            应用域负责人
                        </div>
                        <jacp-input-users
                            class="userorgname"
                            v-model="orgUpdateForm.auditors"
                            placeholder="请输入姓名"
                            prefix-icon="el-icon-search"
                        />
                    </el-form-item>
                    <el-form-item label="机构负责人">
                        <el-input v-model="orgUpdateForm.leaderErp" />
                    </el-form-item>
                </el-form>
            </div>
        </el-main>
    </el-container>
</template>
<script type="text/javascript">
import { apiGateway } from '@/plugins/http';
import { mapState } from 'vuex';
import { User } from '@/models/user.js'

/**
 * archOrg:org:add 新增
   archOrg:org:del 删除
   archOrg:org:addChildOrg 添加子部门
 */
export default {
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    data() {
        return {
            orgData: [],
            orgAddDlgVisible: false,
            orgUpdateFromVisible: false,
            isLoading: false,
            orgAddForm: {
                name: '',
                parentCode: '',
                leaderErp: '',
            },
            orgUpdateForm: {
                code: '',
                name: '',
                level: 1,
                parentCode: '',
                fullPath: '',
                fullName: '',
                leaderErp: '',
                auditors: [],
                codeGroup: '',
            },
            defaultProps: {
                children: 'children',
                label: 'name',
            },
        };
    },
    mounted() {
        apiGateway
            .get('uaos/api/v1/org/level/0')
            .then((data) => {
                this.orgData = this.listToTreeList(data);
            });
    },
    methods: {
        listToTreeList(data) {
            const map = {};
            data.forEach((item) => {
                map[item.code] = item;
            });
            const val = [];
            data.forEach((item) => {
                const pnode = map[item.parentCode];
                if (pnode) {
                    (pnode.children || (pnode.children = [])).push(item);
                } else {
                    val.push(item);
                }
            });
            return val;
        },
        currentNode() {
            return this.$refs.tree.getCurrentNode();
        },
        showOrgAddDlg() {
            const node1 = this.$refs.tree.getNode(this.$refs.tree.getCurrentKey());
            const children = node1.parent.childNodes;
            if (!children.some(d => d.data.code === node1.data.code)) {
                return this.$message.error('请选择要添加的组织！');
            }
            this.orgAddDlgVisible = true;
            const node = this.currentNode();
            Object.assign(this.orgAddForm, {
                name: node.name,
                parentCode: node.code,
                level: node.level + 1,
            });
            return true;
        },
        removeOrg() {
            const { code } = this.currentNode();
            if (!code) {
                return this.$message.error('请选择要删除的组织！');
            }
            const codeVal = +code;
            if (codeVal === 1) {
                return this.$message.error('根组织结构不允许删除！');
            }
            const node = this.$refs.tree.getNode(this.$refs.tree.getCurrentKey());
            const children = node.parent.childNodes;
            if (!children.some(d => d.data.code === node.data.code)) {
                return this.$message.error('请选择要删除的组织！');
            }
            return this.$confirm('是否需要删除?').then(() => {
                apiGateway.delete(`uaos/api/v1/org/${code}`).then(() => {
                    this.$message('删除成功。');
                    children.splice(children.findIndex(d => d.data.code === node.data.code), 1);
                    return true;
                });
            });
        },
        addOrg() {
            this.$refs.orgAddForm.validate((valid) => {
                if (!valid || this.isLoading) {
                    return;
                }
                this.isLoading = true;
                apiGateway
                    .post('uaos/api/v1/org', this.orgAddForm)
                    .then((data) => {
                        this.orgAddDlgVisible = false;
                        this.$message.success('添加成功');
                        this.currentNode().children.push(
                            Object.assign(
                                {
                                    children: [],
                                },
                                data,
                            ),
                        );
                        setTimeout(() => {
                            this.isLoading = false;
                        }, 1000);
                    });
            });
        },
        updateOrg() {
            this.orgUpdateForm.auditors = JSON.stringify(this.orgUpdateForm.auditors);
            apiGateway
                .post('uaos/api/v1/org', this.orgUpdateForm)
                .then((data) => {
                    this.$message('保存成功');
                    Object.assign(this.currentNode(), data);
                    this.orgUpdateForm.auditors = JSON.parse(data.auditors);
                    User.getInfo(false)
                });
        },
        selectOrg(node) {
            apiGateway
                .get(`uaos/api/v1/org/children?code=${node.code}`)
                .then((data) => {
                    this.$set(node, 'children', data);
                });
            Object.assign(this.orgUpdateForm, {
                code: node.code,
                name: node.name,
                parentCode: node.parentCode,
                level: node.level,
                fullPath: node.fullPath,
                fullName: node.fullName,
                leaderErp: node.leaderErp,
                auditors: JSON.parse(node.auditors),
                codeGroup: node.codeGroup,
            });
            this.orgUpdateFromVisible = true;
        },
    },
};
</script>
<style lang="less">
.org-manage {
    height: calc(~'100vh - 140px');
    .el-tree {
        .el-tree-node .el-tree-node__content{
            height: 40px;
            background: rgb(235, 235, 235);
            margin: 1px 0;
            color: #000;
        }
        .is-current>.el-tree-node__content{
            background: rgb(146, 220, 221);
        }
        .el-tree-node__children .el-tree-node__content{
            color: #000;
        }
    }
}
</style>
