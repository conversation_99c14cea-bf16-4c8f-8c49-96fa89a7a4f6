<template>
    <div class="operateLog" v-if="jurisdicList['archOrgAdmin:member:view'] === 0">
        <div class="header">
            <h4>部门管理员添加</h4>
        </div>
        <div class="search">
            <el-form
                :inline="true"
                :model="searchData"
                class="demo-form-inline"
            >
                <el-form-item label="负责部门">
                    <div>
                        <el-autocomplete
                            v-model="addData.userOrgName"
                            :fetch-suggestions="loadOrglist"
                            placeholder="请输入部门关键字"
                            :trigger-on-focus="false"
                            value-key="fullName"
                            label="fullName"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="管理员">
                    <jacp-input-users
                        class="userorgname"
                        :max-count="1"
                        v-model="searchData.admin"
                        placeholder="请输入管理员"
                        prefix-icon="el-icon-search"
                    />
                </el-form-item>
                <el-form-item label="操作人">
                    <jacp-input-users
                        class="userorgname"
                        :max-count="1"
                        v-model="searchData.operator"
                        placeholder="请输入操作人"
                        prefix-icon="el-icon-search"
                    />
                </el-form-item>
                <el-form-item label="操作时间">
                    <el-date-picker
                        v-model="searchData.operateTime"
                        type="daterange"
                        range-separator="-"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        @click="onSubmit"
                        v-if="jurisdicList['archOrgAdmin:member:search'] === 0"
                    >
                        查询
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archOrgAdmin:member:search'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            查询
                        </el-button>
                    </el-tooltip>
                    <el-button
                        type="primary"
                        @click="Add"
                        v-if="jurisdicList['archOrgAdmin:member:add'] === 0"
                    >
                        新增
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archOrgAdmin:member:add'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            新增
                        </el-button>
                    </el-tooltip>
                    <el-button
                        type="danger"
                        @click="deleteAll"
                        v-if="jurisdicList['archOrgAdmin:member:del'] === 0"
                    >
                        批量删除
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archOrgAdmin:member:del'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            批量删除
                        </el-button>
                    </el-tooltip>
                </el-form-item>
            </el-form>
        </div>
        <div class="table">
            <el-table
                :data="tableData"
                style="width: 100%"
                tooltip-effect="dark"
            >
                <el-table-column
                    type="selection"
                    width="55"
                />
                <el-table-column
                    prop="userOrgName"
                    label="负责部门"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="orgName"
                    label="所属部门"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="admin"
                    label="管理员"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="operator"
                    label="操作人"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="operateTime"
                    label="操作时间"
                    show-overflow-tooltip
                />
            </el-table>
        </div>
        <div class="footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="init"
                @current-change="init"
                :page-size.sync="pagesize"
                :total="total"
                :current-page.sync="currentPage"
                layout="prev, pager, next, total, sizes, jumper"
            />
        </div>
        <div class="dialog">
            <el-dialog
                title="添加部门管理员"
                :visible.sync="addOrgAdmin"
                width="30%"
                :close-on-click-modal="false"
            >
                <el-form
                    :model="addData"
                    ref="addOrg"
                    :rules="rules"
                    label-width="100px"
                    class="demo-ruleForm"
                >
                    <el-form-item
                        label="姓名："
                        prop="name"
                    >
                        <jacp-input-users
                            class="userorgname"
                            :max-count="1"
                            v-model="addData.name"
                            placeholder="请输入管理员"
                            prefix-icon="el-icon-search"
                            @input="getUserAdd"
                        />
                    </el-form-item>
                    <el-form-item
                        label="所属部门："
                        prop="orgName"
                    >
                        <span v-if="addData.name && addData.name.length <= 0">暂无数据，请先输入管理员姓名~</span>
                        <span v-else>{{ addData.orgName }}</span>
                    </el-form-item>
                    <el-form-item
                        label="负责部门："
                        prop="userOrgName"
                    >
                        <el-autocomplete
                            style="width: 100%"
                            class="userorgname"
                            v-model="addData.userOrgName"
                            :fetch-suggestions="loadOrglist"
                            placeholder="请输入部门关键字"
                            :trigger-on-focus="false"
                            value-key="fullName"
                            label="fullName"
                        />
                    </el-form-item>
                </el-form>
                <span
                    slot="footer"
                    class="dialog-footer">
                    <div class="footer__left">
                        <el-checkbox v-model="checked">继续创建</el-checkbox>
                    </div>
                    <div class="footer__right">
                        <el-button @click="DiaCancle">取 消</el-button>
                        <el-button
                            type="primary"
                            @click="DiaSave"
                        >保 存</el-button>
                    </div>
                </span>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import AdminOption from '@/models/adminOption';
import { mapState } from 'vuex';

export default {

    data() {
        return {
            searchData: {
            },
            tableData: [],
            currentPage: 1,
            pagesize: 5,
            total: 0,
            addOrgAdmin: false,
            addData: {},
            checked: false,
            rules: {
                name: [{ required: true, message: '请输入姓名', trigger: ['blur', 'change'] }],
                userOrgName: [{ required: true, message: '请选择部门', trigger: ['blur', 'change'] }],
            },
        };
    },
    mounted() {
        this.init();
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    methods: {
        onSubmit() {
        },
        init() {

        },
        deleteAll() {

        },
        Add() {
            this.addOrgAdmin = true;
        },
        loadOrglist(orgInfo, callback) {
            if (orgInfo) {
                AdminOption.getOrgList({
                    keyWord: orgInfo.trim().replace(/\s+/g, ','),
                }).then((data) => {
                    callback(data.records);
                });
            } else {
                // 清空原部门信息
                // this.userOrgId = '';
                this.searchData.userOrgName = '';
                this.addData.userOrgName = '';
            }
        },
        DiaCancle() {
            this.$confirm('取消后，您所编辑的内容都会情况（非重置），确定取消吗？', '提示').then(() => {
                this.addOrgAdmin = false;
                this.addData = {
                    name: [],
                    orgName: '',
                    userOrgName: '',
                };
                this.$refs.addOrg.resetFields();
            });
        },
        DiaSave() {
            this.$refs.addOrg.validate((valid) => {
                console.log(valid);
                // if (!valid) {
                //     return;
                // }
            });
        },
        getUserAdd(data) {
            if (data.length) {
                this.addData.orgName = data[0].orgTierName;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.operateLog {
    margin-left: 24px;
    margin-top: 24px;
    .table {
        .el-table {
            height: calc(100vh - 200px);
        }
    }
    .footer {
        .j-pagination {
            text-align: right;
        }
    }
    .dialog-footer {
        display: flex;
        justify-content: space-between;
    }
}
</style>
