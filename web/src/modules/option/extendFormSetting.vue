<template>
    <div class="extendFormLayout" v-if="jurisdicList['archForm:view'] === 0">
        <div class="extendFormLayout__header">
            <p class="title">全部扩展字段（{{ total }}）</p>
            <el-select
                v-model="currentAppId"
                placeholder="选择应用"
                clearable
            >
                <el-option
                    v-for="item in appModuleList"
                    :key="item.appId"
                    :label="item.name"
                    :value="item.appId"
                />
            </el-select>
            <el-checkbox v-model="onlyCreator">
                只看我创建的
            </el-checkbox>
            <el-button
                class="extendFormLayout__addForm"
                icon="el-icon-plus"
                circle
                alt="新增扩展表单"
                type="primary"
                size="mini"
                @click="handleCreate"
                v-if="jurisdicList['archForm:add'] === 0"
            />
            <el-tooltip
                v-if="jurisdicList['archForm:add'] === 1"
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    class="extendFormLayout__addForm"
                    icon="el-icon-plus"
                    circle
                    alt="新增扩展表单"
                    type="primary"
                    size="mini"
                    style="color: #fff;
                        background-color: #93caf8;
                        border-color: #93caf8;
                        cursor: not-allowed;
                    "
                />
            </el-tooltip>
        </div>
        <el-table
            class="extendFormTable"
            :data="formList"
            :header-cell-style="{ background: '#F7F8F9', color: '#303133'}"
            @row-click="handleEdit"
        >
            <el-table-column
                prop="appName"
                min-width="157"
                label="应用"
                align="left"
            />
            <el-table-column
                prop="name"
                label="扩展表单"
                min-width="200"
                align="left"
            />
            <el-table-column
                prop="version"
                label="最新版本号"
                min-width="100"
                align="left"
            />
            <el-table-column
                prop="creator.name"
                label="创建人"
                min-width="120"
                align="left"
            />
            <el-table-column
                prop="createTime"
                label="创建时间"
                min-width="160"
                align="left"
            />
            <el-table-column
                prop="createTime"
                label="更新时间"
                min-width="160"
                align="left"
            />
            <el-table-column
                label="操作"
                align="right"
                min-width="200"
            >
                <template slot-scope="scope">
                    <el-button
                        v-if="jurisdicList['archForm:copy'] === 0"
                        type="text"
                        size="mini"
                        @click.stop="handleCopy(scope.row)"
                    >
                        复制
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archForm:copy'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="text"
                            size="mini"
                            style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                        >复制</el-button>
                    </el-tooltip>
                    <template v-if="jurisdicList['archForm:update'] === 0">
                        <el-button
                            type="text"
                            size="mini"
                            v-if="scope.row.creator"
                            :disabled="scope.row.creator.erp !== $store.state.user.erp ? true : false"
                            @click.stop="handleEdit(scope.row)"
                        >
                            编辑表单
                        </el-button>
                    </template>
                    <template v-else-if="jurisdicList['archForm:update'] === 1">
                        <el-tooltip
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="text"
                                size="mini"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >编辑表单</el-button>
                        </el-tooltip>
                    </template>
                    <template v-if="jurisdicList['archForm:del'] === 0">
                        <el-button
                            class="el-button-delete"
                            type="text"
                            size="mini"
                            v-if="scope.row.creator"
                            :disabled="scope.row.creator.erp !== $store.state.user.erp ? true : false"
                            @click.stop="handleDelete(scope.row)"
                        >
                            删除
                        </el-button>
                    </template>
                    <template v-else-if="jurisdicList['archForm:del'] === 1">
                        <el-tooltip
                            effect="dark"
                            :content="tips"
                            placement="top">
                            <el-button
                                type="text"
                                size="mini"
                                style="color: #c0c4cc;cursor: not-allowed;background-image: none;"
                            >删除</el-button>
                        </el-tooltip>
                    </template>
                </template>
            </el-table-column>
        </el-table>
        <div class="extendFormLayout__footer">
            <el-pagination
                class="j-pagination"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :page-size.sync="pagesize"
                :total="total"
                :current-page.sync="currentPage"
                layout="prev, pager, next, total, sizes, jumper"
            />
        </div>
        <el-dialog
            :title="title"
            :visible.sync="dialogFormVisible"
            width="502px"
            :show-close="false"
            :close-on-click-modal="false"
        >
            <add-self-def-form
                v-if="dialogFormVisible"
                @closeDialog="closeDialog"
                @reRender="getFormList"
                :is-create="isCreate"
                :schema="targetSchema"
                :app-items="appModuleList"
                :org-list="orgList"
                :is-disabled="!isCreate"
                @reset="targetSchema = {}"
            />
        </el-dialog>
    </div>
</template>

<script>
import FormOption from '@/models/formOption';
import AppModule from '@/modules/root/models/appModule';
import DynamicFormModel from '@/models/dynamicForm';
import addSelfDefForm from './components/addSelfDefForm';
import { mapState } from 'vuex';

/**
archForm:view 扩展表单设置-查看
archForm:add 扩展表单设置-新增
archForm:copy 扩展表单设置-复制
archForm:update 扩展表单设置-编辑
archForm:del 扩展表单设置-删除
 */
export default {
    data() {
        return {
            total: 0,
            currentPage: 1,
            pagesize: 50,
            dialogFormVisible: false,
            targetSchema: {},
            title: '',
            copyTitle: '复制扩展表单',
            createTitle: '新增扩展表单',
            isCreate: true,
            formList: [],
            appModuleList: [],
            appMap: {},
            currentAppId: undefined,
            onlyCreator: false,
            orgList: [],
        };
    },
    computed: {
        condition() {
            return {
                currentPage: this.currentPage,
                pagesize: this.pagesize,
                appId: this.currentAppId,
                onlyCreator: this.onlyCreator,
            };
        },
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    watch: {
        condition: {
            handler() {
                this.getFormList();
            },
        },
    },
    mounted() {
        this.getAppModuleList();
        this.getOrgList();
    },
    methods: {
        getOrgList() {
            FormOption.getAdminOrgList().then((data) => {
                this.orgList = data;
            });
            // // 修改
            // http.get('v1/bizSettingAdmin/orgByAdmin').then((data) => {
            //     this.orgList = data;
            // });
        },
        getFormList() {
            // 后端传来的数据不直接带appName和code。
            DynamicFormModel.loadList({
                appId: this.currentAppId,
                onlyCreator: this.onlyCreator,
                current: this.currentPage,
                size: this.pagesize,
            }).then((data = {}) => {
                this.formList = data.records || [];
                this.total = data.total;
                Object.keys(this.formList).forEach((formItem) => {
                    if (typeof this.appMap[this.formList[formItem].appId] === 'undefined') {
                        // 解决映射存在问题导致部分表项不显示的问题。
                        return true;
                    }
                    this.$set(this.formList[formItem], 'appName', this.appMap[this.formList[formItem].appId].appName);
                    this.$set(this.formList[formItem], 'appCode', this.appMap[this.formList[formItem].appId].appCode);
                    return true;
                });
            });
        },
        getAppModuleList() {
            AppModule.getModuleListWithCode().then((AppItems) => {
                this.appModuleList = AppItems;
                Object.keys(this.appModuleList).forEach((item) => {
                    const appItem = this.appModuleList[item];
                    this.appMap[appItem.appId] = {
                        appName: appItem.name,
                        appCode: appItem.appCode,
                    };
                });
                this.getFormList();
            });
        },
        handleDelete(row) {
            if (row.creator.erp === this.$store.state.user.erp) {
                this.$confirm('是否确认删除该条记录？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    this.targetSchema = row;
                    DynamicFormModel.deleteForm({ schemaId: this.targetSchema.id }).then(() => {
                        this.$message({
                            type: 'success',
                            message: '删除成功！',
                        });
                        this.getFormList();
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除',
                    });
                });
            }
        },
        handleCopy(row) {
            this.targetSchema = row;
            this.isCreate = false;
            this.title = `${this.copyTitle}【${this.targetSchema.name}】`;
            this.dialogFormVisible = true;
        },
        handleEdit(row) {
            if (this.jurisdicList['archForm:update'] !== 0) {
                this.$message({
                    type: 'warning',
                    message: this.tips,
                });
                return;
            }
            if (row.creator.erp === this.$store.state.user.erp) {
                this.$router.push({
                    name: 'extendFormSettingEdit',
                    query: {
                        schemaId: row.id,
                        appName: row.appName,
                        appCode: row.appCode,
                    },
                });
            }
        },
        handleCreate() {
            this.isCreate = true;
            this.title = this.createTitle;
            this.dialogFormVisible = true;
        },
        closeDialog() {
            this.dialogFormVisible = false;
        },
        handleSizeChange(size) {
            this.pagesize = size;
        },
        handleCurrentChange(currentPage) {
            this.currentPage = currentPage;
        },
    },
    components: {
        addSelfDefForm,
    },
};
</script>

<style lang="less">
.extendFormLayout {
    width: calc(~"100% - 48px");
    max-height: calc(~"100% - 48px");
    margin-left: 24px;
    margin-top: 24px;
    background-color: #fff;

    &__header {
        position: relative;
        display: flex;
        align-items: center;
        height: 48px;
        background: rgba(255,255,255,1);
        border-bottom: 1px solid #EBEEF5;
        & > * {
            margin-right: 16px;
        }
        .title {
            font-size: 14px;
            font-weight: 500;
            margin-left: 16px;
            float: left;
            height: 22px;
            line-height: 22px;
            font-family: PingFangSC-Medium;
            color: rgba(48,49,51,1);
        }
    }

    &__addForm {
        position: absolute;
        right: 0;
        margin-right: 16px;
    }

    &__footer {
        background: #fff;
        height: 64px;
        position: relative;

        .j-pagination {
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translateY(-50%);
        }
    }

    .extendFormTable {
        width: calc(~"100% - 32px");
        max-height: calc(~"100vh - 224px");
        overflow-y: auto;
        overflow-x: auto;
        font-size: 13px;
        margin: 16px 16px 0 16px;
        th {
            font-weight: 500;
            height: 30px;
            text-align: center;
        }
        td {
            height: 40px;
            text-align: center;
        }
        &::before {
            height: 0;
        }
    }

    &::before {
        height: 0;
    }

    .el-button--text {
        // color: #2695F1;
    }

    .el-button-delete {
        color: #F55445;
    }
}
</style>
