<template>
    <div class="global-setting">
        <el-tabs
            v-model="activeName"
            type="border-card"
            style="height: 100%;border:none; box-shadow:none;"
        >
            <!-- <el-tab-pane
                label="版本说明"
                name="version-edit"
                v-if="jurisdicList['archConfig:version:view'] === 0"
            >
                <global-setting-version />
            </el-tab-pane> -->
            <!-- <el-tab-pane
                label="产品动向"
                name="product-trend-edit"
                v-if="jurisdicList['archConfig:product:view'] === 0"
            >
                <global-setting-product-trend />
            </el-tab-pane> -->
            <el-tab-pane
                label="数据字典维护"
                name="dict"
                v-if="jurisdicList['archConfig:dict:view'] === 0"
            >
                <global-setting-dict />
            </el-tab-pane>
            <!-- <el-tab-pane label="SSE强制刷新">
                <a target="_blank" href="/space-demand/api/v1/sse/version/push">SSE强制刷新</a>
            </el-tab-pane> -->
        </el-tabs>
    </div>
</template>
<script type="text/javascript">
import globalSettingDict from './globalSettingDict';
// import GlobalSettingVersion from './globalSettingVersion';
// import GlobalSettingProductTrend from './globalSettingProductTrend';
import { mapState } from 'vuex';

export default {
    components: {
        // GlobalSettingVersion,
        globalSettingDict,
        // GlobalSettingProductTrend,
    },
    data() {
        return {
            activeName: 'dict',
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
        }),
    },
};
</script>
<style lang="less">
.global-setting {
    height: calc(~"100% - 24px");
    // margin: 24px 24px 0 16px;
}
</style>
