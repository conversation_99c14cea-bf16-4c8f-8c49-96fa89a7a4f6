<template>
    <el-tabs
        v-model="activeName"
        type="border-card"
        style="height: 100%; border:none;"
    >
        <el-tab-pane
            label="组织结构"
            name="org"
            v-if="jurisdicList['archOrg:org:view'] === 0"
        >
            <org-manage
                v-if="activeName === 'org'"
            />
        </el-tab-pane>
        <el-tab-pane
            label="人员管理"
            name="user"
            v-if="jurisdicList['archOrg:per:view'] === 0"
        >
            <org-user-manage
                v-if="activeName === 'user'"
            />
        </el-tab-pane>
    </el-tabs>
</template>
<script type="text/javascript">
import { mapState } from 'vuex';
import OrgManage from './orgManage';
import OrgUserManage from './orgUserManage';

export default {
    components: {
        OrgManage,
        OrgUserManage,
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            // tips: state => state.option.tips,
        }),
    },
    data() {
        return {
            activeName: 'org',
        };
    },
    methods: {
    },
};
</script>
<style lang="less">
.org-setting {
    height: calc(~'100% - 24px');
    margin: 24px 24px 0 16px;
}
</style>
