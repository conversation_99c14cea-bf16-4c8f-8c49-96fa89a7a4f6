<template>
    <div class="j-wiki-summary">
        <el-input
            class="j-wiki-summary__search j-input--background"
            v-model="searchMenu"
            placeholder="输入并搜索"
            clearable
        >
            <i
                slot="prefix"
                class="el-input__icon el-icon-search"
            />
        </el-input>
        <el-tree
            ref="wikiMenuTree"
            class="j-wiki-product__tree"
            :props="{
                children: 'children',
                label: 'name'
            }"
            :default-expanded-keys="expandedKeys"
            :highlight-current="true"
            :expand-on-click-node="false"
            :data="menus"
            :filter-node-method="filterMenuTree"
            @node-click="handleMenuClick"
            node-key="id"
            :allow-drop="allowDrop"
            @node-drop="changeNodeIndex"
            :draggable="editor || isAdmin"
        >
            <span
                class="el-tree-node__label"
                slot-scope="{ node }"
                :title="node.label"
            >
                {{ node.label }}
            </span>
        </el-tree>
    </div>
</template>

<script type="text/javascript">
import Wiki from '$module/models/WikiMenus';

export default {
    props: {
        editor: {
            type: Boolean,
            default: false,
        },
        isAdmin: {
            type: Boolean,
            default: false,
        },
        keyword: { default: '', type: String },
    },
    data() {
        return {
            searchMenu: undefined,
            menus: [],
            expandedKeys: [],
        };
    },
    mounted() {
        this.loadWikiMenus();
    },
    methods: {
        loadWikiMenus() {
            Wiki.queryWikiMenus().then((data) => {
                this.menus = data;
            }).then(() => {
                this.setCurrent();
                this.handleMenuClick(this.$refs.wikiMenuTree.getCurrentNode());
            });
        },
        handleMenuClick(node = {}) {
            if (node.name) {
                this.$emit('select-menu', node);
            }
        },
        filterMenuTree(value, data) {
            if (!value) {
                return true;
            }
            if (data.pathName) {
                return data.pathName.indexOf(value) !== -1;
            }
            return data.name.indexOf(value) !== -1;
        },
        allowDrop(draggingNode, dropNode, type) {
            // 没有节点权限
            if (!draggingNode.data.opt && !this.isAdmin) {
                return false;
            }
            // 同一个APP
            if (draggingNode.data.appId === dropNode.data.appId) {
                return type !== 'next';
            }
            // 同层级拖拽
            if (draggingNode.parent === dropNode.parent && draggingNode.level === dropNode.level) {
                return type !== 'next' && type !== 'inner';
            }
            return false;
        },
        changeNodeIndex(draggingNode, dropNode, p) {
            let position = 'BEFORE';
            if (p === 'inner') {
                position = 'CHILDREN';
            }
            Wiki.changeNodeIndex(draggingNode.data.id, dropNode.data.id, position);
        },
        setCurrent() {
            const { id } = this.$route.params;
            if (id) {
                this.expandedKeys = [id];
                this.$refs.wikiMenuTree.setCurrentKey(id);
            }
        },
    },
    watch: {
        '$route.path': {
            handler(n, o) {
                if (n && o.includes('create')) {
                    this.loadWikiMenus();
                }
                this.setCurrent();
            },
        },
        searchMenu(val) {
            this.$refs.wikiMenuTree.filter(val);
        },
        keyword(val) {
            this.searchMenu = val;
        },
    },
};
</script>

<style lang="less">
.j-wiki-summary {
    .el-tree{
        background: transparent;
    }
    &__search {
        margin: 10px;
        width: 248px;
    }

    & .j-wiki-product__tree {
        .el-tree-node__content{
            width: 100%;
            height: var(--height--menu-item);

            & span:last-child {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
