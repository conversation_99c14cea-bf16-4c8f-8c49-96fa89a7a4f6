<template>
    <div class="j-wiki-detail">
        <div
            v-if="editor || isAdmin"
            class="j-wiki-detail__toolbars"
        >
            <el-button
                class="j-wiki-detail__edit"
                type="text"
                @click="editWiki"
            >
                编辑
            </el-button>
        </div>
        <mavon-editor
            v-model="content"
            :subfield="false"
            :toolbars-flag="false"
            v-bind="markdownProps"
            default-open="preview"
        />
    </div>
</template>

<script type="text/javascript">
import { mavonEditor } from 'mavon-editor';
import 'mavon-editor/dist/css/index.css';
import Wiki from '$module/models/WikiMenus';
import { markdownProps } from '$module/constant';

export default {
    components: { mavonEditor },
    data() {
        return {
            render: '',
            content: '',
            markdownProps,
        };
    },
    props: {
        editor: {
            type: Boolean,
            default: false,
        },
        isAdmin: {
            type: Boolean,
            default: false,
        },
    },
    mounted() {
        this.loadWiki(this.$route.params.id);
    },
    methods: {
        loadWiki(id) {
            Wiki.getCurrent(id).then((data) => {
                this.content = data.content;
            });
        },
        editWiki() {
            this.$router.push({
                name: 'editWiki',
            });
        },
    },
    watch: {
        '$route.params.id': {
            handler(id) {
                this.loadWiki(id);
            },
            deep: true,
        },
    },
};

</script>

<style lang="less">
.j-wiki-detail {
    & .markdown-body {
        font-size: 16px;
        line-height: 2;
    }
    & .v-note-wrapper .v-note-panel .v-note-show .v-show-content,
    .v-note-wrapper .v-note-panel .v-note-show .v-show-content-html{
        padding: 0 var(--gutter--medium);
    }
}
</style>
