<template>
    <el-form
        :model="formData"
        :inline="true"
        ref="wikiForm"
        class="j-wiki-editor"
    >
        <el-form-item
            prop="name"
            :rules="{ required: true, message: '请输入标题', trigger: 'blur' }"
        >
            <el-input
                class="j-wiki-editor__title"
                v-model="formData.name"
                placeholder="页面标题"
            />
        </el-form-item>
        <el-form-item
            v-if="isAdmin"
            prop="appId"
            style="float: right; margin-right: 60px"
            :rules="{ required: true }"
        >
            <el-select v-model="formData.appId" clearable filterable allow-create>
                <el-option
                    v-for="app in appList"
                    :key="app.id"
                    :label="app.name"
                    :value="app.id"
                />
            </el-select>
        </el-form-item>
        <el-button
            class="j-wiki-editor__save"
            type="primary"
            @click="save"
        >
            保存
        </el-button>
        <mavon-editor
            class="j-wiki-editor__edit"
            ref="md"
            v-model="formData.content"
            code-style="atom-one-dark"
            v-bind="markdownProps"
            :toolbars="toolbars"
            @save="save"
        />
    </el-form>
</template>

<script type="text/javascript">
import { mavonEditor } from 'mavon-editor';
import 'mavon-editor/dist/css/index.css';
import Wiki from '$module/models/WikiMenus';
import { markdownProps } from '$module/constant';
import AppServer from '@/modules/root/models/appServer';

export default {
    components: { mavonEditor },
    data() {
        return {
            value: '',
            toolbars: {
                bold: true, // 粗体
                italic: true, // 斜体
                header: true, // 标题
                underline: true, // 下划线
                strikethrough: true, // 中划线
                mark: true, // 标记
                superscript: true, // 上角标
                subscript: true, // 下角标
                quote: true, // 引用
                ol: true, // 有序列表
                ul: true, // 无序列表
                link: true, // 链接
                imagelink: true, // 图片链接
                code: true, // code
                table: true, // 表格
                fullscreen: true, // 全屏编辑
                readmodel: true, // 沉浸式阅读
                // htmlcode: true, // 展示html源码
                help: true, // 帮助
                undo: true, // 上一步
                redo: true, // 下一步
                trash: true, // 清空
                // save: true, // 保存（触发events中的save事件）
                navigation: true, // 导航目录
                alignleft: true, // 左对齐
                aligncenter: true, // 居中
                alignright: true, // 右对
                subfield: true, // 单双栏模式
                preview: true, // 预览
            },
            markdownProps,
            formData: {
                id: undefined,
                name: '',
                parentId: undefined,
                content: '',
                appId: undefined,
            },
            appList: [],
        };
    },
    props: {
        editor: {
            type: Boolean,
            default: false,
        },
        isAdmin: {
            type: Boolean,
            default: false,
        },
    },
    mounted() {
        const { path } = this.$route;
        if (path.includes('edit')) {
            this.getCurrent();
        } else if (path.includes('child')) {
            this.formData.parentId = this.$route.params.id;
        }
        AppServer.getAppStore().then((appList = []) => {
            this.appList = appList;
        });
    },
    methods: {
        getCurrent() {
            const data = this.$route.params;
            if (data.id) {
                Wiki.getCurrent(data.id).then((wiki) => {
                    this.formData = wiki;
                });
            }
        },
        save() {
            this.$refs.wikiForm.validate((valid) => {
                if (valid) {
                    let req;
                    if (this.formData.id) {
                        req = Wiki.update(new Wiki(this.formData));
                    } else {
                        req = Wiki.save(new Wiki(this.formData));
                    }
                    req.then((data) => {
                        this.$notify.success('保存成功');
                        this.$router.push({
                            name: 'wikiDetail',
                            params: { id: data || this.formData.id, name: this.formData.name },
                        });
                    });
                }
            });
        },
    },
};
</script>

<style lang="less">
.j-wiki-editor {
    & .markdown-body {
        font-size: 16px;
        line-height: 2;
    }

    & .fa-mavon-picture-o .op-image>:nth-child(2) {
        display: none;
    }

    &__title {
        font-weight: bold;
        min-width: 600px;
        width: 80%;

        & input {
            margin: 0;
            padding: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            background: none;
            border: none;
            display: inline-block;
            font-size: 28px;
            height: 45px;
        }
    }

    &__save{
        right: 0;
        top: 68px;
        position: absolute;
    }

    &__edit {
        height: calc(~"100vh - 150px");
    }
}
</style>
