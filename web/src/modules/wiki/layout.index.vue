<template>
    <el-container class="j-wiki-root">
        <el-header
            class="jacp-root-header"
            height="48px"
        >
            <span @click="goWikiHome()">开放平台</span>
            <el-button
                v-if="editor || isAdmin"
                class="j-wiki-create-button"
                type="primary"
                @click="createWiki"
            >
                创建
            </el-button>
        </el-header>

        <el-container class="j-wiki-container">
            <el-aside
                width="268px"
                class="j-wiki-aside"
            >
                <wiki-menus
                    :editor="editor"
                    :isAdmin="isAdmin"
                    @select-menu="currentWiki"
                />
            </el-aside>
            <el-main>
                <router-view
                    :editor="editor"
                    :isAdmin="isAdmin"
                />
            </el-main>
        </el-container>
    </el-container>
</template>

<script type="text/javascript">
import wikiMenus from '$module/components/wikiMenus';
// import SecurityModel from '@/models/security';

export default {
    components: { wikiMenus },
    data() {
        return {
            current: {},
            editor: false,
            isAdmin: false,
        };
    },
    mounted() {
        // SecurityModel.checkPermission(this.$store.state.user.erp, 'change-user').then((data) => {
        //     if (data) {
        //         this.isAdmin = true;
        //     }
        // });
    },
    methods: {
        currentWiki(menu) {
            this.current = menu;
            this.editor = menu.opt;
            this.$router.push({
                name: 'wikiDetail',
                params: menu,
            });
        },
        createWiki() {
            const name = this.current.id ? 'createWiki' : 'createRoot';
            this.goPage(name, { ...this.current });
        },
        goWikiHome() {
            this.current = {};
            this.editor = false;
            this.goPage('openApi');
        },
        goPage(name, params) {
            this.$router.push({ name, params });
        },
    },
};

</script>

<style lang="less">
.j-wiki-root {
    & .jacp-root-header>span {
        cursor: pointer;
    }

    & .j-wiki-create-button {
        margin: 10px 0;
        padding: 6px 14px;
        position: absolute;
        left: 152px;
    }

    & .j-wiki-container{
        height: calc(~"100vh - 50px");
        overflow: hidden;
    }
}
.j-wiki-aside{
    background: #FAFBFC;
}
</style>
