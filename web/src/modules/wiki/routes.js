import layout from './layout';

export default [{
    path: 'openApi',
    name: 'openApi',
    component: layout.index,
    children: [{
        path: 'detail/:id/:name.md',
        name: 'wikiDetail',
        component: layout.wikiDetail,
    }, {
        path: 'edit/:id/:name.md',
        name: 'editWiki',
        component: layout.wikiEditor,
    }, {
        path: 'create/:id/child',
        name: 'createWiki',
        component: layout.wikiEditor,
    }, {
        path: 'create/root',
        name: 'createRoot',
        component: layout.wikiEditor,
    }],
}];
