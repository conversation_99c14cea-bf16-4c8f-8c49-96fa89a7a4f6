<template>
    <el-container class="j-wiki-container">
        <el-aside
            width="268px"
            class="j-wiki-aside"
        >
            <wiki-menus
                class="j-mgt8"
                ref="wikiMenu"
                :keyword="keyword"
                :editor="editor"
                :is-admin="isAdmin"
                @select-menu="currentWiki"
            />
        </el-aside>
        <el-main style="height: 100%; ">
            <el-header>
                <el-button
                    v-for="(item, index) in keywords"
                    :type="keyword === item.value ? 'primary' : undefined"
                    :key="`${item.value}-${index}`"
                    @click="() => handleClickKeyword(item)"
                    round
                >
                    {{ item.label }}
                </el-button>
            </el-header>
            <router-view />
        </el-main>
    </el-container>
</template>

<script type="text/javascript">
import wikiMenus from '$module/components/wikiMenus';
// import SecurityModel from '@/models/security';

const keywords = [{
    label: '全部文档',
    value: '',
    id: 141,
}, {
    label: '开放平台文档',
    value: '开放平台',
    id: 111,
}, {
    label: '接入流程',
    value: '接入流程',
    id: 112,
}, {
    label: '入驻应用商店流程',
    value: '入驻应用商店流程',
    id: 114,
}, {
    label: '服务端文档',
    value: '开放能力介绍',
    id: 106,
}, {
    label: '微前端文档',
    value: '微前端',
    id: 120,
}, {
    label: '平台规范',
    value: '平台规范',
    id: 108,
}, {
    label: '接入常见问题',
    value: '常见问题',
    id: 132,
}];
export default {
    components: { wikiMenus },
    data() {
        return {
            current: {},
            keyword: '开放平台',
            keywords,
            editor: false,
            isAdmin: false,
        };
    },
    mounted() {
        // SecurityModel.checkPermission(this.$store.state.user.erp, 'change-user').then((data) => {
        //     if (data) {
        //         this.isAdmin = true;
        //     }
        // });
    },
    methods: {
        currentWiki(menu) {
            this.current = menu;
            this.editor = menu.opt;
            this.$router.push({
                name: 'innerWikiDetail',
                params: menu,
            });
        },
        handleClickKeyword({ label, value, id }) {
            this.keyword = value;
            this.$router.push({
                name: 'innerWikiDetail',
                params: { id, name: `${label}` },
            });
        },
    },
};

</script>
<style lang="less">
.j-wiki-container{
    grid-template-columns: 268px auto;
    grid-template-rows: 1fr;
    overflow: hidden;
    height: 100%;
}
</style>
