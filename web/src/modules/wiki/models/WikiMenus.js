import isPlainObject from 'lodash/isPlainObject';
import { jacpSecurityHttp } from '@/plugins/http';

function setPathName(item, parent) {
    const result = !parent || !isPlainObject(parent)
        ? {
            ...item, pathName: item.name,
        }
        : { ...item, pathName: `${parent.pathName}-${item.name}` };
    if (item.children) {
        result.children = item.children.map(child => setPathName(child, result));
    }

    return result;
}
export default class WikiMenus {
    constructor({
        id,
        name = '',
        parentId = -1,
        content = '',
        appId,
    } = {}) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.content = content;
        this.appId = appId;
    }

    static queryWikiMenus() {
        return jacpSecurityHttp.get('/app/doc').then(data => data.map(item => setPathName(item)));
    }

    static getCurrent(id) {
        return jacpSecurityHttp.get(`/app/doc/${id}`).then(data => new WikiMenus(data));
    }

    static save(wiki) {
        return jacpSecurityHttp.post('/app/doc', wiki);
    }

    static update(wiki) {
        return jacpSecurityHttp.put(`/app/doc/${wiki.id}`, wiki);
    }

    static changeNodeIndex(docId, targetDocId, position) {
        return jacpSecurityHttp.put(`/app/doc/${docId}/moveTo`, {
            targetDocId,
            position,
        });
    }
}
