<template>
    <Layout
        :left-link="leftLink"
        :right-links="rightLinks"
    >
        <template
            v-if="$route.name==='jacpOpen'"
            slot="default"
        >
            <section class="open-section-banner">
                <div class="open-banner">
                    <div class="open-banner__left">
                        <div class="open-banner__title">开放开源<span style="padding: 0 16px;" />共筑生态</div>
                        <div class="open-banner__subtitle">和你一起打造高效研发协作工具</div>
                        <div class="open-banner__description">行云开放平台是以开发者为中心的技术服务平台，我们提供了丰富的API、组件库，和你一起创造无限协作可能</div>
                        <a
                            class="open-button banner-button"
                            @click.prevent="openCreate"
                            v-if="jurisdicList['archAppManage:app:store:add'] === 0 ||
                                jurisdicList['archAppManage:app:third:add'] === 0"
                        >{{ bannerLink.name }}</a>
                    </div>
                    <div class="open-banner__right">
                        <div class="open-banner__image">
                            <img src="~@/assets/images/<EMAIL>">
                        </div>
                    </div>
                </div>
            </section>
            <section
                class="open-section-docs"
                v-if="helpDoc"
            >
                <div class="open-docs">
                    <div class="section-title">平台文档</div>
                    <div class="open-docs__card-list">
                        <div
                            v-for="(doc, index) in docs"
                            :key="index"
                            class="open-docs-card"
                        >
                            <div class="open-docs-card__left">
                                <div class="open-docs-card__icon">
                                    <img :src="doc.icon">
                                </div>
                            </div>
                            <div class="open-docs-card__right">
                                <div class="open-docs-card__title">{{ doc.title }}</div>
                                <div class="section-divider" />
                                <a
                                    target="_blank"
                                    v-for="(link, lindex) in doc.links"
                                    :key="'link' + lindex"
                                    class="open-docs-card__description"
                                    :href="goHelp(link.href)"
                                >
                                    {{ link.name }}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="open-docs__bg" />
                </div>
            </section>
            <section class="open-section-access">
                <div class="open-access">
                    <div class="section-title">接入流程</div>
                    <div class="open-access__box-list">
                        <div
                            class="open-access-box__item"
                            v-for="(step, index) in accessSteps"
                            :key="'icon'+index"
                        >
                            <div class="open-access-box__icon-box">
                                <div class="open-access-box__icon-inner">
                                    <img :src="step.icon">
                                </div>
                                <div class="open-access-box__icon-line" />
                            </div>
                        </div>
                    </div>
                    <div class="open-access__box-list open-access__detail-list">
                        <div
                            class="open-access__detail-item"
                            v-for="(step, index) in accessSteps"
                            :key="'text'+index"
                        >
                            <div class="open-access__detail-text">{{ step.name }}</div>
                            <!-- <a
                                class="open-access__detail-link"
                                href
                                @click.prevent="openLink(step)"
                            >操作文档</a> -->
                        </div>
                    </div>
                    <div
                        class="open-button open-access__button"
                        @click="openCreate1"
                        v-if="jurisdicList['archAppManage:app:store:add'] === 0 ||
                            jurisdicList['archAppManage:app:third:add'] === 0"
                    >
                        开始创建
                    </div>
                    <div class="open-access-bg" />
                </div>
            </section>
        </template>
    </Layout>
</template>

<script>

import Layout from './layout.outer';
import { docs, accessSteps } from './constant';
import { openLink } from '@/plugins/utils';
import devCenter from '@/modules/open/model/devCenter';
import { mapState } from 'vuex';
import { getPermissionUicomponents } from '@/modules/option/modules';

const leftLink = {
    name: '开放平台',
    route: { name: 'jacpOpen' },
    isPopup: false,
};

const devCenterLink = {
    icon: 'el-icon-setting',
    name: '商店设置',
    route: { name: 'jacpDevCenter' },
    isPopup: false,
};

const appStoreIndex = {
    // icon: 'el-icon-setting',
    name: '应用管理',
    route: { name: 'jacpAppStore' },
    // isPopup: false,
};
const bannerLink = {
    name: '创建应用',
    route: {
        name: 'jacpDevCenter',
    },
    isPopup: false,
};

export default {
    components: { Layout },
    data() {
        return {
            docs,
            accessSteps,
            leftLink,
            rightLinks: [],
            bannerLink,
            devCenterLink,
            appStoreIndex,
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            helpDoc: state => state.app.helpDoc,
        }),
    },
    mounted() {
        devCenter.isAdmin(2).then((res) => {
            if (res) {
                this.rightLinks = [
                    devCenterLink];
            } else {
                this.rightLinks = [];
            }
        });
        this.init();
    },
    methods: {
        openLink,
        openCreate() {
            this.$router.push({
                name: 'jacpAppStore',
                query: {
                    create: true,
                },
            });
        },
        openCreate1() {
            this.$router.push({
                name: 'jacpAppStore',
            });
        },
        goHelp(link) {
            console.log('[===]==', this.$store.state.url.help);
            // console.log('[===]==', this.$store.state.url.spaceDemand);
            return `${this.$store.state.url.help}${link}`;
        },
        init() {
            getPermissionUicomponents();
        },
    },
};
</script>

<style lang="less">
@lineHeight: 1.5;

.jacp-open {
    .section{
        &-title {
            color: var(--color--base--content);
            font-weight: var(--font-weight-bold);
            font-size: 32px;
            line-height: @lineHeight;
            text-align: center;
            margin-bottom: 24px;
        }
        &-divider {
            width: 100%;
            height: 2px;
            transform: scaleY(0.5);
            background: var(--color--base--hr);
            margin: var(--gutter--small) 0;
        }
    }
    .open-button {
        width: fit-content;
        padding: var(--gutter--small) var(--gutter--large);
        background: var(--color--primary);
        color: var(--color--primary-white);
        line-height: @lineHeight;
        border-radius: 20px;
        cursor: pointer;
        transition: .3s;
        &:hover {
            background: var(--color--primary--hover);
        }
    }
    a.plain,
    a.plain:hover {
        color: inherit;
    }
    a[href]:hover {
        color: var(--color--primary--hover);
        transition: .3s;
    }
}

.jacp-open {
    .open-banner {
        display: flex;
        height: 348px;
        &__left {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        &__right {
            flex-basis: 640px;
            position: relative;
        }
        &__image {
            width: 640px;
            position: absolute;
            bottom: 48px;
            right: 0;
            img {
                width: 100%;
            }
        }

        &__title, &__subtitle, &__description {
            line-height: @lineHeight;
        }
        &__title, &__subtitle {
            color: var(--color--base--content);
        }
        &__title {
            font-weight: var(--font-weight-bold);
            font-size: 48px;
        }
        &__subtitle {
            font-size: var(--font-size--title);
        }
        &__description {
            color: var(--color--secondary--content);
            font-size: var(--font-size--content);
            margin: 20px 0;
        }

        a.banner-button:hover {
            color: var(--color--primary-white);
        }

    }

    .open-section-docs {
        background: #FAFBFC;
    }

    .open-docs,
    .open-access {
        position: relative;
        padding: 56px 0;
        // height: 380px;
    }
    .open-docs__card-list {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
    }
    .open-docs-card {
        width: 360px;
        height: 200px;
        display: flex;
        background: var(--color--primary-white);
        border-radius: 8px;
        box-shadow:  0 8px 24px 0 rgba(48,49,51,0.08);
        transition: .3s;
        &:hover {
            transform: translateY(-8px);
        }
        &__left {
            flex-basis: 96px;
        }
        &__icon {
            width: 64px;
            height: 64px;
            transform: translate(32px, 32px);
            img {
                width: 100%;
            }
        }

        &__right {
            flex: 1;
            padding: 40px 48px 0 24px;
        }
        &__title {
            color: var(--color--base--content);
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size--title);
        }
        &__description {
            display: block;
            color: var(--color--secondary--content);
            font-size: var(--font-size--content);
            line-height: @lineHeight;
            margin-bottom: var(--gutter--small);
        }
    }
    .open-docs__bg {
        width: 307px;
        height: 48px;
        background: url('~@/assets/images/advantage.png') no-repeat;
        background-size: 100%;
        position: absolute;
        bottom: 38px;
        left: -95px;
        // z-index: -1;
    }

    .open-access-bg {
        width: 375px;
        height: 48px;
        background: url('~@/assets/images/access-steps.png') no-repeat;
        background-size: 100%;
        position: absolute;
        bottom: 74px;
        right: -74px;
        z-index: -1;
    }
    .open-access {
        &__box-list {
            display: flex;
            justify-content: space-between;
            padding: 0 48px;
        }
        &__detail-list {
            padding: 0 46px;
        }
        &__detail-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        &__detail-text {
            color: var(--color--base--content);
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size--title);

            margin: var(--gutter--small) 0;
        }
    }
    .open-access-box {
        &__icon-box {
            display: flex;
        }
        &__icon-inner {
            width: 75px;
            height: 75px;
            img {
                width: 100%;
            }
        }
        &__item:last-of-type .open-access-box__icon-line {
            display: none;
        }
        &__icon-line {
            width: 244px;
            margin-top: 37px;
            height: 2px;
            transform: scaleY(0.5);
            background: rgba(192,196,204,.4);
        }
    }
    .open-access__button {
        margin: 48px auto;
    }
}
</style>
