<template>
    <div
        class="app-rank-outer"
        style="display: flex; width: 100%;"
    >
        <div class="app-charts-panel">
            <div class="app-block-title">应用访问数据</div>
            <div class="app-charts">
                <div class="app-charts__condition">
                    <el-form
                        ref="form"
                        inline
                    >
                        <el-form-item
                            label="按"
                            label-position="left"
                        >
                            <el-select
                                class="app-charts__dim_select"
                                v-model="dim"
                                placeholder="请选择"
                                @change="dimChange"
                            >
                                <el-option
                                    v-for="item in dims"
                                    :key="item.value"
                                    :label="item.name"
                                    :value="item.value"
                                />
                            </el-select>
                            <el-date-picker
                                class="app-charts__date-picker"
                                v-model="selectedDate"
                                @change="dateChange"
                                :type="dim"
                                :format="dateFormat"
                                :picker-options="pickerOptions"
                            />
                        </el-form-item>
                        <el-form-item
                            label="应用范围"
                            label-position="right"
                            class="app-charts__appscope"
                        >
                            <el-select
                                v-model="appScope"
                                placeholder="请选择"
                                @change="appScopeChange"
                            >
                                <el-option
                                    v-for="item in appScopes"
                                    :key="item.value"
                                    :label="item.name"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <el-alert
                    title="注: 仅统计到昨日及之前，每日中午12时更新数据，数据来源于子午线"
                    type="warning"
                    :closable="false"
                    show-icon
                />
                <el-tabs
                    class="app-charts__tabs"
                    v-model="activeTab"
                    @tab-click="refreshActiveTab"
                >
                    <el-tab-pane
                        label="日均访客数"
                        name="avgUV"
                    >
                        <div
                            id="uv-avg-charts"
                            class="app-charts__block"
                        />
                    </el-tab-pane>
                    <el-tab-pane
                        label="总访客数"
                        name="UV"
                    >
                        <div
                            id="uv-charts"
                            class="app-charts__block"
                        />
                    </el-tab-pane>
                    <el-tab-pane
                        label="总浏览量"
                        name="PV"
                    >
                        <div
                            id="pv-charts"
                            class="app-charts__block"
                        />
                    </el-tab-pane>
                </el-tabs>
                <jacp-empty v-if="nodataFlag">
                    <div style="display: flex;flex-direction: column">
                        <!-- <img
                            class="j-server-error__img"
                            style="width:160px; height: 160px"
                            src="http://storage.jd.com/jacp.attachment/static/images/empty.png"
                        > -->
                        <jacp-text type="disable">
                            数据暂未生成，请修改时间范围
                        </jacp-text>
                    </div>
                </jacp-empty>
            </div>
        </div>
        <div class="app-charts-borderpanel" />
        <div class="app-charts-panel">
            <div class="app-charts__condition">
                <el-form
                    ref="form"
                    inline
                >
                    <el-form-item
                        label="应用范围"
                        label-position="right"
                    >
                        <el-select
                            v-model="rankAppScope"
                            placeholder="请选择"
                            @change="handleAppScopeChange"
                        >
                            <el-option
                                v-for="item in appScopes"
                                :key="item.value"
                                :label="item.name"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="应用排序"
                        label-position="right"
                        class="app-charts__appscope"
                    >
                        <el-select
                            v-model="appRankType"
                            placeholder="请选择"
                            @change="handleAppScopeChange"
                        >
                            <el-option
                                v-for="item in appRankTypes"
                                :key="item.value"
                                :label="item.name"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
            <el-alert
                :title="ranktips[appRankType]"
                type="warning"
                :closable="false"
                show-icon
            />
            <el-table
                :data="appScoreRanks"
                style="width:100%"
                class="appScore-table"
            >
                <el-table-column
                    type="index"
                    width="32"
                >
                    <template slot-scope="scope">
                        <img
                            v-if="scope.$index === 0"
                            src="@/assets/images/rank1.png"
                        >
                        <img
                            v-if="scope.$index === 1"
                            src="@/assets/images/rank2.png"
                        >
                        <img
                            v-if="scope.$index === 2"
                            src="@/assets/images/rank3.png"
                        >
                        <span v-if="scope.$index > 2">
                            {{ scope.$index + 1 }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="appName"
                    label="应用"
                >
                    <template slot-scope="scope">
                        <div
                            class="appScore-table-pic"
                        >
                            <div class="appScore-table-pic__leftwarp">
                                <img
                                    :src="scope.row.appIcon"
                                    style="width:32px;height:32px;"
                                >
                            </div>
                            <div
                                class="j-text-overflow"
                            >
                                <div>
                                    {{ scope.row.appName }}
                                </div>
                                <div
                                    class="description-label"
                                    :title="scope.row.appDesc"
                                >
                                    {{ scope.row.appDesc || '暂无描述' }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="averageScore"
                    label="评分"
                    width="170"
                >
                    <template slot-scope="scope">
                        <el-rate
                            :value="+scope.row.averageScore"
                            disabled
                            void-color="#DBDFE7"
                        />
                        <span class="j-f-large">{{ scope.row.averageScore }}分</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="userCount"
                    label="评分人数"
                    width="80"
                >
                    <template slot-scope="scope">
                        <span class="j-f-large">{{ scope.row.userCount }}人</span>
                    </template>
                </el-table-column>
                <el-table-column
                    width="80"
                    label="操作"
                >
                    <template slot-scope="scope">
                        <el-button>
                            <span
                                class="j-f-large j-link"
                                href="vo"
                                @click="gotoToComment(scope.row.appCode)"
                            >去评分</span>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import moment from 'moment';
import * as echarts from 'echarts';

import DevCenterModel from './model/devCenter';
/* eslint-disable array-callback-return */


export default {
    data() {
        return {
            dim: 'week',
            dateFormat: 'yyyy 第 W 周',
            selectedDate: new Date(),
            startDate: moment().startOf('week').format('YYYY-MM-DD'),
            dims: [{
                name: '周',
                value: 'week',
            }, {
                name: '月',
                value: 'month',
            }],
            appScope: 1,
            appScopes: [{
                name: '全部应用',
                value: 0,
            }, {
                name: '参赛应用',
                value: 1,
            }],
            appRankTypes: [{
                name: '综合排序',
                value: 0,
            }, {
                name: '好评排序',
                value: 1,
            }],
            appRankType: 0,
            daterange: [moment().startOf('week').format('YYYY-MM-DD'), moment().add(6, 'day').format('YYYY-MM-DD')],
            colors: [],
            uvRanksOptions: {
                grid: {
                    left: 100,
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                xAxis: {
                    max: 'dataMax',
                },
                yAxis: {
                    type: 'category',
                    data: [],
                    inverse: true,
                    showBackground: true,
                },
                series: [
                    {
                        name: '总访客数(时间段内总UV)',
                        type: 'bar',
                        data: [],
                        label: {
                            show: true,
                            position: 'right',
                        },
                        itemStyle: {
                            color: '#51AAF4',
                        },
                    },
                ],
                legend: {
                    show: true,
                },
            },
            pvRanksOptions: {
                grid: {
                    left: 100,
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                xAxis: {
                    max: 'dataMax',
                },
                yAxis: {
                    type: 'category',
                    data: [],
                    inverse: true,
                    showBackground: true,
                },
                series: [
                    {
                        name: '总浏览量(时间段内总PV)',
                        type: 'bar',
                        data: [],
                        label: {
                            show: true,
                            position: 'right',
                        },
                        itemStyle: {
                            color: '#32C190',
                        },
                    },
                ],
                legend: {
                    show: true,
                },
            },
            uvAvgRanksOptions: {
                grid: {
                    left: 100,
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                xAxis: {
                    max: 'dataMax',
                },
                yAxis: {
                    type: 'category',
                    data: [],
                    inverse: true,
                },
                series: [
                    {
                        name: '日均访客数(日均uv)',
                        type: 'bar',
                        data: [],
                        label: {
                            show: true,
                            position: 'right',
                        },
                        itemStyle: {
                            color: '#51AAF4',
                        },
                    },
                ],
                legend: {
                    show: true,
                },
            },
            activeTab: 'avgUV',
            avgUVCharts: undefined,
            uvCharts: undefined,
            pvCharts: undefined,
            appScoreRanks: [],
            rankAppScope: 1,
            ranktips: { 0: '综合排序：按照总分（评分*人数）排序', 1: '好评排序：按照应用评分排序' },
            nodataFlag: false,
            mondayTxt: '星期一',
            sundayTxt: '星期日',
            monthNumber: 1,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
        };
    },
    mounted() {
        this.startDate = moment(new Date());
        this.queryAppStats();
        this.handleAppScopeChange();
    },
    created() {
        this.isMonday();
    },
    methods: {
        isMonday() {
            const vm = this;
            if (this.dim === 'week') {
                if (moment().format('dddd') === vm.mondayTxt || moment().format('dddd') === vm.sundayTxt) {
                    this.nodataFlag = true;
                } else {
                    this.nodataFlag = false;
                }
            }
            if (this.dim === 'month') {
                if (moment().get('date') === vm.monthNumber) {
                    this.nodataFlag = true;
                } else {
                    this.nodataFlag = false;
                }
            }
        },
        init() {
            this.queryAppStats();
        },
        dateChange(time) {
            this.nodataFlag = false;
            this.startDate = moment(time);
            this.queryAppStats();
        },
        dimChange() {
            this.nodataFlag = false;
            this.queryAppStats();
        },
        appScopeChange() {
            this.queryAppStats();
        },
        calcDateRange() {
            if (this.dim === 'week') {
                this.dateFormat = 'yyyy 第 W 周';
                this.daterange[0] = moment(this.startDate).startOf('week').format('YYYY-MM-DD');
                // [] closed interval
                this.daterange[1] = moment(this.startDate).startOf('week').add(6, 'day')
                    .format('YYYY-MM-DD');
            } else {
                this.dateFormat = 'yyyy 第 M 月';
                this.daterange[0] = moment(this.startDate).startOf('month').format('YYYY-MM-DD');
                // [] closed interval
                this.daterange[1] = moment(this.startDate).startOf('month').add(1, 'month').subtract(1, 'day')
                    .format('YYYY-MM-DD');
            }
        },
        queryPVRanks() {
            this.pvCharts = echarts.init(document.getElementById('pv-charts'), 'theme');
            this.pvCharts.showLoading();
            DevCenterModel.getRanks(
                {
                    startTime: this.daterange[0],
                    endTime: this.daterange[1],
                    appScope: this.appScope,
                    type: 1,
                },
            ).then((res) => {
                if (res.status === 200 && res.data && res.data.code === 200) {
                    this.pvRanksOptions.yAxis.data = res.data.data.map(v => v.name);
                    this.pvRanksOptions.series[0].data = res.data.data.map(v => v.value);
                    this.pvCharts.setOption(this.pvRanksOptions);
                }
                this.pvCharts.hideLoading();
            });
        },
        queryUVRanks() {
            this.uvCharts = echarts.init(document.getElementById('uv-charts'), 'theme');
            this.uvCharts.showLoading();
            DevCenterModel.getRanks(
                {
                    startTime: this.daterange[0],
                    endTime: this.daterange[1],
                    appScope: this.appScope,
                    type: 2,
                },
            ).then((res) => {
                if (res.status === 200 && res.data && res.data.code === 200) {
                    this.uvRanksOptions.yAxis.data = res.data.data.map(v => v.name);
                    this.uvRanksOptions.series[0].data = res.data.data.map(v => v.value);
                    this.uvCharts.setOption(this.uvRanksOptions);
                }
                this.uvCharts.hideLoading();
            });
        },
        queryUVAvgRanks() {
            this.avgUVCharts = echarts.init(document.getElementById('uv-avg-charts'), 'theme');
            this.avgUVCharts.showLoading();
            DevCenterModel.getRanks(
                {
                    startTime: this.daterange[0],
                    endTime: this.daterange[1],
                    appScope: this.appScope,
                    type: 3,
                },
            ).then((res) => {
                if (res.status === 200 && res.data && res.data.code === 200) {
                    this.uvAvgRanksOptions.yAxis.data = res.data.data.map(v => v.name);
                    this.uvAvgRanksOptions.series[0].data = res.data.data.map(v => v.value);
                    this.avgUVCharts.setOption(this.uvAvgRanksOptions);
                }
                this.avgUVCharts.hideLoading();
            });
        },
        async refreshActiveTab({ name = this.activeTab } = {}) {
            this.$nextTick(() => {
                switch (name) {
                case 'UV':
                    this.uvCharts.resize();
                    break;
                case 'PV':
                    this.pvCharts.resize();
                    break;
                default:
                    this.avgUVCharts.resize();
                    break;
                }
            });
        },
        queryAppStats() {
            this.calcDateRange();
            this.queryPVRanks();
            this.queryUVRanks();
            this.queryUVAvgRanks();
        },
        async handleAppScopeChange(val) {
            this.rankRuls = val;
            const appScoreRanks = await DevCenterModel.getAppScoreRanks(this.rankAppScope);

            if (this.appRankType === 0) {
                this.appScoreRanks = appScoreRanks.map((v) => {
                    const scoreNum = parseFloat(v.averageScore);
                    if (scoreNum < 5) {
                        v.averageScore = scoreNum.toFixed(2);
                    }
                    return v;
                }).sort((v1, v2) => (((v1.averageScore * v1.userCount) > (v2.averageScore * v2.userCount)) ? -1 : 1));
            } else {
                this.appScoreRanks = appScoreRanks.map((v) => {
                    const scoreNum = parseFloat(v.averageScore);
                    if (scoreNum < 5) {
                        v.averageScore = scoreNum.toFixed(2);
                    }
                    return v;
                // eslint-disable-next-line consistent-return
                }).sort((v1, v2) => {
                    if (v1.averageScore > v2.averageScore) {
                        return -1;
                    }
                    if (v1.averageScore === v2.averageScore) {
                        return (v1.averageScore * v1.userCount) > (v2.averageScore * v2.userCount) ? -1 : 1;
                    }
                });
            }
        },
        gotoToComment(appCode) {
            const routeUrl = this.$router.resolve({ name: 'jacpAppDetail', params: { code: appCode }, query: { tab: 'comment' } });
            window.open(routeUrl.href, '_blank');
        },
    },
};
</script>

<style lang="less">
.jacp-open .app-rank-outer.section-main{
    height: calc(100vh - 57px);
    display: flex;
    width: 100%;
}
.app-charts-panel {
    flex: 1;
    padding: 0 var(--gutter--large);
    height: 100%;
    overflow: hidden;
    .el-alert{
        border-radius: var(--radius--default);
        height: var(--height--small);
    }
}
.app-charts-borderpanel {
    width: 1px;
    height: 100%;
    background-color: var(--color--base--hr);
}

.app-charts{
    position: relative;
    &__condition {
        font-size: 14px;
        .el-form-item--small.el-form-item{
            margin-top: var(--gutter--small);
            margin-bottom: var(--gutter--small);
        }
        .el-input--small .el-input__inner{
            color: var(--color--extralight--content);
            font-size: var(--font-size--content);
            border-radius: var(--radius--default);
        }
        .el-form-item__label{
            font-size: var(--font-size--content);
            padding-right: var(--gutter--small);
        }
    }
    &__block{
        height: 1000px;
    }
    &__date-picker {
        &.el-input{
            width: 200px;
        }
        margin-left: var(--gutter--small);
    }
    &__appscope {
        margin-left: var(--gutter--large);
        .el-select{
            width: 150px;
        }
    }
    &__dim_select {
        width: 60px;
    }
    &__tabs{
        .el-tabs__item{
            color: var(--color--secondary--content);
            font-weight: 400;
            &.is-active{
                color: var(--color--base--content);
                font-weight: 600;
            }
        }
        .el-tabs__content{
            height: calc(100vh - 296px);
            width: 100%;
            overflow-y: auto;
        }
    }
    .j-nodata.middle{
        background-color: #fff;
        top: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: calc(100vh - 230px);
    }

}
.appScore-table-pic{
    display: flex;
    align-items: center;
    font-size: var(--font-size--content);
    padding-right: var(--gutter--base);
    &__leftwarp{
        width: 32px;
        height: 32px;
        display: flex;
        margin-right: var(--gutter--small);
        img{
            border-radius: 10px;
        }
    }
    .description-label{
        color: var(--color--secondary--content);
        font-size: var(--font-size--description);
        font-weight: normal;
        line-height: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
.appScore-table.el-table{
    color: var(--color--base--content);
    .cell{
        img{
            width: 32px;
            height: 32px;
        }
    }
    .el-table_1_column_1{
        .cell{
            padding-left: 0;
            padding-right: 0;
            display: flex;
            text-align: center;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    }
    .el-rate{
        display: inline-block;
        margin-right: var(--gutter--small);
        .el-rate__icon{
            margin-right: -4px;
        }
    }
    th>.cell{
        font-weight: 400;
    }
    .el-table__body-wrapper{
        max-height: calc(100vh - 282px);
        overflow-y: auto;
    }
    .j-link{
        cursor: pointer;
        color: var(--color--primary);
    }
}
.app-block-title {
    height: var(--height--menu-item);
    line-height: var(--height--menu-item);
    margin-top: var(--gutter--medium);
    font-size: var(--font-size--subtitle);
    font-weight: var(--font-weight-bold);
    color: var(--color--base--content);
    display: flex;
    justify-content: space-between;
    .right{
       font-size: var(--font-size--description);
       color: var(--color--regular--content);
       font-weight: 400;
    }
}
</style>
