<template>
    <div class="label" v-if="jurisdicList['archLabelManage:label:view'] === 0">
        <h5>标签管理</h5>
        <el-button
            type="primary"
            @click="add"
            class="new"
            v-if="jurisdicList['archLabelManage:label:add'] === 0"
        >
            新增
        </el-button>
        <el-tooltip
            v-else-if="jurisdicList['archLabelManage:label:add'] === 1"
            effect="dark"
            :content="tips"
            placement="top">
            <el-button
                type="primary"
                style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
            >
                新增
            </el-button>
        </el-tooltip>
        <el-table
            :data="labelTable"
            style="width: 100%"
        >
            <el-table-column
                prop="name"
                label="标签名称"
                width="180"
            />
            <el-table-column
                prop="appLabelCount"
                label="关联应用数量"
            />
            <el-table-column
                fixed="right"
                label="操作"
                width="200"
            >
                <template slot-scope="scope">
                    <el-button
                        @click="lookApp(scope.row)"
                        type="text"
                        size="small"
                    >
                        查看关联应用
                    </el-button>
                    <el-button
                        type="text"
                        size="small"
                        @click="edit(scope.row)"
                        v-if="jurisdicList['archLabelManage:label:update'] === 0"
                    >
                        修改
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archLabelManage:label:update'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            修改
                        </el-button>
                    </el-tooltip>
                    <el-button
                        type="text"
                        size="small"
                        @click="remove(scope.row)"
                        v-if="jurisdicList['archLabelManage:label:del'] === 0"
                    >
                        删除
                    </el-button>
                    <el-tooltip
                        v-else-if="jurisdicList['archLabelManage:label:del'] === 1"
                        effect="dark"
                        :content="tips"
                        placement="top">
                        <el-button
                            type="primary"
                            style="cursor: not-allowed;color: #fff;background-color: #93caf8;border-color: #93caf8;"
                        >
                            删除
                        </el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog
            title="关联应用列表"
            :visible.sync="dialogLabelVisible"
            width="35%"
            :append-to-body="true"
            :close-on-click-modal="false"
        >
            <el-table
                :data="AppTable"
                style="width: 100%"
                height="250"
            >
                <el-table-column
                    prop="name"
                    label="应用名称"
                    width="180"
                />
                <el-table-column
                    prop="appTypeName"
                    label="应用类型"
                    width="180"
                />
                <el-table-column
                    prop="appUserRelList"
                    label="负责人"
                >
                    <template slot-scope="scope">
                        <el-tooltip
                            class="item"
                            effect="dark"
                            :content="scope.row.appUserRelList"
                            placement="top"
                        >
                            <span class="toolTipItem">{{ scope.row.appUserRelList }}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog
            :title="title"
            :visible.sync="dialogCreateVisible"
            width="30%"
            :close-on-click-modal="false"
        >
            <el-form
                ref="name"
                :model="data"
            >
                <el-form-item
                    prop="createName"
                    label="标签名称："
                    :rules="[{ max: 10, message: '不能超过10个字符', trigger: ['change', 'blur'] }]"
                >
                    <el-input
                        placeholder="请输入"
                        v-model="data.createName"
                    />
                </el-form-item>
            </el-form>
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="dialogCreateVisible = false">取 消</el-button>
                <el-button
                    type="primary"
                    @click="save"
                    v-if="title === '创建'"
                >确 定</el-button>
                <el-button
                    type="primary"
                    @click="update"
                    v-else
                >确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import devCenter from '../model/devCenter';
import { mapState } from 'vuex';
import { MessageBox } from 'element-ui';

export default {
    data() {
        return {
            labelTable: [],
            dialogLabelVisible: false,
            dialogCreateVisible: false,
            AppTable: [],
            createName: '',
            title: '创建',
            id: '',
            data: {
                createName: '',
            },
        };
    },
    created() {
        this.init();
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    mounted() {
        if (this.jurisdicList['archLabelManage:label:view'] !== 0) {
            MessageBox({
                title: '提示',
                message: '您没有权限，如有诉求，请联系管理员',
                type: 'error',
            });
            this.$router.push({path: '/'});
        }
    },
    methods: {
        init() {
            devCenter.searchAppLabelInfoList().then((res) => {
                this.labelTable = res;
            });
        },
        add() {
            this.dialogCreateVisible = true;
            this.data.createName = '';
            this.id = '';
            this.title = '创建';
        },
        edit(row) {
            this.dialogCreateVisible = true;
            this.title = '修改';
            this.data.createName = row.name;
            this.id = row.id;
        },
        remove(row) {
            if (row.appLabelCount !== 0) {
                this.$message.error('标签已关联应用无法删除');
                return;
            }
            this.$confirm('确定删除该条信息吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '再想想',
                type: 'warning',
            }).then(() => {
                devCenter.removeAppLabelInfo(row.id).then((res) => {
                    if (res === true) {
                        this.$message.success('删除成功!');
                        this.init();
                    } else {
                        this.$message.error('删除失败!');
                    }
                }).catch((err) => {
                    this.$message.error(err);
                });
            });
        },
        save() {
            this.$refs.name.validate((valid) => {
                if (!valid) {
                    return;
                }
                this.dialogCreateVisible = false;
                devCenter.addAppLabelInfo(this.data.createName).then((res) => {
                    if (res === true) {
                        this.$message.success('创建成功!');
                        this.init();
                    } else {
                        this.$message.error('创建失败!');
                    }
                }).catch((err) => {
                    this.$message.error(err);
                }).finally(() => {
                    this.data.createName = '';
                });
            });
        },
        update() {
            this.$refs.name.validate((valid) => {
                if (!valid) {
                    return;
                }
                this.dialogCreateVisible = false;
                devCenter.updateAppLabelInfo({ id: this.id, name: this.data.createName }).then((res) => {
                    if (res === true) {
                        this.$message.success('修改成功!');
                        this.init();
                    } else {
                        this.$message.error('修改失败!');
                    }
                }).catch((err) => {
                    this.$message.error(err);
                }).finally(() => {
                    this.data.createName = '';
                    this.id = '';
                });
            });
        },
        lookApp(row) {
            this.dialogLabelVisible = true;
            devCenter.checkLabelAppList(row.id).then((res) => {
                res.forEach((item) => {
                    const data = [];
                    item.appUserRelList.forEach((item1) => {
                        data.push(item1.userName);
                    });
                    item.appUserRelList = data.join(',');
                    if (item.appType === 1) {
                        item.appTypeName = '外采应用';
                    }
                    if (item.appType === 2) {
                        item.appTypeName = '自研应用';
                    }
                });
                this.AppTable = res;
            });
        },
    },
};
</script>

<style lang="less" scoped>
.label {
  margin: 50px 50px;
  .new {
    float: right;
  }
}
.toolTipItem {
    display: inline-block;
    width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
