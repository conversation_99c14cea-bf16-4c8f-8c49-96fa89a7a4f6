<template>
    <div class="app-row">
        <el-form
            ref="visit"
            label-position="right"
            label-width="120px"
            :model="visit"
        >
            <!-- <el-form-item
                label="访问方式："
                prop="accessMode"
            >
                <el-radio-group
                    v-model="visit.accessMode"
                    :disabled="!permission"
                >
                    <el-radio-button label="2">新开tab跳转</el-radio-button>
                    <el-radio-button label="1">内嵌</el-radio-button>
                </el-radio-group>
            </el-form-item> -->
            <!-- <el-form-item
                label="访问路由："
                prop="topRooter"
                :rules="[
                    {required: true, message: '访问路由不能为空'},
                    { pattern: /(http|https):\/\/([\w.]+\/?)\S*/, message: '路由格式不对，必须以http://或https://开头', trigger: 'change'}
                ]"
            > -->
            <el-form-item
                label="访问路由："
                prop="topRooter"
                :rules="[
                    {required: true, message: '访问路由不能为空'},
                ]"
            >
                <el-input
                    v-model="visit.topRooter"
                    placeholder="请输入"
                    style="width:400px"
                    :disabled="!permission"
                />
            </el-form-item>
            <el-form-item
                label="应用入口："
                prop="appEntry"
            >
                <el-checkbox-group
                    v-model="visit.appEntry"
                    :disabled="!permission"
                >
                    <el-checkbox
                        label="1"
                        disabled
                    >
                        应用导航
                    </el-checkbox>
                    <!-- <el-checkbox
                        label="2"
                        disabled
                    >
                        团队空间
                    </el-checkbox> -->
                </el-checkbox-group>
            </el-form-item>
            <el-form-item
                label="前端资源地址："
                prop="url"
                :rules="[
                    {required: true, message: '前端资源地址不能为空'},
                    { pattern: /(http|https):\/\/([\w.]+\/?)\S*/, message: '地址格式不对，必须以http://或https://开头', trigger: 'change'}
                ]"
                v-if="visit.accessMode == 1"
            >
                <el-input
                    v-model="visit.url"
                    placeholder="请输入"
                    style="width:400px"
                    :disabled="!permission"
                />
            </el-form-item>
            <el-form-item v-if="visit.accessMode == 1 && helpDoc">
                <div>
                    <span>您的应用需要按照 <el-link
                        type="primary"
                        :underline="false"
                        :href="`${$store.state.url.help}/#/Operation-Guide/appstore/jmodule?id=微前端`"
                        target="_blank"
                        style="vertical-align:baseline"
                    >微前端规范</el-link> 进行开发编译，这里需要配置打包编译后的资源地址。请保证地址可以正常访问</span>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    @click="submit"
                    :disabled="!permission || formUnChanged"
                >
                    保存
                </el-button>
            </el-form-item>
        </el-form>
        <div
            class="menu"
            v-if="visit.accessMode == 1"
        >
            <h4>内嵌菜单</h4>
            <div style="padding-left:100px;">
                <el-alert
                    title="注意：仅支持两级菜单设置，且菜单修改即生效"
                    type="warning"
                    style="font-size:12px"
                />
                <el-button
                    type="primary"
                    @click="addRootMenu"
                    class="root-menu"
                    :disabled="!permission"
                >
                    增加一级菜单
                </el-button>
                <el-tree
                    :data="menus"
                    highlight-current
                    draggable
                    @node-drag-end="doDrag"
                    node-key="id"
                    :expand-on-click-node="false"
                >
                    <span
                        class="custom-tree-node"
                        slot-scope="{ node, data }"
                    >
                        <span>{{ data.name }}</span>
                        <span>
                            <el-button
                                type="text"
                                size="mini"
                                @click="() => append(data)"
                                v-show="data.parentId === -1"
                            >
                                增加子菜单
                            </el-button>
                            <el-button
                                type="text"
                                size="mini"
                                @click="() => edit(node, data)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                type="text"
                                size="mini"
                                @click="() => doRemove(node, data)"
                                v-show="!(data.parentId === -1 && data.children && data.children.length)"
                            >
                                删除
                            </el-button>
                        </span>
                    </span>
                </el-tree>
            </div>
        </div>

        <el-dialog
            title="创建菜单"
            width="600px"
            :modal="true"
            :visible.sync="dialogCreateVisible"
        >
            <el-form
                id="#create"
                :model="create"
                ref="create"
                label-width="100px"
            >
                <el-form-item
                    label="菜单名称"
                    prop="name"
                >
                    <el-input v-model="create.name" />
                </el-form-item>
                <el-form-item
                    label="访问地址"
                    prop="url"
                >
                    <el-input v-model="create.url">
                        <template slot="prepend">{{ urlPrepend }}</template>
                    </el-input>
                </el-form-item>
            </el-form>
            <div
                slot="footer"
            >
                <el-button @click="dialogCreateVisible = false">取 消</el-button>
                <el-button
                    type="primary"
                    :loading="createLoading"
                    @click="doCreate"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>
        <el-dialog
            title="修改菜单信息"
            :modal="true"
            width="600px"
            :visible.sync="dialogUpdateVisible"
        >
            <el-form
                id="#update"
                :model="update"
                ref="update"
                label-width="100px"
            >
                <el-form-item
                    label="菜单名称"
                    prop="name"
                >
                    <el-input v-model="update.name" />
                </el-form-item>
                <el-form-item
                    label="访问地址"
                    prop="url"
                >
                    <el-input v-model="update.url">
                        <template slot="prepend">{{ urlPrepend }}</template>
                    </el-input>
                </el-form-item>
            </el-form>
            <div
                slot="footer"
            >
                <el-button @click="dialogUpdateVisible = false">取 消</el-button>
                <el-button
                    type="primary"
                    :loading="updateLoading"
                    @click="doUpdate"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { jacpSecurityHttp as http } from '@/plugins/http';
import devCenter from '../model/devCenter';
import cloneDeep from 'lodash/cloneDeep';
import { mapState } from 'vuex';

export default {
    props: {
        appId: [String, Number],
        permission: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            menus: undefined,
            create: {
                id: undefined,
                parentId: -1,
                type: 1,
                name: undefined,
                url: undefined,
                appId: undefined,
            },
            update: {
                id: undefined,
                parentId: undefined,
                type: 1,
                name: undefined,
                url: undefined,
                appId: undefined,
            },
            visit: {
                accessMode: '2',
                topRooter: undefined,
                appEntry: ['1'],
                url: undefined,
            },
            oldForm: {

            },
            dialogCreateVisible: false, // 创建对话框的显示状态
            dialogUpdateVisible: false, // 编辑对话框的显示状态
            createLoading: false,
            updateLoading: false,
            helpURL: '/openApi/detail/131/%E6%8E%A5%E5%85%A5%E8%A1%8C%E4%BA%91%E9%A2%84%E5%8F%91%2F%E7%94%9F%E4%BA%A7%E7%8E%AF%E5%A2%83.md',
        };
    },
    computed: {
        ...mapState({
            helpDoc: state => state.app.helpDoc,
        }),
    },
    mounted() {
        this.doQuery();
        this.init();
    },
    methods: {
        init() {
            devCenter.getVisitInfo(this.appId).then((res) => {
                if (res) {
                    this.visit = res;
                    if (!res.accessMode) {
                        this.$set(this.visit, 'accessMode', '2');
                    }
                    if (!res.appEntry) {
                        this.visit.appEntry = ['1'];
                    }
                }
                // this.visit.accessMode = '2';
                this.oldForm = cloneDeep(this.visit);
            });
        },
        addRootMenu() {
            this.dialogCreateVisible = true;
            this.create.parentId = -1;
            this.create.url = `/${this.$attrs.code}/`;
        },
        append(data) {
            this.create.parentId = data.id;
            this.dialogCreateVisible = true;
            this.create.url = `/${this.$attrs.code}/`;
        },
        edit(node, data) {
            this.update.id = data.id;
            this.update.name = data.name;
            this.update.url = data.url;
            this.update.appId = data.appId;
            this.dialogUpdateVisible = true;
        },
        doQuery() {
            // http.get(`${this.server}/app/${this.appId}/menu`).then(
            http.get(`/app/${this.appId}/menu`).then(
                (res) => {
                    this.menus = res;
                },
            );
        },
        doCreate() {
            // this.create.appId = this.appId;
            this.create.appId = this.appId;
            http.post('/menu', this.create).then(() => {
                this.dialogCreateVisible = false;
                this.doQuery();
            });
        },
        doUpdate() {
            this.update.appId = this.appId;
            http.put(`/menu/${this.update.id}`, this.update).then(
                () => {
                    this.$message.success('修改成功！');
                    this.dialogUpdateVisible = false;
                    this.updateLoading = false;
                    this.doQuery();
                },
            );
        },
        doRemove(node, data) {
            // if (data.parentId === -1 && data.children && data.children.length) {
            this.$confirm('确定删除该条子菜单吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '再想想',
                type: 'warning',
            }).then(() => {
                http.delete(`/menu/${data.id}`, data.name).then(
                    () => {
                        const { parent } = node;
                        const children = parent.data.children || parent.data;
                        const index = children.findIndex(d => d.id === data.id);
                        children.splice(index, 1);
                    },
                );
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除',
                });
            });
            // }
        },
        doDrag(draggingNode, dropNode, dropType) {
            http.put('/menu/change-seq', {
                curMenuId: draggingNode && draggingNode.data.id,
                targetMenuId: dropNode && dropNode.data.id,
                opt: dropType,
            });
        },
        submit() {
            this.$refs.visit.validate(async (valid) => {
                if (!valid) {
                    return;
                }
                devCenter.getBasicInfo(this.appId).then((res) => {
                    devCenter.updateVisitInfo({
                        ...this.visit, name: res.name, code: res.code, desc: res.desc, appId: this.appId,
                    }).then(() => {
                        this.$message.success('保存成功!');
                        this.oldForm = cloneDeep(this.visit);
                        this.$emit('save-com', this.formUnChanged);
                    });
                });
            });
        },
    },
    computed: {
        urlPrepend() {
            return window.location.origin;
        },
        formUnChanged() {
            // this.$emit('save-com', flag);
            return this.oldForm
                && (JSON.stringify(this.oldForm) === JSON.stringify(this.visit));
        },
    },
    watch: {
        appId: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.init();
                }
            },
        },
        visit: {
            handler() {
                console.log('aaaaaaa');
                this.$emit('save-com', this.formUnChanged);
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>
<style lang="less">
.app-row {
    padding: 5px 0px;
    .root-menu {
        margin: 20px 10px;
    }
    .menu {
        width: 800px;
        margin-top: 50px;
    }
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
        margin: 10px 0px;
        height: 30px;
    }
}
</style>
