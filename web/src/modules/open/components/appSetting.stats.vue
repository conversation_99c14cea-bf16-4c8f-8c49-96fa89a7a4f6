<template>
    <div
        class="app-stats"
    >
        <div class="app-stats-block">
            <label class="app-stats-time" style="margin-right: 5px">时间范围:</label>
            <el-date-picker
                v-model="daterange"
                type="daterange"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                @change="dateChange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
            />
        </div>
        <div class="app-stats-block">
            <el-row :gutter="12">
                <el-col :span="8">
                    总PV: <el-tag>{{ numberFormat(pv) }}</el-tag>
                </el-col>
                <el-col :span="8">
                    总UV: <el-tag>{{ numberFormat(uv) }}</el-tag>
                </el-col>
                <el-col :span="8">
                    人气值(日均UV): <el-tag type="warning">{{ numberFormat(uvAvg) }}</el-tag>
                </el-col>
            </el-row>
        </div>
        <div class="app-stats-block">
            <div id="app-stats-charts" class="app-stats-chart" />
        </div>
    </div>
</template>

<script>
import moment from 'moment';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import DevCenterModel from '../model/devCenter';

const { use } = echarts;
use([
    CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent,
]);

export default {
    props: {
        appId: [String, Number],
        code: [String],
    },
    data() {
        return {
            daterange: [moment().subtract(6, 'day').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
            pv: 0,
            uv: 0,
            uvAvg: 0,
            charts: undefined,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(moment().subtract(6, 'day').valueOf());
                        picker.$emit('pick', [start, end]);
                    },
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        const v = moment().subtract(1, 'month');
                        start.setTime(v.valueOf());
                        picker.$emit('pick', [start, end]);
                    },
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(moment().subtract(3, 'month').valueOf());
                        picker.$emit('pick', [start, end]);
                    },
                }],
            },
            chartOptions: {
                legend: {
                    data: ['pv', 'uv'],
                },
                tooltip: { trigger: 'axis' },
                xAxis: {
                    type: 'category',
                    data: [],
                },
                yAxis: [
                    {
                        type: 'value',
                    }],
                series: [
                    { name: 'pv', type: 'line', data: [] },
                    { name: 'uv', type: 'line', data: [] },
                ],
                grid: {
                    left: '10%',
                    right: '4%',
                    bottom: '25%',
                    top: '5%',
                },
            },
        };
    },
    methods: {
        init() {
            this.queryAppStats();
        },
        dateChange(time) {
            this.daterange = time;
            this.queryAppStats();
        },
        numberFormat(value) {
            let formated = value;
            if (value > 10000) {
                formated = `${(value * 1.0 / 10000).toFixed(2)} 万`;
            }
            return formated;
        },
        queryAppStats() {
            DevCenterModel.getAppStats({
                appCode: this.code,
                startTime: this.daterange[0],
                endTime: this.daterange[1],
            }).then(({ data }) => {
                if (data.code !== 200) {
                    this.uv = 0;
                    this.pv = 0;
                }
                const stats = data.data;
                this.pv = stats.pv || 0;
                this.uv = stats.uv || 0;
                this.uvAvg = stats.popularValue || 0;
                const charts = echarts.init(document.getElementById('app-stats-charts'), 'theme');
                this.chartOptions.xAxis.data = (stats.pvList || []).map(x => x.dt);
                this.chartOptions.series[0].data = (stats.pvList || []).map(x => x.pv);
                this.chartOptions.series[1].data = (stats.uvList || []).map(x => x.uv);
                charts.setOption(this.chartOptions);
            });
        },
    },
    watch: {
        appId: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.init();
                }
            },
        },
    },
};
</script>

<style lang="less">
.app-stats {
    width: 632px;
}

.app-stats-time {
    font-size: 14px;
}

.app-stats-block {
  margin-bottom: 10px;
  font-size: 12px;
}

.app-stats-chart{
    margin-top: 32px;
    width:100%;
    height:400px;
    padding:0px;
}
</style>
