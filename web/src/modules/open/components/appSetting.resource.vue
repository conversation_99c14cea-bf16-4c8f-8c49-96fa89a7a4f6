<template>
    <div class="setting-resource">
        <!-- <div
            class="el-alert el-alert--warning is-light"
            style="font-size: 14px; margin: 16px 0;"
        >
            <i
                class="el-alert__icon el-icon-warning"
                style="margin-right: 4px;"
            />
            您的应用需要按照
            <a
                style="text-decoration: underline; padding: 0 4px;"
                :href="helpURL"
                target="_blank"
            >微前端规范</a>
            进行开发编译，这里需要配置打包编译后的资源地址。一定要保证该资源地址可以正常访问！
        </div> -->
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="right"
            label-width="100px"
        >
            <el-form-item
                prop="resource"
                label="文档地址:"
            >
                <el-input
                    class="j-input"
                    v-model="form.resource"
                    placeholder="请输入"
                    :disabled="!permission"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    class="jacp-button"
                    style="margin-top: 16px;"
                    type="primary"
                    size="small"
                    :disabled="!permission || formUnChanged"
                    @click="save"
                >
                    保存
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import DevCenterModel from '../model/devCenter';

const resourceReg = /(http|https):\/\/([\w.]+\/?)\S*/;
// const resourceReg = /^((https?):\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*([:\d]+)*?\/?/;
const rules = {
    resource: {
        type: 'string',
        // required: true,
        validator: (rule, value, callback) => {
            // if (!value || !value.trim()) {
            //     return callback(new Error('资源地址不能为空'));
            // }
            if (!resourceReg.test(value)) {
                return callback(new Error('请输入合法的资源地址，必须以http://或https://开头'));
            }
            return callback();
        },
    },
};

export default {
    props: {
        appId: [String, Number],
        permission: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            rules,
            form: {
                resource: undefined,
            },
            oldForm: {},
            helpURL: '/openApi/detail/131/%E6%8E%A5%E5%85%A5%E8%A1%8C%E4%BA%91%E9%A2%84%E5%8F%91%2F%E7%94%9F%E4%BA%A7%E7%8E%AF%E5%A2%83.md',
        };
    },
    computed: {
        formUnChanged() {
            // this.$emit('save-com', flag);
            console.log('aaaaaaaaa');
            return this.oldForm
                && (JSON.stringify(this.oldForm) === JSON.stringify(this.form));
        },
    },
    methods: {
        init() {
            DevCenterModel.getHelpAddress(this.appId).then((data) => {
                this.form.resource = data;
                this.oldForm = { ...this.form };
            });
        },
        save() {
            this.$refs.form.validate((valid) => {
                if (!valid) {
                    return;
                }
                DevCenterModel.saveResource(this.appId, this.form.resource).then(() => {
                    this.$message({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.oldForm = { ...this.form };
                    this.$emit('save-com', this.formUnChanged);
                });
            });
        },
    },
    watch: {
        appId: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.init();
                }
            },
        },
        'form.resource': {
            handler() {
                this.$emit('save-com', this.formUnChanged);
            },
        },
    },
};
</script>

<style lang="less">
.setting-resource {
    width: 900px;
}
</style>
