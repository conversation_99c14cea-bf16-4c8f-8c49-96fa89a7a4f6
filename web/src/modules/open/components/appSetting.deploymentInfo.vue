<template>
    <div class="deploymentInfo">
        <div class="deploymentInfo-title">
            <label for="">版本：</label>
            <el-select
                :disabled="!permission"
                v-model="curVersion"
                placeholder="请选择"
            >
                <el-option
                    v-for="item in versionList"
                    :key="item.id"
                    :label="item.num"
                    :value="item.id"
                />
            </el-select>
        </div>
        <div
            class="deploymentInfo-explain"
            v-if="helpDoc"
        >
            <!-- el-icon-info -->
            <i class="el-icon-info myicon" />
            <i class="desc">接入步骤请参考</i>
            <a
                class="link"
                :href="`${$store.state.url.help}/#/Operation-Guide/appstore/deploy_access_standard`"
            >接入文档</a>
        </div>
        <div class="deploymentInfo-content">
            <el-tabs
                v-model="activeName"
                @tab-click="handleClick"
                v-if="curVersion"
            >
                <el-tab-pane
                    label="交付镜像"
                    name="first"
                >
                    <payment-Image
                        v-if="activeName=='first'&&initFlag"
                        :permission="permission"
                        :cur-version="curVersion"
                        v-bind="$attrs"
                    />
                </el-tab-pane>
                <el-tab-pane
                    label="部署步骤"
                    name="second"
                >
                    <deployment-steps
                        v-if="activeName=='second'&&initFlag"
                        :permission="permission"
                        :cur-version="curVersion"
                        v-bind="$attrs"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import paymentImage from './paymentImage';
import deploymentSteps from './deploymentSteps';
// eslint-disable-next-line no-underscore-dangle
import DevCenterModel from '../model/devCenter';
import { mapState } from 'vuex';

export default {
    components: {
        paymentImage, deploymentSteps,
    },
    props: {
        permission: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            initFlag: true,
            activeName: 'first',
            // eslint-disable-next-line vue/no-dupe-keys
            curVersion: '',
            versionList: [],
        };
    },
    computed: {
        ...mapState({
            helpDoc: state => state.app.helpDoc,
        }),
    },
    mounted() {
        this.init();
    },
    watch: {
        '$attrs.code': {
            handler(newV, oldV) {
                if (newV !== oldV) {
                    this.initFlag = false;
                    this.init();
                }
            },
            deep: true,
        },
    },
    methods: {
        handleClick(tab, event) {
            console.log(tab, event);
        },
        init() {
            DevCenterModel.getVersionList(this.$attrs.code).then((res) => {
                if (res.data.code === 200) {
                    this.versionList = res.data.data;
                    if (this.versionList.length > 0) {
                        this.curVersion = this.versionList[0].id;
                    }
                    this.initFlag = true;
                } else if (res.data.code === 400) {
                    this.$message.error(res.data.message);
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
/*  */
.deploymentInfo {
    padding: 20px;
    &-title {
        margin-bottom: 10px;
        label {
            font-size: 14px;
        }
    }
    &-explain {
        max-width: 1000px;
        display: flex;
        align-items: center;
        border-radius: 6px;
        padding: 10px;
        color: #319afd;
        background: #fffbe6;
        border: 1px #ffedb0 solid;
        .desc {
            color: #69685f;
            font-size: 12px;
            margin: 0 20px 0 10px;
        }
        .link {
           font-size: 14px;
        }
    }
    &-content {
         max-width: 1000px;
    }
}
</style>
