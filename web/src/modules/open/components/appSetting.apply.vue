<template>
    <div style="width: 100%">
        <el-row
            type="flex"
            justify="space-between"
            align="middle"
        >
            <span style="color: #303133; font-size: 14px; font-weight: 600;">申请记录</span>
            <el-button
                class="jacp-button"
                type="primary"
                size="small"
                @click="applyApp"
            >
                <!-- :disabled="disabled" -->
                申请上架
            </el-button>
        </el-row>
        <el-table
            :data="tableData"
        >
            <el-table-column
                prop="creator"
                label="申请人"
            >
                <template
                    slot-scope="scope"
                >
                    {{ scope.row.creator ? `${scope.row.creator.name}(${scope.row.creator.erp})` : '-' }}
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="申请状态"
            >
                <template
                    slot-scope="scope"
                >
                    {{ APPLY_STATUS_MAP[scope.row.status] }}
                </template>
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="申请时间"
            />
            <el-table-column
                prop="reviewTime"
                label="上架时间"
                :formatter="(row, column, cellValue) => (cellValue || '-')"
            />
            <el-table-column
                prop="reason"
                label="申请结果"
                :formatter="(row, column, cellValue) => (cellValue || '-')"
            />
        </el-table>
    </div>
</template>

<script>
import DevCenterModel from '../model/devCenter';
import { APPLY_STATUS_MAP } from '../constant';

export default {
    props: {
        appId: [String, Number],
    },
    data() {
        return {
            tableData: [],
            APPLY_STATUS_MAP,
        };
    },
    computed: {
        // disabled() {},
    },
    methods: {
        init() {
            DevCenterModel.getApplyInfo(this.appId).then((data = []) => {
                this.tableData = data;
            });
        },
        applyApp() {
            this.$confirm('确认信息无误，申请上架', '确认提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                confirmButtonClass: 'jacp-button',
                cancelButtonClass: 'jacp-button',
                customClass: 'j-message-box j-message-box--small',
            })
                .then(() => DevCenterModel.applyApp(this.appId))
                .then(() => {
                    // this.tableData.push(data);
                    this.init();
                    this.$emit('refresh-list');
                });
        },
    },
    watch: {
        appId: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.init();
                }
            },
        },
    },
};
</script>
