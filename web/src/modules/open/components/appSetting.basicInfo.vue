<template>
    <div
        class="basic-info"
        style="width: 632px;"
    >
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="right"
            label-width="100px"
        >
            <el-form-item
                prop="name"
                label="名称："
            >
                <el-input
                    class="j-input"
                    v-model="form.name"
                    :placeholder="rules.name.placeholder"
                    :disabled="!permission"
                />
            </el-form-item>
            <el-form-item
                prop="icon"
                label="应用图标："
            >
                <el-row type="flex">
                    <el-col class="basic-info__icon">
                        <img
                            v-if="form.icon"
                            :src="form.icon"
                        >
                        <span
                            v-else
                            class="basic-info__icon-placeholder"
                        >
                            <i class="el-icon-camera-solid" />
                        </span>
                    </el-col>
                    <el-col>
                        <Upload
                            :upload-url="uploadIconUrl"
                            @success="handleIconUploaded"
                            :before-upload="beforeAvatarUpload"
                            :pic-card="picCard"
                            v-if="permission"
                            :headers="getHeader"
                        >
                            <el-button
                                class="jacp-button"
                                :disabled="!permission"
                            >
                                上传图标
                            </el-button>
                        </Upload>
                        <div class="basic-info__form-tip">请上传PNG格式、256*256px以上、1:1、5MB以内的无圆角图标</div>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item
                prop="code"
                label="标识："
            >
                <el-input
                    class="j-input"
                    v-model="form.code"
                    :disabled="!permission"
                />
            </el-form-item>
            <el-form-item
                v-if="jurisdicList['archAppManage:app:third:add'] === 0"
                label="类型："
            >
                <!-- <el-radio v-model="form.appType" label='1'>扩展应用</el-radio> -->
                <el-checkbox
                    :disabled="!permission"
                    v-model="appTypeTemp"
                >
                    外采应用
                </el-checkbox>
                <!-- <el-radio-group
                    v-model="form.appType"
                    @change="handleChange"
                    :disabled="!permission"
                >
                    <el-radio-button
                        label="2"
                    >
                        自研应用
                    </el-radio-button>
                    <el-radio-button
                        label="1"
                        :disabled="disabled"
                    >
                        第三方
                    </el-radio-button>
                </el-radio-group> -->
            </el-form-item>
            <!-- <el-form-item
                prop="appOrgName"
                label="部门："
                v-if="form.appType"
                :rules="[
                    { required: false, message: '部门不能为空'},
                ]"
            >
                <el-input
                    style="width: 248px;"
                    class="j-input"
                    v-model="form.appOrgName"
                    :disabled="!permission"
                />
            </el-form-item> -->
            <el-form-item
                prop="appOrgStr"
                label="部门："
                :rules="[
                    { required: false, message: '部门不能为空'},
                ]"
            >
                <el-select
                    v-model="form.appOrgStr"
                    filterable
                    clearable
                    :filter-method="appOrgNameMethod"
                    placeholder="请选择"
                    :loading="loading"
                    :disabled="!permission"
                >
                    <el-option
                        v-for="item in form.appOrgNameOptions"
                        :key="item.id"
                        :label="item.fullName"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                label="负责人："
                prop="appUserRelRequest"
            >
                <el-select
                    v-model="form.appUserRelRequest"
                    filterable
                    multiple
                    clearable
                    :filter-method="filterMethod"
                    placeholder="请选择"
                    @change="appChange"
                    @remove-tag="removeTag"
                    :disabled="!permission"
                >
                    <el-option
                        v-for="item in form.appUserRelRequestListOptions"
                        :key="item.erp"
                        :label="item.user"
                        :value="item.erp"
                        class="option"
                    >
                        <div style="height:40px">
                            <span style="display: inline-block;">{{ item.user }}</span>
                            <span style="display: block; color: #ccc; font-size:14px">{{ item.orgTierName }}</span>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                prop="appLabels"
                label="标签："
            >
                <el-select
                    v-model="form.appLabels"
                    multiple
                    filterable
                    placeholder="请输入关键词"
                    :filter-method="remoteMethodLabel"
                    :loading="loading"
                    :disabled="!permission"
                >
                    <el-option
                        v-for="item in form.tagsOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item
                prop="desc"
                label="描述："
            >
                <el-input
                    class="j-input"
                    v-model="form.desc"
                    type="textarea"
                    :rows="7"
                    :placeholder="rules.desc.placeholder"
                    :disabled="!permission"
                />
            </el-form-item>
            <el-form-item>
                <el-button
                    style="margin-top: 16px;"
                    class="jacp-button"
                    type="primary"
                    size="small"
                    :disabled="!permission || formUnChanged"
                    @click="save"
                >
                    保存
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep';
import { event } from '@/plugins/event';
import Upload from '@/components/Upload';
// import ImagePreviewer from '@/components/ImagePreviewer';
import DevCenterModel from '../model/devCenter';
import Users from '@/models/user';
import adminOption from '@/models/adminOption';
// import devCenter from '../model/devCenter';
import { mapState } from 'vuex';

// const helpUrlReg = /^(https?):\/\/.*?/;
const rules = {
    icon: {
        required: true,
        message: '应用图标不能为空',
    },
    name: {
        required: true,
        min: 2,
        max: 10,
        trigger: ['change', 'blur'],
        placeholder: '请输入',
        message: '可输入2-10个字符',
    },
    desc: {
        required: true,
        max: 200,
        placeholder: '请输入备注',
        message: '最多不可超过200个字符',
    },
    code: {
        required: true,
        message: '标识不能为空',
    },
    // appType: {
    //     // required: true,
    //     // message: '类型是必选的',
    // },
    appUserRelRequest: {
        required: true,
        message: '负责人是必选的',
    },
};

const baseData = {
    name: undefined,
    icon: undefined,
    tag: undefined,
    appType: false,
    appOrgName: undefined,
    appOrgStr: undefined,
    code: undefined,
    desc: undefined,
    introducePics: [],
    techSupportNumber: undefined,
    owner: {},
    ownerOptions: [],
    appLabels: [],
    tagsOptions: [],
    appOrgNameOptions: [],
    appUserRelRequest: [],
    appUserRelRequestList: [],
    appUserRelRequestListOptions: [],
};
export default {
    components: {
        Upload,
        // ImagePreviewer,
    },
    props: {
        appId: [String, Number],
        permission: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            appTypeTemp: false,
            form: {
                name: undefined,
                icon: undefined,
                tag: undefined,
                appType: 1,
                appOrgName: undefined,
                appOrgStr: undefined,
                code: undefined,
                desc: undefined,
                introducePics: [],
                techSupportNumber: undefined,
                owner: {},
                ownerOptions: [],
                appLabels: [],
                tagsOptions: [],
                appOrgNameOptions: [],
                appUserRelRequest: [],
                appUserRelRequestList: [],
                appUserRelRequestListOptions: [],
            },
            oldForm: {},
            rules,
            loading: false,
            disabled: false,
            saveDisable: false,
            picCard: 'picture-card',
        };
    },
    computed: {
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
        }),
        formUnChanged() {
            return this.oldForm
                && (JSON.stringify(this.oldForm) === JSON.stringify(this.form));
        },
        uploadIconUrl() {
            return `${window.location.origin}/devops-api/arch/api/v1/appInfo/${this.appId}/file?type=icons`;
        },
        getHeader() {
            return { Authorization: localStorage.getItem('Authorization') };
        },
    },
    mounted() {
    },
    methods: {
        async init() {
            console.log('init--- this.appId', this.appId);
            await DevCenterModel.getBasicInfo(this.appId).then((data) => {
                this.form = Object.assign({}, baseData, data);
                // this.oldForm = cloneDeep(this.form);
                this.form.appLabels = [];
                this.form.appUserRelRequest = [];
                data.appLabelRelList.forEach((item) => {
                    this.form.appLabels.push(item.appLabelId);
                });
                this.form.appUserRelRequest = [];
                this.form.appUserRelRequestListOptions = [];
                this.form.appUserRelRequestList = [];
                data.appUserRelList.forEach((item) => {
                    const user = `${item.userName}(${item.userAccount})`;
                    this.form.appUserRelRequest.push(item.userAccount);
                    this.form.appUserRelRequestListOptions.push({ erp: item.userAccount, user });
                });
                data.appUserRelList.forEach((item) => {
                    this.form.appUserRelRequestList.push({
                        userAccount: item.userAccount,
                        userName: item.userName,
                    });
                });
                // if (data.appType) { // 如果 类型选中 就不能修改
                //     this.disabled = true;
                // } else {
                //     this.disabled = false;
                // }
                if (data.appType === 1) {
                    this.appTypeTemp = true;
                } else {
                    this.appTypeTemp = false;
                }
                // devCenter.isAdmin(1).then((res) => {
                //     if (!res) {
                //         this.disabled = true;
                //     }
                // });
                this.remoteMethodLabel();
                this.filterMethod();
                this.appOrgNameMethod();
            }).then(() => {
                this.oldForm = cloneDeep(this.form);
            });
        },
        handleIconUploaded(url) {
            this.form.icon = url;
        },
        handlePreviewUploaded(url) {
            this.form.introducePics.push(url);
        },

        beforeAvatarUpload(file) {
            const isJPG = file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 5;

            if (!isJPG) {
                this.$message.error('上传头像图片只能是 PNG 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传头像图片大小不能超过 5MB!');
            }
            return isJPG && isLt2M;
        },
        save() {
            this.$refs.form.validate(async (valid) => {
                if (!valid) {
                    return Promise.reject();
                }
                // if (this.form.appType === '2') {
                if (this.appTypeTemp === false) {
                    const data = await adminOption.getOrgList({ keyWord: this.form.appOrgStr });
                    data.records.forEach((item) => {
                        if (item.id === this.form.appOrgStr) {
                            this.form.appOrgName = item.name;
                        }
                    });
                }
                const params = cloneDeep(this.form);
                if (this.appTypeTemp) {
                    params.appType = 1;
                } else {
                    params.appType = 2;
                }
                return DevCenterModel.saveBasicInfo(this.appId, params).then(() => {
                    this.$message({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.oldForm = cloneDeep(this.form);
                    event.$emit('appSetting.basicInfo:change', this.oldForm);
                    this.$emit('save-com', this.formUnChanged);
                });
            });
        },
        remoteMethodLabel(name = '') {
            DevCenterModel.getLabelInfoByName(name).then((res) => {
                this.form.tagsOptions = res;
                this.oldForm = cloneDeep(this.form);
            });
        },
        filterMethod(key = '') {
            Users.search(key).then((res) => {
                res.forEach((item) => {
                    item.user = `${item.userName}(${item.erp})`;
                });
                // this.App.appUserRelRequestListOptions = res;
                this.$set(this.form, 'appUserRelRequestListOptions', res);
                this.oldForm = cloneDeep(this.form);
            });
        },
        remoteMethodOwer(name) {
            console.log(name);
        },
        handleChange() {
            this.form.appOrgName = undefined;
        },
        appOrgNameMethod(key) {
            console.log('-- ~ file: appSetting.basicInfo.vue:458 ~ key:', key);
            adminOption.getOrgList({ keyWord: key }).then((res) => {
                console.log('-- ~ file: appSetting.basicInfo.vue:460 ~ res:', res);
                this.$set(this.form, 'appOrgNameOptions', res.records);
                this.oldForm = cloneDeep(this.form);
            });
        },
        appChange(value) {
            this.form.appUserRelRequestListOptions.forEach((item) => {
                value.forEach((item1) => {
                    if (item1 === item.erp && !this.form.appUserRelRequestList.some(item2 => item2.userAccount === item.erp)) {
                        this.form.appUserRelRequestList.push({
                            userAccount: item1,
                            userName: item.userName,
                        });
                    }
                });
            });
            console.log(this.form.appUserRelRequestList);
        },
        removeTag(tag) {
            console.log(tag);
            this.form.appUserRelRequestList.forEach((item, index) => {
                if (item.userAccount === tag) {
                    this.form.appUserRelRequestList.splice(index, 1);
                }
            });
        },
    },
    watch: {
        appId: {
            immediate: true,
            handler(val) {
                if (val) {
                    console.log('this.appId', this.appId);
                    this.init();
                }
            },
        },
        form: {
            handler() {
                console.log('watch - form');
                this.$emit('save-com', this.formUnChanged);
            },
            deep: true,
            // immediate: true,
        },
        appTypeTemp: {
            handler(value) {
                if (value) {
                    this.form.appType = 1;
                } else {
                    this.form.appType = 2;
                }
            },
        },
    },
};
</script>

<style lang="less">
.basic-info {
.el-input__inner,.el-textarea__inner {width: 376px;}
    &__icon {
        width: 64px;
        height: 64px;
        margin-right: var(--gutter--medium);
        & > span,
        & > img {
            width: inherit;
            height: inherit;
        }
        span {
            display: flex;
            justify-content: center;
            align-items: center;
            background: var(--color--base--hr);
        }
        i {
            font-size: 24px;
            color: var(--color--arrow-base);
        }
    }
    &__form-tip {
        color: var(--color--secondary--content);
        font-size: var(--font-size--description);
    }
    &__preview {
        & > * {
            display: inline;
        }
    }
    .el-upload--picture-card {
        width: 128px;
        height: 80px;
        /* line-height: 80px; */
        display: inline-flex;
        justify-content: center;
        align-items: center;
    }
}
.option {
    height:80px;
}
</style>
