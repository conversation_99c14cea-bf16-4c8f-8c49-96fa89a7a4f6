<template>
    <div class="audit">
        <el-form
            label-position="right"
            label-width="100px"
            :model="authorInfo"
        >
            <el-form-item
                label="客户："
                prop="customerName"
            >
                <span>{{ authorInfo.customerName }}</span>
            </el-form-item>
            <el-form-item
                label="serverID："
                prop="serverID"
            >
                <span>{{ authorInfo.serverIds }}</span>
            </el-form-item>
            <el-form-item
                label="license："
                prop="license"
            >
                <el-input
                    type="textarea"
                    rows="12"
                    class="license-box"
                    v-model="authorInfo.content"
                    disabled
                />
                <!-- <span>{{ authorInfo.license }}</span> -->
            </el-form-item>
            <el-form-item
                label="license类型："
                prop="edition"
            >
                <span>{{ authorInfo.edition }}</span>
            </el-form-item>
            <el-form-item
                label="状态："
                prop="status"
            >
                <span>{{ authorInfo.status }}</span>
            </el-form-item>
            <el-form-item
                label="有效期至："
                prop="expireTime"
            >
                <span>{{ authorInfo.expireTime }}</span>
            </el-form-item>
            <el-form-item
                label="剩余可用时间："
                prop="leftDays"
            >
                <span>{{ authorInfo.leftDay }}</span>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import devCenter from '../model/devCenter';

export default {
    props: {
        code: String,
    },
    data() {
        return {
            authorInfo: {
                status: '未授权',
                edition: '-',
                expireTime: '-',
                leftDays: '-',
            },
        };
    },
    created() {
        this.init();
    },
    methods: {
        init() {
            devCenter.searchGrantAuthor(this.code).then((res) => {
                if (res) {
                    this.authorInfo = res;
                    res.serverIds = res.serverIds.toString(',');
                    res.expireTime = this.date(res.expireTime);
                    this.authorInfo.status = res.leftDays <= 0 ? '已过期' : '使用中';
                    this.authorInfo.leftDay = this.computeLeftDay(res.leftDays);
                }
            });
        },
        computeLeftDay(day) {
            const year = day / 365 >= 1 ? `${Math.floor(day / 365)}年` : '';
            const mounth = day % 365 !== 0 ? `${day % 365}天` : '';
            return year + mounth;
        },
        date(times) {
            const date = new Date(times);
            const y = date.getFullYear();
            let m = date.getMonth() + 1;
            m = m < 10 ? (`0${m}`) : m;
            let d = date.getDate();
            d = d < 10 ? (`0${d}`) : d;
            // const h = date.getHours();
            // let minute = date.getMinutes();
            // minute = minute < 10 ? (`0${minute}`) : minute;
            // return `${y}-${m}-${d} ${h}:${minute}`;
            return `${y}-${m}-${d}`;
        },
    },
    watch: {
        code: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.init();
                }
            },
        },
    },
};
</script>

<style lang='less'>
.license-box{
    width: 50%;
}
</style>
