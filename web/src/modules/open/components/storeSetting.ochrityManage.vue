<template>
    <div class="ochrity-manage">
        <h5 class="header" v-if="jurisdicList['archLicenseManage:license:view'] === 0">授权管理</h5>
        <div class="btns">
            <el-tooltip
                v-if="addVisible"
                effect="dark"
                :content="tips1"
                placement="top">
                <el-button
                    class="disabled"
                    type="primary"
                >
                    批量修改授权信息
                </el-button>
            </el-tooltip><el-tooltip
                v-else-if="jurisdicList['archLicenseManage:license:update'] !== 0"
                effect="dark"
                :content="tips"
                placement="top">
                <el-button
                    class="disabled"
                    type="primary"
                >
                    批量修改授权信息
                </el-button>
            </el-tooltip>

            <el-button
                v-else
                type="primary"
                @click="alledit"
            >
                批量修改授权信息
            </el-button>
        </div>
        <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%;margin-top:15px"
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                width="55"
            />
            <el-table-column
                label="应用名"
                prop="appName"
            />
            <el-table-column
                prop="appCode"
                label="应用标识"
            />
            <!-- <el-table-column
                prop="status"
                label="状态"
            >
                <template slot-scope="scope">
                    <div class="state" v-html="stateList[scope.row.status]()" />
                </template>
            </el-table-column> -->
            <el-table-column
                prop="expireTime"
                label="失效时间"
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.expireTime">
                        {{ scope.row.expireTime | jacp-local-time }}
                    </span>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="120"
            >
                <template slot-scope="scope">
                    <span
                        v-if="jurisdicList['archLicenseManage:license:update'] === 0"
                        style="color:#2695F1;cursor: pointer;"
                        @click="handleEdit(scope.$index, scope.row)"
                    >修改授权信息</span>
                    <el-tooltip v-else effect="dark" :content="tips" placement="top">
                        <span style="color:#c0c4cc;cursor: not-allowed;">
                            修改授权信息
                        </span>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>
        <!-- 弹框 -->
        <el-dialog
            :title=" all===true ? '批量修改授权信息' : '修改授权信息'"
            :visible.sync="dialogFormVisible"
            class="o-m-dialog"
        >
            <el-form :model="licenseContent">
                <el-form-item>
                    <div>授权应用</div>
                    <el-input disabled :value="licenseContent.applist" />
                </el-form-item>
                <el-form-item>
                    <div>license</div>
                    <div>
                        <el-input
                            type="textarea"
                            v-model="licenseContent.license"
                            rows="4"
                        />
                    </div>
                </el-form-item>
            </el-form>
            <div
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="close">取 消</el-button>
                <el-button
                    type="primary"
                    @click="submitLicense"
                >
                    提 交
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// import UserModel from '@/models/user';
import devCenter from '../model/devCenter';
import { mapState } from 'vuex';
import { MessageBox } from 'element-ui';

// const stateList = {
//     0: () => '<span class="up">上架</span>',
//     1: () => '<span class="prepare">准备中</span>',
//     2: () => '<span class="dn">下架</span>',
// };
/**
 * color: #c0c4cc;
    cursor: not-allowed;
 * */

export default {
    data() {
        return {
            // stateList,
            dialogFormVisible: false,
            multipleSelection: [],
            licenseContent: {
                license: '',
                applist: '',
            },
            visible: false,
            tableData: [
                // {
                //     appName: '测试管理',
                //     appCode: 'testMassge',
                //     expireTime: '2023-09-01 00:00:00',
                // },
            ], // 数据
            all: false,
        };
    },
    computed: {
        addVisible() {
            if (this.multipleSelection.length) {
                return false;
            }
            return true;
        },
        tips1() {
            // if (!this.visible) {
            //     return '您没有管理权限,请联系管理员！';
            // }
            if (!this.multipleSelection.length) {
                return '请选择要修改的应用';
            }
            return false;
        },
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            tips: state => state.option.tips,
        }),
    },
    created() {
        // 判断当前用户是否管理员/商店管理员 是的话可以修改license
        // UserModel.getMenuAuthor().then((data) => {
        //     if (data.admin.length > 0) {
        //         this.visible = true;
        //     }
        // });
        // Promise.all([devCenter.isAdmin(1), devCenter.isAdmin(2)]).then((res) => {
        //     if (res.includes(true)) {
        //         this.visible = true;
        //     }
        // });
        // 获取页面信息
        this.getDetail();
    },
    mounted() {
        if (this.jurisdicList['archLicenseManage:license:view'] !== 0) {
            MessageBox({
                title: '提示',
                message: '您没有权限，如有诉求，请联系管理员',
                type: 'error',
            });
            this.$router.push({path: '/'});
        }
    },
    methods: {
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        alledit() {
            this.dialogFormVisible = true;
            if (this.multipleSelection.length === 0) {
                this.licenseContent.applist = '';
            } else {
                const keys = [];
                this.multipleSelection.forEach((i) => {
                    keys.push(i.appCode);
                });
                this.licenseContent.applist = keys.join('、');
            }
            this.all = true;
        },
        // 单个
        handleEdit(i, row) {
            this.licenseContent.applist = row.appCode;
            console.log(i, row);
            this.all = false;
            this.dialogFormVisible = true;
        },
        getDetail() {
            devCenter.licenseList().then((res) => {
                console.log('==res==', res);
                if (res) {
                    this.tableData = res;
                }
            });
        },
        submitLicense() {
            if (!this.licenseContent.applist) {
                this.$message.warning('请选择要编辑的应用');
                return;
            }
            // 应用code
            const appCodes = this.licenseContent.applist.split('、');
            devCenter.uploadContent({ appCodes, content: this.licenseContent.license }).then(() => {
                this.$message.success('修改成功');
                this.getDetail();
                this.dialogFormVisible = false;
                this.licenseContent.license = '';
            });
        },
        close() {
            this.dialogFormVisible = false;
            this.licenseContent.license = '';
        },
    },
};
</script>

<style lang="less">
.ochrity-manage {
    .el-textarea__inner {
        width: 48%;
    }
    .header {
        display: flex;
        height: 40px;
        padding: 0;
        margin: 0;
        line-height: 40px;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 500;
        color: rgba(48,49,51,1);
    }
    .btns{
        margin-top: 12px;
        height: 32px;
        .el-button{
            border-radius: 6px;
        }
        .disabled{
            color: #fff;
            background-color: #93caf8;
            border-color: #93caf8;
            cursor: not-allowed;
            background-image: none;
        }
        .el-icon-question{
            margin-left: 5px;
        }
    }
    .licenseApp {
        margin-bottom: 50px;
        .el-form-item--small.el-form-item{
            margin-bottom: 10px;
        }
    }
    .state{
        overflow: hidden;
        .up::before{
            display: inline-block;
            content: '';
            border-radius: 50%;
            margin-right: 5px;
            width: 8px;
            height: 8px;
            background: rgba(73,201,157,1);
        }
        .dn::before{
            display: inline-block;
            content: '';
            border-radius: 50%;
            margin-right: 5px;
            width: 8px;
            height: 8px;
            background: rgba(144,163,174,1);
        }
        .prepare::before{
            display: inline-block;
            content: '';
            border-radius: 50%;
            margin-right: 5px;
            width: 8px;
            height: 8px;
            background: rgba(38,149,241,1);
        }
    }
    .o-m-dialog {
        .el-dialog__header{
            border-bottom: 1px solid rgba(235,238,245,1);
            padding: 10px;
        }
        .el-dialog__body {
            padding: 14px 20px;
        }
        .el-textarea__inner {
            width: 100%;
        }
    }
}
</style>
