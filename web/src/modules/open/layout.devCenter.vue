<template>
    <div class="jacp-open">
        <div
            class="dev-center-header__app-wrapper"
            v-if="$route.name === 'jacpDevAppSetting'"
        >
            <jacp-icon-back
                style="margin-left: 16px;"
                @click.native="toDevCenter"
            />
            <jacp-dropdown
                style="margin:0 16px;"
                menu-align="top"
                :data="appList"
                :value="currentApp.id"
                @change="switchApp"
            />
            <span
                style="white-space: nowrap;margin-right: 6px"
                class="dev-center-header__app-status"
                v-if="currentApp.status"
            >{{ APP_STATUS_MAP[currentApp.status] }}</span>
        </div>
        <router-view
            :status="APP_STATUS_MAP[currentApp.status]"
            :app-id="currentApp.id"
            :app-list="appList"
            :permission="currentApp.permission && currentApp.permissions.length > 0
                && !currentApp.permissions.some(item => item === 'offline-app') && currentApp.auditStatus !== 1"
            @refresh-list="init"
        />
    </div>
</template>

<script>
import { openLink } from '@/plugins/utils';
import DevCenterModel from './model/devCenter';
import { APP_STATUS_MAP } from './constant';

export default {
    data() {
        return {
            appList: [],
            APP_STATUS_MAP,
            event: this.$initEvent(),
        };
    },
    computed: {
        currentApp() {
            return this.appList.find(app => app.code === this.$route.params?.code) || {};
        },
    },
    created() {
        this.init();
        this.event.$on('appSetting.basicInfo:change', (app) => {
            const target = this.appList.find(item => item.id === app.id);
            // 保存以后同步显示一下
            if (target) {
                Object.assign(target, app);
            }
        });
    },
    methods: {
        init() {
            DevCenterModel.searchAllAppInfoList().then((appList = []) => {
                this.appList = appList.filter(item => !item.authApp);
            });
        },
        popup(link) {
            openLink(link);
        },
        switchApp(e) {
            this.$router.push({
                params: { code: e.code, appType: e.appType },
                query: { appType: e.appType },
            });
        },
        toDevCenter() {
            this.$router.push({ name: 'jacpAppStore' });
            this.init();
        },
    },
};
</script>

<style lang="less">
.dev-center-header {
    &__app-status {
        font-size: var(--font-size--description);
        background: var(--color--base--hr);
        padding: var(--gutter--mini) var(--gutter--small);
        border-radius: var(--radius--large);
        color: var(--color--secondary--content);
    }
    &__app-wrapper{
        width: 240px;
        background: #FAFBFC;
        padding-top: var(--gutter--medium);
        display: flex;
        overflow: hidden;
        align-items: center;
    }
}
</style>
