<template>
    <main class="devcenter-setting">
        <div class="devcenter-setting-menu">
            <h4 style="padding-left:30px">商店设置</h4>
            <!-- <div class="devcenter-setting-menu__label">{{ menuBlock.label }}</div> -->
            <div
                v-for="(menuItem, itemIndex) in menuList"
                :key="'menuItem' + itemIndex"
                :class="[
                    'devcenter-setting-menu__item',
                    activeMenu === menuItem.value ? 'is-active' : '',
                ]"
                @click="switchMenu(menuItem)"
            >
                {{ menuItem.label }}
            </div>
        </div>
        <div class="devcenter-setting-view">
            <!-- <keep-alive> -->
            <component
                v-on="$listeners"
                :is="settingComponent"
                :app-id="appId"
                :code="code"
            />
            <!-- </keep-alive> -->
        </div>
    </main>
</template>

<script>
import { mapState } from 'vuex';
import { getPermissionUicomponents } from '@/modules/option/modules';

const menuList = [{
    label: '标签管理',
    value: 'labelMessage',
}, {
    label: '授权管理',
    value: 'ochrityManage',
},
// {
//     label: '入口管理',
//     value: 'entrance',
// },
];

export default {
    inheritAttrs: false,
    components: {
        labelMessage: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/storeSetting.label'),
        entrance: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/storeSetting.entrance'),
        ochrityManage: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/storeSetting.ochrityManage'),
    },
    props: {
        appId: [String, Number],
        code: String,
    },
    data() {
        return {
            menuList,
            activeMenu: menuList[0].value,
        };
    },
    async mounted() {
        await getPermissionUicomponents();
        if (this.jurisdicList['archLabelManage:label:view'] !== 0) {
            this.menuList = this.menuList.filter(item => item.value !== 'labelMessage');
            this.activeMenu = menuList[1].value;
        }
        if (this.jurisdicList['archLicenseManage:license:view'] !== 0) {
            this.menuList = this.menuList.filter(item => item.value !== 'ochrityManage');
        }
        if (!this.$store.state.app.serviveConfig.license) {
            this.menuList = this.menuList.filter(item => item.value !== 'ochrityManage');
        }
    },
    computed: {
        settingComponent() {
            return this.activeMenu;
        },
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
        }),
    },
    methods: {
        async switchMenu({ value }) {
            await getPermissionUicomponents();
            this.activeMenu = value;
        },
    },
};
</script>

<style lang="less" scoped>
.devcenter-setting {
    display: flex;
    flex: 1;
}
.devcenter-setting-menu {
    min-width: 240px;
    background: #FAFBFC;
    padding: var(--gutter--medium) 0;
    // &__label {
    //     color: var(--color--secondary--content);
    //     font-size: var(--font-size--description);
    //     line-height: var(--height--menu-item);
    //     padding-left: var(--gutter--medium);
    // }
    &__item {
        color: var(--color--regular--content);
        font-size: var(--font-size--content);
        line-height: var(--height--menu-item);
        margin: 0 var(--gutter--small);
        padding: 0 var(--gutter--medium);
        cursor: pointer;
        &:hover,
        &.is-active {
            color: var(--color--base--content);
            background: var(--color--base--background);
            font-weight: var(--font-weight-bold);
            border-radius: var(--radius--medium);
        }
    }
}
.devcenter-setting-view {
    flex: 1;
    // width: 632px;
    // margin-top: -48px;
    padding: var(--gutter--medium) var(--gutter--large);
    .el-table th>.cell {
        font-weight: var(--font-weight-default);
    }
}
</style>
