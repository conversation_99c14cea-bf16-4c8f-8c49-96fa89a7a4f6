<template>
    <div class="jacp-open">
        <section class="open-section-header">
            <header class="open-header">
                <div class="open-header__left">
                    <i
                        class="jacp-icon-navi_tab_logo_expansion logo"
                        @click="$router.push('/')"
                    />
                    <!-- <span
                        class="open-header__title"
                        @click="openLink(leftLink)"
                    >{{ leftLink.name }}</span> -->
                    <slot name="left">
                        <LocalTabsMenu
                            class="j-mgl16"
                            slot="left"
                            :menu="menu"
                            v-if="menu"
                        />
                    </slot>
                </div>
                <div class="open-header__right">
                    <div class="jacp-root-header__banner">
                        <!-- <img
                            src="http://storage.jd.com/jacp.attachment/app_store/banner/ad_entry_v2.gif"
                            @click="() => openLink({ route: { path: '/open/appOps' },isPopup: true})"
                        > -->
                    </div>
                    <a
                        v-for="(link, index) in rightLinks"
                        :key="index"
                        class="open-header__link j-link"
                        style="cursor: pointer; padding-left: 16px;"
                        @click.prevent="openLink(link)"
                    >
                        <i
                            v-if="link.icon"
                            :class="link.icon"
                        />
                        {{ link.name }}
                    </a>
                    <jacp-user :data="userInfo" />
                </div>
            </header>
        </section>
        <slot />
        <router-view class="section-main" />
        <section class="open-section-footer">
            <!-- <footer class="open-footer">
                <div>
                    <span style="font-size: 16px;">联系方式：</span>
                    <a
                        class="plain"
                        :href="joinChat(1021883739)"
                    >
                        咚咚群 1021883739
                    </a>
                </div>
                <div>
                    {{ copyright }}
                </div>
            </footer> -->
        </section>
    </div>
</template>

<script>
import { openMenu } from '@/modules/root/models/menu';
import LocalTabsMenu from '@/modules/root/components/tabsMenu';
import store from '$platform.store';
import JacpUser from '@/components/headerMenu/User';
import { openLink } from '@/plugins/utils';
import { joinChat } from '@/utils/timline';
import { User } from '@/models/user';
import { mapState } from 'vuex';
import { getPermissionUicomponents } from '@/modules/option/modules';
import AppServer from '@/modules/root/models/appServer';

export default {
    components: { JacpUser, LocalTabsMenu },
    props: {
        // link: { name, route, isPopup }
        leftLink: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            menu: openMenu,
            copyright: process.env.VUE_APP_COPYRIGHT,
            rightLinks: [],
        };
    },
    computed: {
        userInfo() {
            return store.state.user;
        },
        ...mapState({
            jurisdicList: state => state.option.jurisdictionList,
            helpDoc: state => state.app.helpDoc,
        }),
    },
    methods: {
        openLink,
        joinChat,
    },
    mounted() {
        getPermissionUicomponents().then((res) => {
            if (res['archLabelManage:label:view'] === 0 || res['archLicenseManage:license:view'] === 0) {
                this.rightLinks = [
                    {
                        // icon: 'jacp-icon-kaifazhehoutai',
                        // icon: 'jacp-icon-a-icon-appopen',
                        icon: 'el-icon-setting',
                        // name: '开发者后台',
                        name: '商店设置',
                        route: { name: 'jacpDevCenter' },
                        isPopup: false,
                    }];
            } else {
                this.rightLinks = [];
            }
        });
        User.getBaseUrl().then(async () => {
            await AppServer.getServiceConfig();
            if (!this.helpDoc) {
                const data = this.menu.menus.filter(item => item.name !== '开放文档');
                this.$set(this.menu, 'menus', data);
                // this.menu.menus = data;
            } else {
                const url = `${this.$store.state.url.help}/#/Operation-Guide/appstore/open_platform`;
                this.menu.menus[1].clickParams.url = url;
            }
        });
        getPermissionUicomponents().then(() => {
            if (this.jurisdicList['archAppManage:view'] !== 0) {
                this.menu.menus = this.menu.menus.filter(item => item.name !== '应用管理');
            }
        });
    },
};
</script>

<style lang="less">
.jacp-open {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 1280px;

    section {
        width: 100%;
        & > * {
            width: 1128px;
            margin: 0 auto;
        }
    }

    .section-main {
        min-height: calc(100vh - 57px);
        padding-bottom: 48px;;
    }
    .open-section-header {
        border-bottom: 1px solid var(--color--base--hr);
    }
    .open-section-footer {
        position: absolute;
        bottom: 0;
        background: #FAFBFC;
    }

    .open-header {
        height: 56px;
        width: auto;
        margin: 0 var(--gutter--large);
        display: flex;
        justify-content: space-between;
        align-items: center;
        & > * {
            display: flex;
            align-items: center;
        }
        &__left .logo {
            color: var(--color--primary);
            font-size: 24px;
        }
        &__right {
            .jacp-user {
                transform: translateY(2px);
                margin-left: 14px;
            }
            .jacp-root-header__banner{
                img{
                    width: 160px;
                }
            }
        }
        &__title {
            padding-left: var(--gutter--medium);
            color: var(--color--base--content);
            font-weight: var(--font-weight-bold);
            cursor: pointer;
        }
        &__link {
            color: var(--color--base--content);
            font-size: var(--font-size--content);
            cursor: pointer;
            i {
                color: var(--color--secondary--content);
            }
            &:hover {
                color: var(--color--primary--hover);
            }
        }
    }

    .open-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 48px;
        color: var(--color--secondary--content);
        font-size: var(--font-size--content);
    }
}
</style>
