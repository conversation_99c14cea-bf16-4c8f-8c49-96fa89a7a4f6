<template>
    <main class="devcenter-setting">
        <div class="devcenter-setting-menu">
            <!-- <div class="devcenter-setting-menu__label">{{ menuBlock.label }}</div> -->
            <div
                v-for="(menuItem, itemIndex) in menuList"
                :key="'menuItem' + itemIndex"
                :class="[
                    'devcenter-setting-menu__item',
                    activeMenu === menuItem.value ? 'is-active' : '',
                ]"
                @click="switchMenu(menuItem)"
            >
                {{ menuItem.label }}
            </div>
        </div>
        <div class="devcenter-setting-view">
            <!-- <keep-alive> -->
            <component
                v-on="$listeners"
                :is="settingComponent"
                :app-id="appId"
                :permission="permission"
                :code="code"
                @save-com="saveCom"
            />
            <!-- </keep-alive> -->
        </div>
    </main>
</template>

<script>
const initMenu = [{
    label: '基本信息',
    value: 'basicInfo',
}, {
    label: '访问信息',
    value: 'appMenu',
}, {
    label: '文档信息',
    value: 'resource',
},
];

export default {
    inheritAttrs: false,
    components: {
        basicInfo: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.basicInfo'),
        resource: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.resource'),
        entrance: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.entrance'),
        appMenu: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.menu'),
        apply: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.apply'),
        stats: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.stats'),
        audit: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.audit'),
        deploymentInfo: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.deploymentInfo'),
        grantAuthor: () => import(/* webpackChunkName: "chunk-appSettig" */'./components/appSetting.grantAuthor'),
    },
    props: {
        status: [String, Number],
        appId: [String, Number],
        code: String,
        permission: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            menuList: [],
            menu1: [ // 1外采应用：基本信息、访问信息、文档信息、部署信息、授权信息
                {
                    label: '部署信息',
                    value: 'deploymentInfo',
                }],
            menu2: [ // 2研发应用：基本信息、访问信息、文档信息、审核信息
                {
                    label: '审核信息',
                    value: 'audit',
                }],
            activeMenu: initMenu[0].value,
            flag: true,
        };
    },
    mounted() {
        // appType  2----研发应用  1---外采应用
        if (this.$route.query.appType == 1) {
            this.menuList = [...initMenu, ...this.menu1];
            // this.menuList.push(this.menu1);
            if (this.code === 'archDemand') {
                this.menuList.push({
                    label: '授权信息',
                    value: 'grantAuthor',
                });
            }
        } else {
            this.menuList = [...initMenu, ...this.menu2];
            // this.menuList.push(this.menu2);
        }
    },
    watch: {
        $route(to) {
            if (to.params.appType === 1) {
                this.menuList = [...initMenu, ...this.menu1];
                if (to.params.code === 'archDemand') {
                    this.menuList.push({
                        label: '授权信息',
                        value: 'grantAuthor',
                    });
                } else if (this.menuList.slice(-1)[0].value === 'grantAuthor') {
                    this.menuList.pop();
                }
            } else {
                this.menuList = [...initMenu, ...this.menu2];
            }
            this.activeMenu = 'basicInfo';
        },
    },
    computed: {
        settingComponent() {
            return this.activeMenu;
        },
    },
    methods: {
        // eslint-disable-next-line consistent-return
        switchMenu({ value }) {
            if (value === 'deploymentInfo' && this.status === '已上架') {
                this.$message.warning('已上架应用不可编辑！');
                return false;
            }
            if (!this.flag) {
                this.$confirm('当前页面有未保存数据，确认离开？').then(() => {
                    this.activeMenu = value;
                    this.flag = true;
                });
            } else {
                this.activeMenu = value;
            }
        },
        saveCom(flag) {
            this.flag = flag;
        },
    },
};
</script>

<style lang="less" scoped>
.devcenter-setting {
    display: flex;
    flex: 1;
}
.devcenter-setting-menu {
    min-width: 240px;
    background: #FAFBFC;
    padding: var(--gutter--medium) 0;
    // &__label {
    //     color: var(--color--secondary--content);
    //     font-size: var(--font-size--description);
    //     line-height: var(--height--menu-item);
    //     padding-left: var(--gutter--medium);
    // }
    &__item {
        color: var(--color--regular--content);
        font-size: var(--font-size--content);
        line-height: var(--height--menu-item);
        margin: 0 var(--gutter--small);
        padding: 0 var(--gutter--medium);
        cursor: pointer;
        &:hover,
        &.is-active {
            color: var(--color--base--content);
            background: var(--color--base--background);
            font-weight: var(--font-weight-bold);
            border-radius: var(--radius--medium);
        }
    }
}
.devcenter-setting-view {
    flex: 1;
    // width: 632px;
    // margin-top: -48px;
    padding: var(--gutter--medium) var(--gutter--large);
    .el-table th>.cell {
        font-weight: var(--font-weight-default);
    }
}
</style>
