import { DemandGroupModel, DemandGroupType } from '@/models/demandGroup';

const CODE = {
    NO_PERMISSION: 400,
    ...DemandGroupType,
};
export default {
    state: {
        list: '',
        groups: null,
        // hasGroupEditorPrivileges: false, // 是否有编辑组员的权限
        // isCooGroup: false, // 当前显示的group是否是协同分组
        apportionTabVisible: false,
        apportionCount: 0,
        processors: [],
    },
    getters: {
        demandsGroupInvolvedMine({ groups }) {
            return (groups || []).filter(o => +o.category === CODE.MY_DEFAULT
            || +o.category === CODE.MY_COO);
        },
        // 把我的分组整合到select的options里
        /*  demandsGroupOptions(state, { demandsGroupInvolvedMine }) {
            return demandsGroupInvolvedMine.map(o => Object.assign({}, o, {
                name: o.groupName,
                id: o.groupId,
            }));
        }, */
    },
    mutations: {
        demands_list_update(state, data) {
            Object.assign(state, {
                list: data || [],
            });
        },
        demands_group_update(state, data) {
            Object.assign(state, {
                groups: data || [],
            });
        },
        demands_processors_update(state, data) {
            Object.assign(state, {
                processors: data || [],
            });
        },
        demands_group_add({ groups }, data) {
            groups.splice(0, 0, data);
            return data;
        },
        demands_group_edit({ groups }, item) {
            groups.forEach((group, index) => {
                if (group.groupId === item.groupId) {
                    groups.splice(index, 1, Object.assign(group, item));
                }
            });
            return item;
        },
        demands_group_delete({ groups }, groupId) {
            groups.forEach((group, index) => {
            // 协同分组不能被删除
                if (+group.category === CODE.MY_COO) {
                    return;
                }
                if (+group.groupId === +groupId) {
                    groups.splice(index, 1);
                }
            });
        },
        demands_update(state, data) {
            Object.assign(state, data);
        },
    },
    actions: {
        demands_list_update({ commit }, data) {
            commit('demands_list_update', data);
        },
        demands_group_update({ commit }, data) {
            commit('demands_group_update', data);
        },
        demands_group_fetchData({ commit }) {
            return DemandGroupModel.getGroups().then((data) => {
                commit('demands_group_update', data);
                return data;
            });
        },
        demand_group_detach({ dispatch }, { groupId, demandId }) {
            return DemandGroupModel.detachFromGroup({
                groupId,
                demandId,
            }).catch((error) => {
                if (error.response.data.code === CODE.NO_PERMISSION) {
                    dispatch('demands_group_fetchData');
                }
                throw error;
            });
        },
        demand_group_join({ dispatch }, { groupId, demandId }) {
            return DemandGroupModel.joinInGroup({
                groupId,
                demandId,
            }).catch((error) => {
                if (error.response.data.code === CODE.NO_PERMISSION) {
                    dispatch('demands_group_fetchData');
                }
                throw error;
            });
        },
        async demand_group_save({ commit }, { groupId, groupName }) {
            let newItem;
            if (groupId) {
                newItem = await DemandGroupModel.editGroup({ groupId, groupName });
                commit('demands_group_edit', newItem);
            } else {
                newItem = await DemandGroupModel.addGroup(groupName);
                commit('demands_group_add', newItem);
            }
            return newItem;
        },
        demands_group_delete({ commit }, { category, groupId }) {
            // 后端接口根据cateId，（0，2，1）判定是删除分组/取关分组
            return DemandGroupModel.deleteGroup({
                cateId: category,
                groupId,
            })
                .then(() => commit('demands_group_delete', groupId))
                .catch(() => {});
        },
    },
};
