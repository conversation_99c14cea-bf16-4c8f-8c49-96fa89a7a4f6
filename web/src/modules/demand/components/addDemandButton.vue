<template>
    <!-- 新建 -->
    <el-tooltip
        :disabled="authority.valid || !authority.erp"
        placement="left"
        effect="light"
    >
        <div
            v-if="!authority.valid"
            slot="content"
        >
            有疑问请联系管理员：
            <jacp-erp
                :data="authority"
                :disable-timline="isCurrentUser(authority.erp)"
                :key="authority.erp"
                style="position: relative; top: -2px;"
            />
            进行开通
        </div>
        <div>
            <el-button
                size="small"
                type="primary"
                class="jacp-button--mini jacp-button--gradient"
                circle
                icon="el-icon-plus"
                @click="goto('demandAdd')"
                :disabled="!authority.valid"
            />
        </div>
    </el-tooltip>
</template>
<script>
import { isCurrentUser } from '@/plugins/utils';

export default {
    name: 'AddDemandButton',
    props: {
        authority: { type: Object, default: () => ({}), required: true },
    },
    methods: {
        isCurrentUser,
        goto(urlName) {
            this.$router.push({ name: urlName });
        },
    },
};
</script>
