<template>
    <div class="demand-detail-actions">
        <jacp-button
            v-for="(action, index) in actions"
            :key="action.cmd"
            :type="isHighlightButton(action, index) ? 'primary' : 'default'"
            :plain="index !== 0"
            :loading="action.loading"
            :disabled="action.disabled"
            size="small"
            @click="handleAction(action)"
        >
            {{ action.text }}
        </jacp-button>
        <i
            v-show="actions.length && $slots.extra"
            class="demand-detail-actions__separator"
        />
        <slot name="extra" />
    </div>
</template>

<script type="text/javascript">
export default {
    props: {
        actions: {
            type: Array,
            default: () => [],
        },
        actionHandle: {
            type: Function,
            default: Promise.resolve,
        },
        isHighlightButton: {
            type: Function,
            default: (action, index) => index === 0,
        },
    },
    methods: {
        handleAction(action) {
            /* eslint-disable no-param-reassign */
            action.disabled = true;
            const handleResult = this.actionHandle(action);
            if (handleResult instanceof Promise) {
                handleResult.finally(() => {
                    action.disabled = false;
                });
            } else {
                action.disabled = false;
            }
        },
    },
};
</script>
<style lang="less">
.demand-detail-actions__separator{
    margin: 0 16px;
    &::before{
        content: ' ';
        display: inline-block;
        height: 32px;
        width: 1px;
        background: #f5f5f5;
    }
}
</style>
