<script>
import debounce from 'lodash/debounce';
import DemandProject from '$module/models/demandProject';

/* 在特殊范围的需求内查询需求关联的项目，以api和参数区分条件 */
export default {
    name: 'DemandRelatedProjectsSelector',
    inheritAttrs: false,
    inject: ['dynamicForm'],
    props: {
        value: { type: [Array, String], default: () => [] },
        multiple: { type: Boolean, default: true },
        api: { type: String, default: '' },
    },
    data() {
        return { options: [], searchStr: '' };
    },
    mounted() {
        this.getOptions();
    },
    computed: {
        model() {
            return this.dynamicForm.getReaciveModel();
        },
        apiPath() {
            if (!this.api) {
                return [];
            }
            return this.api.split('/');
        },
        orgnazitionAvaliable() {
            return this.model.orgnazitionId;
        },
        queryOptionsParams() {
            const { model } = this;
            if (this.apiPath.includes('org')) {
                return {
                    orgCode: model.orgnazitionId,
                };
            }
            return {};
        },
        filteredOptions() {
            if (!this.searchStr) {
                return this.options;
            }
            return this.options.filter(item => item.fullLabel.toLowerCase().includes(this.searchStr.toLowerCase()));
        },
    },
    render() {
        return (
            <el-select
                on={{ ...this.$listeners }}
                props={{
                    ...this.$attrs,
                    ...this.$props,
                    filterMethod: debounce(this.filterMethod.bind(this), 200),
                }}

            >
                {this.filteredOptions
                    .map(option => <el-option
                        key={`option-${option.value}`}
                        {...{
                            props: {
                                ...option,
                            },
                        }}>
                        <div style={{
                            display: 'flex',
                            'justify-content': 'space-between',
                        }}>
                            <span>{option.label}</span>
                            <jacp-text style="margin-left: 24px;" size="12" type="disable">{option.code}</jacp-text>
                        </div>

                    </el-option>)}
            </el-select>
        );
    },
    methods: {
        getOptions() {
            if (!this.api) {
                return;
            }
            if (this.apiPath.includes('org') && !this.orgnazitionAvaliable) {
                return;
            }
            DemandProject.getScopedProject(this.api, this.queryOptionsParams)
                .then((data) => {
                    this.options = data.map(project => ({
                        value: project.pmpProjectId,
                        label: project.pmpProjectName,
                        code: project.pmpProjectCode,
                        fullLabel: `${project.pmpProjectName}${project.pmpProjectCode}`,
                    }));
                });
        },
        filterMethod(keyword) {
            this.searchStr = keyword;
        },
    },
    watch: {
        'model.orgCombination': {
            handler(val) {
                if (!val || !val.orgnazitionId) {
                    return;
                }
                this.getOptions();
            },
        },
    },
};
</script>
