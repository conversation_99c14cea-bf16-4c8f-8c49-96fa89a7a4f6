<template>
    <div>
        <!-- 创建分享链接 -->
        <el-dialog
            :title="title"
            append-to-body
            custom-class="demand-group__dialog"
            :visible.sync="dialogShareListVisible"
        >
            <jacp-demand-group-share-list
                :data="shareList"
                :group-data="groupData"
            />
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="dialogShareListVisible = false">取消</el-button>
                <el-button
                    type="primary"
                    @click="getShareUrl"
                >创建分享链接</el-button>
            </span>
        </el-dialog>
        <!-- 分享详情 -->
        <el-dialog
            width="30%"
            :title="title"
            append-to-body
            :visible.sync="dialogShareUrlVisible"
        >
            <el-row>
                <p class="demand-group__desc">
                    分组名： {{ groupData.groupName }}
                </p>
                <span style="color: green;"><i class="el-icon-success j-mgr8" />成功创建分享链接</span>
            </el-row>
            <el-row class="j-mgr16">
                <el-col :span="18">
                    <el-input
                        type="text"
                        readonly
                        :value="groupData.shortUrl"
                        id="shareUrlEl"
                    />
                </el-col>
                <el-col :span="6">
                    <el-button
                        v-j-copy
                        data-clipboard-target="#shareUrlEl"
                        type="primary"
                    >
                        复制链接
                    </el-button>
                </el-col>
            </el-row>
            <el-row>
                <p class="demand-group__desc">
                    可将该链接分享给其他人
                </p>
            </el-row>
        </el-dialog>
    </div>
</template>
<script>
import { DemandGroupModel } from '@/models/demandGroup';
import JacpDemandGroupShareList from './demandGroupShareList';

export default {
    name: 'DemandGroupShare',
    components: {
        JacpDemandGroupShareList,
    },
    props: {
        title: String,
    },
    data() {
        const { protocol } = window.location;
        return {
            groupData: {},
            dialogShareListVisible: false,
            dialogShareUrlVisible: false,
            shareList: [],
            protocol,
        };
    },
    methods: {
        fetchData() {
            const { groupId } = this.groupData;
            if (!groupId) {
                return;
            }
            DemandGroupModel.getGroupList(groupId, {
                pageSize: 10000,
            }).then(({ records }) => {
                this.shareList = records;
                this.dialogShareListVisible = true;
            });
        },
        getShareUrl() {
            const { groupId } = this.groupData;
            DemandGroupModel.getShareUrl(groupId).then((data) => {
                Object.assign(this.groupData, data);
                this.dialogShareUrlVisible = true;
                this.dialogShareListVisible = false;
            });
        },
        show(groupData = {}) {
            this.groupData = groupData;
            this.fetchData();
        },
    },
};
</script>
<style lang="less">
.demand-group__dialog{
    position: absolute;
    margin-top: 0 !important;
    top: 50%;
    left: 50%;
    margin: 0;
    transform: translate(-50%, -50%);
    border-radius: 0;
    & .el-dialog__header {
        border-bottom: 1px solid #EBEEF5;
        padding: 12px 16px;
        position: relative;
        & > span {
            font-size: 16px;
        }
        & > .el-dialog__headerbtn {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    & .el-dialog__footer {
        padding: 16px;
        .dialog-footer > .el-button {
            border-radius: 6px;
        }
    }
    & .el-dialog__body {
        padding: 0 16px;
        & .demand-group__table {
            max-height: 400px;
            overflow-y: auto;
        }
    }
}
</style>
