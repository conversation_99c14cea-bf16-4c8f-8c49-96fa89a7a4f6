<template>
    <div class="demand-group">
        <slot name="desc">
            <p class="demand-group__desc">
                分组名： {{ groupData.groupName }}
            </p>
        </slot>
        <el-table
            :data="data"
            :show-header="showHeader"
            class="demand-group__table"
            stripe
        >
            <el-table-column property="name">
                <span
                    class="j-text-overflow"
                    slot-scope="scope"
                >{{ scope.row.name }}</span>
            </el-table-column>
            <el-table-column
                property="confidential"
                v-if="hasConfidential"
                width="80"
            >
                <span
                    class="demand-group__item--red"
                    slot-scope="scope"
                    v-if="scope.row.confidential"
                >
                    保密
                </span>
            </el-table-column>
            <el-table-column
                property="status"
                width="80"
            >
                <span slot-scope="scope">{{ scope.row.status|jacp-status-text }}</span>
            </el-table-column>
            <el-table-column
                property="bizStatusArchiveTime"
                width="120"
            >
                <span slot-scope="scope">
                    持续时间 {{ scope.row.bizStatusArchiveTime | getContinueTime }} 天
                </span>
            </el-table-column>
            <el-table-column
                property="submitTime"
                width="200"
            >
                <span slot-scope="scope">提交时间 {{ scope.row.submitTime|jacp-local-time }}</span>
            </el-table-column>
        </el-table>
        <slot name="footer" />
    </div>
</template>
<script>
export default {
    name: 'JacpDemandGroupShareList',
    props: {
        data: {
            type: Array,
            default: () => ([]),
        },
        groupData: Object,
        showHeader: Boolean,
        owner: String,
    },
    filters: {
        getContinueTime(time) {
            return Math.max(Math.ceil((new Date() - time) / (24 * 3600000)), 0) || 0;
        },
    },
    computed: {
        hasConfidential() {
            return this.data.some(o => o.confidential);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.demand-group{
    &__item--red{
        color: @redColor;
    }
}
</style>
