<template>
    <div class="demand-group-show-cooinfo">
        <el-dialog
            class="show-cooinfo-dialog"
            :title="title"
            width="464px"
            :visible.sync="dialogShowinfoVisible"
            :close-on-click-modal="false"
        >
            <div
                v-if="group.owner"
                class="demand-group-creator"
            >
                <div class="group-tag">分组创建人:</div>
                <div class="group-content">
                    <jacp-erp
                        :data="{erp: group.owner.userErp, name: group.owner.userName}"
                        :disable-timline="$utils.isCurrentUser({erp: group.owner.userErp})"
                        :key="group.owner.userErp"
                        display-erp-mode="block"
                        class="is-stopPropagation"
                    />
                </div>
            </div>
            <div
                class="demand-group-members"
                v-if="members.length !== 0"
            >
                <div class="group-tag">成员：</div>
                <div class="group-content">
                    <el-tag
                        class="members-mgr__item--checked"
                        size="small"
                        v-for="item in members"
                        :key="item.userErp"
                    >
                        {{ item.userName }}
                    </el-tag>
                </div>
            </div>
            <div class="demand-group-footer">
                <el-button
                    style="float: right;"
                    @click="dialogShowinfoVisible = false"
                >
                    我知道了
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    props: {
        title: { type: String },
    },
    data() {
        return {
            group: {},
            members: [],
            dialogShowinfoVisible: false,
        };
    },
    methods: {
        show(group, cooperators) {
            this.group = group;
            this.members = cooperators;
            this.dialogShowinfoVisible = true;
        },
    },
};
</script>

<style lang="less">
.demand-group {
    &-show-cooinfo {
        .show-cooinfo-dialog {
            & > .el-dialog {
                border-radius: 0;
                & .el-dialog__header {
                    border-bottom: 1px solid #EBEEF5;
                    padding: 12px 16px;
                    // display: flex;
                    // justify-content: space-between;
                    position: relative;
                }
                & .el-dialog__body {
                    padding: 0;
                }
                & .el-dialog__title {
                    color: #303133;
                    font-weight: 600;
                    font-size: 16px;
                }
                & .el-dialog__headerbtn{
                    position: absolute;
                    right: 16px;
                    top: 50%;
                    transform: translateY(-50%);
                    & .el-dialog__close {
                        color: #C0C4CC;
                    }
                }
            }
        }
    }
    &-creator {
        margin: 16px 16px 0 16px;
    }
    &-members {
        margin: 24px 16px 16px 16px;
    }
}
.demand-group-footer {
    height: 64px;
    margin-right: 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    & > .el-button {
        border-radius: 4px;
    }
}

.group-tag {
    display: inline-block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #303133;
}
.group-content {
    min-height: 32px;
    padding-bottom: 4px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    & > span {
        margin: 4px 4px 0 4px;
    }
}
</style>
