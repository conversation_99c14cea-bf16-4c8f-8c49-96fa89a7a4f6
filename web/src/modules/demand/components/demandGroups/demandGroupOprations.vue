<template>
    <span class="demand-group__opration">
        <i
            :class="['el-icon-share', { hidden: isFollowedGroup(item) }]"
            @click.stop="$emit('on-share')"
        />
        <i
            :class="['el-icon-edit', { hidden: !isEditable(item) }]"
            @click.stop="$emit('on-edit')"
        />
        <i
            :class="['el-icon-delete', { hidden: isMyCOOGroup(item) }]"
            @click="$emit('on-del')"
        />
    </span>
</template>
<script>
import { DemandGroupType as CODE } from '@/models/demandGroup';

export default {
    name: 'DemandGroupOprations',
    props: {
        item: Object,
    },
    methods: {
        isEditable({ category }) {
            return Number(category) === 0;
        },
        isMyCOOGroup({ category }) {
            return +category === CODE.MY_COO;
        },
        isFollowedGroup({ category }) {
            return +category === CODE.FOLLOWED;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.demand-group__opration{
    float: right;
    flex-grow: 0;
    [class*="el-icon-"]{
        &:hover{
            color: @primaryColor;
        }
        padding: 5px 8px;
        display: inline-block;
        &.hidden{
            visibility: hidden;
            pointer-events: none;
        }
    }
    text-align: left;
}
</style>
