<template>
    <el-select
        clearable
        :popper-append-to-body="false"
        placeholder="请选择"
        :value="value"
        v-bind="[$attrs]"
        v-on="$listeners"
        @input="$emit('input', $event)"
    >
        <el-option
            ref="demandGroupAddEl"
            class="demand-group__add"
            @click.native="addGroup"
            disabled
            value="add"
            v-if="addable"
        >
            新增分组
        </el-option>
        <el-option
            v-for="item in demandsGroupInvolvedMine"
            :key="item.groupId"
            :label="item.groupName"
            :value="item.groupId"
        >
            <span
                class="demand-group__text"
            ><span
                class="demand-group__text--emphasize"
                v-if="isCooGroup(item)"
            >协</span>{{ item.groupName }}</span>
        </el-option>
    </el-select>
</template>
<script>
import demandGroupList from './demandGroupList';
import { sortMyGroupList } from './utils.demandGroup';

export default {
    extends: demandGroupList,
    inheritAttrs: false,
    mounted() {
        // 防止点选
        this.$refs.demandGroupAddEl.$el.classList.remove('is-disabled');
    },
    props: {
        addable: {
            type: Boolean,
            default: true,
        },
        value: {
            type: [String, Number],
        },
    },
    computed: {
        demandsGroupInvolvedMine() {
            return sortMyGroupList(this.$store.getters.demandsGroupInvolvedMine);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.demand-group__add.el-select-dropdown__item{
    cursor: pointer;
    font-size: 12px;
    .highlight();
    margin-bottom: 8px;
    overflow: visible;
    &:after{
        content: ' ';
        display: block;
        position: absolute;
        bottom: -4px;
        width: calc(~"100% - 40px");
        height: 1px;
        background: @borderColor;
        padding-right: 4px;
    }
}
</style>
