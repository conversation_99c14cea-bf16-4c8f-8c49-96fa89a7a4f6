<template>
    <el-popover
        ref="popoverEl"
        @show="$emit('on-toggle-visible', true)"
        @hide="$emit('on-toggle-visible', false)"
        popper-class="demand-group__wrapper"
        :reference="reference"
        trigger="click"
        v-clickoutside="hide"
    >
        <ul class="demand-group__list">
            <li
                class="demand-group__item el-dropdown-menu__item"
                v-if="currentGroupCate === 'my'"
                @click="addGroup"
            >
                新建分组
            </li>
            <li
                class="demand-group__item el-dropdown-menu__item is-disabled"
                v-if="!list.length"
            >
                无
            </li>
            <li
                class="demand-group__item el-dropdown-menu__item"
                :class="{
                    'el-dropdown-menu__item--divided': index === 0 && currentGroupCate === 'my'
                }"
                v-for="(item, index) in list"
                :key="index"
            >
                <span
                    class="demand-group__text"
                    @click="handleGroupClick(item)"
                >
                    <span
                        class="demand-group__text--emphasize"
                        v-if="isCooGroup(item) || hasCooGroup(item)"
                    >协</span>{{ item.groupName }}</span>
                <demand-group-oprations
                    v-if="opration"
                    :item="item"
                    @on-share="shareGroup(item)"
                    @on-edit="editGroup(item)"
                    @on-del="deleteGroupConfirm(item)"
                />
            </li>
        </ul>
        <demand-group-share
            ref="shareEl"
            v-if="opration"
            :title="dialogTitle.share"
        />
        <div slot="reference">
            <slot name="reference" />
        </div>
    </el-popover>
</template>
<script>
import trim from 'lodash/trim';
import Clickoutside from 'element-ui/src/utils/clickoutside';
import { groupCateLabelMap, DemandGroupType as CODE } from '@/models/demandGroup';
import DemandGroupOprations from './demandGroupOprations';
import DemandGroupShare from './demandGroupShare';
import { sortMyGroupList, isCooGroup } from './utils.demandGroup';

export default {
    name: 'DemandGroupList',
    components: { DemandGroupOprations, DemandGroupShare },
    props: {
        demandId: [Number, String],
        currentGroupCate: {
            type: String,
            default: 'my',
        },
        opration: {
            type: Boolean,
            default: false,
        },
        // eslint-disable-next-line
        reference: {},
        isOpening: Boolean,
        dialogConfirmButtonText: String,
    },
    data() {
        return {
            dialogTitle: this.$t('jacp.demandGroup.dialogTitle'),
        };
    },
    directives: { Clickoutside },
    mounted() {
        if (!this.isOpening) return;
        this.show();
    },
    computed: {
        currentGroupCateId() {
            return groupCateLabelMap[this.currentGroupCate].id;
        },
        data() {
            return this.$store.state.demands.groups || [];
        },
        list() {
            const filterFn = (item) => {
                if (Array.isArray(this.currentGroupCateId)) {
                    return this.currentGroupCateId.includes(+item.category);
                }
                return +item.category === this.currentGroupCateId;
            };
            const currentList = this.data.filter(filterFn);
            // 我的分组：
            // -我创建的分组&&组员>0
            // -有我在内的协同分组，category = 2
            // -我创建的分组&&组员===0
            if (this.currentGroupCate === 'my') {
                return sortMyGroupList(currentList);
            }
            return currentList.sort((o) => {
                if (this.hasCooGroup(o)) {
                    return -1;
                }
                return 1;
            });
        },
    },
    methods: {
        show() {
            this.$nextTick(() => {
                const { popoverEl } = this.$refs;
                if (!popoverEl.showPopper) {
                    popoverEl.doShow();
                }
            });
        },
        hide() {
            const { popoverEl } = this.$refs;
            if (popoverEl && popoverEl.showPopper) {
                popoverEl.doClose();
            }
        },
        getCurrentListFirstGroup() {
            return this.list[0] || {};
        },
        // 判断是否是协同分组，
        isCooGroup,
        hasCooGroup(item) {
            return this.data.some(o => o.groupId === item.groupId && o.category === CODE.MY_COO);
        },
        handleGroupClick(item) {
            this.$emit('on-select', item);
            this.hide();
        },
        // 若返回一个字符串, 则返回结果会被赋值给 inputErrorMessage
        validateName(groupId, val) {
            const my = this.data.filter(item => +item.category === 0);
            const pureValue = trim(val);
            const checkRepeated = (value) => {
                const r = my.filter(o => o.groupName === value && groupId !== o.groupId);
                return r.length >= 1;
            };
            const checkLength = (value) => {
                /* eslint-disable no-control-regex */
                const r = value.replace(/[^\x00-\xff]/g, '**').length;
                return r > 40;
            };
            switch (true) {
            case pureValue === '':
                return '分组名不能为空';
            case checkLength(pureValue):
                return '分组名不能超过20个中文或40个英文';
            case checkRepeated(pureValue):
                return '分组名不能重复';
            default:
                return true;
            }
        },

        /* 新建分组 / 编辑分组名的弹出框 */
        dialogEditorPrompt({ type, inputValue, groupId }) {
            const title = this.dialogTitle[type];
            return this.$prompt('请输入分组名(最多20个中文或者40个英文)', title, {
                inputValidator: this.validateName.bind(this, groupId),
                inputValue,
                confirmButtonText: this.dialogConfirmButtonText,
            });
        },
        addGroup() {
            this.dialogEditorPrompt({
                type: 'add',
            }).then(this.doSave);
        },
        editGroup({ groupId, groupName }) {
            this.dialogEditorPrompt({
                type: 'edit',
                inputValue: groupName,
                groupId,
            }).then((data) => {
                this.doSave(data, groupId);
            });
        },
        async doSave({ value }, groupId) {
            const next = (item) => {
                this.$message.success('保存成功！');
                if (groupId) {
                    this.$emit('on-edit', item);
                } else {
                    this.$emit('on-add', item);
                }
            };
            const newItem = await this.$store.dispatch('demand_group_save', {
                groupName: trim(value),
                groupId,
            });
            next(newItem);
        },
        shareGroup(groupData) {
            this.$refs.shareEl.show(groupData);
        },
        deleteGroupConfirm(item) {
            this.$confirm(`确定要删除“${item.groupName}”这个分组么？`, '提示', {
                type: 'warning',
            }).then(this.doDel.bind(this, item));
        },
        doDel(item) {
            this.$store.dispatch('demands_group_delete', item).then(() => {
                this.$message.success('删除成功！');
                this.$emit('on-del', item);
            });
        },
    },
};
</script>
<style lang="less">
.demand-group{
    &__list{
        max-height: 350px;
        overflow: auto;
    }
    &__wrapper{
        padding: 10px 0;
        margin: 5px 0;
    }

    &__item{
        font-size: 12px;
    }
    &__text{
        display: inline-block;
        min-width: 100px;
        flex-grow: 1;
        font-size: 12px;
        &--emphasize{
            background: #44A7FA;
            text-align: center;
            display: inline-block;
            font-size: 12px;
            width: 16px;
            line-height: 16px;
            color: #fff;
            margin-right: 4px;
        }
    }
}
.el-message-box.demand-group-share{
    min-width: 800px;
}
</style>
