import { DemandGroupType as CODE } from '@/models/demandGroup';

export function sortMyGroupList(list) {
    const arrP1 = [];
    const arrP2 = [];
    const arrP3 = [];
    list.forEach((item) => {
        switch (true) {
        case +item.category === CODE.MY_DEFAULT && item.cooperatorCount > 0:
            arrP1.push(item);
            break;
        case +item.category === CODE.MY_COO:
            arrP2.push(item);
            break;
        default:
            arrP3.push(item);
            break;
        }
    });
    return arrP1.concat(arrP2, arrP3);
}
export function isCooGroup(item) {
    // 作为组员存在的分组没有返回组员数量
    return item.cooperatorCount > 0 || item.category === CODE.MY_COO;
}
export default {};
