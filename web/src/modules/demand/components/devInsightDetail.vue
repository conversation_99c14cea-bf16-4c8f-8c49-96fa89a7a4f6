<template>
    <div class="dev-insight detail-tab-content">
        <label
            class="detail-tab-content__label"
        >
            <span v-if="data.spaceMode === 2">迭代/Backlog</span>
            <p
                class="detail-tab-content__label--secend"
                style="color: #333333"
            >
                <span v-if="data.spaceMode === 2">{{ data.sprintName || 'Backlog' }}</span>
                <router-link
                    class="detail-tab-content__label--right"
                    :to="toCardDetail"
                    target="_blank"
                >
                    进入研发空间<i class="el-icon-arrow-right" />
                </router-link>
            </p>
        </label>
        <dev-milestone
            class="detail-tabs__dev-milestone"
            :data="data.mileStoneStatus"
        />
        <label class="detail-tab-content__label">
            <span>基本信息</span>
        </label>
        <label class="detail-tab-content__label--secend">处理人</label>
        <div
            class="detail-tab-content-users"
            v-if="data.processors"
        >
            <jacp-erp
                v-for="user in data.processors"
                avatar
                :key="user.erp"
                :data="user"
                :disable-timline="$utils.isCurrentUser(user)"
            />
            <jacp-erp
                avatar
                nodata
                v-if="data.processors.length === 0"
            />
        </div>
        <div class="detail-tab-content-noticeboards">
            <jacp-noticeboard
                v-if="data.spaceMode === 2"
                size="14"
                label="剩余工时"
                :value="`${data.estimated}h`"
            />
            <jacp-noticeboard
                size="14"
                label="计划工时"
                :value="`${data.planHours}h`"
            />
            <jacp-noticeboard
                size="14"
                label="开始日期"
                :value="data.cardStartDate | jacp-local-time('YYYY-MM-DD')"
            />
            <jacp-noticeboard
                size="14"
                label="截止日期"
                :value="data.deadline | jacp-local-time('YYYY-MM-DD')"
            />
            <jacp-noticeboard
                size="14"
                label="优先级"
                :value="getPriorityValue(data.priority)"
            />
        </div>
        <template v-if="data.spaceMode === 2">
            <label class="detail-tab-content__label">
                <span>{{ $t('jacp.teamspace.cardFields.release') }}</span>
            </label>
            <div
                class="detail-tab-content-noticeboards j-mgt16"
            >
                <jacp-noticeboard
                    size="14"
                    label="计划上线版本号"
                    :value="cardRelease.planVersion"
                />
                <jacp-noticeboard
                    size="14"
                    label="实际上线版本"
                    :value="cardRelease.realVersion"
                />
                <jacp-noticeboard
                    size="14"
                    label="计划上线日期"
                    :value="cardRelease.planDate | jacp-local-time('YYYY-MM-DD')"
                />
                <jacp-noticeboard
                    size="14"
                    label="实际上线日期"
                    :value="cardRelease.realDate | jacp-local-time('YYYY-MM-DD')"
                />
            </div>
            <div class="dev-insight-notReleaseReason j-mgb24">
                <label class="detail-tab-content__label--secend">未按计划上线原因</label>
                <el-input
                    type="textarea"
                    :value="cardRelease.notReleaseReason || noDataText"
                    :disabled="true"
                />
            </div>
        </template>
        <label class="detail-tab-content__label">最新卡片备注</label>
        <jacp-record-pane
            mode="remark"
            :list="data.remark"
        />
    </div>
</template>

<script type="text/javascript">
import { mapActions, mapState } from 'vuex';
// import { card as CardModel } from '@/models/teamspace';
import devMilestone from './devMilestone';

export default {
    components: { devMilestone },
    props: {
        noDataText: {
            type: String,
            default: '无数据',
        },
        data: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            cardDetail: {
                name: 'teamspaceCardsList',
                params: {
                    spaceKey: this.data.spaceKey,
                },
                query: {
                    cardId: this.data.cardId,
                    sprintId: this.data.sprintId,
                },
            },
            generalCardDetail: {
                name: 'generalCardDetail',
                params: {
                    spaceKey: this.data.spaceKey,
                    cardId: this.data.cardId,
                },
            },
            labelWidth: 115,
            // priorityList: [],
            cardRelease: {},
        };
    },
    computed: {
        ...mapState('chilldteamspace', ['priorityList']),
        toCardDetail() {
            return this.data.spaceMode === 1 ? this.generalCardDetail : this.cardDetail;
        },
    },
    created() {
        this.fetchPriorityList();
    },
    methods: {
        getPriorityValue(id) {
            const item = this.priorityList.find(o => o.code === id);
            return item ? item.name : '';
        },
        ...mapActions('chilldteamspace', ['fetchPriorityList']),

        /* async fetchPriorityList() {
            this.priorityList = await CardModel.getPriorityConfig();
        }, */
    },
    watch: {
        data: {
            immediate: true,
            handler(val) {
                if (val.cardRelease) {
                    Object.assign(this.cardRelease, val.cardRelease);
                }
            },
        },
    },
};
</script>
