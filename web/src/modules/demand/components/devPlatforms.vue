<template>
    <div
        class="dev-platforms"
        :class="{'dev-platforms--disabled': disabled}"
    >
        <template v-if="disabled">
            <el-tag
                :class="{
                    'dev-platforms__item': true,
                    'checked': true,
                }"
                size="small"
                v-for="item in value"
                :key="item.platformId"
            >
                {{ item.platformName }}
            </el-tag>
            <span
                v-if="!value.length"
                class="jacp-form--nodata"
            >无数据</span>
        </template>
        <template v-else>
            <div
                class="dev-platforms__selector"
                v-if="list.length"
            >
                <div class="dev-platforms__values">
                    <el-tag
                        v-for="config in currentValue"
                        class="dev-platforms__item checked"
                        size="small"
                        :key="config.platformId"
                        closable
                        @close="removePlatform(config)"
                    >
                        {{ config.platformName }}
                    </el-tag>
                </div>
                <button
                    ref="reference"
                    class="dev-platforms--button"
                    type="button"
                    @click.stop="toggleDropDownVisible()"
                    v-clickoutside="() => {
                        if (this.dropDownVisible) {
                            toggleDropDownVisible(false);
                        }
                    }"
                >
                    添加
                </button>
                <div
                    ref="popper"
                    v-show="dropDownVisible"
                    class="el-popper el-cascader__dropdown"
                >
                    <el-cascader-panel
                        ref="panel"
                        v-model="innerValueIds"
                        :options="currentGroup"
                        :props="{ value: 'code', label: 'name', emitPath: false, multiple: true, expandTrigger: 'hover' }"
                        :show-all-levels="false"
                        :border="false"
                        @change="handleChange"
                    />
                </div>
            </div>
        </template>
    </div>
</template>

<script type="text/javascript">
import Popper from 'element-ui/src/utils/vue-popper';
import Clickoutside from 'element-ui/src/utils/clickoutside';
import FormValidate from '@/mixins/mixin.form.validate';

function formartter(item) {
    return {
        platformId: item.code,
        platformName: item.name,
    };
}

const PopperMixin = {
    props: {
        appendToBody: {
            type: Boolean,
            default: false,
        },
        offset: {
            default: -75,
        },
    },
};

export default {
    name: 'DevPlatforms',
    mixins: [FormValidate, Popper, PopperMixin],
    props: {
        disabled: {
            type: Boolean,
            default: true,
        },
        list: {
            type: Array,
            default: () => ([]),
        },
        value: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            dropDownVisible: false,
            currentGroup: [],
            currentValue: [],
            innerValueIds: [],
            changeFlag: false,
            canPopper: true,
        };
    },
    directives: { Clickoutside },
    methods: {
        handleChange() {
            this.currentValue = [];
            this.$refs.panel.getCheckedNodes(true)
                .forEach((item) => {
                    this.currentValue.push(formartter(item.data));
                });
            this.changeFlag = true;
        },
        removePlatform(item) {
            this.currentValue.splice(this.currentValue.findIndex(o => o.platformId === item.platformId), 1);
            this.changeFlag = true;
            this.toggleDropDownVisible(false);
        },
        toggleDropDownVisible(visible) {
            if (this.disabled) {
                return;
            }

            const { dropDownVisible } = this;
            const show = visible !== undefined && visible !== null ? visible : !dropDownVisible;
            if (show !== dropDownVisible) {
                this.dropDownVisible = show;
                if (show) {
                    this.innerValueIds = this.currentValue.map(o => o.platformId);
                    if (this.canPopper) {
                        this.updatePopper();
                        this.canPopper = false;
                    }
                }
            }
            if (!show && this.changeFlag) {
                this.$emit('input', this.currentValue);
                this.$emit('change', this.currentValue);
                this.$_triggerValidateChangeEvent();
                this.changeFlag = false;
            }
        },
    },
    watch: {
        list: {
            immediate: true,
            handler(val) {
                if (val.length) {
                    this.currentGroup = val.map((item, i) => Object.assign({}, {
                        code: -1000 + i,
                        name: item.group,
                        children: item.configList || [],
                    }));
                }
            },
        },
        value: {
            immediate: true,
            handler(val) {
                this.currentValue = [].concat(val);
            },
        },
    },
};
</script>

<style lang="less">
    @import '~@/theme/var';

    .dev-platforms{
        display: flex;
        padding-left: 1px;
        flex-wrap: wrap;
        position: relative;
        &--disabled {
            cursor: default;
            pointer-events: none;
        }
        &--button {
            background-color: #F1F1F1;
            border: 1px solid transparent;
            border-radius: 2px;
            color: @remarkColor;
            font-size: 12px;
            height: 24px;
            line-height: 24px;
            padding: 0 6px;
            cursor: pointer;
            position: relative;
            &:not(.disabled):hover{
                background-color: #2695F1;
                color: #fff;
            }
        }
        & .el-tag{
            cursor: pointer;
            border-radius: 20px;
            // line-height: 24px;
            // height: 24px;
            border: 1px solid transparent;
            padding: 0 16px;
            margin-bottom: 8px;
            margin-right: 8px;
            &:not(.checked) {
                border: 1px solid @fontDisabledColor;
                color: @fontColor;
                background: transparent;
            }
        }
        &__values{
            display: inline;
            line-height: 24px;
        }
    }
</style>
