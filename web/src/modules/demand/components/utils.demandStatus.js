/* Utils for DemandStatus */
import { statusGroup, ColorMap } from '@/models/config';

export const getDemandStatusColor = (status) => {
    let color = '';
    switch (true) {
    // 完成
    case [20].includes(+status):
        color = ColorMap.green;
        break;
        // 草稿、待沟通
    case [1, 2].includes(+status):
        color = ColorMap.blue;
        break;
        // 已驳回、验证失败、验收失败、已取消
    case [4, 8, 10, 21].includes(+status):
        color = ColorMap.red;
        break;
    default:
        color = ColorMap.orange;
        break;
    }
    return color;
};

export function demandStatusExpand(status) { // 5 => [5, 11]
    const res = statusGroup[+status] || [];
    res.push(+status);
    return res;
}
