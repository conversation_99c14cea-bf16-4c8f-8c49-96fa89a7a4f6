import i18n from '$platform.i18n';

export default {
    filters: {
        fieldText(label) {
            return i18n.t(`jacp.demandFields.${label}`);
        },
    },
    computed: {
        fieldText() {
            return {
                name: this.$t('jacp.demandFields.name'),
                demanders: this.$t('jacp.demandFields.demanders'),
                description: this.$t('jacp.demandFields.description'),
                richText: this.$t('jacp.demandFields.description'),
                followers: this.$t('jacp.demandFields.followers'),
                receivers: this.$t('jacp.demandFields.receivers'),
                processor: this.$t('jacp.demandFields.processor'),
                addAttachments: this.$t('jacp.demandFields.addAttachments'),
                attachments: this.$t('jacp.demandFields.attachments'),
                projectRelation: this.$t('jacp.demandFields.projectRelation'),
                actuality: this.$t('jacp.demandFields.actuality'),
                roi: this.$t('jacp.demandFields.roi'),
                expectedReleaseDate: this.$t('jacp.demandFields.expectedReleaseDate'),
                expectedOfflineDate: this.$t('jacp.demandFields.expectedOfflineDate'),
                platform: this.$t('jacp.demandFields.platform'),
                priority: this.$t('jacp.demandFields.priority'),
                type: this.$t('jacp.demandFields.type'),
                source: this.$t('jacp.demandFields.source'),
                system: this.$t('jacp.demandFields.system'),
                risk: this.$t('jacp.demandFields.risk'),
                demandMilestone: this.$t('jacp.demandFields.demandMilestone'),
                index: this.$t('jacp.demandFields.index'),
                urgent: this.$t('jacp.demandFields.urgent'),
                confidential: this.$t('jacp.demandFields.confidential'),
                tag: this.$t('jacp.demandFields.tag'),
                joinInGroup: this.$t('jacp.demandFields.group'),
                apportions: this.$t('jacp.demandFields.apportions'),
                // 新增
                proposer: this.$t('jacp.demandFields.proposer'),
                submitTime: this.$t('jacp.demandFields.submitTime'),
                revenues: this.$t('jacp.demandFields.revenues'),
                revenueOther: this.$t('jacp.demandFields.revenueOther'),
                revenueDays: this.$t('jacp.demandFields.revenueDays'),
                parentAndChildren: this.$t('jacp.demandFields.parentAndChildren'),
                links: this.$t('jacp.demandFields.links'),
                isNoProject: this.$t('jacp.demandFields.isNoProject'),
                channelRisk: this.$t('jacp.demandFields.channelRisk'),
                systemRelation: this.$t('jacp.demandFields.systemRelation'),
                demandSourceOrg: this.$t('jacp.demandFields.demandSourceOrg'),
                demandMark: this.$t('jacp.demandFields.demandMark'),
                autoAcceptanceDays: this.$t('jacp.demandFields.autoAcceptanceDays'),
            };
        },
        uploadText() {
            const size = {
                size: '10M',
            };
            return {
                button: this.$t('jacp.upload.button'),
                tip: this.$t('jacp.upload.tip', size),
                errorTitle: this.$t('jacp.upload.errorTitle'),
                maxSizeError: this.$t('jacp.upload.maxSizeError', size),
            };
        },
    },
};
