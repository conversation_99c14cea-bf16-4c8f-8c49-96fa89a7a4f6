import {
    toggleExpandAll,
    toggleExpandStatus,
} from '@/utils/children';
import { isCurrentUser } from '@/plugins/utils';

const loadChildren = (demand, demandsList) => {
    const { id } = demand;
    if (demand.$children) {
        return demand;
    }
    Object.assign(demand, {
        $children: demand.hasChild ? demandsList.filter(o => o.parentDemandId === id) : [],
    });
    if (!demand.hasChild) {
        demand = loadChildren(demand, demandsList);
    }
    return demand;
};
const formatDemandsList = ({
    list = [],
    isSortable = false,
    isGroup = false,
    defaultExpandStatus = false,
}) => {
    const formartor = (demand, index, demandList) => {
        const parentDemand = demand.parentDemandId;
        // 排序的时候才需要增加的参数
        if (isSortable) {
            let $isHandler = isCurrentUser(demand.processor) && demand.level === 1;
            if (isGroup) {
                $isHandler = demand.level === 1;
            }
            Object.assign(demand, { $isHandler });
        }
        // loadchildren
        // 之前列表的父子被拍平了，重新恢复树状结构，便于处理父子拖动和展开收起的联动
        demand = loadChildren(demand, demandList);
        // 其他通用参数
        return Object.assign(demand, {
            $expandStatus: defaultExpandStatus,
            $vParentId: parentDemand,
            $level: demand.level,
        });
    };
    // const isParent = demand => +demand.parentDemandId === -1;
    // const isHasChild = demand => demand.hasChild || demand.$children.length;
    return list.map(formartor);
};
// 移除不需要mixin的pure fn
export default {
    data() {
        return {
            defaultExpandStatus: true,
        };
    },
    methods: {
        formatDemandsList({
            list = [],
            isSortable = false,
            isGroup = false,
        }) {
            return formatDemandsList({
                list,
                isSortable,
                isGroup,
                defaultExpandStatus: this.defaultExpandStatus,
            });
        },
        toggleExpandAll(expandStatus) {
            toggleExpandAll(expandStatus, this.activedList);
        },
        toggleExpandStatus(item) {
            toggleExpandStatus(item, this.activedList);
        },
    },
};
