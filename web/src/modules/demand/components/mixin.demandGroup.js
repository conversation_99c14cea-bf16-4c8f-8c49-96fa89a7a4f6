import Vue from 'vue';
import store from '$platform.store';
import i18n from '$platform.i18n';
import { DemandGroupModel, DemandGroupType as CODE } from '@/models/demandGroup';
import DemandGroupList from './demandGroups/demandGroupList';
import { sortMyGroupList } from './demandGroups/utils.demandGroup';

export default {
    data() {
        return {
            involvedGroupList: [],
        };
    },
    computed: {
        currentGroupId() {
            return this.$route.query.groupId;
        },
        // 我的分组才显示移出分组，关注分组不能移出
        isMyGroup() {
            const groups = (this.$store.state.demands.groups || []);
            const validator = (o) => {
                const isCurrent = +o.groupId === +this.currentGroupId;
                const isMy = +o.category === CODE.MY_DEFAULT || +o.category === CODE.MY_COO;
                return isCurrent && isMy;
            };
            return groups.some(validator);
        },
    },
    methods: {

        /* 列表里的加入分组 */
        $_joinInGroup({ demandId, reference, onJoinIn = () => {} }) {
            const doJoin = ({ groupId }) => {
                this.$_doJoinInGroup({
                    demandId,
                    groupId,
                    onJoinIn,
                });
            };
            this.$_showGroupList({
                demandId,
                reference,
                onAdd: doJoin,
                onSelect: doJoin,
                confirmText: '保存并加入',
            });
        },
        $_detachFromGroup({ demandId, groupId, onDetach = () => {} }) {
            return this.$confirm('确定要将这个需求从这个分组移出吗?', '提示', {
                type: 'warning',
            }).then(() => this.$store.dispatch('demand_group_detach', {
                groupId: groupId || this.currentGroupId,
                demandId,
            }).then(() => {
                this.$message.success('移出成功！');
                this.$emit('refresh-list');
                onDetach(groupId || this.currentGroupId, 'detach');
            }));
        },
        $_doJoinInGroup({ demandId, groupId, onJoinIn }) {
            return this.$store.dispatch('demand_group_join', {
                groupId,
                demandId,
            }).then((data) => {
                this.$message.success(data);
                this.$emit('on-join-in', groupId, demandId);
                onJoinIn(groupId);
            });
        },
        $_showGroupList({
            demandId,
            reference,
            onAdd = () => {},
            onSelect = () => {},
            isOpening = true,
            confirmText,
        }) {
            const createList = () => {
                const props = {
                    reference,
                    isOpening,
                    dialogConfirmButtonText: confirmText,
                };
                if (demandId) {
                    Object.assign(props, {
                        demandId,
                    });
                }
                const P = Vue.extend({
                    components: { DemandGroupList },
                    render(h) {
                        return h('demand-group-list', {
                            props,
                            on: {
                                'on-select': onSelect.bind(this),
                                'on-add': onAdd.bind(this),
                            },
                        });
                    },
                });
                const ins = new P({
                    i18n,
                    store,
                }).$mount();
                this.$refs.demandGroupListEl.set(reference, ins);
                document.body.appendChild(ins.$el);
            };
            if (!this.$refs.demandGroupListEl) {
                this.$refs.demandGroupListEl = new Map();
            }
            if (this.$refs.demandGroupListEl.get(reference)) return;
            createList();
        },
        async $_fetchInvolvedGroupList(id) {
            const list = await DemandGroupModel.getInvolvedGroupByDemandId(id);
            this.involvedGroupList = sortMyGroupList(list);
        },
    },
};
