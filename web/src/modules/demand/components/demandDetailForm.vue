<template>
    <div>
        <el-form
            class="j-form latest-detail-form"
            ref="formIns"
            :model="data"
            :rules="rules"
            :validate-on-rule-change="false"
        >
            <el-row class="latest-detail__main">
                <el-col
                    class="latest-detail__main--left"
                    :span="16"
                >
                    <el-form-item
                        class="is-underline-input"
                        :label="'name' | fieldText"
                        prop="name"
                    >
                        <!-- 这个和详情里的那个不是一个东东 -->
                        <el-input
                            class="latest-detail-title"
                            v-model="data.name"
                            type="textarea"
                            autosize
                            resize="none"
                            placeholder="可输入3-80个字符"
                        />
                    </el-form-item>
                    <el-checkbox
                        style="display: block; margin-top: 12px;"
                        :label="'urgent' | fieldText"
                        v-model="data.urgent"
                    />
                    <el-form-item
                        ref="richEditorFormEl"
                        :label="'description' | fieldText"
                        prop="richText"
                    >
                        <quill-editor
                            v-model="data.richText"
                            ref="richEditorEl"
                            auto-save-key="jacp-qleditor-demandadd"
                            :placeholder="$t('jacp.demandFields.descriptionPlaceholder')"
                            @change="checkDescription"
                        >
                            <div
                                v-if="data.name"
                                v-text="data.name"
                                slot="full-title"
                                class="ql-editor-plus__fulltitle"
                            />
                        </quill-editor>
                    </el-form-item>
                    <div style="display: flex">
                        <!-- 受理方, 发生变更的时候需要根据受理人获取一下rules getOrgFieldAttrByErp -->
                        <el-form-item
                            style="margin-top: 12px;"
                            :label="'receivers' | fieldText"
                            prop="receivers"
                        >
                            <form-users-group
                                class="latest-detail-receivers"
                                v-model="data.receivers"
                                placeholder="请选择"
                                :max-count="1"
                                :no-timline-users="[$store.state.user]"
                                storage-key="demand_receivers"
                                @on-add="checkOrgReceiver"
                            />
                        </el-form-item>
                        <!-- 需求人，发生变更的时候要处理成本分摊的部门数据校验 -->
                        <el-form-item
                            style="margin-top: 12px;"
                            :label="'demanders' | fieldText"
                            prop="demanders"
                        >
                            <form-users-group
                                class="latest-detail-demanders__wrapper"
                                v-model="data.demanders"
                                placeholder="请选择"
                                storage-key="demand_demanders"
                                :no-timline-users="[$store.state.user]"
                                :disabled="!apportionEditable"
                            />
                        </el-form-item>
                    </div>

                    <!-- 期望上线日期 -->
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="'expectedReleaseDate' | fieldText"
                        prop="expectedReleaseDate"
                    >
                        <el-date-picker
                            type="date"
                            size="small"
                            placeholder="选择日期"
                            v-model="data.expectedReleaseDate"
                            :picker-options="expReleaseDateOptions"
                        />
                    </el-form-item>
                    <!-- 成本分摊 -->
                    <el-form-item
                        ref="apportionFormItemEl"
                        prop="apportions"
                        :required="true"
                        v-if="apportionVisible"
                        v-show="!$store.state.user.orgTierCode.includes('/00023242')"
                    >
                        <demand-apportion
                            ref="apportionEl"
                            @on-change="checkApportions"
                            :apportion-edit="apportionEditable"
                            :org-data="data.demanders"
                            v-model="data.apportions"
                        />
                    </el-form-item>
                    <div style="margin-top: 12px;">
                        <el-row :gutter="24">
                            <el-col :span="revenueRowSpan">
                                <el-form-item
                                    :label="fieldText.revenues"
                                    prop="revenueId"
                                >
                                    <el-cascader
                                        style="width: 100%"
                                        v-model="data.revenueId"
                                        :options="revenuesList"
                                        :placeholder="`请选择${fieldText.revenues}`"
                                        :props="{ expandTrigger: 'hover', value: 'code', label: 'name', emitPath: false }"
                                        @change="revenueChange"
                                        separator=" - "
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col
                                :span="revenueRowSpan"
                                v-if="!hiddenRevenueOther"
                            >
                                <el-form-item
                                    :label="fieldText.revenueOther"
                                    prop="revenueOther"
                                >
                                    <el-input
                                        v-model="data.revenueOther"
                                        :placeholder="`请输入${fieldText.revenueOther}`"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="revenueRowSpan">
                                <el-form-item
                                    :label="fieldText.revenueDays"
                                    prop="revenueDays"
                                >
                                    <jacp-input-select
                                        v-model="data.revenueDays"
                                        clearable
                                        :data="revenueDaysList"
                                        :placeholder="`请选择${fieldText.revenueDays}`"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="fieldText.roi"
                        prop="roi"
                    >
                        <el-input
                            type="textarea"
                            :autosize="{ minRows: 3, maxRows: 6 }"
                            v-model="data.roi"
                            placeholder="请填写【预期收益】/【上线后预计收益时间】"
                        />
                    </el-form-item>
                    <div class="roi-tooltip">
                        <el-button
                            type="text"
                            @click="roiTooltipVisible = !roiTooltipVisible"
                        >
                            <span>如何填写收益描述？</span>
                        </el-button>
                        <div
                            x-placement="bottom"
                            class="el-popover el-popper el-popover--plain"
                            :style="{
                                position: 'relative',
                                marginTop: '4px',
                                zIndex: '0',
                                display: roiTooltipVisible ? 'block' : 'none',
                            }"
                        >
                            <p style="margin-top: 4px;">建议格式：</p>
                            <p>
                                收益描述应包含 5 部分：
                                <strong>产生收益的范围</strong> +
                                <strong>收益指标</strong> + (同比/环比) 提升/下降/保持/达成 等 +
                                <strong>指标值</strong> +
                                <strong>【验收数据来源】</strong>
                            </p>
                            <p>收益描述示例：</p>
                            <div style="font-size: 12px;">
                                <div>1. 预计“大客户”的“满意度”环比提升（80%）;【验收数据来源】质量日报</div>
                                <div>2. 预计“特惠小件产品”的“日均单量”达成（1万单）;【验收数据来源】经分报告</div>
                                <div>3. 预计“单仓”的“48小时接收清理率”同比提升（10%）;【验收数据来源】仓储产能看板</div>
                            </div>
                            <el-button
                                type="text"
                                style="width: 100%; display: flex; justify-content: flex-end;"
                                @click="roiTooltipVisible = false"
                            >
                                我明白了
                            </el-button>
                            <div
                                x-arrow=""
                                class="popper__arrow"
                                style="left: 45px"
                            />
                        </div>
                    </div>
                </el-col>
                <!-- right -->
                <el-col
                    class="latest-detail__main--right"
                    :span="8"
                >
                    <slot name="extra" />
                    <!-- 需求来源 -->
                    <el-form-item
                        :label="'source' | fieldText"
                        prop="demandSourceId"
                    >
                        <jacp-input-select
                            v-model="data.demandSourceId"
                            placeholder="请选择"
                            :data="sourceList"
                        />
                    </el-form-item>
                    <!-- 需求类型 -->
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="'type' | fieldText"
                        prop="demandTypeId"
                    >
                        <jacp-input-select
                            v-model="data.demandTypeId"
                            placeholder="请选择"
                            clearable
                            :data="typeList"
                            @change="changeDemandType"
                        />
                    </el-form-item>
                    <!-- 保密需求 -->
                    <el-checkbox
                        style="display: block;"
                        v-model="data.confidential"
                        :disabled="confidentialDisabled"
                    >
                        保密
                    </el-checkbox>
                    <!-- 期望下线日期 -->
                    <el-form-item
                        :label="'expectedOfflineDate' | fieldText"
                        prop="expectedOfflineDate"
                        v-if="showExpectedOfflineDate"
                    >
                        <el-date-picker
                            type="date"
                            size="small"
                            placeholder="选择日期"
                            v-model="data.expectedOfflineDate"
                            :picker-options="expOfflineDateOptions"
                        />
                    </el-form-item>
                    <!-- 关注人 -->
                    <el-form-item
                        :label="'followers' | fieldText"
                        prop="followers"
                    >
                        <form-users-group
                            class="latest-detail-followers__editor"
                            v-model="data.followers"
                            :no-timline-users="[$store.state.user]"
                            storage-key="demand_followers"
                            placeholder="请选择"
                        />
                    </el-form-item>
                    <!-- 需求来源部门 -->
                    <el-form-item
                        :label="'demandSourceOrg' | fieldText"
                        v-if="parentDemandId === -1"
                        prop="demandSourceOrg"
                    >
                        <!-- FIXME: 如果清空的话，需要整个对象清空，避免必填校验异常。诡异的逻辑。 -->
                        <jacp-orgs-autocomplete
                            :value="(data.demandSourceOrg || {}).orgTierName"
                            @change="(orgTierName) => {
                                if (orgTierName) {
                                    data.demandSourceOrg = {
                                        orgTierName,
                                    };
                                } else {
                                    data.demandSourceOrg = undefined;
                                }
                            }"
                            @select="({ fullName: orgTierName, parentIds: orgTierCode } = {}) => {
                                data.demandSourceOrg = {
                                    orgTierCode,
                                    orgTierName,
                                }
                            }"
                            :filter-method="org => org.level >= 2"
                            value-key="fullName"
                            label="fullName"
                            placeholder="请输入部门名称"
                            clearable
                        />
                    </el-form-item>
                    <!-- 业务需求优先级评分 -->
                    <el-form-item
                        ref="priorityScoreFormItemEl"
                        v-if="demandSourceOrgVisible"
                        :label="'demandMark' | fieldText"
                        prop="priorityScore"
                    >
                        <demand-priority-mark
                            ref="demandPriorityMark"
                            :demand="data"
                            :demand-add="data"
                            @doMark="changePriorityScore(...arguments)"
                        />
                    </el-form-item>
                    <!-- 是否待立项需求 -->
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="'isNoProject' | fieldText"
                        prop="pmpProjectApproval"
                    >
                        <jacp-input-select
                            v-model="data.pmpProjectApproval"
                            :data="pmpProjectOption"
                        />
                    </el-form-item>
                    <!-- 业务优先级 -->
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="'priority' | fieldText"
                        prop="priorityId"
                    >
                        <jacp-input-select
                            v-model="data.priorityId"
                            clearable
                            :data="validPriorityList"
                            placeholder="请选择"
                        />
                    </el-form-item>
                    <!-- 风险 -->
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="'risk' | fieldText"
                        prop="riskId"
                    >
                        <jacp-input-select
                            v-model="data.riskId"
                            placeholder="请选择"
                            clearable
                            :data="riskList"
                        />
                    </el-form-item>
                    <el-form-item
                        ref="projectIdFormItemEl"
                        style="margin-top: 12px;"
                        :label="'pmpProjectId' | fieldText"
                        prop="projectId"
                    >
                        <!-- 2020.4.21: 成本分摊部门【apportions】发生变化，则清空已选项目,根据【apportions】获取成本分摊相关项目列表 -->
                        <!-- 目前用的字段是pmpProjectId，但是校验使用的字段是projectId -->
                        <!-- 在拆分需求的时候，获取关联项目的列表中需要按照父需求的成本分摊部门筛选 -->
                        <related-projects
                            v-model="data.pmpProjectId"
                            :entity="data"
                            :request-parameters="projectRequestParameters"
                            :filterable="true"
                            :clearable="true"
                            :confirm="false"
                            @change="($event, item) => {
                                data.projectId = item ? item.projectId : null;
                            }"
                        />
                    </el-form-item>
                    <!-- 关联产品 -->
                    <el-form-item
                        v-if="prdmAvailable"
                        :label="'productRelation' | fieldText"
                    >
                        <jacp-prdm-relate-to-product
                            v-model="data.product"
                            :pop-width="892"
                        />
                    </el-form-item>
                    <!-- 关联系统 -->
                    <el-form-item
                        v-if="showSystemRelation"
                        :label="'systemRelation' | fieldText"
                        prop="systemInfoList"
                    >
                        <!-- 选项点击外侧无法收起:需要设置一下popper-append-to-body就好了 -->
                        <demand-related-systems
                            v-model="data.systemInfoList"
                            ref="systemRelation"
                            multiple
                            reserve-keyword
                            filterable
                            remote
                            value-key="id"
                            :remote-method="selectSystemRelation"
                            :options="systemList"
                        />
                    </el-form-item>
                    <!-- 涉及研发平台 -->
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="'platform' | fieldText"
                        prop="platformRelations"
                    >
                        <dev-platforms
                            :list="platformList"
                            v-model="data.platformRelations"
                            :disabled="false"
                            ref="devPlatform"
                        />
                        <section
                            class="latest-detail__block j-mgt16"
                            v-show="hasApp"
                        >
                            <h5>
                                {{ 'channelRisk' | fieldText }}
                                <jacp-icon
                                    style="margin-left: 4px;color: #c5c5c5;cursor: pointer"
                                    name="icon-warning2"
                                    @click.native="openCF"
                                    :size="16"
                                />
                            </h5>
                            <jacp-input-select
                                style="width: 100%;"
                                v-model="data.channelRisk"
                                :data="channelRiskList"
                                ref="channelRiskSelect"
                            />
                            <span
                                class="channelRiskTip"
                                v-show="showTip"
                            >
                                1.务必与预审团队、版本经理评审需求
                                <br>
                                2.618与双十一期间谨慎规划
                            </span>
                        </section>
                    </el-form-item>
                    <!-- 标签 -->
                    <el-form-item
                        :label="'tag' | fieldText"
                        prop="tags"
                        class="latest-detail-form__tags"
                    >
                        <custom-tag-group
                            v-model="data.tags"
                            :rules="rules.tags"
                            tag-editable
                            :suggestions="tagsList"
                            :min-count="rules.tags && rules.tags.some(o => o.required) ? 1 : 0"
                            editable
                        />
                    </el-form-item>
                    <!-- 附件 -->
                    <el-form-item
                        style="margin-top: 12px;"
                        :label="'attachments' | fieldText"
                        prop="attachments"
                    >
                        <label slot="label">
                            附件
                            <el-tooltip placement="top">
                                <div slot="content">
                                    文件名只包含字母、数字、中文、“/”、“.”、“-”、“ ”
                                </div>
                                <jacp-icon
                                    style="margin-left: 4px;color: #c5c5c5;"
                                    name="icon-help_outline"
                                    :size="16"
                                />
                            </el-tooltip>
                        </label>
                        <jacp-form-upload
                            v-model="data.attachments"
                            :can-reload="false"
                            :min-count="rules.attachments && rules.attachments.some(o => o.required) ? 1 : 0"
                        />
                    </el-form-item>
                    <!-- 链接 -->
                    <el-form-item
                        :label="'links' | fieldText"
                        prop="links"
                    >
                        <jacp-form-link-group
                            v-model="data.links"
                        />
                    </el-form-item>
                    <!-- 云文档 -->
                    <el-form-item
                        label="云文档"
                        prop="joySpaceLinks"
                        v-if="joyspaceModuleAvailable"
                    >
                        <DemandJoyspaceLinks
                            :demand="data"
                            :disabled="false"
                            :on-add="(links) => addLinksAndRemoveDuplicates(links, data).then((links = []) => {
                                data.joySpaceLinks = links;
                            })"
                        />
                    </el-form-item>
                    <!-- 自动验收通过天数 -->
                    <el-form-item
                        v-if="lazyAutoAcceptance"
                        :label="'autoAcceptanceDays' | fieldText"
                        prop="autoAcceptanceDays"
                    >
                        <label slot="label">
                            ⾃动验收通过时效
                            <el-tooltip placement="top">
                                <div slot="content">
                                    需求提交验收后，开始倒计时，倒计时到达需求填写的时效后，需求⾃动验收通过。
                                </div>
                                <jacp-icon
                                    style="margin-left: 4px;color: #c5c5c5;"
                                    name="icon-help_outline"
                                    :size="16"
                                />
                            </el-tooltip>
                        </label>
                        <span class="latest-detail-form__text">提交验收</span>
                        <el-input-number
                            v-model="data.autoAcceptanceDays"
                            style="width: 60px"
                            :controls="false"
                            step-strictly
                            :min="1"
                            :max="21"
                        />
                        <span class="latest-detail-form__text">天后自动验收通过</span>
                    </el-form-item>
                    <el-form-item :label="fieldText.joinInGroup">
                        <demand-group-select
                            v-model="data.groupId"
                            @on-add="onGroupAdd"
                        />
                    </el-form-item>
                    <!-- 自定义表单 -->
                    <dynamic-form
                        class="latest-detail-dynamic-form j-form--small"
                        ref="dynamicFormIns"
                        v-if="formHelper.dynamicFormSchema"
                        v-model="data.extendedFields"
                        v-bind="formHelper.dynamicFormSchema"
                        :rules="formHelper.dynamicFormSchema.rules"
                    />
                </el-col>
            </el-row>
        </el-form>

        <transition name="fade">
            <div class="latest-detail__footer">
                <div class="latest-detail__actions">
                    <jacp-button
                        type="primary"
                        :on-click="submit"
                    >
                        {{ $t('jacp.button.submit') }}
                    </jacp-button>
                    <jacp-button
                        v-if="$route.name !== 'demandEdit'"
                        :on-click="submitAndContinue"
                    >
                        提交并继续
                    </jacp-button>
                    <jacp-button @click="$emit('on-close')">
                        关闭
                    </jacp-button>
                    <jacp-button
                        v-if="!disableDraft"
                        :on-click="saveDraft"
                    >
                        {{ $t('jacp.button.saveDraft') }}
                    </jacp-button>
                </div>
            </div>
        </transition>
        <white-list
            :show-list.sync="showReceiverList"
            :white-list="receiverWhiteList"
            @select="selectReceiver"
        />
        <el-dialog
            title="受理人选择"
            :visible.sync="showReceiverList"
            width="420px"
            :append-to-body="true"
            custom-class="receiver-list"
        >
            <div>
                <p class="receiver-tips">
                    您所选择受理人部门已开启受理人白名单功能，请从下方正确的受理人中选择
                </p>
                <ul>
                    <li
                        v-for="receiver in receiverWhiteList"
                        :key="receiver.userErp"
                        @click="selectReceiver(receiver)"
                    >
                        <img
                            v-if="receiver.headImage"
                            :key="receiver.userErp"
                            :title="receiver.userName"
                            :src="receiver.headImage"
                        >
                        <img
                            v-else
                            :key="receiver.userErp"
                            :title="receiver.userName"
                            src="../../../assets/images/avatar.png"
                        >
                        <div class="receiver-info">
                            <p class="receiver-name">
                                {{ receiver.userName }}
                            </p>
                            <p
                                class="receiver-org"
                                :title="receiver.orgTierName"
                            >
                                {{ receiver.orgTierName }}
                            </p>
                        </div>
                    </li>
                </ul>
            </div>
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button
                    type="primary"
                    size="small"
                    plain
                    @click="showReceiverList = false"
                >关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import Demand from '@/modules/demand/models/Demand';
import { validate, focusFirstErrorItem } from '@/plugins/utils';
import DemandModel from '@/models/demands';
import mixinsDemandDetails from './mixin.demandDetails';
import mixinFieldText from './mixin.fieldText';
import DevPlatforms from './devPlatforms';
import DemandApportion from './demandApportion';
import DemandRelatedSystems from './demandRelatedSystems';
import DemandGroupSelect from './demandGroups/demandGroupSelect';
import DemandPriorityMark from './demandPriorityMark';
import GlobalSetting from '@/models/globalSetting';
import { addLinksAndRemoveDuplicates } from '@/modules/demand/models/joyspaceService';
import DemandJoyspaceLinks from '$module/components/extensionPoint/demandJoyspaceLinks';

function resetListenerForElFormItem(component, rules) {
    if (component.prop && rules.includes(component.prop)) {
        component.$on('el.form.blur', component.onFieldBlur);
        component.$on('el.form.change', component.onFieldChange);
    }
    if (component.$children && component.$children.length) {
        component.$children
            .forEach(o => resetListenerForElFormItem(o, rules));
    }
}
export default {
    name: 'DemandDetailForm',
    components: {
        DemandPriorityMark,
        DevPlatforms,
        DemandApportion,
        DemandGroupSelect,
        DemandRelatedSystems,
        DemandJoyspaceLinks,
    },
    props: {
        detail: {
            type: Object,
        },
        parentDemandId: {
            type: Number,
            default: -1,
        },
        disableDraft: {
            type: Boolean,
            default: true,
        },
    },
    mixins: [mixinsDemandDetails, mixinFieldText],
    data() {
        return {
            showReceiverList: false,
            receiverWhiteList: [],
            expReleaseDateOptions: {
                disabledDate(time) {
                    const today = new Date((new Date()).toLocaleDateString());
                    return time < today;
                },
            },
            expOfflineDateOptions: {
                disabledDate: (time) => {
                    if (this.data.expectedReleaseDate !== undefined) {
                        return time < new Date((new Date(this.data.expectedReleaseDate)).toLocaleDateString());
                    }
                    const today = new Date((new Date()).toLocaleDateString());
                    return time < today;
                },
            },
            showExpectedOfflineDate: false,
            lazyAutoAcceptance: undefined,
            projectRequestParameters: null,
            roiTooltipVisible: false,
            revenueRowSpan: 12,
            hiddenRevenueOther: true,
        };
    },
    created() {
        this.$nextTick(() => {
            this.focusOnFirstInput();
        });
        this.$on('rules:updated', (newRules = []) => {
            // 切换rules以后先清除掉之前的校验结果
            this.$refs.formIns.clearValidate();
            // 有自定义表单的，也主动清除一下
            if (this.$refs.dynamicFormIns && this.$refs.dynamicFormIns.clearValidate) {
                this.$refs.dynamicFormIns.clearValidate();
            }
            if (!newRules.length) {
                return;
            }
            // 一开始没有rules的formitem不会监听这个事件, 需要获取完了rules以后手动监听一下
            resetListenerForElFormItem(this.$refs.formIns, newRules);
        });
        const { 'to-my-submit': hToMySubmit } = this.$listeners;
        this.hToMySubmit = hToMySubmit;
    },
    async mounted() {
        // 加载基础数据
        this.getBasicInfos();
        this.tagsList = await DemandModel.getTagsList();
    },
    computed: {
        // 检查是否有产品管理的权限
        prdmAvailable() {
            return !!this.$store.getters['root/getActiveModuleByCode']('prdm');
        },
        joyspaceModuleAvailable() {
            return !!this.$store.getters['root/getActiveModuleByCode']('joyspace');
        },
        apportionVisible() {
            const { name } = this.$route;
            // 新增需求  更需求编辑 时需求人可以修改
            return name === 'demandAdd' || (name === 'demandEdit' && this.data.parentDemandId === -1);
        },
        apportionEditable() {
            return this.data.apportionApproved !== 1;
        },
        confidentialDisabled() {
            return this.$route.name !== 'demandAdd';
        },
        hasApp() {
            return (this.data.platformRelations || []).some(x => x.platformName === 'APP');
        },
        showTip() {
            return this.hasApp && this.data.channelRisk === 1;
        },
        demandSourceOrgVisible() {
            return this.parentDemandId === -1 && this.showDemandPriorityScore && this.data.demandSourceId === 1;
        },
    },
    methods: {
        addLinksAndRemoveDuplicates,
        async validateForm() {
            let dynamicFormValid;
            let valid = false;
            try {
                valid = await this.$refs.formIns.validate();
            } catch (error) {
                focusFirstErrorItem();
                return Promise.reject();
            }
            if (this.$refs.dynamicFormIns) {
                try {
                    dynamicFormValid = await this.$refs.dynamicFormIns.validate();
                } catch (error) {
                    focusFirstErrorItem();
                    return Promise.reject();
                }
            }
            // 富文本和成本分摊两个字段比较特殊，需要联合其他字段进行联合校验。所以单拎出来的。
            const customValidateList = [this.checkDescription()];
            if (this.apportionEditable && this.apportionVisible) {
                customValidateList.push(this.checkApportions());
            }
            if (this.demandSourceOrgVisible) {
                customValidateList.push(this.validatePriorityScore());
            }
            const customValidate = await Promise.all(customValidateList);

            if (valid && dynamicFormValid) {
                // 只保留有效error
                return Promise.resolve(customValidate.filter(o => o).join(','));
            }

            return Promise.reject(valid);
        },
        focusOnFirstInput() {
            const { formIns } = this.$refs;
            const firstInput = formIns.$el.querySelector('textarea, input');
            if (firstInput) {
                firstInput.focus();
            }
        },
        changeDemandType(demandTypeId) {
            if (this.$route.name === 'demandAdd') {
                // 如果是活动类需求 显示期望下线日期，否则不显示
                this.showExpectedOfflineDate = demandTypeId === 8;
                if (!this.showExpectedOfflineDate) {
                    this.data.expectedOfflineDate = undefined;
                }
            }
        },
        checkApportions() {
            const { apportionFormItemEl } = this.$refs;
            const demandersAndApportions = {
                apportions: this.data.apportions,
                demanders: this.data.demanders,
            };
            return new Promise((resolve) => {
                validate(demandersAndApportions, this.rules.demanderAndApportions).then(() => {
                    apportionFormItemEl.validateState = '';
                    resolve();
                }, ([err]) => {
                    apportionFormItemEl.validateState = 'error';
                    apportionFormItemEl.validateMessage = err;
                    resolve(err);
                });
            });
        },
        // 富文本改成联合校验
        checkDescription({ text } = {}) {
            const { richEditorFormEl } = this.$refs;
            if (typeof text !== 'undefined') {
                this.data.description = text;
            }
            return new Promise((resolve, reject) => {
                validate(this.data, this.rules.richTextAndDescription).then(() => {
                    richEditorFormEl.validateState = '';
                    this.$refs.richEditorEl.error = false;
                    resolve();
                }, ([err]) => {
                    richEditorFormEl.validateState = 'error';
                    richEditorFormEl.validateMessage = err;
                    this.$refs.richEditorEl.error = true;
                    reject();
                });
            });
        },
        resetForm() {
            this.data = new Demand();
            if (this.apportionVisible && this.apportionEditable) {
                this.$refs.apportionEl.addDemandApportion();
            }
            if (this.$refs.dynamicFormIns) {
                this.$refs.dynamicFormIns.resetForm();
            }
            this.$nextTick(() => {
                this.focusOnFirstInput();
                this.$refs.formIns.clearValidate();
            });
        },
        // 这里需要用set，否则初次新建的时候无法触发响应
        onGroupAdd({ groupId }) {
            this.$set(this.data, 'groupId', groupId);
        },
        checkOrgReceiver(receiver) {
            DemandModel.getCanRecipientDemand({
                userErp: receiver.erp,
            }).then((data) => {
                if (!data.valid) {
                    this.data.receivers = [];
                    this.showReceiverList = true;
                    this.receiverWhiteList = data.users || [];
                }
                DemandModel.getInfos([{
                    name: 'revenuesList',
                    params: {
                        erp: receiver.erp,
                    },
                }]).then((res) => {
                    this.revenuesList = res[0] || [];
                });
            });
        },
        selectReceiver(receiver) {
            Object.assign(receiver, {
                erp: receiver.userErp,
                name: receiver.userName,
            });
            this.data.receivers.push(receiver);
            this.showReceiverList = false;
        },
        // 从layout.demandAdd里挪过来
        async submit() {
            const afterSubmit = (demand) => {
                if (this.data.receivers[0].erp === this.$store.state.user.erp
                    && demand.status === 3) {
                    DemandModel.cmd('distribute', demand).finally(this.saveRefresh);
                } else {
                    this.saveRefresh();
                }
            };
            const hasError = await this.validateForm();
            if (hasError) {
                return Promise.reject();
            }
            return this.data.save(Object.assign({
                demandId: this.demandId,
                parentDemandId: this.parentDemandId,
            },
            // 检查一下是否需要传递systemInfoList
            this.showSystemRelation
                ? { systemInfoList: this.$refs.systemRelation.getSelectedData() }
                : {})).then((demand = {}) => {
                const h = this.$createElement;
                this.toSubmitMessage = this.$message.success({
                    showClose: true,
                    offset: 40,
                    customClass: 'latest-detail__actions__message',
                    message: h('div', { style: 'font-size: 14px; color: #67C23A;' }, [
                        '保存成功，',
                        h('el-button', {
                            props: {
                                type: 'text',
                            },
                            style: 'color: #67C23A; font-size: 14px; text-decoration: underline',
                            on: {
                                click: this.toMySubmit,
                            },
                        }, '点击查看“我提交的”需求列表'),
                    ]),
                });
                afterSubmit({ ...this.data, ...demand });
            });
        },
        toMySubmit() {
            this.hToMySubmit();
            this.toSubmitMessage.close();
        },
        saveDraft() {
            return this.data.saveDraft({
                parentDemandId: this.parentDemandId,
            }).then(() => {
                this.$message.success('保存成功！');
                this.saveRefresh();
            });
        },
        submitAndContinue() {
            this.continueSplit = true;
            return this.submit().catch(() => {
                this.continueSplit = false;
            });
        },
        saveRefresh() {
            this.$emit('on-saved');
            if (this.continueSplit) {
                this.$emit('on-reset-form');
                this.resetForm();
                this.continueSplit = false;
            } else {
                this.$emit('on-close');
            }
        },
        changePriorityScore(val) {
            if (!val) {
                this.data.priorityScore = undefined;
                this.data.priorityScoreMap = undefined;
            } else {
                this.data.priorityScore = val.mark;
                this.data.priorityScoreMap = val;
            }
            this.validatePriorityScore();
        },
        validatePriorityScore() {
            let value = this.data.priorityScore;
            if (this.data.priorityScore === -1) {
                value = undefined;
            }
            const { priorityScoreFormItemEl } = this.$refs;
            return new Promise((resolve, reject) => {
                validate(value, this.rules.priorityScore).then(() => {
                    priorityScoreFormItemEl.validateState = '';
                    resolve();
                }, ([err]) => {
                    priorityScoreFormItemEl.validateState = 'error';
                    priorityScoreFormItemEl.validateMessage = err;
                    reject();
                });
            });
        },
        getLazyAutoAcceptance(erp) {
            if (!erp) {
                return;
            }
            GlobalSetting.getSettingOrgExtendByErp('demand', erp).then((d) => {
                this.lazyAutoAcceptance = d ? d.jsonValue.lazyDemandAutoAcceptance : false;
            });
        },
        revenueChange(value) {
            // 不太友好 前后端必须协商一致  其他：-1
            if (value === -1) {
                this.revenueRowSpan = 8;
                this.hiddenRevenueOther = false;
            } else {
                this.revenueRowSpan = 12;
                this.hiddenRevenueOther = true;
                this.data.revenueOther = undefined;
            }
            this.revenueOtherRule();
        },
        revenueOtherRule() {
            const revenueIdValid = this.rules.revenueId;
            if (this.data.revenueId === -1 && revenueIdValid && revenueIdValid.length > 0) {
                this.rules.revenueOther[1].required = revenueIdValid.filter(r => r.required)[0].required;
                return;
            }
            this.rules.revenueOther[1].required = false;
        },
    },
    watch: {
        detail: {
            handler(newVal) {
                if (!newVal) {
                    return;
                }
                this.data = new Demand(newVal, { loadRichText: true });
            },
        },
        'data.receivers': {
            handler(newVal) {
                const erp = newVal && newVal.length > 0 ? newVal[0].erp : undefined;
                this.getLazyAutoAcceptance(erp);
                this.getOrgFieldsRules().then(() => this.revenueChange(this.data.revenueId));
            },
        },
        'data.product': {
            async handler(newVal) {
                if (newVal && !this.data.receivers.length) {
                    // 当未选择受理人，且选择了关联产品的时候，会填充受理人为产品负责人
                    let receiver;
                    await DemandModel.getProductMember({
                        productId: newVal.id,
                    }).then((data) => {
                        receiver = (data || [])[0];
                    });
                    if (!receiver) {
                        return;
                    }

                    receiver.orgTierCode = receiver.orgId;
                    // 判断产品负责人是不是在部门白名单内
                    await DemandModel.getCanRecipientDemand({
                        userErp: receiver.erp,
                    }).then((data) => {
                        if (data.valid) {
                            this.data.receivers = [];
                            this.data.receivers.push(receiver);
                        }
                    });
                }
            },
        },
        'data.demandSourceId': {
            handler: 'getOrgFieldsRules',
        },
        'data.demandSourceOrg.orgTierCode': {
            handler: 'checkDemandPriorityScore',
        },
        lazyAutoAcceptance: {
            handler(newVal) {
                const { name } = this.$route;
                // 拆分、新增 开启 默认为7
                if (name !== 'demandEdit' && newVal) {
                    this.data.autoAcceptanceDays = 7;
                }
                // 编辑
                if (name === 'demandEdit') {
                    // 有值 但未开启 清空
                    if (this.data.autoAcceptanceDays && !newVal) {
                        this.data.autoAcceptanceDays = undefined;
                    }
                    // 无值 但开启 默认7
                    if (!this.data.autoAcceptanceDays && newVal) {
                        this.data.autoAcceptanceDays = 7;
                    }
                }
            },
        },
        demandSourceOrgVisible: {
            handler(newVal) {
                if (!newVal) {
                    this.data.priorityScore = -1;
                    this.data.priorityScoreMap = undefined;
                }
            },
        },
        'data.apportions': {
            deep: true,
            handler(val = []) {
                // 其中有无效的部门，不触发变化
                if (val.length && val.find(o => !o.apportionOrgId)) {
                    return;
                }
                this.projectRequestParameters = this.parentDemandId === -1 ? {
                    apportionOrgIdList: this.data.apportions.map(a => a.apportionOrgId).join(','),
                } : {
                    demandId: this.parentDemandId,
                };
            },
        },
    },
};
</script>
<style lang="less">
.latest-detail-form{
  &__tags{
    .el-form-item__content{
      line-height: 24px;
    }
  }
  &__text{
    color: #999999;
    font-size: 12px;
  }
}
.latest-detail__actions__message {
    padding: 10px 10px 10px 15px;
}
.receiver-list{
    & .receiver-tips{
        font-size: 14px;
        padding: 0;
        margin: 0;
        color: #333333;
        line-height: 20px;
    }
    & .receiver-info{
        padding: 8px;
    }
    & .receiver-name{
        font-size: 14px;
        margin: 0;
        line-height: 20px;
    }
    & .receiver-org{
        margin: 0;
        line-height: 16px;
        font-size: 12px;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        color: #999999;
        width: 300px;
    }
    & ul{
        margin-top: 16px;
        list-style: none;
        max-height: 200px;
        overflow-y: auto;
        & li{
            height: 52px;
            font-size: 12px;
            display: flex;
            align-items: center;
            border-bottom: #F1F1F1 1px solid;
            &:first-child{
                border-top: #F1F1F1 1px solid;
            }
            &:hover{
                cursor: pointer;
                background-color: #e9f4fe;
            }
        }
        & img{
            display: inline-block;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #f1f1f1;
            margin-right: 2px;
        }
    }
}
</style>
