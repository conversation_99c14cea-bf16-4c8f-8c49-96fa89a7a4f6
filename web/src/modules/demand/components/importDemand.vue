<template>
    <div style="height: 301px;text-align:center">
        <div style="height:40px;">
            <el-button
                type="text"
                @click="downloadTemplate"
            >
                模板下载<i class="el-icon-download" />
            </el-button>
        </div>
        <div
            v-show="!finished"
            style="height:210px;"
        >
            <el-upload
                :action="importUrl"
                drag
                :headers="getHeader"
                :before-upload="checkFile"
                :show-file-list="false"
                :on-progress="fileUploading"
                :on-success="fileUploaded"
            >
                <i class="el-icon-upload" />
                <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                </div>
                <div
                    class="el-upload__tip"
                    slot="tip"
                >
                    只能上传xls或xlsx文件，且不超过20M，总记录数不能超过200条。
                </div>
                <el-progress
                    v-show="percent !== 0"
                    :text-inside="true"
                    :stroke-width="18"
                    :percentage="percent"
                />
            </el-upload>
        </div>
        <div
            v-show="finished"
            class="uploadBox"
        >
            <div v-if="reportError">
                <div style="color:red">
                    需求导入失败！错误原因已标注，请下载错误文件重新导入!
                </div>
                <el-button
                    type="text"
                    @click="downloadError"
                >
                    错误文件下载<i class="el-icon-download" />
                </el-button>
            </div>
            <div v-if="reportOk">
                <div style="color:green">
                    导入成功！
                </div>
            </div>
            <div v-if="fileError">
                <div style="color:red">
                    模板文件错误！
                </div>
            </div>
        </div>
    </div>
</template>

<script type="text/javascript">
import { downloadFile } from '@/plugins/utils';

export default {
    props: {
    },
    data() {
        return {
            reportError: false,
            reportOk: false,
            fileError: false,
            reportUrl: undefined,
            templateUrl: `${window.location.origin}/devops-api/space-demand/api/v1/demands/import/template`,
            importUrl: `${window.location.origin}/devops-api/space-demand/api/v1/demands/import`,
            percent: 0,
            finished: false,
        };
    },
    computed: {
        getHeader() {
            return { Authorization: localStorage.getItem('Authorization') };
        },
    },
    methods: {
        downloadTemplate() {
            downloadFile({
                url: this.templateUrl,
            });
        },
        downloadError() {
            downloadFile({
                url: this.reportUrl,
            });
        },
        checkFile(file) {
            const check = /\.(xlsx|xls)$/.test(file.name);
            if (!check) {
                this.reportOk = false;
                this.reportError = false;
                this.fileError = true;
                this.finished = true;
            }
            return check;
        },
        fileUploading() {
            this.percent = Math.floor(Math.random() * 30 + 50);
        },
        fileUploaded(res) {
            this.percent = 100;
            if (res.code === 200) {
                this.fileError = false;
                if (res.data.error) {
                    this.reportError = true;
                    this.reportUrl = res.data.errorLink;
                } else {
                    this.reportOk = true;
                }
            } else {
                this.reportOk = false;
                this.reportError = false;
                this.fileError = true;
            }
            this.finished = true;
        },
    },
};
</script>


<style lang="less">
.uploadBox {
    height: 210px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
