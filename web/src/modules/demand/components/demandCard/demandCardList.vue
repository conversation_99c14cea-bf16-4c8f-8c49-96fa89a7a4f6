<template>
    <div class="demand-card-list">
        <div class="demand-card-filter">
            <blockquote
                class="j-blockquote"
                v-if="blockquoteVisible"
                style="margin-bottom:8px"
            >
                <i
                    class="el-icon-warning j-mgr8"
                    style="font-size: 14px;color: #2695F1"
                />
                <jacp-text size="12">该页面仅可查看来自于团队空间直接创建的产品和研发类需求。</jacp-text>
                <jacp-icon
                    style="float: right;line-height: 32px;"
                    active
                    name="el-icon-close"
                    @click.native="blockquoteVisible = false"
                />
            </blockquote>
            <el-form
                :model="conditions"
                inline
            >
                <el-form-item>
                    <el-input
                        placeholder="输入标题或编号"
                        prefix-icon="el-icon-search"
                        v-model="conditions.keyword"
                    />
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="conditions.cardType"
                        placeholder="请选择需求类型"
                        clearable
                    >
                        <el-option
                            v-for="(value, key) in cardDemandTypesMap"
                            :key="key"
                            :label="value"
                            :value="+key"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        plain
                        @click="load"
                    >
                        查询
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
        <basic-table
            class="j-table"
            sortable="custom"
            :data="data"
            :columns="columns"
            @sort-change="doSort"
        >
            <!-- 分页 -->
            <el-pagination
                class="j-pagination"
                slot="pager"
                background
                style="margin-top: 16px; text-align: right;"
                :current-page.sync="pager.currentPage"
                :page-size.sync="pager.pageSize"
                :total="pager.total"
                @prev-click="load"
                @next-click="load"
                @current-change="load"
                @size-change="load"
                layout="prev, pager, next, total, sizes, jumper"
            />
        </basic-table>
    </div>
</template>
<script>
import { snake } from 'naming-style';
import debounce from 'lodash/debounce';
import DemandCard, { demandCardColumns, cardDemandTypesMap } from '$module/models/demandCard';

import Pager from '@/plugins/pager';

export default {
    name: 'DemandCardList',
    data() {
        return {
            blockquoteVisible: true,
            data: [],
            conditions: {},
            columns: demandCardColumns,
            pager: new Pager({ pageSize: 20 }),
            cardDemandTypesMap,
        };
    },
    created() {
        this.$watch('conditions', {
            deep: true,
            handler: debounce(this.load.bind(this), 500),
        });
    },
    mounted() {
        this.load();
    },
    methods: {
        async load() {
            this.data = await DemandCard.getList({ ...this.conditions }, this.pager);
        },
        doSort({ prop, order } = {}) {
            if (!order) {
                delete this.conditions.orderType;
                delete this.conditions.orderField;
            } else {
                this.conditions = {
                    ...this.conditions,
                    orderType: order.includes('asc') ? 'asc' : 'desc',
                    orderField: snake(prop),
                };
            }
        },
    },
};
</script>
<style lang="less" scoped>
.demand-card-list /deep/ .j-table{
  background-color: #fff;
  height: calc(100vh - 224px);
  .el-table th>.cell{
    padding-left: var(--gutter--large);
  }
}
.demand-card-filter{
  background-color: #fff;
  padding-top: var(--gutter--base);
  padding-bottom: var(--gutter--base);
  border-bottom: var(--border--hr);
  .el-form-item{
    margin-bottom: 0;
  }
  .el-button{
    border-radius: var(--radius--default);
  }
}
</style>
