<template>
    <div
        class="demands-root-header"
    >
        <!-- 有产品管理权限的显示出产研需求入口 -->
        <el-dropdown
            class="demand-title"
            placement="bottom"
            trigger="click"
            v-if="prdmAvailable"
        >
            <span>
                业务需求
                <jacp-icon
                    active
                    name="el-icon-arrow-down el-icon--right"
                />
            </span>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                    @click.native="$router.push({
                        name: 'cardDemands',
                        query: { ref: value } })"
                >
                    产研需求
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
        <!--   <p
            v-else
            class="demand-title"
        >
            需求管理
        </p> -->
        <el-divider
            direction="vertical"
            class="title-divider"
            style="transform: translateY(50%);"
        />
        <el-tabs
            class="jacp-tabs--gray jacp-tabs"
            v-bind="$attrs"
            v-on="$listeners"
        >
            <!-- FIXME: 命名外层处理了tabPanes的数组，这里又来了，很不干净 -->
            <el-tab-pane
                v-for="tab in tabPanes
                    .filter(tab => tab !== 'apportion' || (tab === 'apportion' && apportionTabVisible))"
                :key="tab"
                :name="tab"
            >
                <!-- tab label -->
                <span slot="label">
                    <template v-if="tab === 'apportion'">
                        <!-- FIXME: 这里应该针对tabPanes给一个追加dot的通用处理 -->
                        <el-badge
                            is-dot
                            :hidden="!showApportionDot"
                            style="line-height: 20px"
                        >
                            {{ $t(`jacp.tab.${tab}`) }}
                        </el-badge>
                    </template>
                    <template v-else>
                        {{ $t(`jacp.tab.${tab}`) }}
                    </template>
                </span>
                <!-- tab content -->
            </el-tab-pane>
        </el-tabs>
        <!-- 我的产研需求 -->
    </div>
</template>
<script>
// FIXME: 先简单对demandIndex做一个拆分，这里处理header的部分
export default {
    name: 'DemandRootHeader',
    inheritAttrs: false,
    props: {
        tabPanes: { type: Array, default: () => [] },
    },
    computed: {
        // 检查是否有产品管理的权限
        prdmAvailable() {
            return this.$store.getters['root/getActiveModuleByCode']('prdm');
        },
        showApportionDot() {
            return this.$store.state.demands.apportionCount > 0;
        },
        apportionTabVisible() {
            return this.$store.state.demands.apportionTabVisible;
        },
    },
}; </script>
<style lang="less">
@tabHeight: 40px;
.demands-root-header {
    display: flex;
    align-items: center;
    .jacp-tabs .el-tabs__nav{
         height:@tabHeight;
    }
    .jacp-tabs .el-tabs__item{
        height:@tabHeight;
        line-height:@tabHeight;
        font-size: var(--font-size--subtitle);
        padding: 0 12px;
    }
}
.demand-title {
    margin: 0;
    padding: 0 16px 0  24px;
    font-size: 14px;
    font-weight: 600;
    color: var(--color--base--content);
}
.title-divider {
      margin: 0 var(--gutter--medium) 0 0 ;
  }
</style>
