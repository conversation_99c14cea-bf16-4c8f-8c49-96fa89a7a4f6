<template>
    <div class="apportion-wrapper">
        <div class="demands__operates">
            <el-input
                class="j-input--background j-mgr16"
                placeholder="输入标题或编号"
                prefix-icon="el-icon-search"
                @keydown.enter.native="filterList"
                @clear="filterList"
                clearable
                v-model="keyWord"
            />
            <jacp-button
                size="mini"
                @click.native="() => filterList()"
            >
                查询
            </jacp-button>
        </div>
        <div class="demands__listwrap">
            <div class="demands__list">
                <table
                    class="el-table j-table"
                    ref="tableIns"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                >
                    <jacp-table-header
                        :columns="columns"
                    />
                    <jacp-table-body
                        :columns="columns"
                        :rows="list"
                        :current-row="currentRow"
                        :slot-map="['demand.name',
                                    'demand.proposer',
                                    'apportion.apportion', 'apportion.apportionOrgName', 'demand.recipient']"
                        @on-row-click="handleRowClick"
                    >
                        <template
                            slot-scope="scope"
                            slot="demand.name"
                        >
                            <el-tooltip
                                :disabled="scope.demand.name.length < 30"
                                :content="scope.demand.name"
                                popper-class="j-table__namepopper"
                                placement="top"
                            >
                                <div
                                    class="j-text-overflow"
                                    style="max-width: 25vw"
                                >
                                    <span class="j-table__link">{{ scope.demand.name }}</span>
                                </div>
                            </el-tooltip>
                        </template>
                        <template
                            slot-scope="scope"
                            slot="demand.proposer"
                        >
                            <jacp-erp
                                class="is-stopPropagation"
                                :data="scope.demand.proposer"
                                :disable-timline="$utils.isCurrentUser(scope.demand.proposer)"
                                :key="scope.demand.proposer.erp"
                                display-erp-mode="block"
                            />
                        </template>
                        <template
                            slot-scope="scope"
                            slot="demand.recipient"
                        >
                            <jacp-erp
                                class="is-stopPropagation"
                                :data="scope.demand.recipient"
                                :disable-timline="$utils.isCurrentUser(scope.demand.recipient)"
                                :key="scope.demand.recipient.erp"
                                display-erp-mode="block"
                            />
                        </template>
                        <template
                            slot-scope="scope"
                            slot="apportion.apportionOrgName"
                        >
                            <span class="j-text-warningcolor">{{ scope.apportion.apportionOrgName }}</span>
                        </template>
                        <div
                            class="is-stopPropagation"
                            slot-scope="scope"
                            slot="apportion.apportion"
                        >
                            <strong class="j-text-warningcolor">{{ scope.apportion.apportion }}%</strong>
                            <i
                                v-show="+scope.apportion.apportion !== 100"
                                class="el-icon-view j-mgl32 j-hover-highlight"
                                @click.prevent="showApportionDetails(scope.demand.id, scope)"
                            />
                        </div>
                        <template
                            slot-scope="scope"
                            slot="oprations"
                        >
                            <div class="demandslist-operations is-stopPropagation">
                                <a
                                    v-for="act in apportionOpration"
                                    class="demandslist-operations__item"
                                    @click.prevent="handleAction(act, scope)"
                                    :key="act"
                                >
                                    {{ $t('jacp.button')[act] }}
                                </a>
                            </div>
                        </template>
                    </jacp-table-body>
                </table>
            </div>
            <!-- 分页 -->
            <div class="demands__app__listfooter">
                <el-pagination
                    class="demands__pagenation"
                    @size-change="handleSizeChange"
                    @current-change="filterList({ page: $event })"
                    :page-size.sync="data.size"
                    :total="data.total"
                    :current-page.sync="current"
                    layout="prev, pager, next, sizes, total, jumper"
                />
            </div>
        </div>
        <el-dialog
            title="「不同意」原因："
            :visible.sync="rejectParams.reasonDialogVisible"
            width="40%"
        >
            <el-select
                :value="rejectParams.rejectReason[rejectParams.reasonId]"
                @change="handleReasonChange"
            >
                <el-option
                    v-for="(reason, index) in rejectParams.rejectReason"
                    :label="reason"
                    :value="index"
                    :key="reason"
                />
            </el-select>
            <el-input
                class="j-mgt16"
                type="textarea"
                v-model="rejectParams.reason"
                v-show="isCustomReason"
                placeholder="填写具体原因"
                :rows="2"
            />
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="rejectParams.reasonDialogVisible = false">取 消</el-button>
                <el-button
                    type="primary"
                    @click="doReject"
                >确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="currentTaskVisible"
            width="40%"
            :title="currentTaskDetailTitle"
        >
            <apportion-detail :data="currentTask" />
        </el-dialog>
    </div>
</template>
<script>
import { ApportionModel, Apportion } from '@/models/apportion';
import { closest } from '@/plugins/utils';
import MixinsTable from '@/mixins/mixin.table';
import ApportionDetail from './apportionDetail';
import mixinsApportion from './mixin.apportion';

// const ApportionModel = new ApportionModel();
export default {
    name: 'ApportionList',
    mixins: [MixinsTable, mixinsApportion],
    components: {
        ApportionDetail,
    },
    props: {
        // demandAuthority: { type: Object },
    },
    data() {
        return {
            data: {},
            list: [],
            columns: ApportionModel.getColumns(),
            currentRow: '',
            // currentTask: null,
            // currentTaskDetail: null,
            keyWord: '',
            apportionOpration: Object.values(ApportionModel.ACTION),
            rejectParams: {
                reasonId: 0,
                reason: '',
                task: null,
                rejectReason: ApportionModel.rejectReason,
                // textareaVisible: false,
                reasonDialogVisible: false,
            },
            pageSize: 20,
            current: 1,
        };
    },
    mounted() {
        // 没有权限查看分摊页面的不再调用该接口，父组件判定后跨级调用
        // this.filterList();
    },
    methods: {
        handleRowClick({ demand }, ev) {
            // 不能加stop的字段需要增加一个class去stop一下
            if (!closest(ev.target, '.is-stopPropagation')) {
                this.handleLinkClick(demand);
            }
        },
        handleLinkClick(scope) {
            this.setActive(scope.id);
            this.$emit('on-demand-focus', scope);
        },
        handleAction(act, item) {
            const { ACTION } = ApportionModel;
            switch (act) {
            case ACTION.CONFIRM:
                this.askWhenConfirm(item);
                break;
            case ACTION.REJECT:
                this.askWhenReject(item);
                break;
            default:
                break;
            }
        },
        askWhenConfirm(item) {
            const { ACTION } = ApportionModel;
            const doConfirm = Apportion.confirmTask.bind(this, {
                taskId: item.id,
                action: 1,
            });
            this.$confirm(
                `同意将需求「${item.demand.demandCode}」按「${item.apportion.apportionOrgName}」「${item.apportion.apportion}%」 分摊比例进行分摊？`,
                '提示', {
                    confirmButtonText: this.$t(`jacp.button.${ACTION.CONFIRM}`),
                    cancelButtonText: '取消',
                    type: 'warning',
                },
            ).then(doConfirm).then(this.filterList, this.filterList);
        },
        askWhenReject(item) {
            this.rejectParams.task = item;
            this.rejectParams.reasonDialogVisible = true;
        },
        doReject() {
            const { reason, rejectReason, reasonId } = this.rejectParams;
            const doRejectFn = Apportion.confirmTask.bind(this);
            const cb = () => {
                this.rejectParams.reasonDialogVisible = false;
                this.filterList();
            };
            return doRejectFn({
                taskId: this.rejectParams.task.id,
                action: 2,
                reason: this.isCustomReason ? reason : rejectReason[reasonId],
            }).then(cb, cb);
        },
        handleReasonChange(val) {
            this.$set(this.rejectParams, 'reasonId', val);
        },
        filterList(params) {
            const { keyWord, pageSize } = this;
            if (params && typeof params.page !== 'undefined') {
                this.current = params.page;
                // this.$set(this.data, 'pages', params.page);
            }
            Apportion.getTasks({
                keyWord,
                current: this.current,
                pageSize,
            }).then((data) => {
                this.data = data;
                if (keyWord) {
                    return;
                }
                this.$store.commit('demands_update', {
                    apportionCount: data.total,
                });
            });
        },
        handleSizeChange($event) {
            this.pageSize = $event;
            this.filterList();
        },
    },
    computed: {
        isCustomReason() {
            const { rejectReason, reasonId } = this.rejectParams;
            return reasonId === rejectReason.length - 1;
        },
    },
    watch: {
        data: {
            handler(val) {
                if (!Array.isArray(val.records)) {
                    return;
                }
                this.list = val.records.slice();
            },
        },
    },
};
</script>

<style lang="less">
.apportion-wrapper {
    width: 100%;
    height: calc(100vh - 100px - 16px);
    .demands {
        &__operates {
            height: 48px;
            width: 100%;
            padding: 0 16px;
            background-color: #fff;
            display: flex;
            margin-bottom: 1px;
            align-items: center;
            .el-input{
                max-width: 180px;
            }

        }
        &__app__listfooter {
            height: 64px;
            padding: 16px 0;
        }
    }
}
</style>
