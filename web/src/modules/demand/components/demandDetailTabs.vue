<template>
    <el-tabs
        class="detail-tabs"
        v-model="activeTab"
        @tab-click="loadTabData()"
    >
        <!-- 备注 -->
        <el-tab-pane
            label="备注"
            name="remark"
        >
            <jacp-record-pane
                mode="remark"
                class="detail-tabs__remarklist"
                :list="data.remark"
            />
        </el-tab-pane>
        <el-tab-pane
            label="研发信息"
            name="devInsight"
        >
            <dev-insight
                v-if="data.devInsight.length"
                class="detail-tabs__dev-milestone"
                :data="data.devInsight[0]"
            />
            <div
                class="j-nodata"
                v-if="!data.devInsight.length"
            >
                无数据, 需要先分配到团队空间
            </div>
        </el-tab-pane>
        <el-tab-pane
            label="状态流程记录"
            name="processHistory"
        >
            <jacp-record-pane
                mode="simple"
                time-format="YYYY-MM-DD HH:mm"
                :list="data.processHistory"
            >
                <div
                    slot="prefix"
                    slot-scope="{ row }"
                >
                    {{ row.demandStatus|jacp-status-text }}
                </div>
                <div slot-scope="{ row }">
                    <template v-if="row.remark.length < 10">
                        {{ row.remark }}
                    </template>
                    <el-tooltip
                        v-else
                    >
                        <div
                            style="max-width: 250px"
                            slot="content"
                        >
                            {{ row.remark }}
                        </div>
                        <div
                            class="j-text-overflow"
                        >
                            {{ row.remark }}
                        </div>
                    </el-tooltip>
                </div>
            </jacp-record-pane>
        </el-tab-pane>
        <!-- 操作历史 -->
        <el-tab-pane
            label="操作历史"
            name="history"
        >
            <jacp-record-pane
                mode="simple"
                time-format="YYYY-MM-DD HH:mm"
                :list="data.history"
            >
                <div
                    slot="prefix"
                    slot-scope="{ row }"
                >
                    {{ row.demandStatus|jacp-status-text }}
                </div>
                <div slot-scope="{ row }">
                    <template v-if=" !row.remark || row.remark.length < 10">
                        {{ row.remark }}
                    </template>
                    <el-tooltip
                        v-else
                    >
                        <div
                            style="max-width: 250px"
                            slot="content"
                        >
                            {{ row.remark }}
                        </div>
                        <div
                            class="j-text-overflow"
                        >
                            {{ row.remark }}
                        </div>
                    </el-tooltip>
                </div>
            </jacp-record-pane>
        </el-tab-pane>
        <!-- 操作历史 -->
        <el-tab-pane
            label="需求变更记录"
            name="changes"
        >
            <change-history :list="data.changes" />
            <div
                class="j-nodata"
                v-if="!data.changes.length"
            >
                无数据
            </div>
        </el-tab-pane>
        <!-- TODO: 从后端动态获取挂载点 -->
        <template v-if="demand.id">
            <el-tab-pane
                v-for="(addon, index) in addons"
                :label="addon.name"
                :name="addon.name"
                :key="`module-point-${addon.id}-${index}`"
                lazy
            >
                <LocalModulePoint
                    height="500"
                    :addon="addon"
                    :context-params="extensionPointContextParams"
                />
            </el-tab-pane>
        </template>
    </el-tabs>
</template>

<script type="text/javascript">
import moment from 'moment';
import DemandModel from '@/models/demands';
import devInsight from './devInsightDetail';
import ChangeHistory from '@/modules/card/components/cardChange/changeHistory';
import { loadDemandAddons, demandDetailTabExtensionPoint } from '@/models/app';
// 支持挂载点
import LocalModulePoint from '@/modules/root/components/modulePointRender';

export default {
    components: {
        devInsight,
        ChangeHistory,
        LocalModulePoint,
    },
    props: {
        demand: Object,
        demandId: {
            type: [Number, String],
            required: true,
        },
    },
    data() {
        return {
            data: {
                remark: [],
                history: [],
                processHistory: [],
                // demandStakeholder: [],
                devInsight: [],
                demandMilestone: [],
                // pmpTaskList: [],
                changes: [],
            },
            dataFormatter: {
                history: list => list.map(item => Object.assign(item, {
                    remark: item.remark || item.description,
                    cTime: item.cTime || item.uTime,
                })),
            },
            activeTab: 'remark',
            developActive: {},
            addons: [],
        };
    },
    async mounted() {
        this.loadTabData();
        this.addons = await loadDemandAddons(demandDetailTabExtensionPoint);
    },
    methods: {
        refresh(tabName = 'history') {
            if (this.activeTab.toLowerCase().includes(tabName.toLowerCase())) {
                this.loadTabData(this.activeTab);
            }
        },
        loadTabData(tabName) {
            const tab = tabName || this.activeTab;
            if (!tab || this.addons.map(item => item.name).includes(tab)) {
                return;
            }
            const queryData = [tab];
            const queryParams = { demandId: this.demandId };
            if (tab === 'changes') {
                DemandModel.getCardChangeListByDemandId({
                    demandId: this.demandId,
                }).then((res) => {
                    this.data.changes = res;
                });
                return;
            }
            DemandModel.getInfos(queryData.map(item => ({
                name: item,
                params: queryParams,
            }))).then((res) => {
                queryData.forEach((key, i) => {
                    const fn = this.dataFormatter[key] || (value => value);
                    this.data[key] = fn(res[i]);
                });
            });
        },
        dateFormatter(row, column, cellValue) {
            return moment(cellValue).format('YYYY-MM-DD');
        },
        developActiveFormatter(row, column, cellValue) {
            return this.developActive[cellValue];
        },

    },
    computed: {
        // 挂载点用的
        extensionPointContextParams() {
            return demandDetailTabExtensionPoint.contextGenerator(this.demand);
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.detail-tabs{
    &__input {
        margin-bottom: 10px;
    }
    &__remarklist{
        margin-bottom: 20px;
    }
    &-remark__input{
        position: fixed;
        bottom: 62px;
        width: 100%;
    }
    &__content{
        display: inline;
        margin-right: 5px;
    }
    &__link{
        display: inline;
        padding: 0;
    }
    &__demand-milestone{
        width: calc(~"100% - 20px");
        margin-bottom: 20px;
    }
    &__demand-stakeholder{
      width: calc(~"100% - 20px");
      margin-bottom: 20px;
    }
    &__dev-milestone{
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px dashed #eee;
        &:last-child{
            padding-bottom: 0;
            border-bottom: none;
        }
    }
    &__label{
        color: #999;
        display: block;
        font-size: 14px;
        margin-bottom: 10px;
        &:after{
            content: ':';
        }
    }
}
</style>
