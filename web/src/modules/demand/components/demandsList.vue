<template>
    <div class="demand-list">
        <div
            ref="demandsListTablewrapperEl"
        >
            <table
                id="jacp-table_virtual"
                class="el-table j-table jacp-table_virtual jacp-tableHead-fix "
                :class="{'jacp-table-fixed-left': columnFixed.name && fixStyleFlag.name, 'jacp-table-fixed-right': columnFixed.operation && fixStyleFlag.operation}"
                ref="tableIns"
                cellspacing="0"
                cellpadding="0"
                border="0"
            >
                <jacp-table-header-resize
                    :disabled-sortby-columns="isGroup"
                    :columns="columns"
                    :sorted-column="sortCondition.orderField"
                    :sorted-order="sortCondition.orderType"
                    :expand-all.sync="expandAll"
                    :batch="batch"
                    :column-options="columnOptions"
                    @sort="handleHeaderSort"
                    @resize-column="resizeColumn"
                />
                <jacp-table-body-resize
                    :columns="columns"
                    :rows="data.filter(item => item.deleted === 0)"
                    :current-row="currentRow"
                    :sortable="sortable"
                    :slot-map="['seq']"
                    :batch="batch"
                    :error-rows="errorRows"
                    @on-row-click="handleRowClick"
                >
                    <template
                        slot-scope="scope"
                        slot="batch"
                    >
                        <span
                            :class="{
                                'batch-unchecked': !batchList.includes(scope.id),
                                'batch-checked': batchList.includes(scope.id)}"
                        />
                    </template>
                    <template
                        slot="seq"
                        slot-scope="scope"
                    >
                        <div
                            class="j-mgr8"
                            style="text-align: center;width: 100%;"
                        >
                            {{ scope.seq }}
                        </div>
                    </template>
                    <template
                        slot="name"
                        slot-scope="scope"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <el-tooltip
                            :content="scope.name"
                            popper-class="j-table__namepopper"
                            placement="top"
                            :open-delay="300"
                        >
                            <div
                                :class="[
                                    'demand-list__item__name',
                                    'j-table__item__name--level'+( scope.level - 1),
                                    {
                                        'j-table__item__expand': scope.$expandStatus,
                                        'j-table__item__haschildren': scope.hasChild
                                    },
                                    ' td-content']"
                            >
                                <i
                                    v-if="!scope.$level || scope.$level < 7"
                                    @click.stop="$emit('toggle-children', scope, data)"
                                    class="j-table__item__shrinkicon"
                                    :class="scope.$expandStatus ? 'icon-minus' : 'icon-plus'"
                                />
                                <span class="j-table__link">{{ scope.name }}</span>
                                <el-tooltip
                                    effect="dark"
                                    placement="top"
                                    v-if="scope.system !== 'jacp'"
                                    :content="`需求来自${scope.system}系统， 不可编辑`"
                                    :open-delay="300"
                                >
                                    <i
                                        class="j-other demand-list__item__other"
                                    />
                                </el-tooltip>
                                <i
                                    :class="[statusIcon[scope.apportionApproved],
                                             'apportion-status__icon', 'is-stopPropagation']"
                                    :style="{
                                        position: 'absolute',
                                        right: '8px',
                                        top: '50%',
                                        transform: 'translateY(-6px)',
                                    }"
                                    @click.prevent="showApportionDetails(scope.rootDemandId)"
                                />
                            </div>
                        </el-tooltip>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="demandTypeId"
                    >
                        <div>{{ typeList[scope.demandTypeId] }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="time"
                    >
                        <div>
                            <span v-if="scope.currentKey === 'bizStatusUTime'">
                                {{ getContinueTime(scope.bizStatusArchiveTime) }}</span>
                            <span v-else>{{ +scope[scope.currentKey]|jacp-local-time }}</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="space"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <div>
                            <span
                                v-if="scope.space"
                                @click.stop="cardDetail(scope)"
                                class="demand-list__row--space"
                            >{{ scope.space.name }}</span>
                            <span
                                class="j-table__row--nodata j-nodata"
                                v-else
                            >未分配</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="spaceSprint"
                    >
                        <div>
                            <span v-if="scope.spaceSprint">{{ scope.spaceSprint.name }}</span>
                            <div v-else>
                                --
                            </div>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="priority"
                    >
                        <div v-if="scope.priorityId">{{ priorityMap[scope.priorityId] }}</div>
                        <div v-else>
                            --
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="person"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <div>
                            <jacp-erp
                                :data="scope[scope.currentKey]"
                                class="is-stopPropagation"
                                :disable-timline="$utils.isCurrentUser(scope[scope.currentKey])"
                                display-erp-mode="block"
                            />
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="multiPerson"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <el-tooltip
                            class="item"
                            :open-delay="300"
                            :content="scope.demanders.map(item => item.name).join('，')"
                            placement="top"
                        >
                            <div>
                                <jacp-erp
                                    v-for="person in scope[scope.currentKey]"
                                    :data="person"
                                    :disable-timline="$utils.isCurrentUser(person)"
                                    :key="person.erp"
                                    display-erp-mode="block"
                                    class="is-stopPropagation"
                                />
                            </div>
                        </el-tooltip>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="follower"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <el-tooltip
                            class="item"
                            v-if="scope.followers.length"
                            :open-delay="300"
                            :content="scope.followers.map(item => item.name).join('，')"
                            placement="top"
                        >
                            <div class="td-content">
                                {{ scope.followers.map(item => item.name).join('，') }}
                            </div>
                        </el-tooltip>
                        <div v-else>
                            --
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="status"
                    >
                        <div>{{ scope.status|jacp-status-text }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="sourceType"
                    >
                        <div>{{ sourceList[+scope.demandSourceId] }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="oprations"
                    >
                        <slot
                            name="oprations"
                            :scope="scope"
                        >
                            <template v-if="oprations">
                                <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                                <div :class="{'table-lightbox': batch}" />
                                <div class="demand-list__flexbox is-stopPropagation">
                                    <!-- 需求的基础操作 -->
                                    <demands-list-operations
                                        :is-handler="$utils.isCurrentUser(scope.processor)"
                                        :is-proposer="$utils.isCurrentUser(scope.proposer)"
                                        :status="scope.status"
                                        :evaluated="scope.evaluated"
                                        :root="scope.parentDemandId === -1"
                                        :is-other-source="scope.system !== 'jacp'"
                                        :has-child="scope.hasChild"
                                        :has-dev-status="scope.hasDevStatus"
                                        :disabled-actions="disabledAction({
                                            approved: scope.apportionApproved,
                                            status: scope.status,
                                            proposerErp: scope.proposerErp,
                                            estimatedDaysApprovalResult: scope.estimatedDaysApprovalResult,
                                        })"
                                        :avaliable-operations="avaliableOperations"
                                        @on-action="handleAction($event, scope)"
                                    />
                                    <!-- 需求的分组操作 -->
                                    <template
                                        v-if="(!avaliableOperations
                                            || avaliableOperations && avaliableOperations.includes('group'))"
                                    >
                                        <a
                                            href="#"
                                            class="demandslist-operations__item"
                                            :ref="`joinInGroupBtnEl${scope.id}`"
                                            @click.prevent="$_joinInGroup({
                                                demandId: scope.id,
                                                reference: $refs[`joinInGroupBtnEl${scope.id}`]
                                            })"
                                        >加入分组</a>
                                        <a
                                            href="#"
                                            class="demandslist-operations__item"
                                            v-if="isGroup && isMyGroup"
                                            @click.prevent.stop="$_detachFromGroup({demandId: scope.id})"
                                        >移出分组</a>
                                    </template>
                                    <!-- 复制 -->
                                    <el-dropdown
                                        trigger="click"
                                        v-if="scope.system === 'jacp' && (!avaliableOperations
                                            || avaliableOperations && avaliableOperations.includes('copy'))"
                                    >
                                        <a
                                            href="#"
                                            class="demandslist-operations__item"
                                            v-show="canAdd || checkSplitAuthority(scope)"
                                        >复制</a>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item
                                                v-show="canAdd"
                                                @click.native="copyDemand(scope, 'copy')"
                                            >
                                                复制内容
                                            </el-dropdown-item>
                                            <el-dropdown-item
                                                v-show="checkSplitAuthority(scope)"
                                                @click.native="copyDemand(scope, 'copy-split')"
                                            >
                                                复制内容及关系
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </template>
                        </slot>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="pmpProjectName"
                    >
                        <div v-if="scope.bizProjectInfo">
                            {{ scope.bizProjectInfo.pmpProjectName }}
                        </div>
                        <span v-else>--</span>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="projectTypeName"
                    >
                        <div v-if="scope.bizProjectInfo">
                            {{ scope.bizProjectInfo.projectTypeName }}
                        </div>
                        <span v-else>--</span>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="pmpProjectCode"
                    >
                        <div v-if="scope.bizProjectInfo">
                            {{ scope.bizProjectInfo.pmpProjectCode }}
                        </div>
                        <span v-else>--</span>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="plannedHours"
                    >
                        <div>{{ scope.workload ? (scope.workload.plannedHours || '0') : '0' }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="actualHours"
                    >
                        <div>{{ scope.workload ? (scope.workload.actualHours || '0') : '0' }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="stageId"
                    >
                        <div>{{ scope.stageName }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="devStatusName"
                    >
                        <div>{{ scope.devStatusName || '--' }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="tags"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <el-tooltip
                            v-if="scope.tags && scope.tags.length"
                            :content="scope.tags.map(item => item.name).join('，')"
                            placement="top"
                            :open-delay="300"
                        >
                            <div>
                                <custom-tag
                                    class="jacp-form-tags__item"
                                    :key="item.name"
                                    color="#2695F1"
                                    style="margin-bottom: 0;"
                                    v-for="item in scope.tags"
                                >
                                    {{ item.name }}
                                </custom-tag>
                            </div>
                        </el-tooltip>
                        <div v-else>
                            --
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="lastComment"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <el-tooltip
                            v-if="scope.lastComment"
                            popper-class="jacp-max-width600"
                            :content="scope.lastComment"
                            :open-delay="300"
                        >
                            <div class="j-text-overflow td-content">
                                {{ scope.lastComment }}
                            </div>
                        </el-tooltip>
                        <span v-else> -- </span>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="urgent"
                    >
                        <div>{{ scope.urgent?'是':'否' }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="platformRelations"
                    >
                        <!-- 单元格遮罩 阻止内部子元素点击事件 -->
                        <div :class="{'table-lightbox': batch}" />
                        <el-tooltip
                            class="item"
                            :open-delay="300"
                            v-if="scope.platformRelations && scope.platformRelations.length"
                            :content="scope.platformRelations.map(item => item.platformName).join('，')"
                            placement="top"
                        >
                            <div class="td-content">
                                {{ scope.platformRelations.map(item => item.platformName).join('，') }}
                            </div>
                        </el-tooltip>
                        <div v-else>
                            --
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="planDate"
                    >
                        <div>
                            <span v-if="scope.cardRelease && scope.cardRelease.planDate">
                                {{ scope.cardRelease.planDate|jacp-local-time('YYYY-MM-DD') }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="evaluation"
                    >
                        <div>
                            <span v-if="scope.evaluation && scope.evaluation[evaluation[scope.currentKey]]">
                                {{ scope.evaluation[evaluation[scope.currentKey]]|evaluationHZ(evaluation[scope.currentKey]) }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="channelRisk"
                    >
                        <div>
                            <span
                                v-if="scope.channelRisk !== null"
                                :style="{color: channelRiskColor(scope.channelRisk)}"
                            >
                                {{ scope.channelRisk|channelRiskHZ }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="processTime"
                    >
                        <div>
                            <span v-if="scope.processTime">
                                {{ scope.processTime|jacp-local-time }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>

                    <template
                        slot-scope="scope"
                        slot="cardCompleteTime"
                    >
                        <div>
                            <span v-if="scope.cardCompleteTime">
                                {{ scope.cardCompleteTime|jacp-local-time }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="acceptTime"
                    >
                        <div>
                            <span v-if="scope.acceptTime">
                                {{ scope.acceptTime|jacp-local-time }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="revenueEndDate"
                    >
                        <div>
                            <span v-if="scope.revenueEndDate">
                                {{ scope.revenueEndDate|jacp-local-time('YYYY-MM-DD') }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="groupNames"
                    >
                        <div :class="{'table-lightbox': batch}" />
                        <el-tooltip
                            class="item"
                            :open-delay="300"
                            v-if="scope.groupNames"
                            :content="scope.groupNames"
                            placement="top"
                        >
                            <div class="td-content">
                                {{ scope.groupNames }}
                            </div>
                        </el-tooltip>
                        <div v-else>
                            --
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="priorityScore"
                    >
                        <div>
                            <span v-if="+scope.priorityScore > -1">
                                {{ scope.priorityScore }}
                            </span>
                            <span v-else>--</span>
                        </div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="revenueName"
                    >
                        <div>{{ scope.revenue ? (scope.revenueId === -1 ? `${scope.revenue.name}-${scope.revenueOther}` : scope.revenue.name) : '--' }}</div>
                    </template>
                    <template
                        slot-scope="scope"
                        slot="accpetorOrg"
                    >
                        {{ scope.bizContacts|contactOrgName }}
                    </template>
                </jacp-table-body-resize>
            </table>
            <!--
            <div
                class="leftfixedwrap"
                :style="divStyle"
            />-->
        </div>
        <el-dialog
            :visible.sync="currentTaskVisible"
            width="40%"
            :title="currentTaskDetailTitle"
        >
            <apportion-detail :data="currentTask" />
        </el-dialog>
    </div>
</template>

<script type="text/javascript">
import Sortable from 'sortablejs';
import flattenDeep from 'lodash/flattenDeep';
import moment from 'moment';
import { closest, equalIgnoreCase, isCurrentUser } from '@/plugins/utils';
import DemandModel from '@/models/demands';
import { demandOperations } from '@/models/config';
import Card from '@/models/card';
import Dialog from '@/models/dialog';
import DragHandler from '@/modules/_components/dragHandler';
import MixinsTable from '@/mixins/mixin.table';
import MixinsRouter from '@/mixins/mixin.router';
import { ApportionModel } from '@/models/apportion';
import DemandsListOperations from './demandsListOperations';
// import JacpDemandGroupMgr from './demandGroupMgr';
import mixinApportion from './mixin.apportion';
import ApportionDetail from './apportionDetail';
import mixinGroup from './mixin.demandGroup';

export default {
    name: 'DemandsList',
    mixins: [MixinsTable, mixinApportion, mixinGroup, MixinsRouter],
    components: {
        DemandsListOperations,
        // JacpDemandGroupMgr,
        ApportionDetail,
    },
    props: {
        type: String,
        data: {
            type: Array,
            default: () => [],
        },
        sortable: {
            type: Boolean,
            default: false,
        },
        orgColumns: {
            type: Array,
        },
        sortCondition: {
            type: Object,
            default: () => ({}),
        },
        isGroup: Boolean,
        isExpandAll: Boolean,
        batch: {
            type: Boolean,
            default: false,
        },
        batchList: {
            type: Array,
            default: () => [],
        },
        canAdd: {
            type: Boolean,
            default: false,
        },
        oprations: { type: Boolean, default: true },
        avaliableOperations: { type: Array },
    },
    data() {
        return {
            childrenCache: {},
            defaultSort: {
                prop: this.type === 'draft' ? 'cTime' : 'submitTime',
                order: 'descending',
            },
            priorityMap: {},
            typeList: [],
            sourceList: [],
            revenuesMap: {},
            sortableIns: undefined,
            columns: this.orgColumns,
            // 除数据以外的附加列：操作
            extraColumns: [{
                name: 'oprations',
                show: 1,
                index: this.orgColumns.length,
                label: '操作',
            }],
            cardStatusMap: {},
            currentRow: this.$route.query.demandId || 0,
            expandAll: this.isExpandAll,
            errorRows: [],
            // 需求分担状态
            statusIcon: ApportionModel.statusIcon,
            columnOptions: {
                demandCode: '160px',
                priorityId: '150px',
                demandSourceId: '120px',
                demandTypeId: '130px',
                follower: '140px',
                expectedReleaseDate: '170px',
                bizStatusUTime: '140px',
                // devStatus: '100px',
                devStatusName: '140px',
                // computeStatus: '100px',
                stageId: '140px',
                sprintName: '160px',
                urgent: '120px',
                platform: '220px',
                proposerOrgName: '300px',
                accpetorOrg: '300px',
                lastComment: '170px',
                pmpProjectCode: '160px',
                pmpProjectName: '260px',
                projectTypeName: '130px',
                name: '320px',
                demanders: '120px',
                submitTime: '160px',
                spaceId: '120px',
                tags: '220px',
                status: '130px',
                processor: '120px',
                proposer: '120px',
                receivers: '120px',
                plannedHours: '140px',
                actualHours: '140px',
                oprations: '440px',
                planDate: '170px',
                seq: '130px',
                pmpProjectApprovalName: '140px',
                evaluation: '140px',
                evaluationUser: '120px',
                evaluationTime: '160px',
                revenueEndDate: '160px',
                channelRisk: '140px',
                processTime: '160px',
                cardCompleteTime: '160px',
                acceptTime: '160px',
                groupNames: '140px',
                priorityScore: '160px',
                sourceOrgName: '160px',
                revenueName: '200px',
                roi: '200px',
                evaluationRealEarnings: '200px',
                evaluationReason: '200px',
            },
            // 收益验证 显示字段与实体字段映射
            evaluation: {
                evaluation: 'result',
                evaluationUser: 'creatorName',
                evaluationTime: 'cTime',
                evaluationRealEarnings: 'realEarnings',
                evaluationReason: 'reason',
            },
            // 固定列
            columnFixed: {
                name: false,
                operation: true,
            },
            // 滑动位置判断
            fixStyleFlag: {
                name: false,
                operation: true,
            },
        };
    },
    created() {
        DemandModel.getInfos(['priorityList', 'typeList', 'sourceList', 'revenuesList'])
            .then(([priorityList, typeList, sourceList, revenuesList]) => {
                priorityList.forEach((item) => {
                    this.priorityMap[item.id] = item.name;
                });
                typeList.forEach((item) => {
                    this.typeList[item.code] = item.name;
                });
                /*eslint-disable*/
                sourceList.forEach((item) => {
                    this.sourceList[item.code] = item.name;
                });
                flattenDeep(revenuesList.map(o => o.children)).filter(o => !!o).forEach((o) => {
                    this.revenuesMap[o.code] = `${o.group} - ${o.name}`;
                });
            });
        Card.getStatusList(1).then((data)=> {
            data.forEach(item=> this.cardStatusMap[item.id] = item.name);
            // Object.assign(this.cardStatusMap, data)
        });
        // 读取缓存表格设置
        this.$store.dispatch('demands_group_fetchData');
        // 表格加载【加入分组】按钮需要获取分组数据
        const localOptions = JSON.parse(localStorage.jacp_demand_tableOption || '{}');
        Object.assign(this.columnOptions, localOptions);
        this.changeColumnFix();
    },
    mounted() {
        if (this.sortable) {
            this.initSortablePlugin();
        }
        this.scrollJuge();
    },
    methods: {
        handleRowClick(rowData, ev) {
            // 不能加stop的字段需要增加一个class去stop一下
            if (!closest(ev.target, '.is-stopPropagation') && !this.batch) {
                this.handleLinkClick(rowData);
            }
            if (this.batch) {
                const index = this.batchList.indexOf(rowData.id);
                const trObj = closest(ev.target, 'tr');
                if (index > -1) {
                    this.batchList.splice(index, 1);
                    trObj.className = trObj.className.replace('checkedRow', '');
                } else {
                    this.batchList.push(rowData.id);
                    trObj.className = `${trObj.className} checkedRow`;
                }
            }
        },
        handleLinkClick(scope) {
            this.setActive(scope.id);
            this.$emit('on-demand-focus', scope);
        },
        initSortablePlugin() {
            if (!this.sortableIns) {
                const dragHandlerIns = new DragHandler(this);
                // 落点的样式，拖动父的时候，连着子一起拖动
                this.sortableIns = new Sortable(this.$el.querySelector('.j-table__body'), {
                    ghostClass: 'j-table__item__ghost',
                    filter: '.j-table__item--undragable',
                    animation: 150,
                    onStart: dragHandlerIns.onStart.bind(dragHandlerIns),
                    onMove: dragHandlerIns.onMove.bind(dragHandlerIns),
                    onEnd: dragHandlerIns.onEnd.bind(dragHandlerIns),
                    setData(dataTransfer) { // 修正火狐下自动打开标签页搜索的问题
                        dataTransfer.setData('Text', '');
                    },
                });
            }
            return this.sortableIns;
        },
        cardDetail(demand) {
            DemandModel.getCardSprintId(demand.id).then(({cardId, sprintId}) => {
                const cardDetail = {
                    name: 'teamspaceCardDetail',
                    params: {
                        spaceKey: demand.space.key,
                    },
                    query: {
                        cardId,
                        sprintId,
                    }
                };
                const generalCardDetail = {
                    name: 'generalCardDetail',
                    params: {
                        cardId,
                        spaceKey: demand.space.key,
                    },
                };
                this.$_routerOpen(demand.space.mode === 1 ? generalCardDetail : cardDetail);
            });
        },
        isHandle(erp) {
            return equalIgnoreCase(erp, this.$store.state.user.erp);
        },
        handleAction(cmd, demand) {
            if (demand.$_onAction) {
                return;
            }
            /* eslint-disable no-param-reassign */
            demand.$_onAction = true;
            DemandModel.cmd(cmd, demand).then(() => {
                this.$emit('after-action', cmd, demand, this.data);
                // 在需求分组里删除子需求的话需要刷新列表
                if (cmd === 'revoke' || cmd === 'cancel' || cmd === 'resolveReceive'
                    || cmd === 'requestAcceptance' || cmd === 'reRequestReceive' || cmd === 'logicDelete'
                    || cmd === 'distribute' || cmd === 'reDistribute') {
                    this.$emit('refresh-list');
                    if (cmd === 'logicDelete') {
                      this.$router.replace({
                          name: 'demandsIndex',
                      });
                        // this.$emit('hide-detail');
                    }
                }
            }).then(() => {
                const content = {
                    remark: '添加备注成功',
                    revoke: '撤回成功',
                }[cmd];
                if (content) {
                    Dialog.alert({
                        title: '操作提示',
                        content,
                        type: 'success',
                        timeout: 2000,
                    });
                }
                demand.$_onAction = false;
            }).catch(() => {
                demand.$_onAction = false;
            });
        },
        getContinueTime(time) {
            return time ? `${Math.max(Math.ceil((new Date() - time) / (24 * 3600000)), 0)}天` : '--';
        },
        elips(content, maxLength = 30) {
            const cutedContent = content.slice(0, maxLength);
            return content.length > maxLength ? `${cutedContent}...` : cutedContent;
        },
        // 根据表头排序
        handleHeaderSort(row, order) {
            this.$emit('sort-change', {
                orderField: row.name,
                orderType: order,
            });
        },
        setExpandAll(val) {
            this.expandAll = val;
        },
        disabledAction({ approved, status, proposerErp, estimatedDaysApprovalResult }) {
            let action = [];
            if (approved !== 1) {
                action.push('communicate');
            }
            if (estimatedDaysApprovalResult !== 0 && estimatedDaysApprovalResult !== 3) {
                action.push('distribute');
            }
            if (status === 1 && !isCurrentUser({ erp: proposerErp })) {
                action.push('edit', 'logicDelete');
            }
            return action;
        },
        copyDemand(demandInfo, type) {
            let router = {};
            if (type === 'copy') {
                router = {
                    name: 'demandAdd',
                    query: {
                        id: demandInfo.id,
                        action: type,
                    },
                };
            } else {
                router = {
                    name: 'demandSplit',
                    query: {
                        id: demandInfo.id,
                        action: type,
                    },
                    params: {
                        id: demandInfo.parentDemandId,
                    },
                };
            }
            this.$router.push(router);
        },
        // 带关系复制 子节点携权限校验 相当于拆分父节点
        checkSplitAuthority(demandInfo) {
            if (demandInfo.parentDemandId !== -1) {
                const parentDemand = this.data.find(demand => demand.id === demandInfo.parentDemandId);
                if (!parentDemand) {
                    return false;
                }
                const isHandler = isCurrentUser(parentDemand.processor);
                const isProposer = isCurrentUser(parentDemand.proposer);
                const actions = (demandOperations[isHandler ? 1 : 0][parentDemand.status] || []);
                const proposerActions = isProposer ? (demandOperations[2][parentDemand.status] || []) : [];
                return proposerActions.concat(actions).includes('split');
            }
            return false;
        },
        resizeColumn() {
            const newOptions = JSON.stringify(this.columnOptions || {});
            localStorage.jacp_demand_tableOption = newOptions;
        },
        channelRiskColor(cr) {
            return cr === 1 ? 'red' : 'green';
        },
        //获取表格固定列的设置
        changeColumnFix() {
            if(localStorage.jacp_demand_columnFixed) {
                const localColumnFixed = JSON.parse(localStorage.jacp_demand_columnFixed);
                Object.assign(this.columnFixed, localColumnFixed);
            }

        },
        //滚动判断
        scrollJuge() {
            const vm = this;
            const el = document.querySelector('.demands__list');
            el.addEventListener('scroll', function() {
                const sl = parseInt(el.scrollLeft);
                const cw = parseInt(el.clientWidth);
                const sw = parseInt(el.scrollWidth);
                if(el.scrollLeft > 0) {
                    vm.fixStyleFlag.name = true;
                }else{
                    vm.fixStyleFlag.name = false;
                }
                if((sl + cw + 2) < sw){
                    vm.fixStyleFlag.operation = true;
                }else{
                    vm.fixStyleFlag.operation = false;
                }
            }, false);
        }
    },
    computed: {
        currentGroupId() {
            // temporary.使用了el-container外套。
            return this.$parent?.$parent.$parent.$parent.currentGroupId;
        },
        filters() {
            const filterDemandStatus = [2, 3, 4, 5, 9, 10, 20];
            return filterDemandStatus.map(item => ({
                text: this.$t(`jacp.demandStatus.${item}`),
                value: item,
            }));
        },
        currentTaskDetailTitle() {
            return '成本分摊详情';
        },
    },
    filters: {
        evaluationHZ(val, fileds) {
            if (fileds === 'result') {
                return ['未达成预期', '达成预期', '高于预期'][val-1];
            }
            if (fileds === 'cTime') {
                return moment(val).format('YYYY-MM-DD');
            }
            return val.replace(/\n{1,}/g, ';');
        },
        channelRiskHZ(val) {
            return ['否', '是'][val];
        },
        contactOrgName(val) {
            return val.filter(p => p.type === 3).map(p => p.contactOrgName)[0];
        },
    },
    watch: {
        sortable(n) {
            const ins = n ? this.initSortablePlugin() : this.sortableIns;
            if (!n && (!ins || ins.option('disabled'))) {
                return;
            }
            ins.option('disabled', !n);
        },
        orgColumns: {
            deep: true,
            immediate: true,
            handler(n) {
                this.columns = [].concat(n, this.extraColumns);
            },
        },
        expandAll(value) {
            this.$emit('toggle-expand-all', value);
        },
    },
};
</script>

<style lang='less'>
.demand-list{
    overflow: auto;
    //overflow-x: auto;
    //overflow-y: hidden;
    position: relative;
    //padding-top: 40px;
    &__nodata{
        width: 100%;
        line-height: 80px;
    }
    &__user{
        margin: 8px 0;
        color: #666;
        font-size: 12px;

        &__hasmultiusers:after{
            content: '...';
        }
        &__orgname{
            margin-top: 10px;
        }
    }
    &__flexbox{
        display: flex;
        align-items: center;
    }
    & .el-table td div.table-lightbox{
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 1;
    }
    & .el-table tr.checkedRow{
        background: #E9F4FD;
        & td{
            border-bottom: 1px solid #e1e1e1;
        }
    }
    & .batch-checked{
        background: url("~@/assets/icons/<EMAIL>") no-repeat;
        background-size: 100%;
        width: 16px;
        height: 16px;
        display: inline-block;
    }
    & .batch-unchecked{
        background: url("~@/assets/icons/check2x.png") no-repeat;
        background-size: 100%;
        width: 16px;
        height: 16px;
        display: inline-block;
    }
    &__copy{
        list-style: none;
        padding: 5px 0;
        & p{
            padding: 0 10px;
            margin: 2px 0;
            width: 110px;
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            color: rgb(81, 170, 244);
            &:hover{
               background-color: rgb(233, 244, 254);
               cursor: pointer;
            }
        }
    }
}

.demand-list__item{
    &:hover{
        background-color: rgb(238, 243, 246);
        background-clip: padding-box;
    }

}

.el-table tr.demand-list__row__child{
    background-color: #f9f9f9;
}

.demand-list{
    &__item__{
        &name{
            max-width: 35vw;
        }
        &other{
          position: absolute;
          right: 18px;
          top: 50%;
          transform: translateY(-8px);
        }
    }
    &__row--space{
        color: #08a4da;
        font-size: 12px;
        cursor: pointer;
    }
    &__header__{
        &name{
            min-width: 20vw;
        }
        &time, &priority, &demanders{
            min-width: 160px;
        }
        &processor{
            min-width: 120px;
        }
        &status, &space{
            min-width: 100px;
        }
    }
}

.jacp-tableHead-fix{
    thead tr{
        position: sticky;
        top: 0;
        z-index: 11;
    }
}

.jacp-table-fixed-left{
    td:first-child{
        position: sticky;
        left: 0;
        z-index: 10;
        box-shadow: 6px 0 6px 0 rgba(0, 0, 0, 0.05);
    }
    tbody tr{
        td:first-child{
            background-color: inherit;
        }
    }
}

.jacp-table-fixed-right{
    td:nth-last-child(2){
        position: sticky;
        right: 0;
        z-index: 10;
        box-shadow: -6px 0 6px 0 rgba(0, 0, 0, 0.05);
    }
    tbody tr{
        td:nth-last-child(2){
            background-color: inherit;
        }
    }
}

.j-table {
    &__body {
        &--sortable tr:not(.j-nodata) {
            background-image: none;
            td:first-child {
                background-image: url("~@/assets/icons/sortable_simple_32.svg");
                background-repeat: no-repeat;
                background-size: 24px 24px;
                background-position: -6px center;
            }
            &.j-table__item--undragable {
                td:first-child{
                    cursor: not-allowed;
                    background-image: none;
                }

            }
        }
    }
}
</style>
