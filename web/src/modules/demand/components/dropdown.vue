<template>
    <el-dropdown
        v-if="active && data.length"
        class="nav-dropdown"
        trigger="click"
        @command="select"
    >
        <span class="nav-dropdown__header">
            <span>{{ active.name }}</span>
            <jacp-icon :name="active.icon" />
            <jacp-icon
                name="el-icon-caret-bottom"
                :size="12"
            />
        </span>
        <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
                class="nav-dropdown__item"
                v-for="item in data"
                v-show="active !== item"
                :command="item"
                :key="item.id"
            >
                <span>{{ item.name }}</span>
                <jacp-icon :name="item.icon" />
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script type="text/javascript">
export default {
    props: {
        data: {
            type: Array,
            required: true,
        },
        value: {
            type: String,
        },
    },
    data() {
        let active = this.data.filter(item => item.id === this.value)[0] || this.data[0];
        if (!active) {
            active = this.data[0];
            this.$emit('input', active ? active.id : undefined);
        }
        return {
            active,
        };
    },
    watch: {
        value(n) {
            if (n && this.active && this.active.id === n) {
                return;
            }
            this.active = this.data.filter(item => item.id === n)[0];
        },
    },
    methods: {
        select(obj) {
            this.active = obj;
            this.$emit('input', obj ? obj.id : undefined);
            this.$emit('change', obj);
        },
    },
};
</script>

<style lang="less">
.nav-dropdown__header{
    cursor: pointer;
}
</style>
