<template>
    <el-select
        class="demand-related-systems__select"
        ref="selectEl"
        v-bind="[$attrs]"
        :value="value"
        :popper-append-to-body="false"
        :placeholder="placeholder"
        @input="(val) => $emit('input', val)"
        @visible-change="(val) => optionsVisible = val"
        @change="handleChange"
    >
        <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        >
            <span class="latest-detail-system__option">{{ item.name }}</span>
            <span class="latest-detail-system__option-type">
                {{ item.source === 1 ? 'J-ONE' : 'JDos' }}
            </span>
        </el-option>
    </el-select>
</template>
<script>
export default {
    name: 'DemandRelatedSystems',
    inheritAttrs: false,
    props: {
        value: { type: [Number, String, Array], required: true },
        options: { type: Array, default: () => [] },
        placeholder: { type: String, default: '请输入中文名称' },
    },
    data() {
        return {
            optionsVisible: false,
            changed: false,
        };
    },
    methods: {
        getSelectedData() {
            return this.options.filter(o => this.value.includes(o.id));
        },
        handleChange(val) {
            const changeHandler = [
                () => {
                    this.changed = true;
                },
            ];
            if (this.$listeners?.change) {
                changeHandler.push(this.$listeners?.change);
            }
            changeHandler.forEach(fun => fun.call(this, val));
        },
    },
    watch: {
        // 直接监听blur不准，改成模拟的监听菜单变化
        optionsVisible(val) {
            if (val) {
                this.changed = false;
            }
            // blur的时候简单判断一下是否value有change过
            this.$emit('blur');
        },
    },
};
</script>
<style lang="less">
.demand-related-systems__select{
    &.el-select{
      width: 100%;
    }
    .el-tag .el-select__tags-text{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
        display: inline-block;
    }
    .el-tag__close.el-icon-close {
        right: 5px;
        top: -6px;
    }
}
</style>
