<template>
    <div>
        <el-table
            class="apportion-detail__table j-table--simple"
            v-if="data"
            :data="data.records"
            style="width: 100%"
        >
            <el-table-column
                v-for="col in columns"
                :key="col.name"
                :prop="col.name"
                :label="col.label"
            >
                <template slot-scope="scope">
                    <div v-if="col.name === 'apportion'">
                        {{ scope.row[col.name] }}%
                    </div>
                    <div v-else-if="col.name === 'apportionOrgName'">
                        <el-tooltip
                            effect="dark"
                            :content="scope.row.apportionOrgName"
                            placement="top"
                        >
                            <span>{{ getOrgShortName(scope.row.apportionOrgName) }}</span>
                        </el-tooltip>
                    </div>
                    <div v-else-if="col.name === 'confirmors'">
                        <div
                            v-for="user in scope.row[col.name]"
                            :key="user.erp"
                        >
                            <jacp-erp
                                v-if="user.erp"
                                :disable-timline="$utils.isCurrentUser(user)"
                                :data="user"
                            />
                            <span v-else>--</span>
                        </div>
                    </div>
                    <div
                        v-else-if="col.name === 'approved'"
                    >
                        <i :class="[statusIcon[scope.row[col.name]], 'apportion-status__icon']" />
                    </div>
                    <div v-else>
                        {{ scope.row[col.name] }}
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
import { ApportionModel } from '@/models/apportion';

export default {
    name: 'ApportionDetail',
    props: {
        // title: String,
        data: Object,
    },
    data() {
        return {
            visible: false,
            columns: ApportionModel.detailColumns,
            statusIcon: ApportionModel.statusIcon,
        };
    },
    methods: {
        getOrgShortName(fullName) {
            const tempArr = fullName.split('-');
            if (!tempArr.length) {
                return fullName;
            }
            return tempArr.pop();
        },
    },
    watch: {
        data: {
            handler(val) {
                this.visible = Boolean(val);
            },
        },
    },
};
</script>
<style>
.apportion-detail__table.el-table th:nth-child(4),
.apportion-detail__table .el-table__row td:nth-child(4){
    text-align: right;
}
</style>
