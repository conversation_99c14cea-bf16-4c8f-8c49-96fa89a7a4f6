<template>
    <div class="input-demand-stakeholder">
        <el-table :data="rowsData">
            <el-table-column
                label=""
                width="40"
            >
                <template slot-scope="scope">
                    <div
                        v-if="!scope.row._isBlank"
                        class="input-demand-stakeholder__removeicon"
                        @click="remove(scope.row)"
                    >
                        <jacp-icon name="icon-minus" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="角色"
                width="200"
            >
                <template slot-scope="scope">
                    <el-select
                        filterable
                        allow-create
                        clearable
                        default-first-option
                        :class="{
                            'input-demand-stakeholder--error': scope.row.$_roleNameError
                        }"
                        placeholder="必填，例：关注人"
                        v-model="scope.row.roleName"
                        @keydown.native.enter.prevent
                        @change="add(scope.row)"
                    >
                        <el-option
                            v-for="item in suggestions"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column
                label="名称"
                width="160"
            >
                <template slot-scope="scope">
                    <jacp-input-users
                        v-model="scope.row.persons"
                        placeholder="单选"
                        :max-count="1"
                        @change="add(scope.row)"
                    />
                </template>
            </el-table-column>
            <el-table-column label="职责">
                <template slot-scope="scope">
                    <el-input
                        type="textarea"
                        placeholder="填写职责信息"
                        class="input-demand-stakeholder__duty"
                        autosize
                        v-model="scope.row.duty"
                        @keydown.native.enter.prevent
                        :class="{
                            'input-demand-stakeholder--error': scope.row.$_dutyError
                        }"
                        @change="validate(scope.row, 'duty')"
                        @focus="validate(scope.row, 'duty')"
                    />
                </template>
            </el-table-column>
        </el-table>
        <div
            class="el-form-item__error"
            v-show="lastErrorMessage"
        >
            {{ lastErrorMessage }}
        </div>
    </div>
</template>

<script type="text/javascript">
import { validate } from '@/plugins/utils';

export default {
    props: {
        value: {
            type: Array,
            default: () => ([]),
        },
        suggestions: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            lastErrorMessage: undefined,
            blankItem: this.newBlankItem(),
            rules: {
                roleName: [{
                    required: true,
                    message: '角色不能为空',
                }, {
                    max: 20,
                    message: '角色限20字',
                }],
                duty: [{
                    max: 100,
                    message: '限100字',
                }],
            },
        };
    },
    methods: {
        newBlankItem() {
            return {
                roleName: '',
                persons: undefined,
                duty: '',
                _isBlank: true,
            };
        },
        remove(item) {
            Object.assign(item, {
                $_personError: undefined,
                $_dutyError: undefined,
                $_roleNameError: undefined,
            });
            const i = this.value.indexOf(item);
            this.value.splice(i, 1);
        },
        add(item) { // 删除记录时这个触发得有点莫名其妙
            /* eslint-disable no-underscore-dangle */
            if (item._isBlank && !item.roleName) {
                return;
            }
            this.validate(item, 'roleName').then(() => {
                if (!item._isBlank || !item.roleName) {
                    return;
                }
                // 更新旧的干系人到后端
                // 创建新的空干系人
                this.value.push(Object.assign(item, {
                    _isBlank: undefined,
                }));
                this.blankItem = this.newBlankItem();
            });
        },
        validate(rowData, type) {
            return validate(rowData[type], this.rules[type]).then(() => {
                this.$set(rowData, `$_${type}Error`, undefined);
                this.lastErrorMessage = undefined;
            }).catch((errors) => {
                this.$set(rowData, `$_${type}Error`, errors[0]);
                this.lastErrorMessage = errors[0];
            });
        },
    },
    computed: {
        rowsData() {
            return this.value.concat(this.blankItem);
        },
        hasError() { // used by parent component
            let count = 0;
            this.rowsData.forEach((item) => {
                count += item.$_roleError ? 1 : 0;
                count += item.$_nameError ? 1 : 0;
                count += item.$_dutyError ? 1 : 0;
            });
            return count > 0;
        },
    },
};
</script>

<style lang="less">
.input-demand-stakeholder{
    &__removeicon{
        border-radius: 100%;
        background-color: red;
        color: #fff;
        width: 20px;
        height: 20px;
        text-align: center;
        cursor: pointer;
    }
    &--error input,
    &--error input:focus,
    &--error input:hover{
        border-color: red;
    }
    & &__date.el-date-editor.el-input{
        width: 120px;
    }
    & .el-form-item__error{
        position: static;
    }
    &__duty{
        margin: 5px 0;
    }
}
</style>
