<template>
    <jacp-form-item-wrapper
        class="jacp-form-demand-stakeholder"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form-demand-stakeholder__value"
        >
            <el-table :data="value">
                <el-table-column
                    label="角色"
                    prop="roleName"
                    width="160"
                />
                <el-table-column
                    label="姓名"
                    prop="person"
                    width="120"
                />
                <el-table-column
                    label="负责内容"
                    prop="duty"
                />
            </el-table>
        </div>
        <div slot="edit">
            <demand-stakeholder-edit
                :value="value"
                :suggestions="suggestions"
                ref="editIns"
            />
        </div>
    </jacp-form-item-wrapper>
</template>

<script type="text/javascript">
import demandStakeholderEdit from './edit';

export default {
    name: 'InputDemandStakeholder',
    props: {
        value: {
            type: Array,
            default: () => ([]),
        },
        disabled: {
            type: <PERSON>olean,
            default: false,
        },
        suggestions: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        demandStakeholderEdit,
    },
    data() {
        const isElFormItem = this.$parent.$options.componentName === 'ElFormItem';
        return {
            useElFormValidator: !this.rules && isElFormItem,
            errorMsg: undefined,
            editMode: false,
            locked: false,
        };
    },
    methods: {
        blur() {
            const formError = this.useElFormValidator
                && ['error', 'validating'].includes(this.$parent.validateState);

            // 使用自带校验
            if (!formError && !this.$refs.editIns.hasError) {
                this.editMode = false;
                this.$emit('change', this.value);
            }
        },
        focus() {
            if (this.locked) {
                this.locked = false;
                return;
            }
            this.editMode = true;
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.jacp-form-demand-stakeholder{
    &__error .el-input__inner,
    &__error .el-textarea__inner{
        border-color: @errorColor;
    }
}
</style>
