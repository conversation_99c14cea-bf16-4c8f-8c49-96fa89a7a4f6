<template>
    <div
        slot="reference"
        class="priority-mark__item priority-mark__item--add"
    >
        <div
            v-if="mark == -2 || (mark == -1 && textParam == 'edit')"
        >
            <span
                style="display: block;color: #c5c5c5;"
            >未评分</span>
            <span
                style="color: #2695F1;display: block;"
                @click="viewOrUpdate(textParam)"
                role="button"
            >+评分</span>
            <div
                v-if="receiverErp == '' || demandSourceOrgTierCode == ''||
                    demandSourceOrgTierCode == undefined || JSON.stringify(template) == '{}' || template == undefined"
            >
                <span style="color: #F55445">{{ noticeMessage }}</span>
            </div>
        </div>
        <div v-else-if="mark != -1">
            <div style="padding-left: 1px;">{{ mark }}</div>
            <el-button
                type="text"
                @click="viewOrUpdate(textParam)"
            >
                {{ spanText }}
            </el-button>
            <div
                v-if="receiverErp == '' || demandSourceOrgTierCode == ''||
                    demandSourceOrgTierCode == undefined || JSON.stringify(template) == '{}' || template == undefined"
            >
                <span style="color: #F55445">{{ noticeMessage }}</span>
            </div>
        </div>
        <span
            v-else
            style="color:#999;cursor:default;"
        >暂无</span>
        <div>
            <el-dialog
                title="需求优先级评分"
                custom-class="priority-mark__dialog"
                :visible.sync="dialogVisible"
                width="38%"
                append-to-body
            >
                <el-form
                    class="j-form"
                    :disabled="disabled"
                    :model="form"
                    ref="factorListForm"
                >
                    <template v-for="(factor) in factorList">
                        <el-form-item
                            class="line"
                            v-if="(factor.factorName.indexOf('roi') !== -1) || (factor.factorName.indexOf('ROI') !== -1)"
                            :key="factor.id"
                        >
                            <label slot="label">
                                {{ factor.factorName }}
                                <el-tooltip placement="top">
                                    <div slot="content">
                                        {{ factor.factorDefine }}
                                    </div>
                                    <jacp-icon
                                        style="margin-left: 4px;color: #c5c5c5;"
                                        name="icon-help_outline"
                                        :size="16"
                                    />
                                </el-tooltip>
                            </label>
                            <template v-if="roiRate !== '0' ">
                                <label>
                                    <span>{{ roiRate }}</span>
                                </label>
                            </template>
                            <template v-else>
                                <label>
                                    <span style="color: #01C0DD;">未选择需求来源部门或未匹配到部门roi达成率，此项默认按0分计算</span>
                                </label>
                            </template>
                        </el-form-item>
                        <el-form-item
                            v-if="(factor.factorName.indexOf('roi') == -1) && (factor.factorName.indexOf('ROI') == -1)"
                            :prop="'factorId_' + factor.id"
                            :rules="[{ required: !(factor.ifEdit == 0 && textParam == 'edit' && demand.status != 1),
                                       message: factor.factorName + '不能为空'}]"
                            :key="factor.id"
                        >
                            <label slot="label">
                                {{ factor.factorName }}
                                <el-tooltip placement="top">
                                    <div slot="content">
                                        {{ factor.factorDefine }}
                                    </div>
                                    <jacp-icon
                                        style="margin-left: 4px;color: #c5c5c5;"
                                        name="icon-help_outline"
                                        :size="16"
                                    />
                                </el-tooltip>
                            </label>
                            <el-select
                                @change="getItemSpecial($event, factor)"
                                :disabled="factor.ifEdit == 0 && textParam == 'edit'
                                    && demand.status != 1 && demand.status != 4 && demandId != undefined"
                                v-model="form['factorId_'+factor.id]"
                                class="line"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in factor.itemsList"
                                    :key="item.id"
                                    :label="item.itemName"
                                    :value="item.id"
                                />
                            </el-select>
                        </el-form-item>
                    </template>
                    <!-- 附件 -->
                    <el-form-item
                        prop="attachments"
                        :rules="attachmentsRules"
                    >
                        <label slot="label">
                            需求来源附件
                            <el-tooltip placement="top">
                                <div slot="content">
                                    文件名只包含字母、数字、中文、“/”、“.”、“-”、“ ”
                                </div>
                                <jacp-icon
                                    style="margin-left: 4px;color: #c5c5c5;"
                                    name="icon-help_outline"
                                    :size="16"
                                />
                            </el-tooltip>
                        </label>
                        <jacp-form-upload
                            :disabled="textParam == 'view'"
                            v-model="attachments"
                            :can-reload="false"
                            ref="uploadAttachments"
                            toolbar-placement="right-start"
                            :toolbar-offset="-7"
                        />
                    </el-form-item>
                    <el-form-item style="text-align: center;">
                        <el-button
                            type="primary"
                            @click="doMark('factorListForm')"
                        >
                            提交
                        </el-button>
                        <el-button @click="dialogVisible = false">
                            取消
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>
        </div>
    </div>
</template>

<script type="text/javascript">
import mixinFieldText from './mixin.fieldText';
import mixinsDemandDetails from './mixin.demandDetails';
import DemandPriorityMark from '@/models/demandPriorityMark';

export default {
    name: 'DemandPriorityMark',
    mixins: [mixinFieldText, mixinsDemandDetails],
    props: {
        demand: Object,
        demandAdd: Object,
    },
    data() {
        const validateAttachments = (rule, value, callback) => {
            if (!this.attachments.length) {
                callback(new Error('请上传附件'));
            }
            callback();
        };
        return {
            mark: -2,
            dialogVisible: false,
            disabled: false,
            factorList: [],
            form: {},
            itemsList: [],
            template: {},
            attachments: [],
            rules: {
                attachmentsRequired: [{ required: true, trigger: 'change', validator: validateAttachments }],
                attachmentsNoRequired: [],
            },
            attachmentsRules: [{ required: true, trigger: 'change', validator: validateAttachments }],
            receiverErp: '',
            // currentUserErp: '',
            receiverOrgTierCode: '', // 受理人部门编号全称
            demandSourceOrgTierCode: '', // 需求来源部门编号全称
            spanText: '新增',
            textParam: 'add',
            noticeMessage: '',
            itemSpecial: 0,
            demandId: '',
            roiRate: '0',
        };
    },
    // mounted() {
    //     this.currentUserErp = this.$store.state.user.erp; // 当前登录用户erp
    // },
    methods: {
        getItemSpecial(event, factor) {
            if (factor.factorSpecial === 1) {
                const factorItem = factor.itemsList.find(item => item.id === event);
                this.itemSpecial = factorItem.itemSpecial;
            }
        },
        calculateScore() {
            DemandPriorityMark.viewOrUpdate(this.demand.id, this.demandSourceOrgTierCode).then((data) => {
                if (data) {
                    const tempData = data;
                    delete tempData.attachment; // 删除返回对象的附件数据
                    const ftList = [];
                    // 获取上传文件信息
                    this.factorList.forEach((factor) => {
                        const tempFactor = factor;
                        if (tempFactor.factorSpecial === 2) {
                            ftList.push(tempFactor);
                        }
                        Object.keys(tempData).forEach((key) => {
                            if (tempFactor.id === parseInt(key.split('_')[1], 10)) {
                                tempFactor.factorItemId = tempData[key];
                                tempFactor.itemsList.forEach((item) => {
                                    if (item.id === tempData[key]) {
                                        tempFactor.factorItemName = item.itemName;
                                    }
                                });
                                ftList.push(tempFactor);
                            }
                        });
                    });
                    const factorVoObj = {};
                    factorVoObj.factorList = ftList;
                    factorVoObj.sourceDepartmentCode = this.demandSourceOrgTierCode;
                    DemandPriorityMark.doMark(factorVoObj).then((res) => {
                        if (res) {
                            this.mark = res;
                            const obj = {};
                            obj.mark = res;
                            this.$emit('doMark', obj);
                        } else {
                            this.$message.error('评分失败');
                        }
                    });
                } else {
                    this.$message.error('获取评分数据失败');
                }
            });
        },
        viewOrUpdate(param) { // 点编辑查看评分
            this.addOrUpdateForGradePage();
            if (param === 'edit') {
                const flag = this.validateParams();
                if (flag === true) {
                    this.getRankByDemandId();
                }
            } else if (param === 'view') {
                this.dialogVisible = true;
                this.getRankByDemandId();
            } else {
                this.validateParams();
            }
        },

        doMark(formName) { // 评分弹窗提交功能
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const ftList = [];
                    const formObj = { ...this.form };
                    // 获取上传文件信息
                    this.factorList.forEach((factor) => {
                        const tempFactor = factor;
                        if (tempFactor.factorSpecial === 2) {
                            ftList.push(tempFactor);
                        }
                        Object.keys(formObj).forEach((key) => {
                            if (tempFactor.id === parseInt(key.split('_')[1], 10)) {
                                tempFactor.factorItemId = formObj[key];
                                tempFactor.itemsList.forEach((item) => {
                                    if (item.id === formObj[key]) {
                                        tempFactor.factorItemName = item.itemName;
                                    }
                                });
                                ftList.push(tempFactor);
                            }
                        });
                    });
                    const factorObj = {};
                    factorObj.factorList = ftList;
                    factorObj.sourceDepartmentCode = this.demandSourceOrgTierCode;
                    const attachmentArr = [];
                    if (this.textParam === 'add') {
                        factorObj.demandRootId = null;
                    } else {
                        factorObj.demandRootId = this.demand.id;
                    }
                    factorObj.template = this.template;
                    this.attachments.forEach((attachment) => {
                        const attachmentObj = {};
                        const { uid, name, url } = attachment;
                        Object.assign(attachmentObj, { uid, name, url });
                        attachmentArr.push(attachmentObj);
                    });
                    factorObj.rankAttachment = attachmentArr;
                    DemandPriorityMark.doMark(factorObj).then((data) => {
                        if (data || data === 0) {
                            this.mark = data;
                            factorObj.mark = this.mark;
                            this.dialogVisible = false;
                            this.$emit('doMark', factorObj);
                        } else {
                            this.dialogVisible = false;
                            this.$message.error('评分失败');
                        }
                    });
                }
            });
        },
        addOrUpdateForGradePage() { // 评分弹窗页面数据初始化
            DemandPriorityMark.addOrUpdateForGradePage(this.receiverErp,
                this.receiverOrgTierCode, this.demandSourceOrgTierCode).then((data) => {
                if (data) {
                    this.factorList = Object.assign([], data.factorList);
                    this.template = Object.assign({}, data.template);
                    this.roiRate = data.roiRate;
                    // this.itemSpecial = data.itemSpecial;
                    if (this.textParam === 'add') { // 新增时设置默认选中项
                        this.form = Object.assign({}, data.defaultSelectMap); // 设置默认选中项
                        this.itemSpecial = data.itemSpecial;
                    }
                }
            });
        },
        getRankByDemandId(param) {
            if (this.demandId !== undefined) {
                DemandPriorityMark.viewOrUpdate(this.demandId, this.demandSourceOrgTierCode, param).then((data) => {
                    if (data) {
                        const tempData = data;
                        this.attachments = tempData.attachment; // 获取附件数据
                        delete tempData.attachment; // 删除返回对象的附件数据
                        if (this.textParam === 'view') { // 查看时，赋值rank表中的指标项数据供页面回显
                            this.factorList = tempData.factorList;
                            delete tempData.factorList; // 删除
                        }
                        this.itemSpecial = tempData.itemSpecial;
                        if (this.itemSpecial === 5) {
                            this.attachmentsRules = this.rules.attachmentsNoRequired;
                        } else {
                            this.attachmentsRules = this.rules.attachmentsRequired;
                        }
                        delete tempData.itemSpecial;
                        this.roiRate = tempData.roiRate;
                        delete tempData.roiRate;
                        // this.form = Object.assign({}, tempData);
                        this.form = tempData; // 赋值获取到的下拉框回显数据
                    } else {
                        this.$message.error('获取数据失败');
                    }
                });
            }
        },
        validateParams() {
            if (JSON.stringify(this.template) === '{}' || this.template === undefined || this.template === null) {
                this.noticeMessage = '您还没有模板';
                return false;
            }
            this.dialogVisible = true;
            return true;
        },
    },
    watch: {
        demand: { // 编辑需求
            handler() {
                if (this.demand !== undefined) {
                    if (this.demand.parentDemandId === -1) {
                        this.demandId = this.demand.id;
                    } else {
                        this.demandId = this.demand.rootDemandId;
                    }
                    this.mark = this.demand.priorityScore;
                    if (this.demand.receivers.length > 0) {
                        this.receiverErp = this.demand.receivers[0].erp; // 需求受理人
                        this.receiverOrgTierCode = this.demand.receivers[0].orgTierCode; // 受理人部门编号全称
                    }
                    if (this.demand.demandSourceOrg !== undefined) {
                        this.demandSourceOrgTierCode = this.demand.demandSourceOrg.orgTierCode; // 需求来源部门编号
                    } else {
                        this.demandSourceOrgTierCode = '';
                    }
                    if (((this.demand.status === 2 || this.demand.status === 3)
                        && (this.$store.state.user.erp === this.receiverErp)
                        && this.demand.parentDemandId === -1)
                        || (this.demand.status === 1 && this.demand.parentDemandId === -1)
                        || (this.demand.status === 4 && this.$store.state.user.erp === this.demand.proposer.erp)) {
                        this.disabled = false;
                        this.spanText = '编辑';
                        this.textParam = 'edit';
                    } else if (this.demand.status === undefined) {
                        this.mark = -2;
                    } else if ((this.demand.status === 2 || this.demand.status === 3 || this.demand.status === 4)
                        && this.demand.priorityScore === -1) {
                        this.disabled = false;
                        this.spanText = '新增';
                        this.textParam = 'add';
                        this.mark = -2;
                    } else {
                        this.disabled = true;
                        this.spanText = '查看';
                        this.textParam = 'view';
                    }
                }
            },
            deep: true,
            immediate: true,
        },
        demandAdd: { // 新增需求
            handler() {
                if (this.demandAdd !== undefined) {
                    if (this.demandAdd.receivers.length > 0 && this.demandAdd.receivers !== undefined) {
                        this.receiverErp = this.demandAdd.receivers[0].erp; // 需求受理人
                        this.receiverOrgTierCode = this.demandAdd.receivers[0].orgTierCode; // 受理人部门编号全称
                    }
                    if (this.demandAdd.demandSourceOrg !== undefined) {
                        this.demandSourceOrgTierCode = this.demandAdd.demandSourceOrg.orgTierCode; // 需求来源部门编号
                    } else {
                        this.demandSourceOrgTierCode = '';
                    }
                    if (this.demandAdd.status === undefined && this.demandAdd.priorityScore === -1) {
                        this.mark = -2;
                    } else {
                        this.mark = this.demandAdd.priorityScore; // 评分
                    }
                    if (this.receiverErp !== '') {
                        this.noticeMessage = '';
                    }
                    if (this.demandSourceOrgTierCode !== '' || this.demandSourceOrgTierCode !== undefined) {
                        this.noticeMessage = '';
                    }
                    if (JSON.stringify(this.template) !== '{}' || this.template !== undefined) {
                        this.noticeMessage = '';
                    }
                    if (this.demandAdd.priorityScore !== -1) {
                        this.textParam = 'edit';
                        this.spanText = '编辑';
                        this.disabled = false;
                    }
                }
            },
            deep: true,
            immediate: true,
        },
        itemSpecial: {
            handler() {
                if (this.itemSpecial === 5) {
                    this.attachmentsRules = this.rules.attachmentsNoRequired;
                } else {
                    this.attachmentsRules = this.rules.attachmentsRequired;
                }
            },
        },
        receiverErp: {
            handler() {
                this.template = {};
                this.addOrUpdateForGradePage();
            },
            immediate: true,
        },
        form: {
            handler() {

            },
        },
    },
};
</script>

<style lang="less" scoped>
    @import '~@/theme/var';
    .priority-mark{
        &__item{
            font-size: 12px;
            color: @fontColor;
            line-height: 18px;
            &--add{
                flex-direction: column;
                align-content: center;
                align-items: center;
                text-align: left;
                cursor: pointer;
                i{
                    font-size: 20px;
                    display: inline-block;
                    width: 32px;
                    height: 32px;
                    line-height: 3px;
                    border-radius: 50%;
                }
                span{
                    margin-top: 5px;
                    font-size: 12px;
                }
            }
        }
    }
    .priority-mark__dialog{
        .el-select-dropdown__item{
            max-width: auto;
        }
        .el-form-item--small .el-form-item__label{
            line-height: 3px !important;
        }
    }
</style>
