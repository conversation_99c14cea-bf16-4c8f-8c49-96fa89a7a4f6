<template>
    <jacp-form-item-wrapper
        class="jacp-form-demand-milestone"
        :edit-mode="editMode"
        :disabled="disabled"
        @focus="focus()"
        @blur="blur($event)"
    >
        <div
            slot="view"
            class="jacp-form-demand-milestone__value"
        >
            <el-table :data="value">
                <el-table-column
                    label="名称"
                    prop="name"
                    width="160"
                />
                <el-table-column
                    label="日期"
                    prop="content"
                    width="120"
                >
                    <template slot-scope="scope">
                        {{ scope.row.finishedDate|jacp-local-time("YYYY-MM-DD") }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="备注"
                    prop="remark"
                />
            </el-table>
        </div>
        <div slot="edit">
            <demand-milestone-edit
                :value="value"
                @input="input"
                :suggestions="suggestions"
                ref="editIns"
            />
        </div>
    </jacp-form-item-wrapper>
</template>

<script type="text/javascript">
import demandMilestoneEdit from './edit';

export default {
    name: 'InputDemandMilestone',
    props: {
        value: {
            type: Array,
            default: () => ([]),
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        suggestions: {
            type: Array,
            default: () => ([]),
        },
    },
    components: {
        demandMilestoneEdit,
    },
    data() {
        const isElFormItem = this.$parent.$options.componentName === 'ElFormItem';
        return {
            useElFormValidator: !this.rules && isElFormItem,
            errorMsg: undefined,
            editMode: false,
            locked: false,
            lockWatch: false,
            originValue: JSON.stringify(this.value),
        };
    },
    methods: {
        updateOriginValue() {
            this.originValue = JSON.stringify(this.value || []);
        },
        blur() {
            const formError = this.useElFormValidator
                && ['error', 'validating'].includes(this.$parent.validateState);

            // 使用自带校验
            if (!formError && !this.$refs.editIns.hasError) {
                this.editMode = false;

                if (JSON.stringify(this.value || []) !== this.originValue) {
                    this.$emit('change', this.value);
                    this.updateOriginValue();
                }
            }
        },
        input(value) {
            this.$emit('input', value);
            this.lockWatch = true;
        },
        focus() {
            if (this.locked) {
                this.locked = false;
                return;
            }
            this.editMode = true;
        },
    },
    watch: {
        value() {
            if (!this.lockWatch) { // change by parent component
                this.updateOriginValue();
            } else { // change by input event
                this.lockWatch = false;
            }
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.jacp-form-demand-milestone{
    &__error .el-input__inner,
    &__error .el-textarea__inner{
        border-color: @errorColor;
    }
}
</style>
