<template>
    <div class="input-demand-milestone">
        <el-table :data="rowsData">
            <el-table-column
                label=""
                width="40"
            >
                <template slot-scope="scope">
                    <div
                        v-if="!scope.row._isBlank"
                        class="input-demand-milestone__removeicon"
                        @click="remove(scope.row)"
                    >
                        <jacp-icon name="icon-minus" />
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="名称"
                width="200"
            >
                <template slot-scope="scope">
                    <el-select
                        filterable
                        allow-create
                        clearable
                        default-first-option
                        :class="{
                            'input-demand-milestone--error': scope.row.$_nameError
                        }"
                        placeholder="必填，例：BRP评审"
                        v-model="scope.row.name"
                        @keydown.native.enter.prevent
                        @change="add(scope.row)"
                    >
                        <el-option
                            v-for="item in suggestions"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column
                label="日期"
                width="160"
            >
                <template slot-scope="scope">
                    <el-date-picker
                        type="date"
                        placeholder="必填"
                        class="input-demand-milestone__date"
                        :class="{
                            'input-demand-milestone--error': scope.row.$_finishedDateError
                        }"
                        :clearable="false"
                        v-model="scope.row.finishedDate"
                        @change="validate(scope.row, 'finishedDate')"
                    />
                </template>
            </el-table-column>
            <el-table-column label="备注">
                <template slot-scope="scope">
                    <el-input
                        type="textarea"
                        placeholder="填写备注信息"
                        class="input-demand-milestone__remark"
                        autosize
                        v-model="scope.row.remark"
                        @keydown.native.enter.prevent
                        :class="{
                            'input-demand-milestone--error': scope.row.$_remarkError
                        }"
                        @change="validate(scope.row, 'remark')"
                        @focus="validate(scope.row, 'remark')"
                    />
                </template>
            </el-table-column>
        </el-table>
        <div
            class="el-form-item__error"
            v-show="lastErrorMessage"
        >
            {{ lastErrorMessage }}
        </div>
    </div>
</template>

<script type="text/javascript">
import { validate } from '@/plugins/utils';

export default {
    props: {
        value: {
            type: Array,
            default: () => ([]),
        },
        suggestions: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {
            lastErrorMessage: undefined,
            blankItem: this.newBlankItem(),
            rules: {
                name: [{
                    required: true,
                    message: '名称不能为空',
                }, {
                    max: 10,
                    message: '名称限10字',
                }],
                finishedDate: [{
                    required: true,
                    message: '请设置日期',
                }],
                remark: [{
                    max: 200,
                    message: '限200字',
                }],
            },
        };
    },
    methods: {
        newBlankItem() {
            return {
                name: '',
                finishedDate: new Date(),
                remark: '',
                _isBlank: true,
            };
        },
        remove(item) {
            Object.assign(item, {
                $_nameError: undefined,
                $_finishedDateError: undefined,
                $_remarkError: undefined,
            });
            const i = this.value.indexOf(item);
            this.value.splice(i, 1);
            this.$emit('input', this.value);
        },
        add(item) { // 删除记录时这个触发得有点莫名其妙
            /* eslint-disable no-underscore-dangle */
            if (item._isBlank && !item.name) {
                return;
            }
            this.validate(item, 'name').then(() => {
                if (!item._isBlank || !item.finishedDate || !item.name) {
                    return;
                }
                this.value.push(Object.assign(item, {
                    _isBlank: undefined,
                }));
                this.$emit('input', this.value);
                this.blankItem = this.newBlankItem();
            });
        },
        validate(rowData, type) {
            return validate(rowData[type], this.rules[type]).then(() => {
                this.$set(rowData, `$_${type}Error`, undefined);
                this.lastErrorMessage = undefined;
            }).catch((errors) => {
                this.$set(rowData, `$_${type}Error`, errors[0]);
                this.lastErrorMessage = errors[0];
            });
        },
    },
    computed: {
        rowsData() {
            return this.value.concat(this.blankItem);
        },
        hasError() { // used by parent component
            let count = 0;
            this.rowsData.forEach((item) => {
                count += item.$_nameError ? 1 : 0;
                count += item.$_finishedDateError ? 1 : 0;
                count += item.$_remarkError ? 1 : 0;
            });
            return count > 0;
        },
    },
};
</script>

<style lang="less">
.input-demand-milestone{
    &__removeicon{
        border-radius: 100%;
        background-color: red;
        color: #fff;
        width: 20px;
        height: 20px;
        text-align: center;
        cursor: pointer;
    }
    &--error input,
    &--error input:focus,
    &--error input:hover{
        border-color: red;
    }
    & &__date.el-date-editor.el-input{
        width: 120px;
    }
    & .el-form-item__error{
        position: static;
    }
    &__remark{
        margin: 5px 0;
    }
}
</style>
