<template>
    <div class="demand-group-aside-root">
        <el-menu
            @select="handleGroupItemSelectMy"
            :default-active="`${currentGroupId}`"
        >
            <el-submenu
                class="demand-group-aside-submenu"
                index="myList"
            >
                <template slot="title">
                    <div class="group-cate-title">
                        <span>{{ groupCateLabelMap.my.label }}</span>
                        <i
                            @click.stop="handleAdd"
                            class="el-icon-plus add-group-item"
                        />
                    </div>
                </template>
                <el-menu-item
                    class="demand-group-aside-menuitem"
                    v-for="(item, index) in mylist"
                    :key="index"
                    :index="`${item.groupId}`"
                >
                    <div class="demand-group-asideItem">
                        <span>
                            <ellipsis-text
                                :max-width="120"
                                :content="item.groupName"
                            />
                            <span
                                class="demand-group__text--emphasize"
                                v-if="isCooGroup(item) || hasCooGroup(item)"
                            >协</span>
                        </span>
                        <el-dropdown
                            trigger="click"
                            :hide-on-click="false"
                            @command="handleGroupOpMy"
                            placement="bottom-start"
                        >
                            <span
                                @click.stop
                            >
                                <jacp-icon
                                    class="demand-group-asideItem-icon"
                                    name="jacp-icon-more"
                                    active
                                />
                            </span>
                            <el-dropdown-menu
                                slot="dropdown"
                            >
                                <el-dropdown-item
                                    v-show="item.category === CODE.MY_COO"
                                    :command="getIns('doShowCooInfo', item)"
                                >
                                    协同分组信息
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-show="item.category === CODE.MY_DEFAULT"
                                    :command="getIns('doEditCooperator', item)"
                                >
                                    {{ item.cooperatorCount > 0 ? '编辑协同分组' : '开启协同分组' }}
                                </el-dropdown-item>
                                <el-dropdown-item
                                    :command="getIns('doShare', item)"
                                >
                                    分享
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-show="item.category === CODE.MY_DEFAULT"
                                    :command="getIns('doEditName', item)"
                                >
                                    重命名
                                </el-dropdown-item>
                                <el-dropdown-item
                                    v-show="item.category === CODE.MY_DEFAULT"
                                    :command="getIns('doDelete', item)"
                                >
                                    删除
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </el-menu-item>
            </el-submenu>
            <el-submenu
                class="demand-group-aside-submenu"
                index="followedList"
            >
                <template slot="title">
                    <div class="group-cate-title">
                        <span>{{ groupCateLabelMap.followed.label }}</span>
                    </div>
                </template>
                <el-menu-item
                    class="demand-group-aside-menuitem"
                    v-for="(item, index) in followedlist"
                    :key="index"
                    :index="`${item.groupId}`"
                >
                    <div class="demand-group-asideItem">
                        <span>
                            <ellipsis-text
                                :max-width="120"
                                :content="item.groupName"
                            />
                            <span
                                class="demand-group__text--emphasize"
                                v-if="isCooGroup(item) || hasCooGroup(item)"
                            >协</span>
                        </span>
                        <el-dropdown
                            trigger="click"
                            placement="bottom-start"
                            @command="handleGroupOpFollowed"
                        >
                            <span
                                @click.stop
                            >
                                <jacp-icon
                                    class="demand-group-asideItem-icon"
                                    name="jacp-icon-more"
                                    active
                                />
                            </span>
                            <el-dropdown-menu
                                slot="dropdown"
                            >
                                <el-dropdown-item
                                    :command="getIns('unsubscribe', item)"
                                >
                                    取消关注
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </el-menu-item>
            </el-submenu>
        </el-menu>
        <!-- 成员管理 -->
        <el-dialog
            class="group-member-manage"
            width="840px"
            :title="dialogTitle.editCooperator"
            :visible.sync="dialogMembersMgrVisible"
        >
            <jacp-members-mgr
                ref="memberMgrEl"
                :except="[$store.state.user.erp]"
                :members="cooperators"
                @change="cooperatorsTemp = $event"
            >
                <span slot="header">分组名：{{ opGroup.groupName }}</span>
            </jacp-members-mgr>
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="dialogMembersMgrVisible = false">取消</el-button>
                <el-button
                    type="primary"
                    @click="saveGroupCooperators"
                >保存</el-button>
            </span>
        </el-dialog>
        <demand-group-share
            ref="shareEl"
            :title="dialogTitle.share"
        />
        <demand-group-show-cooinfo
            ref="showCooInfoEl"
            :title="dialogTitle.showCooInfo"
        />
    </div>
</template>

<script>
import trim from 'lodash/trim';
import { DemandGroupModel, groupCateLabelMap, DemandGroupType as CODE } from '@/models/demandGroup';
import { sortMyGroupList, isCooGroup } from './demandGroups/utils.demandGroup';
// import logicDeleteVue from '../../../models/dialogs/logicDelete.vue';
import DemandGroupShare from './demandGroups/demandGroupShare';
import DemandGroupShowCooinfo from './demandGroups/demandGroupShowCooinfo';

export default {
    name: 'JacpDemandGroupAside',
    props: {
        dialogConfirmButtonText: String,
    },
    components: {
        DemandGroupShare,
        DemandGroupShowCooinfo,
    },
    data() {
        return {
            currentGroupId: this.$route.query.groupId,
            currentGroup: {},
            groupCateLabelMap,
            queryParamsTagsList: [],
            groupInfo: {
                isOwner: false,
                isSortable: false,
                owner: {},
            },
            dialogTitle: this.$t('jacp.demandGroup.dialogTitle'),
            dialogMembersMgrVisible: false,
            cooperators: [],
            cooperatorsTemp: [],
            groupCondition: {
                groupId: '',
                isSortable: false,
            },
            opGroup: {},
            CODE,
        };
    },
    methods: {
        handleAdd() {
            this.dialogEditorPrompt({
                type: 'add',
            }).then(this.doSave);
        },
        // 若返回一个字符串, 则返回结果会被赋值给 inputErrorMessage
        validateName(groupId, val) {
            const my = this.data.filter(item => +item.category === 0);
            const pureValue = trim(val);
            const checkRepeated = (value) => {
                const r = my.filter(o => o.groupName === value && groupId !== o.groupId);
                return r.length >= 1;
            };
            const checkLength = (value) => {
                /* eslint-disable no-control-regex */
                const r = value.replace(/[^\x00-\xff]/g, '**').length;
                return r > 40;
            };
            switch (true) {
            case pureValue === '':
                return '分组名不能为空';
            case checkLength(pureValue):
                return '分组名不能超过20个中文或40个英文';
            case checkRepeated(pureValue):
                return '分组名不能重复';
            default:
                return true;
            }
        },
        dialogEditorPrompt({ type, inputValue, groupId }) {
            const title = this.dialogTitle[type];
            return this.$prompt('请输入分组名(最多20个中文或者40个英文)', title, {
                inputValidator: this.validateName.bind(this, groupId),
                inputValue,
                confirmButtonText: this.dialogConfirmButtonText,
                customClass: 'demand-group-editorbox',
            });
        },
        async doSave({ value }, groupId) {
            const next = (item) => {
                this.$message.success('保存成功！');
                // 该事件考虑删除。
                if (groupId) {
                    this.$emit('on-edit', item);
                } else {
                    this.$emit('on-add', item);
                }
            };
            const newItem = await this.$store.dispatch('demand_group_save', {
                groupName: trim(value),
                groupId,
            });
            next(newItem);
        },
        handleGroupOpMy(ins) {
            this.opGroup = ins.group;
            switch (ins.type) {
            case 'doShowCooInfo':
                this.showCooInfo(this.opGroup);
                break;
            case 'doEditCooperator':
                this.editCooperator(this.opGroup);
                break;
            case 'doShare':
                this.shareGroup(this.opGroup);
                break;
            case 'doEditName':
                this.editGroupName(this.opGroup);
                break;
            case 'doDelete':
                this.deleteGroup(this.opGroup);
                break;
            default:
                break;
            }
        },
        handleGroupOpFollowed(ins) {
            this.opGroup = ins.group;
            if (ins.type === 'unsubscribe') {
                this.unsubscribe(this.opGroup);
            }
        },
        showCooInfo(group) {
            DemandGroupModel.getGroupCooperators(group.groupId).then((data) => {
                // map为兼容方案，account与name已不使用，后期删除
                const cooperators = data.map((item) => {
                    const { account, name } = item;
                    return Object.assign(item, { userErp: item.userErp || account, userName: item.userName || name });
                });
                this.$refs.showCooInfoEl.show(group, cooperators);
            });
        },
        editCooperator(group) {
            DemandGroupModel.getGroupCooperators(group.groupId).then((data) => {
                // map为兼容方案，account与name已不使用，后期删除
                this.cooperators = data.map((item) => {
                    const { account, name } = item;
                    return Object.assign(item, { userErp: item.userErp || account, userName: item.userName || name });
                });
                this.dialogMembersMgrVisible = true;
            });
        },
        shareGroup(group) {
            this.$refs.shareEl.show(group);
        },
        editGroupName(group) {
            const { groupId, groupName } = group;
            this.dialogEditorPrompt({
                type: 'edit',
                inputValue: groupName,
                groupId,
            }).then((data) => {
                this.doSave(data, groupId);
            });
        },
        deleteGroup(group) {
            this.$confirm(`确定要删除“${group.groupName}”这个分组么？`, '提示', {
                type: 'warning',
                customClass: 'demand-group-confirmbox',
            }).then(() => {
                this.$store.dispatch('demands_group_delete', group).then(() => {
                    this.$message.success('删除成功！');
                    this.$emit('on-del', group);
                });
            });
        },
        unsubscribe(group) {
            this.$confirm(`确定要取消关注“${group.groupName}”这个分组么？`, '提示', {
                type: 'warning',
                customClass: 'demand-group-confirmbox',
            }).then(() => {
                this.$store.dispatch('demands_group_delete', group).then(() => {
                    this.$message.success('取消关注成功！');
                    this.$emit('on-del', group);
                });
            });
        },
        handleGroupItemSelectMy(groupId) {
            this.currentGroupId = groupId;
        },
        saveGroupCooperators() {
            const { cooperatorsTemp, opGroup } = this;
            const cb = () => {
                this.dialogMembersMgrVisible = false;
                this.fetchData();
            };
            DemandGroupModel.saveGroupCooperators({
                groupId: opGroup.groupId,
                cooperators: cooperatorsTemp.map(item => item.userErp),
            }).then(() => {
                this.$message.success('保存成功！');
                this.cooperators = this.cooperatorsTemp;
                cb();
            }, cb);
        },
        fetchData() {
            return this.$store.dispatch('demands_group_fetchData');
        },
        updateGroupCondition() {
            this.$emit('updateGroupCondition', this.groupCondition);
        },
        isCooGroup,
        hasCooGroup(item) {
            return this.data.some(o => o.groupId === item.groupId && o.category === CODE.MY_COO);
        },
        getCurrentListFirstGroup() {
            return this.mylist[0] || {};
        },
        refreshGroups() {
            this.fetchData();
        },
        getIns(type, group) {
            return {
                type,
                group,
            };
        },
    },
    computed: {
        data() {
            const { groups } = this.$store.state.demands;
            return groups || [];
        },
        mylist() {
            const currentList = this.data.filter(item => +item.category === CODE.MY_COO || +item.category === CODE.MY_DEFAULT);
            // 我的分组：
            // -我创建的分组&&组员>0
            // -有我在内的协同分组，category = 2
            // -我创建的分组&&组员===0
            return sortMyGroupList(currentList);
        },
        followedlist() {
            const currentList = this.data.filter(item => +item.category === CODE.FOLLOWED);
            // 这个判定似乎无用？？
            return currentList.sort((o) => {
                if (this.hasCooGroup(o)) {
                    return -1;
                }
                return 1;
            });
        },
    },
    mounted() {
        this.refreshGroups();
    },
    watch: {
        currentGroupId: {
            handler() {
                if (!this.currentGroupId) {
                    this.currentGroup = this.getCurrentListFirstGroup();
                } else {
                    this.currentGroup = this.data.find(item => +item.groupId === +this.currentGroupId) || {};
                }
                this.groupCondition.groupId = this.currentGroupId;
                // 因为同一分组可能同时出现在参与和关注列表，设计有些问题，待后续产品完善。
                const sortFlag = this.mylist.some(item => item.groupId === this.currentGroup.groupId);
                this.groupCondition.isSortable = sortFlag;
                this.updateGroupCondition();
            },
        },
        mylist: {
            handler() {
                if (this.mylist.length !== 0 && !this.currentGroupId) {
                    this.currentGroup = this.mylist[0];
                    this.currentGroupId = this.currentGroup.groupId;
                }
            },
        },
        dialogMembersMgrVisible: {
            handler(val) {
                if (!val) {
                    this.cooperatorsTemp = this.cooperators.slice();
                } else {
                    this.$nextTick(() => {
                        this.$refs.memberMgrEl.reset();
                    });
                }
            },
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.demand-group-aside-root {
    margin-top: 16px;
    & > .el-menu {
        border: 0;
        background-color: transparent;
    }
    .demand-group-asideItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        vertical-align: baseline;
        height: 100%;
        & > * {
            vertical-align: baseline;
        }
        &-icon {
            color: #C0C4CC;
            &.active {
                color: #C0C4CC;
                &:hover {
                    color: @secendColor;
                }
            }
        }
    }
    .group-cate-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #909399;
        font-size: 12px;
        height: 40px;
        .add-group-item {
            font-size: 14px;
            color: #C0C4CC;
            margin-right: 14px;
        }
    }
    .demand-group-aside-menuitem {
        padding: 0 8px 0 16px !important;
        height: 40px;
        line-height: 40px;
        min-width: 100px;
        border-radius: 4px;
        &.el-menu-item.is-active {
            color: #2695F1;
            background-color: #E9F4FD;
        }
    }
    .demand-group-aside-submenu {
        margin-bottom: 24px;
        & > .el-submenu__title {
            padding: 0 16px !important;
            height: 40px;
            line-height: 40px;
            &:hover {
                background-color: #fff;
            }
            & > .el-submenu__icon-arrow {
                color: #C0C4CC;
                font-size: 14px;
                right: 17px;
            }
        }
        & > .el-menu {
            padding: 0 8px;
            background-color: transparent;
        }
    }
}
.demand-group-editorbox, .demand-group-confirmbox {
    padding: 0;
    border-radius: 0;
    width: 464px;
    & .el-message-box {
        &__header {
            border-bottom: 1px solid #EBEEF5;
            padding: 12px 16px;
        }
        &__headerbtn {
            & > .el-message-box__close {
                color: #C0C4CC
            }
        }
        &__title {
            & > span {
                font-size: 16px;
                font-weight: 600;
            }
        }
        &__content {
            padding: 16px 16px 0 16px;
        }
        &__input {
            padding-top: 8px;
        }
        &__btns {
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
        &__errormsg {
            height: 24px;
        }
    }
}
.group-member-manage {
    & > .el-dialog {
        position: absolute;
        margin-top: 0 !important;
        top: 50%;
        left: 50%;
        margin: 0;
        transform: translate(-50%, -50%);
        height: 600px;
        border-radius: 0;
        & .el-dialog__header {
            border-bottom: 1px solid #EBEEF5;
            padding: 12px 16px;
            position: relative;
            & > span {
                font-size: 16px;
            }
            & > .el-dialog__headerbtn {
                position: absolute;
                right: 16px;
                top: 50%;
                transform: translateY(-50%);
            }
        }
        & .el-dialog__footer {
            padding: 16px;
            .dialog-footer > .el-button {
                border-radius: 6px;
            }
        }
        & .el-dialog__body {
            padding: 0;
        }
    }
}
</style>
