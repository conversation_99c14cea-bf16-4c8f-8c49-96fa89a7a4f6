<template>
    <div class="demand-detail-relatives">
        <el-table
            class="j-table--simple"
            v-if="tableData"
            :data="tableData"
        >
            <el-table-column
                prop="name"
                label="需求名称"
                width="180"
            >
                <template slot-scope="scope">
                    <div class="demand-detail-relatives__type">
                        {{ scope.row.role === "parent" ? '父' : '子' }}
                    </div>
                    <a
                        :href="`/demands/list?demandId=${scope.row.id}`"
                        target="_blank"
                    >
                        {{ scope.row.name }}
                    </a>
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="需求状态"
            >
                <span slot-scope="scope">
                    {{ scope.row.status | jacp-status-text }}
                </span>
            </el-table-column>
            <el-table-column
                prop="receiverName"
                label="受理人"
            />
            <el-table-column
                prop="proposerName"
                label="提交人"
            />
        </el-table>
    </div>
</template>
<script type="text/javascript">
export default {
    props: {
        tableData: {
            type: Array,
            required: true,
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';

.demand-detail-relatives{
    font-size: 12px;
    width: 100%;
    &__type{
        border: none;
        background-color: @primaryColor;
        color: #fff;
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        transform: scale(0.6);
        font-size: 15px;
        display: inline-block;
    }
    & .el-table th:nth-child(4),
    & .el-table__row td:nth-child(4){
        text-align: right;
    }
}
</style>
