<template>
    <div>
        <jacp-form-link-group
            v-model="demand.joySpaceLinks"
            :disabled="disabled"
            :on-delete="handleDelete"
        >
            <LocalDialogModulePointRender
                v-if="addon"
                ref="dialog"
                :addon="addon"
                :context-params="contextParams"
                :disabled="disabled"
                slot="action"
            />
        </jacp-form-link-group>
    </div>
</template>
<script>
import LocalDialogModulePointRender from '@/modules/root/components/dialogModulePointRender.vue';

import {
    loadDemandAddons, demandFieldTabExtensionPoint,
} from '@/models/app';

export default {
    name: 'DemandJoyspaceLinks',
    props: {
        demand: { type: Object, default: () => ({}) },
        disabled: { type: Boolean, default: true },
        onAdd: { type: Function, default: () => {} },
        onDelete: { type: Function, default: () => {} },
    },
    data() {
        return {
            addon: null,
        };
    },
    components: {
        LocalDialogModulePointRender,
    },
    computed: {
        contextParams() {
            return {
                ...demandFieldTabExtensionPoint.contextGenerator(this.demand),
                // 给joyspace提供的回掉方法
                // 参数列表文档：https://joyspace.jd.com/page/E1Ks2iFqDdR50PLgZ7dH
                onUpdated: this.handleChange.bind(this),
            };
        },
    },
    methods: {
        async initAddon() {
            const addons = await loadDemandAddons(demandFieldTabExtensionPoint);
            [this.addon] = addons;
        },
        handleDelete(link) {
            return Promise.resolve(this.onDelete(link, this.demand));
        },
        handleChange(evt) {
            const { type, data } = evt;
            if (type === 'confirm') {
                Promise.resolve(this.onAdd(data)).then((result = []) => {
                    this.$refs.dialog.hideDialog();
                    // 这里返回的是全量数据，因为是批量保存，所以服务器端有查重的处理
                    // 新建的时候是前端查重
                    this.$emit('update:joySpaceLinks', result);
                });
            }
            if (type === 'cancel') {
                this.$refs.dialog.hideDialog();
            }
        },
    },
    watch: {
        demand: {
            immediate: true,
            handler() {
                this.initAddon();
            },
        },
    },
};
</script>
