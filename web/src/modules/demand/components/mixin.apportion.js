import { Apportion } from '@/models/apportion';

export default {
    data() {
        return {
            currentTask: null,
            currentTaskDetail: null,
        };
    },
    methods: {
        showApportionDetails(id, item = {}) {
            // console.log(id);
            Apportion.getTaskDetail(id).then((data) => {
                this.currentTask = Object.assign({}, item, {
                    records: data,
                });
            });
        },
    },
    computed: {
        currentTaskVisible: {
            get() {
                return Boolean(this.currentTask);
            },
            set(val) {
                if (!val) {
                    this.currentTask = null;
                }
            },
        },
        currentTaskDetailTitle() {
            return !this.currentTask ? '' : `查看分摊详情「${this.currentTask.demand.demandCode}」`;
        },
    },
    destroyed() {
        this.currentTask = null;
    },
};
