<template>
    <!-- 编辑态/显示态的表单 -->
    <el-form
        v-if="mode === 'editable'"
        class="demand-task-line"
        ref="formIns"
        :model="formData"
        :rules="rules"
        :inline="true"
    >
        <el-form-item prop="content">
            <jacp-input
                v-model="formData.content"
                placeholder="请输入任务名称"
                :disabled="disabled"
                @change="update"
            />
        </el-form-item>
        <el-form-item prop="erp">
            <jacp-user-selector
                v-model="formData.erp"
                placeholderText="请选择"
                :users="users"
                :currentUser="{ erp: formData.erp, name: formData.name }"
                :disabled="disabled"
                :showErp="true"
                @change="update"
            />
        </el-form-item>
        <el-form-item prop="duration">
            <jacp-date
                v-model="formData.duration"
                placeholder="请选择"
                type="daterange"
                :disabled="disabled"
                :clearable="false"
                @change="update"
            />
        </el-form-item>
        <el-form-item
            prop="plannedHours"
            :rules="rules.plannedHours.concat(extraRules.plannedHours)"
        >
            <jacp-input
                v-model="formData.plannedHours"
                type="number"
                placeholder="请输入"
                :disabled="disabled"
                @change="update"
            />
        </el-form-item>
        <el-form-item prop="status">
            <card-task-status
                v-model="formData.status"
                :change-status-disabled="disabled"
                @change-status="update"
            />
        </el-form-item>
        <el-form-item>
            <el-tooltip content="删除" placement="top-start" :open-delay="400">
                <i
                    class="jacp-icon-delete demand-task__icon"
                    @click="handleDelete"
                />
            </el-tooltip>
            <el-tooltip content="复制" placement="top-start" :open-delay="400">
                <i
                    class="jacp-icon-copy demand-task__icon"
                    @click="handleCopy"
                />
            </el-tooltip>
        </el-form-item>
    </el-form>

    <!-- 只有编辑态的表单 -->
    <el-form
        v-else
        class="demand-task-line demand-task-line-form"
        ref="formIns"
        :model="formData"
        :rules="rules"
        :inline="true"
    >
        <el-form-item prop="content">
            <el-input
                v-model="formData.content"
                placeholder="请输入任务名称"
            />
        </el-form-item>
        <el-form-item prop="erp">
            <user-selector
                v-model="formData.erp"
                placeholderText="请选择"
                :space-members="users"
                :showErp="true"
            />
        </el-form-item>
        <el-form-item prop="duration">
            <el-date-picker
                v-model="formData.duration"
                placeholder="请选择"
                type="daterange"
                :pickerOptions="pickerOptions"
            />
        </el-form-item>
        <el-form-item
            prop="plannedHours"
            :rules="rules.plannedHours.concat(extraRules.plannedHours)"
        >
            <el-input
                v-model="formData.plannedHours"
                type="number"
                placeholder="请输入"
            />
        </el-form-item>
        <el-form-item prop="status" class="no-transform">
            <card-task-status
                v-model="formData.status"
                :change-status-disabled="true"
            />
        </el-form-item>
        <el-form-item class="no-transform">
            <el-tooltip content="删除" placement="top-start" :open-delay="400">
                <i
                    class="jacp-icon-delete demand-task__icon"
                    @click="handleDelete"
                />
            </el-tooltip>
            <el-tooltip content="复制" placement="top-start" :open-delay="400">
                <i
                    class="jacp-icon-copy demand-task__icon"
                    @click="handleCopy"
                />
            </el-tooltip>
        </el-form-item>
    </el-form>
</template>

<script type="text/javascript">
import UserSelector from '@/modules/card/components/userSelector';
import CardTaskStatus from '@/modules/_components/CardTaskStatus';
import { pickerOptions } from '@/plugins/rules.form';

export default {
    components: {
        UserSelector,
        CardTaskStatus,
    },
    props: {
        formData: Object,
        rules: Object,
        disabled: {
            type: Boolean,
            default: false,
        },
        mode: {
            type: String, // 'editable' | 'form'
            default: 'editable',
        },
    },
    computed: {
        users() {
            return this.$store.state.demands.processors;
        },
    },
    data() {
        const vm = this;
        const plannedHoursRules = [{
            trigger: ['change', 'blur'],
            validator(rule, value, callback = () => {}) {
                const [startDate, deadline] = vm.formData.duration || [];
                let errorMsg = '';
                const days = ((deadline - startDate) / (1000 * 60 * 60 * 24)) + 1;

                if (+value > days * 24) {
                    errorMsg = '超过单日24小时计划工时建议修改';
                    return callback(new Error(errorMsg));
                }
                return callback();
            },
        }]
        return {
            pickerOptions,
            extraRules: {
                plannedHours: plannedHoursRules,
            },
        };
    },
    methods: {
        validate() {
            return this.$refs.formIns.validate();
        },
        update() {
            this.$emit('update', this.formData);
        },
        handleDelete() {
            this.$emit('delete', this.formData);
        },
        handleCopy() {
            this.$emit('copy', this.formData);
        },
    },
};
</script>

<style lang="less">
.demand-task-line {    
    display: grid;
    grid-template-columns: 240px 190px 250px 110px 100px 1fr;
    padding: 8px 0;
    border-bottom: 1px solid #EBEEF5;

    &:hover {
        background: #F5F7Fa;
    }
    .el-form-item {
        width: inherit;
        margin: 0 9px;
        .el-form-item__content {
            width: 100%;
            & > * {
                width: 100%;
            }
        }

        .el-form-item__error {
            position: relative;
        }
    }

    .jacp-form-input__edit {
        & > * {
            width: 100%;
        }
    }

    // type === 'form'
    &-form .el-form-item:not(.no-transform) {
        transform: translate(-9px);
    }

    .demand-task__icon {
        display: inline;
    }
}
</style>
