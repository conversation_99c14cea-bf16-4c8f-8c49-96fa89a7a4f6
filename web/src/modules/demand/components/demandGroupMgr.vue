<template>
    <div class="demand-group">
        <demand-group-list
            ref="groupListEl"
            opration
            @on-select="handleSelect"
            @on-del="setDefault"
            :current-group-cate="currentGroupCate"
        >
            <label
                slot="reference"
                class="demand-group__btn demands-filter__label"
            >
                <i>{{ getGroupNameSlice(currentGroup.groupName) }}</i>
                <i class="el-icon-arrow-down el-icon--right" />
            </label>
        </demand-group-list>
        <!-- 成员管理 -->
        <el-dialog
            width="80%"
            :title="dialogTitle.editCooperator"
            :visible.sync="dialogMembersMgrVisible"
        >
            <jacp-members-mgr
                ref="memberMgrEl"
                :except="[$store.state.user.erp]"
                :members="cooperators"
                @change="cooperatorsTemp = $event"
            >
                <span slot="header">分组名：{{ getGroupItemById(currentGroupId).groupName }}</span>
            </jacp-members-mgr>
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="dialogMembersMgrVisible = false">取消</el-button>
                <el-button
                    type="primary"
                    @click="saveGroupCooperators"
                >保存</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { DemandGroupModel, groupCateLabelMap, DemandGroupType as CODE } from '@/models/demandGroup';
import DemandGroupList from './demandGroups/demandGroupList';

export default {
    name: 'JacpDemandGroupMgr',
    props: {
        value: {
            type: [Number, String],
        },
        // 当前操作的demand
        currentItemId: {
            type: [Number, String],
        },
        currentGroupCate: {
            type: String,
            default: 'my', // 默认显示我的分组
        },
    },
    components: {
        DemandGroupList,
    },
    data() {
        return {
            currentGroupId: this.value,
            currentGroup: {},
            shareList: [],
            shareGroupData: {},
            dialogMembersMgrVisible: false,
            groupCateLabelMap,
            dialogTitle: this.$t('jacp.demandGroup.dialogTitle'),
            cooperators: [],
            cooperatorsTemp: [], // 取消的时候恢复原状
        };
    },
    computed: {
        data() {
            return this.$store.state.demands.groups || [];
        },
        currentGroupCateId() {
            return this.groupCateLabelMap[this.currentGroupCate].id;
        },
        isSortable() {
            const { category } = this.currentGroup;
            if (+category === CODE.MY_DEFAULT || +category === CODE.MY_COO) {
                return true;
            }
            if (this.$refs.groupListEl) {
                return this.$refs.groupListEl.hasCooGroup(this.currentGroup);
            }
            return false;
        },
        isOwner() {
            const { erp } = this.$store.state.user;
            const { ownerErp, category } = this.currentGroup;
            return ownerErp === erp && category === CODE.MY_DEFAULT;
        },
    },
    methods: {
        getGroupNameSlice(name) {
            const MAX_LEN = 12;
            if (!name) return '选择分组';
            if (name.length > MAX_LEN) return `${name.slice(0, MAX_LEN)}...`;
            return name;
        },
        getGroupItemById(groupId) {
            return this.data
                .filter(item => Number(item.groupId) === Number(groupId))[0] || {};
        },

        /* 这个需要在加载组件前调用，或者直接用store diapatch，为了防止多次调用，需要自己选择调用位置。。。 */
        fetchData() {
            return this.$store.dispatch('demands_group_fetchData');
        },
        handleSelect(val) {
            this.currentGroup = val;
            // this.$refs.groupListEl.hide();
        },
        // 协同分组组员管理
        editCooperator() {
            DemandGroupModel.getGroupCooperators(this.currentGroupId).then((data) => {
                // map为兼容方案，account与name已不使用，后期删除
                this.cooperators = data.map((item) => {
                    const { account, name } = item;
                    return Object.assign(item, { userErp: item.userErp || account, userName: item.userName || name });
                });
                this.dialogMembersMgrVisible = true;
            });
        },
        saveGroupCooperators() {
            const { cooperatorsTemp, currentGroupId } = this;
            const cb = () => {
                this.dialogMembersMgrVisible = false;
                this.fetchData();
            };
            DemandGroupModel.saveGroupCooperators({
                groupId: currentGroupId,
                cooperators: cooperatorsTemp.map(item => item.userErp),
            }).then(() => {
                this.$message.success('保存成功！');
                this.cooperators = this.cooperatorsTemp;
                cb();
            }, cb);
        },
        setDefault() {
            const {
                value, currentGroup, currentGroupCateId, data,
            } = this;
            const next = () => {
                this.$nextTick(() => {
                    this.currentGroup = this.$refs.groupListEl.getCurrentListFirstGroup();
                });
            };
            const isExist = group => data.includes(group);
            if (value) {
                const filterFn = (o) => {
                    if (+o.groupId !== +value) return false;
                    if (Array.isArray(currentGroupCateId)) {
                        return currentGroupCateId.includes(+o.category);
                    }
                    return +o.category === +currentGroupCateId;
                };
                const defaultGroup = data.find(filterFn);
                if (defaultGroup) {
                    this.currentGroup = defaultGroup;
                } else {
                    next();
                }
                return;
            }
            if (isExist(currentGroup)) return;
            next();
        },
    },
    watch: {
        currentGroupId: {
            immediate: true,
            handler(val, oldValue) {
                if (+val === +oldValue) return;
                this.$emit('input', val);
                this.$emit('on-change', val);
            },
        },
        currentGroup: {
            // immediate: true,
            handler() {
                this.currentGroupId = this.currentGroup.groupId;
                this.$emit('update:isOwner', this.isOwner);
                this.$emit('update:isSortable', this.isSortable);
                this.$emit('update:owner', this.currentGroup.owner);
            },
        },
        'currentGroup.cooperatorCount': {
            immediate: true,
            handler(val) {
                if (val !== undefined) {
                    this.$emit('update:coo-count', val);
                }
            },
        },
        dialogMembersMgrVisible: {
            handler(val) {
                if (!val) {
                    this.cooperatorsTemp = this.cooperators.slice();
                } else {
                    this.$nextTick(() => {
                        this.$refs.memberMgrEl.reset();
                    });
                }
            },
        },
        currentGroupCate: {
            handler() {
                this.$nextTick(() => {
                    this.currentGroup = this.$refs.groupListEl.getCurrentListFirstGroup();
                });
            },
        },
        data: {
            immediate: true,
            deep: true,
            handler(val) {
                if (!val.length) {
                    // this.currentGroup = {};
                    return;
                }
                this.setDefault();
            },
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.demand-group{
    // font-size: 12px;
    &__item{
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        position: relative;
        &.el-dropdown-menu__item--divided:before{
            position: absolute;
            top: 0;
        }
    }
    &__btn{
        cursor: pointer;
        white-space: nowrap;
        &--limit{
            max-width: 120px;
        }
    }
   /*  &__desc{
        color: @remarkColor;
        font-size: 12px;
    } */
    /* &__dialog{
        .demand-group__table{
            max-height: 500px;
            overflow: auto;
            &:before{
                display: none;
            }
        }
    } */
}
</style>
