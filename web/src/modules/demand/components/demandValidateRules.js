import { calcLength } from '@/plugins/utils';

function textRuleCreator(min, max, trigger = ['change']) {
    const countRule = {
        trigger,
    };
    const res = [countRule];
    if (max) {
        countRule.max = max;
        countRule.message = `限${max}字`;
    }
    if (min !== undefined && min !== null) {
        countRule.min = min;
        countRule.message = `不得少于${min}字`;
    }
    if (max && min !== undefined && min !== null) {
        countRule.message = `长度在${min} - ${max}个字符`;
    }
    return res;
}

// function validateReciever(rule, value, callback) {
//     if (value.length > 0) {
//         DemandModel.getCanRecipientDemand({
//             userErp: value[0].erp,
//         }).then((data) => {
//             if (data.valid) {
//                 callback();
//             } else {
//                 callback(new Error(rule.message));
//             }
//         });
//     } else {
//         callback(new Error(rule.message));
//     }
// }

function validateApportions(rule, value, callback) {
    const demanders = JSON.parse(JSON.stringify(value.demanders));
    const apportions = JSON.parse(JSON.stringify(value.apportions));
    let apportion = 0;
    if (!apportions || apportions.length === 0) {
        return callback(new Error('成本分摊不能为空'));
    }
    const apportionsOrgIdSet = Array.from(new Set((apportions.map(e => e.apportionOrgId))));
    if (apportionsOrgIdSet.length !== apportions.length) {
        return callback(new Error('成本分摊不能填写相同部门'));
    }
    for (let i = 0; i < apportionsOrgIdSet.length; i += 1) {
        const item = apportions[i];
        if (!item.apportionOrgId) {
            return callback(new Error('分摊部门存在未填项'));
        }
        if (item.apportionOrgId === '/00000000') {
            return callback(new Error('请明确分摊部门'));
        }
        demanders.forEach((demander) => {
            const orgCode = demander.orgTierCode || demander.orgCode;
            if (orgCode.startsWith(item.apportionOrgId)) {
                Object.assign(demander, { isChecked: true });
                apportions[i].isChecked = true;
            }
        });
        apportion += item.apportion;
    }
    if (demanders.filter(item => !item.isChecked).length > 0
        || apportions.filter(item => !item.isChecked).length > 0) {
        return callback(new Error('您录入的分摊部门与需求人不能正确匹配，请检查并修改'));
    }
    if (apportion !== 100) {
        return callback(new Error('分摊比例总和必须为100'));
    }
    return callback();
}
function validateRichText(rule, value, callback) {
    // const MAX_LEN = 1000;
    const isEmpty = () => {
        if (!value || value.replace(/<p><br(\/)?><\/p>/, '') === '') return true;
        return false;
    };
    // debugger;
    if (isEmpty()) {
        return callback(new Error('需求描述不能为空'));
    }
    // TODO: 怎么校验富文本好呢？？这个依赖了VueComponent作为this，需要同时校验this.data.description

    /* if (this.data && this.data.description) {
        const { description } = this.data;
        if (description.length > MAX_LEN) {
            return callback(new Error(`限${MAX_LEN}字`));
        }
    } */
    return callback();
}

/* const isDuplication = (target, originList) => {
    // const MAX_EXIST_LEN = target.id ? 1 : 0;
    if (target.id) {
        return originList.filter(tag => tag.name === target.name && tag.id !== target.id).length;
    }
    return originList.filter(tag => tag.name === target.name && tag.id).length;
}; */
export function validateTags(rules, value = {}, callback) {
    // 数组的时候不参与名称的校验
    if (Array.isArray(value)) return callback();
    // 以下是tagName的校验
    if (value.name.trim() === '') return callback(new Error('标签名称不能为空'));
    // if (this.data && this.data.tags && isDuplication(value, this.data.tags)) return callback(new Error('标签名重复'));
    if (calcLength(value.name.trim()) > 16) return callback(new Error('最多输入8个中文或16个英文'));
    return callback();
}
export default {
    name: textRuleCreator(3, 80, ['change', 'blur']).concat({
        required: true,
        message: '需求名称不能为空',
    }),
    richText: [{
        required: true,
        validator: validateRichText,
        trigger: ['change', 'blur'],
    }],
    richTextAndDescription: [{
        validator(rule, { richText }, callback) {
            if (!richText) {
                return callback(new Error('需求描述不能为空'));
            }
            return callback();
        },
    }],
    actuality: textRuleCreator(undefined, 1000),
    roi: textRuleCreator(undefined, 1000),
    receivers: [{
        required: true,
        message: '请选择受理人',
    }],
    priorityId: [{
        pattern: /^[\w\d]*$/,
        message: '请设置业务优先级',
        trigger: 'change',
    }],
    demandSourceId: [{
        required: true,
        message: '请选择需求来源',
        trigger: 'change',
    }],
    demanderAndApportions: [{
        required: true,
        validator: validateApportions,
        trigger: 'blur',
    }],
    tags: [{
        validator: validateTags,
        trigger: ['change', 'blur'],
    }],
    autoAcceptanceDays: [{
        required: true,
        pattern: /^(([1-9])|(1[0-9])|20|21)$/,
        message: '请填写范围在1-21之间的数字',
        trigger: ['change', 'blur'],
    }],
    demandSourceOrg: [{
        validator(rule, value, callback) {
            if (value) {
                // 空对象不行
                if (!Object.keys(value).length) {
                    return callback(new Error('您录入的部门无效，请重新选择！'));
                }
                // 没有code不行
                if (!value.orgTierCode) {
                    return callback(new Error('您录入的部门无效，请重新选择！'));
                }
            }
            return callback();
        },
        trigger: ['change', 'blur'],
    }],
    priorityScore: [{
        required: true,
        message: '请填写需求优先级评分',
    }],
    revenueOther: textRuleCreator(undefined, 10).concat({
        required: false,
        message: '请填写其它收益分类',
    }),
};
