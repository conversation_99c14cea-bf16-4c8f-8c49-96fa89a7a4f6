<template>
    <div class="demand-apportion">
        <el-table
            class="demand-apportion__table"
            :data="value"
        >
            <el-table-column
                width="280"
            >
                <!-- element-ui@2.4.11才提供header的slot，必须加上slotscope，因为源码取的是slotscope -->
                <!-- eslint-disable vue/no-unused-vars -->
                <template
                    slot="header"
                    slot-scope="scope"
                >
                    <span class="cell-inner">成本分摊</span><el-tooltip
                        class="item"
                        placement="top"
                    >
                        <div slot="content">
                            1、成本分摊比例总和必须为100% <br>
                            2、成本分摊部门为需求人所在部门的同级或上级部门 <br>
                            3、需求人必须有对应的成本分摊部门 且 分摊部门下必须有对应的需求人
                        </div>
                        <jacp-icon
                            style="position: absolute;left: 75px;top: 1px;color: #c5c5c5;font-size: 16px"
                            name="jacp-icon-help"
                        />
                    </el-tooltip>
                </template>
                <template slot-scope="scope">
                    <el-autocomplete
                        v-model="scope.row.apportionOrgName"
                        :fetch-suggestions="querySearchOrgList"
                        :disabled="!apportionEdit"
                        placeholder="输入并选择分摊部门"
                        @select="handleSelect($event, scope.row)"
                        @focus="handleFocus(scope.row)"
                        @blur="handleBlur(scope.row)"
                        value-key="orgName"
                        label="orgCode"
                        style="width: 100%"
                    >
                        <i
                            v-show="scope.row.apportionOrgName && apportionEdit"
                            slot="suffix"
                            class="el-input__icon el-icon-error"
                            @click="clearQueryStr($event, scope.row)"
                        />
                    </el-autocomplete>
                </template>
            </el-table-column>
            <el-table-column label="分摊比例">
                <template slot-scope="scope">
                    <jacp-input-number
                        style="width: 100%"
                        v-model="scope.row.apportion"
                        controls-position="right"
                        :disabled="!apportionEdit"
                        @change="changeApportion()"
                        @blur="changeApportion()"
                        :min="1"
                        :max="100"
                        :precision="0"
                    />
                </template>
            </el-table-column>
            <el-table-column
                label=""
                width="60"
            >
                <template slot-scope="scope">
                    <el-button
                        class="j-link-warning"
                        type="text"
                        v-show="value && value.length > 1 && apportionEdit"
                        @click="deleteDemandApportion(scope.$index)"
                    >
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <div v-if="apportionEdit">
            <el-button
                type="text"
                @click="addDemandApportion"
            >
                +增加分摊
            </el-button>
        </div>
    </div>
</template>

<script>
import OrgModel from '@/models/org';

let tempOrgName;
export default {
    name: 'DemandApportion',
    props: {
        value: {
            default: () => [],
            type: Array,
        },
        apportionEdit: {
            default: false,
            type: Boolean,
        },
        orgData: {
            default: () => [],
            type: Array,
        },
    },
    data() {
        return {
            apportionUpdate: false,
            lockEdit: false,
            orgList: [],
        };
    },
    methods: {
        newApportionItem(name = '', id = '', apportion = 0) {
            return {
                apportionOrgName: name,
                apportionOrgId: id,
                apportion,
            };
        },
        handleSelect(data, row) {
            Object.assign(row, { apportionOrgId: data.orgCode, apportionOrgName: data.orgName });
            this.$nextTick(() => {
                this.$emit('on-change');
            });
        },
        handleFocus(row) {
            tempOrgName = row.apportionOrgName;
        },
        handleBlur(row) {
            const flag = row.apportionOrgId && row.apportionOrgId.split('/').length - 1 !== row.apportionOrgName.split('-').length;
            if (flag || (row.apportionOrgId && row.apportionOrgName !== tempOrgName)) {
                Object.assign(row, { apportionOrgName: tempOrgName });
                return;
            }
            this.$nextTick(() => {
                this.$emit('on-change');
            });
        },
        clearQueryStr($event, row) {
            Object.assign(row, { apportionOrgName: '', apportionOrgId: '' });
            const inputObj = $event.target.parentNode.parentNode.previousElementSibling;
            this.$nextTick(() => {
                inputObj.focus();
                this.$emit('on-change');
            });
        },
        addDemandApportion() {
            const apportionItem = this.newApportionItem();
            if (this.value.length === 0) {
                apportionItem.apportion = 100;
            }
            this.value.splice(this.value.length + 1, 0, apportionItem);
        },
        deleteDemandApportion(index) {
            this.value.splice(index, 1);
            // 当只有一个需求方时默认分摊比例 100
            if (this.value.length === 1) {
                this.value[0].apportion = 100;
            }
            this.$emit('on-change');
        },
        changeApportion() {
            this.$emit('on-change');
        },
        querySearchOrgList(queryStr, cb) {
            const list = this.orgList;
            cb(queryStr ? list.filter(item => item.orgName.indexOf(queryStr) >= 0) : list);
        },
        handleOrgData(data) {
            // 特殊处理
            if (this.$store.state.user.orgTierCode.includes('/00023242')) {
                this.specialOrgData(data);
                return;
            }
            const tempList = [];
            data.forEach((d) => {
                const code = (d.orgTierCode || d.orgCode).split('/').pop();
                OrgModel.getHighLevelOrgs(code).then((orgs) => {
                    orgs.forEach((item) => {
                        if (tempList.findIndex(org => org.orgCode === item.orgCode) < 0) {
                            tempList.push(item);
                        }
                    });
                });
            });
            this.orgList = tempList;
        },
        specialOrgData(data) {
            // 去重
            const temp = [];
            data.forEach((d) => {
                const orgFullId = d.orgTierCode || d.orgCode;
                if (temp.findIndex(org => org.orgCode === orgFullId) < 0) {
                    temp.push({ orgCode: orgFullId, orgName: d.orgTierName || d.orgName });
                }
            });

            this.value.splice(0, this.value.length);
            // 均摊分摊
            if (temp.length === 1) {
                this.value.push(this.newApportionItem(temp[0].orgName, temp[0].orgCode, 100));
            } else if (temp.length > 1) {
                const len = temp.length;
                const avg = Math.floor(100 / len);
                const last = 100 - avg * (len - 1);
                for (let i = 0; i < len - 1; i += 1) {
                    const item = temp[i];
                    this.value.push(this.newApportionItem(item.orgName, item.orgCode, avg));
                }
                this.value.push(this.newApportionItem(temp[len - 1].orgName, temp[len - 1].orgCode, last));
            }
            this.orgList = temp;
            this.changeApportion();
        },
    },
    watch: {
        orgData: {
            handler(n) {
                this.handleOrgData(n);
            },
            deep: true,
            immediate: true,
        },
        value: {
            immediate: true,
            handler(val) {
                if (!this.apportionEdit) {
                    return;
                }
                if (val && !val.length) {
                    this.addDemandApportion();
                }
            },
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var.less';

.option-admin-auto&.el-autocomplete-suggestion li,
.option-admin-auto li{
        white-space: inherit;
        line-height: 26px;
        font-size: 12px;
        border-bottom: 1px solid #dcdbdb;
        &:nth-of-type(2n){
            background-color: #f9f9f9;
        }
        &.highlighted{
            background-color: #08a4da;
            color: #fff;
        }
}
.demand-apportion__table{
    &.el-table--enable-row-hover .el-table__body tr:hover>td{
        background: transparent;
    }
    &.el-table{
        margin-top: 38px;
        overflow: visible;
    }
    &.el-table::before{
        top: -15px;
        // display: none;
    }
    td,th,
    th.is-leaf
    {
        padding: 0;
        border: none;
    }
    tr+tr{
        .cell{
            margin-top: 16px;
        }
    }
    th>.cell{
        font-weight: 500;
        font-size: 14px;
        color: @fontColor;
        // padding-left: 0;
    }
    &.el-table .cell{
        padding-left: 0;
        padding-right: 24px;
    }
    th>.cell:not(:empty):after{
        content: '*';
        color: @requiredColor;
        margin-left: 4px;
    }
}
</style>
