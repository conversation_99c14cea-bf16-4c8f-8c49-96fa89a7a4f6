<template>
    <div class="dev-milestone">
        <el-steps
            :style="`flex-basis: ${100/data.length * allStatus.length}%;`"
            align-center
            :active="active"
        >
            <el-step
                v-for="status in allStatus"
                :key="status.cardStatusId"
                icon="icon"
                :title="getTitle(status)"
                :description="status.isShowDetail ? status.curStageStatusName : ''"
            >
                <i
                    slot="icon"
                    class="dev-milestone__statusicon"
                />
            </el-step>
        </el-steps>
        <el-steps
            :style="`flex-basis: ${100/data.length}%;`"
            v-if="isSuspendedActive"
            align-center
            :active="1"
        >
            <el-step
                icon="icon"
                :title="isSuspendedActive.stageName"
            >
                <i
                    slot="icon"
                    class="dev-milestone__statusicon"
                />
            </el-step>
        </el-steps>
    </div>
</template>

<script type="text/javascript">
import findLastIndex from 'lodash/findLastIndex';

export default {
    props: {
        data: {
            type: Array,
            required: true,
        },
    },
    methods: {
        getTitle(status) {
            if (status.isShowDetail === 1) {
                return `${status.stageName}(${status.curStageStatusIndex
                    || 0}/${status.totalStageStatusNum || 0})`;
            }
            return status.stageName;
        },
    },
    computed: {
        allStatus() {
            return this.data.filter(o => +o.cardStageId !== 170);
        },
        active() {
            const index = findLastIndex(this.allStatus, 'isActive');
            return index + 1;
        },
        isFinish() {
            return this.allStatus.every(o => o.isActive);
        },
        isSuspendedActive() {
            return this.data.find(o => +o.cardStageId === 170
            && o.isActive);
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.dev-milestone{
    display: flex;
    justify-content: space-between;
    .el-step__head.is-process,
    .el-step__head.is-wait {
        color: #E1E1E1;
        border-color: #E1E1E1;
    }
    .el-step__title.is-process,
    .el-step__title.is-wait{
        font-weight: normal;
        color: #999999;
    }
    & .el-step__icon.is-icon{
        width: 6px;
    }
    & .el-step__title{
        font-size: 12px;
    }
    &__statusicon{
        &::before{
            content: ' ';
            display: inline-block;
            border-radius: 50%;
            width: 8px;
            height: 8px;
            transform: translateY(-1px);
            background-color: #c0c4cc;
        }
    }
    .is-finish &__statusicon:before{
        background-color: @primaryColor;
    }
}
</style>
