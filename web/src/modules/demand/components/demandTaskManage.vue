<template>
    <el-dialog
        title="管理任务"
        custom-class="app-root j-dialog demand-task-dialog"
        :append-to-body="true"
        :modal-append-to-body="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="dialogVisible"
        :before-close="handleClose"
        @closed="$emit('close-task-manage')"
    >
        <div class="demand-task__description">在需求上新增任务，用于记录在需求沟通过程中产生的任务</div>

        <div class="j-table">
            <el-table>
                <el-table-column
                    label="名称"
                    width="240"
                />
                <el-table-column
                    width="190"
                >
                    <div slot="header">
                        <span>处理人</span>
                        <el-tooltip content="处理人包括需求提交人、受理人、需求人、关注人" placement="right">
                            <i
                                class="jacp-icon-question"
                                style="color: #c0c4cc; margin-left: 4px;"
                            />
                        </el-tooltip>
                    </div>
                </el-table-column>
                <el-table-column
                    label="起止时间"
                    width="250"
                />
                <el-table-column
                    label="计划工时"
                    width="110"
                />
                <el-table-column
                    label="任务状态"
                    width="100"
                />
                <el-table-column
                    label="操作"
                />
            </el-table>
            <demand-task-line
                mode="editable"
                v-for="(formData, index) in tasksSaved"
                :key="'line-editable' + index"
                :form-data="formData"
                :rules="rules"
                :disabled="disabled"
                @update="update($event)"
                @delete="handleDelete($event, index)"
                @copy="handleCopy($event, index)"
            />
            <demand-task-line
                ref="lineForms"
                mode="form"
                v-for="(formData, index) in tasksUnsaved"
                :key="'line-form' + formData.index"
                :form-data="formData"
                :rules="rules"
                :disabled="disabled"
                @delete="handleDelete($event, index)"
                @copy="handleCopy($event, index)"
            />
        </div>

        <el-button
            v-if="!disabled"
            type="text"
            @click.native="addTask"
        >
            +新增任务
        </el-button>
        <div
            v-if="disabled"
            class="demand-task__description demand-task__description--center"
        >
            <div>需求提交人、受理人、需求人、关注人</div>
            <div>可以在这里记录任务</div>
        </div>

        <span
            slot="footer"
            class="dialog-footer"
        >
            <el-button
                v-show="tasksUnsaved.length"
                type="primary"
                @click="handleSaveInBatches"
            >
                保存
            </el-button>
            <el-button @click="handleClose">关闭</el-button>
        </span>
    </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce';
import DemandTaskModel from '@/modules/demand/models/demandTask';
import demandTaskRules from '@/modules/demand/models/demandTaskRules';
import demandTaskLine from './demandTaskLine';

const frequency = 500; // 服务端处理完毕之后，该条任务行不会立即更新，禁止连续点击操作

export default {
    components: {
        demandTaskLine,
    },
    props: {
        demandId: Number,
        projectId: Number,
    },
    data() {
        return {
            dialogVisible: false,
            tasksSaved: [],
            tasksUnsaved: [],
            rules: demandTaskRules,
            processors: [], // 处理人（需求相关人）
            adding: false, // 正在添加任务中，限制动作为单条任务添加
            lock: false,
            disabled: false,
        };
    },
    computed: {
        currentUser() {
            return this.$store.state.user;
        },
    },
    created() {
        const delay = 200;
        this.handleDelete = debounce(this.handleDelete, delay, { leading: true });
        this.handleCopy = debounce(this.handleCopy, delay);
        // this.handleSave = debounce(this.handleSave, delay);
    },
    methods: {
        async init() {
            await this.getProcessors();
            await this.getTasks();
            this.open();
        },
        async getProcessors() {
            await DemandTaskModel.getProcessors(this.demandId)
                .then((data) => {
                    this.processors = data;
                    this.disabled = !this.processors
                        .map(processor => processor.erp)
                        .includes(this.currentUser.erp);
                    this.originDisabled = this.disabled;
                });
        },
        async getTasks() {
            await DemandTaskModel.getList(this.demandId)
                .then((demandTasks) => {
                    this.tasksSaved = demandTasks;
                    this.lastIndex = demandTasks[demandTasks.length - 1]?.index ?? 0;
                });
        },
        open() {
            this.dialogVisible = true;
        },
        update(task) {
            return task.save();
        },
        handleClose() {
            if (this.disabled) {
                this.dialogVisible = false;
                return;
            }

            if (this.tasksUnsaved.length) {
                this.$confirm('任务还没有保存，确认离开？', '提示', {
                    confirmButtonText: '离开',
                    cancelButtonText: '去保存',
                    type: 'warning',
                    customClass: 'j-message-box',
                }).then(() => {
                    this.dialogVisible = false;
                });
            } else if (!this.projectId && this.tasksSaved.length) {
                this.$alert('该需求没有关联项目，需要关联项目后，才可以填报工时。', '提示', {
                    confirmButtonText: '知道了',
                    customClass: 'j-message-box',
                    showClose: false,
                }).then(() => {
                    this.dialogVisible = false;
                });
            } else {
                this.dialogVisible = false;
            }
        },
        addTask(row = {}) {
            if (this.tasksSaved.length >= 200) {
                this.$message.warning('需求下任务数量不可超过200条');
                return;
            }

            this.lastIndex += 1;
            const defaultProps = {
                id: undefined,
                status: 1, // 新增，复制的任务状态都是只能是 未开始
            };
            const newTask = new DemandTaskModel({
                ...row,
                ...defaultProps,
                index: this.lastIndex,
            });
            this.tasksUnsaved.push(newTask);
        },
        handleSaveInBatches() {
            const promises = this.$refs.lineForms.reduce((acc, ins) => {
                acc.push(ins.validate());
                return acc;
            }, []);
            Promise.all(promises).then(() => DemandTaskModel.batchCreate(this.tasksUnsaved, this.demandId)).then((data) => {
                this.tasksSaved = [
                    ...this.tasksSaved,
                    ...data,
                ];
                this.tasksUnsaved = [];
                this.$message.success('保存成功');
            });
        },
        handleDelete({ demandId, id, usedHours }, index) {
            if (this.lock) {
                return;
            }

            if (!id) {
                this.tasksUnsaved.splice(index, 1);
                return;
            }
            if (demandId && id && usedHours) {
                this.$confirm('该任务已填工时，无法删除', '提示', {
                    confirmButtonText: '知道了',
                });
                return;
            }
            if (demandId && id && !usedHours) {
                this.$confirm('删除该条任务后将同步清除项目中对应任务，是否确定', '提示', {
                    confirmButtonText: '确定删除',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    this.lock = true;
                    return DemandTaskModel.delete({ demandId, id });
                }).then(() => {
                    this.tasksSaved.splice(index, 1);
                    this.$message.success('删除成功');
                }).finally(() => {
                    setTimeout(() => {
                        this.lock = false;
                    }, frequency);
                });
            }
        },
        handleCopy(row) {
            this.addTask(row);
        },
    },
};
</script>

<style lang="less">
.j-dialog.demand-task-dialog {
    width: 1032px;
    height: 70vh;
    display: flex;
    flex-direction: column;
    .el-dialog {
        &__body {
            flex: 1;
            overflow: overlay;
        }
    }
    tbody td {
        padding: 0;
        .cell {
            .el-form-item {
                margin-bottom: 0;
            }
        }
    }

    .el-table__body-wrapper {
        display: none;
    }

    .demand-task {
        &__description {
            color: var(--color--secondary--content);
            font-size: var(--font-size--description);
            padding-bottom: var(--gutter--medium);
            &--center {
                padding: var(--gutter--medium);
                display: flex;
                flex-direction: column;
                place-items: center;
            }
        }
        &__icon {
            color: var(--color--extralight--content);
            cursor: pointer;
            margin-right: var(--gutter--small);
        }
    }
}
</style>
