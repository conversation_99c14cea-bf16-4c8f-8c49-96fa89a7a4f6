<template>
    <div class="demandslist-operations">
        <template v-for="item in actions">
            <a
                class="demandslist-operations__item"
                v-if="item.type !== 'dropdown'"
                @click="clickHandle(item)"
                :disabled="item.disabled"
                :key="item.cmd"
            >{{ item.text }}</a>
            <el-dropdown
                v-if="item.type === 'dropdown'"
                trigger="click"
                :key="item.cmd"
            >
                <span class="el-dropdown-link demandslist-operations__item">
                    {{ item.text }}
                </span>
                <el-dropdown-menu
                    slot="dropdown"
                    :append-to-body="false"
                >
                    <el-dropdown-item
                        v-for="childItem in item.children"
                        :key="childItem.cmd"
                        @click.native="clickHandle(childItem)"
                    >
                        <a>{{ childItem.text }}</a>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </template>
    </div>
</template>

<script type="text/javascript">
import { demandOperations } from '@/models/config';

export default {
    props: {
        isHandler: { // 是否是处理方
            required: true,
            type: <PERSON><PERSON>an,
        },
        isProposer: { // 是否提交人
            type: Boolean,
            default: false,
        },
        status: { // 需求状态
            required: true,
            type: Number,
        },
        evaluated: {
            type: Number,
            default: 0,
        },
        hasChild: { // 是否有子需求
            type: Boolean,
            default: false,
        },
        hasDevStatus: { // 是否关联卡片
            type: Boolean,
            default: false,
        },
        isOtherSource: { // 是否外部系统需求
            type: Boolean,
            default: false,
        },
        root: {
            type: Boolean,
            default: false,
        },
        disabledActions: [String, Array],
        avaliableOperations: { type: Array },
    },
    methods: {
        clickHandle(action) {
            if (action.disabled) {
                return;
            }
            this.$emit('on-action', action.cmd);
        },
    },
    computed: {
        actions() {
            // 外部系统需求禁用所有数据操作
            if (this.isOtherSource) {
                return [];
            }
            const handlerIndex = this.isHandler ? 1 : 0;
            const actions = demandOperations[handlerIndex][this.status] || [];
            const proposerActions = this.isProposer ? (demandOperations[2][this.status] || []) : [];
            let childActions = [];
            let mergedActions = [];
            proposerActions.concat(actions).forEach((action) => {
                if (action instanceof Array) {
                    childActions = childActions.concat(action);
                } else if (action !== 'split' || !this.hasDevStatus) {
                    mergedActions.push(action);
                }
            });
            // 数组去重, 合并
            mergedActions = [...new Set(mergedActions)];
            if (childActions.length) {
                mergedActions.push([...new Set(childActions)]);
            }
            if (!this.root) {
                mergedActions = mergedActions.filter(item => item !== 'evaluate')
                    .filter(item => item !== 'evaluation');
            }
            // 如果需求状态是已完成，则判断是否收益验证，来分别过滤"收益验证"和"查看收益验证"
            if (this.status === 20) {
                if (this.evaluated === 1) {
                    mergedActions = mergedActions.filter(item => item !== 'evaluate');
                } else {
                    mergedActions = mergedActions.filter(item => item !== 'evaluation');
                }
            }
            // 有删除的就把删除的操作挪到最后去
            if (mergedActions.includes('cancel')) {
                mergedActions = [...mergedActions.filter(i => i !== 'cancel'), 'cancel'];
            }
            // 有一个细节，如果两个元素一个在外层，一个被收纳在“更多”中，会出现两次，单不确定具体应该被怎么处理，暂时还没有这种情况。
            if (Array.isArray(this.avaliableOperations)) {
                mergedActions = mergedActions.filter(action => this.avaliableOperations.includes(action));
            }
            return mergedActions.map((action) => {
                let disabled;
                if (action instanceof Array) {
                    return {
                        text: this.$t('jacp.demandOperations.other'),
                        type: 'dropdown',
                        children: action.map(childAction => ({
                            text: this.$t(`jacp.demandOperations.${childAction}`),
                            cmd: childAction,
                        })),
                    };
                }
                if (this.disabledActions) {
                    disabled = this.disabledActions.includes(action);
                }
                return {
                    text: this.$t(`jacp.demandOperations.${action}`),
                    cmd: action,
                    disabled,
                };
            });
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var.less';
.demandslist-operations__item{
    color: @linkColor;
    font-size: 12px;
    margin: 0 4px;
    cursor: pointer;

    &>i{
        font-size: 12px;
    }
    &[disabled] {
        color: @fontDisabledColor;
        pointer-events: none;
    }
}
</style>
