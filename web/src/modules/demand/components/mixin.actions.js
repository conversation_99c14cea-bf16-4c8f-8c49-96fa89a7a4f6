import AsyncValidator from 'async-validator';
import { demandOperations } from '@/models/config';
import demandValidateRules from './demandValidateRules';
import { isCurrentUser } from '@/plugins/utils';


function createActions({
    isHandle, status, isProposer, hasDevStatus, evaluated, root,
}) {
    const handlerIndex = isHandle ? 1 : 0;
    let childActions = [];
    // 定义附加操作
    const attachActions = [];
    const actions = (demandOperations[handlerIndex][status] || []).concat([]);
    const proposerActions = isProposer ? (demandOperations[2][status] || []) : [];
    actions.concat(proposerActions).forEach((action) => {
        if (action instanceof Array) {
            childActions = childActions.concat(action);
        } else if (action !== 'split' || !hasDevStatus) {
            childActions.push(action);
        }
    });
    // 需要对 childActions 去重
    childActions = [...new Set(childActions)];
    if (!root) {
        childActions = childActions.filter(item => item !== 'evaluate')
            .filter(item => item !== 'evaluation');
    }
    if (status === 20) {
        if (evaluated === 1) {
            childActions = childActions.filter(item => item !== 'evaluate');
        } else {
            childActions = childActions.filter(item => item !== 'evaluation');
        }
    }
    // 有删除的就把删除的操作挪到最后去
    if (childActions.includes('cancel')) {
        childActions = [...childActions.filter(i => i !== 'cancel'), 'cancel'];
    }
    return attachActions.map(action => ({
        text: this.$t(`jacp.demandOperations.${action}`),
        cmd: action,
        disabled: false,
    })).concat(childActions.filter(action => action !== 'remark').map(action => ({
        text: this.$t(`jacp.demandOperations.${action}`),
        disabled: false,
        cmd: action,
    })));
}

function validateSubmitData(data) {
    return new Promise((resolve, reject) => {
        const validator = new AsyncValidator(demandValidateRules);
        validator.validate(data, (err) => {
            if (!err || !err.length) {
                resolve();
            } else {
                reject(err.map(item => item.message));
            }
        });
    });
}

export default {
    data() {
        return {
            actions: [],
        };
    },
    methods: {

        /* TODO: async-validator*里版本升级了以后，原本抛异常的地方被catch了，后面的就都不执行了，解决方案：
            1. 版本降级
            2. 校验是否必须？没有异常的时候为什么没有被resolve？ */
        $_setActions(demandIns, callBack) {
            validateSubmitData(demandIns).then(() => true).catch(() => false)
                .then((isValid) => {
                    if (demandIns.system !== 'jacp') {
                        this.actions = [];
                    } else {
                        this.actions = createActions.bind(this)({
                            isHandle: isCurrentUser(demandIns.processor),
                            status: demandIns.status,
                            hasChild: demandIns.hasChild,
                            hasDevStatus: demandIns.hasDevStatus,
                            isValid,
                            isProposer: isCurrentUser(demandIns.proposer),
                            evaluated: demandIns.evaluated,
                            root: demandIns.parentDemandId === -1,
                        });
                    }
                    if (typeof callBack === 'function') {
                        callBack(demandIns, this.actions);
                    }
                });
        },
    },
};
