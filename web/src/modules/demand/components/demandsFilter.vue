<template>
    <div class="demands-filter-root">
        <div
            class="demands-operates"
        >
            <div
                class="demands-operates__leftwrap"
                v-if="type === 'draft'"
            >
                <div
                    style="padding: 10px 0; display: flex"
                >
                    <el-input
                        class="j-input--background j-mgr16"
                        placeholder="输入标题或编号"
                        prefix-icon="el-icon-search"
                        @clear="filterDemand"
                        clearable
                        v-model="condition.keyword"
                        @keydown.enter.native="filterDemand"
                    />
                    <jacp-button
                        size="small"
                        style="height: 32px;"
                        @click.native="filterDemand"
                    >
                        查询
                    </jacp-button>
                </div>
            </div>
            <div
                v-if="type === 'group'"
                class="demands-operates__groupheader"
            >
                <div class="group_condition_item">
                    <label class="demands-filter__label">状态</label>
                    <jacp-multiselect
                        class="demands-filter__value"
                        :data="filteredStatus"
                        v-model="condition.status"
                    />
                </div>
                <div class="group_condition_item">
                    <label class="demands-filter__label">需求人</label>
                    <jacp-user-select
                        placeholder="请输入"
                        class="demands-filter__value"
                        v-model="condition.demanders"
                    />
                </div>
                <div class="group_condition_item">
                    <label class="demands-filter__label">当前操作人</label>
                    <jacp-input-users
                        class="demands-filter__value"
                        v-model="condition.processors"
                        placeholder="请输入"
                        :max-count="1"
                    />
                </div>
                <div class="group_condition_item">
                    <label class="demands-filter__label">关键词</label>
                    <el-input
                        class="demands-filter__value"
                        type="text"
                        @input="$forceUpdate()"
                        v-model="condition.keyword"
                        placeholder="标题/编号"
                    />
                </div>
                <div :class="['group_condition_item', 'group_condition_item__tags']">
                    <label class="demands-filter__label">标签</label>
                    <el-select
                        class="demands-filter__value"
                        v-model="condition.tags"
                        multiple
                        filterable
                        collapse-tags
                        clearable
                        allow-create
                    >
                        <el-option
                            v-for="item in queryParamsTagsList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.name"
                        />
                    </el-select>
                </div>
                <jacp-button
                    size="small"
                    @click="filterDemand"
                >
                    查询
                </jacp-button>
            </div>
            <div class="demands-operates__rightwrap">
                <slot name="rightwrap" />
            </div>
        </div>
    </div>
</template>
<script type="text/javascript">
import DemandModel from '@/models/demands';
import { pick, omit } from 'lodash';

const filteredStatus = [2, 13, 3, 4, 5, 9, 10, 18, 19, 20, 21];

export default {
    props: {
        type: {
            type: String,
        },
    },
    data() {
        return {
            filteredStatus: filteredStatus.map(item => ({
                name: this.$t(`jacp.demandStatus.${item}`),
                id: item,
            })),
            condition: {
                keyword: '',
            },
            queryParamsTagsList: [],
        };
    },
    methods: {
        filterDemand() {
            // 该方法仅用于发射condition进行条件筛选，所有的筛选操作都经过这里发出。
            // this.condition永远保持可预筛选状态。
            this.$emit('filterDemand', this.filterDemandFixCondition());
        },
        filterDemandFixCondition() {
            // 任何时候的查询都对this.condition进行重构，以匹配后端原有的查询字段标准。
            if (this.type === 'draft') {
                const field = ['queryType', 'keyword'];
                return pick(this.condition, field);
            }
            if (this.type === 'group') {
                const field = [
                    'queryType',
                    'keyword',
                    'status',
                    'demanders',
                    'processors',
                    'tags'];
                return pick(this.condition, field);
            }
            if (this.type !== 'department') {
                const field = ['orgnazitionId', 'direction'];
                return omit(this.condition, field);
            }
            return this.condition;
        },
        clearAndRefreshGroupForm() {
            // used for parent
            if (this.type === 'group') {
                this.condition.status = [];
                this.condition.processors = [];
                this.condition.keyword = '';
                this.condition.tags = [];
                this.condition.demanders = [];
                this.filterDemand();
            }
        },
        getTagsListInGroup() {
            DemandModel.getInfos(['tagsList']).then(([tagsList]) => {
                this.queryParamsTagsList = tagsList;
            });
        },
        // Public methods:
        getConditions() {
            return { ...this.conditions };
        },
    },
    mounted() {
        this.getTagsListInGroup();
    },
};
</script>

<style lang="less">
.demands-filter-root {
    margin: 0;
    display: flex;
    flex-direction: column;
    .demands-operates {
        position: relative;
        height: 48px;
        width: 100%;
        // padding: 10px 16px;
        padding: 0 16px;
        background-color: #fff;
        margin-bottom: 1px;
        // border-bottom: 1px solid rgba(240,242,245,1);
        display: flex;
        justify-content: space-between;
        &__leftwrap {
            width: 100%;
            display: flex;
            .demands-filters-op {
                flex: 1;
                max-width: 100%;
            }
            .demands-filters-op-department {
                max-width: 80%;
            }
        }
        &__rightwrap {
            display: flex;
            flex-direction: row;
            align-items: center;
        }
        &-right__item {
            display: inline-block;
        }
        &__label, &__label .el-dropdown {
            font-size: 12px;
            color: #48576a;
        }
        &__innerleftwrap {
            width: calc(100% - 80px);
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            .org-selector__value {
                min-width: 0;
            }
            & .el-divider {
                margin: 0 16px;
            }
        }
        &__groupheader {
            display: flex;
            align-items: center;
        }
        &__dept-tree {
            color: #303133;
            .org-selector__value {
                & > div {
                    font-weight: 600 !important;
                }
                & > .el-icon--right {
                    margin-left: 2px;
                }
            }
        }
    }
    .demands-action {
        width: 100%;
        margin-bottom: 24px;
        background-color: #fff;
        .query-actions {
            border-bottom: 1px solid #f0f2f5;
        }
    }
    .demand-query-form {
        border: 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s cubic-bezier(0.2, 0, 0.8, 1);
        will-change: transform;
        &__show {
            max-height: 1000px;
        }
    }
    .group_condition_item {
        margin-right: 24px;
        & > .demands-filter{
            &__label {
                margin-right: 4px;
            }
            &__value {
                display: inline-block;
                width: 120px;
                & .el-input__inner {
                    border-radius: 4px;
                }
                & > .jacp-multiselect__body {
                    & > .el-select-dropdown__item {
                        overflow: hidden;
                        text-overflow: clip;
                    }
                }
            }
        }
        &__tags {
            & > .demands-filter__value {
                width: 180px;
            }
        }
    }
}
</style>
