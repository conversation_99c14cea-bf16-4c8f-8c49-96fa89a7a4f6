/**
    业务需求排序相关utils
*/

/* 根据类型确定可排序字段 */
export function getSortTypes(type) {
    let res;
    const submitTime = [{
        id: 'descBySubmitTime',
        name: '按提交时间降序',
    }, {
        id: 'ascBySubmitTime',
        name: '按提交时间升序',
    }];
    const cTime = [{
        id: 'descByCTime',
        name: '按创建时间降序',
    }, {
        id: 'ascByCTime',
        name: '按创建时间升序',
    }];
    const index = [{
        id: 'ascByIndex',
        name: '按辅助优先级由高到低',
    }, {
        id: 'descByIndex',
        name: '按辅助优先级由低到高',
    }];
    const priority = [{
        id: 'ascByPriorityId',
        name: '按业务优先级由高到低',
    }, {
        id: 'descByPriorityId',
        name: '按业务优先级由低到高',
    }];
    switch (type) {
    case 'draft': res = [...cTime, ...priority]; break;
    case 'pending':
    case 'department': res = [...index, ...submitTime, ...priority]; break;
    default: res = [...submitTime, ...priority]; break;
    }
    return res;
}

/* 解析排序id, 得到排序字段及方向 */
export function resolveSortId(sortId) {
    if (!sortId) {
        return {};
    }
    return {
        orderType: sortId.indexOf('asc') === 0 ? 'asc' : 'desc',
        orderField: sortId.replace(/^(?:asc|desc)By(\w)/, (a, b) => b.toLowerCase()),
    };
}

export default {};
