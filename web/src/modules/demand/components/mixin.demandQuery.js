
import i18n from '$platform.i18n';
import pick from 'lodash/pick';
import isPlainObject from 'lodash/isPlainObject';
import flattenDeep from 'lodash/flattenDeep';
import DemandModel from '@/models/demands';
import { DemandGroupModel } from '@/models/demandGroup';
import { space as SpaceModel } from '@/models/teamspace';
import {
    filterCategories,
    allFieldsConfig,
    DEMAND_QUERY_FORM,
} from '$module/constant';

// 以下都是高级筛选里字段的本地枚举值
import { stageMap } from '@/modules/teamspace/constant';
import {
    transformOptionFromId,
    transformOptionFromCode,
    transformOptionFromGroup,
    bindQueryFilterChangedHandler, setQueryFilterOption,
} from '@/components/queryFilter/utils';


const filteredStatus = [2, 13, 3, 4, 5, 9, 10, 18, 20, 21];
const filteredStatusOption = filteredStatus.map(item => ({
    label: i18n.t(`jacp.demandStatus.${item}`),
    value: item,
}));
const stageList = Object.keys(stageMap).reduce((result, id) => {
    result.push({
        value: id,
        label: stageMap[id].name,
    });
    return result;
}, []);


// 目前先不需要缓存option的请求
const queryOptionMap = {
    // local
    stageIdList: () => Promise.resolve(stageList),
    // remote
    apps: () => DemandModel.getInfos(['platformList'])
        .then(([options]) => flattenDeep(options.map(o => o.configList))
            .map((item = {}) => Object.assign(item, {
                value: item.code,
                label: item.name,
            }))),
    priorityId: () => DemandModel.getInfos(['priorityList'])
        .then(([options]) => transformOptionFromId(options)),
    status: () => Promise.resolve(filteredStatusOption),
    tags: () => DemandModel.getInfos(['tagsList'])
        .then(([options]) => transformOptionFromId(options)),
    groupName: () => DemandGroupModel.getGroups()
        .then(transformOptionFromGroup),
    spaceId: () => SpaceModel.getList()
        .then(transformOptionFromId),
    demandTypeIds: () => DemandModel.getInfos(['typeList'])
        .then(([options]) => transformOptionFromCode(options)),
    revenueIdList: () => DemandModel.getInfos(['revenuesList'])
        .then(([options]) => {
            const flatList = [];
            options.forEach((item = {}) => {
                if (item.children) {
                    const deepArr = item.children.map((citem = {}) => ({
                        value: citem.code,
                        label: `${item.name}-${citem.name}`,
                    }));
                    flatList.push(...deepArr);
                } else {
                    flatList.push({ value: item.code, label: item.name });
                }
            });
            return flatList;
        }),
};
const outerFields = {
    keyword: {
        // input() {}, // debounce
        // 仅在输入框失去焦点或用户按下回车时触发
        change(val) {
            this.$refs.compoundQuery.setKeyword(val);
        },
    },
    // 内外部门树保持一致
    orgnazitionId: {
        change(val) {
            // 组件里change的时候emit的value是一个obj，但是queryItem里emit出来的是一个id
            if (isPlainObject(val)) {
                this.currentOrg = val;
            } else {
                this.currentOrg.id = val;
            }
        },
    },
    productId: {
        change(val) {
            // FIXME: 部门树和产品树的第一次change这里都不会被触发，是怎么回事？
            this.product = val;
        },
    },
};
const disabledFields = ['relationUser', 'keyword', 'orgnazitionId', 'direction'];
const setDemandQueryFilterOptionFn = setQueryFilterOption(queryOptionMap);
const queryMixin = {
    data() {
        return {
            filterCategories,
            // TODO: remove
            currentOrg: {},
            product: null,
            allFieldsConfig,
        };
    },
    methods: {
        $_onMounedFilter({ filter = {}, schema = {} }) {
            let targetField;
            // FIXME: 每个字段都要查一遍，这有点纠结
            schema.fields.some((field) => {
                if (filter.column === field.name) {
                    // console.log(`${field.name} 's option`, options);
                    targetField = field;
                    return true;
                }
                return false;
            });
            if (filter.column in queryOptionMap) {
                setDemandQueryFilterOptionFn(filter, targetField);
            }
            if (filter.column in outerFields) {
                bindQueryFilterChangedHandler.call(this, targetField, outerFields);
            }
        },
        $_getDefaultQueryFilter(tab = 'mine') {
            const basic = {
                keyword: '',
                status: [],
                processors: [],
                demanders: [],
            };
            return {
                mine: {
                    relationUser: [pick(this.$store.state.user, 'erp', 'name')],
                    ...basic,
                },
                department: {
                    orgnazitionId: this.$store.state.user.orgCode || '',
                    direction: null,
                    ...basic,
                },
                product: {
                    productId: null,
                    ...basic,
                },
            }[tab];
        },
        // 查询器发生变化的时候更新外围条件
        $_handleQueryChange(query) {
            let filter;

            if (this.dataIndex === 'department') {
                filter = query.find('orgnazitionId') || {};
                this.currentOrg.id = filter.value;
            }
            if (this.dataIndex === 'product') {
                filter = query.find('productId') || {};
                this.product = filter.value;
            }
        },
        // 内外部门树保持一致,通知内部表单同步参数
        $_asyncCurrentOrg($event = {}) {
            this.currentOrg = $event;
            return this.$refs.compoundQuery.setTempFilterValue('orgnazitionId', $event.id);
        },
        // 内外产品线保持一致
        $_asyncProduct($event = {}) {
            this.product = $event;
            return this.$refs.compoundQuery.setTempFilterValue('productId', $event);
        },
    },
    computed: {
        // TODO:拆分页面以后， 这个挪到使用的地方去
        defaultQueryFilter() {
            return this.$_getDefaultQueryFilter(this.dataIndex);
        },
        filterPersonalKey() {
            return DEMAND_QUERY_FORM[this.dataIndex];
        },
        disabledFields() {
            if (this.dataIndex === 'product') {
                return [...disabledFields, 'productId'];
            }
            return disabledFields;
        },
    },
};
export default queryMixin;
