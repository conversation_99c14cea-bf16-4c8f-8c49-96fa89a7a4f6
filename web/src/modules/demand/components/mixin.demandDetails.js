// 未来卡片改造可能有能复用的部分，到时候再拆出去
import Clickoutside from 'element-ui/src/utils/clickoutside';
import Emitter from 'element-ui/src/mixins/emitter';
import FormHelper from '@/modules/demand/models/demandFormHelper';
import DemandModel from '@/models/demands';
import Demand from '@/modules/demand/models/Demand';
import demandValidateRules from './demandValidateRules';
import demandPriorityMark from '@/models/demandPriorityMark';

export default {
    mixins: [Emitter],
    data() {
        const formHelper = new FormHelper();
        const data = new Demand();
        return {
            formHelper,
            data,
            tagsList: [],
            priorityList: [], // 业务优先级 ，需要用show过滤一下是否是有效的
            typeList: [],
            sourceList: [],
            platformList: [],
            systemList: [],
            revenuesList: [],
            revenueDaysList: [
                { id: 7, name: '一周内' },
                { id: 14, name: '两周内' },
                { id: 30, name: '一个月内' },
                { id: 90, name: '一季度内' },
                { id: 180, name: '半年内' },
            ],
            riskList: [],
            channelRiskList: [
                { id: 1, code: 1, name: '是' },
                { id: 0, code: 0, name: '否' },
            ],
            // 改成继承，否则获取了新的rules以后会直接修改掉demandValidateRules的原始值
            rules: { ...demandValidateRules },
            // TODO: 这个好像没有其他需要复用的地方啊
            // showReceiverList: false,
            receiverWhiteList: [],
            pmpProjectOption: [{
                name: '否',
                id: 0,
                code: 0,
            }, {
                name: '是',
                id: 1,
                code: 1,
            }],
            showDemandPriorityScore: undefined,
        };
    },
    directives: { Clickoutside },
    // 要加载的基础数据
    basicInfos: ['platformList', 'typeList', 'sourceList', 'priorityList', 'riskList', 'revenuesList'],
    computed: {
        validPriorityList() {
            return this.priorityList.filter(o => o.show);
        },
        showSystemRelation() {
            // 目前只有受理人部门为 物流集团-技术发展部及子部门时显示
            return (this.data.receivers || [])
                .some(user => user.orgTierCode.includes('/00000000/00029430/00007721'));
        },
    },
    methods: {
        handleParentScroll() { },
        // 加载基础数据
        getBasicInfos() {
            const infos = this.$options.basicInfos;
            return DemandModel.getInfos(infos).then((res) => {
                infos.forEach((key, i) => {
                    if (typeof key !== 'string') {
                        this[key.name] = res[i];
                    } else {
                        this[key] = res[i];
                    }
                });
            });
        },
        // 个性化校验规则设置
        getOrgFieldsRules() {
            return this.formHelper.mergeRules(this, this.data)
                .then(({ newRules = [] }) => {
                    this.$emit('rules:updated', newRules);
                });
        },
        // 此段代码无用
        openCF() {
        },
        selectSystemRelation(query) {
            if (query) {
                // 受理人部门
                let fullOrgId = '';
                if (this.data.receivers && this.data.receivers.length > 0) {
                    fullOrgId = this.data.receivers[0].orgCode || this.data.receivers[0].orgTierCode;
                }
                DemandModel.getRemoteSystems({ name: query, fullOrgId }).then((data) => {
                    this.systemList = data;
                });
            } else {
                this.systemList = [];
            }
        },
        checkDemandPriorityScore(orgId) {
            if (!orgId) {
                this.showDemandPriorityScore = false;
                return;
            }
            // 需求优先级评分
            if (orgId.startsWith('/00000000/00013807')) {
                // 是否存在模板
                demandPriorityMark.hasTemplate(orgId).then((data) => {
                    this.showDemandPriorityScore = data;
                });
            }
        },
    },
};
