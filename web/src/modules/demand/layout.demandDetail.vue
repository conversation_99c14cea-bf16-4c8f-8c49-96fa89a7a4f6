<template>
    <div
        class="latest-detail__wrapper"
        v-if="data"
    >
        <div class="latest-detail__header">
            <el-tooltip
                effect="dark"
                placement="top"
                v-if="data.system !== 'jacp'"
                :content="`需求来自${data.system}系统， 不可编辑`"
                :open-delay="300"
            >
                <i
                    class="j-other latest-detail-briefitem"
                />
            </el-tooltip>
            <span
                v-if="data.urgent"
                class="latest-detail-briefitem latest-detail-briefitem--urgent"
            >紧急</span>
            <span
                class="latest-detail-briefitem"
                :style="{
                    color: getDemandStatusColor(data.status)
                }"
                title="需求状态"
            >{{ data.status|jacp-status-text }}</span>
            <span
                class="latest-detail-briefitem"
                v-if="sourceList.length && data.demandSourceId !== undefined"
                :title="'source' | fieldText"
            >
                {{ sourceList.find(o => o.code === data.demandSourceId).name }}
            </span>
            <span
                class="latest-detail-briefitem"
                title="需求编号"
            >{{ data.demandCode }}</span>
            <jacp-icon
                active
                v-j-copy
                title="复制"
                :data-clipboard-text="data.demandCode"
                name="el-icon-copy-document"
            />
            <span
                v-if="data.demandId && data.demandId !== -1"
                class="latest-detail-briefitem"
                title="团队空间"
            >团队空间：
                <router-link
                    v-if="devInsight && devInsight.length"
                    :to="toCardDetail"
                    target="_blank"
                >
                    <span>{{ devInsight[0].spaceName }}</span>
                </router-link>
                <span v-else>暂无</span>
            </span>
            <el-popover
                v-if="involvedGroupList.length"
                placement="bottom"
                trigger="click"
                v-model="involvedGroupListVisible"
            >
                <!-- 本来el-popover是支持点击外面就隐藏popper的，
          但是由于offCanvas stop了click事件，事件无法到达document，
          导致poppver里接不到这个click，也就无法隐藏了，需要手动判断一下是否点击外侧
          也可以设置 :popper-append-to-body="false" 来解决-->
                <ul
                    class="latest-detail-involvedlist"
                    v-clickoutside="() => involvedGroupListVisible = false"
                >
                    <li
                        class="latest-detail-involveditem"
                        v-for="item in involvedGroupList"
                        :key="item.groupId"
                    >
                        <span
                            v-show="item.category === 2 || item.cooperatorCount > 0"
                            class="demand-group__text--emphasize"
                        >协</span>
                        <small>{{ item.groupName }}</small>
                    </li>
                </ul>
                <span
                    slot="reference"
                    :class="{
                        'latest-detail-briefitem': true,
                        'j-hover-highlight': involvedGroupList.length,
                    }"
                    style="transform: translateY(-2px);"
                    title="已加入分组"
                >已加入分组</span>
            </el-popover>
            <div class="latest-detail__close">
                <el-dropdown
                    @command="menuAction"
                >
                    <el-tooltip
                        content="开始沟通需求后，才可以创建任务"
                        :disabled="showManageTask"
                    >
                        <jacp-icon
                            name="el-icon-more"
                            size="14"
                            style="transform: translate(-4px, 0)"
                            active
                        />
                    </el-tooltip>
                    <el-dropdown-menu
                        slot="dropdown"
                        v-if="showManageTask"
                    >
                        <el-dropdown-item command="task">管理任务</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <jacp-icon
                    name="el-icon-close"
                    active
                    @click.native.stop="handleHide"
                />
                <!-- <i
                    class="el-icon-close latest-detail__close"
                    @click.stop="handleHide"
                /> -->
            </div>
        </div>
        <!-- 悬浮的title -->
        <!-- <transition name="fade"> -->
        <div
            class="latest-detail__header--fixed"
            v-show="fixedHeaderVisible"
            @click="scrollToTop"
        >
            <div><span>{{ data.name }}</span></div>
            <i
                class="el-icon-close latest-detail__close"
                @click.stop="handleHide"
            />
        </div>
        <!-- </transition> -->
        <!-- Main content Start -->
        <el-form
            class="j-form"
            ref="formMain"
            :model="data"
            :rules="rules"
            :validate-on-rule-change="false"
        >
            <el-row class="latest-detail__main">
                <el-col
                    class="latest-detail__main--left"
                    :span="16"
                >
                    <section class="text-placeholder">
                        <jacp-form-input
                            ref="titleEl"
                            v-model="data.name"
                            class="latest-detail-title"
                            type="textarea"
                            autosize
                            confirm
                            resize="none"
                            placeholder="可输入3 - 80个字符"
                            :rules="rules.name"
                            :disabled="!data.proposerEditable"
                            @change="update('name', $event)"
                        />
                        <!-- 虽然以下是必填，但是不显示红色星号 -->
                        <div class="latest-detail__block--inline">
                            <!-- 业务优先级 -->
                            <el-form-item :label="`${fieldText.priority}:`">
                                <jacp-simple-select
                                    v-model="data.priorityId"
                                    :options="priorityEdit ? validPriorityList : priorityList"
                                    :disabled="!data.proposerEditable"
                                    @change="update('priorityId', ...arguments)"
                                    @visible-change="priorityEdit = $event"
                                />
                            </el-form-item>
                            <!-- 风险 -->
                            <el-form-item :label="`${fieldText.risk}:`">
                                <jacp-simple-select
                                    v-model="data.riskId"
                                    :options="riskList"
                                    :disabled="!data.editable"
                                    @change="update('riskId', ...arguments)"
                                />
                            </el-form-item>
                            <!-- 需求类型 -->
                            <el-form-item :label="`${fieldText.type}:`">
                                <jacp-simple-select
                                    v-model="data.demandTypeId"
                                    :options="typeList"
                                    :disabled="!data.editable"
                                    @change="update('demandTypeId', ...arguments)"
                                />
                            </el-form-item>
                        </div>
                    </section>
                    <!-- 需求提出人 及 需求详情 -->
                    <section
                        :class="['latest-detail__desc latest-detail__block j-mgt16', {
                            'latest-detail--hasConfidential': data.confidential
                        }]"
                    >
                        <div class="latest-detail-proposer">
                            <jacp-erp
                                :class="['form-users__item']"
                                :disable-timline="$utils.isCurrentUser(data.proposer)"
                                avatar
                                :data="data.proposer"
                            />
                            <div>
                                <h5>
                                    {{ 'proposer' | fieldText }}: {{ data.proposer ? data.proposer.name : '' }}
                                </h5>
                                <p class="latest-detail__block--inline text-placeholder">
                                    <el-form-item :label="`${fieldText.submitTime}:`">
                                        <span v-if="data.submitTime">{{ data.submitTime | jacp-local-time }}</span>
                                        <span
                                            v-else
                                            class="jacp-form--nodata"
                                        >无数据</span>
                                    </el-form-item>
                                    <el-form-item
                                        class="j-mgl16"
                                        props="expectedReleaseDate"
                                        :label="`${fieldText.expectedReleaseDate}:`"
                                    >
                                        <!-- 只有需求提交人和需求人能修改期望上线日期，当前操作人和受理人不可修改 -->
                                        <jacp-simple-date-picker
                                            v-model="data.expectedReleaseDate"
                                            placeholder="请选择"
                                            :disabled="(!data.demanderEditable && !data.proposerEditable)"
                                            @change="update('expectedReleaseDate', $event)"
                                            :picker-options="expReleaseDateOptions"
                                            type="date"
                                        />
                                        <span
                                            class="jacp-form--nodata"
                                            v-if="!data.expectedReleaseDate
                                                && (!data.demanderEditable && !data.proposerEditable)"
                                        >无数据</span>
                                    </el-form-item>
                                </p>
                            </div>
                        </div>
                        <el-form-item
                            ref="richEditorFormEl"
                            prop="richText"
                        >
                            <quill-editor-plus
                                ref="richEditorEl"
                                v-model="data.richText"
                                :auto-save-key="demand.id"
                                @confirm="updateRichText"
                                @cancel="() => data.loadRichTextHtml()"
                                :placeholder="$t('jacp.demandFields.descriptionPlaceholder')"
                                unfold-text="展开需求描述"
                                fold-text="收起"
                                :disabled="!data.proposerEditable"
                                :disabled-confirm="$refs.richEditorEl && $refs.richEditorEl.error"
                                @change="checkDescription"
                            >
                                <div
                                    v-if="data.name"
                                    v-text="data.name"
                                    slot="full-title"
                                    class="ql-editor-plus__fulltitle"
                                />
                            </quill-editor-plus>
                        </el-form-item>
                    </section>
                    <!-- 受理人+需求人 -->
                    <section class="latest-detail__block latest-detail__block--inline j-mgt24">
                        <div class="latest-detail-receivers">
                            <h5>{{ 'receivers' | fieldText }}</h5>
                            <jacp-erp
                                class="jacp-form__value"
                                avatar
                                v-for="user in data.receivers"
                                :key="user.erp"
                                :data="user"
                                :disable-timline="$utils.isCurrentUser(user)"
                            />
                            <jacp-erp
                                class="jacp-form__value"
                                avatar
                                nodata
                                v-if="!data.receivers || !data.receivers.length"
                            />
                        </div>
                        <div class="latest-detail-demanders">
                            <h5>{{ 'demanders' | fieldText }}</h5>
                            <div class="latest-detail-demanders__wrapper">
                                <jacp-erp
                                    class="jacp-form__value"
                                    avatar
                                    v-for="user in data.demanders"
                                    :key="user.erp"
                                    :data="user"
                                    :disable-timline="$utils.isCurrentUser(user)"
                                />
                            </div>
                        </div>
                    </section>
                    <HR />
                    <!-- 成本分摊 -->
                    <section
                        class="latest-detail__block j-mgt24"
                        v-if="data.parentDemandId === -1"
                    >
                        <h5>{{ 'apportions' | fieldText }}</h5>
                        <apportion-detail :data="apportionTasks" />
                    </section>
                    <!-- 收益分类 -->
                    <section
                        class="latest-detail__block j-mgt24"
                        v-if="data.revenueId !== undefined"
                    >
                        <h5>{{ 'revenues' | fieldText }}</h5>
                        <p class="latest-detail-revenues">
                            {{ data.revenue ? (data.revenueId === -1 ?
                                `${data.revenue.name}-${data.revenueOther}`
                                : data.revenue.name) : '' }}
                        </p>
                    </section>
                    <section class="latest-detail__block j-mgt24">
                        <h5>{{ 'roi' | fieldText }}</h5>
                        <!-- ROI -->
                        <el-form-item
                            class="latest-detail__confirminput"
                            prop="roi"
                        >
                            <!-- TODO: 目前show-word-limit不能使，要升到element-ui 新版才提供 -->
                            <jacp-form-input
                                v-model="data.roi"
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 6 }"
                                mode="background"
                                resize="none"
                                placeholder="【预期收益】【上线后预计收益时间】"
                                :disabled="!data.proposerEditable"
                                maxlength="1000"
                                :max-height="96"
                                show-word-limit
                                confirm
                                :no-data-text="data.proposerEditable ? '暂无收益描述，请填写相关信息。' : '暂无收益描述'"
                                @change="update('roi', $event)"
                            />
                        </el-form-item>
                    </section>
                    <!-- 父子关系 -->
                    <section
                        class="latest-detail__block j-mgt24"
                        v-if="parentAndChildren.length"
                    >
                        <h5>{{ 'parentAndChildren' | fieldText }}</h5>
                        <div class="latest-detail-relatives">
                            <demand-relatives
                                class="comp-demand-detail__relatives__item"
                                :table-data="parentAndChildren"
                            />
                        </div>
                    </section>
                </el-col>
                <el-col
                    class="latest-detail__main--right"
                    :span="8"
                >
                    <section v-if="data.workload">
                        <jacp-noticeboard
                            label="实际工时"
                            style="margin-right: 40px;"
                            :value="`${data.workload.actualHours || 0}h`"
                        />
                        <jacp-noticeboard
                            label="计划工时"
                            :value="`${data.workload.plannedHours || 0}h`"
                        />
                    </section>
                    <section
                        class="latest-detail__block j-mgt24"
                        v-if="data.processor && data.processor.erp"
                    >
                        <h5>{{ 'processor' | fieldText }}</h5>
                        <jacp-erp
                            class="jacp-form__value"
                            avatar
                            :data="data.processor"
                            :disable-timline="$utils.isCurrentUser(data.processor)"
                        />
                    </section>
                    <section class="latest-detail__block j-mgt20">
                        <h5>{{ 'isNoProject' | fieldText }}</h5>
                        <jacp-form-select
                            style="width: 100%;"
                            v-model="data.pmpProjectApproval"
                            :options="pmpProjectOption"
                            :disabled="!!data.projectLock || (!data.editable && !data.proposerEditable)"
                            @change="update('pmpProjectApproval', ...arguments)"
                        />
                    </section>
                    <!-- 关联项目 -->
                    <section
                        class="latest-detail__block j-mgt24"
                        v-if="data.id"
                    >
                        <h5>{{ 'projectRelation' | fieldText }}</h5>
                        <related-projects
                            v-model="data.pmpProjectId"
                            :tips="data.projectLock ? '请先联系项目经理去项目管理或pmp进行需求解绑，再修改关联项目' : ''"
                            :entity="data"
                            :request-parameters="{
                                demandId: data.id,
                            }"
                            :filterable="true"
                            :clearable="!rules.projectId"
                            :disabled="!!data.projectLock || (!data.editable && !data.proposerEditable)"
                            @change="updateForProject('projectId', ...arguments)"
                        />
                    </section>
                    <!-- 关联产品 -->
                    <section
                        class="latest-detail__block j-mgt24"
                        v-if="prdmAvailable"
                    >
                        <h5>
                            {{ 'productRelation' | fieldText }}
                        </h5>
                        <jacp-prdm-relate-to-product
                            v-model="data.product"
                            :disabled="!data.editable && !data.proposerEditable"
                            @change="($event) => update('product', $event, $event, '关联产品')"
                            :pop-width="892"
                        />
                    </section>
                    <section
                        class="latest-detail__block j-mgt16"
                        v-if="isActivityDemandType"
                    >
                        <h5>{{ 'expectedOfflineDate' | fieldText }}</h5>
                        <el-date-picker
                            style="width: 100%"
                            v-model="data.expectedOfflineDate"
                            placeholder="请选择"
                            :disabled="!editExpectedOfflineDate"
                            @change="update('expectedOfflineDate', $event)"
                            :picker-options="expOfflineDateOptions"
                            type="date"
                        />
                    </section>
                    <section class="latest-detail__block latest-detail-followers j-mgt24">
                        <h5>
                            {{ 'followers' | fieldText }}
                            <span
                                v-if="data.followers.length > maxFollowersLength"
                                class="latest-detail__title--extra"
                                @click="expandAllFollowers = !expandAllFollowers"
                            >
                                {{ expandAllFollowers ? '收起' : '展开全部' }}
                                <i :class="[expandAllFollowers ? 'el-icon-arrow-up': 'el-icon-arrow-down']" />
                            </span>
                        </h5>

                        <form-users-group
                            class="latest-detail-followers__editor"
                            v-model="data.followers"
                            placeholder="请选择"
                            :max-visible-item-length="maxFollowersLength"
                            :expand-all="expandAllFollowers"
                            :no-timline-users="[$store.state.user]"
                            :min-count="rules.followers && rules.followers.some(o => o.required) ? 1 : 0"
                            :disabled="data.system !== 'jacp'"
                            storage-key="demand_followers"
                            @on-add="updateFieldIndividual('followers', $event)"
                            @on-delete="updateFieldIndividual('followers', $event, 1)"
                        />
                    </section>

                    <section
                        class="latest-detail__block j-mgt16"
                    >
                        <!-- 需求来源部门 -->
                        <h5>{{ 'demandSourceOrg' | fieldText }}</h5>
                        <el-tooltip
                            :content="data.demandSourceOrg.orgTierName"
                            v-if="data.demandSourceOrg && data.demandSourceOrg.orgTierName"
                        >
                            <span
                                class="org-selector__content j-mgt4"
                                style="display: inline-block;"
                            >
                                {{ data.demandSourceOrg.orgTierName.split('-').pop() }}
                            </span>
                        </el-tooltip>
                        <span
                            v-else
                            class="j-nodata"
                        >
                            暂无
                        </span>
                    </section>

                    <!-- 业务需求优先级评分 -->
                    <section
                        class="latest-detail__block j-mgt24"
                        v-if="data.priorityScore >= 0 || showDemandPriorityScore"
                    >
                        <h5>{{ 'demandMark' | fieldText }}</h5>
                        <el-form-item prop="priorityScore">
                            <demand-priority-mark
                                ref="demandPriorityMark"
                                :demand="data"
                                @doMark="changePriorityScore(...arguments)"
                            />
                        </el-form-item>
                    </section>

                    <section
                        class="latest-detail__block j-mgt16"
                        v-if="showSystemRelation"
                    >
                        <h5>{{ 'systemRelation' | fieldText }}</h5>
                        <demand-related-systems
                            ref="systemRelation"
                            v-model="data.systemInfoList"
                            multiple
                            reserve-keyword
                            filterable
                            remote
                            value-key="id"
                            :remote-method="selectSystemRelation"
                            :options="systemList"
                            @blur="updateSystemRelation"
                        />
                    </section>
                    <section
                        class="latest-detail__block j-mgt20"
                    >
                        <h5>{{ 'platform' | fieldText }}</h5>
                        <!-- 涉及研发平台 :disabled="!data.editable" -->
                        <dev-platforms
                            :list="platformList"
                            :disabled="!data.editable && !data.proposerEditable"
                            v-model="data.platformRelations"
                            @change="update('platform', $event)"
                        />
                    </section>
                    <section
                        class="latest-detail__block j-mgt16"
                        v-show="hasApp"
                    >
                        <h5>
                            {{ 'channelRisk' | fieldText }}
                            <jacp-icon
                                style="margin-left: 4px;color: #c5c5c5;cursor: pointer"
                                name="icon-warning2"
                                @click.native="openCF"
                                :size="16"
                            />
                        </h5>
                        <jacp-form-select
                            style="width: 100%;"
                            :disabled="!data.editable && !data.proposerEditable"
                            v-model="data.channelRisk"
                            :options="channelRiskList"
                            @change="update('channelRisk', ...arguments)"
                            ref="channelRiskSelect"
                        />
                        <span
                            class="channelRiskTip"
                            v-show="showTip"
                        >
                            1.务必与预审团队、版本经理评审需求
                            <br>
                            2.618与双十一期间谨慎规划
                        </span>
                    </section>
                    <section class="latest-detail__block j-mgt20">
                        <h5>{{ 'tag' | fieldText }}</h5>
                        <custom-tag-group
                            v-model="data.tags"
                            :rules="rules.tags"
                            tag-editable
                            :suggestions="tagsList"
                            @on-blur="updateFieldIndividual('tags', $event)"
                            @on-delete="updateFieldIndividual('tags', $event, 1)"
                            :min-count="rules.tags && rules.tags.some(o => o.required) ? 1 : 0"
                            editable
                        />
                    </section>
                    <section class="latest-detail__block j-mgt24">
                        <h5 style="position: relative">
                            {{ 'attachments' | fieldText }}
                            <el-tooltip placement="top">
                                <div slot="content">
                                    文件名只包含字母、数字、中文、“/”、“.”、“-”、“ ”
                                </div>
                                <jacp-icon
                                    style="position: absolute;left: 32px;top: 0;color: #c5c5c5;"
                                    name="icon-help_outline"
                                    :size="14"
                                />
                            </el-tooltip>
                        </h5>
                        <jacp-form-upload
                            v-model="data.attachments"
                            :reload-params="{
                                attachmentType: 'demand',
                                entityId: data.id,
                            }"
                            :can-reload="false"
                            :disabled="!data.editable && !data.proposerEditable"
                            :min-count="rules.attachments && rules.attachments.some(o => o.required) ? 1 : 0"
                            @on-delete="updateFieldIndividual('attachments', $event, 1)"
                            @on-uploaded="updateFieldIndividual('attachments', $event)"
                        />
                    </section>
                    <section class="latest-detail__block j-mgt16">
                        <h5>{{ 'links' | fieldText }}</h5>
                        <jacp-form-link-group
                            v-model="data.links"
                            :disabled="!data.editable && !data.proposerEditable"
                            @on-add="updateFieldIndividual('links', $event)"
                            @on-delete="updateFieldIndividual('links', $event, 1)"
                        />
                    </section>
                    <!-- joyspace 云文档 -->
                    <section
                        class="latest-detail__block j-mgt16"
                        v-if="joyspaceLinksVisible"
                    >
                        <h5>云文档</h5>
                        <LocalDemandJoyspaceLinks
                            title="云文档"
                            :demand="data"
                            :disabled="!data.editable && !data.proposerEditable"
                            :on-add="(links) => addLinks(links, data).then((links = []) => {
                                data.joySpaceLinks = links;
                            })"
                            :on-delete="(link) => $confirm(`确定移除云文档「${link.attachmentName}」？`)
                                .then(() => removeLink(link, data))"
                        />
                    </section>
                    <!-- 自动验收通过天数 -->
                    <section
                        class="latest-detail__block j-mgt16"
                        v-if="data.autoAcceptanceDays || editAutoAcceptanceDays"
                    >
                        <el-form-item
                            prop="autoAcceptanceDays"
                            :rules="rules.autoAcceptanceDays"
                        >
                            <h5>
                                {{ 'autoAcceptanceDays' | fieldText }}
                                <el-tooltip placement="top">
                                    <div slot="content">
                                        需求提交验收后，开始倒计时，倒计时到达需求填写的时效后，需求⾃动验收通过。
                                    </div>
                                    <jacp-icon
                                        style="margin-left: 4px;color: #c5c5c5;"
                                        :size="16"
                                    />
                                </el-tooltip>
                            </h5>
                            <span class="latest-detail__text">提交验收</span>
                            <el-input-number
                                v-model="data.autoAcceptanceDays"
                                style="width: 60px"
                                :disabled="data.status === 20 || !$utils.isCurrentUser(data.proposer)"
                                :controls="false"
                                step-strictly
                                :min="1"
                                :max="21"
                                @focus="focusAutoAcceptanceDays"
                            />
                            <span class="latest-detail__text">天后自动验收通过</span>
                            <form-button-group
                                v-if="editAutoAcceptanceDays"
                                @confirm="updateAutoAcceptanceDays"
                                @cancel="cancelEditAutoAcceptanceDays"
                            />
                        </el-form-item>
                    </section>

                    <!-- 自定义表单 -->
                    <dynamic-form
                        class="latest-detail-dynamic-form j-form--small"
                        ref="dynamicFormIns"
                        v-if="formHelper.dynamicFormSchema"
                        v-model="data.extendedFields"
                        v-bind="formHelper.dynamicFormSchema"
                        :disabled="!data.editable && !data.proposerEditable"
                        :rules="formHelper.dynamicFormSchema.rules"
                    />
                </el-col>
            </el-row>
        </el-form>
        <!-- Main content End -->
        <!-- 备注历史等 -->
        <el-row
            class="latest-detail__middle"
            :style="{
                'marginBottom': remarkInputVisible ? '201px' : '62px',
            }"
        >
            <demand-detail-tabs
                ref="demandDetailTabsIns"
                :demand-id="demand.id"
                :demand="data"
                :disable-demand-milestone="!data.editable"
            />
        </el-row>
        <transition name="fade">
            <el-row class="latest-detail__footer">
                <transition name="fade">
                    <record-remark-input
                        v-model="remark"
                        :visible.sync="remarkInputVisible"
                        :input-suggestions="data.demandRelevantPeople"
                        @submit="addRemark"
                    />
                </transition>
                <demand-actions
                    class="latest-detail__actions"
                    :actions="actions"
                    :action-handle="handleAction"
                    ref="demandActionsIns"
                >
                    <template slot="extra">
                        <jacp-button
                            @click.native="$_joinInGroup({
                                reference: $event.target,
                                demandId: data.id,
                                onJoinIn: handleGroupAction.bind(this),
                            })"
                        >
                            加入分组
                        </jacp-button>
                        <jacp-button
                            v-if="$route.query.groupId && isMyGroup && showDetachBtn"
                            @click.native="$_detachFromGroup({
                                demandId: data.id,
                                groupId: currentGroupId,
                                onDetach: handleGroupAction.bind(this),
                            })"
                        >
                            移出分组
                        </jacp-button>
                        <jacp-button
                            @click.native="remarkInputVisible = true"
                            v-if="data.system === 'jacp'"
                        >
                            备注
                        </jacp-button>
                        <!-- 此段代码无用 -->
                        <!-- <el-dropdown
                            @command="handleCommand"
                            style="margin-left:10px"
                            v-if="showNewProject"
                        >
                            <el-button>
                                一键立项
                                <i
                                    class="el-icon-arrow-down el-icon--right"
                                />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="pre-new-project">
                                    预立项
                                </el-dropdown-item>
                                <el-dropdown-item command="new-project">
                                    立项
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown> -->
                    </template>
                </demand-actions>
            </el-row>
        </transition>
        <demand-task-manage
            v-if="taskManageVisible"
            ref="demandTaskManageIns"
            :demand-id="data.id"
            :project-id="data.projectId"
            @close-task-manage="taskManageVisible = false"
        />
    </div>
</template>

<script type="text/javascript">
import { findIndex } from 'lodash';
// import { isAnyPartOfElementInViewport, scrollElementIntoViewport } from '@/plugins/utils';
import DemandModel from '@/models/demands';
// import PmpModel from '@/models/pmp';
import mixinLayoutDetail from '@/mixins/mixin.layoutDetail';
import { Apportion } from '@/models/apportion';
import { getDemandStatusColor } from './components/utils.demandStatus';
import mixinsDemandDetails from './components/mixin.demandDetails';
import mixinActions from './components/mixin.actions';
import mixinFieldText from './components/mixin.fieldText';
import mixinGroup from './components/mixin.demandGroup';
import DemandActions from './components/demandActions';
import DemandDetailTabs from './components/demandDetailTabs';
import DevPlatforms from './components/devPlatforms';
import ApportionDetail from './components/apportionDetail';
import DemandRelatives from './components/demandRelatives';
import DemandRelatedSystems from './components/demandRelatedSystems';
import { validate, isCurrentUser, isObject } from '@/plugins/utils';
import FormHelper from '@/modules/demand/models/demandFormHelper';
import DemandPriorityMark from './components/demandPriorityMark';
import DemandTaskManage from './components/demandTaskManage';
import LocalDemandJoyspaceLinks from '$module/components/extensionPoint/demandJoyspaceLinks';
import http from '@/plugins/http';

import { removeLink, addLinks } from '@/modules/demand/models/joyspaceService';

const getFieldLabel = (key, fieldText) => {
    const langKey = {
        riskId: 'risk',
        priorityId: 'priority',
        demandTypeId: 'type',
        projectId: 'projectRelation',
        pmpProjectApproval: 'isNoProject',
        tags: 'tag',
    }[key] || key;
    return fieldText[langKey];
};
export default {
    name: 'JacpLayoutDemandDetail',
    componentName: 'JacpLayoutDetail', // emitter里使用的是componentName来查找的，非name
    mixins: [mixinActions, mixinFieldText, mixinGroup, mixinsDemandDetails, mixinLayoutDetail],
    components: {
        DemandActions,
        DemandDetailTabs,
        DevPlatforms, // 研发平台
        ApportionDetail,
        DemandRelatives,
        DemandRelatedSystems,
        DemandPriorityMark,
        DemandTaskManage,
        LocalDemandJoyspaceLinks,
    },
    props: {
        demand: {
            type: Object,
            default: () => ({}),
        },
    },
    // 防误点：离开detail前，有二次确认按钮的如果在编辑状态，就confirm一下
    // TODO: 还要在route里增加个字段，需要优化一下
    // TODO: 和cardDetail又有重合的部分了，下次重构需要考虑一下
    created() {
        const { event: $event } = this;
        this.formDoubleCheckCache = [];
        $event.$on('double-check:start', (uuid) => {
            this.$nextTick(() => {
                // 防止在渲染button前就执行了
                if (this.$el.querySelector(`#${uuid}`)) {
                    this.formDoubleCheckCache.push(uuid);
                }
            });
        });
        $event.$on('double-check:end', (uuid) => {
            this.formDoubleCheckCache.splice(this.formDoubleCheckCache.indexOf(uuid), 1);
        });
    },
    beforeRouteLeave(to, from, next) {
        if (!this.formDoubleCheckCache.length) {
            next();
            return;
        }
        this.$confirm('需求正在编辑，确认离开么？', '提示', {
            type: 'warning',
            cancelButtonText: '再想想',
            confirmButtonText: '离开',
        })
            .then(() => next())
            .catch(() => next(false));
    },
    async mounted() {
        const { id } = this.demand;
        this.loadDetail(id).then(({ systemInfoList = [] }) => {
            // 加载基础数据
            this.getBasicInfos();
            this.$_fetchInvolvedGroupList(id);
            DemandModel.getInfos([{
                name: 'parentAndChildren',
                params: {
                    id,
                },
            }, {
                name: 'devInsight',
                params: {
                    demandId: id,
                },
            }]).then(([parentAndChildren, devInsight]) => {
                this.parentAndChildren = parentAndChildren;
                this.devInsight = devInsight;
            });
            Apportion.getTaskDetail(id).then((data) => {
                this.apportionTasks = Object.assign({}, this.apportionTasks, {
                    records: data,
                });
            });
            DemandModel.getTagsList().then((res) => {
                this.tagsList = res;
            });
            this.initSystemInfo(systemInfoList);
            if (this.data.demandSourceOrg) {
                this.checkDemandPriorityScore(this.data.demandSourceOrg.orgTierCode);
            }
        });
        if (this.$route.query.cancelAutoSubmitAcceptance) {
            http.get(`v1/setting/worker/cancel/demand?demandId=${this.demand.id}`).then(() => {
                this.$message('已经取消自动发起验收。');
            });
        }
        // }
    },
    data() {
        const doUpdate = this.update.bind(this);
        const formHelper = new FormHelper({
            // 目前接口不支持单个字段进行更新
            onDynamicFormUpdated: async (fieldInstance, value) => {
                if (this.$refs.dynamicFormIns) {
                    // update事件发生在extendedFields变化之前，oh no
                    this.data.extendedFields[fieldInstance.name] = value;
                    const dynamicFormValid = await this.$refs.dynamicFormIns.validate();
                    if (dynamicFormValid) {
                        doUpdate('extendedFields', this.data.extendedFields);
                    }
                }
            },
        });
        return {
            event: this.$initEvent(),
            formHelper,
            riskList: [],
            // projectsList: [],
            priorityEdit: false,
            parentAndChildren: [],
            showDetachBtn: true,
            apportionTasks: {},
            // fixedHeaderVisible: false,
            involvedGroupListVisible: false,
            // remarkInputVisible: false,
            // 关注人默认最多展示两行， 超出两行，增加「展开全部」「收起」 的按钮 添加的按钮始终在末尾 hover头像时右上角增加删除的 红色按钮，点击删除可直接 删除该关注人
            maxFollowersLength: 7,
            expandAllFollowers: false,
            // 添加备注
            // remark: '',
            pmpProjectOption: [{
                name: '否',
                id: 0,
            }, {
                name: '是',
                id: 1,
            }],
            expReleaseDateOptions: {
                disabledDate: (time) => {
                    const today = new Date(this.data.submitTime);
                    return time < new Date(today.toLocaleDateString());
                },
            },
            expOfflineDateOptions: {
                disabledDate: (time) => {
                    if (this.data.expectedReleaseDate !== undefined) {
                        return time < new Date(new Date(this.data.expectedReleaseDate).toLocaleDateString());
                    }
                    const today = new Date(this.data.submitTime);
                    return time < new Date(today.toLocaleDateString());
                },
            },
            editAutoAcceptanceDays: false,
            oldAutoAcceptanceDays: undefined,
            taskManageVisible: false,
        };
    },
    computed: {
        prdmAvailable() { return !!this.$store.getters['root/getActiveModuleByCode']('prdm'); },
        joyspaceModuleAvailable() { return !!this.$store.getters['root/getActiveModuleByCode']('joyspace'); },
        joyspaceLinksVisible() {
            if (this.data.joySpaceLinks.length) return true;
            if (this.joyspaceModuleAvailable) return this.data.editable || this.data.proposerEditable;
            return false;
        },
        hasApp() {
            return (this.data.platformRelations || []).some(x => x.platformName === 'APP');
        },
        showTip() {
            return this.hasApp && this.$refs.channelRiskSelect.currentValue === 1;
        },
        // 此段代码无用
        // showNewProject() {
        //     return this.data.pmpProjectApproval === 1
        //     && [3, 5, 12].includes(this.data.status)
        //     && !this.data.pmpProjectId
        //     && this.data.demandRelevantPeople.some(u => isCurrentUser(u));
        // },
        isActivityDemandType() {
            return this.data.demandTypeId === 8;
        },
        editExpectedOfflineDate() {
            return this.data.status !== 20;
        },
        showManageTask() {
            return ![1, 2, 21].includes(this.data.status);
        },
        toCardDetail() {
            const card = this.devInsight[0];
            return card.spaceMode === 2 ? {
                name: 'teamspaceCardsList',
                params: {
                    spaceKey: card.spaceKey,
                },
                query: {
                    cardId: card.cardId,
                    sprintId: card.sprintId,
                },
            } : {
                name: 'generalCardDetail',
                params: {
                    spaceKey: card.spaceKey,
                    cardId: card.cardId,
                },
            };
        },
    },
    methods: {
        removeLink,
        addLinks,
        // 富文本改成联合校验
        checkDescription({ text } = {}) {
            const { richEditorFormEl } = this.$refs;
            if (typeof text !== 'undefined') {
                this.data.description = text;
            }
            return new Promise((resolve, reject) => {
                validate(this.data, this.rules.richTextAndDescription).then(() => {
                    richEditorFormEl.validateState = '';
                    this.$refs.richEditorEl.error = false;
                    resolve();
                }, ([err]) => {
                    richEditorFormEl.validateState = 'error';
                    richEditorFormEl.validateMessage = err;
                    this.$refs.richEditorEl.error = true;
                    reject();
                });
            });
        },
        getDemandStatusColor,
        updateForProject(key, value, attachData = {}) {
            if (attachData.projectId) {
                this.update(key, attachData.projectId, attachData);
            } else {
                if (this.rules.projectId && this.rules.projectId.some(r => r.required)) {
                    this.$message.error('请填写关联项目');
                    return;
                }
                this.update(key, '', attachData);
            }
        },
        update(key, value, attachData = {}, fieldLabel) { // jacp-select组件attachData为value对应的完整对象
            return DemandModel.update(this.demand.id, {
                fieldKey: key,
                fieldlabel: fieldLabel || getFieldLabel(key, this.fieldText),
                fieldValue: value === undefined ? this.data[key] : value,
                fieldValueDescription: attachData.name,
            }).then(() => {
                this.$set(this.data, key, value);
                if (attachData) {
                    const attachDataKey = `${key}Name`;
                    this.$set(this.data, attachDataKey, attachData);
                }
                this.clearExpectedReleaseDate();
                this.triggerOuterUpdate();
                this.$message.success('保存成功!');
                this.$emit('refresh-list');
            }, this.updateFailed);
        },
        updateRichText({ text, html }) {
            return this.updateBatch([{
                key: 'richText',
                value: html,
            }, {
                key: 'description',
                value: text,
            }]);
        },
        updateSourceOrg({ orgTierCode, orgTierName }) {
            return this.updateBatch([{
                key: 'sourceOrgId',
                value: orgTierCode,
            }, {
                key: 'sourceOrgName',
                value: orgTierName,
            }]).then(() => {
                const { demandPriorityMark } = this.$refs;
                if (demandPriorityMark) {
                    demandPriorityMark.calculateScore();
                }
            });
        },
        // 同时更新几个字段
        updateBatch(fields) {
            const params = fields.map(field => ({
                fieldKey: field.key,
                fieldlabel: getFieldLabel(field.key, this.fieldText),
                fieldValue: field.value || this.data[field.key] || undefined,
                fieldValueDescription: field.attachData && field.attachData.name,
            }));
            return DemandModel.update(this.demand.id, params).then(() => {
                this.$message.success('保存成功!');
                this.triggerOuterUpdate();
                // this.$emit('update:demand', Object.assign(this.demand, this.data));
            }, this.updateFailed);
        },
        backfillArrayWithNewItem(targetList, value) {
            const index = findIndex(targetList, ['id', value.id]);
            if (index !== -1) {
                this.$set(targetList, index, value);
            } else {
                targetList.pop();
                targetList.push(value);
            }
        },

        /* 更新数组类型的字段 */
        updateFieldIndividual(key, value, isDelete = false) {
            return DemandModel.updateFieldIndividual({
                value,
                type: isDelete ? 'delete' : 'post',
                key,
                demandId: this.data.id,
            })
                .then((data) => {
                    // 但是只是update的话是不返回的
                    if (data && isObject(data) && isObject(value)) {
                        Object.assign(value, data);
                        const updateFieldList = this.data[key];
                        if (Array.isArray(updateFieldList)) {
                            // TODO: 现在默认新建的时候都会返回个新的实体
                            this.backfillArrayWithNewItem(updateFieldList, value);
                        }
                    }
                    this.$message.success(isDelete ? '删除成功' : '保存成功!');
                    this.triggerOuterUpdate();
                    return data;
                })
                .catch(({ msg }) => {
                    if (msg) {
                        this.$message.error(isDelete ? `删除失败:${msg}` : `保存失败:${msg}`);
                    }
                    this.updateFailed();
                });
        },
        updateFailed() {
            // 异常提示在全局
            this.loadDetail(this.demand.id);
        },
        triggerOuterUpdate() {
            this.$refs.demandDetailTabsIns.refresh();
            this.$emit('update:demand', Object.assign({}, this.demand, this.data));
        },
        loadDetail(id) {
            return DemandModel.getDetail(id).then((detail = {}) => {
                // 先检查保密需求
                // 为了后台确认是否有权限方位，然后弹框，放在最前面
                if (detail.status === 1
                && !isCurrentUser(detail.proposer)) {
                    this.handleHide();
                    return null;
                }
                this.data = detail;
                // this.data.loadRichTextHtml();
                // 加载自定义校验规则
                this.getOrgFieldsRules();

                // 需求action检验
                this.demandActionCheck();

                return detail;
            })
                .catch(() => {
                    // 没有权限查看或者返回异常的时候，关闭详情页
                    // this.$emit('hide');
                    this.handleHide();
                });
        },
        demandActionCheck() {
            this.$_setActions(this.data, (details, actions) => {
                // 成本分摊审批未通过
                if (this.data.apportionApproved !== 1) {
                    Object.assign(actions.find(o => o.cmd === 'communicate') || {}, { disabled: true });
                }
                if (this.data.estimatedDaysApprovalResult !== 0
                    && this.data.estimatedDaysApprovalResult !== 3) {
                    Object.assign(actions.find(o => o.cmd === 'distribute') || {}, { disabled: true });
                }
            });
        },
        handleAction(action) {
            /* eslint-disable no-param-reassign */
            const { cmd } = action;
            const defer = cmd === 'submit'
                ? DemandModel.save(this.data)
                : DemandModel.cmd(cmd, this.data);
            action.disabled = true;
            return defer.then(() => {
                // 刷新页面数据 关闭详情
                if (cmd === 'logicDelete') {
                    this.handleHide();
                    // this.$emit('hide-detail');
                }
                if (!['edit', 'split'].includes(cmd)) {
                    // 需求action检验
                    this.demandActionCheck();
                    this.triggerOuterUpdate();
                    this.$emit('refresh-list');
                }
            }).finally(() => {
                action.disabled = false;
            });
        },

        /* 需求分组 */
        handleGroupAction(groupId, type) {
            // 加入当前的分组列表 => 显示移出按钮
            // 加入其他分组列表 => 移出按钮不处理
            // 移出当前列表 => 隐藏移出按钮
            this.$emit('refresh-list');
            if (+groupId === +this.$route.query.groupId) {
                this.showDetachBtn = type !== 'detach';
            }
            this.$_fetchInvolvedGroupList(this.demand.id);
        },
        // Moved

        /*  async fetchRichTextHtml({ demandDescLink, description }) {
            if (demandDescLink) {
                this.data.richText = await DemandModel.getDetailRichTextHtmlByUrl(demandDescLink);
            } else {
                this.data.richText = description;
            }
            // 设置一下富文本的初始值
            // this.data.richText = await DemandModel.getDetailRichTextHtmlByUrl(url);
        }, */
        actionsCallBack(detail, actions) {
            // 成本分摊审批未通过
            if (detail.apportionApproved !== 1) {
                actions.forEach((item) => {
                    if (item.cmd === 'accept') {
                        Object.assign(item, { disabled: true });
                    }
                });
            }
        },
        addRemark() {
            if (this.remark.length === 0) {
                return;
            }
            DemandModel.serverHandles.addRemark({
                demandId: this.data.id,
                remark: this.remark,
            }).then(() => {
                this.$message.success('保存成功!');
                this.$refs.demandDetailTabsIns.refresh('remark');
                this.$emit('update-focused', {
                    key: 'lastComment',
                    value: this.remark,
                });
                this.remark = '';
                this.remarkInputVisible = false;
                // lastComment
            });
        },
        // 此段代码无用
        // handleCommand(cmd) {
        //     if (cmd === 'new-project') {
        //         window.open(`http://pmp.jd.com/proposal/my/toAdd/0c127b2165c2f05a?reqId=${this.data.id}`, '_blank');
        //     } else if (cmd === 'pre-new-project') {
        //         window.open(`http://pmp.jd.com/proposal/my/toAdd/79c76f618bd9f046?reqId=${this.data.id}`, '_blank');
        //     }
        // },
        clearExpectedReleaseDate() {
            // 修改需求类型，非活动类 期望下线时间会被清空
            if (!this.isActivityDemandType) {
                this.$set(this.data, 'expectedOfflineDate', undefined);
            }
        },
        initSystemInfo(systemInfoList) {
            // 过滤只需要id
            this.data.systemInfoList = (systemInfoList || []).map(item => item.id);
            // 初始已选项 用于select Label显示
            this.systemList = systemInfoList;
        },
        updateSystemRelation() {
            if (!this.$refs.systemRelation.changed) return;
            DemandModel.saveSystemRelations(this.demand.id, this.$refs.systemRelation.getSelectedData()).then(() => {
                // 该字段目前不显示在列表页因此不需刷新
                this.$message.success('保存成功!');
            }, this.updateFailed);
        },
        changePriorityScore(val) {
            if (!val) return;
            const params = {
                id: this.demand.id,
                priorityScore: val.mark,
                priorityScoreMap: val,
            };
            DemandModel.updatePriorityScore(params).then(() => {
                // 该字段目前不显示在列表页因此不需刷新
                this.$message.success('更新成功!');
            }, this.updateFailed);
        },
        updateAutoAcceptanceDays() {
            const days = this.data.autoAcceptanceDays;
            if (days === this.oldAutoAcceptanceDays) {
                this.editAutoAcceptanceDays = false;
                return;
            }
            this.$refs.formMain.validateField('autoAcceptanceDays', (error) => {
                if (!error) {
                    DemandModel.updateAutoAcceptanceDays(this.demand.id, days).then(() => {
                        this.oldAutoAcceptanceDays = days;
                        // 该字段目前不显示在列表页因此不需刷新
                        this.$message.success('保存成功!');
                    }, this.updateFailed);
                    this.editAutoAcceptanceDays = false;
                }
            });
        },
        focusAutoAcceptanceDays() {
            this.editAutoAcceptanceDays = true;
            if (!this.oldAutoAcceptanceDays) {
                this.oldAutoAcceptanceDays = this.data.autoAcceptanceDays;
            }
        },
        cancelEditAutoAcceptanceDays() {
            this.data.autoAcceptanceDays = this.oldAutoAcceptanceDays;
            this.oldAutoAcceptanceDays = undefined;
            this.editAutoAcceptanceDays = false;
        },
        menuAction(cmd) {
            if (cmd === 'task') {
                this.taskManageVisible = true;
                this.$nextTick(() => {
                    this.$refs.demandTaskManageIns.init();
                });
            }
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
/* 单独弱化一些字段 */
.text-placeholder{
    & .el-form-item__label,
    & .el-form-item__content,
    & .jacp-simple-select,
    & .simple-datepicker,
    & .el-input__inner
    {
        color: @fontThirdColor;
    }
}
.latest-detail{
    /* 业务字段 */
    &-involvedlist{
        max-height: 150px;
        overflow: auto;
    }
    &-involveditem{
        list-style: none;
        margin: 5px 0;
    }
    &-briefitem{
      &:first-of-type{
        margin-left: 0;
      }
      font-size: 12px;
      color: @fontThirdColor;
        display: inline-block;
        margin: 0 8px;
        &--urgent{
            transform: scale(0.9);
            padding: 0 4px;
            display: inline-block;
            color: @redColor;
            border: 1px solid @redColor;
        }
    }
    &-title{
        font-size: 18px;
    }
    &--hasConfidential{
        background: #fff url("~@/assets/images/<EMAIL>") no-repeat right top;
        background-size: 54px 42px;
    }
    &-proposer{
        display: flex;
        align-items: flex-start;
        .jacp-erp{
            margin-right: 8px;
        }
        & .jacp-erp__name{
            display: none;
        }
        & .latest-detail__block--inline{
            margin-top: -10px;
        }
    }
    &-receivers{
        margin-right: 70px;
        min-width: 40px;
    }
    &-demanders{
        &__wrapper{
            display: flex;
            flex-wrap: wrap;
            & .jacp-erp{
                margin-right: 14px;
            }
        }
    }
    &-revenues{
        width: 100%;
        font-size: 12px;
        padding: 8px 16px;
        color: @fontColor;
        border: 1px solid @borderColorSecend;
        border-radius: 4px;
    }
    &-followers__editor{
        flex-wrap: wrap;
        &>*{
            flex-basis: 24%;
        }
        & .form-users__item{
            margin: 6px 0;
            transform: translateX(-8px);
        }
    }
    &-system__option {
        width: 135px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
        float: left;

        &-type {
            float: right;
            margin-right: 26px;
            color: @fontThirdColor;
        }
    }
    &__text{
        color: #999999;
        font-size: 12px;
    }
}
.channelRiskTip {
    color: #BBB;
    font-size: 12px;
    line-height: 12px;
}
// 保持与detail页面间隔一致
.latest-detail-dynamic-form .j-form .dynamic-form-item{
    margin-top: 12px;
}
</style>
