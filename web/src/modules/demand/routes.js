import DemandModel from '@/models/demands';
import group from './layout.demandGroup';
import detail from './layout.demandDetail';
import add from './layout.demandAdd';
import cardDemands from './layout.cardDemands';
import index from './layout.demandIndex';

const layout = {
    add,
    group,
    detail,
    index,
};
const routers = [{
    path: 'demands',
    name: 'demands',
    redirect: { name: 'demandsIndex', params: { tab: 'mine' } },
    component: {
        template: '<router-view></router-view>',
    },
    children: [{
        // FIXME: 兼容老用户的收藏、详情路由跳转
        path: 'list',
        redirect: { name: 'demandsIndex', params: { tab: 'mine' } },
    }, {
        path: 'list/detail/:id?',
        redirect: (to) => {
            const { params } = to;
            if (params.id) {
                return {
                    name: 'demandDetail',
                    params: {
                        id: +params.id,
                        tab: 'mine',
                    },
                };
            }
            return {
                name: 'demandsIndex',
                params: { tab: 'mine' },
            };
        },
    }, {
        path: 'list/:tab',
        name: 'demandsIndex',
        component: layout.index,
        beforeEnter: (to, from, next) => {
            if (to.query && to.query.demandId) {
                next({
                    name: 'demandDetail',
                    params: {
                        id: to.query.demandId,
                    },
                });
            } else {
                next();
            }
        },
        children: [{
            path: 'detail/:id',
            name: 'demandDetail',
            component: layout.detail,
            // props: route => ({ layout: { ...route.params } }),
            props: route => ({ demand: { id: route.params.id } }),
            meta: {
                showOffCanvas: true,
                leaveConfirm: true,
            },
        }, {
            path: 'add',
            name: 'demandAdd',
            component: layout.add,
            meta: {
                showOffCanvas: true,
                leaveConfirm: true,
                // title: i18n.t('jacp.newDemand'),
            },
            beforeEnter: (to, from, next) => {
                DemandModel.getDemandAuthority().then(({ valid }) => {
                    if (valid) {
                        next();
                    } else {
                        next({ name: 'demandsIndex' });
                    }
                });
            },
        }, {
            path: 'edit/:id',
            name: 'demandEdit',
            component: layout.add,
            meta: {
                showOffCanvas: true,
                leaveConfirm: true,
            },
        }, {
            path: 'split/:id',
            name: 'demandSplit',
            component: layout.add,
            meta: {
                showOffCanvas: true,
                leaveConfirm: true,
            },
            beforeEnter: (to, from, next) => {
                DemandModel.hasValidApportion(to.params.id)
                    .then(next)
                    .catch(() => next({ name: 'demandsIndex' }));
            },
        }],
    }, {
        path: 'group/:groupId',
        name: 'groupDetail',
        component: layout.group,
    }, {
        path: 'cardDemands',
        name: 'cardDemands',
        component: cardDemands,
        props: route => ({ referenceTab: route.query.ref || 'mine' }),
    }],
}];

export default routers;
