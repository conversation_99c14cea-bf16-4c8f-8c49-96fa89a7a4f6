<template>
    <el-container class="demands-root">
        <!-- 假装是demand的header -->
        <el-header
            class="demands-root-header j-mgt16"
            style="padding: 0;"
            height="40px"
        >
            <el-dropdown
                class="demand-title"
                placement="bottom"
                size="medium"
                trigger="click"
            >
                <span>
                    产研需求
                    <jacp-icon
                        active
                        name="el-icon-arrow-down el-icon--right"
                    />
                </span>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="gotoDemands">业务需求</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
            <el-divider
                direction="vertical"
                class="title-divider"
            />
            <el-tabs
                class="jacp-tabs--gray  jacp-tabs"
                v-model="activeName"
            >
                <el-tab-pane
                    name="cardDemands"
                    label="我的"
                />
            </el-tabs>
        </el-header>
        <el-main class="demands-root-container">
            <DemandCardList class="demands-root__content" />
        </el-main>
    </el-container>
</template>
<script>
import DemandCardList from './components/demandCard/demandCardList';

export default {
    name: 'LayoutCardDemands',
    props: { referenceTab: String },
    components: { DemandCardList },
    data() {
        return { activeName: 'cardDemands' };
    },
    methods: {
        gotoDemands() {
            this.$router.push({ name: 'demandsIndex', params: { tab: this.referenceTab } });
        },
    },
};
</script>
<style>
.demands-root__content{
  margin: 0 var(--gutter--large);
}
</style>
