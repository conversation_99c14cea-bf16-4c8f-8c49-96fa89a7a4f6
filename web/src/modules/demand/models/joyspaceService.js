import { Message } from 'element-ui';
import http from '@/plugins/http';

const attachmentType = 'joy_space_link';

/* [
    {
        "id": "cmQ0m12Hho36vHg0v1D",
        "title": "xxx需求",
        "iconUrl": "https://",
        "pageType": 1,
        "link": "https://joyspace.jd.com/page/xxxxx",
        "deepLink": ""
    }
] */
export function joyspaceLinkAdapter(item) {
    const { link: path, title: attachmentName } = item;
    const link = {
        attachmentName,
        path,
    };
    return link;
}
export function removeLink(link = {}, demand = {}) {
    const { id: attachmentId } = link;
    return http.delete('v1/infrabucket/deleteLink', {
        params: {
            attachmentType,
            attachmentId,
            entityId: demand.id,
        },
    }).then(() => {
        Message.success('删除成功！');
    });
}
export function addLinks(items = [], demand = {}) {
    const links = items.map(item => joyspaceLinkAdapter(item));
    return http.post('v1/infrabucket/saveLinks', {
        entityId: demand.id,
        attachmentType,
        attachmentLinks: links,
    }).then((data) => {
        Message.success('保存成功!');
        return data;
    });
}

const arrToObj = prop => arr => arr.reduce((result = {}, item = {}) => {
    const key = item[prop];
    result[key] = item;
    return result;
}, {});

// 新建需求：joyspace文档列表转成需求的links然后去重
export function addLinksAndRemoveDuplicates(items = [], demand = {}) {
    const transform = arrToObj('path');
    const existingLinkMap = transform(demand.joySpaceLinks);
    const newLinkMap = transform(items.map(item => joyspaceLinkAdapter(item)));
    const result = Object.values({
        ...existingLinkMap,
        ...newLinkMap,
    });
    return Promise.resolve(result);
}
