/* eslint-disable */
import http from '@/plugins/http';
import store from '$platform.store';
import { taskStatus } from '@/modules/task/constant';
import { date2Number, timestamp2Date } from '@/plugins/utils.transfer';
import { initProperties } from '@/plugins/utils';
import { Message } from 'element-ui';

const DEFAULT = [
    ['demandId'],
    ['code'],
    ['id'],
    ['index'],
    ['content', ''],
    ['erp'],
    ['name'],
    ['startDate'], ['deadline'],
    ['plannedHours'],
    ['usedHours'],
    ['status', taskStatus.undo.code], // 1: 未完成 2:完成
];

const createDemandTasks = (data = []) => data.map(task => new DemandTask(task));

export default class DemandTask {
    constructor(values = {}) {
        initProperties(this, DEFAULT, values);
        const startDate = this.startDate || values.duration?.[0];
        const deadline = this.deadline || values.duration?.[1];
        this.duration = [
            timestamp2Date(startDate),
            timestamp2Date(deadline),
        ];
        
        return this;
    }

    updateStatus() {}
    
    save() {
        if (!this.id) return;

        const { demandId, id } = this;
        return http.put(`v1/demand/${demandId}/task/${id}`, DemandTask.toServerData(this))
            .then(() => {
                Message.success('更新成功');
            })
            .catch((error) => {
                throw new Error(error);
            });
    }
    
    static toServerData(params = {}) {
        const data = Object.create(null);
        Object.keys(params).forEach((key) => {
            if (key === 'duration') {
                [data.startDate, data.deadline] = params[key] || [];
                return;
            }
            data[key] = params[key];
        })
        return Object.assign(data, {
            startDate: date2Number(data.startDate),
            deadline: date2Number(data.deadline),
        })
    }
    
    static create(params = {}, demandId) {
        return http.post(`v1/demand/${demandId}/task`, {
            ...DemandTask.toServerData(params),
            demandId,
        });
    }
    
    static batchCreate(params = [], demandId) {
        return http.post(
            `v1/demand/${demandId}/task/batch`,
            params.map(task => DemandTask.toServerData({
                ...task,
                demandId,
            })),
        ).then(data => createDemandTasks(data));
    }

    static delete({ demandId, id }) {
        return http.delete(`v1/demand/${demandId}/task/${id}`);
    }

    static getList(demandId) {
        return http.get(`v1/demand/${demandId}/task`)
            .then(data => createDemandTasks(data));
        }
        
    static getProcessors(demandId) {
        return http.get(`v1/demand/${demandId}/user/stakeholder`)
            .then((data) => {
                store.commit('demands_processors_update', data);
                return data;
            });
    }

}