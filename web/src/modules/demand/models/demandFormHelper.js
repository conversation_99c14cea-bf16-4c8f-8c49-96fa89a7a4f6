import { childHttp } from '@/plugins/http';
import DynamicForm from '@/models/dynamicForm';
import { formRequiredValidator as formValidate } from '@/plugins/utils';

export const demandMap = {
    1: 'demand', // 业务需求
    2: 'prd-demand', // 产品需求
    3: 'dev-demand', // 研发需求
};
// 检查字段例外部门信息, receiver只有一个
function checkIncludes(includes, receivers = []) {
    let flag = true;
    // const receiver = this.data.receivers[0];
    const [receiver] = receivers;
    if (includes && includes.length > 0) {
        includes.forEach((item) => {
            //  检查例外部门是否包含当前受理人部门或其上级部门
            if (receiver) {
                // 表单编辑和控件获取人员部门编码字段不一致 取有值的
                const orgCode = receiver.orgTierCode || receiver.orgCode;
                if (orgCode.startsWith(item.orgId)) {
                    flag = false;
                }
            }
        });
    }
    return flag;
}

const fieldsVisibleFilter = (displayRules = {}, fields = []) => fields.filter(field => displayRules[field.name]);
const generateRequiredRule = item => ({
    required: true,
    message: `请设置${item.fieldName}`,
    trigger: ['change', 'blur'],
    validator: formValidate,
});
const generateMergeFn = (
    vm = {},
    demandIns = {},
    newRules = [],
) => (displayRules = {}, item) => {
    // FIXME: 混乱的rules处理
    const rules = vm.rules[item.fieldKey];
    const defaultRequiredRule = generateRequiredRule(item);
    // 更新一下显隐状态
    Object.assign(displayRules, { [item.fieldKey]: item.display ?? true });
    // 扩展字段的rule处理
    if (item.extended) {
        // 新建
        if (item.required) {
            if (!vm.rules.extendedFields) {
                vm.$set(vm.rules, 'extendedFields', { type: 'object', fields: {} });
            }
            newRules.push(item.fieldKey);
            vm.$set(vm.rules.extendedFields.fields, item.fieldKey, [defaultRequiredRule]);
        } else if (vm.rules.extendedFields) {
            vm.$delete(vm.rules?.extendedFields?.fields, item.fieldKey);
        }
        return displayRules;
    }
    if (item.required && checkIncludes(item.excludes, demandIns.receivers)) {
        if (rules) {
            if (!rules.some(o => o.required)) {
                // 为保证多次切换 每次创建新数组
                // 同时触发rules的改变，使重新计算el-form-item的required计算
                vm.$set(vm.rules, item.fieldKey, rules.concat([defaultRequiredRule]));
            }
        } else {
            // TODO: 追加的这种rules无法触发elFormItem的change或者blur，因为只有在mounted的时候才会监听
            vm.$set(vm.rules, item.fieldKey, [defaultRequiredRule]);
            newRules.push(item.fieldKey);
        }
    }
    // 如果是配置了非必填的话，过滤掉required的rules
    if (!item.required
        && rules
        && rules.some(o => o.required)) {
        vm.$set(vm.rules, item.fieldKey, rules.filter(o => !o.required));
    }
    return displayRules;
};
// 向field中注入demand的业务代码：field发生变化的时候通知demand页面调接口更新
export const initDynamicFormField = (
    onDynamicFormUpdated = () => {},
    defaultOptions = { display: true },
) => (field) => {
    const superChange = field.getListener('change');
    // 注入listeners
    /* eslint-disable no-param-reassign */
    field.listeners = {
        change(arg) {
            const fieldInstance = this;
            superChange.call(this, arg);
            onDynamicFormUpdated(fieldInstance, arg); // 通知外围更新，同时把field信息传递过去
        },
    };
    Object.assign(field, defaultOptions);
    return field;
};

export default class FormHelper {
    constructor({ onDynamicFormUpdated = () => {} } = {}) {
        this.displayRules = {}; // 统一保存详情页的字段是否显示，包括普通字段和自定义字段
        this.dynamicFormSchema = null; // 存储自定义表单对象
        this.onDynamicFormUpdated = onDynamicFormUpdated;
    }

    // 根据需求模版字段里设置的显示隐藏状态过滤一下, 同时注入业务事件响应onDynamicFormUpdated
    initDynamicForm(dynamicFormIns, dynamicFormSchemaRules = {}) {
        const { displayRules, onDynamicFormUpdated } = this;
        // 过滤隐藏field
        const visibleFields = fieldsVisibleFilter(
            displayRules,
            dynamicFormIns.schema.fields,
        ).map(initDynamicFormField(onDynamicFormUpdated));
        // 自定义字段的显示或者不显示，是以模版字段中的配置为准，不以schema为准，schema中存储的是默认值

        Object.assign(dynamicFormIns.schema, { fields: visibleFields });
        this.dynamicFormSchema = dynamicFormIns.schema;
        // 把rules通知到自定义表单form实例中，生成dynamicForm最终的rules，单独进行表单校验
        this.dynamicFormSchema.setRules(dynamicFormSchemaRules, true);
        // this.dynamicFormSchema.rules = dynamicFormSchemaRules;
    }

    /**
     * 获取受理人部门的个性化校验规则设置
     * 如果存在自定义自动，同时还要获取该部门的自定义表单schema
     * 与默认rules进行merge
     * @param {object} vm
     * @param {Demand} demandIns
     */
    mergeRules(vm = {}, demandIns = {}) {
        const { displayRules } = this;
        const { receivers = [], demandSourceId } = demandIns;
        const formKey = demandMap[demandSourceId];
        const newRules = [];
        // 同时处理rules和显示rules，顺便保存一下哪些是新规则
        const mergeRulesAndDisplayRules = generateMergeFn(vm, demandIns, newRules);
        if (!receivers || !receivers.length) {
            // TODO: 不知道为什么groupId变化的时候会触发这里
            return Promise.reject();
        }
        return Promise.all([
            // 获取受理人部门的个性化校验规则设置
            FormHelper.getOrgFieldAttrByErp({
                formKey,
                erp: receivers[0].erp,
            }),
            // 获取该部门的自定义表单schema
            DynamicForm.load({
                name: 'demand',
                scope: receivers[0].orgTierCode,
            }),
        ]).then(([{ fields: commonFields = [] }, dynamicFormIns]) => {
            // 生成rules
            commonFields.reduce(mergeRulesAndDisplayRules, displayRules);
            this.initDynamicForm(dynamicFormIns, vm.rules?.extendedFields?.fields);

            return {
                rules: vm.rules,
                newRules,
            };
        });
    }

    // 查询当前用户所在部门需求详情页个性化信息
    static getOrgFieldAttrByErp(params) {
        return childHttp.get('v1/bizMetadataForm/orgFieldAttrByErp', {
            params,
        });
    }
}
