
/**
 * 一条需求所有相关的操作都收录在Demand class中，其他周边操作仍放在demanda.js中慢慢拆分
 */
import uniqBy from 'lodash/uniqBy';
import isPlainObject from 'lodash/isPlainObject';
import store from '$platform.store';
import http, { demandHttp } from '@/plugins/http';
import {
    initProperties, jsonAdapter, isCurrentUser, isCurrentUserExist,
} from '@/plugins/utils';
import { demandsQuerytypeMap } from '@/models/config';
import { date2Number } from '@/plugins/utils.transfer';
import { User } from '@/models/user';
import { Attachment } from '@/models/file';

// TODO: 以下是从demands.js里迁移过来的，以后再慢慢改造
const toServerDataMap = {
    description: 'demandDesc',
    // attachments: fileList => serverAdapter.fileList('server', fileList),
    attachments: fileList => fileList.map(file => Attachment.toServer(file)),
    expectedReleaseDate: {
        targetKey: 'expectedReleaseDate',
        handle: date2Number,
    },
};

// 所有需要支持的查询部门的类型
const orgArguments = ['recipientOrg', 'demanderOrg', 'proposerOrg', 'apportionOrg', 'processorOrg'];
const transformOrg2OrgId = (orgs = [], valueKey = 'parentIds') => {
    const [org = {}] = orgs;
    return org[valueKey] || undefined;
};
// 部门的类型的查询参数都改成部门id
const listServerDataMapOrgArguments = orgArguments.reduce((result, target) => {
    result[target] = {
        targetKey: `${target}Id`,
        handle: transformOrg2OrgId,
    };
    return result;
}, {});
const listServerDataMap = {
    queryType: type => demandsQuerytypeMap[type],
    demanders: {
        targetKey: 'demandContactErp',
        handle: users => ((users || [])[0] || {}).erp,
    },
    processors: {
        targetKey: 'processorErp',
        handle: users => ((users || [])[0] || {}).erp,
    },
    conserners: {
        targetKey: 'consernContactErps',
        handle: users => (users || []).map(u => u.erp),
    },
    proposers: {
        targetKey: 'proposerErp',
        handle: users => ((users || [])[0] || {}).erp,
    },
    recipient: {
        targetKey: 'recipientErp',
        handle: users => ((users || [])[0] || {}).erp,
    },
    // 暂时加上相关人选项，筛选条件Erp作为字段。
    relationUser: {
        targetKey: 'relationUserErp',
        handle: users => ((users || [])[0] || {}).erp,
    },
    transferUser: {
        targetKey: 'transferUserErp',
        handle: users => ((users || [])[0] || {}).erp,
    },
    ...listServerDataMapOrgArguments,
    // 如果需要将嵌套字段打平的话，需要在调用时设置needflattenObject = true
    // e.g. jsonAdapter(config, params, true)
    // statusKeepTime: (statusKeepTime = {}) => ({
    //     statusKeepTime: statusKeepTime.keepTime,
    //     statusRelation: statusKeepTime.relation,
    // }),
    productId: (product) => {
        if (isPlainObject(product)) {
            return product.id;
        }
        return product;
    },
    // 该处未启用，暂时避免影响后端保存。
    // orgCombination: (orgCombination = {}) => ({
    //     direction: orgCombination.direction,
    //     orgnazitionId: orgCombination.orgnazitionId,
    // }),
};


// 迁移部分结束
// TODO:需求详情里接口已经直接输出对象了，这个其实也应该直接输出对象，有时间需要调整一下
// 操作人
class Processor extends User {
    constructor({
        orgId: orgTierCode,
        orgName: orgTierName,
        ...props
    }) {
        super({
            orgTierCode, orgTierName, ...props,
        });
    }
}
// 提交人
class Proposer extends Processor {}
// 相关联系人
class Contact extends User {
    // 参数名字转一下
    constructor({
        contactErp: erp,
        contactName: name,
        contactOrgId: orgTierCode,
        contactOrgName: orgTierName,
        type,
        id,
        ...props
    }) {
        super({
            erp, name, orgTierCode, orgTierName, ...props,
        });
        this.type = type;
        this.id = id;
    }

    // 原来构造的对象字段名字是短的，这里用getter兼容一下其他位置的取用，保证老代码不出错
    get orgCode() { return this.orgTierCode; }

    get orgName() { return this.orgTierName; }

    toServer() {
        const { type, erp } = this;
        return {
            type,
            contactErp: erp,
        };
    }
}
// 联系人的分类
export const ContactTypeMap = {
    demanders: 1,
    followers: 2,
    receivers: 3,
};

/* eslint-disable no-param-reassign */
/**
 * 统一拦截一下保存联系人的数组
 * TODO: 代理了以后，私有属性就不能用了。。
 */
export function generateContactsProxy(targetDemand, types = {}) {
    const contactChangeHandler = {
        set: (obj, prop, val) => {
            if (prop in types) {
                if (Array.isArray(val)) {
                    obj[prop] = val.map(user => (user instanceof Contact
                        ? user
                        : new Contact({ ...user, type: types[prop] })));
                }
            } else {
                // 默认行为是保存属性值
                obj[prop] = val;
            }
            return true;
        },
    };
    return new Proxy(targetDemand, contactChangeHandler);
}

// TODO: 如果不是从server端来的数据就不会有这个bizContacts参数
export function resolveContacts(data = []) {
    // const contacts = data.map(user => new Contact(user));
    return {
        demanders: data.filter(user => user.type === 1),
        followers: data.filter(user => user.type === 2),
        receivers: data.filter(user => user.type === 3),
    };
}

export const resolveAttachments = (data = []) => data.map(file => new Attachment(file));
const demandDefaultProperties = [
    ['name', '', { clone: true }],
    ['description', '', { clone: true }],
    ['richText', '', { clone: true }],
    ['demandDescLink', '', { clone: true }],
    ['actuality', '', { clone: true }],
    ['roi', '', { clone: true }],
    ['demanders', [], { clone: true }], // 需求人
    ['followers', [], { clone: true }], // 关注者
    ['receivers', [], { clone: true }], // 受理人
    ['apportions', []], // 成本分摊
    ['attachments', [], { clone: true }],
    ['links', [], { clone: true }],
    ['joySpaceLinks', [], { clone: true }], //  joyspace 云文档
    ['tags', [], { clone: true }],
    ['platformRelations', [], { clone: true }],
    ['expectedReleaseDate', undefined, { clone: true }],
    ['expectedOfflineDate', undefined],
    ['priorityId', undefined, { clone: true }],
    ['priorityScore', -1, { clone: true }],
    ['demandTypeId', undefined, { clone: true }],
    ['demandSourceId', 1, { clone: true }],
    ['urgent', false, { clone: true }],
    ['confidential', false, { clone: true }],
    ['revenueId', undefined, { clone: true }],
    ['revenueOther', undefined, { clone: true }],
    ['revenueDays', undefined, { clone: true }], // 收益验证时长
    ['riskId', undefined, { clone: true }],
    ['channelRisk', undefined, { clone: true }],
    ['parentDemandId', -1],
    ['projectId', undefined, { clone: true }], // 项目ID
    ['pmpProjectApproval', 0],
    // http://jagile.jd.com/teamspace/cardlist/JACP/carddetail?cardId=379271&sprintId=22960
    // 当进行需求拆分时，点击“复制父需求”后，则“关联项目”字段的值可以继承。
    ['pmpProjectId', undefined, { clone: true }],
    ['pmpProjectName', undefined, { clone: true }],
    ['systemInfoList', []],
    ['demandSourceOrg', undefined],
    ['autoAcceptanceDays', undefined],
    // 关联产品
    ['product', undefined, { clone: true }],
    ['revenue', undefined],
    ['extendedFields', undefined, { clone: true }],
];
// 已创建的需求属性
const existDemandProperties = [
    // 这些是新建的时候不应该有的
    ['id'],
    ['rootDemandId'],
    ['processor', { clone: true }],
    ['proposer', { clone: true }],
    ['submitTime'],
    ['system', ''],
    ['hasChild', false],
    ['status'],
    // ['urgent'],
    ['estimatedDaysApprovalResult', 0],
    ['apportionApproved', 0],
    ['workload', {}],
    ['demandCode', ''],
    ['taskId', ''], // TODO: 这是啥？
    ['projectLock', 0], // TODO: 这是啥？
    ['hasDevStatus'],
    ['evaluated', 0],
];
const getDemandPropertiesSettings = () => {
    const properties = demandDefaultProperties.concat(existDemandProperties);
    return properties.reduce((pMap, property) => {
        if (Array.isArray(property)) {
            const [name, defaultValue, option] = property;
            // eslint-disable-next-line
            pMap[name] = {
                defaultValue,
                option,
            };
        }
        return pMap;
    }, {});
};
const editableStatus = [13, 3, 5, 6, 7, 8, 10, 11, 12];
const proposerEditableStatus = [2].concat(editableStatus);
export default class Demand {
    #options = {
        loadRichText: false,
        defaultDemander: true,
    };

    constructor({
        demandDesc: description,
        bizContacts,
        attachments,
        submitTime,
        cTime,
        processor,
        proposer,
        id,
        extendedFields = {},
        sourceOrgId,
        sourceOrgName,
        ...props
    } = {}, options = {}) {
        // const self = this;
        const self = generateContactsProxy(this, ContactTypeMap);
        // 判断是否是新建的，非新建的需要追加一些字段
        const properties = id
            ? demandDefaultProperties.concat(existDemandProperties)
            : demandDefaultProperties;
        // 初始化option
        Object.assign(this.#options, options);
        // ----FIXME: 列表里返回的是平铺的结构，需要临时找齐一下
        let product = props.product ? props.product : undefined;
        if (props.productId) {
            product = {
                id: props.productId,
                name: props.productName,
                type: props.productType,
            };
        }
        // ----end
        // 初始化需求参数
        initProperties(self, properties, Object.assign({
            id,
            description,
            attachments: resolveAttachments(attachments),
            submitTime: submitTime || cTime,
            // 当前处理人/操作人
            processor: isPlainObject(processor) ? new Processor(processor) : undefined,
            // 提交人
            proposer: isPlainObject(proposer) ? new Proposer(proposer) : undefined,
            // 需求来源部门
            demandSourceOrg: sourceOrgId ? { orgTierCode: sourceOrgId, orgTierName: sourceOrgName } : undefined,
            // 关联产品
            product,
            ...props,
        }, bizContacts ? resolveContacts(bizContacts) : {})); // 只有从服务器获取的数据才有这个参数

        // 存储自定义表单的字段值
        self.extendedFields = extendedFields;

        if (!self.demanders.length && this.#options.defaultDemander) {
            self.initDemanders();
        }
        if (this.#options.loadRichText) {
            self.loadRichTextHtml();
        }
        return self;
    }

    get demandId() { return this.id; }

    // Processor:当前操作人的权限
    get editable() {
        const data = this;
        return editableStatus.includes(data.status)
            && isCurrentUser(data.processor)
            && data.system === 'jacp';
    }

    // Proposer:提交人权限
    get proposerEditable() {
        const data = this;
        return proposerEditableStatus.includes(data.status)
            && isCurrentUser(data.proposer)
            && data.system === 'jacp';
    }

    // demander Editable: 需求人可以修改期望上线时间
    get demanderEditable() {
        const data = this;
        return editableStatus.includes(data.status)
            && isCurrentUserExist(data.demanders)
            && data.system === 'jacp';
    }

    // 所有关联的人
    get demandRelevantPeople() {
        const {
            demanders,
            followers,
            processor,
            processorOrgName,
            proposer,
            proposerOrgName,
        } = this;
        const getValue = user => Object.assign(user, {
            value: `${user.name}(${user.erp})`,
        });
        const users = [
            ...(demanders.map(getValue)),
            ...followers.map(getValue),
        ];
        if (proposer && proposer.erp) {
            users.push(getValue(Object.assign({}, proposer, {
                orgName: proposerOrgName,
            })));
        }

        /* TODO: 在获取列表的时候被拼了一个无效的processor。列表里不确定能不能把拼接去掉，所以先这么处理 */
        if (processor && processor.erp) {
            users.push(getValue(Object.assign({}, processor, {
                orgName: processorOrgName,
            })));
        }
        return uniqBy(users, 'erp');
    }

    initDemanders() {
        const {
            erp, name, orgTierCode, orgTierName, headImage,
        } = store.state.user;
        this.demanders = [{
            erp,
            name,
            orgTierCode,
            orgTierName,
            headImage,
        }];
    }

    async loadRichTextHtml() {
        if (this.demandDescLink) {
            this.richText = await Demand.getDetailRichTextHtmlByUrl(this.demandDescLink);
        } else {
            this.richText = this.description || '';
        }
    }

    save(extraParams = {}) {
        return http.post('v1/demands', {
            ...this.constructor.toServer(this),
            ...this.constructor.toServer(this),
            ...extraParams,
        });
    }

    saveDraft(extraParams = {}) {
        return http.post('v1/demands/draft', {
            ...this.constructor.toServer(this),
            ...extraParams,
        });
    }

    // 仅复制允许复制的字段, 在字段初始化的数组里面配置字段是否允许复制
    copy() {
        const propertiesSettings = getDemandPropertiesSettings();
        const isClone = prop => propertiesSettings[prop]?.option?.clone;
        return Object.keys(this).reduce((target, prop) => {
            if (isClone(prop)) {
                // eslint-disable-next-line
                target[prop] = this[prop];
            }
            return target;
        }, {});
    }

    // 仅复制基础信息
    copyBasic() {
        const {
            name, demandSourceId, description, demandDescLink,
            product,
        } = this;
        return {
            name,
            demandSourceId,
            description,
            demandDescLink,
            product,
        };
    }

    // 拆分的时候要保持保密状态与父需求一致, 默认显示父需求的附件，并且为只读
    split() {
        const { confidential, attachments } = this;
        return {
            confidential,
            attachments: attachments?.length
                ? attachments.map(o => Object.assign(o, {
                    readOnly: true,
                }))
                : [],
        };
    }

    //
    // toLocal 已经简化在constructor里处理了
    // 这部分是从demands.js中迁移来的
    static toServer(data = {}) {
        // const data = this;
        const defaultDemanders = [{
            contactErp: store.state.user.erp,
            type: 1,
        }];
        const followers = (data.followers || []).map(user => ({
            contactErp: user.erp,
            type: 2,
        }));
        let demanders = (data.demanders || []).map(user => ({
            contactErp: user.erp,
            type: 1,
        }));
        const receivers = (data.receivers || []).map(user => ({
            contactErp: user.erp,
            type: 3,
        }));
        if (!demanders.length) {
            demanders = defaultDemanders;
        }
        // 对象数据平铺
        const sourceOrgId = (data.demandSourceOrg || {}).orgTierCode;
        const sourceOrgName = (data.demandSourceOrg || {}).orgTierName;
        return jsonAdapter(toServerDataMap, Object.assign({}, data, {
            sourceOrgId, sourceOrgName,
        }, {
            bizContacts: demanders.concat(followers).concat(receivers),
            followers: undefined,
            receivers: undefined,
            demanders: undefined,
        }));
    }

    static // 直接获取富文本资源
    getDetailRichTextHtmlByUrl(url) {
        if (!url) {
            return Promise.reject(new Error('没有资源url'));
        }
        return http.get(url, {
            headers: {
                'Content-Type': 'text/plain',
                'Cache-Control': 'no-cache',
            },
            responseType: 'text',
            baseURL: '',
            withCredentials: false, // 平台里发起请求的话静态资源会做跨域处理Access-Control-Allow-Origin=*，所以不能带cookie
        });
    }

    static loadList(where = {}) {
        // const ignoreQueryTypes = ['department', 'mine', 'product'];
        // console.log(where);

        const api = {
            group: 'v1/demandGroupRelation/group',
            draft: 'v1/demands/advancedQuery',
            default: 'v1/demands/advancedQueryV2',
        };
        const isQueryGroup = where.queryType === 'group';
        const url = (where.queryType && where.queryType in api)
            ? api[where.queryType]
            : api.default;
        if (isQueryGroup && !where.groupId) {
            return Promise.resolve({ records: [] });
        }
        // if (ignoreQueryTypes.includes(where.queryType)) {
        //     // 用了筛选器的查询去掉queryType??
        //     delete where.queryType;
        // }
        // FIXME: 为啥加了debounce以后会死循环？先屏蔽一下
        return demandHttp.post(url,
            jsonAdapter(listServerDataMap, where, true), { debounce: false }).then((res) => {
            /* eslint-disable no-param-reassign */
            /* TODO:  */
            res.records = res.records.map((record) => {
                const d = new Demand({
                    ...record,
                    processor: {
                        erp: record.processorErp,
                        name: record.processorName,
                    },
                    proposer: {
                        erp: record.proposerErp,
                        name: record.proposerName,
                    },
                });
                // return d;
                // TODO:列表里用到了构造函数以外的字段？omd，需要梳理完了才行，目前先保留构造函数
                return Object.assign(d, record);
            });
            return res;
        });
    }

    static loadDetail(id, demand = {}) {
        return http.get(`v1/demands/detail?id=${id}`, { debounce: false })
            .then(data => (data
                ? new Demand({ ...demand, ...data }, {
                    loadRichText: !demand.richText,
                }) : null));
    }
}
