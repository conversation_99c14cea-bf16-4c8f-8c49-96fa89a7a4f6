import { spaceHttp } from '@/plugins/http';
import moment from 'moment';
// import i18n from '$platform.i18n';
// import store from '$platform.store';
// import router from '$platform.router';
// 标题、编号、类型、团队空间、更新日期
export const cardDemandTypesMap = {
    5: '研发需求',
    2: '产品需求',
};
export const demandCardColumns = [{
    prop: 'cardName',
    minWidth: 150,
    // label: i18n.t('jacp.teamspace.cardFields.title'),
    label: '标题',
    cellRender(h, { row }) {
        const { id: cardId, sprintId, spaceKey } = row;
        return <router-link target="_blank" to={{
            name: 'teamspaceCardDetail',
            params: {
                spaceKey,
            },
            query: {
                cardId,
                sprintId,
            },
        }}>
            <el-tooltip>
                <div slot="content" style="max-width: 350px">{row.cardName}</div>
                <span style="display: block;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">{row.cardName}</span>
            </el-tooltip>
        </router-link>;
    },
}, {
    prop: 'spaceKey',
    label: '团队空间',
    cellRender(h, { row }) {
        const { spaceKey, spaceName } = row;
        return <router-link target="_blank" to={{
            name: 'teamspaceCardsList',
            params: {
                spaceKey,
            },
        }}>{spaceName}</router-link>;
    },
}, {
    prop: 'cardCode',
    label: '编号',
}, {
    prop: 'uTime',
    label: '更新时间',
    sortable: true,
    cellRender(h, { row }) {
        return <span>{moment(row.uTime).format('YYYY-MM-DD HH:mm:ss')}</span>;
    },
}, {
    prop: 'cardType',
    label: '类型',
    cellRender(h, { row }) {
        return <span>{cardDemandTypesMap[row.cardType]}</span>;
    },
}];
// 获取产研需求类型的卡片
export default {
    getList(params = {}, pager = {}) {
        return spaceHttp.post('v1/bizSpaceCard/demandCard', {
            ...params,
            ...pager,
        }).then(({
            resultCount,
            resultList = [],
        } = {}) => {
            pager.total = resultCount;
            return resultList;
        });
        // return http.post('v1/bizSpaceCard/demandCard', params);
    },
};
