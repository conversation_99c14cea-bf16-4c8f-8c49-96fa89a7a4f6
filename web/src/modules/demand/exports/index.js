// import $workspace from '$module/exports/workspaceWidgets';

import J<PERSON><PERSON><PERSON>ommonDemandList from './commonDemandList';

import JacpCommonQueryFilter from '@/components/queryFilter/commonQueryFilter';
// 兼容一下产品管理里用的旧名字。。。自己挖坑自己填
const JacpCommonDemandFilter = {
    name: 'JacpCommonDemandFilter',
    extends: JacpCommonQueryFilter,
};
JacpCommonDemandFilter.name = 'JacpCommonDemandFilter';
export default {
    // $workspace,
    $globalComponents: {
        JacpCommonDemandList,
        JacpCommonDemandFilter,
    },
};
