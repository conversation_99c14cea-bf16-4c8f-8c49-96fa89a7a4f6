<script>
import Pager from '@/plugins/pager';
import MixinRouter from '@/mixins/mixin.router';
import demandsList from '$module/components/demandsList';
import DemandsModel from '$module/models/Demand';
import MixinChildren from '$module/components/mixin.children';

/*
 * 默认功能：
 * 展开/收起；
 * 表头排序；
 * 列表分页；
 */
export default {
    name: 'JacpCommonDemandList',
    inheritAttrs: false,
    props: {
        conditions: { type: Object, default: () => ({}) },
        columns: { type: Array, default: () => [] },
        loadList: { type: Function, default: DemandsModel.loadList },
        // 分页
        withPager: { type: Boolean, default: true },
        // TODO: 批量
        withBatch: { type: Boolean, default: false },
        // Open new tab
        openNewTabWhenShowDetail: { type: Boolean, default: false },
        // 是否一加载就执行load
        immediateMountedLoad: { type: Boolean, default: false },
        withOprations: { type: Boolean, default: false },
    },
    components: { demandsList },
    mixins: [MixinChildren, MixinRouter],
    mounted() {
        if (this.immediateMountedLoad) {
            this.load();
        }
    },
    data() {
        return {
            activedList: [],
            sortCondition: {},
            pager: this.withPager ? new Pager() : {},
        };
    },
    methods: {
        load(params = {}) {
            const requestParams = {
                current: this.pager.currentPage,
                pageSize: this.pager.pageSize,
                ...this.conditions,
                ...this.sortCondition,
                ...params,
            };
            return Promise.resolve(this.loadList(requestParams))
                .then((data = {}) => {
                    this.activedList = this.formatDemandsList({
                        list: data.records,
                    });
                    this.pager.total = data.total;
                    this.pager.currentPage = requestParams.current;
                    return this.activedList;
                });
        },
        sortByColumns({ orderField, orderType } = {}) {
            this.sortCondition = orderType ? Object.assign({}, {
                orderField,
                orderType,
            }) : {};
        },
        showDetail(demand = {}) {
            const routeData = {
                name: 'demandDetail',
                params: {
                    id: demand.id,
                },
            };
            if (this.openNewTabWhenShowDetail) {
                // 新开窗口
                this.$_routerOpen(routeData);
            } else {
                this.$router.push(routeData);
            }
        },
        handleCurrentPageChange(page) {
            let changed = false;
            if (this.pager.currentPage !== page) {
                changed = true;
            }
            this.pager.currentPage = page;
            // 防止pagesize发生变化的时候重复请求
            if (changed) {
                this.load();
            }
        },
        handleSizeChange(size) {
            this.pager.currentPage = 1;
            this.pager.pageSize = size;
            this.load();
        },
    },
    render() {
        const scopedSlots = {};
        if (this.$scopedSlots.oprations) {
            scopedSlots.oprations = ({ scope }) => this.$scopedSlots.oprations({
                ...scope,
            });
        }
        return (
            <div class="common-demand-list">
                <demands-list
                    {...{ props: { ...this.$attrs } }}
                    {...{ on: { ...this.$listeners } }}
                    { ...{
                        scopedSlots,
                    }}
                    org-columns={ this.columns }
                    data={ this.activedList }
                    oprations={this.withOprations}
                    sort-condition={this.sortCondition}
                    onSort-change={this.sortByColumns}
                    onToggle-children={this.toggleExpandStatus}
                    onToggle-expand-all={this.toggleExpandAll}
                    onOn-demand-focus={this.showDetail}
                    onRefresh-list={this.load} />
                <div class="common-demand-list-footer">
                    {
                        (this.withPager && !this.$scopedSlots.pager) ? <el-pagination
                            background
                            class="j-pagination"
                            style="margin-left: auto;"
                            page-size={this.pager.pageSize}
                            total={this.pager.total}
                            current-page={this.pager.currentPage}
                            onCurrent-change={this.handleCurrentPageChange}
                            onSize-change={this.handleSizeChange}
                            layout="prev, pager, next, sizes, total, jumper"
                        /> : null
                    }
                    {
                        this.$scopedSlots.pager ? this.$scopedSlots.pager({
                            pager: this.pager,
                        }) : null
                    }
                </div>
            </div>
        );
    },
};
</script>
<style scoped>
.common-demand-list{
    background: #fff;
}
.common-demand-list-footer{
    height: 64px;
    padding: var(--gutter--medium) 0;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
}
</style>
