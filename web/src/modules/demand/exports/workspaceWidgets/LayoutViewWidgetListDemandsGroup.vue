<template>
    <layout-card
        title="需求分组"
        class="jacp-plain"
    >
        <el-select
            v-model="orderBy"
            placeholder="请选择"
            slot="extra"
            style="width: 150px;margin-right: -9px;"
        >
            <el-option
                v-for="item in orderByOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
        <el-tabs
            v-model="currentGroupCate"
            @tab-click="handleTabClick"
            class="jacp-plain-tabs"
        >
            <el-tab-pane
                :label="value.label"
                :name="name"
                v-for="(value, name) in groupCateLabelMap"
                :key="name"
            />
        </el-tabs>
        <jacp-demand-group-mgr
            class="layoutview-group__wrapper"
            ref="demandGroupEl"
            :current-group-cate="currentGroupCate"
            v-model="searchCondition.groupId"
        />
        <plain-list
            :data="list"
            :current-page="currentPage"
            :page-size="data.size"
            :total="data.total"
            :height="510"
            @more="handleMoreClick"
            @on-item-click="handleItemClick"
            ref="list"
        >
            <template slot-scope="scope">
                <div class="jacp-plain-list-item-colunm left j-mgr24">
                    <p class="j-text-overflow j-f-large jacp-plain-list-item__link">
                        {{ scope.row.name }}
                    </p>
                </div>
                <div class="jacp-plain-list-item-colunm right no-shrink light">
                    <div
                        class="j-mgr16"
                        v-if="scope.row.tags && scope.row.tags.length"
                    >
                        <div>
                            <custom-tag
                                class="jacp-form-tags__item"
                                :key="item.id"
                                color="#2695F1"
                                v-for="item in scope.row.tags.slice(0,2)"
                            >
                                {{ item.name }}
                            </custom-tag><span v-if="scope.row.tags.length > 2">...</span>
                        </div>
                    </div>
                    <div v-if="activeTab !== 'draft'">
                        <div
                            class="j-mgr16 jacp-plain-list-item__link"
                            v-if="scope.row.space"
                            @click.stop.prevent="openLink({
                                                              name: 'teamspaceCardsList',
                                                              params: {
                                                                  spaceKey: scope.row.space.key,
                                                              }
                                                          },
                                                          scope.row.space.key)"
                        >
                            {{ scope.row.space.name | ellipsis(12) }}
                        </div>
                        <div
                            class="j-mgr16"
                            v-else
                        >
                            未分配
                        </div>
                    </div>
                    <div class="j-mgr16">
                        {{ scope.row.status | jacp-status-text }}
                    </div>
                    <div
                        class="j-mgr16"
                        v-show="activeTab !== 'draft'"
                    >
                        持续 {{ scope.row.bizStatusArchiveTime | getContinueTime }}天
                    </div>
                    <div
                        class="j-mgr16"
                        v-show="activeTab !== 'draft'"
                    >
                        提交时间 {{ scope.row.submitTime | jacp-local-time }}
                    </div>
                    <div
                        class="j-mgr16"
                        v-show="activeTab === 'draft'"
                    >
                        创建时间 {{ scope.row.cTime | jacp-local-time }}
                    </div>
                    <div
                        class="j-mgr16 jacp-plain-list-item__link"
                        v-show="activeTab === 'draft'"
                        @click.stop.prevent="openLink({
                            name: 'demandEdit',
                            params: {
                                id: scope.row.id,
                            },
                        }, scope.row.id)"
                    >
                        编辑
                    </div>
                    <div
                        class="j-mgr16 jacp-plain-list-item__link"
                        v-show="activeTab === 'draft'"
                        @click.stop.prevent="deleteDraft(scope.row)"
                    >
                        删除
                    </div>
                </div>
            </template>
        </plain-list>
    </layout-card>
</template>
<script>
import { DemandGroupModel, groupCateLabelMap } from '@/models/demandGroup';
import JacpDemandGroupMgr from '@/modules/demand/components/demandGroupMgr';
import mixinLayoutViewWidgetList from '@/mixins/mixin.LayoutViewWidgetListExtra';

export default {
    name: 'LayoutViewWidgetListDemandsGroup',
    components: { JacpDemandGroupMgr },
    mixins: [mixinLayoutViewWidgetList],
    data() {
        return {
            currentGroupCate: 'my',
            data: {},
            list: [],
            groupCateLabelMap,
            orderBy: 'ascByBizStatusArchiveTime',
            orderByOptions: [{
                value: 'ascByBizStatusArchiveTime',
                label: '持续时长由长到短',
            }, {
                value: 'descByBizStatusArchiveTime',
                label: '持续时长由短到长',
            }, {
                value: 'descBySubmitTime',
                label: '提交时间由近及远',
            }, {
                value: 'ascBySubmitTime',
                label: '提交时间由远及近',
            }],
            searchCondition: {
                groupId: 0,
                pageSize: 10000,
            },
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            if (this.$refs.demandGroupEl) {
                this.$refs.demandGroupEl.fetchData();
            }
        },
        getList() {
            if (!this.searchCondition.groupId) {
                this.list = [];
                return;
            }
            DemandGroupModel.getGroupList(this.searchCondition.groupId,
                this.searchCondition).then(this.setData);
        },

        /* 以下部分都和LayoutViewWidgetListDemands雷同 */
        setData(data) {
            this.data = Object.assign({}, this.data, data);
            this.list = this.data.records;
        },
        handleItemClick({ id }) {
            // 跳转到详情
            this.openLink({
                name: 'demandDetail',
                params: { id },
            }, id);
        },
        getContinueTime(time) {
            return Math.max(Math.ceil((new Date() - time) / (24 * 3600000)), 0) || 0;
        },
    },
};
</script>
<style lang="less">
.layoutview-group__wrapper{
    width: fit-content;
    max-width: 150px;
    margin: 22px 0 14px 0;
    .demand-group__btn{
        span{
            font-size: 1.17em;
            font-weight: bold;
        }
        .el-icon-arrow-down{
            font-weight: bold;
        }
    }
}
</style>
