<template>
    <layout-card
        title="待办需求"
        class="jacp-plain"
    >
        <el-select
            v-model="orderBy"
            placeholder="请选择"
            slot="extra"
            style="width: 150px;margin-right: -9px;"
        >
            <el-option
                v-for="item in orderByOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
        <el-tabs
            v-model="activeTab"
            @tab-click="handleTabClick"
            class="jacp-plain-tabs"
        >
            <el-tab-pane
                :label="getTabLabel(value, name)"
                :name="name"
                v-for="(value, name) in tabs"
                :key="name"
            />
        </el-tabs>
        <plain-list
            :data="list"
            :current-page="currentPage"
            :page-size="data.size"
            :total="data.total"
            :height="510"
            @more="handleMoreClick"
            @on-item-click="handleItemClick"
            ref="list"
        >
            <template slot-scope="scope">
                <div class="jacp-plain-list-item-colunm left j-mgr24">
                    <p class="j-text-overflow j-f-large jacp-plain-list-item__link">
                        {{ scope.row.name }}
                    </p>
                </div>
                <div class="jacp-plain-list-item-colunm right no-shrink light">
                    <div
                        class="j-mgr16"
                        v-if="scope.row.tags && scope.row.tags.length"
                    >
                        <div>
                            <custom-tag
                                class="jacp-form-tags__item"
                                :key="item.id"
                                color="#2695F1"
                                v-for="item in scope.row.tags.slice(0,2)"
                            >
                                {{ item.name }}
                            </custom-tag><span v-if="scope.row.tags.length > 2">...</span>
                        </div>
                    </div>
                    <div v-if="activeTab !== 'draft'">
                        <div
                            class="j-mgr16 jacp-plain-list-item__link"
                            v-if="scope.row.space"
                            @click.stop.prevent="openLink({
                                                              name: 'teamspaceCardsList',
                                                              params: {
                                                                  spaceKey: scope.row.space.key,
                                                              }
                                                          },
                                                          scope.row.space.key)"
                        >
                            {{ scope.row.space.name | ellipsis(12) }}
                        </div>
                        <div
                            class="j-mgr16"
                            v-else
                        >
                            未分配
                        </div>
                    </div>
                    <div
                        class="j-mgr16"
                        v-show="activeTab !== 'draft'"
                    >
                        持续 {{ scope.row.bizStatusArchiveTime | getContinueTime }}天
                    </div>
                    <div
                        class="j-mgr16"
                        v-show="activeTab !== 'draft'"
                    >
                        提交时间 {{ scope.row.submitTime | jacp-local-time }}
                    </div>
                    <div
                        class="j-mgr16"
                        v-show="activeTab === 'draft'"
                    >
                        创建时间 {{ scope.row.cTime | jacp-local-time }}
                    </div>
                    <div
                        v-show="activeTab === 'draft'"
                        class="j-mgr16 jacp-plain-list-item__link"
                        @click.stop.prevent="openLink({
                            name: 'demandEdit',
                            params: {
                                id: scope.row.id,
                            },
                        }, scope.row.id)"
                    >
                        编辑
                    </div>
                    <div
                        class="j-mgr16 jacp-plain-list-item__link"
                        v-show="activeTab === 'draft'"
                        @click.stop.prevent="deleteDraft(scope.row)"
                    >
                        删除
                    </div>
                </div>
            </template>
        </plain-list>
    </layout-card>
</template>
<script type="text/javascript">
import { MyZoneModel } from '@/models/myzone';
import Demands from '@/models/demands';
import demandActions from '@/models/demandActions';
import mixinLayoutViewWidgetList from '@/mixins/mixin.LayoutViewWidgetListExtra';

export default {
    name: 'LayoutViewWidgetListDemands',
    mixins: [mixinLayoutViewWidgetList],
    data() {
        return {
            data: {
                todoTotal: 0,
                doneTotal: 0,
            },
            list: [],
            totalCount: {},
            tabs: {
                pmToCommunicate: [2],
                pmCommunicating: [13],
                pmAccepted: [3],
                processing: [5, 11, 12],
                bizReject: [10],
                bizToBeAccepted: [9],
                pmRejected: [4],
                draft: [1],
            },
            activeTab: 'pmToCommunicate',
            searchCondition: {
                status: [2], // 默认： ‘待沟通’
            },
            orderBy: 'ascByBizStatusArchiveTime',
        };
    },
    mounted() {
        const { activeModuleMap } = this.$store.state.root;
        let archSpaceFlag = false;
        let archDemandFlag = false;
        activeModuleMap.forEach((item) => {
            if (item.code === 'archSpace') {
                archSpaceFlag = true;
            }
            if (item.code === 'archDemand') {
                archDemandFlag = true;
            }
        });
        if (archSpaceFlag && archDemandFlag) {
            this.handleTotalCount();
        }
    },
    methods: {
        getTabLabel(status, name) {
            const count = typeof this.totalCount[name] === 'undefined' ? '(0)' : `(${this.totalCount[name]})`;
            if (Array.isArray(status)) {
                return this.$t('jacp.demandStatus')[status[0]] + count;
            }
            return this.$t('jacp.demandStatus')[status.toString()] + count;
        },

        /* getContinueTime(time) {
            return Math.max(Math.ceil((new Date() - time) / (24 * 3600000)), 0) || 0;
        }, */
        handleItemClick({ id }) {
            // 跳转到详情
            this.openLink({
                name: 'demandDetail',
                params: { id },
            }, id);
        },
        setData(data) {
            this.data = Object.assign({}, this.data, data);
            // debugger;
            this.list = (this.data && this.data.records) || [];
        },
        handleTotalCount() {
            return MyZoneModel.getDemandsStatusCount().then((data) => {
                this.totalCount = Object.assign({}, data);
            });
        },
        getList() {
            const params = Object.assign({},
                this.searchCondition, { currentPage: this.currentPage });
            return MyZoneModel.getDemandsList(params).then(this.setData);
        },
        deleteDraft(row) {
            demandActions.logicDelete.call(Demands, row, () => {
                this.removeItem(row);
                if (+this.totalCount.draft >= 0) {
                    this.totalCount.draft -= 1;
                }
            });
        },
        removeItem({ id }) {
            this.data.records.forEach((item, index) => {
                if (item.id === id) {
                    this.data.records.splice(index, 1, Object.assign(item, {
                        hide: true,
                    }));
                }
            });
        },
    },
    computed: {
        // 草稿箱的特殊处理
        orderByOptions() {
            return this.activeTab !== 'draft' ? [{
                value: 'ascByBizStatusArchiveTime',
                label: '持续时长由长到短',
            }, {
                value: 'descByBizStatusArchiveTime',
                label: '持续时长由短到长',
            }, {
                value: 'descBySubmitTime',
                label: '提交时间由近及远',
            }, {
                value: 'ascBySubmitTime',
                label: '提交时间由远及近',
            }] : [{
                value: 'descByCTime',
                label: '创建时间由近及远',
            }, {
                value: 'ascByCTime',
                label: '创建时间由远及近',
            }];
        },
    },
    watch: {
        activeTab: {
            handler(val) {
                this.resetList();
                this.$nextTick(() => {
                    this.searchCondition.status = this.tabs[val];
                    // 草稿箱的特殊处理
                    this.orderBy = this.activeTab === 'draft' ? 'descByCTime' : 'ascByBizStatusArchiveTime';
                });
            },
        },
    },
};
</script>
