<template>
    <el-container
        :class="['demands-root', `demands-root__${dataIndex}`]"
    >
        <el-header
            height="40px"
            style="display: flex;justify-content: space-between;margin-top: var(--gutter--medium);padding: 0;"
        >
            <!-- tabs -->
            <local-demand-root-header
                v-model="activeName"
                :tab-panes="tabPanes"
                @tab-click="switchTab"
            />
            <!-- 右侧按钮组 -->
            <el-row
                type="flex"
                align="middle"
                class="j-mgr24"
            >
                <addDemandButton :authority="demandAuthority" />
                <jacp-dropdown-settings
                    label="需求"
                    module-key="demand"
                    :disabled="[dataIndex !== 'draft'? 'import':'']"
                    setting-type="list"
                    :un-show-setting-fields="unShowSettingFields"
                    @export="downloadDemands"
                    @import="importDemands"
                    @save-displayFields="(fields) => {
                        columns = fields || [];
                        filterDemand();
                    }"
                />
            </el-row>
        </el-header>

        <el-container
            class="demands-root-container"
            :class="{'demands-root-container-group': dataIndex === 'group'}"
        >
            <!-- 需求分组的侧边栏 -->
            <el-aside
                class="demands-root-aside"
                v-if="isGroupTab"
                width="216px"
            >
                <el-scrollbar style="height: 100%">
                    <demand-group-aside
                        @updateGroupCondition="updateGroupCondition"
                    />
                </el-scrollbar>
            </el-aside>
            <el-main
                v-if="dataIndex !== 'apportion'"
                :class="{'el-main-groupcontent': dataIndex === 'group'}"
            >
                <div
                    class="el-main-subdiv"
                    :class="{'el-main-subdiv-groupcontent': dataIndex === 'group'}"
                >
                    <!-- 支持复合高级查询的页面 -->
                    <template
                        v-if="dataIndex === 'mine'
                            || dataIndex === 'department'
                            || (dataIndex === 'product' && prdmAvailable)"
                    >
                        <JacpCompoundQuery
                            ref="compoundQuery"
                            :filter-personal-key="filterPersonalKey"
                            :on-query-change="$_handleQueryChange"
                            :categories="filterCategories"
                            :disabled-fields="disabledFields"
                            :initial-conditions="defaultQueryFilter"
                            :meta-filter-type-config="allFieldsConfig"
                            :get-item-value-key="(field) => {
                                if (!field) return undefined;
                                if (field.name === 'tags' || field.name === 'groupName') {
                                    return 'label';
                                }
                            }"
                            dark
                            @add="$_onMounedFilter"
                            @mount="$_onMounedFilter"
                            @query="filterDemand"
                        >
                            <el-row
                                v-if="dataIndex === 'product'"
                                slot="prefix"
                                type="flex"
                                align="middle"
                                style="flex-wrap: nowrap"
                            >
                                <jacp-prdm-productline-tree
                                    ref="productTree"
                                    v-model="product"
                                    :popper-append-to-body="true"
                                    @change="($event) =>{
                                        $_asyncProduct($event).then(() => {
                                            $refs.compoundQuery.tempQuery();
                                        });
                                    }"
                                />
                                <el-divider
                                    direction="vertical"
                                />
                            </el-row>
                            <el-row
                                v-if="dataIndex === 'department'"
                                slot="prefix"
                                type="flex"
                                align="middle"
                                style="flex-wrap: nowrap"
                            >
                                <!-- 部门条件变更后立刻触发查询 -->
                                <jacp-orgs-selector-tree-abs
                                    ref="orgTreeEl"
                                    class="demands-operates__dept-tree"
                                    style="height: 100%; margin: 0;"
                                    :has-footer="false"
                                    v-model="currentOrg.id"
                                    @change="($event) => {
                                        $_asyncCurrentOrg($event).then(() => {
                                            $refs.compoundQuery.tempQuery();
                                        });
                                    }"
                                >
                                    <template
                                        slot-scope="scope"
                                    >
                                        <div
                                            v-if="scope.data"
                                            style="font-size: 14px; font-weight: 500;"
                                        >
                                            {{ scope.data.name }}
                                        </div>
                                        <div
                                            v-else
                                            style="font-size: 14px; font-weight: 500;"
                                        >
                                            {{ $t('jacp.selectPlaceHolder') }}
                                        </div>
                                    </template>
                                </jacp-orgs-selector-tree-abs>
                                <el-divider
                                    direction="vertical"
                                />
                            </el-row>
                        </JacpCompoundQuery>
                    </template>
                    <!-- TODO: remove -->
                    <!-- 定制的查询字段 -->
                    <demands-filter
                        v-else
                        class="demands__filter"
                        ref="filterIns"
                        :type="dataIndex"
                        @filterDemand="filterDemand"
                    />
                    <!-- 列表展示与页码 -->
                    <div
                        class="demands__listwrap"
                        :class="{'demands__listwrap__short1': useCompoundQuery,
                                 'demands__listwrap__group': dataIndex === 'group'}"
                    >
                        <demands-list
                            v-if="dataIndex !== 'apportion'"
                            class="demands__list"
                            :class="{'demands__list__short1': useCompoundQuery,
                                     'demands__list__group': dataIndex === 'group'}"
                            ref="demandListEl"
                            :data="activedList"
                            :type="dataIndex"
                            :sortable="isSortable && isGroupTab"
                            :org-columns="columns"
                            :sort-condition="sortCondition"
                            :is-group="isGroupTab"
                            :is-expand-all="defaultExpandStatus"
                            :batch="batchFlag"
                            :batch-list="batchList"
                            :can-add="demandAuthority.valid"
                            @drag-end="updateIndex"
                            @sort-change="sortByColumns"
                            @toggle-children="toggleExpandStatus"
                            @on-demand-focus="showDetail"
                            @toggle-expand-all="toggleExpandAll"
                            @refresh-list="refreshCurrent"
                            @hide-detail="backToIndex"
                        />
                        <div class="demands__listfooter">
                            <jacp-list-batch
                                class="demands__batchop"
                                :class="{'demands__batchop__unvisible': activedList.length === 0}"
                                :cards-list="activedList"
                                :batch-list="batchList"
                                :batch-flag="batchFlag"
                                @openBatch="openBatch"
                                @closeBatch="closeBatch"
                                @checkAll="checkAll"
                            >
                                <div slot="options">
                                    <el-cascader
                                        placeholder="操作"
                                        v-model="action"
                                        style="width: 100px;"
                                        size="mini"
                                        :show-all-levels="false"
                                        :options="options"
                                        :props="{value: 'groupId', label: 'groupName'}"
                                        @change="batchAction"
                                    />
                                </div>
                            </jacp-list-batch>
                            <div style="position: relative;">
                                <el-pagination
                                    class="demands__pagenation"
                                    :class="{'demands__pagenation__unvisible': batchFlag}"
                                    @size-change="handleSizeChange"
                                    @current-change="refreshCurrent(true)"
                                    :page-size.sync="activedPagenation.size"
                                    :page-sizes="pageNum"
                                    :page-count="demandListPageCount()"
                                    :current-page.sync="activedPagenation.current"
                                    :total="maxPageCount ? undefined : activedPagenation.total "
                                    layout="prev, pager, next, sizes, jumper, slot"
                                >
                                    <span style="line-height: 32px; font-weight: 400; color: #606265;">
                                        ，共{{ activedPagenation.total }}条
                                    </span>
                                </el-pagination>
                                <jacp-text
                                    style="position:absolute;right: 0;bottom: -16px;"
                                    type="secend"
                                    size="12"
                                    v-if="maxPageCount && (activedPagenation.total / activedPagenation.size) > maxPageCount"
                                >
                                    *列表内仅显示前100页数据，如需查看更多，请导出
                                </jacp-text>
                            </div>
                        </div>
                    </div>
                </div>
            </el-main>
            <!-- 成本分摊列表 -->
            <apportion-list
                ref="apportionListEl"
                v-show="dataIndex === 'apportion'"
                @on-demand-focus="showDetail"
            />
        </el-container>
        <!-- 右侧滑出容器，渲染router-view -->
        <jacp-off-canvas
            ref="offCanvasEl"
            :before-hide="beforeOffCanvasHide"
            @hide="backToIndex"
        >
            <router-view
                :demand.sync="focusedDemand"
                ref="demandOffCanvasContentEl"
                @refresh-list="refreshCurrent"
                @update-focused="updateFocusedDemand"
                @to-my-submit="toMySubmit"
            />
        </jacp-off-canvas>
        <jacp-version :lazy-load="false" />
        <el-dialog
            title="提示"
            width="30%"
            custom-class="j-dialog"
            :visible.sync="draftSubmitDialogVisible"
            :before-close="handleDraftSubmitDialogClose"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
        >
            <el-progress
                :text-inside="true"
                :stroke-width="26"
                :percentage="batchSubmitPercentage"
            />
            <div v-html="failedDraftsHTML" />
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button
                    v-if="batchSubmitPercentage !== 100"
                    @click="handleDraftSubmitDialogClose"
                >
                    {{ $t('jacp.button.cancelBtn') }}
                </el-button>
                <el-button
                    v-else
                    type="primary"
                    @click="handleDraftSubmitDialogClose"
                >
                    {{ $t('jacp.button.confirmBtn') }}
                </el-button>
            </span>
        </el-dialog>
    </el-container>
</template>

<script>
import sortBy from 'lodash/sortBy';
import isPlainObject from 'lodash/isPlainObject';
import remove from 'lodash/remove';
import LocalDemandRootHeader from '$module/components/layout/demandRootHeader';

import demandsList from './components/demandsList';
import addDemandButton from './components/addDemandButton';
import { demandsExcelTemplateUrl } from '@/models/config';
import apportionList from './components/apportionList';
import DemandsModel from '@/models/demands';
import { DemandGroupModel } from '@/models/demandGroup';
import DemandsFilter from './components/demandsFilter';
import PersonalModel from '@/models/personal';
import MixinChildren from './components/mixin.children';
import MixinImport from '@/modules/_components/mixin.import';
import demandGroupAside from './components/demandGroupAside';
import Dialog from '@/models/dialog';
import importDemand from './components/importDemand';

import MixinQuery from './components/mixin.demandQuery';


const tabPanes = ['mine', 'department', 'product', 'draft', 'apportion', 'group'];

// 追加一个【产品的】的查询条件
/* const productModeMixin = {
    data() {
        return { product: null, productCurrentConditions: {} };
    },
    methods: {
        async setProduct(conditions = {}) {
            if (conditions.productId) {
                const node = await this.$refs.productTree.getNode(conditions.productId);
                this.product = node;
            } else {
                this.product = null;
            }
        },
        loadProductList(conditions) {
            if (conditions) {
                this.productCurrentConditions = conditions;
            }
            setTimeout(() => {
                this.$refs.productList.load();
            }, 1);
        },
    },
}; */

export default {
    name: 'DemandIndex',
    mixins: [MixinImport, MixinChildren, MixinQuery],
    components: {
        demandsList,
        DemandsFilter,
        apportionList,
        demandGroupAside,
        addDemandButton,
        LocalDemandRootHeader,
    },
    mounted() {
        this.loadColumns();
        this.importTemplateUrl = demandsExcelTemplateUrl;
        this.importUrl = 'v1/demands/import';
        this.$on('import-success', this.refreshCurrent);
        this.getDemandAuthority();
        this.initOffCanvas();
        this.checkApportionPermission();
    },
    data() {
        const groupPageSize = +localStorage.getItem('jacp_demand_list_size') || 50;
        const list = {};
        const pageData = {};
        const { tab } = this.$route.params;
        const condition = {};
        tabPanes.forEach((item) => {
            pageData[item] = {
                total: 0,
                current: 1,
                size: 10,
            };
            if (item === 'group') {
                pageData[item].size = groupPageSize;
            }
            list[item] = [];
            condition[item] = {};
        });
        return {
            tabPanes,
            activeName: tab || 'mine',
            demandAuthority: {
                valid: false, // 是否有权限提报需求
            },
            focusedDemand: {}, // 详情
            columns: [],
            batchFlag: false,
            batchList: [],
            sortMode: false,
            groupOptions: [],
            list,
            groupModeCondition: {
                orderField: 'seq',
                orderType: 'asc',
            },
            sortCondition: {},
            condition,
            pageData,
            pageNum: [10, 20, 50],
            options: [{
                groupId: '0',
                groupName: '加入分组',
            }],
            isSortable: false,
            action: [],
            // apportionTabVisible: false,
            draftSubmitDialogVisible: false,
            draftSubmitsAll: 0,
            draftSubmitsFullfilled: 0,
            draftSubmitsFailed: 0,
            failedDrafts: [],
        };
    },
    computed: {
        unShowSettingFields() {
            const unShowFields = [];
            let currentOrg = {};
            const condition = this.condition[this.dataIndex];
            // 扩展字段：需求管理 && 部门的 && 部门受理的 && 一级部门及以下 (仅仅是部门受理)
            const isAcceptanceDepartment = condition?.filters?.find(filter => filter.column === 'direction'
            && Array.isArray(filter.value)
            && filter.value.includes('0')
            && filter.value.length === 1);
            if (isAcceptanceDepartment) {
                // 取一下部门id判断一下level
                const orgId = (condition?.filters?.find(filter => filter.column === 'orgnazitionId') || {}).value;
                currentOrg = this.$refs.orgTreeEl?.orgMap[orgId];
            }
            // 有且仅有 "部门受理的" 导出 '扩展属性'
            if (this.dataIndex !== 'department' || !isAcceptanceDepartment || (currentOrg && currentOrg.level <= 1)) {
                unShowFields.push('extendedFields');
            }
            return unShowFields;
        },
        // 检查是否有产品管理的权限
        prdmAvailable() { return this.$store.getters['root/getActiveModuleByCode']('prdm'); },
        isGroupTab() {
            return this.dataIndex === 'group';
        },
        dataIndex: {
            get() {
                return this.activeName;
            },
            set(val) {
                this.activeName = val;
            },
        },
        activedList() {
            return this.list[this.dataIndex] || [];
        },
        activedPagenation() {
            return this.pageData[this.dataIndex];
        },
        groupData() {
            return this.$store.state.demands.groups;
        },

        currentGroupId() {
            return this.condition.group.groupId;
        },
        confirmBeforeHideOffCanvas() {
            const { meta } = this.$route;
            if (!meta) return false;
            return meta.leaveConfirm;
        },
        pageConditions() {
            return {
                queryType: this.dataIndex,
            };
        },
        batchSubmitPercentage() {
            const {
                draftSubmitsAll: all,
                draftSubmitsFullfilled: fullfilled,
                draftSubmitsFailed: failed,
            } = this;

            return all > 0
                ? Math.trunc((fullfilled + failed) / all * 100)
                : 100;
        },
        failedDraftsHTML() {
            const { failedDrafts, activedList } = this;
            return failedDrafts.map(({ id, msg }) => {
                const demand = activedList.find(item => item.id === id);
                return `
                    <div style="line-height: 20px; font-size: 12px">
                        <span>${demand.demandCode}</span>
                        :
                        <span>${msg}</span>
                    </div>
                `;
            }).join('');
        },
        useCompoundQuery() {
            return ['mine', 'department', 'product'].includes(this.dataIndex);
        },
        maxPageCount() {
            if (this.useCompoundQuery) {
                return 100;
            }
            return undefined;
        },
    },
    methods: {
        demandListPageCount() {
            const pageCount = Math.ceil(this.activedPagenation.total / this.activedPagenation.size);
            return pageCount > this.maxPageCount ? this.maxPageCount : pageCount;
        },
        switchTab(targetFilter) {
            this.options = [{
                groupId: '0',
                groupName: '加入分组',
                children: this.groupOptions,
            }];

            this.$router.push({
                name: this.$route.name,
                params: {
                    tab: this.dataIndex,
                    targetFilter: typeof targetFilter === 'string' ? targetFilter : undefined,
                },
                query: {
                    ...this.$route.query,
                },
            });
            this.closeBatch();
            const updateConditionAndRefresh = () => {
                const type = this.dataIndex;
                this.pageData[type].current = 1;
                if (type === 'group') {
                    this.updateGroupCondition();
                }
            };
            this.pageNum = this.pageNum.slice(0, 3);
            switch (this.dataIndex) {
            case 'product':
                break;
            case 'group':
                this.pageNum = this.pageNum.concat([200, 500]);
                updateConditionAndRefresh();
                break;
            case 'draft':
                this.options.push({
                    groupId: '1',
                    groupName: '批量提交',
                }, {
                    groupId: '2',
                    groupName: '批量删除',
                });
                updateConditionAndRefresh();
                this.refreshCurrent();
                break;
            default:
                updateConditionAndRefresh();
                break;
            }
        },
        beforeOffCanvasHide() {
            // 先触发router变化，根据router是否变化再处理是否关闭offCanvas
            this.backToIndex();
            // 阻止来自点击外侧的关闭
            return !this.confirmBeforeHideOffCanvas;
        },
        toMySubmit() {
            const mySubmit = '我提交的';
            if (this.dataIndex === 'mine') {
                this.$refs.compoundQuery
                    .$refs.filter
                    .$refs.queryFilter
                    .setCurrentTabByName(mySubmit);
            } else {
                this.dataIndex = 'mine';
                this.switchTab(mySubmit);
            }
        },
        updateFocusedDemand({ key, value }) {
            if (key === 'pipeline') { // value is detail
                Object.assign(this.focusedDemand, value, {
                    submitTime: value.submitTime || value.cTime,
                });
            } else if (this.sortCondition.orderField === key || key === 'group') {
                this.refreshCurrent();
            } else {
                this.focusedDemand[key] = value;
            }
        },
        updateGroupCondition($event) {
            if (this.isGroupTab) {
                if ($event) {
                    this.$set(this.condition[this.dataIndex], 'groupId', +$event?.groupId);
                } else if (this.$route.query) {
                    this.$set(this.condition[this.dataIndex], 'groupId', this.$route.query.groupId);
                }
                if (this.$refs.filterIns) {
                    this.$refs.filterIns.clearAndRefreshGroupForm();
                }
                this.isSortable = !!$event?.isSortable;
            }
        },
        // 获取当前登录用户提报需求权限
        getDemandAuthority() {
            DemandsModel.getDemandAuthority().then((data) => {
                // 初始化是否可提报需求标识
                Object.assign(this.demandAuthority, data);
            });
        },
        checkApportionPermission() {
            // 有权限则加载分摊确认数据，并更新store。
            DemandsModel.hasApportionPermission().then((data) => {
                // this.apportionTabVisible = data;
                this.$store.commit('demands_update', {
                    apportionTabVisible: data,
                });
                if (data) {
                    this.$refs.apportionListEl.filterList();
                }
            });
        },

        /* goto(urlName) {
            this.$router.push({ name: urlName });
        }, */
        getList(type, backTop = false) { // 非排序模式
            const {
                isGroupTab, isSortable, sortCondition,
            } = this;
            const condition = Object.assign({},
                this.condition[type],
                sortCondition);
            if (type === 'group') {
                Object.assign(condition, this.groupModeCondition);
            }
            return DemandsModel.getList(Object.assign({
                queryType: type,
                pageSize: this.pageData[type].size,
                current: this.pageData[type].current,
            }, condition)).then((data) => {
                this.pageData[type].total = data.total;
                this.list[type] = this.formatDemandsList({
                    list: data.records,
                    isSortable: isGroupTab && isSortable,
                    isGroup: isGroupTab,
                });
                if (!this.$refs.demandListEl) return;
                this.$nextTick(() => {
                    if (this.dataIndex !== 'apportion') {
                        this.$refs.demandListEl.setExpandAll(this.defaultExpandStatus);
                    }
                    if (backTop && this.$refs.demandListEl && this.$refs.demandListEl.$refs.demandsListTablewrapperEl) {
                        // 每次翻页后不记忆之前位置，返回顶部
                        this.$refs.demandListEl.$refs.demandsListTablewrapperEl.scroll(0, 0);
                    }
                });
            });
        },
        loadColumns() {
            return PersonalModel.getListDisplayFields('demand', true).then((data) => {
                // 这里每次都要保证顺序
                this.columns = sortBy(data, 'index');
            });
        },
        refreshCurrent(backtop = false) {
            // if (this.dataIndex === 'product') {
            //     return this.loadProductList();
            // }
            return this.loadColumns().then(() => {
                this.getList(this.dataIndex, backtop);
                if (this.batchFlag) {
                    // 清空批量数据
                    this.batchList = [];
                    [].forEach.call(this.$refs.demandListEl.$el.querySelectorAll('tbody tr'), (tr) => {
                        const trObj = tr;
                        trObj.className = trObj.className.replace('checkedRow', '');
                    });
                }
            });
        },
        draftInit() {
            // 清除草稿错误样式
            this.$refs.demandListEl.errorRows = [];
        },
        updateIndex({
            oldIndex, newIndex, demand, data,
        }) {
            const i = data.indexOf(demand);
            // 下移，取前一条的index
            const target = oldIndex < newIndex ? data[i - 1] : data[i + 1];
            const targetId = (target || {}).id;
            const groupData = this.isGroupTab ? {
                groupId: this.currentGroupId,
                // 这个东西没有要用？
                // category: this.groupData.find(o => +o.groupId === +this.currentGroupId).category,
            } : {};
            return DemandsModel.updateIndex(
                demand.id,
                targetId,
                groupData,
            ).then(() => {
            // 排序完了以后要把子需求也一起拖过来，直接处理数组总有各种异常
                if (this.isGroupTab) {
                    this.refreshCurrent();
                }
            }).catch((error) => {
                // 分组如果出现400异常代表没有权限查看，不刷列表，其他情况都刷列表
                if (this.isGroupTab && error.response?.data.code === 401) {
                    // 400异常的时候刷一下分组列表，及时干掉失效的协同分组,但是不能刷需求列表，因为没有权限了
                    this.$store.dispatch('demands_group_fetchData');
                    return;
                }
                this.refreshCurrent();
            });
        },
        // 这个排序是点击表头字段排序
        sortByColumns({ orderField, orderType }) {
            this.sortCondition = orderType ? Object.assign({}, {
                orderField,
                orderType,
            }) : {};
            this.refreshCurrent();
        },
        showDetail(demand) {
            const query = {
                // demandId: demand.id,
            };
            if (this.isGroupTab) {
                Object.assign(query, {
                    groupId: this.$route.query.groupId || this.condition.group.groupId,
                });
            }
            this.focusedDemand = demand;
            // 改变地址参数
            this.$router.replace({
                name: 'demandDetail',
                params: {
                    id: demand.id,
                },
                query,
            });
        },
        backToIndex() {
            // this.demandDetailsVisible = false;
            this.focusedDemand = {};
            this.$router.replace({
                name: 'demandsIndex',
                params: {
                    tab: this.dataIndex,
                },
            });
        },
        filterDemand(condition) {
            const type = this.dataIndex;
            this.pageData[type].current = 1;
            if (isPlainObject(condition)) {
                if (type === 'group') {
                    Object.assign(this.condition[type], JSON.parse(JSON.stringify(condition)));
                } else {
                    this.condition[type] = JSON.parse(JSON.stringify(condition));
                }
            }

            // this.columns = condition.fieldsFilter || [];
            this.refreshCurrent();
        },
        columnFixedSetting() {
            this.$refs.demandListEl.changeColumnFix();
        },
        downloadDemands() {
            const MAX_SYNC_EXPORT_COUNT = 1000;
            const exportMode = this.activedPagenation.total > MAX_SYNC_EXPORT_COUNT ? 1 : 0;
            const params = {
                queryType: this.dataIndex,
                ...this.condition[this.dataIndex],
                ...this.sortCondition,
                exportMode,
            };
            if (exportMode) {
                this.$notify({
                    title: '导出提示',
                    message: '哇，您导出的需求数量较大，系统将会以附件形式发送到您邮件，请您耐心等待哦！',
                    type: 'warning',
                    duration: 0,
                });
            } else {
                this.$notify({
                    title: '导出提示',
                    message: '开始打包导出需求，打包成功后会自动下载，请您耐心等待哦！',
                    type: 'success',
                });
            }
            if (params.queryType !== 'group') {
                delete params.groupId;
            } else {
                Object.assign(params, this.groupModeCondition);
            }
            DemandsModel.exportDemands(params);
        },
        importDemands() {
            return Dialog.confirm({
                title: '导入需求',
                slot: importDemand,
                confirmDisplay: false,
                cancelBtnText: '关闭',
                slotProps: {
                },
            }).then(() => this.refreshCurrent())
                .catch(() => this.refreshCurrent());
        },
        openBatch() {
            this.batchFlag = true;
            this.$refs.demandListEl.errorRows = [];
        },
        closeBatch() {
            this.batchFlag = false;
            this.batchList = [];
            if (this.$refs.demandListEl) {
                [].forEach.call(this.$refs.demandListEl.$el.querySelectorAll('tbody tr'), (tr) => {
                    const trObj = tr;
                    trObj.className = trObj.className.replace('checkedRow', '');
                });
                this.$refs.demandListEl.errorRows = [];
            }
        },
        checkAll(flag) {
            this.batchList = [];
            if (flag) {
                this.activedList.forEach(demand => this.batchList.push(demand.id));
            }
            [].forEach.call(this.$refs.demandListEl.$el.querySelectorAll('tbody tr'), (tr) => {
                const trObj = tr;
                if (flag) {
                    if (trObj.className.indexOf('checkedRow') < 0) {
                        trObj.className = `${trObj.className} checkedRow`;
                    }
                } else {
                    trObj.className = trObj.className.replace('checkedRow', '');
                }
            });
        },
        batchAction(action) {
            const index = action[0];
            if (index === '0') {
                const group = this.options[index].children.filter(item => item.groupId === action[1]);
                this.batchAddGroup(group[0]);
            } else if (index === '1') {
                this.batchSubmitDemands();
            } else if (index === '2') {
                this.batchDeletedDemands();
            }
            this.action = [];
        },
        batchAddGroup(group) {
            if (this.batchList.length < 1) {
                this.$alert('请选择批量操作的需求！', '提示', {
                    confirmButtonText: '确定',
                });
                return;
            }
            this.$confirm(`此操作将添加${this.batchList.length}个需求到「${group.groupName.replace('【协】  ', '')}」分组, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                DemandGroupModel.batchJionGroup(group.groupId, this.batchList).then(() => {
                    this.refreshCurrent();
                    this.closeBatch();
                    this.$notify({
                        title: '已加入分组！',
                        type: 'success',
                        duration: 2000,
                    });
                });
            });
        },
        defaultNoData(dataList) {
            if (dataList && dataList.length === 0) {
                dataList.push({
                    groupName: '暂无数据',
                    groupId: -1,
                    disabled: 'true',
                });
            }
            return dataList;
        },
        handleDraftSubmitDialogClose() {
            this.draftSubmitDialogVisible = false;
            this.draftSubmitsAll = 0;
            this.draftSubmitsFullfilled = 0;
            this.draftSubmitsFailed = 0;
            this.failedDrafts = [];
            this.refreshCurrent();
        },
        batchSubmitDemands() {
            if (this.batchList.length < 1) {
                this.$alert('请选择批量操作的需求！', '提示', {
                    confirmButtonText: '确定',
                });
                return;
            }

            this.draftSubmitDialogVisible = true;
            this.draftSubmitsAll = this.batchList.length;

            this.batchList.forEach((demandId) => {
                DemandGroupModel.submitDemand(demandId)
                    .then(() => {
                        this.draftSubmitsFullfilled += 1;
                    })
                    .catch(({ message }) => {
                        this.draftSubmitsFailed += 1;
                        this.failedDrafts.push({
                            id: demandId,
                            msg: message,
                        });
                        this.$refs.demandListEl.errorRows.push(demandId);
                    });
            });
        },
        batchDeletedDemands() {
            if (this.batchList.length < 1) {
                this.$alert('请选择批量操作的需求！', '提示', {
                    confirmButtonText: '确定',
                });
                return;
            }
            this.$confirm(`此操作批量删除${this.batchList.length}个需求，如果有子需求也一并删除。是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                DemandGroupModel.batchDeletedDemands(this.batchList).then((result) => {
                    this.closeBatch();
                    if (result && result.length > 0) {
                        this.$alert('批量提交中有非草稿或者取消状态的需求，无法删除，系统已用红框标注，请检查修正后再次提交，谢谢！', '错误提示', {
                            confirmButtonText: '我知道了',
                        });
                        // 高亮显示错误行
                        this.$refs.demandListEl.errorRows = result;
                    } else {
                        this.refreshCurrent();
                        this.$notify({
                            title: '已删除成功！',
                            type: 'success',
                            duration: 2000,
                        });
                    }
                });
            });
        },
        getAdditionColumns() {
            const { isGroupTab, columns } = this;
            if (isGroupTab && !columns.some(o => o.name === 'seq')) {
                let position = 0;
                columns.forEach((o, index) => {
                    // 放在需求名称后面
                    if (o.name === 'name') {
                        position = index + 1;
                    }
                });
                columns.splice(position, 0, {
                    name: 'seq',
                    show: 1,
                    index: -1,
                    label: '协同排序号',
                });
            }
            if (!isGroupTab) {
                columns.forEach((o, index) => {
                    if (o.name === 'seq') {
                        columns.splice(index, 1);
                    }
                });
            }
        },
        initOffCanvas() {
            // 兼容一下改版之前的访问url
            const {
                $refs, $route,
            } = this;
            const { showOffCanvas } = $route.meta;
            if (!$refs.offCanvasEl) return;
            if (showOffCanvas) {
                $refs.offCanvasEl.open();
            } else {
                $refs.offCanvasEl.setOpenStatus(false);
            }
        },
        handleSizeChange(size) {
            if (this.activeName === 'group') {
                // 设置需求分组的分页数据
                localStorage.setItem('jacp_demand_list_size', size);
            }
            this.activedPagenation.size = size;
            this.refreshCurrent();
        },
        buildTab() {
            const productTabExisted = this.tabPanes.includes('product');
            if (!this.prdmAvailable && productTabExisted) {
                remove(this.tabPanes, 'product');
            }
            if (this.prdmAvailable && !productTabExisted) {
                const targetIndex = this.tabPanes.indexOf('department') + 1;
                this.tabPanes.splice(targetIndex, 0, 'product');
            }
        },
    },
    watch: {
        focusedDemand: {
            handler(val) {
                if (!val) return;
                const targetDemand = this.activedList.find(o => o.id === val.id);
                if (targetDemand) {
                    Object.assign(targetDemand, val);
                }
            },
        },
        columns: 'getAdditionColumns',
        activeName: 'getAdditionColumns',
        $route: {
            deep: true,
            handler: 'initOffCanvas',
        },
        '$store.state.demands.groups': {
            handler(groups) {
                const dataList = groups || [];
                const myGroups = dataList.filter(group => group.category !== 1);
                const myGroupsNoCoo = [];
                const myGroupsWithCoo = [];
                const myGroupsAsCoo = [];
                myGroups.forEach((item) => {
                    const group = Object.assign({}, item);
                    if (group.category === 2) {
                        group.groupName = `【协】  ${group.groupName.replace('【协】  ', '')}`;
                        myGroupsAsCoo.push(group);
                    } else if (group.category === 0 && group.cooperatorCount > 0) {
                        group.groupName = `【协】  ${group.groupName.replace('【协】  ', '')}`;
                        myGroupsWithCoo.push(group);
                    } else if (group.category === 0 && group.cooperatorCount === 0) {
                        myGroupsNoCoo.push(group);
                    }
                });
                // 按顺序拼接分组
                const tmpList = myGroupsWithCoo.concat(myGroupsAsCoo, myGroupsNoCoo);
                this.groupOptions = this.defaultNoData(tmpList);
                // 补充空值处理
                this.options[0].children = this.groupOptions;
            },
            immediate: true,
        },
        prdmAvailable: {
            immediate: true,
            handler: 'buildTab',
        },
    },
    beforeRouteUpdate(to, from, next) {
        if (from.params.tab !== to.params.tab && this.$refs.filterIns) {
            if (this.dataIndex) {
                if (this.dataIndex !== 'group') {
                    this.isSortable = false;
                }
            }
        }
        next();
    },
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            vm.switchTab();
        });
    },
};
</script>

<style lang="less">
@contentHeight:calc(~"100vh - 98px - 16px");
// FIXME: 这个页面的class命名杂乱无章，暂时不敢动，有机会的时候要整体调整一下
@import '~@/theme/var.less';
    .demands-root {
        margin: 0;
        padding: 0;
        background-color: #fff;
        &__group{
            // 在需求分组下将筛选条整体放大
            .demands-operates{
                height: 64px;
                .el-button--default,
                .demands-filter__label{
                    font-size: 14px;
                }
                .el-button--default,
                .el-input__inner,
                .el-select__input{
                    height: 32px !important;
                }
            }
        }
       .jacp-root-header{
            padding: 0;
            border-bottom: none;
       }
        &-aside {
            background-color: #fafbfc;
            height: @contentHeight;
            & .el-scrollbar__wrap {
                overflow-x: hidden;
            }
            .el-menu-item {
                padding-right: 20px;
                vertical-align: baseline;
            }
        }
        .el-main {
            padding: 0;
            height: @contentHeight;
            &-groupcontent {
                min-width: 0;
            }
            &-subdiv {
                height: 100%;
                &-groupcontent {
                    min-width: 1056px;
                    height: 100%;
                    border-left: var(--border--hr);
                }
            }
        }
        .demands-root-container-group{
            border-top: var(--border--hr);
        }
        .demands {
            &__listwrap {
                padding: 0 var(--gutter--medium);
                height: calc(100% - 48px - 1px);
                &__short1 {
                    height: calc(100% - 50px);
                }
                &__group {
                    height: calc(100% - 65px);
                }
            }
            &__list {
                margin: 0;
                height: calc(100% - 64px);
                overflow-x: auto;
                &__short1 {
                    height: calc(100% - 64px);
                }
                &__group {
                    height: calc(100% - 64px);
                }
            }
            &__listfooter {
                height: 64px;
                padding: 16px 0;
                display: flex;
                flex-flow: row nowrap;
                justify-content: space-between;
            }
            &__pagenation {
                height: 100%;
                margin: 0;
                padding: 0;
                line-height: 30px;
                float: right;
                & > .el-pager {
                    height: 100%;
                    line-height: 30px;
                    & > .number {
                        height: 100%;
                        line-height: 30px;
                        padding: 0;
                        width: 32px;
                        min-width: 32px;
                        border: 1px solid #DCDFE6;
                        border-radius: 4px;
                        background-color: #fff;
                        margin: 0 5px;
                        font-size: 12px;
                        color: #303133;
                        &.active {
                            color: #2695F1;
                            border: 1px solid #2695F1;
                        }
                    }
                }
                & .btn-prev, .btn-next {
                    height: 100%;
                    line-height: 30px;
                    border: 1px solid #DCDFE6;
                    border-radius: 4px;
                    background-color: #fff;
                    margin: 0 5px;
                    width: 32px;
                    min-width: 30px;
                    padding: 0 6px;
                    font-size: 12px;
                }
                & > .el-pagination__sizes {
                    height: 100%;
                    & .el-input__inner {
                        height: 32px;
                        line-height: 30px;
                        border-radius: 4px;
                    }
                }
                & > .el-pagination__total {
                    height: 32px;
                    line-height: 32px;
                    margin-right: 8px;
                }
                & > .el-pagination__jump {
                    margin-left: 0;
                    height: 100%;
                    border-radius: 4px;
                    & .el-input__inner {
                        height: 32px;
                        line-height: 30px;
                        border-radius: 4px;
                    }
                }
                &__unvisible {
                    display: none;
                }
            }
            &__batchop {
                position: static;
                padding: 0;
                &__unvisible {
                    visibility: hidden;
                }
                // & .el-button {
                //     // height: 32px;
                //     border-radius: 4px;
                // }
            }
        }
    }
</style>
