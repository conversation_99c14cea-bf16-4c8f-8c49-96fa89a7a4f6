import router from '$platform.router';

// 目前只有需求详情页有挂载点，保持各处详情页挂载点的context是一致的
export function getExtensionPointContextParams(demand) {
    const {
        id: demandId, name: demandName, demandCode, status: demandStatus,
    } = demand;
    const url = router.resolve({
        name: 'demandDetail',
        params: { id: router.currentRoute.params.id },
    });
    return {
    // TODO: 这个参数结构要废弃
        domain: 'demands', // 域
        entityData: {
            id: demandId,
            demandCode,
            name: demandName,
            status: demandStatus,
        },
        callbackUrl: new URL(url.href, window.location.href).href,
        // 以下为新结构，上面的结构在项目管理改造完毕后废弃
        demandId,
        demandCode,
        demandName,
        demandStatus,
        demandLink: new URL(url.href, window.location.href).href,
    };
}
