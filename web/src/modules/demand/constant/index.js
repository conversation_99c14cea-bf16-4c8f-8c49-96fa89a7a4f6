import { FILTER_TYPE_CATE } from '@/components/queryFilter/constant';

/*
“我的”
| 字段名称 | 英文代码 |
| :--- | :--- |
| 关键词 | keyword |
| 需求状态 | status |
| 当前操作人 | processors |
| 需求人 | demanders |
| 标签 | tags |
| 团队空间 | spaceId |
| 受理人部门 | recipientOrg |
| 需求人部门 | demanderOrg |
| 提交人部门 | proposerOrg |
| 成本分摊部门 | apportionOrg |
| 当前操作人部门 | processorOrg |
| 涉及研发平台 | apps |
| 部门与需求关系（类型） | direction |
| 业务优先级 | priorityId |
| 需求类型 | demandTypeIds |
| 紧急需求 | urgent |
| 收益分类 | revenueIdList |
| 需求来源 | demandSourceId |
| 提交人 | proposers |
| 受理人 | recipient |
| 关注人 | conserners |
| 相关人 | relationUser |
| 提交时间 | submitDate |
| 期望上线时间 | expectedReleaseDate |
| 验收通过时间 | acceptTimes |
| 进入已受理时间 | processTimes |
| 状态持续时长 | statusKeepTime |
| 研发完成时间 | cardCompleteTimes |
| 收益验证时间 | evaluationTime |
| 收益验证截止日期 | revenueEndDate |
| 所属迭代 | sprintName |
| 收益验证状态 | evaluated |
| 渠道审核风险 | channelRisk |
| 收益验证评价 | evaluation |
| 卡片状态 | |
-------
| 卡片阶段 | stageIdList |
| 需求分组 | groupName | */

export const filterCategories = [{
    value: 'basic',
    label: '基础条件',
    data: [
        'orgnazitionId',
        'direction',
        'keyword',
        'status',
        'processors',
        'demanders',
        'tags',
    ],
}, {
    value: 'teams',
    label: '部门/团队',
    data: [
        'spaceId',
        'recipientOrg',
        'demanderOrg',
        'proposerOrg',
        'apportionOrg',
        'processorOrg',
        'apps',
    ],
}, {
    value: 'types',
    label: '类型/级别',
    data: [
        'priorityId',
        'demandTypeIds',
        'urgent',
        'revenueIdList',
        'demandSourceId',
    ],
}, {
    value: 'role',
    label: '角色',
    data: [
        'relationUser',
        'proposers',
        'recipient',
        'conserners',
    ],
}, {
    value: 'date',
    label: '日期/时间',
    data: [
        'submitDate',
        'expectedReleaseDate',
        'acceptTimes',
        'processTimes',
        'statusKeepTime',
        'cardCompleteTimes',
        'evaluationTime',
        'revenueEndDate',
    ],
}, {
    value: 'other',
    label: '其他',
    data: [
        'groupName',
        'sprintName',
        'stageIdList',
        'channelRisk',
        'evaluated',
        'evaluation',
        'productId',
        'pmpProjectIds',
    ],
}];

// FIXME: 每增加一个字段还要在前端维护一些映射？好麻烦
// TODO:
// 一迭代先硬编码，不提供界面编辑
// 二迭代考虑是否有界面编辑

export const allFieldsConfig = {
    orgnazitionId: {
        filterType: [
            { value: 'EQ', label: '=' },
        ],
    },
    direction: {
        filterType: [
            { value: 'CONTAIN', label: '包含' },
        ],
    },
    keyword: {
        filterType: [
            { value: 'CONTAIN', label: '包含' },
        ],
    },
    // 后端建议去掉不包含：实现困难
    status: {
        // filterType: FILTER_TYPE_CATE.oSelectReqired,
        filterType: [
            { value: 'CONTAIN', label: '包含' },
        ],
    },
    // 在取消、完成、草稿状态下的需求里当前操作人是可能为空
    processors: {
        filterType: [
            { value: 'CONTAIN', label: '包含' },
            { value: 'ISNULL', label: '为空' },
            { value: 'ISNOTNULL', label: '不为空' },
        ],
    },
    demanders: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    tags: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    spaceId: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    recipientOrg: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    demanderOrg: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    proposerOrg: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    // 子需求没有分摊部门，所以可能为空
    apportionOrg: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    // 在取消、完成、草稿状态下的需求里当前操作人是可能为空
    processorOrg: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    apps: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    priorityId: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    demandTypeIds: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    urgent: {
        filterType: [
            { value: 'EQ', label: '=' },
        ],
    },
    revenueIdList: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    demandSourceId: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    relationUser: {
        filterType: [
            { value: 'EQ', label: '=' },
        ],
    },
    proposers: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    recipient: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    conserners: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    submitDate: {
        filterType: [
            { value: 'CI', label: '区间' },
        ],
    },
    expectedReleaseDate: {
        filterType: FILTER_TYPE_CATE.oDate,
    },
    acceptTimes: {
        filterType: [
            { value: 'CI', label: '区间' },
        ],
    },
    processTimes: {
        filterType: FILTER_TYPE_CATE.oDate,
    },
    statusKeepTime: {
        filterType: [
            { value: 'GTE', label: '>=' },
            { value: 'LTE', label: '<=' },
        ],
    },
    cardCompleteTimes: {
        filterType: FILTER_TYPE_CATE.oDate,
    },
    evaluationTime: {
        filterType: FILTER_TYPE_CATE.oDate,
    },
    revenueEndDate: {
        filterType: FILTER_TYPE_CATE.oDate,
    },
    groupName: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    sprintName: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    stageIdList: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    channelRisk: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    evaluated: {
        filterType: FILTER_TYPE_CATE.oSelectReqired,
    },
    evaluation: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
    productId: {
        filterType: [
            { value: 'CONTAIN', label: '包含' },
            { value: 'ISNOTNULL', label: '不为空' },
        ],
    },
    pmpProjectIds: {
        filterType: FILTER_TYPE_CATE.oSelect,
    },
};

// 复合高级查询的元数据schema key
export const DEMAND_QUERY_FORM = Object.freeze({
    department: 'demand-query-dept-v2',
    mine: 'demand-query-personal-v2',
    product: 'demand-query-product-v2',
});

/* export const DEMAND_QUERY_FORM = Object.freeze({
    department: 'demand-query-dept',
    mine: 'demand-query-personal',
    product: 'demand-query-product',
});
 */
