<template>
    <LayoutCard
        title="查看需求分组"
        class="demand-group__root j-mgt32"
    >
        <jacp-demand-group-share-list
            v-if="!errorMsg"
            :data="list"
            :group-data="groupData"
        >
            <p
                class="demand-group__desc"
                slot="desc"
            >
                <span
                    class="j-mgr32"
                    style="display: inline-block"
                >来自：{{ groupData.ownerName }}({{ groupData.ownerErp }}) 的分享</span>
                <span>分组名： {{ groupData.groupName }}</span>
                <span
                    v-if="isOwner"
                    class="demand-group__desc--red j-mgl8"
                >(这是您自己的分组，只能查看)</span>
            </p>
            <p
                slot="footer"
                class="j-mgt32"
                style="float:right;"
            >
                <template v-if="isOwner">
                    <el-button
                        type="primary"
                        @click="openLink"
                    >
                        前往查看
                    </el-button>
                </template>
                <template v-else>
                    <el-button
                        v-if="isFollowed"
                        type="primary"
                        @click="openFollowedLink"
                    >
                        前往查看
                    </el-button>
                    <el-button
                        v-if="!isFollowed"
                        type="primary"
                        @click="follow"
                    >
                        关注分组
                    </el-button>
                    <el-button
                        type="primary"
                        @click="save"
                        :disabled="disabledSave || isFollowed"
                    >
                        {{ !(disabledSave || isFollowed) ? `${labelMap.save}分组` : `已${labelMap.save}` }}
                    </el-button>
                </template>
            </p>
        </jacp-demand-group-share-list>
        <div
            class="j-nodata"
            v-else
        >
            {{ this.errorMsg }}
        </div>
    </LayoutCard>
</template>
<script>
import { DemandGroupModel } from '@/models/demandGroup';
import JacpDemandGroupShareList from './components/demandGroups/demandGroupShareList';

export default {
    name: 'JacpLayoutDemandGroup',
    components: {
        JacpDemandGroupShareList,
    },
    data() {
        return {
            list: [],
            groupData: {},
            isFollowed: false,
            // disabledFollow: false,
            disabledSave: false,
            dialogSuccessVisible: false,
            labelMap: {
                follow: '关注',
                save: '保存',
            },
            // dialogSuccessTitle: '',
            errorMsg: '',
        };
    },
    computed: {
        groupId() {
            return this.$route.params.groupId;
        },
        isOwner() {
            const user = this.$store.state.user.erp;
            return this.groupData.ownerErp === user;
        },
    },
    created() {
        DemandGroupModel.checkFollowedStatus(this.$route.params.groupId).then(({ followed }) => {
            this.isFollowed = followed;
        });
    },
    mounted() {
        this.fetchGroupData();
        this.fetchGroupList();
    },
    methods: {
        fetchGroupData() {
            DemandGroupModel.getGroup(this.groupId).then((data) => {
                this.groupData = Object.assign({}, data);
            }, ({ response }) => {
                if (!response || !response.data) {
                    this.errorMsg = '未知错误';
                }
                switch (+response.data.code) {
                case 404:
                    this.errorMsg = '分组不存在或已被删除';
                    break;
                default:
                    this.$message.error(this.errorMsg);
                    break;
                }
            });
        },
        fetchGroupList() {
            DemandGroupModel.getGroupList(this.groupId, {
                pageSize: 10000,
            }).then(({ records }) => {
                this.list = records;
            });
        },
        save() {
            DemandGroupModel.copyGroup(this.groupData).then(({ groupId, category }) => {
                this.disabledSave = true;
                this.groupData = Object.assign(this.groupData, { groupId, category });
                this.dialogSuccess({
                    title: `已${this.labelMap.save}`,
                    content: `成功${this.labelMap.save}`,
                });
            });
        },
        follow() {
            DemandGroupModel.followedGroup(this.groupId).then(() => {
                this.isFollowed = !this.isFollowed;
                this.groupData = Object.assign(this.groupData, {
                    category: 1,
                });
                this.dialogSuccess({
                    title: this.isFollowed ? '已关注' : '已取消',
                    content: this.isFollowed ? `成功${this.labelMap.follow}` : `成功取消${this.labelMap.follow}`,
                });
                this.$store.dispatch('demands_group_fetchData');
            });
        },
        dialogSuccess({ title, content }) {
            // this.dialogSuccessTitle = `已${this.labelMap[type]}`;
            this.dialogSuccessVisible = true;
            this.$alert(`<p style="color: green;"><i class="el-icon-success j-mgr8"></i><span>${content}</span></p>`,
                title, {
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: '前往查看',
                }).then(this.openLink);
        },
        openLink() {
            this.$router.push({
                name: 'demandsIndex',
                params: {
                    tab: 'group',
                },
                query: {
                    groupId: this.groupData.groupId,
                    cateKey: Number(this.groupData.category) === 0 ? 'my' : 'followed',
                },
            });
        },
        openFollowedLink() {
            this.$router.push({
                name: 'demandsIndex',
                params: {
                    tab: 'group',
                },
                query: {
                    groupId: this.groupData.groupId,
                    cateKey: 'followed',
                },
            });
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.demand-group{
    &__root{
        width: calc(~"100% - 320px");
        max-width: 1024px;
        margin: 0 auto;
        .demand-group__table {
            max-height: calc(~"100vh - 350px");
            overflow: auto;
            &:before{
                display: none;
            }
        }
    }
    &__desc{
        &--red{
            color: @redColor;
        }
    }
}
</style>
