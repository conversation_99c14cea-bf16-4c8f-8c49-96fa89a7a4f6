<template>
    <div
        class="latest-detail__wrapper"
        id="jacp-latest-detail"
    >
        <header class="latest-detail__header">
            <h1>
                {{ $route.name === 'demandSplit' ? $t('jacp.splitDemand') : $t('jacp.newDemand') }}
            </h1><i
                class="el-icon-close latest-detail__close"
                @click.stop="gotoIndex"
            />
        </header>
        <demand-detail-form
            ref="formIns"
            :detail="detail"
            :parent-demand-id="parentDemandId"
            :disable-draft="disableDraft"
            v-on="$listeners"
            @on-close="gotoIndex"
            @on-saved="() => {
                isSaved = true;
                $emit('refresh-list');
            }"
            @on-reset-form="init"
            style="margin-bottom: 62px;"
        >
            <section
                slot="extra"
                class="demand-add__extra"
                v-if="$route.name === 'demandSplit'"
            >
                <h5>父需求</h5>
                <router-link
                    :to="{
                        name: 'demandDetail',
                        id: parentDemandId
                    }"
                >
                    {{ parentDetail.name }}
                </router-link>
                <el-button
                    size="mini"
                    type="primary"
                    round
                    @click="copyParent"
                >
                    复制父需求
                </el-button>
            </section>
        </demand-detail-form>
    </div>
</template>

<script type="text/javascript">
import DemandModel from '@/models/demands';
import DemandDetailForm from './components/demandDetailForm';
// import demandTree from './components/demandTree';

export default {
    name: 'LayoutDemandAdd',
    components: {
        DemandDetailForm,
        // demandTree,
    },
    data() {
        const { params, name } = this.$route;
        const id = +params.id;
        return {
            // continueSplit: true,
            demandId: name === 'demandEdit' ? id : undefined,
            parentDemandId: name === 'demandSplit' ? id : -1,
            disableDraft: true,
            detail: null,
            parentDetail: {},
        };
    },
    created() {
        // 需求分组
        this.$store.dispatch('demands_group_fetchData');
    },
    mounted() {
        this.init();
    },
    async beforeRouteLeave(to, from, next) {
        if (this.isSaved) {
            next();
            return;
        }
        const answer = await this.$confirm('需求尚未提交，确认离开么？', '提示', {
            type: 'warning',
            cancelButtonText: '再想想',
            confirmButtonText: '离开',
        });
        if (answer) {
            next();
        } else {
            next(false);
        }
    },
    methods: {
        init() {
            const { name, query } = this.$route;
            this.isSaved = false;
            switch (name) {
            case 'demandEdit':
                this.initEdit();
                break;
            case 'demandSplit':
            // 复制内容及关系
                this.initSplit(query.action === 'copy-split' ? query.id : undefined);
                break;
            default:
                this.disableDraft = false;
                if (query.action && query.action.indexOf('copy') > -1 && query.id) {
                    this.initCopy(query.id);
                } else {
                    this.detail = {};
                }
                break;
            }
        },
        async initSplit(targetId) {
            const parentDetail = await DemandModel.getDetail(this.parentDemandId);
            let copyTarget = {};
            // 有复制目标的就复制一下目标基本信息，同时获取父需求拆分时要初始化的参数
            if (targetId) {
                const target = await DemandModel.getDetail(targetId);
                copyTarget = target.copyBasic();
            }
            this.detail = {
                ...copyTarget,
                ...parentDetail.split(),
            };
            this.parentDetail = parentDetail;
        },
        initEdit() {
            DemandModel.getDetail(this.demandId).then((detail) => {
                this.detail = { ...detail };
                this.parentDemandId = detail.parentDemandId;
                this.disableDraft = detail.status === 4;
            }).catch(() => {
                this.isSaved = true;
                this.gotoIndex();
            });
        },
        async initCopy(targetId) {
            const copyTarget = await DemandModel.getDetail(targetId);
            this.detail = copyTarget.copyBasic();
        },
        gotoIndex() {
            this.$router.replace({
                name: 'demandsIndex',
            });
        },
        copyParent() {
            // 填充有效字段
            const { parentDetail } = this;
            if (typeof parentDetail.copy === 'function') {
                this.detail = { ...this.detail, ...parentDetail.copy() };
            }
        },
    },
};
</script>

<style lang="less">
@import '~@/theme/var';
.demand-add{
    &__extra{
        background-color: @bgColor;
        padding: 16px;
        // display: flex;
        // flex-direction: column;
        font-size: 12px;
        border-radius: 8px;
        & a[href]{
            text-decoration: underline;
            margin-bottom: 16px;
            display: block;
        }
        & .el-button{
            width: fit-content;
        }
    }
}
</style>
