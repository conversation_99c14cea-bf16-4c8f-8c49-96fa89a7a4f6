import Layout from './layout';

export const routes = {
    path: '/competition',
    name: 'competitionIndex',
    component: Layout.competition,
    // redirect: { name: 'jacpAppStore' },
    props: {
    },
    // children: [{
    //     path: 'index',
    //     path: '',
    //     name: 'competitionPage',
    //     component: Layout.competition,
    // }],
};

/**
 * 导出的是作为子模块，子模块的路由出口在module/root/layoutIndex的router-view中
 */
export default [];
