
<script>
import UserProfile from '$module/components/userProfile';
// import HelpCenter from '$module/components/widgets/LayoutViewWidgetHelpCenter';
import { workspaceManager } from '$module/models/workspaceManager';
import i18n from '$platform.i18n';
import LocalWidgetsCtrl from '$module/components/widgetsCtrl';
import LocalModuleLoader from '@/modules/root/components/moduleLoader';
// 切换视图
const ViewSelector = {
    render() {
        return <div>
            <el-select
                vModel={workspaceManager.activedView}
                style="width: 110px;display: inline-block;margin-left: -13px;"
                onChange={(view) => {
                    workspaceManager.saveView(view);
                    workspaceManager.loadWidgetsByView(view);
                }}
            >
                {
                    workspaceManager.viewOptions.map(item => <el-option
                        key={item.enName}
                        label={item.cnName}
                        value={item.enName}></el-option>)
                }

            </el-select>
            <span class="j-divider" />
            <span
                class="j-mgl16"
                onClick={() => this.$emit('update:visible', true)}
            >{ i18n.t('jacp.myzone.layoutViewCtrlTitle') }</span></div>;
    },
};
// 缓存加载的模块
const loadedModuleResource = new Map();
const onModuleLoaded = ({ module } = {}) => {
    if (module) {
        const { resource } = module;
        if (resource && !resource.styleMounted) {
            resource.applyStyle();
        }
        loadedModuleResource.set(module, resource);
    }
};

const clearModuleResource = () => {
    const cache = loadedModuleResource.values();
    Array.from(cache).forEach((resource) => {
        if (resource && resource.styleMounted) {
            resource.removeStyle();
        }
    });
    loadedModuleResource.clear();
};

export default {
    name: 'LayoutZone',
    components: { LocalWidgetsCtrl, LocalModuleLoader },
    data() {
        return {
            widgets: [],
            ctlsVisiable: false,
        };
    },
    computed: {
        isWidgetChanged() {
            return workspaceManager.widgetMap && workspaceManager.checkedWidgets;
        },
    },
    beforeCreate() {
        workspaceManager.init();
    },
    beforeRouteLeave(to, from, next) {
        // 清理来自模块的工作台组件样式，这个清理模块的时机需要在路由跳转前完成，不能在beforeDestory里，beforeDestory发生在
        clearModuleResource();
        next();
    },
    watch: {
        ctlsVisiable(val) {
            if (!val && this.$refs.widgetsCtrl) {
                this.$refs.widgetsCtrl.reset();
            }
            if (val && !workspaceManager.remoteAllWidgets.length) {
                workspaceManager.loadAllWidgets();
            }
        },
        isWidgetChanged: {
            immediate: true,
            handler() {
                this.updateComponents();
            },
        },
    },
    methods: {
        update(widgets = []) {
            this.widgets = widgets;
        },
        updateComponents() {
            const { checkedWidgets, widgetMap } = workspaceManager;
            const widgets = checkedWidgets.map((widget) => {
                // TODO:moduleLoader只有第一次加载的时候才会被触发，后续都需要主动拉区一下模块资源，但是工作台组件是否确实需要移除该模块样式？
                if (widgetMap[widget.enName] && widget.appCode !== 'jacp') {
                    JModule.getModuleAsync(widget.appCode)
                        .then(module => onModuleLoaded({ module }));
                }

                return {
                    key: widget.enName,
                    ...widgetMap[widget.enName],
                    ...widget,
                    component: widgetMap[widget.enName]
                        ? widgetMap[widget.enName].component
                        : {
                            render() {
                                return <LocalModuleLoader
                                    style="position: relative;min-height: 200px;background: #fff;margin-top: 24px;width: calc(100% - 24px)"
                                    moduleKey={widget.appCode}
                                    pageMode={false}
                                    afterLoaded={onModuleLoaded}
                                    showLoading={false}
                                    loaderOptions={{
                                        autoApplyScript: true,
                                        autoApplyStyle: true,
                                    }}
                                ><jacp-empty label="加载中..." vLoading={true} ></jacp-empty></LocalModuleLoader>;
                            },
                        },
                };
            });
            this.widgets = widgets;
        },
    },
    render(h) {
        const { widgets } = this;
        // 动态输出的widget
        const leftNode = widgets.filter(w => w.type === 1);
        const rightNode = widgets.filter(w => w.type === 2);
        return <el-main class="myzone-main">
            <div class="myzone-main-columes fl2">
                <UserProfile onChange={this.update} scopedSlots={{
                    action: () => <ViewSelector {...{
                        on: {
                            'update:visible': () => {
                                this.ctlsVisiable = true;
                            },
                        },
                    }}></ViewSelector>,
                }}></UserProfile>
                { leftNode.map(o => h('div', {class: 'mt-4',}, [h(o.component)])) }
                {/* <HelpCenter type="banner" title="行云介绍"></HelpCenter> */}
            </div>
            { rightNode.length ? <div class="myzone-main-columes fl1">{ rightNode.map(o => h(o.component)) }</div> : null }
            <el-dialog
                class="jacp-layoutview-ctrl"
                width="750px"
                visible={this.ctlsVisiable}
                {...{
                    on: {
                        'update:visible': (val) => {
                            this.ctlsVisiable = val;
                        },
                    },
                }}
            >
                <LocalWidgetsCtrl
                    workspaceManager={workspaceManager}
                    {...{
                        on: {
                            'on-save': () => {
                                this.ctlsVisiable = false;
                            },
                            'on-close': () => {
                                this.ctlsVisiable = false;
                            },
                        },
                    }}
                    ref="widgetsCtrl"
                />
            </el-dialog>
        </el-main>;
    },
};

</script>

<style lang="less">
@import '~@/theme/var';

.myzone-main{
    position: relative;
    padding-bottom: 24px;
    padding-left: 32px;
    flex: 1;
    background: #fff;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    overflow:auto;
    &-columes{
        flex-direction: column;
        overflow: hidden;
    }
    & a[href]{
      line-height: 1.8;
    }
    .layout-card-root{
        width: calc(~"100% - 24px");
    }
}
@media screen and (max-width: 1024px) {
    .fl2{
        min-width: 520px;
    }
}
@media screen and (min-width: 1024px)  and (max-width: 2400px) {
    min-width: 800px;
}
.fl2{
    flex-grow: 2;
    margin-right: 16px;
}
.fl1{
    flex-grow: 1;
    width: 360px;
    flex-shrink: 0;
    margin-right: 24px;
    .border-card-root + .border-card-root{
        margin-top: 16px;
    }
}

</style>
