<script>
import LeftHeader from './leftHeader.vue';
import { workspaceManager } from '$module/models/workspaceManager';
import LocalModuleLoader from '@/modules/root/components/moduleLoader';
import PopoverCard from './popoverCard.vue';
import OrgModel from '@/models/org';
import UserProfile from '$module/components/userProfile';
// eslint-disable-next-line std/no-internal-modules
import MyImage from '@/assets/images/static_images_empty_暂无数据.png';

const loadedModuleResource = new Map();
const onModuleLoaded = ({ module } = {}) => {
    if (module) {
        const { resource } = module;
        if (resource && !resource.styleMounted) {
            resource.applyStyle();
        }
        loadedModuleResource.set(module, resource);
    }
};
export default {
    name: 'Mine',
    components: {
        LeftHeader,
        PopoverCard,
        LocalModuleLoader,
    },
    props: {
    },
    data() {
        return {
            right: true,
            checkList: [],
            currentOrg: {
                id: this.$store.state.user.orgTierCode,
                name: this.$store.state.user.orgTierName,
                topOrgCode: '',
                topOrgName: '',
            },
            activeName: '',
            orgId: this.$store.state.user.orgTierCode,
            orgName: this.$store.state.user.orgTierName,
        };
    },
    computed: {
        isWidgetChanged() {
            return workspaceManager.widgetMap && workspaceManager.checkedWidgets;
        },
    },
    watch: {
        isWidgetChanged: {
            immediate: true,
            handler() {
                this.updateComponents();
            },
        },
    },
    beforeCreate() {
        workspaceManager.init();
    },
    mounted() {
        this.initFilterData();
    },
    methods: {
        hiddenRight() {
            this.right = !this.right;
        },
        initFilterData() {
            OrgModel.getDeptByLevel({ level: 0 }).then((orgs) => {
                this.currentOrg.topOrgCode = orgs.fullPath;
                this.currentOrg.topOrgName = orgs.fullName;
            });
        },
        departmentChange(orgId, orgName) {
            this.orgId = orgId;
            this.orgName = orgName;
        },
        updateComponents() {
            const { checkedWidgets, widgetMap } = workspaceManager;
            const widgets = checkedWidgets.map((widget) => {
                // TODO:moduleLoader只有第一次加载的时候才会被触发，后续都需要主动拉区一下模块资源，但是工作台组件是否确实需要移除该模块样式？
                if (widgetMap[widget.enName] && widget.appCode !== 'jacp') {
                    JModule.getModuleAsync(widget.appCode)
                        .then(module => onModuleLoaded({ module }));
                }
                this.checkList = checkedWidgets.filter(i => i.category === 2);
                return {
                    key: widget.enName,
                    ...widgetMap[widget.enName],
                    ...widget,
                    component: widgetMap[widget.enName]
                        ? widgetMap[widget.enName].component
                        : {
                            render() {
                                return <LocalModuleLoader
                                    style="position: relative;min-height: 200px;background: #fff;margin-top: 24px;width: calc(100% - 24px)"
                                    moduleKey={widget.appCode}
                                    pageMode={false}
                                    afterLoaded={onModuleLoaded}
                                    showLoading={false}
                                    loaderOptions={{
                                        autoApplyScript: true,
                                        autoApplyStyle: true,
                                    }}
                                ><jacp-empty label="加载中..." vLoading={true} ></jacp-empty></LocalModuleLoader>;
                            },
                        },
                };
            });
            this.widgets = widgets;
        },
    },
    render(h) {
        const {
            widgets, checkList, currentOrg, orgId, orgName, activeName,
        } = this;
        const leftNode = widgets.filter(i => i.category === 2 && i.status && i.type === 1);
        if (!leftNode.find(i => i.title === activeName)) {
            this.activeName = leftNode[0]?.title || '';
        }
        // let activeName = '';
        const that = this;
        const onTabClick = (tab) => {
            that.activeName = tab.name;
        };
        return (
            <div>
                <div class="mine-box">
                    <div class="mine-box-left">
                        <UserProfile class="mine-box-left-header" />
                        <div class="left-content">
                            <div class="myzone-main-columes fl2">
                                {
                                    leftNode.length > 0
                                        ? <el-tabs
                                            value={activeName}
                                            class="left-content-menu-tabs"
                                            on-tab-click={onTabClick}>
                                            <el-tab-pane disabled={true}>
                                                <jacp-org-tree-by-department
                                                    size="mini"
                                                    default-org-id={currentOrg.id}
                                                    default-org-name={currentOrg.name}
                                                    root-org-id={currentOrg.topOrgCode}
                                                    root-org-name={currentOrg.topOrgName}
                                                    class="left-content-org"
                                                    slot="label"
                                                    {...{
                                                        on: {
                                                            'check-org': (orgTigerId, orgTigerName) => this.departmentChange(orgTigerId, orgTigerName),
                                                        },
                                                    }}
                                                />
                                            </el-tab-pane>
                                            {
                                                leftNode.map(item => (
                                                    <el-tab-pane
                                                        label={item.title}
                                                        name={item.title}
                                                    >
                                                        { h('div', { class: 'mt-4' }, [h(item.component, {
                                                            attrs: {
                                                                orgName,
                                                                orgId,
                                                            },
                                                        })]) }
                                                    </el-tab-pane>
                                                ))
                                            }
                                            <el-tab-pane disabled={true}>
                                                <span slot="label" class="left-content-menu-tabs-icon">
                                                    <PopoverCard
                                                        workspaceManager={workspaceManager}
                                                        checkList={checkList}
                                                    >
                                                        <i
                                                            class="el-icon-plus"
                                                            slot="reference"
                                                        />
                                                    </PopoverCard>
                                                </span>
                                            </el-tab-pane>
                                        </el-tabs>
                                        : <div class="department-no-data">
                                            <img
                                                src={MyImage} />
                                            <PopoverCard
                                                workspaceManager={workspaceManager}
                                                checkList={checkList}
                                            >
                                                <el-button
                                                    type="primary"
                                                    slot="reference"
                                                    class=""
                                                    plain>+ 添加组件</el-button>
                                            </PopoverCard>
                                        </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    },
};
</script>
<style lang="less" scoped>
.mine-box {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    height: 100%;
    &-left {
        flex: 1;
        padding: 24px 24px 0;
        display: flex;
        flex-direction: column;
        overflow: auto;
        &-header {
            height: 168px;
        }
        .left-content {
            flex: 1;
            height: calc(100vh - 240px);
            margin-top: 16px;
            overflow: auto;
            &-menu {
                height: 48px;
                &-tabs {
                    /deep/ .el-tabs__item.is-active {
                        color: #303133;
                        font-size: 16px;
                        border-bottom: none;
                        font-weight: 600;
                    }
                    /deep/ .el-tabs__item {
                        padding: 0 12px;
                        color: #606265;
                        font-size: 16px;
                        &:hover {
                            color: #000;
                        }
                    }
                    /deep/ .el-tabs__active-bar {
                        background-color: transparent;
                    }
                    /deep/ .el-tabs__nav-wrap::after {
                        position: static;
                    }
                    &-icon {
                        font-size: 16px;
                        padding: 2px 5px;
                        border-radius: 5px;
                        cursor: pointer;
                        &:hover {
                            background-color: rgba(240,242,245,1);
                        }
                    }
                }
            }
            .department-no-data {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 400px;
                img {
                    width: 200px;
                }
                button {
                    border-radius: 20px;
                }
            }
        }
    }
    &-shark {
        width: 2px;
        &-span {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 228px;
            z-index: 10;
            margin-left: 1px;
            cursor: pointer;
            width: 16px;
            height: 44px;
            background-color: #fff;
            border: 1px solid #dcdfe6;
        }
        .right {
            border-radius: 12px;
            transform: translateX(-50%);
        }
        .left {
            transform: translateX(-100%);
            border-radius: 12px 0 0 12px;
            border-right: 0px;
        }
    }
    &-right {
        width: 25%;
        padding-top: 16px;
        border-left: 1px solid #ebeef5;
        .right-content {
            margin: 0 24px;
        }
    }
    &-hidden {
        display: none;
    }
}
</style>
