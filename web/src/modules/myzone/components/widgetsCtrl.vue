<template>
    <div style="display: flex;justify-content: space-between;">
        <div class="jacp-layoutview-ctrl-left">
            <span class="jacp-layoutview-ctrl-left__title">{{ title }}</span>
            <!-- 全部可选控件列表 -->
            <ul class="jacp-layoutview-ctrl-left-list">
                <template
                    v-for="domain in domainList"
                >
                    <li
                        :key="domain.code"
                        class="item groupname"
                        v-show="compsGroupByDomain[domain.code] && compsGroupByDomain[domain.code].length"
                    >
                        {{ domain.name }}
                    </li>
                    <li
                        :class="['item', comp.checked ? 'checked' : '']"
                        :key="comp.key"
                        v-for="comp in compsGroupByDomain[domain.code]"
                    >
                        <el-checkbox
                            v-model="comp.checked"
                            :label="comp.title"
                            :disabeld="comp.disabled"
                            @change="handleChecked($event, comp)"
                        />
                    </li>
                </template>
            </ul>
        </div>
        <div class="jacp-layoutview-ctrl-right">
            <i
                class="el-icon el-icon-close"
                @click="$emit('on-close')"
            />
            <!-- 预览区 -->
            <div class="jacp-layoutview-ctrl-right__previewer">
                <div class="left">
                    <!-- 预览区左侧有两个固定控件占位 -->
                    <fake-widget
                        title="个人信息"
                        fake-style="article"
                        disabled
                    />
                    <transition-group>
                        <fake-widget
                            v-for="item in leftComps"
                            :key="item.key"
                            :fake-style="item.fakeStyle"
                            :title="item.title"
                        >
                            <span
                                slot="extra"
                                @click="sticky(item)"
                            >置顶</span>
                        </fake-widget>
                    </transition-group>
                    <!-- <fake-widget
                        title="行云介绍"
                        fake-style="block"
                        disabled
                    /> -->
                </div>
                <!-- 预览区右侧 -->
                <div class="right">
                    <fake-widget
                        v-for="item in currentCheckedComps.filter(o => o.type === 2)"
                        :key="item.key"
                        :fake-style="item.fakeStyle"
                        :title="item.title"
                        :disabeld="item.disabled"
                    >
                        <span
                            slot="extra"
                            @click="sticky(item)"
                        >置顶</span>
                    </fake-widget>
                </div>
            </div>
            <footer class="jacp-layoutview-ctrl-footer">
                <el-button
                    @click="() => {
                        $emit('on-close');
                        reset();
                    }"
                >
                    取 消
                </el-button>
                <el-button
                    type="primary"
                    @click="save"
                >
                    保 存
                </el-button>
            </footer>
        </div>
    </div>
</template>
<script>
import findIndex from 'lodash/findIndex';
import cloneDeep from 'lodash/cloneDeep';
import Dict, { DICT_TYPE } from '@/models/dict';
import FakeWidget, { isFakeStyleValid } from './fakeWidget';

export default {
    name: 'LayoutViewCtrl',
    components: { FakeWidget },
    props: {
        workspaceManager: {
            type: Object,
            required: true,
        },
        checked: {
            type: Array,
        },
    },
    created() {
        Dict.getConfig(DICT_TYPE.domain).then((data) => {
            this.domainList = data;
        });
    },
    data() {
        return {
            title: this.$t('jacp.myzone.layoutViewCtrlTitle'),
            currentCheckedComps: [],
            domainList: [],
        };
    },
    watch: {
        'workspaceManager.checkedWidgets': {
            immediate: true,
            handler: 'reset',
        },
    },
    computed: {
        allWidgets() {
            if (!this.$store.state.app.helpDoc) {
                return this.workspaceManager.remoteAllWidgets.filter(item => item.key !== 'LayoutViewWidgetHelpCenter');
            }
            return this.workspaceManager.remoteAllWidgets;
        },
        defaultDomain() { return this.domainList[0] || { code: 1 }; },
        compsGroupByDomain() {
            const { widgetMap } = this.workspaceManager;
            return this.domainList.reduce((result, domain = {}) => {
                result[domain.code] = this.allWidgets.filter((comp) => {
                    // 默认放到内置domain里，兼容以前没有这个字段的widgets
                    if (!comp.domainId) {
                        comp.domainId = this.defaultDomain.code;
                    }
                    return comp.domainId === domain.code;
                }).map(widget => ({
                    // widget信息以后端返回的为准，有fakeStyle需要取用一下代码里的
                    ...widget,
                    fakeStyle: isFakeStyleValid(widget.fakeStyle)
                        ? widget.fakeStyle
                        : widgetMap[widget.key].fakeStyle,
                    checked: !!this.currentCheckedComps.find(o => o.key === widget.key),
                }));
                return result;
            }, {});
        },
        // TODO: remove
        leftComps() { return this.currentCheckedComps.filter(o => o.type === 1); },
        rightComps() { return this.currentCheckedComps.filter(o => o.type === 2); },
    },
    methods: {
        save() {
            const copyWidgets = cloneDeep(this.currentCheckedComps);
            this.workspaceManager.saveWidgetsByView(copyWidgets).then(() => {
                this.$emit('on-save', copyWidgets);
            });
        },
        sticky(item) {
            this.currentCheckedComps.forEach((o, index) => {
                if (o.key === item.key) {
                    this.currentCheckedComps.splice(index, 1);
                }
            });
            this.currentCheckedComps.splice(0, 0, item);
        },
        handleChecked(checked, item) {
            if (checked) {
                this.currentCheckedComps.push(item);
            } else {
                const index = findIndex(this.currentCheckedComps, comp => comp.key === item.key);
                if (index > -1) {
                    this.currentCheckedComps.splice(index, 1);
                }
            }
        },
        reset() {
            this.currentCheckedComps = cloneDeep(this.workspaceManager.checkedWidgets);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-layoutview-ctrl{
  &-left,&-right{
    display: flex;
    flex-direction: column;
    height: 560px;
  }
  &-left{
    text-align: left;
    background-color: #fff;
    padding-top: 0;
    &__title{
      font-size: 16px;
      height: 49px;
      line-height: 49px;
      padding-left: 16px;
    }
    &-list{
        padding: 0 32px;
        width: 264px;
        list-style: none;
        overflow-y: auto;
      & .item{
        & .el-checkbox{
          display: flex;
          flex-direction: row-reverse;
          height: 38px;
          align-items: center;
          justify-content: space-between;
          &__label{
            padding: 0;
            font-weight: 400;
          }
        }
        &.groupname{
          font-weight: 800;
          color: @fontColor;
          height: 52px;
          padding-top: 14px;
        }
        line-height:38px;
        height: 38px;
        font-size: 14px;
        color: @fontSecendColor;
        border-bottom: @borderStyleSecend;
        &.checked{
          color: @primaryColor;
        }
      }
    }
  }
  &-right{
    width: 100%;
    text-align: right;
    min-width: 480px;
    justify-content: space-between;
    overflow: hidden;
    & .el-icon{
      color: rgba(197,197,197,1);
      margin: 16px 16px;
      font-size: 16px;
      cursor: pointer;
      &:hover{
        color: @primaryColor;
      }
    }
    &__previewer{
      width: 100%;
      display: flex;
      justify-content: center;
      overflow: auto;
      height: 100%;
      padding-top: 24px;
      & .left{
        width: 200px;
      }
      & .right{
        width: 100px;
      }
    }
  }
  &-footer{
    padding: 32px 32px 32px 0;
  }
  & .el-dialog{
    height: 560px;
    background-color: rgba(245,245,245,1);
    & .el-dialog__body,
    & .el-dialog__footer{
      border-top: none;
      padding: 0;
    }
    & .el-dialog__header{
      display: none;
    }
  }

}
</style>
