<template>
    <layout-card
        :title="title"
        :span="1"
    >
        <a
            :href="$store.state.url.help+'/#/faq'"
            target="_blank"
            slot="extra"
            v-if="type === 'list'"
        >查看更多
            <i
                class="el-icon-arrow-right"
                style="margin-left: 8px;"
            /></a>
        <div
            class="helpcenter-list"
            v-if="type === 'list'"
        >
            <div class="helpcenter-list-service">
                <img
                    class="helpcenter-list-service__img"
                    src="@/assets/images/help_service_avatar.png"
                    alt="咚咚客服"
                >
                <p class="helpcenter-list-service__text">
                    我是您的专属客服行云小妹，如您在使用中遇到问题，请咨询：{{ supportGroup }}（咚咚群）
                </p>
            </div>
            <ol>
                <li
                    class="helpcenter-list-item"
                    v-for="(item, index) in list"
                    :key="index"
                >
                    <span class="j-mgr16">{{ index + 1 }}.</span><a
                        :href="item.link"
                        target="_blank"
                    >{{ item.title }}</a>
                </li>
            </ol>
        </div>
        <div
            class="helpcenter-banner"
            v-if="type === 'banner'"
        >
            <template v-for="item in bannerList">
                <a
                    class="helpcenter-banner-item"
                    v-if="item.link"
                    :key="item.link"
                    :href="item.link"
                    target="_blank"
                >
                    <img
                        :class="['helpcenter-banner-item__img']"
                        :src="item.bannerUrl"
                        :alt="item.bannerTitle"
                    >
                </a>
                <div
                    class="helpcenter-banner-item disabled"
                    :key="item.bannerUrl"
                    v-else
                >
                    <img
                        :class="['helpcenter-banner-item__img']"
                        :src="item.bannerUrl"
                        :alt="item.bannerTitle"
                    >
                </div>
            </template>
        </div>
    </layout-card>
</template>
<script>
import helpCenter from '@/models/configHelpcenterFAQ';
import GlobalSetting from '@/models/globalSetting';

export default {
    name: 'HelpCenter',
    props: {
        title: {
            type: String,
            default: '帮助中心',
        },
        type: {
            type: String,
            default: 'list',
        }, // list,banner
    },
    data() {
        // 重置帮助文档地址
        helpCenter.data.forEach((item) => {
            if (item.link) {
                if (item.link.indexOf('http') > -1) {
                    return;
                }
                item.link = `${this.$store.state.url.help}${item.link}`;
            }
        });
        return {
            ...helpCenter,
            supportGroup: GlobalSetting.getSupportGroup(),
        };
    },
    computed: {
        bannerList() {
            return this.data.filter(item => item.bannerTitle && item.bannerUrl);
        },
        list() {
            return this.data.filter(item => item.title && item.link);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.helpcenter-banner{
  display: flex;
  height: 100%;
  &-item{
    flex: 1;
    padding: 16px 12px;
    transition: all .3s;
    a[href]&{
      color: @fontColor;
    }
    &:hover:not(.disabled){
      transform: scale(1.05);
    }
    &__img{
      background-size: cover;
      width: 100%;
      display: block;
    }
  }
}
.helpcenter-list{
  text-align: left;
  &-service{
    background-color: #F7F7F7;
    margin: 8px 0 8px 0;
    padding-left: 10px;
    display: flex;
    align-items: center;
    &__img{
      width: 48px;
      height: 48px;
      margin-right: 8px;
      // display: inline;
    }
    &__text{
      display: inline-block;
      line-height: 1.8;
      width: calc(~"100% - 75px");
    }
  }
  &-item{
    width: 100%;
    list-style: none;
    & a[href] {
      color: @fontColor;
    }
  }
}
</style>
