<template>
    <border-card
        class="ser-profile-root"
        title="个人信息"
        position="left"
    >
        <div class="user-profile">
            <jacp-erp
                :data="userInfo"
                :display-name="false"
                :avatar-size="34"
                avatar
            />
            <div class="user-profile-info">
                <div>
                    <h2>{{ user.name }}, 欢迎回来</h2>
                    <span
                        class="j-mgl8"
                        shrink
                    >{{ user.positionName }}</span>
                    <span
                        style="margin-left: 8px;"
                        shrink
                    >{{ user.email }}</span>
                </div>
                <div class="user-profile-info__action">
                    <slot name="action" />
                </div>
            </div>
            <ul class="user-profile-counter">
                <!-- <li
                    class="user-profile-counter-item jacp-version-btn"
                    title="版本说明查看"
                    @click="showVersion"
                /> -->
                <li class="user-profile-counter-item">
                    <div class="j-mgb8">
                        {{ $t('jacp.myzone.counterTitleCard') }}
                    </div>
                    <span><strong>{{ counter.cards }}</strong>个</span>
                </li>
                <li class="user-profile-counter-item">
                    <div class="j-mgb8">
                        {{ $t('jacp.myzone.counterTitleDemand') }}
                    </div>
                    <span><strong>{{ counter.demands }}</strong>个</span>
                </li>
            </ul>
        </div>
        <jacp-version
            :lazy-load="false"
            ref="version"
        />
    </border-card>
</template>
<script>
import { MyZoneModel } from '@/models/myzone';
import { User } from '@/models/user';
import AppModule from '@/modules/root/models/appModule';

export default {
    name: 'UserProfile',
    data() {
        return {
            counter: {
                cards: 0,
                demands: 0,
            },
            userInfo: {},
            event: this.$initEvent(),
        };
    },
    created() {
        User.getBaseUrl().then(this.updateCounter);
        this.event.$on('on-total-change', ({ todo }) => {
            this.updateCounterCards(todo);
        });
        this.getUserInfo();
    },
    computed: {
        user() {
            return this.$store.state.user;
        },
    },
    methods: {
        // 获取用户信息，后端接口不能更新，只能用这个
        getUserInfo() {
            User.getUserInfo(this.$store.state.user.erp).then((data = {}) => {
                this.userInfo = data;
                data.headImage = data.headImg;
                data.name = data.realName;
                data.userName = data.realName;
            });
        },
        showVersion() {
            this.$refs.version.showVersion(this.version);
        },
        // TODO: remove 爸爸里的cards更新了的时候调一下
        updateCounterCards(val) {
            this.$set(this.counter, 'cards', val || 0);
        },
        updateCounter() {
            AppModule.getModuleList({ status: 'active' }).then(() => {
                const { activeModuleMap } = this.$store.state.root;
                let archSpaceFlag = false;
                let archDemandFlag = false;
                activeModuleMap.forEach((item) => {
                    if (item.code === 'archSpace') {
                        archSpaceFlag = true;
                    }
                    if (item.code === 'archDemand') {
                        archDemandFlag = true;
                    }
                });
                // 必须有团队空间和需求管理时候才调用这俩接口
                if (archSpaceFlag && archDemandFlag) {
                    // 获取待办任务数量和待办需求数量
                    Promise.all([
                        MyZoneModel.getCardCount(),
                        MyZoneModel.getDemandsStatusCount()]).then(([cardsCount, demandsCount]) => {
                        console.log('=cardsCount=', cardsCount, demandsCount);
                        // 兼容一下判断指是否存在
                        if (cardsCount) {
                            this.updateCounterCards(cardsCount);
                        }
                        if (demandsCount) {
                            const reducer = (prev, cur) => prev + cur;
                            const demandSum = Object.values(demandsCount)
                                .reduce(reducer);
                            this.$set(this.counter, 'demands', demandSum);
                        }
                    });
                }
            });
        },

    },
};
</script>
<style lang="less">
@import '~@/theme/var';
@media screen and (max-width: 1024px) {
    .user-profile-info [shrink]{
        display:none;
    }
}
.user-profile-root {
    padding-bottom: 8px;
    .layout-card-content {
        padding: 0;
    }
}

.user-profile{
    position: relative;
    height: 168px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 16px;
    border: 1px solid #ebeef5;
    padding: 0 10px;
    // margin-left: 8px;
    li{
        list-style: none;
    }
    .no-borderbg-select();
    &-info{
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        flex-basis: 100%;
        h2{
            color: @fontColor;
            font-size: 24px;
            font-weight: normal;
            margin: 0;
            display: inline-block;
            & ~ span{
                color: @remarkColor;
            }
        }
        &__action{
            color: @fontSecendColor;
            cursor: pointer;
        }
    }
    &-counter{
        display: flex;
        flex-direction: row;
        width: min-content;
        margin-right: 8px;
        align-items: center;
        &-item{
            // border-right: @borderStyleSecend;
            height: 46px;
            color: @remarkColor;
            padding-left: 12px;
            margin-left: 32px;
            white-space: nowrap;
            & span{
                line-height: 28px;
            }
            & strong{
                color: @fontColor;
                font-size: 28px;
                margin-right: 8px;
                font-weight: 400;
            }
            &.jacp-timeLine-btn{
                width: 74px;
                height: 50px;
                background-size: 100% 100%;
                background-image: url("~@/assets/images/timeLine/<EMAIL>");
                margin-right: -25px;
                border-left: none;
                animation: changBtnPic .5s infinite;
                cursor: pointer;
            }
            &.jacp-version-btn{
                width: 84px;
                height: 71px;
                background-size: 100% 100%;
                background-image: url("~@/assets/images/<EMAIL>");
                border-left: none;
                cursor: pointer;
                transition:margin-top .3s;
                -webkit-transition:margin-top .3s;
                &:hover {
                    margin-top: -5px;
                }
            }
            &.jacp-timemachine-btn{
                width: 96px;
                height: 70px;
                background-size: 100% 100%;
                border-left: none;
                cursor: pointer;
                transition:margin-top .3s;
                -webkit-transition:margin-top .3s;
                &:hover {
                    margin-top: -5px;
                }
            }
        }
    }
    &__bg{
        position: absolute;
        width: 410px;
        right: -24px;
        bottom: -16px;
        width: 431px;
    }
    &-avatar{
        width: 64px;
        border-radius: 50%;
        overflow: hidden;
        img{
            width: 100%;
        }
    }
    @keyframes changBtnPic{
        from {
            background-image: url("~@/assets/images/timeLine/<EMAIL>");
        }
        to {
            background-image: url("~@/assets/images/timeLine/<EMAIL>");
        }
    }
}
</style>
