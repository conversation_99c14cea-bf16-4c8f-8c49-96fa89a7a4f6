<template>
    <div
        :class="['jacp-fakewidget', { disabled }]"
        :style="{
            width,
        }"
    >
        <div class="jacp-fakewidget-header">
            <span class="j-text-overflow">{{ title }}</span>
            <div class="jacp-fakewidget-header-extra">
                <slot name="extra" />
            </div>
        </div>
        <div
            v-if="!isCustomized"
            :class="['jacp-fakewidget-content', fakeStyle ? fakeStyle : 'lines']"
        />
        <component
            v-else
            class="jacp-fakewidget-content"
            :is="fakeStyle"
        />
        <slot />
    </div>
</template>
<script>
import isPlainObject from 'lodash/isPlainObject';

export function isFakeStyleValid(val) {
    if (!val) {
        return false;
    }
    if (isPlainObject(val)) {
        return true;
    }
    return 'lines, article, sudoku, block, article_large, lines_blod'.split(',').map(item => item.trim() === val);
}
export default {
    name: 'LayoutViewFakeWidget',
    props: {
        title: String,
        disabled: Boolean,
        fakeStyle: {
            type: [String, Object],
            validator: isFakeStyleValid,
        },
        width: {
            type: [Number, String],
            default: 100,
        },
    },
    computed: {
        isCustomized() {
            return isPlainObject(this.fakeStyle);
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-fakewidget{
  margin: 12px 8px 0 8px;

  &-header{
    color: @fontSecendColor;
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 12px;
    overflow: hidden;
    span{
      width: 120px;
      overflow: hidden;
      text-align: left;
    }
  }
  &.disabled &-header-extra,
  &-header-extra{
    display: none;
  }
  &-content{
    width: 100%;
    background: #fff;
    border-radius:2px;
    box-shadow:0px 1px 4px 0px rgba(51,51,51,0.08);
    transition: all .3s;
    & img{
      width: 100%;
    }
    @list: lines, article, sudoku, block, article_large, lines_blod;
    each(@list, {
      &.@{value} {
        content: url('~@/assets/images/fake_@{value}@2x.png');
      }
    });
  }
  &:not(.disabled):hover{
     .jacp-fakewidget-content{
      box-shadow:0px 4px 8px 0px rgba(51,51,51,0.2);
    }
    .jacp-fakewidget-header-extra{
      display: block;
      color: @primaryColor;
      cursor: pointer;
      flex-shrink: 0;
    }
  }
}
</style>
