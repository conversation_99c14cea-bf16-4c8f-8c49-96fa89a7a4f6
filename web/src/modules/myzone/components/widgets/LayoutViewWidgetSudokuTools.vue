<template>
    <border-card class="sudo-root" title="热门工具" :span="1" position="right">
        <ul v-if="list.length > 0" class="jacp-toolsList-root">
            <li class="jacp-toolsList-item" v-for="item in list" :key="item.code">
                <a :href="item.link" :target="item.name">
                    <img class="jacp-toolsList-item__icon" :src="item.icon" alt="">
                    <span class="jacp-toolsList-item__label">{{ item.name }}</span>
                </a>
            </li>
        </ul>
        <jacp-empty
            v-if="!list.length"
            label="暂无数据"
        />
    </border-card>
</template>
<script>
import { jacpArchHttp } from '@/plugins/http';
const url = window.location.origin;

export default {
    name: 'LayoutViewWidgetSudokuTools',
    data() {
        return {
            list: [],
        };
    },
    created() {
        jacpArchHttp.get('/desktop/view/hotTools').then(res => {
            if (res.data.code === 200) {
                const { data = [] } = res.data;
                this.list = data.map(i => {
                    return {
                        name: i.name,
                        icon: i.icon,
                        link: `${url}/${i.linkUrl}`
                    }
                })
            }
        })
    }
};
</script>
<style lang="less">
@import '~@/theme/var';

.sudo-root {
    position: relative;
    min-height: 258px;
    .layout-card-content {
        padding: 0;
    }
}

.jacp-toolsList {
    &-root {
        display: flex;
        align-items: center;
        align-content: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding-top: 16px;
    }

    &-item {
        box-sizing: border-box;
        margin-bottom: 16px;
        width: 33%;
        list-style: none;
        text-align: center;

        &__icon {
            width: 46px;
            height: 46px;
            display: inline-block;
            background-size: 100%;
            background-position: center;
            border-radius: 16px;
        }

        &__label {
            display: block;
            color: @fontSecendColor;

            .jacp-toolsList-item:hover & {
                color: @primaryColor;
            }
        }
    }

}
</style>
