import LayoutViewWidgetSudokuTools from './LayoutViewWidgetSudokuTools';
import LayoutViewWidgetNewsFeed from './LayoutViewWidgetNewsFeed';
import LayoutViewWidgetHelpCenter from './LayoutViewWidgetHelpCenter';
import LayoutViewWidgetNavigation from './LayoutViewWidgetNavigation';

const widgets = [
    {
        key: LayoutViewWidgetNewsFeed.name,
        fakeStyle: 'lines_blod',
        component: LayoutViewWidgetNewsFeed,
        type: 2,
        title: '公告栏',
    },
    {
        key: LayoutViewWidgetSudokuTools.name,
        fakeStyle: 'sudoku',
        component: LayoutViewWidgetSudokuTools,
        type: 2,
        title: '常用工具',
    },
    {
        key: LayoutViewWidgetHelpCenter.name,
        fakeStyle: 'sudoku',
        component: LayoutViewWidgetHelpCenter,
        type: 2,
        title: '帮助中心',
    }, {
        key: LayoutViewWidgetNavigation.name,
        component: LayoutViewWidgetNavigation,
        type: 2,
        title: '快捷导航',
    },
];

export default widgets;
