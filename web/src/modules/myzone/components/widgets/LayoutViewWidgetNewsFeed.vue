<template>
    <border-card
        class="jacp-newsfeed"
        :title="`公告栏(${totalAll || 0})`"
        :height="320"
        position="right"
    >
        <!-- <span
            slot="extra"
            :class="{'jacp-newsfeed-disabled': !totalAll}"
            @click="removeAllMessage"
        >清空全部</span> -->
        <el-tooltip
            placement="left"
            content="清空全部公告"
            slot="extra"
        >
            <i
                class="el-icon-close"
                @click="removeAllMessage"
            />
        </el-tooltip>
        <el-tabs v-model="currentTab">
            <el-tab-pane
                label="需求"
                name="demand"
            >
                <el-badge
                    is-dot
                    slot="label"
                    :hidden="!demandUnReadNum"
                >
                    <span>需求</span>
                </el-badge>
                <feed-list
                    :list="demandMessageList"
                    :total="demandTotal"
                    :type="1"
                    @load="loadMoreMessage"
                    @remove="removeMessage"
                    @read="markMessageRead"
                />
            </el-tab-pane>
            <el-tab-pane
                label="卡片"
                name="card"
            >
                <el-badge
                    is-dot
                    slot="label"
                    :hidden="!cardUnReadNum"
                >
                    <span>卡片</span>
                </el-badge>
                <feed-list
                    :list="cardMessageList"
                    :total="cardTotal"
                    :type="3"
                    @load="loadMoreMessage"
                    @remove="removeMessage"
                    @read="markMessageRead"
                />
            </el-tab-pane>
            <el-tab-pane
                label="@消息"
                name="at"
            >
                <el-badge
                    is-dot
                    slot="label"
                    :hidden="!atUnReadNum"
                >
                    <span>@消息</span>
                </el-badge>
                <feed-list
                    :list="atMessageList"
                    :total="atTotal"
                    :type="4"
                    @load="loadMoreMessage"
                    @remove="removeMessage"
                    @read="markMessageRead"
                />
            </el-tab-pane>
        </el-tabs>
    </border-card>
</template>
<script>
import { MyZoneModel } from '@/models/myzone';
import FeedList from '$module/components/feedList';

export default {
    name: 'LayoutViewWidgetNewsFeed',
    components: { FeedList },
    data() {
        return {
            currentTab: 'demand',
            demandMessageList: [],
            demandUnReadNum: 0,
            demandTotal: 0,
            cardMessageList: [],
            cardUnReadNum: 0,
            cardTotal: 0,
            atMessageList: [],
            atUnReadNum: 0,
            atTotal: 0,
            totalAll: 0,
            typeMap: {
                1: 'demand',
                3: 'card',
                4: 'at',
            },
        };
    },
    created() {
        this.getAllMessage();
    },
    methods: {
        getAllMessage() {
            MyZoneModel.getAllMessage().then((data) => {
                this.totalAll = data.all.totalNum;
                this.demandMessageList = data.demand.data;
                this.cardMessageList = data.card.data;
                this.atMessageList = data.at.data;
                this.demandUnReadNum = data.demand.unreadNum;
                this.cardUnReadNum = data.card.unreadNum;
                this.atUnReadNum = data.at.unreadNum;
                this.demandTotal = data.demand.totalNum;
                this.cardTotal = data.card.totalNum;
                this.atTotal = data.at.totalNum;
            });
        },
        loadMoreMessage(messageType, lastMessageCTIme) {
            MyZoneModel.getMoreMessage({
                latestTime: lastMessageCTIme,
                type: messageType,
            }).then((data) => {
                this[`${this.typeMap[messageType]}MessageList`] = this[`${this.typeMap[messageType]}MessageList`].concat(data);
            });
        },
        markMessageRead(idList, messageType) {
            MyZoneModel.setMessageRead({ ids: idList });
            this[`${this.typeMap[messageType]}UnReadNum`] -= 1;
        },
        removeMessage(id, messageType, messageStatus) {
            MyZoneModel.removeFeed(id).then(() => {
                this.totalAll -= 1;
                this[`${this.typeMap[messageType]}Total`] -= 1;
                if (messageStatus === 0) {
                    this[`${this.typeMap[messageType]}UnReadNum`] -= 1;
                }
                this.$notify({
                    title: '删除成功！',
                    type: 'success',
                    duration: 2000,
                });
            });
        },
        removeAllMessage() {
            this.$confirm('此操作将清空全部类型动态消息, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                MyZoneModel.removeAllMessage().then(() => {
                    this.$notify({
                        title: '删除成功！',
                        type: 'success',
                        duration: 2000,
                    });
                    this.getAllMessage();
                });
            });
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-newsfeed{
    .layout-card-content{
        position: relative;
        padding: 0;
    }
    & .el-tabs__header{
        margin-bottom: 5px;
    }
    & .layout-card-content{
        overflow-y: hidden;
    }
    & .el-badge__content.is-fixed{
        top: 6px;
        right: 4px;
        background-color: #2695F1;
    }
}
</style>
