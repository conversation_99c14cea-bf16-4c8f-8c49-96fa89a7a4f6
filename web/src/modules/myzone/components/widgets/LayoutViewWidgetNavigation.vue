<template>
    <layout-card
        :title="title"
        class="jacp-plain"
        position="right"
    >
        <i
            class="el-icon-plus navigation-plus"
            @click="addNavigation"
            slot="extra"
        />
        <el-dialog
            :title="navigatorTitle"
            :visible.sync="navigatorVisible"
            width="30%"
        >
            <el-form
                label-position="top"
                :model="navigatorForm"
                ref="navigatorForm"
                :rules="rules"
            >
                <el-form-item
                    label="标题"
                    prop="name"
                >
                    <el-input
                        v-model="navigatorForm.name"
                        placeholder="请输入标题"
                    />
                </el-form-item>
                <el-form-item
                    label="URL链接"
                    prop="url"
                >
                    <el-input
                        v-model="navigatorForm.url"
                        placeholder="请输入URL链接"
                    />
                </el-form-item>
            </el-form>
            <span
                slot="footer"
                class="dialog-footer"
            >
                <el-button @click="closeNaigator">取 消</el-button>
                <el-button
                    type="primary"
                    @click="confirmNaigator"
                >确 定</el-button>
            </span>
        </el-dialog>
        <div
            v-if="navigatorTable.length > 0"
        >
            <ul>
                <li
                    v-for="item in navigatorTable"
                    :key="item.id"
                    class="navigation-li"
                >
                    <div>
                        <img
                            src="../../../../assets/images/app_store_icons_icon-link-default.png"
                            alt=""
                            class="navigation-li-img"
                        >
                        <span @click="openUrl(item)">
                            {{ item.name }}
                        </span>
                    </div>
                    <div>
                        <i
                            class="el-icon-edit navigation-li-icon"
                            @click="navigatorEdit(item)"
                        />
                        <i
                            class="el-icon-delete navigation-li-icon"
                            @click="navigatorDelete(item)"
                        />
                    </div>
                </li>
            </ul>
        </div>
        <div
            class="navigation-empty"
            v-else
        >
            <img
                src="../../../../assets/images/static_images_empty_empty-xs.png"
                alt=""
            >
            <div>点击快捷导航右侧+号</div>
            <div>快来添加几条您的快捷导航页面吧！</div>
        </div>
    </layout-card>
</template>
<script type="text/javascript">
import { MyZoneModel } from '@/models/myzone';

export default {
    name: 'LayoutViewWidgetNavigation',
    components: {
    },
    props: {
        title: {
            default: '快捷导航',
            type: String,
        },
    },
    data() {
        return {
            navigatorVisible: false,
            navigatorTitle: '添加快捷导航',
            navigatorForm: {
                name: '',
                url: '',
                id: '',
            },
            navigatorTable: [],
            rules: {
                name: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { max: 20, message: '名称不能超过20个字', trigger: 'blur' },
                ],
                url: [
                    { required: true, message: '请输入链接', trigger: 'blur' },
                ],
            },
            edit: false,
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            MyZoneModel.queryFastLink({
                current: 1,
                size: 10,
                orderType: 'desc',
                orderField: 'create_time',
            }).then((res) => {
                this.navigatorTable = res.records;
            });
        },
        openUrl(item) {
            window.open(item.url, '_blank');
        },
        addNavigation() {
            this.navigatorTitle = '添加快捷导航';
            this.navigatorVisible = true;
            this.edit = false;
        },
        reset() {
            this.$refs.navigatorForm.resetFields();
            this.$refs.navigatorForm.clearValidate();
            this.navigatorForm.name = '';
            this.navigatorForm.url = '';
            this.navigatorForm.id = '';
        },
        closeNaigator() {
            this.navigatorVisible = false;
            this.reset();
        },
        confirmNaigator() {
            this.$refs.navigatorForm.validate((valid) => {
                if (valid) {
                    MyZoneModel[this.edit ? 'updateFastLink' : 'addFastLink'](this.navigatorForm).then(() => {
                        this.$message.success(this.edit ? '修改成功!' : '添加成功!');
                        this.init();
                        this.closeNaigator();
                    });
                }
            });
        },
        navigatorEdit(data) {
            this.navigatorTitle = '编辑快捷导航';
            this.navigatorVisible = true;
            this.edit = true;
            this.navigatorForm.name = data.name;
            this.navigatorForm.url = data.url;
            this.navigatorForm.id = data.id;
        },
        navigatorDelete(data) {
            this.navigatorForm.id = data.id;
            this.$confirm('确定要删除当前的快捷导航吗?').then(() => {
                MyZoneModel.deleteFastLink(this.navigatorForm).then(() => {
                    this.$message.success('删除成功!');
                    this.init();
                });
            }).catch(() => {
                this.$message.info('取消成功');
            });
        },
    },
    watch: {
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-plain-list{
    &-item{
        &__content .red-time {
            color: @redColor;
        }
    }
}
.navigation-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 14px;
}
.navigation-li {
    display: flex;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    &:hover {
        background-color: rgba(240,242,245,1);
        border-radius: 10px;
    }
    &-img {
        width: 20px;
        height: 20px;
        line-height: 40px;
        margin: 0 8px;
        vertical-align: middle;
    }
    &-icon {
        font-size: 16px;
        margin-right: 8px;
    }
}
.navigation-plus {
    font-size: 16px;
    padding: 3px;
    &:hover {
        background-color: rgba(222,226,230,1);
        border-radius: 5px;
        color: rgba(144,147,153,1)!important;
    }
}
</style>
