<template>
    <div class="jacp-newsfeed-root">
        <ul>
            <li
                class="jacp-newsfeed-item"
                v-for="(item, index) in list"
                :key="item.id"
            >
                <div
                    class="jacp-newsfeed-item-left j-text-overflow j-mgr24"
                    :class="{'jacp-newsfeed-item_read': item.status === 0}"
                >
                    <a
                        :title="item.title"
                        :href="item.link"
                        :class="['jacp-newsfeed-item__link', item.status ? 'read' : '']"
                        target="_blank"
                        @click="setRead(item)"
                    >{{ item.title }}</a>
                </div>
                <span class="jacp-newsfeed-item__time">{{ item.cTime | dateFormatter }}</span>
                <div class="jacp-newsfeed-item-right">
                    <a
                        href="#"
                        class="jacp-newsfeed-item__link"
                        @click.prevent="remove(item.id, index, item.status)"
                    ><i class="el-icon-close" /></a>
                </div>
            </li>
        </ul>
        <div style="text-align: center">
            <a
                href="#"
                v-if="totalNum !== list.length"
                style="line-height: 48px;"
                @click="getMoreMessage"
            >查看更多</a>
        </div>
        <jacp-empty
            v-if="!list.length"
            label="暂无消息"
        />
    </div>
</template>
<script>

export default {
    name: 'FeedList',
    props: {
        list: {
            type: Array,
            default: () => [],
        },
        type: {
            type: Number,
            // default: '',
        },
        total: {
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            totalNum: this.total,
        };
    },
    methods: {
        getMoreMessage() {
            this.$emit('load', this.type, this.list[this.list.length - 1].cTime);
        },
        remove(id, index, status) {
            this.totalNum -= 1;
            this.list.splice(index, 1);
            this.$emit('remove', id, this.type, status);
        },
        setRead(message) {
            const messageTmp = message;
            if (message.status === 0) {
                messageTmp.status = 1;
                this.$emit('read', [message.id], this.type);
            }
        },
    },
    filters: {
        dateFormatter(cTime) {
            const creatDate = new Date(cTime);
            // 获取创建时间和今天的毫秒数 精确到天
            const dateString = creatDate.toLocaleDateString();
            const todayString = new Date().toLocaleDateString();
            // 获取创建时间对应的月日时分
            const month = creatDate.getMonth() + 1 > 9 ? `${creatDate.getMonth() + 1}` : `0${creatDate.getMonth() + 1}`;
            const day = creatDate.getDate() > 9 ? `${creatDate.getDate()}` : `0${creatDate.getDate()}`;
            const hours = creatDate.getHours() > 9 ? `${creatDate.getHours()}` : `0${creatDate.getHours()}`;
            const minutes = creatDate.getMinutes() > 9 ? `${creatDate.getMinutes()}` : `0${creatDate.getMinutes()}`;
            // 当天创建消息显示时分 否则显示创建日期对应月日
            return todayString === dateString ? `${hours}:${minutes}` : `${month}-${day}`;
        },
    },
    watch: {
        total(v) {
            this.totalNum = v;
        },
    },
};
</script>
<style lang="less">
@import '~@/theme/var';
.jacp-newsfeed{
    &-root{
        height: 238px;
        overflow: auto;
        margin: 8px 0;
        & ul{
            padding: 0;
        }
    }
    &-item{
        line-height: 48px;
        overflow: hidden;
        color: @fontColor;
        display: flex;
        justify-content: space-between;
        padding: 0 14px;
        position: relative;
        & a[href]{
            color: @fontColor;
        }
        &:hover{
            background-color: @hoverBgColor;
            & .jacp-newsfeed-item__time{
                visibility: hidden;
            }
            & .jacp-newsfeed-item-right{
                opacity: 1;
                visibility: visible;
            }
        }
        &-right{
            position: absolute;
            right: 8px;
            visibility: hidden;
            opacity: 0;
            // transition: all .2s ease-in;
            width: 20px;
            height: 48px;
        }
        &__time{
            flex-shrink: 0;
        }
        &_read::before{
            content: " ";
            border: 3px solid #2695F1;
            border-radius: 3px;
            position: absolute;
            z-index: 1;
            left: 0;
            top: 22px;
        }
    }
}
</style>
