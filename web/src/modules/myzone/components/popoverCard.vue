<template>
    <el-popover
        placement="bottom"
        width="140"
        ref="popoverSH"
        trigger="click"
    >
        <slot
            name="reference"
            slot="reference"
        />
        <div>
            <ul>
                <draggable
                    handle=".mover"
                    v-model="data"
                    force-fallback="true"
                    animation="500"
                >
                    <li
                        v-for="item in data"
                        :key="item.title"
                        class="popver-li"
                    >
                        <div class="popver-content">
                            <span>
                                <i
                                    class="jacp-icon jacp-icon-yidong mover"
                                />
                                {{ item.title }}
                            </span>
                            <el-switch
                                v-model="item.status"
                                class="popver-switch"
                            />
                        </div>
                    </li>
                </draggable>
            </ul>
            <div class="popver-footer">
                <el-button
                    type="text"
                    style="color: rgba(144,147,153, 1);"
                    @click="popverCancel"
                >
                    取消
                </el-button>
                <el-button
                    type="text"
                    @click="popverConfirm"
                >
                    确定
                </el-button>
            </div>
        </div>
    </el-popover>
</template>
<script>
import draggable from 'vuedraggable';
import cloneDeep from 'lodash/cloneDeep';

export default {
    name: 'PopoverCard',
    components: {
        draggable,
    },
    props: {
        checkList: {
            type: Array,
            default: () => [],
        },
        workspaceManager: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            data: cloneDeep(this.checkList),
            visible: false,
        };
    },
    methods: {
        popverConfirm() {
            this.$refs.popoverSH.doClose();
            console.log(this.data, 'this.data');
            this.workspaceManager.saveWidgets(this.data);
        },
        popverCancel() {
            this.$refs.popoverSH.doClose();
            this.data = cloneDeep(this.checkList);
        },
    },
    watch: {
        checkList: {
            handler() {
                this.data = cloneDeep(this.checkList);
                console.log('-- ~ file: popoverCard.vue:97 ~ this.checkList:', this.checkList);
            },
        },
    },
    computed: {
    },
};
</script>
<style lang="less" scoped>
.popver-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .el-switch {
        height: 16px;
        width: 28px;
        line-height: 16px;
        /deep/ .el-switch__core {
            height: 16px !important;
            width: 28px !important;
            line-height: 16px !important;
            &:after {
                width: 12px;
                height: 12px;
            }
        }
    }
    /deep/ .el-switch.is-checked .el-switch__core:after {
        margin-left: -13px;
    }
    i {
        cursor: move;
    }
}
.popver-li {
    list-style-type:none;
    height: 40px;
}
.popver-footer {
    border-top: 1px solid #ebeef5;
    text-align: right;
}
</style>
