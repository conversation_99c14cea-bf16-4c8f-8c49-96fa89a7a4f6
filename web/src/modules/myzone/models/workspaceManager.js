import Vue from 'vue';
import { MyZoneModel } from '@/models/myzone';
import uniqBy from 'lodash/uniqBy';
import store from '$platform.store';

export default class WorkspaceManager {
    constructor() {
        this.activedView = '';
        this.checkedWidgets = [];
        this.viewOptions = [];
        this.remoteAllWidgets = [];
        this.widgetMap = {};
    }

    async init() {
        this.loadWidgetsByView();
    }

    // get widgetMap() { return this.#widgetMap; }

    addWidgets(module, widgets = []) {
        if (!Array.isArray(widgets)) {
            console.warn('[个人工作台]期望注册组件列表为<Array>，否则不会生效。异常模块：', module.key, widgets);
            return;
        }
        widgets.forEach((widget) => {
            // console.debug(`[个人工作台]应用<${module.key}>追加了工作台小组件<${widget.key}>`, widget);
            Vue.set(this.widgetMap, widget.key, widget);
        });
    }

    getLeftTab() {
        return this.checkedWidgets.filter(i => i.type === 1);
    }

    getRightTab() {
        return this.checkedWidgets.filter(i => i.type === 2);
    }

    switchView(view) {
        this.activedView = view;
        // this.loadWidgetsByView(view);
    }

    // 仅切换视图
    saveView(view) {
        return MyZoneModel.setLayoutView(view).then(() => {
            this.activedView = view;
            return view;
        });
    }

    saveWidgetsByView(widgets = [], view) {
        const result = uniqBy(widgets, 'key');
        const data = result.filter(i => i.checked);
        return MyZoneModel.setLayoutViewComponents(
            view || this.activedView,
            data.map(o => ({ enName: o.key })),
        )
            .then(() => {
                this.checkedWidgets = data;
            });
    }

    saveWidgets(widgets = []) {
        let data = this.checkedWidgets.filter((i) => {
            if (widgets.every(item => item.key !== i.key)) {
                return true;
            }
            return false;
        });
        data = data.concat(widgets);
        return MyZoneModel.setLayoutViewComponents(
            data.map(o => ({
                ...o,
                enName: o.key,
                status: o.status ? 1 : 0,
            })),
        )
            .then(() => {
                this.checkedWidgets = data;
            });
    }

    loadViews() {
        return MyZoneModel.getLayoutView().then((view = {}) => {
            const { desktopDefaultView, desktopViews = [] } = view;
            // 后段返回的默认值
            this.activedView = desktopDefaultView.enName;
            // 研发试图列表
            this.viewOptions = desktopViews;
            return desktopViews;
        });
    }

    loadWidgetsByView(params) {
        return MyZoneModel.getLayoutViewComponents(params)
            .then((data = []) => {
                // 返回 jacp myzone 我的任务，动态消息，帮助中心
                if (!store.state.app.helpDoc) {
                    data = data.filter(i => i.key !== 'LayoutViewWidgetHelpCenter');
                }
                this.checkedWidgets = data.map(i => ({
                    ...i,
                    status: !!i.status,
                }));
                return data;
            });
    }

    loadAllWidgets() {
        return MyZoneModel.getAllLayoutViewComponents().then((data = []) => {
            this.remoteAllWidgets = data;
            return data;
        });
    }
}

export const workspaceManager = Vue.observable(new WorkspaceManager());
