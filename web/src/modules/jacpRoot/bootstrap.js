/* eslint-disable */
import JModule from 'JModule';
import Vue from 'vue';
import Http from '@jmodule/http';
import Utils from '@/plugins/utils';
import XSS from '@/plugins/xss';
import zh from '@/locale/custom/zh-cn';
import en from '@/locale/custom/en';
import i18n from '$platform.i18n';
import Components from '@/components';
import index from './index';
import Directives from '@/directives';
import Filters from '@/filters';
import '@/theme/index.less';

i18n.mergeLocaleMessage('zh-cn', zh);
i18n.mergeLocaleMessage('en', en);
Vue.use(Utils);
Vue.use(Components);
Vue.use(Http);
Vue.use(XSS);
Vue.use(Directives);
Vue.use(Filters);
/**
 * 独立模块导出
 *
 * @param key       { String }              [required]  moduleKey
 * @param init      { Funciton }            [optional]  初始化函数，参数为模块实例
 * @param routes    { Array<RouteConfig> }  [optional]  路由定义
 * @param imports   { Array<String> }       [optional]  声明模块依赖关系，moduleKey列表
 * @param exports   { Object }              [optional]  需要暴露给其它的模块的内容
 */


JModule.define({
    key: 'jacpRoot',
    // defaultRouteName: '需求管理',
    ...index,
    init(module) {
        console.log('module init', module);
    },
});
