/* eslint-disable */
import demand from '@/modules/demand';
import teamspace from '@/modules/teamspace';
import myzone from '@/modules/myzone';
import UserModel from '@/models/user';

export default [
  {
    path: '',
    name: 'jacpRootIndex',
    component: {
      template: '<router-view></router-view>',
    },
    children: [
      ...demand.routes,
      ...teamspace.routes,
      ...myzone.routes,
    ],
    beforeEnter: ((to, from, next) => UserModel.getInfo().then(() => {
      next();
    })),
  },
  // 
];
