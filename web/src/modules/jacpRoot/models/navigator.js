const dataMap = new WeakMap();

export default class Navigator {
    constructor(menus) {
        dataMap.set(this, menus.reduce((res, item) => Object.assign(res, {
            [item.id]: item,
        }), {}));
        this.menus = menus;
        this.activedMenu = menus[0];
    }

    setActiveMenu(route) {
        const activedMenu = this.menus.find(menu => menu.activeFn(route));
        if (activedMenu) {
            this.activedMenu = activedMenu;
        }
    }
}
