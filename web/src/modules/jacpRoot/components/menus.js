// eslint-disable-next-line
import Navigator from '$module/models/navigator';

function getActiveFn(menusId) {
    return (route) => {
        const matchedNames = route.matched.map(item => item.name);
        return matchedNames.includes(menusId);
    };
}
const menus = [{
    id: 'myzone',
    name: '个人工作台',
    routeName: 'myzone',
}, {
    id: 'teamspace',
    name: '团队空间',
    routeName: 'teamspace',
}, {
    id: 'demands',
    name: '需求列表',
    routeName: 'demands',
}, {
    id: 'dataCenterIndex',
    name: '数据报表',
    routeName: 'dataCenterIndex',
}];

export default new Navigator(menus.map(menu => Object.assign(menu, {
    activeFn: getActiveFn(menu.id),
})));
