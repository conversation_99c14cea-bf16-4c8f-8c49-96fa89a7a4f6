<template>
    <div class="jpui-header-nav__list">
        <router-link
            class="jpui-navitem"
            v-for="item in navigatorIns.menus"
            :class="{'jpui-navitem--active': navigatorIns.activedMenu.id === item.id}"
            :key="item.id"
            :to="{'name': item.routeName}"
        >
            {{ item.name }}
        </router-link>
    </div>
</template>

<script>
import navigatorIns from './menus';


export default {
    data() {
        return {
            navigatorIns,
        };
    },
    created() {
        navigatorIns.setActiveMenu(this.$route);
    },
    watch: {
        $route() {
            navigatorIns.setActiveMenu(this.$route);
        },
    },
};
</script>

<style lang="less">
/* 这里的样式从平台读取了 */
</style>
