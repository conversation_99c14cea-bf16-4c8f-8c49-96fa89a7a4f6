/* eslint-disable */

import routes from './routes';
import root from '@/modules/root';
import demand from '@/modules/demand';
import teamspace from '@/modules/teamspace';
// header三剑客
import Navigator from './components/Navigator';
import TopWidget from './components/TopWidget';
import RouteMap from './components/RouteMap';
/* 新增iconfont */
import '@/assets/fonts/style.css';
// import '@/assets/font_jacp/iconfont.css';
export default {
    routes,
    store: {
        ...root.store,
        ...demand.store,
        ...teamspace.store,
    },
    imports: [],
    exports: {
        $navigator: Navigator,
        $topWidget: TopWidget,
        $routeMap: RouteMap,
    },
};
