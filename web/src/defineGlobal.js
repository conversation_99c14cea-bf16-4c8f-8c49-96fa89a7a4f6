/* eslint-disable no-underscore-dangle */
import { JModule } from '@jmodule/client';
import { MenuBridge } from '@/modules/root/models/menuBridge';
import { navigateToUrl } from '@/modules/root/utils/patchRoute';

export function def(obj = {}, key = '', val, writable, enumerable) {
    Object.defineProperty(obj, key, {
        value: val,
        enumerable: !!enumerable,
        writable: !!writable,
        configurable: true,
    });
}

// defineGlobal
[
    { key: 'JModule', value: JModule },
    { key: '__jagile__env__', value: true },
    { key: '__jagile__navigate__', value: navigateToUrl },
    { key: '__jagile__navigate__submenu__', value: MenuBridge },
].forEach(({ key, value }) => {
    if (typeof value === 'undefined') {
        def(window, key, window[key], false);
    } else {
        def(window, key, value, false);
    }
});
// async
// User里还有一个__jagile__user__
