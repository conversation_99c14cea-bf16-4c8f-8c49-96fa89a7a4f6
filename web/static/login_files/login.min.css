html,
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
p,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
input,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
textarea,
article,
aside,
audio,
canvas,
figure,
footer,
header,
mark,
menu,
nav,
section,
time,
video {
    margin: 0;
    padding: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
    font-weight: normal;
}
article,
aside,
dialog,
figure,
footer,
header,
hgroup,
nav,
section,
blockquote {
    display: block;
}
ul,
ol {
    list-style: none;
}
img {
    border: 0 none;
    vertical-align: top;
}
blockquote,
q {
    quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
    content: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
strong,
em,
i {
    font-style: normal;
    font-weight: normal;
}
ins {
    text-decoration: underline;
}
del {
    text-decoration: line-through;
}
mark {
    background: 0;
}
input::-ms-clear {
    display: none !important;
}
body {
    font: 12px/1.5 \5fae\8f6f\96c5\9ed1, \5b8b\4f53, "Hiragino Sans GB", STHeiti,
        "WenQuanYi Micro Hei", "Droid Sans Fallback", SimSun, sans-serif;
    background: #fff;
}
a {
    text-decoration: none;
    color: #333;
}
a:hover {
    text-decoration: underline;
}
.ly_box {
    display: -webkit-box;
    display: flex;
}
.ly_box.ly_box_column {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
}
.ly_box_flexone {
    -webkit-box-flex: 1;
    flex: 1;
    width: 0;
}
.ly_multiline {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}
body {
    background: #f0f5f9;
}
html,
body,
.login {
    height: 99%;
    overflow: hidden;
}
.hide {
    display: none;
}
.login {
    font-size: 0;
    text-align: center;
}
.login:after,
.login .valign_after {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    width: 0;
    height: 100%;
    vertical-align: middle;
}
.login:after {
    content: "";
}
.login .valign_wrap {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: middle;
    font-size: 14px;
}
.login .login_pop {
    margin-top: 120px;
    text-align: left;
    box-shadow: 0 10px 25px 5px rgba(0, 0, 0, 0.1);
}
.login .login_pop_inner {
    position: relative;
    padding: 40px;
    width: 300px;
    height: 320px;
    background: #fff;
    border-radius: 4px;
}
.login .login_pop_inner.login_withpc:before,
.login .login_pop_inner.login_withpc .login_withpc_logo {
    content: "";
    display: block;
    margin: 0 auto;
    width: 155px;
    height: 50px;
    background: url(../images/logo_2021.png);
}
[data-dpr="2"] .login .login_pop_inner.login_withpc:before,
[data-dpr="2"] .login .login_pop_inner.login_withpc .login_withpc_logo {
    background: url(../images/logo_2017.png);
    background-size: 100%;
}
.login .login_pop_inner.login_withqrc {
    display: none;
    text-align: center;
    color: #808080;
}
.login .login_pop.withqrc .login_withpc {
    display: none;
}
.login .login_pop.withqrc .login_withqrc {
    display: block;
}
.login .login_form_row {
    border: 0;
}
.login .login_form_row.account .login_form_label,
.login .login_form_row.password .login_form_label {
    *zoom: 1;
    display: block;
    border: 1px solid #c4c4c4;
    border-radius: 4px;
    overflow: hidden;
}
.login .login_form_row.account .login_form_label:before,
.login .login_form_row.account .login_form_label:after,
.login .login_form_row.password .login_form_label:before,
.login .login_form_row.password .login_form_label:after {
    content: " ";
    display: table;
}
.login .login_form_row.account .login_form_label:after,
.login .login_form_row.password .login_form_label:after {
    clear: both;
}
.login .login_form_row.account .login_form_icon,
.login .login_form_row.password .login_form_icon {
    float: left;
    width: 38px;
    height: 38px;
}
.login .login_form_row.account .login_form_inp,
.login .login_form_row.password .login_form_inp {
    margin-left: 38px;
    *margin-left: 0;
    *height: 38px;
}
.login .login_form_row.account .login_form_inp_input,
.login .login_form_row.password .login_form_inp_input{
    box-sizing: border-box;
    display: block;
    padding: 10px 0;
    width: 259px;
    height: 38px;
    *float: left;
    *height: 18px;
    font-size: 12px;
    line-height: 18px;
    border: 0;
    outline: 0;
    color: #333;
    -webkit-appearance: none;
}
.login .oauth{
    border: 1px solid #c4c4c4;
    border-radius: 4px;
    color: #333;
    font-size: 12px;
    margin-top: 150px;
    cursor: pointer;
    padding-top: 100;
    /* margin-top: 100px; */
}
.oauth .oauth-select {
    width: 250px;
    height: 40px;
    border: 0;
    outline: 0;
    display: inline-block;
    font-size: 12px;
    line-height: 40px;
    color: #333;
    -webkit-appearance: none;
    cursor: pointer;
}
.option {
    position: relative;
}
.option ul{
    display: block;
    position: absolute;
    /* top:60px; */
    width: 100%;
    box-shadow: 0px 0px 46px 0px
    rgba(117, 117, 117, 0.12);
    max-height: 320px;
    overflow-y: auto;
    z-index: 1;
    font-size: 0;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    border: 1px solid #e4e7ed;
    border-radius: 4px;
}
.option ul li {
    font-size: 14px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #606266;
    height: 34px;
    line-height: 34px;
    box-sizing: border-box;
    cursor: pointer;
    background-color: #FFF;
}
.option ul .option-li-disable {
    color: #c0c4cc;
    cursor: not-allowed;
}
.option ul li:hover {
    background-color: #f5f7fa;
}
.login .login_form_row.account .login_form_inp_input::-webkit-input-placeholder,
.login .login_form_row.account .login_form_inp_input::-moz-input-placeholder,
.login .login_form_row.account .login_form_inp_input::-ms-input-placeholder,
.login
    .login_form_row.password
    .login_form_inp_input::-webkit-input-placeholder,
.login .login_form_row.password .login_form_inp_input::-moz-input-placeholder,
.login .login_form_row.password .login_form_inp_input::-ms-input-placeholder {
    color: #b2b2b2;
}
.login .login_form_row.account .login_form_inp_input:-webkit-autofill,
.login .login_form_row.password .login_form_inp_input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px white inset;
}
.login .login_form_row.account.warn .login_form_label,
.login .login_form_row.password.warn .login_form_label {
    border-color: #f94f3f;
}
.login .login_form_row.account.focus .login_form_label,
.login .login_form_row.password.focus .login_form_label {
    border-color: #0091ff;
}
.login .login_form_row.account .login_form_icon {
    background: url(../images/default-account.png);
    background-size: 100%;
}
[data-dpr="2"] .login .login_form_row.account .login_form_icon {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_form_row.account.warn .login_form_icon {
    background: url(../images/warn-account.png);
    background-size: 100%;
}
[data-dpr="2"] .login .login_form_row.account.warn .login_form_icon {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_form_row.account.focus .login_form_icon {
    background: url(../images/active-account.png);
    background-size: 100%;
}
[data-dpr="2"] .login .login_form_row.account.focus .login_form_icon {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_form_row.password .login_form_icon {
    background: url(../images/default-pass.png);
    background-size: 100%;
}
[data-dpr="2"] .login .login_form_row.password .login_form_icon {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_form_row.password.warn .login_form_icon {
    background: url(../images/warn-pass.png);
    background-size: 100%;
}
[data-dpr="2"] .login .login_form_row.password.warn .login_form_icon {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_form_row.password.focus .login_form_icon {
    background: url(../images/active-pass.png);
    background-size: 100%;
}
.oauth .login_form_icon {
    background: url(../images/default-pass.png);
    background-size: 100%;
    float: left;
    width: 38px;
    height: 38px;
}
[data-dpr="2"] .login .login_form_row.password.focus .login_form_icon {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_form_row.findback {
    text-align: right;
}
.login .login_form_row.findback a {
    font-size: 12px;
    line-height: 1;
    color: #0091ff;
}
.login .login_form_row.formsubmit .formsubmit_btn,
.login .login_form_row.formoauthsubmit .formoauth_btn{
    display: block;
    width: 100%;
    height: 40px;
    font-family: \5fae\8f6f\96c5\9ed1, \5b8b\4f53, "Hiragino Sans GB", STHeiti,
        "WenQuanYi Micro Hei", "Droid Sans Fallback", SimSun, sans-serif;
    font-size: 16px;
    color: #fff;
    background: #2695f1;
    border: 0;
    outline: 0;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 20px;
}
.login .login_form_row.formbacksubmit .formback_btn{
    display: block;
    width: 100%;
    height: 40px;
    font-family: \5fae\8f6f\96c5\9ed1, \5b8b\4f53, "Hiragino Sans GB", STHeiti,
        "WenQuanYi Micro Hei", "Droid Sans Fallback", SimSun, sans-serif;
    font-size: 16px;
    color: #333;
    background: #FFF;
    border: 1px solid #c4c4c4;
    outline: 0;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 60px;
    text-align: center;
}
.login .login_form_row.formbacksubmit .formback_btn:hover{
    background: #ecf5ff;
    color: #409eff;
    border-color: #c6e2ff;
}
.login .login_form_row.formsubmit .formsubmit_btn:hover,
.login .login_form_row.formsubmit .formoauth_btn:hover {
    background: #0d87eb;
}
.login .login_form_row.warntip {
    font-size: 14px;
    font-weight: 600;
    color: #f94f3f;
    text-align: center;
}
.login .login_form_row.warntip .warntip_icon {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    margin-right: 7px;
    width: 16px;
    height: 16px;
    background: url(../images/warn.png);
}
[data-dpr="2"] .login .login_form_row.warntip .warntip_icon {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_form_row.warntip .warntip_text {
    white-space: nowrap;
    word-wrap: normal;
}
.login .login_form_row.warntip .warntip_icon,
.login .login_form_row.warntip .warntip_text {
    vertical-align: middle;
}
.login .login_form_row.account {
    margin-top: 40px;
}
.login .login_form_row.password {
    margin-top: 20px;
}
.login .login_form_row.findback,
.login .login_form_row.formsubmit {
    margin-top: 14px;
}
.login .login_form_row.warntip {
    margin-top: 30px;
}
.login .login_style {
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    width: 72px;
    height: 72px;
}
.login .login_pop_inner.login_withpc .login_style {
    background: url(../images/withqrc.png);
}
[data-dpr="2"] .login .login_pop_inner.login_withpc .login_style {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_pop_inner.login_withqrc .login_style {
    background: url(../images/withpc.png);
}
[data-dpr="2"] .login .login_pop_inner.login_withqrc .login_style {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_withqrc_tit {
    margin-top: 10px;
    *margin-top: 50px;
    line-height: 1;
    font-size: 16px;
}
.login .login_withqrc_code {
    margin: 40px auto 0;
    padding: 10px;
    width: 148px;
    border: 1px solid #e6e6e6;
}
.login .login_withqrc_code:hover {
    border-color: #ccc;
}
.login .login_withqrc_code img {
    display: block;
    width: 150px;
    height: 150px;
}
.login .login_withqrc_tip {
    margin-top: 15px;
    line-height: 1;
    font-size: 12px;
}
.login .login_withqrc_tip a {
    color: #808080;
}
.login .login_withqrc_tip .login_withqrc_tip_sep {
    color: #d2d2d2;
}
.login .login_withqrc_result {
    display: none;
}
.login .login_withqrc_doge {
    margin: 60px auto 0;
    *margin-top: 100px;
    width: 110px;
    height: 100px;
    background: url(../images/fatdog.png);
}
[data-dpr="2"] .login .login_withqrc_doge {
    background: url(../images/<EMAIL>);
    background-size: 100%;
}
.login .login_withqrc_succ {
    margin-top: 25px;
    line-height: 1;
    font-size: 20px;
}
.login .login_withqrc.succ .login_withqrc_wrap {
    display: none;
}
.login .login_withqrc.succ .login_withqrc_result {
    display: block;
}
