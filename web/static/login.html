<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <base href="/static/" />
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
        <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
        <meta name="renderer" content="ie-stand" />
        <title>智航</title>
        <link rel="stylesheet" href="./login_files/sso_login.css" />
        <link rel="stylesheet" href="./login_files/login.min.css" />
    </head>

    <body>
        <div class="login">
            <div class="valign_wrap">
                <!-- +.withqrc 切换 -->
                <div class="login_pop">
                    <div
                        class="login_pop_inner login_withpc"
                        style="height: 350px"
                    >
                        <!--[if lt IE 8
                            ]><span class="login_withpc_logo"></span
                        ><![endif]-->
                        <form
                            class="login_form"
                            method="post"
                            name="login_form"
                            onsubmit="return false;"
                        >
                            <input
                                type="hidden"
                                id="fp"
                                name="fp"
                                value="LHPCPKPJJLBMTV2WIWHTKXXX7ODTI2Q32IIKV66DEE3JIZJ5COE5QTDYO2OUIFIUFWD33ECZRFF336KWMN2CYMDLJA"
                            />
                            <!-- +.warn +.focus +.hide -->
                            <div class="login_form_row account">
                                <label class="login_form_label"
                                    ><i class="login_form_icon"></i>
                                    <div class="login_form_inp">
                                        <input
                                            class="login_form_inp_input"
                                            type="text"
                                            id="username"
                                            name="username"
                                            placeholder="用户名"
                                        />
                                    </div>
                                </label>
                            </div>
                            <div class="login_form_row password">
                                <label class="login_form_label"
                                    ><i class="login_form_icon"></i>
                                    <div class="login_form_inp">
                                        <input
                                            class="login_form_inp_input"
                                            type="password"
                                            id="password"
                                            name="password"
                                            placeholder="密码"
                                        />
                                    </div>
                                </label>
                            </div>
                            <div
                                class="login_form_row oauth hide"
                                style="margin-top: 100px"
                            >
                                <label class="login_form_label"
                                    ><i class="login_form_icon"></i>
                                    <div class="login_form_inp">
                                        <!-- <select class="oauth-select">
                                        <option>虚拟部门</option>
                                        <li>虚拟部门2</li>
                                    </select> -->
                                        <span class="oauth-select">请选择</span>
                                    </div>
                                </label>
                            </div>
                            <div class="login_form_row option hide">
                                <ul class="option-ul"></ul>
                            </div>
                            <div
                                class="login_form_row warntip hide"
                                style="margin-top: 10px"
                            >
                                <span class="warntip_text"></span>
                            </div>
                            <div class="login_form_row formsubmit">
                                <input
                                    class="formsubmit_btn"
                                    type="submit"
                                    value="登  录"
                                />
                            </div>
                            <div class="login_form_row formbacksubmit hide">
                                <button class="formback_btn">返回登录</button>
                            </div>
                            <div class="login_form_row formoauthsubmit hide">
                                <input
                                    class="formoauth_btn"
                                    type="submit"
                                    value="前往行云"
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <script src="./login_files/jquery.min.js"></script>
        <script src="./login_files/md5.min.js" type="text/javascript"></script>
        <script src="./login_files/td.js" type="text/javascript"></script>
        <script src="./login_files/y.html"></script>
        <!-- <script src="https://cdn.bootcss.com/jsencrypt/3.0.0-beta.1/jsencrypt.js"></script> -->
        <script src="./login_files/jsencrypt.js"></script>
        <script>
            var cookieName = "jd.erp.lang";
            initLan();
            // 租户列表
            let optionData = [];
            // 选择的租户数据
            let selectedData = {
                tenantCode: "",
                tenantId: "",
                tenantName: ""
            };
            // 设置Rsa加密
            const jsRsa = new JSEncrypt();
            // 设置公钥
            const PBULIC_KEY_STR =
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvRygUt13UqaNJ+8JLB5VhjAw090SwzZh79gntOlGx5bFk37HCaSQnS8faOlhQV2AO4ozFIPn54JXixZHRscyp3VrzJRY/m1OFMIjHSVyYgxcEY8Sw/+xr3FqeGUwIyNXhAOEF/Y9VyrolYLpvLTAwQlsqFy3yZXjTaMIJgaZwoBxGYMBFoclCeVZB8JalkwmduHalyioam4gR2osYN+dOEsItT9CI391ekoD8p2wXEzWkJBDskxSE77Dfyh3wQ6BQhi9sB8Gwfx1j9H9W+2SNK1GYBeAJr3KX23FPQrKnejdP5KPIHF6EwxtJ5JlyIsNgCaCLBiTXF28hBvFOGuj+QIDAQAB";
            jsRsa.setPublicKey(PBULIC_KEY_STR);
            function initLan() {
                var x = document.cookie;
                var s = getCookie(cookieName);
                if (s != "") {
                    setCookie(cookieName, s);
                    // setSelectChecked("lanSelect",s)
                } else {
                    setCookie(cookieName, "zh_CN");
                }
            }
            function selectLan() {
                var nSel = document.getElementById("lanSelect");
                var index = nSel.selectedIndex;
                var value = nSel.options[index].value;
                setCookie(cookieName, value);
                reloadPage();
            }
            function reloadPage() {
                location.reload();
            }
            function setCookie(name, value, minute = 30 * 24 * 60 * 60) {
                var exp = new Date();
                exp.setTime(exp.getTime() + minute * 1000);
                var cookies =
                    name + "=" + value + ";path=/;expires=" + exp.toGMTString();
                document.cookie =
                    name + "=" + value + ";path=/;expires=" + exp.toGMTString();
                console.log(cookies);
            }
            function getCookie(name) {
                var arr,
                    reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");

                if ((arr = document.cookie.match(reg))) return arr[2];
                else return "";
            }
            function setSelectChecked(selectId, checkValue) {
                var select = document.getElementById(selectId);
                for (var i = 0; i < select.options.length; i++) {
                    if (select.options[i].value == checkValue) {
                        select.options[i].selected = true;
                        break;
                    }
                }
            }
            //删除cookies
            function delCookie(name) {
                var exp = new Date();
                exp.setTime(exp.getTime() - 1);
                var cval = getCookie(name);
                if (cval != null)
                    document.cookie =
                        name + "=" + cval + ";expires=" + exp.toGMTString();
            }
            $(function() {
                var timeout = false;
                var returnUrl = "http://jagile.jd.com/";
                var qrCheck;

                function refreshImage() {
                    var now = new Date();
                    $("#qr_image").attr(
                        "src",
                        "/sso/image?v=33&t=" + now.getTime()
                    );
                    stopCheck();
                    startCheck();
                    $(".warntip").hide();
                }

                function stopCheck() {
                    if (qrCheck != null) {
                        timeout = true;
                        window.clearInterval(qrCheck);
                    }
                }

                function startCheck() {
                    timeout = false;
                    qrCheck = window.setInterval(check, 1000);
                }

                function show_notice(content) {
                    $(".warntip_text").html(content);
                    $(".warntip").show();
                }

                function check() {
                    if (timeout) {
                        return;
                    }
                    $.post(
                        "/sso/check.json",
                        {
                            ReturnUrl: returnUrl,
                            fp: $("#fp").val(),
                            _t: new Date().getTime()
                        },
                        function(data) {
                            if (data.REQ_CODE == -1) {
                                show_notice("登录超时，请刷新后重新扫描");
                                $(".login_withqrc").removeClass("succ");
                                stopCheck();
                            } else if (data.REQ_CODE == 202) {
                                $(".login_withqrc").addClass("succ");
                                show_notice("等待手机客户端确认，请勿刷新页面");
                            } else if (data.REQ_CODE == 205) {
                                stopCheck();
                                window.location.href = data.REQ_DATA;
                            } else if (data.REQ_CODE !== 201) {
                                show_notice(data.REQ_MSG);
                                $(".login_withqrc").removeClass("succ");
                                stopCheck();
                            }
                        },
                        "json"
                    );
                }

                //设备指纹开始
                try {
                    getJdEid(function(eid, fp) {
                        $("#fp").val(eid);
                    });
                } catch (e) {
                    //你的逻辑
                }
                //设备指纹结束

                $(".login_form_inp_input")
                    .focus(function() {
                        $(".warntip").hide();
                        $(".login_form_row.focus").removeClass("focus");
                        $(this)
                            .parents(".login_form_row")
                            .addClass("focus");
                    })
                    .blur(function() {
                        $(".login_form_row.focus").removeClass("focus");
                    });
                $(".login_style").click(function() {
                    $(".login_pop").toggleClass("withqrc");
                    if ($(".login_pop").hasClass("withqrc")) {
                        refreshImage();
                    } else {
                        stopCheck();
                    }
                });

                $("#refresh_code").click(function() {
                    refreshImage();
                });
                $(".formsubmit_btn").click(function(event) {
                    login(event);
                });
                $("#password").keyup(function(event) {
                    if (event.keyCode === 13) {
                        login(event);
                    }
                });
                $(document).keydown(function(event) {
                    if (
                        $(".oauth-select").html() &&
                        $(".oauth-select").html() !== "请选择" &&
                        event.keyCode === 13
                    ) {
                        goLogin(event);
                    }
                });
                $("#username").keyup(function(event) {
                    if (event.keyCode === 13) {
                        login(event);
                    }
                });
                function login(event) {
                    if ($("#username").val() == "") {
                        $(".login_form_row.account").addClass("warn");
                        $(".warntip").show();
                        show_notice("用户名不能为空");
                        event.preventDefault();
                        return;
                    }
                    if ($("#password").val() == "") {
                        $(".login_form_row.password").addClass("warn");
                        $(".warntip").show();
                        show_notice("密码不能为空");
                        event.preventDefault();
                        return;
                    }
                    $(".warntip").hide();
                    event.preventDefault();
                    const username = $("#username").val();
                    const password = $("#password").val();
                    const passwordPsa = jsRsa.encrypt(password);
                    $.post(
                        {
                            url: "/devops/sso/tenant",
                            contentType: "application/json",
                            data: JSON.stringify({
                                username,
                                password: passwordPsa
                            })
                        },
                        res => {
                            if (res.code === 400) {
                                $(".warntip_text").html(res.message);
                                $(".warntip").show();
                                show_notice(res.message);
                            } else {
                                $(".login_form_row.account.warn").removeClass(
                                    "warn"
                                );
                                $(".login_form_row.password.warn").removeClass(
                                    "warn"
                                );
                                $(".option-ul").empty();
                                optionData = res.data;
                                if (optionData.length) {
                                    optionData.forEach(item => {
                                        const el = $(
                                            `<li data-value="${item.tenantId}" title="${item.tenantName}">${item.tenantName}</li>`
                                        );
                                        $(".option-ul").append(el);
                                    });
                                    selectedData = optionData[0];
                                    $(".oauth-select").text(
                                        optionData[0].tenantName
                                    );
                                    if (optionData.length === 1) {
                                        $(".formoauth_btn").click();
                                        return;
                                    }
                                } else {
                                    const el = $(
                                        '<li class="option-li-disable">无数据</li>'
                                    );
                                    $(".option-ul").append(el);
                                }
                                $(".oauth").show();
                                $(".formoauthsubmit").show();
                                $(".formbacksubmit").show();
                                $(".password").hide();
                                $(".account").hide();
                                $(".formsubmit").hide();
                            }
                        }
                    );
                }
                // 点击选择框事件
                $(".oauth-select").click(function(event) {
                    event.stopPropagation();
                    $(".option").toggle();
                });
                // 点击旁边把选择框隐藏
                $(".login").click(function(event) {
                    $(".option").hide();
                });
                $(".option-ul").on("click", "li", function(event) {
                    const selectId = $(this).attr("data-value");
                    selectedData = optionData.find(
                        item => item.tenantId === selectId
                    );
                    $(".oauth-select").text(selectedData.tenantName);
                });
                $(".formoauth_btn").click(function(event) {
                    goLogin(event);
                });
                function goLogin(event) {
                    if ($(".oauth-select").text() == "") {
                        $(".oauth-select").addClass("warn");
                        event.preventDefault();
                        $(".warntip_text").html("租户不能为空");
                        $(".warntip").show();
                    }
                    event.preventDefault();
                    const username = $("#username").val();
                    const password = $("#password").val();
                    const passwordPsa = jsRsa.encrypt(password);
                    // const passwordPsa = password;
                    $.post(
                        {
                            url: "/devops/sso/login",
                            contentType: "application/json",
                            data: JSON.stringify({
                                username,
                                password: passwordPsa,
                                tenantId: selectedData.tenantId
                            })
                        },
                        res => {
                            if (res.code === 400) {
                                $(".warntip_text").html(res.message);
                                $(".warntip").show();
                                show_notice(res.message);
                            } else {
                                const {
                                    token,
                                    tokenHead,
                                    refreshToken,
                                    expiresIn
                                } = res.data;
                                const oauthData = `${tokenHead}${token}`;
                                const expiresInTime =
                                    new Date().getTime() + expiresIn * 1000;
                                setCookie("ARCH-TOKEN", token, expiresIn);
                                localStorage.setItem(
                                    "refreshToken",
                                    refreshToken
                                );
                                localStorage.setItem(
                                    "Authorization",
                                    oauthData
                                );
                                localStorage.setItem(
                                    "tenantId",
                                    selectedData.tenantId
                                );
                                localStorage.setItem(
                                    "expiresIn",
                                    expiresInTime
                                );
                                if (typeof res.data === "object") {
                                    if (res.data.returnUrl) {
                                        window.location.href =
                                            res.data.returnUrl;
                                    } else {
                                        window.location.href =
                                            window.location.origin;
                                    }
                                } else if (typeof res.data === "string") {
                                    window.location.href =
                                        window.location.origin;
                                }
                            }
                        }
                    );
                }
                $(".formback_btn").click(function(event) {
                    $(".oauth").hide();
                    $(".formoauthsubmit").hide();
                    $(".formbacksubmit").hide();
                    $(".password").show();
                    $(".account").show();
                    $(".formsubmit").show();
                });
            });
        </script>

        <div
            id="userdata_el"
            style="visibility: hidden; position: absolute;"
        ></div>
    </body>
</html>
