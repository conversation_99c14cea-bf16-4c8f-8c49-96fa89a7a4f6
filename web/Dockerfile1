FROM test-registry-kuberun.jdcloud.com/coding/node12-jonefe as build
# FROM test-registry-kuberun.jdcloud.com/jci/node:8.9.4-npm-5.6.0 as build
USER root
WORKDIR /app/

COPY package.json .
COPY package-lock.json .
COPY .npmrc .
COPY .proxy.prod.js .
COPY .proxy.dev.js .
# RUN npm i webpack -g 
# RUN npm i @jonefe/cli@beta -g --registry=http://artifactory.jd.com/api/npm/npm/
RUN npm i @jmodule/cli@beta -g --@jmodule:registry=http://artifactory.jd.com/api/npm/npm/
# RUN npm i webpack -g
# RUN --mount=type=cache,target=/app/node_modules,id=my_app_npm_module,sharing=locked \
RUN npm i
COPY .env .
COPY vue.config.js .
# RUN npm i vue-styleguidist -g 
COPY src src 
# RUN npm run docs:build
COPY public public 
COPY babel.config.js .
COPY .eslintignore .
COPY .eslintrc.js .
COPY .jmodule.js .
COPY index.html .

# RUN --mount=type=cache,target=/app/node_modules,id=my_app_npm_module,sharing=locked \ 
RUN npm run vue:build:development

# FROM test-registry-kuberun.jdcloud.com/turbo/openresty:1.15.8.2-centos6.7
COPY jacp-web.nginx.conf /usr/local/openresty/nginx/conf/domains/jacp-web.nginx.conf
COPY --from=build /app/dist /export/App/jacp-web/dist
RUN chmod -R 755 /usr/share/nginx/html/dist
