// http://eslint.org/docs/user-guide/configuring
const path = require('path');
const defaultWebpackResolve = {
  extensions: ['.js', '.vue', '.json'],
  alias: {
    'vue$': 'vue/dist/vue.esm.js',
    '@': path.resolve('./src'),
  },
};


module.exports = {
  root: true,
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "parser": "babel-eslint",
    "sourceType": "module"
  },
  globals: {
    JModule: true,
  },
  env: {
    browser: true,
  },
  extends: ['plugin:std/vue', 'plugin:vue/strongly-recommended', 'airbnb-base'],
  // required to lint *.vue files
  plugins: [
    'html',
    'std',
    'vue',
    'import'
  ],
  // check if imports actually resolve
  'settings': {
    "import/extensions": [
      ".js",
      ".jsx",
      ".vue"
    ],
    'import/resolver': {
      'webpack': {
        'config': {
          resolve: defaultWebpackResolve
        }
      }
    }
  },
  // add your custom rules here
  'rules': {
    'import/prefer-default-export': ['off'],
    // don't require .vue extension when importing
    "import/extensions": [0, 'ignorePackages'],
    // allow optionalDependencies
    'import/no-extraneous-dependencies': ['error', {
      'optionalDependencies': ['test/unit/index.js']
    }],
    'import/no-unresolved': [2, {
      ignore: ['^\\$(platform|node_modules|module)', 'JModule']
    }],
    // allow debugger during development
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
    'indent': ['error', 4],
    'vue/html-indent': ['error', 4],
    'no-param-reassign': [0],
    'no-prototype-builtins': 0,
    'no-underscore-dangle': ["error", { "allowAfterThis": true }],
    'no-nested-ternary': [1],
    'linebreak-style': 0,
    'std/no-risky-ajaxurl': 2,
    'std/no-parent-modules': 1,
    'std/no-internal-modules': [2, {
      "allow": [
        "**/components/**",
        "**/_components/**",
        "**/plugins/**",
        "**/utils/**",
        "**/models/**",
        "**/pages/demand/**",
        "**/pages/index",
        "**/modules/**",
        "**/mixins/**",
        "**/theme/**",
        path.resolve('./src') + '/*',
      ],
      webpackResolve: defaultWebpackResolve,
      "rootDir": path.resolve('./'),
      checkModule: false
    }],
    'std/no-http-in-component': [2],
    "prefer-destructuring": ["error", {
      "VariableDeclarator": {
        "array": false,
        "object": true
      },
      "AssignmentExpression": {
        "array": false,
        "object": true
      }
    }],
    'max-len': ["error", {
      "code": 128,
      "ignoreComments": true,
      "ignoreTemplateLiterals": true,
      "ignoreStrings": true
    }],
    'import/no-cycle': [0],
    'vue/require-default-prop': [0],
    "vue/singleline-html-element-content-newline": [0],
    "import/order": "off",
  }
}