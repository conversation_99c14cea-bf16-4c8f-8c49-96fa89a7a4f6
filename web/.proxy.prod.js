module.exports = {
    '/jacp/api': {
        target: 'http://jagile-pr.jd.com',
        changeOrigin: true,
    },
    '/jacp/rest': {
        target: 'http://jagile-pr.jd.com',
        changeOrigin: true,
    },
    '/jacp-module/rest': {
        target: 'http://jagile.jd.com',
        changeOrigin: true,
    },
    '/jacp-security': {
        target: 'http://jaas-pr.jd.com',
        changeOrigin: true,
    },
    '/jacp.jd.local/': {
        target: 'http://storage.jd.local',
        changeOrigin: true,
        /* pathRewrite: {
            '^/local.jd.com': '/jacp.jd.local',// ????
        }, */
    },
    '/uaos/uaos/api/v1/': {
        target:'http://dev.devops.prod.devopsdev.local',
        changeOrigin: true,
    },
    '/jacp-jdr/': {
        target: 'http://pre-gateway.jd.com',
        // target: 'http://jagile-jdr.jd.com:1601',
        changeOrigin: true,
        /*pathRewrite: {
            '/jacp-jdr': '',// ????
        },*/
    },
    '/jacp-tools':{
        target: 'http://api.dev.devops.prod.devopsdev.local',
        changeOrigin: true,
    },
    '/devops-compo/': {
        target: 'http://jagile-pr.jd.com',
        changeOrigin: true,
    },
    '/jacp-dc/': {
        target: 'http://jagile-dc.jd.com',
        changeOrigin: true,
    },
    '/module_data/': {
        target: 'http://jacp-mdc.jd.local',
        changeOrigin: true,
    },
};