@font-face {
  font-family: "jacp-iconfont"; /* Project id 873141 */
  src: url('iconfont.woff2?t=1698117980503') format('woff2'),
       url('iconfont.woff?t=1698117980503') format('woff'),
       url('iconfont.ttf?t=1698117980503') format('truetype');
}

.jacp-iconfont {
  font-family: "jacp-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.jacp-icon-a-icon-massoperation1:before {
  content: "\e663";
}

.jacp-icon-avatar:before {
  content: "\e70b";
}

.jacp-icon-add-someone:before {
  content: "\e70a";
}

.jacp-icon-shangyige:before {
  content: "\e642";
}

.jacp-icon-xiayige:before {
  content: "\e643";
}

.jacp-icon-you:before {
  content: "\e65b";
}

.jacp-icon-ic_app_crm:before {
  content: "\e709";
}

.jacp-icon-a-ic_tasktag:before {
  content: "\e708";
}

.jacp-icon-ic_process_error:before {
  content: "\e701";
}

.jacp-icon-ic_process_todo:before {
  content: "\e702";
}

.jacp-icon-ic_process_done:before {
  content: "\e703";
}

.jacp-icon-a-ic_dwelltime:before {
  content: "\e704";
}

.jacp-icon-ic_process_warning:before {
  content: "\e705";
}

.jacp-icon-ic_process_doing:before {
  content: "\e706";
}

.jacp-icon-ic_process_reject:before {
  content: "\e707";
}

.jacp-icon-enter:before {
  content: "\e7bd";
}

.jacp-icon-tag:before {
  content: "\e7be";
}

.jacp-icon-x:before {
  content: "\e67f";
}

.jacp-icon-charuricheng:before {
  content: "\e7bb";
}

.jacp-icon-gongzuoxiangzongshu-gailan:before {
  content: "\e7b7";
}

.jacp-icon-jihuagongshi-gailan:before {
  content: "\e7b8";
}

.jacp-icon-renwuzongshu-gailan:before {
  content: "\e7b9";
}

.jacp-icon-shengyugongshi-gailan:before {
  content: "\e7ba";
}

.jacp-icon-cancel:before {
  content: "\e7b6";
}

.jacp-icon-ic_dropdown_selected:before {
  content: "\e700";
}

.jacp-icon-icon_calendar:before {
  content: "\e641";
}

.jacp-icon-demand2:before {
  content: "\e791";
}

.jacp-icon-ic_associate:before {
  content: "\e6ff";
}

.jacp-icon-dongjieshoulie:before {
  content: "\e7b4";
}

.jacp-icon-quxiaodongjieshoulie:before {
  content: "\e7b5";
}

.jacp-icon-a-ic_groupview:before {
  content: "\e6fe";
}

.jacp-icon-icon-tubiao:before {
  content: "\e640";
}

.jacp-icon-a-ic_relaydraft:before {
  content: "\e6fd";
}

.jacp-icon-ic_tree1:before {
  content: "\e6fc";
}

.jacp-icon-a-indicatorline_3:before {
  content: "\e6f9";
}

.jacp-icon-a-indicatorline_2:before {
  content: "\e6fa";
}

.jacp-icon-a-indicatorline_1:before {
  content: "\e6fb";
}

.jacp-icon-demand1:before {
  content: "\e7b3";
}

.jacp-icon-project:before {
  content: "\e7b0";
}

.jacp-icon-bug1:before {
  content: "\e7b1";
}

.jacp-icon-task:before {
  content: "\e7b2";
}

.jacp-icon-tiaozhuan:before {
  content: "\e7ae";
}

.jacp-icon-fuzhi:before {
  content: "\e7af";
}

.jacp-icon-ic-pack:before {
  content: "\e63f";
}

.jacp-icon-yanfalei:before {
  content: "\e773";
}

.jacp-icon-richangguanlilei:before {
  content: "\e774";
}

.jacp-icon-yunyinglei:before {
  content: "\e775";
}

.jacp-icon-yuyanlei:before {
  content: "\e776";
}

.jacp-icon-fuwulei:before {
  content: "\e777";
}

.jacp-icon-department:before {
  content: "\e7ac";
}

.jacp-icon-a-lujing4:before {
  content: "\e7a7";
}

.jacp-icon-jiebang:before {
  content: "\e7a0";
}

.jacp-icon-zhankai:before {
  content: "\e7a6";
}

.jacp-icon-share-thin:before {
  content: "\e7a3";
}

.jacp-icon-ic_sys_info:before {
  content: "\e6f7";
}

.jacp-icon-box:before {
  content: "\e79e";
}

.jacp-icon-switch:before {
  content: "\e79f";
}

.jacp-icon-a-baohedudi:before {
  content: "\e63c";
}

.jacp-icon-baoheduzhengchang:before {
  content: "\e63d";
}

.jacp-icon-a-baohedugao:before {
  content: "\e63e";
}

.jacp-icon-cancel-select:before {
  content: "\e79b";
}

.jacp-icon-bookmark:before {
  content: "\e79c";
}

.jacp-icon-folder-move:before {
  content: "\e79d";
}

.jacp-icon-arrow-right:before {
  content: "\e797";
}

.jacp-icon-shrink-all:before {
  content: "\e798";
}

.jacp-icon-expand-all:before {
  content: "\e799";
}

.jacp-icon-arrow-down:before {
  content: "\e79a";
}

.jacp-icon-pingshenicon:before {
  content: "\e796";
}

.jacp-icon-a-ic_workorder2:before {
  content: "\e6f6";
}

.jacp-icon-ic_del1:before {
  content: "\e6f3";
}

.jacp-icon-a-ico-upload1:before {
  content: "\e6f4";
}

.jacp-icon-a-ico-download:before {
  content: "\e6f5";
}

.jacp-icon-ic-link:before {
  content: "\e6f1";
}

.jacp-icon-a-linkicon:before {
  content: "\e6f0";
}

.jacp-icon-a-ico-upload:before {
  content: "\e63b";
}

.jacp-icon-desc:before {
  content: "\e639";
}

.jacp-icon-asc:before {
  content: "\e63a";
}

.jacp-icon-share:before {
  content: "\e6ef";
}

.jacp-icon-unselected:before {
  content: "\e638";
}

.jacp-icon-thumbsdown-unselected:before {
  content: "\e6ed";
}

.jacp-icon-thumbsdown-selected:before {
  content: "\e6ee";
}

.jacp-icon-thumbsup-unselected:before {
  content: "\e636";
}

.jacp-icon-thumbsup-selected:before {
  content: "\e637";
}

.jacp-icon-ico-right:before {
  content: "\e634";
}

.jacp-icon-ico-wrong:before {
  content: "\e635";
}

.jacp-icon-fold:before {
  content: "\e6ec";
}

.jacp-icon-liked:before {
  content: "\e6eb";
}

.jacp-icon-a-ic_call:before {
  content: "\e623";
}

.jacp-icon-like1:before {
  content: "\e6e6";
}

.jacp-icon-a-moreactions:before {
  content: "\e6e7";
}

.jacp-icon-a-appevaluate:before {
  content: "\e6e8";
}

.jacp-icon-a-addgroup:before {
  content: "\e6e9";
}

.jacp-icon-p0:before {
  content: "\e6e2";
}

.jacp-icon-p2:before {
  content: "\e6e3";
}

.jacp-icon-p1:before {
  content: "\e6e4";
}

.jacp-icon-p3:before {
  content: "\e6e5";
}

.jacp-icon-risk-high:before {
  content: "\e6dc";
}

.jacp-icon-risk-middle:before {
  content: "\e6db";
}

.jacp-icon-risk-low:before {
  content: "\e6da";
}

.jacp-icon-xingzhuangjiehe2:before {
  content: "\e622";
}

.jacp-icon-notification-at:before {
  content: "\e621";
}

.jacp-icon-tixing:before {
  content: "\e620";
}

.jacp-icon-ic_copy:before {
  content: "\e6d6";
}

.jacp-icon-ic_navi_console:before {
  content: "\e6d5";
}

.jacp-icon-ic_navi_setting:before {
  content: "\e6d4";
}

.jacp-icon-ic_navi_guide:before {
  content: "\e6d2";
}

.jacp-icon-ic_navi_help:before {
  content: "\e6d3";
}

.jacp-icon-a-ic_navi_inputworkload:before {
  content: "\e6d0";
}

.jacp-icon-ic_navi_message:before {
  content: "\e6d1";
}

.jacp-icon-ic_explore:before {
  content: "\e6cc";
}

.jacp-icon-ic_Refresh:before {
  content: "\e6cb";
}

.jacp-icon-ic_download:before {
  content: "\e6ca";
}

.jacp-icon-yingyongfuzeren:before {
  content: "\e6c9";
}

.jacp-icon-tag-hangyun:before {
  content: "\e6c6";
}

.jacp-icon-tag-keji:before {
  content: "\e6c5";
}

.jacp-icon-tag-lingshou:before {
  content: "\e6c4";
}

.jacp-icon-mark-all-as-read:before {
  content: "\e617";
}

.jacp-icon-notification-transfer:before {
  content: "\e615";
}

.jacp-icon-notification-remind:before {
  content: "\e616";
}

.jacp-icon-notification-change:before {
  content: "\e613";
}

.jacp-icon-notification-add:before {
  content: "\e614";
}

.jacp-icon-notification-remove:before {
  content: "\e612";
}

.jacp-icon-xingzhuangjiehe1:before {
  content: "\e610";
}

.jacp-icon-xingzhuangjiehe:before {
  content: "\e60f";
}

.jacp-icon-gongzuoxiangmiaoshuxinxi:before {
  content: "\e606";
}

.jacp-icon-warning:before {
  content: "\e6c3";
}

.jacp-icon-xitongshejipingshenicon:before {
  content: "\e6c1";
}

.jacp-icon-prdpingshenicon:before {
  content: "\e6c2";
}

.jacp-icon-done2:before {
  content: "\e60e";
}

.jacp-icon-undone2:before {
  content: "\e60c";
}

.jacp-icon-jiantouqianduandesanjiaoxingicon:before {
  content: "\e602";
}

.jacp-icon-bianjiicon:before {
  content: "\e601";
}

.jacp-icon-ic_del:before {
  content: "\e6bb";
}

.jacp-icon-tixingdingyue:before {
  content: "\e6b9";
}

.jacp-icon-star-off:before {
  content: "\e607";
}

.jacp-icon-star-on:before {
  content: "\e605";
}

.jacp-icon-feedback:before {
  content: "\e6b7";
}

.jacp-icon-ic-del:before {
  content: "\e6b5";
}

.jacp-icon-a-ic-toolbar-fliter:before {
  content: "\e6b4";
}

.jacp-icon-a-ic-toolbar-kanban:before {
  content: "\e6b3";
}

.jacp-icon-a-ic-toolbar-list:before {
  content: "\e6b2";
}

.jacp-icon-a-ic_tablesetting:before {
  content: "\e6b1";
}

.jacp-icon-tag-new:before {
  content: "\e6ac";
}

.jacp-icon-branch1:before {
  content: "\e6aa";
}

.jacp-icon-commit:before {
  content: "\e6a9";
}

.jacp-icon-merge-request:before {
  content: "\e6a8";
}

.jacp-icon-internal:before {
  content: "\e6a7";
}

.jacp-icon-public:before {
  content: "\e6a6";
}

.jacp-icon-private:before {
  content: "\e6a5";
}

.jacp-icon-kaifazhehoutai:before {
  content: "\e698";
}

.jacp-icon-a-icon-appopen:before {
  content: "\e696";
}

.jacp-icon-a-icon-appmaket:before {
  content: "\e695";
}

.jacp-icon-a-dongdongicon:before {
  content: "\e694";
}

.jacp-icon-a-appmarket-smallarrow:before {
  content: "\e693";
}

.jacp-icon-jingdu-logo:before {
  content: "\e690";
}

.jacp-icon-copy_surface:before {
  content: "\e68f";
}

.jacp-icon-navi_system:before {
  content: "\e604";
}

.jacp-icon-navi_process:before {
  content: "\e603";
}

.jacp-icon-copy:before {
  content: "\e633";
}

.jacp-icon-delete:before {
  content: "\e632";
}

.jacp-icon-question:before {
  content: "\e631";
}

.jacp-icon-overview:before {
  content: "\e630";
}

.jacp-icon-code:before {
  content: "\e62d";
}

.jacp-icon-logservice:before {
  content: "\e62e";
}

.jacp-icon-release:before {
  content: "\e62f";
}

.jacp-icon-monitoring:before {
  content: "\e624";
}

.jacp-icon-LB:before {
  content: "\e625";
}

.jacp-icon-applicationmanagement:before {
  content: "\e626";
}

.jacp-icon-build:before {
  content: "\e62b";
}

.jacp-icon-resource:before {
  content: "\e62c";
}

.jacp-icon-navi_ic_product:before {
  content: "\e68d";
}

.jacp-icon-navi_ic_Workbench:before {
  content: "\e68b";
}

.jacp-icon-navi_ic_rizhi:before {
  content: "\e68a";
}

.jacp-icon-test:before {
  content: "\e686";
}

.jacp-icon-bug:before {
  content: "\e685";
}

.jacp-icon-demand:before {
  content: "\e684";
}

.jacp-icon-task1:before {
  content: "\e683";
}

.jacp-icon-error:before {
  content: "\e682";
}

.jacp-icon-ic_quote:before {
  content: "\e681";
}

.jacp-icon-icon_file_open:before {
  content: "\e680";
}

.jacp-icon-icon_file_default:before {
  content: "\e67e";
}

.jacp-icon-navi_ic_Operation1:before {
  content: "\e67c";
}

.jacp-icon-navi_ic_deploy:before {
  content: "\e67b";
}

.jacp-icon-unlike:before {
  content: "\e67a";
}

.jacp-icon-like:before {
  content: "\e679";
}

.jacp-icon-done:before {
  content: "\e678";
}

.jacp-icon-doing:before {
  content: "\e677";
}

.jacp-icon-todo:before {
  content: "\e676";
}

.jacp-icon-down:before {
  content: "\e675";
}

.jacp-icon-navi_ic_Reverse:before {
  content: "\e674";
}

.jacp-icon-navi_ic_positive:before {
  content: "\e673";
}

.jacp-icon-icon_fullscreen:before {
  content: "\e672";
}

.jacp-icon-navi_tab_logo_expansion:before {
  content: "\e66e";
}

.jacp-icon-navi_tab_logo_eceipts:before {
  content: "\e66d";
}

.jacp-icon-navi_ic_coding:before {
  content: "\e671";
}

.jacp-icon-navi_ic_pipeline:before {
  content: "\e670";
}

.jacp-icon-navi_ic_appssetting:before {
  content: "\e66f";
}

.jacp-icon-navi_ic_expansion2:before {
  content: "\e66c";
}

.jacp-icon-navi_ic_eceipts2:before {
  content: "\e66b";
}

.jacp-icon-navi_ic_Operation:before {
  content: "\e66a";
}

.jacp-icon-navi_ic_data:before {
  content: "\e669";
}

.jacp-icon-navi_ic_ruler:before {
  content: "\e668";
}

.jacp-icon-navi_ic_project:before {
  content: "\e667";
}

.jacp-icon-navi_ic_test:before {
  content: "\e666";
}

.jacp-icon-navi_ic_teamspace:before {
  content: "\e665";
}

.jacp-icon-navi_ic_demand:before {
  content: "\e664";
}

.jacp-icon-navi_ic_guide:before {
  content: "\e662";
}

.jacp-icon-help:before {
  content: "\e661";
}

.jacp-icon-more:before {
  content: "\e660";
}

.jacp-icon-branch:before {
  content: "\e65f";
}

.jacp-icon-collapsed:before {
  content: "\e64c";
}

.jacp-icon-attachment:before {
  content: "\e64d";
}

.jacp-icon-link:before {
  content: "\e64e";
}

.jacp-icon-clock:before {
  content: "\e64f";
}

.jacp-icon-chat:before {
  content: "\e650";
}

.jacp-icon-time:before {
  content: "\e651";
}

.jacp-icon-pie:before {
  content: "\e652";
}

.jacp-icon-card-view:before {
  content: "\e653";
}

.jacp-icon-add:before {
  content: "\e654";
}

.jacp-icon-help2:before {
  content: "\e655";
}

.jacp-icon-filter:before {
  content: "\e656";
}

.jacp-icon-expand:before {
  content: "\e657";
}

.jacp-icon-burnoutmap:before {
  content: "\e658";
}

.jacp-icon-list-view:before {
  content: "\e659";
}

.jacp-icon-bell:before {
  content: "\e65a";
}

.jacp-icon-search:before {
  content: "\e65c";
}

.jacp-icon-refresh:before {
  content: "\e65e";
}

.jacp-icon-setting:before {
  content: "\e64a";
}

.jacp-icon-fullscreen-exit:before {
  content: "\e649";
}

.jacp-icon-fullscreen:before {
  content: "\e648";
}

.jacp-icon-edit-light:before {
  content: "\e647";
}

.jacp-icon-yidong:before {
  content: "\e645";
}

.jacp-icon-download:before {
  content: "\e62a";
}

.jacp-icon-del:before {
  content: "\e629";
}

.jacp-icon-edit:before {
  content: "\e628";
}

.jacp-icon-xie:before {
  content: "\e627";
}

