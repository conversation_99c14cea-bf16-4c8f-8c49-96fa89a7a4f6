<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.jacp.arch.mapper.ArchRuleComposeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.jacp.arch.entity.ArchRuleCompose">
        <id column="id" property="id" />
        <result column="rule_code" property="ruleCode" />
        <result column="rule_name" property="ruleName" />
        <result column="description" property="description" />
        <result column="trigger_type" property="triggerType" />
        <result column="trigger_type_action" property="triggerTypeAction" />
        <result column="process_element_code" property="processElementCode" />
        <result column="source_type" property="sourceType" />
        <result column="source_code" property="sourceCode" />
        <result column="target_type" property="targetType" />
        <result column="target_code" property="targetCode" />
        <result column="property_change_code" property="propertyChangeCode" />
        <result column="trigger_timing" property="triggerTiming" />
        <result column="unfinished_repeated" property="unfinishedRepeated" />
        <result column="plus_n" property="plusN" />
        <result column="cron_expression" property="cronExpression" />
        <result column="actions" property="actions" />
        <result column="priority" property="priority" />
        <result column="is_reversible" property="isReversible" />
        <result column="oa" property="oa" />
        <result column="oa_data" property="oaData" />
        <result column="update_status_or_stage" property="updateStatusOrStage" />
        <result column="update_status_or_stage_data" property="updateStatusOrStageData" />
        <result column="property_change" property="propertyChange" />
        <result column="property_change_data" property="propertyChangeData" />
        <result column="msg" property="msg" />
        <result column="msg_data" property="msgData" />
        <result column="interceptor" property="interceptor" />
        <result column="interceptor_data" property="interceptorData" />
        <result column="space_id" property="spaceId" />
        <result column="state" property="state" />
        <result column="extra_data" property="extraData" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_erp" property="createErp" />
        <result column="update_erp" property="updateErp" />
        <result column="is_del" property="isDel" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_code, rule_name, description, trigger_type, trigger_type_action, 
        process_element_code, source_type, source_code, target_type, target_code, 
        property_change_code, trigger_timing, unfinished_repeated, plus_n, 
        cron_expression, actions, priority, is_reversible, oa, oa_data, 
        update_status_or_stage, update_status_or_stage_data, property_change, 
        property_change_data, msg, msg_data, interceptor, interceptor_data, 
        space_id, state, extra_data, create_time, update_time, create_erp, 
        update_erp, is_del, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where">
        <where>
            is_del = 0
            <if test="query.ruleCode != null and query.ruleCode != ''">
                AND rule_code LIKE CONCAT('%', #{query.ruleCode}, '%')
            </if>
            <if test="query.ruleName != null and query.ruleName != ''">
                AND rule_name LIKE CONCAT('%', #{query.ruleName}, '%')
            </if>
            <if test="query.triggerType != null">
                AND trigger_type = #{query.triggerType}
            </if>
            <if test="query.triggerTypeAction != null">
                AND trigger_type_action = #{query.triggerTypeAction}
            </if>
            <if test="query.processElementCode != null and query.processElementCode != ''">
                AND process_element_code = #{query.processElementCode}
            </if>
            <if test="query.state != null">
                AND state = #{query.state}
            </if>
            <if test="query.spaceId != null">
                AND space_id = #{query.spaceId}
            </if>
            <if test="query.createErp != null and query.createErp != ''">
                AND create_erp = #{query.createErp}
            </if>
            <if test="query.createTimeStart != null and query.createTimeStart != ''">
                AND create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND create_time &lt;= #{query.createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 分页查询自动化规则列表 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_rule_compose_new
        <include refid="Query_Where" />
        ORDER BY create_time DESC
    </select>

    <!-- 查询自动化规则列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_rule_compose_new
        <include refid="Query_Where" />
        ORDER BY create_time DESC
    </select>

    <!-- 根据规则编码查询 -->
    <select id="selectByRuleCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_rule_compose_new
        WHERE rule_code = #{ruleCode} AND is_del = 0
    </select>

    <!-- 批量删除（逻辑删除） -->
    <update id="batchDelete">
        UPDATE biz_rule_compose_new 
        SET is_del = 1, update_erp = #{updateErp}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_del = 0
    </update>

</mapper>
