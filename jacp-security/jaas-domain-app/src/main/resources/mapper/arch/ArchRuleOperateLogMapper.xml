<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.jacp.arch.mapper.ArchRuleOperateLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jd.jacp.arch.entity.ArchRuleOperateLog">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="create_erp" property="createErp" />
        <result column="create_time" property="createTime" />
        <result column="action_id" property="actionId" />
        <result column="before" property="before" />
        <result column="after" property="after" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_id, create_erp, create_time, action_id, before, after, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where">
        <where>
            1=1
            <if test="query.ruleId != null">
                AND rule_id = #{query.ruleId}
            </if>
            <if test="query.actionId != null">
                AND action_id = #{query.actionId}
            </if>
            <if test="query.createErp != null and query.createErp != ''">
                AND create_erp = #{query.createErp}
            </if>
            <if test="query.createTimeStart != null and query.createTimeStart != ''">
                AND create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
                AND create_time &lt;= #{query.createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 分页查询操作记录列表 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_rule_operate_log_new
        <include refid="Query_Where" />
        ORDER BY create_time DESC
    </select>

    <!-- 查询操作记录列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_rule_operate_log_new
        <include refid="Query_Where" />
        ORDER BY create_time DESC
    </select>

    <!-- 根据规则ID查询操作记录 -->
    <select id="selectByRuleId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_rule_operate_log_new
        WHERE rule_id = #{ruleId}
        ORDER BY create_time DESC
    </select>

    <!-- 批量删除 -->
    <delete id="batchDelete">
        DELETE FROM biz_rule_operate_log_new 
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
