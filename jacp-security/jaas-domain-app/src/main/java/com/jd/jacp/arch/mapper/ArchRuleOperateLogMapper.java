package com.jd.jacp.arch.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jacp.arch.entity.ArchRuleOperateLog;
import com.jd.jacp.arch.query.ArchRuleOperateLogQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作记录表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ArchRuleOperateLogMapper extends BaseMapper<ArchRuleOperateLog> {

    /**
     * 分页查询操作记录列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ArchRuleOperateLog> selectPageList(Page<ArchRuleOperateLog> page, @Param("query") ArchRuleOperateLogQuery query);

    /**
     * 查询操作记录列表
     *
     * @param query 查询条件
     * @return 结果列表
     */
    List<ArchRuleOperateLog> selectList(@Param("query") ArchRuleOperateLogQuery query);

    /**
     * 根据规则ID查询操作记录
     *
     * @param ruleId 规则ID
     * @return 操作记录列表
     */
    List<ArchRuleOperateLog> selectByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 批量删除
     *
     * @param ids 主键ID列表
     * @return 影响行数
     */
    int batchDelete(@Param("ids") List<Long> ids);
}
