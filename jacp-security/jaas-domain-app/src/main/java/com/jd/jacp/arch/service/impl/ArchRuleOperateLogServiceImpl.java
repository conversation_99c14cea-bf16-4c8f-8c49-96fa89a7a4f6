package com.jd.jacp.arch.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.jacp.arch.dto.ArchRuleOperateLogDTO;
import com.jd.jacp.arch.entity.ArchRuleOperateLog;
import com.jd.jacp.arch.mapper.ArchRuleOperateLogMapper;
import com.jd.jacp.arch.query.ArchRuleOperateLogQuery;
import com.jd.jacp.arch.service.IArchRuleOperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 操作记录表 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchRuleOperateLogServiceImpl extends ServiceImpl<ArchRuleOperateLogMapper, ArchRuleOperateLog> implements IArchRuleOperateLogService {

    @Override
    public IPage<ArchRuleOperateLog> selectPageList(ArchRuleOperateLogQuery query) {
        Page<ArchRuleOperateLog> page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseMapper.selectPageList(page, query);
    }

    @Override
    public List<ArchRuleOperateLog> selectList(ArchRuleOperateLogQuery query) {
        return baseMapper.selectList(query);
    }

    @Override
    public ArchRuleOperateLog selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<ArchRuleOperateLog> selectByRuleId(Long ruleId) {
        return baseMapper.selectByRuleId(ruleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ArchRuleOperateLogDTO dto, String operatorErp) {
        ArchRuleOperateLog entity = new ArchRuleOperateLog();
        BeanUtils.copyProperties(dto, entity);
        entity.setCreateTime(new Date());
        entity.setCreateErp(operatorErp);

        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ArchRuleOperateLogDTO dto, String operatorErp) {
        ArchRuleOperateLog existEntity = selectById(dto.getId());
        if (existEntity == null) {
            throw new RuntimeException("操作记录不存在");
        }

        ArchRuleOperateLog entity = new ArchRuleOperateLog();
        BeanUtils.copyProperties(dto, entity);

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        ArchRuleOperateLog entity = selectById(id);
        if (entity == null) {
            throw new RuntimeException("操作记录不存在");
        }

        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> ids) {
        return baseMapper.batchDelete(ids) > 0;
    }

    @Override
    public void exportData(ArchRuleOperateLogQuery query, HttpServletResponse response) {
        try {
            List<ArchRuleOperateLog> dataList = selectList(query);
            
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("操作记录数据");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "规则ID", "操作类型", "操作前快照", "操作后快照", "创建者", "创建时间"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                ArchRuleOperateLog data = dataList.get(i);
                Row row = sheet.createRow(i + 1);
                
                row.createCell(0).setCellValue(data.getRuleId());
                row.createCell(1).setCellValue(data.getActionId());
                row.createCell(2).setCellValue(data.getBefore());
                row.createCell(3).setCellValue(data.getAfter());
                row.createCell(4).setCellValue(data.getCreateErp());
                row.createCell(5).setCellValue(data.getCreateTime() != null ? data.getCreateTime().toString() : "");
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("操作记录数据.xlsx", "UTF-8"));
            
            // 输出文件
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            log.error("导出操作记录数据失败", e);
            throw new RuntimeException("导出数据失败");
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("操作记录导入模板");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "规则ID", "操作类型", "操作前快照", "操作后快照"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("操作记录导入模板.xlsx", "UTF-8"));
            
            // 输出文件
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            log.error("下载操作记录导入模板失败", e);
            throw new RuntimeException("下载模板失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(MultipartFile file, String operatorErp) {
        if (file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }

        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            
            List<ArchRuleOperateLog> dataList = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMsg = new StringBuilder();
            
            // 从第二行开始读取数据（第一行是表头）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                try {
                    ArchRuleOperateLog entity = new ArchRuleOperateLog();
                    
                    // 读取必填字段
                    Long ruleId = getLongCellValue(row.getCell(0));
                    Integer actionId = getIntegerCellValue(row.getCell(1));
                    
                    if (ruleId == null || actionId == null) {
                        errorMsg.append("第").append(i + 1).append("行：必填字段不能为空；");
                        failCount++;
                        continue;
                    }
                    
                    entity.setRuleId(ruleId);
                    entity.setActionId(actionId);
                    entity.setBefore(getCellValue(row.getCell(2)));
                    entity.setAfter(getCellValue(row.getCell(3)));
                    
                    // 设置默认值
                    entity.setCreateTime(new Date());
                    entity.setCreateErp(operatorErp);
                    
                    dataList.add(entity);
                    successCount++;
                    
                } catch (Exception e) {
                    errorMsg.append("第").append(i + 1).append("行：").append(e.getMessage()).append("；");
                    failCount++;
                }
            }
            
            // 批量保存
            if (!dataList.isEmpty()) {
                saveBatch(dataList);
            }
            
            workbook.close();
            
            String result = "导入完成！成功：" + successCount + "条，失败：" + failCount + "条";
            if (errorMsg.length() > 0) {
                result += "。错误信息：" + errorMsg.toString();
            }
            
            return result;
            
        } catch (IOException e) {
            log.error("导入操作记录数据失败", e);
            throw new RuntimeException("导入数据失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordOperateLog(Long ruleId, Integer actionId, String before, String after, String operatorErp) {
        ArchRuleOperateLog log = new ArchRuleOperateLog();
        log.setRuleId(ruleId);
        log.setActionId(actionId);
        log.setBefore(before);
        log.setAfter(after);
        log.setCreateTime(new Date());
        log.setCreateErp(operatorErp);
        
        save(log);
    }
    
    /**
     * 获取单元格字符串值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }
    
    /**
     * 获取单元格整数值
     */
    private Integer getIntegerCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return (int) cell.getNumericCellValue();
            case STRING:
                String value = cell.getStringCellValue();
                if (StringUtils.hasText(value)) {
                    try {
                        return Integer.parseInt(value);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
                return null;
            default:
                return null;
        }
    }
    
    /**
     * 获取单元格长整数值
     */
    private Long getLongCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return (long) cell.getNumericCellValue();
            case STRING:
                String value = cell.getStringCellValue();
                if (StringUtils.hasText(value)) {
                    try {
                        return Long.parseLong(value);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
                return null;
            default:
                return null;
        }
    }
}
