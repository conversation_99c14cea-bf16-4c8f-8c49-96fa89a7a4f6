package com.jd.jacp.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.jacp.common.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作记录表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("biz_rule_operate_log_new")
public class ArchRuleOperateLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 自动化规则ID
     */
    @TableField("rule_id")
    private Long ruleId;

    /**
     * 创建者 ERP 账号
     */
    @TableField("create_erp")
    private String createErp;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 操作类型（如新增、修改、删除等）
     */
    @TableField("action_id")
    private Integer actionId;

    /**
     * 操作前快照（JSON 字符串）
     */
    @TableField("before")
    private String before;

    /**
     * 操作后快照（JSON 字符串）
     */
    @TableField("after")
    private String after;

    /**
     * 租户 ID
     */
    @TableField("tenant_id")
    private Integer tenantId;
}
