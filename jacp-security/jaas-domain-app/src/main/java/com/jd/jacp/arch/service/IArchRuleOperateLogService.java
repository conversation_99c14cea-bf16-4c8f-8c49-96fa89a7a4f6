package com.jd.jacp.arch.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jd.jacp.arch.dto.ArchRuleOperateLogDTO;
import com.jd.jacp.arch.entity.ArchRuleOperateLog;
import com.jd.jacp.arch.query.ArchRuleOperateLogQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作记录表 服务类
 *
 * <AUTHOR>
 */
public interface IArchRuleOperateLogService extends IService<ArchRuleOperateLog> {

    /**
     * 分页查询操作记录列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ArchRuleOperateLog> selectPageList(ArchRuleOperateLogQuery query);

    /**
     * 查询操作记录列表
     *
     * @param query 查询条件
     * @return 结果列表
     */
    List<ArchRuleOperateLog> selectList(ArchRuleOperateLogQuery query);

    /**
     * 根据ID查询操作记录详情
     *
     * @param id 主键ID
     * @return 操作记录详情
     */
    ArchRuleOperateLog selectById(Long id);

    /**
     * 根据规则ID查询操作记录
     *
     * @param ruleId 规则ID
     * @return 操作记录列表
     */
    List<ArchRuleOperateLog> selectByRuleId(Long ruleId);

    /**
     * 新增操作记录
     *
     * @param dto 操作记录DTO
     * @param operatorErp 操作者ERP
     * @return 是否成功
     */
    boolean insert(ArchRuleOperateLogDTO dto, String operatorErp);

    /**
     * 修改操作记录
     *
     * @param dto 操作记录DTO
     * @param operatorErp 操作者ERP
     * @return 是否成功
     */
    boolean update(ArchRuleOperateLogDTO dto, String operatorErp);

    /**
     * 删除操作记录
     *
     * @param id 主键ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 批量删除操作记录
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean batchDelete(List<Long> ids);

    /**
     * 导出操作记录数据
     *
     * @param query 查询条件
     * @param response HTTP响应
     */
    void exportData(ArchRuleOperateLogQuery query, HttpServletResponse response);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入操作记录数据
     *
     * @param file 上传文件
     * @param operatorErp 操作者ERP
     * @return 导入结果信息
     */
    String importData(MultipartFile file, String operatorErp);

    /**
     * 记录操作日志
     *
     * @param ruleId 规则ID
     * @param actionId 操作类型
     * @param before 操作前数据
     * @param after 操作后数据
     * @param operatorErp 操作者ERP
     */
    void recordOperateLog(Long ruleId, Integer actionId, String before, String after, String operatorErp);
}
