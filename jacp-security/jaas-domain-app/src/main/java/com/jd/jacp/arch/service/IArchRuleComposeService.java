package com.jd.jacp.arch.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jd.jacp.arch.dto.ArchRuleComposeDTO;
import com.jd.jacp.arch.entity.ArchRuleCompose;
import com.jd.jacp.arch.query.ArchRuleComposeQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 自动化规则表 服务类
 *
 * <AUTHOR>
 */
public interface IArchRuleComposeService extends IService<ArchRuleCompose> {

    /**
     * 分页查询自动化规则列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ArchRuleCompose> selectPageList(ArchRuleComposeQuery query);

    /**
     * 查询自动化规则列表
     *
     * @param query 查询条件
     * @return 结果列表
     */
    List<ArchRuleCompose> selectList(ArchRuleComposeQuery query);

    /**
     * 根据ID查询自动化规则详情
     *
     * @param id 主键ID
     * @return 自动化规则详情
     */
    ArchRuleCompose selectById(Long id);

    /**
     * 根据规则编码查询
     *
     * @param ruleCode 规则编码
     * @return 自动化规则
     */
    ArchRuleCompose selectByRuleCode(String ruleCode);

    /**
     * 新增自动化规则
     *
     * @param dto 自动化规则DTO
     * @param operatorErp 操作者ERP
     * @return 是否成功
     */
    boolean insert(ArchRuleComposeDTO dto, String operatorErp);

    /**
     * 修改自动化规则
     *
     * @param dto 自动化规则DTO
     * @param operatorErp 操作者ERP
     * @return 是否成功
     */
    boolean update(ArchRuleComposeDTO dto, String operatorErp);

    /**
     * 删除自动化规则
     *
     * @param id 主键ID
     * @param operatorErp 操作者ERP
     * @return 是否成功
     */
    boolean delete(Long id, String operatorErp);

    /**
     * 批量删除自动化规则
     *
     * @param ids 主键ID列表
     * @param operatorErp 操作者ERP
     * @return 是否成功
     */
    boolean batchDelete(List<Long> ids, String operatorErp);

    /**
     * 启用/禁用自动化规则
     *
     * @param id 主键ID
     * @param state 状态：0禁用、1启用
     * @param operatorErp 操作者ERP
     * @return 是否成功
     */
    boolean updateState(Long id, Integer state, String operatorErp);

    /**
     * 导出自动化规则数据
     *
     * @param query 查询条件
     * @param response HTTP响应
     */
    void exportData(ArchRuleComposeQuery query, HttpServletResponse response);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入自动化规则数据
     *
     * @param file 上传文件
     * @param operatorErp 操作者ERP
     * @return 导入结果信息
     */
    String importData(MultipartFile file, String operatorErp);
}
