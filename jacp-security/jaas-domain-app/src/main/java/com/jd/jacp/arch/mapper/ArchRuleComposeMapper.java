package com.jd.jacp.arch.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.jacp.arch.entity.ArchRuleCompose;
import com.jd.jacp.arch.query.ArchRuleComposeQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自动化规则表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ArchRuleComposeMapper extends BaseMapper<ArchRuleCompose> {

    /**
     * 分页查询自动化规则列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ArchRuleCompose> selectPageList(Page<ArchRuleCompose> page, @Param("query") ArchRuleComposeQuery query);

    /**
     * 查询自动化规则列表
     *
     * @param query 查询条件
     * @return 结果列表
     */
    List<ArchRuleCompose> selectList(@Param("query") ArchRuleComposeQuery query);

    /**
     * 根据规则编码查询
     *
     * @param ruleCode 规则编码
     * @return 自动化规则
     */
    ArchRuleCompose selectByRuleCode(@Param("ruleCode") String ruleCode);

    /**
     * 批量删除（逻辑删除）
     *
     * @param ids 主键ID列表
     * @param updateErp 更新者ERP
     * @return 影响行数
     */
    int batchDelete(@Param("ids") List<Long> ids, @Param("updateErp") String updateErp);
}
