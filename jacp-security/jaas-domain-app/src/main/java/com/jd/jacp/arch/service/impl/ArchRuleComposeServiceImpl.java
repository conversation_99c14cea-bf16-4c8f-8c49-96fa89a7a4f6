package com.jd.jacp.arch.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jd.jacp.arch.dto.ArchRuleComposeDTO;
import com.jd.jacp.arch.entity.ArchRuleCompose;
import com.jd.jacp.arch.mapper.ArchRuleComposeMapper;
import com.jd.jacp.arch.query.ArchRuleComposeQuery;
import com.jd.jacp.arch.service.IArchRuleComposeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 自动化规则表 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ArchRuleComposeServiceImpl extends ServiceImpl<ArchRuleComposeMapper, ArchRuleCompose> implements IArchRuleComposeService {

    @Override
    public IPage<ArchRuleCompose> selectPageList(ArchRuleComposeQuery query) {
        Page<ArchRuleCompose> page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseMapper.selectPageList(page, query);
    }

    @Override
    public List<ArchRuleCompose> selectList(ArchRuleComposeQuery query) {
        return baseMapper.selectList(query);
    }

    @Override
    public ArchRuleCompose selectById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public ArchRuleCompose selectByRuleCode(String ruleCode) {
        return baseMapper.selectByRuleCode(ruleCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ArchRuleComposeDTO dto, String operatorErp) {
        // 检查规则编码是否已存在
        ArchRuleCompose existRule = selectByRuleCode(dto.getRuleCode());
        if (existRule != null) {
            throw new RuntimeException("规则编码已存在：" + dto.getRuleCode());
        }

        ArchRuleCompose entity = new ArchRuleCompose();
        BeanUtils.copyProperties(dto, entity);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setCreateErp(operatorErp);
        entity.setUpdateErp(operatorErp);
        entity.setIsDel(0);

        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ArchRuleComposeDTO dto, String operatorErp) {
        ArchRuleCompose existEntity = selectById(dto.getId());
        if (existEntity == null) {
            throw new RuntimeException("自动化规则不存在");
        }

        // 检查规则编码是否被其他记录使用
        ArchRuleCompose existRule = selectByRuleCode(dto.getRuleCode());
        if (existRule != null && !existRule.getId().equals(dto.getId())) {
            throw new RuntimeException("规则编码已存在：" + dto.getRuleCode());
        }

        ArchRuleCompose entity = new ArchRuleCompose();
        BeanUtils.copyProperties(dto, entity);
        entity.setUpdateTime(new Date());
        entity.setUpdateErp(operatorErp);

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, String operatorErp) {
        ArchRuleCompose entity = selectById(id);
        if (entity == null) {
            throw new RuntimeException("自动化规则不存在");
        }

        entity.setIsDel(1);
        entity.setUpdateTime(new Date());
        entity.setUpdateErp(operatorErp);

        return updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> ids, String operatorErp) {
        return baseMapper.batchDelete(ids, operatorErp) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateState(Long id, Integer state, String operatorErp) {
        ArchRuleCompose entity = selectById(id);
        if (entity == null) {
            throw new RuntimeException("自动化规则不存在");
        }

        entity.setState(state);
        entity.setUpdateTime(new Date());
        entity.setUpdateErp(operatorErp);

        return updateById(entity);
    }

    @Override
    public void exportData(ArchRuleComposeQuery query, HttpServletResponse response) {
        try {
            List<ArchRuleCompose> dataList = selectList(query);
            
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("自动化规则数据");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "规则编码", "规则名称", "规则描述", "触发器类型", "触发器动作", 
                "流程要素编码", "原编码类型", "原编码", "目标编码类型", "目标编码",
                "发生变更的属性", "触发时机", "有未完结的执行时不能重复发起", "发布成功后的第几日", "cron表达式",
                "执行动作", "权重", "规则是否可逆", "触发OA", "触发OA涉及到的数据",
                "变更关联流程要素状态或阶段", "变更流程要素状态或阶段涉及到的数据", "触发属性变更", "变更属性涉及到的数据", "触发消息",
                "消息涉及到的数据", "触发拦截器", "拦截器涉及到的数据", "团队空间ID", "状态", "扩展字段"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                ArchRuleCompose data = dataList.get(i);
                Row row = sheet.createRow(i + 1);
                
                row.createCell(0).setCellValue(data.getRuleCode());
                row.createCell(1).setCellValue(data.getRuleName());
                row.createCell(2).setCellValue(data.getDescription());
                row.createCell(3).setCellValue(data.getTriggerType());
                row.createCell(4).setCellValue(data.getTriggerTypeAction());
                row.createCell(5).setCellValue(data.getProcessElementCode());
                row.createCell(6).setCellValue(data.getSourceType());
                row.createCell(7).setCellValue(data.getSourceCode());
                row.createCell(8).setCellValue(data.getTargetType());
                row.createCell(9).setCellValue(data.getTargetCode());
                row.createCell(10).setCellValue(data.getPropertyChangeCode());
                row.createCell(11).setCellValue(data.getTriggerTiming());
                row.createCell(12).setCellValue(data.getUnfinishedRepeated());
                row.createCell(13).setCellValue(data.getPlusN());
                row.createCell(14).setCellValue(data.getCronExpression());
                row.createCell(15).setCellValue(data.getActions());
                row.createCell(16).setCellValue(data.getPriority());
                row.createCell(17).setCellValue(data.getIsReversible());
                row.createCell(18).setCellValue(data.getOa());
                row.createCell(19).setCellValue(data.getOaData());
                row.createCell(20).setCellValue(data.getUpdateStatusOrStage());
                row.createCell(21).setCellValue(data.getUpdateStatusOrStageData());
                row.createCell(22).setCellValue(data.getPropertyChange());
                row.createCell(23).setCellValue(data.getPropertyChangeData());
                row.createCell(24).setCellValue(data.getMsg());
                row.createCell(25).setCellValue(data.getMsgData());
                row.createCell(26).setCellValue(data.getInterceptor());
                row.createCell(27).setCellValue(data.getInterceptorData());
                row.createCell(28).setCellValue(data.getSpaceId());
                row.createCell(29).setCellValue(data.getState());
                row.createCell(30).setCellValue(data.getExtraData());
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("自动化规则数据.xlsx", "UTF-8"));
            
            // 输出文件
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            log.error("导出自动化规则数据失败", e);
            throw new RuntimeException("导出数据失败");
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("自动化规则导入模板");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "规则编码", "规则名称", "规则描述", "触发器类型", "触发器动作", 
                "流程要素编码", "原编码类型", "原编码", "目标编码类型", "目标编码",
                "发生变更的属性", "触发时机", "有未完结的执行时不能重复发起", "发布成功后的第几日", "cron表达式",
                "执行动作", "权重", "规则是否可逆", "触发OA", "触发OA涉及到的数据",
                "变更关联流程要素状态或阶段", "变更流程要素状态或阶段涉及到的数据", "触发属性变更", "变更属性涉及到的数据", "触发消息",
                "消息涉及到的数据", "触发拦截器", "拦截器涉及到的数据", "团队空间ID", "状态", "扩展字段"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("自动化规则导入模板.xlsx", "UTF-8"));
            
            // 输出文件
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            log.error("下载自动化规则导入模板失败", e);
            throw new RuntimeException("下载模板失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(MultipartFile file, String operatorErp) {
        if (file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }

        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            
            List<ArchRuleCompose> dataList = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMsg = new StringBuilder();
            
            // 从第二行开始读取数据（第一行是表头）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                try {
                    ArchRuleCompose entity = new ArchRuleCompose();
                    
                    // 读取必填字段
                    String ruleCode = getCellValue(row.getCell(0));
                    String ruleName = getCellValue(row.getCell(1));
                    String description = getCellValue(row.getCell(2));
                    String actions = getCellValue(row.getCell(15));
                    
                    if (!StringUtils.hasText(ruleCode) || !StringUtils.hasText(ruleName) || 
                        !StringUtils.hasText(description) || !StringUtils.hasText(actions)) {
                        errorMsg.append("第").append(i + 1).append("行：必填字段不能为空；");
                        failCount++;
                        continue;
                    }
                    
                    // 检查规则编码是否已存在
                    ArchRuleCompose existRule = selectByRuleCode(ruleCode);
                    if (existRule != null) {
                        errorMsg.append("第").append(i + 1).append("行：规则编码已存在；");
                        failCount++;
                        continue;
                    }
                    
                    entity.setRuleCode(ruleCode);
                    entity.setRuleName(ruleName);
                    entity.setDescription(description);
                    entity.setActions(actions);
                    
                    // 读取其他字段
                    entity.setTriggerType(getIntegerCellValue(row.getCell(3)));
                    entity.setTriggerTypeAction(getIntegerCellValue(row.getCell(4)));
                    entity.setProcessElementCode(getCellValue(row.getCell(5)));
                    entity.setSourceType(getIntegerCellValue(row.getCell(6)));
                    entity.setSourceCode(getCellValue(row.getCell(7)));
                    entity.setTargetType(getIntegerCellValue(row.getCell(8)));
                    entity.setTargetCode(getCellValue(row.getCell(9)));
                    entity.setPropertyChangeCode(getCellValue(row.getCell(10)));
                    entity.setTriggerTiming(getCellValue(row.getCell(11)));
                    entity.setUnfinishedRepeated(getIntegerCellValue(row.getCell(12)));
                    entity.setPlusN(getIntegerCellValue(row.getCell(13)));
                    entity.setCronExpression(getCellValue(row.getCell(14)));
                    entity.setPriority(getIntegerCellValue(row.getCell(16)));
                    entity.setIsReversible(getIntegerCellValue(row.getCell(17)));
                    entity.setOa(getIntegerCellValue(row.getCell(18)));
                    entity.setOaData(getCellValue(row.getCell(19)));
                    entity.setUpdateStatusOrStage(getIntegerCellValue(row.getCell(20)));
                    entity.setUpdateStatusOrStageData(getCellValue(row.getCell(21)));
                    entity.setPropertyChange(getIntegerCellValue(row.getCell(22)));
                    entity.setPropertyChangeData(getCellValue(row.getCell(23)));
                    entity.setMsg(getIntegerCellValue(row.getCell(24)));
                    entity.setMsgData(getCellValue(row.getCell(25)));
                    entity.setInterceptor(getIntegerCellValue(row.getCell(26)));
                    entity.setInterceptorData(getCellValue(row.getCell(27)));
                    entity.setSpaceId(getIntegerCellValue(row.getCell(28)));
                    entity.setState(getIntegerCellValue(row.getCell(29)));
                    entity.setExtraData(getCellValue(row.getCell(30)));
                    
                    // 设置默认值
                    entity.setCreateTime(new Date());
                    entity.setUpdateTime(new Date());
                    entity.setCreateErp(operatorErp);
                    entity.setUpdateErp(operatorErp);
                    entity.setIsDel(0);
                    
                    dataList.add(entity);
                    successCount++;
                    
                } catch (Exception e) {
                    errorMsg.append("第").append(i + 1).append("行：").append(e.getMessage()).append("；");
                    failCount++;
                }
            }
            
            // 批量保存
            if (!dataList.isEmpty()) {
                saveBatch(dataList);
            }
            
            workbook.close();
            
            String result = "导入完成！成功：" + successCount + "条，失败：" + failCount + "条";
            if (errorMsg.length() > 0) {
                result += "。错误信息：" + errorMsg.toString();
            }
            
            return result;
            
        } catch (IOException e) {
            log.error("导入自动化规则数据失败", e);
            throw new RuntimeException("导入数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取单元格字符串值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }
    
    /**
     * 获取单元格整数值
     */
    private Integer getIntegerCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return (int) cell.getNumericCellValue();
            case STRING:
                String value = cell.getStringCellValue();
                if (StringUtils.hasText(value)) {
                    try {
                        return Integer.parseInt(value);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
                return null;
            default:
                return null;
        }
    }
}
