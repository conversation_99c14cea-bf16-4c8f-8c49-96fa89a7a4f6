package com.jd.jacp.arch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.jacp.common.BaseEntity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 自动化规则表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("biz_rule_compose_new")
public class ArchRuleCompose extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则编码
     */
    @TableField("rule_code")
    private String ruleCode;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String ruleName;

    /**
     * 规则描述
     */
    @TableField("description")
    private String description;

    /**
     * 触发器类型：1事项触发、2定时触发
     */
    @TableField("trigger_type")
    private Integer triggerType;

    /**
     * 触发器动作：1事项状态改变或属性改变、2事项状态改变、3事项属性改变
     */
    @TableField("trigger_type_action")
    private Integer triggerTypeAction;

    /**
     * 流程要素编码，与字典配置对应
     */
    @TableField("process_element_code")
    private String processElementCode;

    /**
     * 原编码类型：1阶段、2主状态、3子状态
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 原编码
     */
    @TableField("source_code")
    private String sourceCode;

    /**
     * 目标编码类型：1阶段、2主状态、3子状态
     */
    @TableField("target_type")
    private Integer targetType;

    /**
     * 目标编码
     */
    @TableField("target_code")
    private String targetCode;

    /**
     * 发生变更的属性
     */
    @TableField("property_change_code")
    private String propertyChangeCode;

    /**
     * 触发时机：before事前、after事后
     */
    @TableField("trigger_timing")
    private String triggerTiming;

    /**
     * 有未完结的执行时，不能重复发起：0否、1是
     */
    @TableField("unfinished_repeated")
    private Integer unfinishedRepeated;

    /**
     * 发布成功后的第几日
     */
    @TableField("plus_n")
    private Integer plusN;

    /**
     * cron表达式, 用于定时触发
     */
    @TableField("cron_expression")
    private String cronExpression;

    /**
     * 执行动作：是具体的代码函数
     */
    @TableField("actions")
    private String actions;

    /**
     * 权重
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 规则是否可逆：0不可逆、1可逆
     */
    @TableField("is_reversible")
    private Integer isReversible;

    /**
     * 触发OA：0否、1是
     */
    @TableField("oa")
    private Integer oa;

    /**
     * 触发OA涉及到的数据
     */
    @TableField("oa_data")
    private String oaData;

    /**
     * 变更关联流程要素状态或阶段：0否、1是
     */
    @TableField("update_status_or_stage")
    private Integer updateStatusOrStage;

    /**
     * 变更流程要素状态或阶段涉及到的数据
     */
    @TableField("update_status_or_stage_data")
    private String updateStatusOrStageData;

    /**
     * 触发属性变更：0否、1是
     */
    @TableField("property_change")
    private Integer propertyChange;

    /**
     * 变更属性涉及到的数据
     */
    @TableField("property_change_data")
    private String propertyChangeData;

    /**
     * 触发消息：0否、1是
     */
    @TableField("msg")
    private Integer msg;

    /**
     * 消息涉及到的数据
     */
    @TableField("msg_data")
    private String msgData;

    /**
     * 触发拦截器：0否、1是
     */
    @TableField("interceptor")
    private Integer interceptor;

    /**
     * 拦截器涉及到的数据
     */
    @TableField("interceptor_data")
    private String interceptorData;

    /**
     * 团队空间ID
     */
    @TableField("space_id")
    private Integer spaceId;

    /**
     * 状态：0禁用、1启用
     */
    @TableField("state")
    private Integer state;

    /**
     * 扩展字段
     */
    @TableField("extra_data")
    private String extraData;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建者ERP
     */
    @TableField("create_erp")
    private String createErp;

    /**
     * 更新者ERP
     */
    @TableField("update_erp")
    private String updateErp;

    /**
     * 删除标识：0未删除、1已删除
     */
    @TableField("is_del")
    private Integer isDel;
}
