package com.jd.jacp.arch.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 操作记录表DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ArchRuleOperateLogDTO", description = "操作记录表DTO")
public class ArchRuleOperateLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "自动化规则ID", required = true)
    @NotNull(message = "自动化规则ID不能为空")
    private Long ruleId;

    @ApiModelProperty(value = "操作类型（如新增、修改、删除等）", required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer actionId;

    @ApiModelProperty(value = "操作前快照（JSON 字符串）")
    @Size(max = 10000, message = "操作前快照长度不能超过10000个字符")
    private String before;

    @ApiModelProperty(value = "操作后快照（JSON 字符串）")
    @Size(max = 10000, message = "操作后快照长度不能超过10000个字符")
    private String after;
}
