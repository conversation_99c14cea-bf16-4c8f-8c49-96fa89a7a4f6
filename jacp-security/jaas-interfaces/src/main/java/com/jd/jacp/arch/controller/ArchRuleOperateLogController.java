package com.jd.jacp.arch.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jd.jacp.arch.dto.ArchRuleOperateLogDTO;
import com.jd.jacp.arch.entity.ArchRuleOperateLog;
import com.jd.jacp.arch.query.ArchRuleOperateLogQuery;
import com.jd.jacp.arch.service.IArchRuleOperateLogService;
import com.jd.jacp.common.BaseController;
import com.jd.jacp.common.Result;
import com.jd.jacp.common.ResultGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 操作记录表 前端控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("${api.arch.web}/arch/rule/operate/log")
@Api(tags = "操作记录管理")
@Validated
public class ArchRuleOperateLogController extends BaseController {

    @Autowired
    private IArchRuleOperateLogService archRuleOperateLogService;

    /**
     * 分页查询操作记录列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询操作记录列表")
    public Result<IPage<ArchRuleOperateLog>> list(@RequestBody @Valid ArchRuleOperateLogQuery query) {
        log.info("分页查询操作记录列表，查询条件：{}", query);
        IPage<ArchRuleOperateLog> result = archRuleOperateLogService.selectPageList(query);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 查询操作记录列表（不分页）
     */
    @PostMapping("/listAll")
    @ApiOperation(value = "查询操作记录列表（不分页）")
    public Result<List<ArchRuleOperateLog>> listAll(@RequestBody ArchRuleOperateLogQuery query) {
        log.info("查询操作记录列表，查询条件：{}", query);
        List<ArchRuleOperateLog> result = archRuleOperateLogService.selectList(query);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 根据ID查询操作记录详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询操作记录详情")
    public Result<ArchRuleOperateLog> getById(@PathVariable Long id) {
        log.info("根据ID查询操作记录详情，ID：{}", id);
        ArchRuleOperateLog result = archRuleOperateLogService.selectById(id);
        if (result == null) {
            return ResultGenerator.genFailResult("操作记录不存在");
        }
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 根据规则ID查询操作记录
     */
    @GetMapping("/rule/{ruleId}")
    @ApiOperation(value = "根据规则ID查询操作记录")
    public Result<List<ArchRuleOperateLog>> getByRuleId(@PathVariable Long ruleId) {
        log.info("根据规则ID查询操作记录，规则ID：{}", ruleId);
        List<ArchRuleOperateLog> result = archRuleOperateLogService.selectByRuleId(ruleId);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 新增操作记录
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增操作记录")
    public Result<String> add(@RequestBody @Valid ArchRuleOperateLogDTO dto) {
        log.info("新增操作记录，数据：{}", dto);
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleOperateLogService.insert(dto, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("新增成功");
            } else {
                return ResultGenerator.genFailResult("新增失败");
            }
        } catch (Exception e) {
            log.error("新增操作记录失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 修改操作记录
     */
    @PostMapping("/edit")
    @ApiOperation(value = "修改操作记录")
    public Result<String> edit(@RequestBody @Valid ArchRuleOperateLogDTO dto) {
        log.info("修改操作记录，数据：{}", dto);
        if (dto.getId() == null) {
            return ResultGenerator.genFailResult("ID不能为空");
        }
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleOperateLogService.update(dto, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("修改成功");
            } else {
                return ResultGenerator.genFailResult("修改失败");
            }
        } catch (Exception e) {
            log.error("修改操作记录失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 删除操作记录
     */
    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除操作记录")
    public Result<String> delete(@PathVariable Long id) {
        log.info("删除操作记录，ID：{}", id);
        try {
            boolean success = archRuleOperateLogService.delete(id);
            if (success) {
                return ResultGenerator.genSuccessResult("删除成功");
            } else {
                return ResultGenerator.genFailResult("删除失败");
            }
        } catch (Exception e) {
            log.error("删除操作记录失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 批量删除操作记录
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除操作记录")
    public Result<String> batchDelete(@RequestBody List<Long> ids) {
        log.info("批量删除操作记录，IDs：{}", ids);
        if (ids == null || ids.isEmpty()) {
            return ResultGenerator.genFailResult("请选择要删除的数据");
        }
        try {
            boolean success = archRuleOperateLogService.batchDelete(ids);
            if (success) {
                return ResultGenerator.genSuccessResult("批量删除成功");
            } else {
                return ResultGenerator.genFailResult("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除操作记录失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 导出操作记录数据
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出操作记录数据")
    public void export(@RequestBody ArchRuleOperateLogQuery query, HttpServletResponse response) {
        log.info("导出操作记录数据，查询条件：{}", query);
        try {
            archRuleOperateLogService.exportData(query, response);
        } catch (Exception e) {
            log.error("导出操作记录数据失败", e);
            throw new RuntimeException("导出数据失败：" + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载导入模板")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("下载操作记录导入模板");
        try {
            archRuleOperateLogService.downloadTemplate(response);
        } catch (Exception e) {
            log.error("下载操作记录导入模板失败", e);
            throw new RuntimeException("下载模板失败：" + e.getMessage());
        }
    }

    /**
     * 导入操作记录数据
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入操作记录数据")
    public Result<String> importData(@RequestParam("file") MultipartFile file) {
        log.info("导入操作记录数据，文件名：{}", file.getOriginalFilename());
        try {
            String operatorErp = getCurrentUserErp();
            String result = archRuleOperateLogService.importData(file, operatorErp);
            return ResultGenerator.genSuccessResult(result);
        } catch (Exception e) {
            log.error("导入操作记录数据失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }
}
