package com.jd.jacp.arch.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jd.jacp.arch.dto.ArchRuleComposeDTO;
import com.jd.jacp.arch.entity.ArchRuleCompose;
import com.jd.jacp.arch.query.ArchRuleComposeQuery;
import com.jd.jacp.arch.service.IArchRuleComposeService;
import com.jd.jacp.common.BaseController;
import com.jd.jacp.common.Result;
import com.jd.jacp.common.ResultGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 自动化规则表 前端控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("${api.arch.web}/arch/rule/compose")
@Api(tags = "自动化规则管理")
@Validated
public class ArchRuleComposeController extends BaseController {

    @Autowired
    private IArchRuleComposeService archRuleComposeService;

    /**
     * 分页查询自动化规则列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询自动化规则列表")
    public Result<IPage<ArchRuleCompose>> list(@RequestBody @Valid ArchRuleComposeQuery query) {
        log.info("分页查询自动化规则列表，查询条件：{}", query);
        IPage<ArchRuleCompose> result = archRuleComposeService.selectPageList(query);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 查询自动化规则列表（不分页）
     */
    @PostMapping("/listAll")
    @ApiOperation(value = "查询自动化规则列表（不分页）")
    public Result<List<ArchRuleCompose>> listAll(@RequestBody ArchRuleComposeQuery query) {
        log.info("查询自动化规则列表，查询条件：{}", query);
        List<ArchRuleCompose> result = archRuleComposeService.selectList(query);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 根据ID查询自动化规则详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询自动化规则详情")
    public Result<ArchRuleCompose> getById(@PathVariable Long id) {
        log.info("根据ID查询自动化规则详情，ID：{}", id);
        ArchRuleCompose result = archRuleComposeService.selectById(id);
        if (result == null) {
            return ResultGenerator.genFailResult("自动化规则不存在");
        }
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 根据规则编码查询自动化规则
     */
    @GetMapping("/code/{ruleCode}")
    @ApiOperation(value = "根据规则编码查询自动化规则")
    public Result<ArchRuleCompose> getByRuleCode(@PathVariable String ruleCode) {
        log.info("根据规则编码查询自动化规则，规则编码：{}", ruleCode);
        ArchRuleCompose result = archRuleComposeService.selectByRuleCode(ruleCode);
        if (result == null) {
            return ResultGenerator.genFailResult("自动化规则不存在");
        }
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 新增自动化规则
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增自动化规则")
    public Result<String> add(@RequestBody @Valid ArchRuleComposeDTO dto) {
        log.info("新增自动化规则，数据：{}", dto);
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleComposeService.insert(dto, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("新增成功");
            } else {
                return ResultGenerator.genFailResult("新增失败");
            }
        } catch (Exception e) {
            log.error("新增自动化规则失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 修改自动化规则
     */
    @PostMapping("/edit")
    @ApiOperation(value = "修改自动化规则")
    public Result<String> edit(@RequestBody @Valid ArchRuleComposeDTO dto) {
        log.info("修改自动化规则，数据：{}", dto);
        if (dto.getId() == null) {
            return ResultGenerator.genFailResult("ID不能为空");
        }
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleComposeService.update(dto, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("修改成功");
            } else {
                return ResultGenerator.genFailResult("修改失败");
            }
        } catch (Exception e) {
            log.error("修改自动化规则失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 删除自动化规则
     */
    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除自动化规则")
    public Result<String> delete(@PathVariable Long id) {
        log.info("删除自动化规则，ID：{}", id);
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleComposeService.delete(id, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("删除成功");
            } else {
                return ResultGenerator.genFailResult("删除失败");
            }
        } catch (Exception e) {
            log.error("删除自动化规则失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 批量删除自动化规则
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除自动化规则")
    public Result<String> batchDelete(@RequestBody List<Long> ids) {
        log.info("批量删除自动化规则，IDs：{}", ids);
        if (ids == null || ids.isEmpty()) {
            return ResultGenerator.genFailResult("请选择要删除的数据");
        }
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleComposeService.batchDelete(ids, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("批量删除成功");
            } else {
                return ResultGenerator.genFailResult("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除自动化规则失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 启用/禁用自动化规则
     */
    @PostMapping("/updateState")
    @ApiOperation(value = "启用/禁用自动化规则")
    public Result<String> updateState(@RequestParam Long id, @RequestParam Integer state) {
        log.info("启用/禁用自动化规则，ID：{}，状态：{}", id, state);
        if (state == null || (state != 0 && state != 1)) {
            return ResultGenerator.genFailResult("状态参数错误");
        }
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleComposeService.updateState(id, state, operatorErp);
            if (success) {
                String message = state == 1 ? "启用成功" : "禁用成功";
                return ResultGenerator.genSuccessResult(message);
            } else {
                return ResultGenerator.genFailResult("操作失败");
            }
        } catch (Exception e) {
            log.error("启用/禁用自动化规则失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 导出自动化规则数据
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出自动化规则数据")
    public void export(@RequestBody ArchRuleComposeQuery query, HttpServletResponse response) {
        log.info("导出自动化规则数据，查询条件：{}", query);
        try {
            archRuleComposeService.exportData(query, response);
        } catch (Exception e) {
            log.error("导出自动化规则数据失败", e);
            throw new RuntimeException("导出数据失败：" + e.getMessage());
        }
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/downloadTemplate")
    @ApiOperation(value = "下载导入模板")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("下载自动化规则导入模板");
        try {
            archRuleComposeService.downloadTemplate(response);
        } catch (Exception e) {
            log.error("下载自动化规则导入模板失败", e);
            throw new RuntimeException("下载模板失败：" + e.getMessage());
        }
    }

    /**
     * 导入自动化规则数据
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入自动化规则数据")
    public Result<String> importData(@RequestParam("file") MultipartFile file) {
        log.info("导入自动化规则数据，文件名：{}", file.getOriginalFilename());
        try {
            String operatorErp = getCurrentUserErp();
            String result = archRuleComposeService.importData(file, operatorErp);
            return ResultGenerator.genSuccessResult(result);
        } catch (Exception e) {
            log.error("导入自动化规则数据失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }
}
