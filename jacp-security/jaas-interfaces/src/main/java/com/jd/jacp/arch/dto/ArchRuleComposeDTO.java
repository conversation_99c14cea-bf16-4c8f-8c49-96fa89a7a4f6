package com.jd.jacp.arch.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 自动化规则表DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ArchRuleComposeDTO", description = "自动化规则表DTO")
public class ArchRuleComposeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "规则编码", required = true)
    @NotBlank(message = "规则编码不能为空")
    @Size(max = 50, message = "规则编码长度不能超过50个字符")
    private String ruleCode;

    @ApiModelProperty(value = "规则名称", required = true)
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 50, message = "规则名称长度不能超过50个字符")
    private String ruleName;

    @ApiModelProperty(value = "规则描述", required = true)
    @NotBlank(message = "规则描述不能为空")
    @Size(max = 100, message = "规则描述长度不能超过100个字符")
    private String description;

    @ApiModelProperty(value = "触发器类型：1事项触发、2定时触发", required = true)
    @NotNull(message = "触发器类型不能为空")
    private Integer triggerType;

    @ApiModelProperty(value = "触发器动作：1事项状态改变或属性改变、2事项状态改变、3事项属性改变")
    private Integer triggerTypeAction;

    @ApiModelProperty(value = "流程要素编码，与字典配置对应", required = true)
    @NotBlank(message = "流程要素编码不能为空")
    @Size(max = 32, message = "流程要素编码长度不能超过32个字符")
    private String processElementCode;

    @ApiModelProperty(value = "原编码类型：1阶段、2主状态、3子状态")
    private Integer sourceType;

    @ApiModelProperty(value = "原编码")
    @Size(max = 32, message = "原编码长度不能超过32个字符")
    private String sourceCode;

    @ApiModelProperty(value = "目标编码类型：1阶段、2主状态、3子状态")
    private Integer targetType;

    @ApiModelProperty(value = "目标编码")
    @Size(max = 32, message = "目标编码长度不能超过32个字符")
    private String targetCode;

    @ApiModelProperty(value = "发生变更的属性")
    @Size(max = 32, message = "发生变更的属性长度不能超过32个字符")
    private String propertyChangeCode;

    @ApiModelProperty(value = "触发时机：before事前、after事后")
    @Size(max = 32, message = "触发时机长度不能超过32个字符")
    private String triggerTiming;

    @ApiModelProperty(value = "有未完结的执行时，不能重复发起：0否、1是")
    private Integer unfinishedRepeated;

    @ApiModelProperty(value = "发布成功后的第几日")
    private Integer plusN;

    @ApiModelProperty(value = "cron表达式, 用于定时触发")
    @Size(max = 30, message = "cron表达式长度不能超过30个字符")
    private String cronExpression;

    @ApiModelProperty(value = "执行动作：是具体的代码函数", required = true)
    @NotBlank(message = "执行动作不能为空")
    @Size(max = 4000, message = "执行动作长度不能超过4000个字符")
    private String actions;

    @ApiModelProperty(value = "权重")
    private Integer priority;

    @ApiModelProperty(value = "规则是否可逆：0不可逆、1可逆")
    private Integer isReversible;

    @ApiModelProperty(value = "触发OA：0否、1是", required = true)
    @NotNull(message = "触发OA不能为空")
    private Integer oa;

    @ApiModelProperty(value = "触发OA涉及到的数据")
    @Size(max = 1024, message = "触发OA涉及到的数据长度不能超过1024个字符")
    private String oaData;

    @ApiModelProperty(value = "变更关联流程要素状态或阶段：0否、1是", required = true)
    @NotNull(message = "变更关联流程要素状态或阶段不能为空")
    private Integer updateStatusOrStage;

    @ApiModelProperty(value = "变更流程要素状态或阶段涉及到的数据")
    @Size(max = 1024, message = "变更流程要素状态或阶段涉及到的数据长度不能超过1024个字符")
    private String updateStatusOrStageData;

    @ApiModelProperty(value = "触发属性变更：0否、1是", required = true)
    @NotNull(message = "触发属性变更不能为空")
    private Integer propertyChange;

    @ApiModelProperty(value = "变更属性涉及到的数据")
    @Size(max = 100, message = "变更属性涉及到的数据长度不能超过100个字符")
    private String propertyChangeData;

    @ApiModelProperty(value = "触发消息：0否、1是", required = true)
    @NotNull(message = "触发消息不能为空")
    private Integer msg;

    @ApiModelProperty(value = "消息涉及到的数据")
    @Size(max = 1024, message = "消息涉及到的数据长度不能超过1024个字符")
    private String msgData;

    @ApiModelProperty(value = "触发拦截器：0否、1是", required = true)
    @NotNull(message = "触发拦截器不能为空")
    private Integer interceptor;

    @ApiModelProperty(value = "拦截器涉及到的数据")
    @Size(max = 1024, message = "拦截器涉及到的数据长度不能超过1024个字符")
    private String interceptorData;

    @ApiModelProperty(value = "团队空间ID")
    private Integer spaceId;

    @ApiModelProperty(value = "状态：0禁用、1启用", required = true)
    @NotNull(message = "状态不能为空")
    private Integer state;

    @ApiModelProperty(value = "扩展字段")
    @Size(max = 1024, message = "扩展字段长度不能超过1024个字符")
    private String extraData;
}
