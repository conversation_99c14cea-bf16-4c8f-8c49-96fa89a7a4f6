package com.jd.jacp.arch.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 操作记录表查询参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ArchRuleOperateLogQuery", description = "操作记录表查询参数")
public class ArchRuleOperateLogQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自动化规则ID")
    private Long ruleId;

    @ApiModelProperty(value = "操作类型")
    private Integer actionId;

    @ApiModelProperty(value = "创建者ERP")
    @Size(max = 100, message = "创建者ERP长度不能超过100个字符")
    private String createErp;

    @ApiModelProperty(value = "创建时间开始")
    private String createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    private String createTimeEnd;

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;
}
