package com.jd.jacp.arch.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jd.jacp.arch.entity.ArchRuleExecLog;
import com.jd.jacp.arch.query.ArchRuleExecLogQuery;
import com.jd.jacp.arch.service.IArchRuleExecLogService;
import com.jd.jacp.common.BaseController;
import com.jd.jacp.common.Result;
import com.jd.jacp.common.ResultGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 自动化规则执行记录表 前端控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("${api.arch.web}/arch/rule/exec/log")
@Api(tags = "执行记录管理")
@Validated
public class ArchRuleExecLogController extends BaseController {

    @Autowired
    private IArchRuleExecLogService archRuleExecLogService;

    /**
     * 分页查询执行记录列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询执行记录列表")
    public Result<IPage<ArchRuleExecLog>> list(@RequestBody @Valid ArchRuleExecLogQuery query) {
        log.info("分页查询执行记录列表，查询条件：{}", query);
        IPage<ArchRuleExecLog> result = archRuleExecLogService.selectPageList(query);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 查询执行记录列表（不分页）
     */
    @PostMapping("/listAll")
    @ApiOperation(value = "查询执行记录列表（不分页）")
    public Result<List<ArchRuleExecLog>> listAll(@RequestBody ArchRuleExecLogQuery query) {
        log.info("查询执行记录列表，查询条件：{}", query);
        List<ArchRuleExecLog> result = archRuleExecLogService.selectList(query);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 根据ID查询执行记录详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询执行记录详情")
    public Result<ArchRuleExecLog> getById(@PathVariable Long id) {
        log.info("根据ID查询执行记录详情，ID：{}", id);
        ArchRuleExecLog result = archRuleExecLogService.selectById(id);
        if (result == null) {
            return ResultGenerator.genFailResult("执行记录不存在");
        }
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 根据规则编码查询执行记录
     */
    @GetMapping("/rule/{ruleCode}")
    @ApiOperation(value = "根据规则编码查询执行记录")
    public Result<List<ArchRuleExecLog>> getByRuleCode(@PathVariable String ruleCode) {
        log.info("根据规则编码查询执行记录，规则编码：{}", ruleCode);
        List<ArchRuleExecLog> result = archRuleExecLogService.selectByRuleCode(ruleCode);
        return ResultGenerator.genSuccessResult(result);
    }

    /**
     * 删除执行记录
     */
    @PostMapping("/delete/{id}")
    @ApiOperation(value = "删除执行记录")
    public Result<String> delete(@PathVariable Long id) {
        log.info("删除执行记录，ID：{}", id);
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleExecLogService.delete(id, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("删除成功");
            } else {
                return ResultGenerator.genFailResult("删除失败");
            }
        } catch (Exception e) {
            log.error("删除执行记录失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 批量删除执行记录
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除执行记录")
    public Result<String> batchDelete(@RequestBody List<Long> ids) {
        log.info("批量删除执行记录，IDs：{}", ids);
        if (ids == null || ids.isEmpty()) {
            return ResultGenerator.genFailResult("请选择要删除的数据");
        }
        try {
            String operatorErp = getCurrentUserErp();
            boolean success = archRuleExecLogService.batchDelete(ids, operatorErp);
            if (success) {
                return ResultGenerator.genSuccessResult("批量删除成功");
            } else {
                return ResultGenerator.genFailResult("批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除执行记录失败", e);
            return ResultGenerator.genFailResult(e.getMessage());
        }
    }

    /**
     * 导出执行记录数据
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出执行记录数据")
    public void export(@RequestBody ArchRuleExecLogQuery query, HttpServletResponse response) {
        log.info("导出执行记录数据，查询条件：{}", query);
        try {
            archRuleExecLogService.exportData(query, response);
        } catch (Exception e) {
            log.error("导出执行记录数据失败", e);
            throw new RuntimeException("导出数据失败：" + e.getMessage());
        }
    }
}
