package com.jd.jacp.arch.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 自动化规则表查询参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ArchRuleComposeQuery", description = "自动化规则表查询参数")
public class ArchRuleComposeQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则编码")
    @Size(max = 50, message = "规则编码长度不能超过50个字符")
    private String ruleCode;

    @ApiModelProperty(value = "规则名称")
    @Size(max = 50, message = "规则名称长度不能超过50个字符")
    private String ruleName;

    @ApiModelProperty(value = "触发器类型：1事项触发、2定时触发")
    private Integer triggerType;

    @ApiModelProperty(value = "触发器动作：1事项状态改变或属性改变、2事项状态改变、3事项属性改变")
    private Integer triggerTypeAction;

    @ApiModelProperty(value = "流程要素编码")
    @Size(max = 32, message = "流程要素编码长度不能超过32个字符")
    private String processElementCode;

    @ApiModelProperty(value = "状态：0禁用、1启用")
    private Integer state;

    @ApiModelProperty(value = "团队空间ID")
    private Integer spaceId;

    @ApiModelProperty(value = "创建者ERP")
    @Size(max = 100, message = "创建者ERP长度不能超过100个字符")
    private String createErp;

    @ApiModelProperty(value = "创建时间开始")
    private String createTimeStart;

    @ApiModelProperty(value = "创建时间结束")
    private String createTimeEnd;

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;
}
