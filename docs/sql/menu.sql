-- 应知应会管理菜单SQL

-- 1. 添加模块记录到 auth_module 表
INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
VALUES ('arch', 'archKnowledgeConfig', '应知应会管理', 'plat', '14', 'system');

-- 2. 添加菜单记录到 auth_menu 表
-- 注意：应知应会现在是一个独立的设置页面
INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
VALUES (-1, 'archKnowledgeConfig', '应知应会管理', 14, 'archKnowledgeConfig', 1, 'arch', 'el-icon-document', 'archKnowledgeConfig:view');

-- 3. 添加权限记录到 auth_permission 表
-- 基础查看权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archKnowledgeConfig', 'archKnowledgeConfig:view', '查看页面', '1', '应知应会管理查看权限', 'system', NOW());

-- 新增权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archKnowledgeConfig', 'archKnowledgeConfig:add', '新增', '2', '应知应会管理新增权限', 'system', NOW());

-- 编辑权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archKnowledgeConfig', 'archKnowledgeConfig:edit', '编辑', '3', '应知应会管理编辑权限', 'system', NOW());

-- 删除权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archKnowledgeConfig', 'archKnowledgeConfig:delete', '删除', '4', '应知应会管理删除权限', 'system', NOW());

-- 管理权限（可选，如果需要编辑功能）
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archKnowledgeConfig', 'archKnowledgeConfig:admin', '管理', '5', '应知应会管理权限', 'system', NOW());

-- 4. 添加权限依赖关系到 auth_permission_property 表
-- 新增权限依赖查看权限
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archKnowledgeConfig:add', 'DEPEND', 'archKnowledgeConfig:view', 'system', NOW());

-- 编辑权限依赖查看权限
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archKnowledgeConfig:edit', 'DEPEND', 'archKnowledgeConfig:view', 'system', NOW());

-- 删除权限依赖查看权限
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archKnowledgeConfig:delete', 'DEPEND', 'archKnowledgeConfig:view', 'system', NOW());

-- 管理权限依赖查看权限
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archKnowledgeConfig:admin', 'DEPEND', 'archKnowledgeConfig:view', 'system', NOW());

-- 5. 添加权限到admin权限模板 auth_permission_template 表
-- 为archAdmin模板添加查看权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archKnowledgeConfig:view', 1, 'system', NOW());

-- 为archAdmin模板添加新增权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archKnowledgeConfig:add', 1, 'system', NOW());

-- 为archAdmin模板添加编辑权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archKnowledgeConfig:edit', 1, 'system', NOW());

-- 为archAdmin模板添加删除权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archKnowledgeConfig:delete', 1, 'system', NOW());

-- 为archAdmin模板添加管理权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archKnowledgeConfig:admin', 1, 'system', NOW());

-- 6. 为admin角色分配权限到 auth_role_permission 表
-- 分配查看权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archKnowledgeConfig:view', 'system', '', NOW());

-- 分配新增权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archKnowledgeConfig:add', 'system', '', NOW());

-- 分配编辑权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archKnowledgeConfig:edit', 'system', '', NOW());

-- 分配删除权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archKnowledgeConfig:delete', 'system', '', NOW());

-- 分配管理权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archKnowledgeConfig:admin', 'system', '', NOW());

-- 7. 添加UI组件权限配置到 auth_resource_ui_component 表
-- 菜单查看权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archKnowledgeConfig', 'menu:archKnowledgeConfig:view', '平台设置菜单-应知应会管理', 'btn', 'archKnowledgeConfig:view', 1, 1, 'system', NOW());

-- 页面查看权限（整体设置页面中的tab）
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:view', '应知应会管理-查看', 'tab', 'archKnowledgeConfig:view', 1, 1, 'system', NOW());

-- 新增按钮权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:add', '应知应会管理-新增', 'btn', 'archKnowledgeConfig:add', 1, 1, 'system', NOW());

-- 编辑按钮权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:edit', '应知应会管理-编辑', 'btn', 'archKnowledgeConfig:edit', 1, 1, 'system', NOW());

-- 删除按钮权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:delete', '应知应会管理-删除', 'btn', 'archKnowledgeConfig:delete', 1, 1, 'system', NOW());


-- ========================================
-- 自动化规则管理菜单SQL
-- ========================================

-- 1. 添加模块记录到 auth_module 表
INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
VALUES ('arch', 'bizRuleComposeNew', '自动化规则管理', 'plat', '15', 'system');

-- 2. 添加菜单记录到 auth_menu 表
INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
VALUES (-1, 'bizRuleComposeNew', '自动化规则管理', 15, 'bizRuleComposeNew', 1, 'arch', 'el-icon-setting', 'bizRuleComposeNew:view');

-- 3. 添加权限记录到 auth_permission 表
-- 基础查看权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'bizRuleComposeNew', 'bizRuleComposeNew:view', '查看页面', '1', '自动化规则管理查看权限', 'system', NOW());

-- 新增权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'bizRuleComposeNew', 'bizRuleComposeNew:add', '新增', '2', '自动化规则管理新增权限', 'system', NOW());

-- 编辑权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'bizRuleComposeNew', 'bizRuleComposeNew:edit', '编辑', '3', '自动化规则管理编辑权限', 'system', NOW());

-- 删除权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'bizRuleComposeNew', 'bizRuleComposeNew:delete', '删除', '4', '自动化规则管理删除权限', 'system', NOW());

-- 4. 添加权限依赖关系到 auth_permission_property 表
-- 新增权限依赖查看权限
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('bizRuleComposeNew:add', 'DEPEND', 'bizRuleComposeNew:view', 'system', NOW());

-- 编辑权限依赖查看权限
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('bizRuleComposeNew:edit', 'DEPEND', 'bizRuleComposeNew:view', 'system', NOW());

-- 删除权限依赖查看权限
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('bizRuleComposeNew:delete', 'DEPEND', 'bizRuleComposeNew:view', 'system', NOW());

-- 5. 添加权限到admin权限模板 auth_permission_template 表
-- 为archAdmin模板添加查看权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'bizRuleComposeNew:view', 1, 'system', NOW());

-- 为archAdmin模板添加新增权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'bizRuleComposeNew:add', 1, 'system', NOW());

-- 为archAdmin模板添加编辑权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'bizRuleComposeNew:edit', 1, 'system', NOW());

-- 为archAdmin模板添加删除权限
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'bizRuleComposeNew:delete', 1, 'system', NOW());

-- 6. 为admin角色分配权限到 auth_role_permission 表
-- 分配查看权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'bizRuleComposeNew:view', 'system', '', NOW());

-- 分配新增权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'bizRuleComposeNew:add', 'system', '', NOW());

-- 分配编辑权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'bizRuleComposeNew:edit', 'system', '', NOW());

-- 分配删除权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'bizRuleComposeNew:delete', 'system', '', NOW());

-- 7. 添加UI组件权限配置到 auth_resource_ui_component 表
-- 菜单查看权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('bizRuleComposeNew', 'menu:bizRuleComposeNew:view', '平台设置菜单-自动化规则管理', 'btn', 'bizRuleComposeNew:view', 1, 1, 'system', NOW());

-- 页面查看权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('bizRuleComposeNew', 'bizRuleComposeNew:view', '自动化规则管理-查看', 'tab', 'bizRuleComposeNew:view', 1, 1, 'system', NOW());

-- 新增按钮权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('bizRuleComposeNew', 'bizRuleComposeNew:add', '自动化规则管理-新增', 'btn', 'bizRuleComposeNew:add', 1, 1, 'system', NOW());

-- 编辑按钮权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('bizRuleComposeNew', 'bizRuleComposeNew:edit', '自动化规则管理-编辑', 'btn', 'bizRuleComposeNew:edit', 1, 1, 'system', NOW());

-- 删除按钮权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('bizRuleComposeNew', 'bizRuleComposeNew:delete', '自动化规则管理-删除', 'btn', 'bizRuleComposeNew:delete', 1, 1, 'system', NOW());