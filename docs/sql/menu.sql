-- =====================================================
-- 自动化规则管理模块菜单SQL配置
-- =====================================================

-- 1. 添加模块记录到 auth_module 表
INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
VALUES ('arch', 'archRuleCompose', '自动化规则管理', 'plat', '15', 'system');

INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
VALUES ('arch', 'archRuleOperateLog', '操作记录管理', 'plat', '16', 'system');

INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
VALUES ('arch', 'archRuleExecLog', '执行记录管理', 'plat', '17', 'system');

INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
VALUES ('arch', 'archOaRecord', 'OA记录管理', 'plat', '18', 'system');

INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
VALUES ('arch', 'archProcessElementStatus', '流程要素状态管理', 'plat', '19', 'system');

-- 2. 添加菜单记录到 auth_menu 表
-- 自动化规则管理菜单
INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
VALUES (-1, 'archRuleCompose', '自动化规则管理', 15, 'archRuleCompose', 1, 'arch', 'el-icon-setting', 'archRuleCompose:view');

-- 操作记录管理菜单
INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
VALUES (-1, 'archRuleOperateLog', '操作记录管理', 16, 'archRuleOperateLog', 1, 'arch', 'el-icon-document', 'archRuleOperateLog:view');

-- 执行记录管理菜单
INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
VALUES (-1, 'archRuleExecLog', '执行记录管理', 17, 'archRuleExecLog', 1, 'arch', 'el-icon-tickets', 'archRuleExecLog:view');

-- OA记录管理菜单
INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
VALUES (-1, 'archOaRecord', 'OA记录管理', 18, 'archOaRecord', 1, 'arch', 'el-icon-files', 'archOaRecord:view');

-- 流程要素状态管理菜单
INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
VALUES (-1, 'archProcessElementStatus', '流程要素状态管理', 19, 'archProcessElementStatus', 1, 'arch', 'el-icon-connection', 'archProcessElementStatus:view');

-- 3. 添加权限记录到 auth_permission 表
-- 自动化规则管理权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleCompose', 'archRuleCompose:view', '查看页面', '1', '自动化规则管理查看权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleCompose', 'archRuleCompose:add', '新增', '2', '自动化规则管理新增权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleCompose', 'archRuleCompose:edit', '编辑', '3', '自动化规则管理编辑权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleCompose', 'archRuleCompose:delete', '删除', '4', '自动化规则管理删除权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleCompose', 'archRuleCompose:export', '导出', '5', '自动化规则管理导出权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleCompose', 'archRuleCompose:import', '导入', '6', '自动化规则管理导入权限', 'system', NOW());

-- 操作记录管理权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleOperateLog', 'archRuleOperateLog:view', '查看页面', '1', '操作记录管理查看权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleOperateLog', 'archRuleOperateLog:add', '新增', '2', '操作记录管理新增权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleOperateLog', 'archRuleOperateLog:edit', '编辑', '3', '操作记录管理编辑权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleOperateLog', 'archRuleOperateLog:delete', '删除', '4', '操作记录管理删除权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleOperateLog', 'archRuleOperateLog:export', '导出', '5', '操作记录管理导出权限', 'system', NOW());

-- 执行记录管理权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleExecLog', 'archRuleExecLog:view', '查看页面', '1', '执行记录管理查看权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleExecLog', 'archRuleExecLog:delete', '删除', '2', '执行记录管理删除权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archRuleExecLog', 'archRuleExecLog:export', '导出', '3', '执行记录管理导出权限', 'system', NOW());

-- OA记录管理权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archOaRecord', 'archOaRecord:view', '查看页面', '1', 'OA记录管理查看权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archOaRecord', 'archOaRecord:add', '新增', '2', 'OA记录管理新增权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archOaRecord', 'archOaRecord:edit', '编辑', '3', 'OA记录管理编辑权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archOaRecord', 'archOaRecord:delete', '删除', '4', 'OA记录管理删除权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archOaRecord', 'archOaRecord:export', '导出', '5', 'OA记录管理导出权限', 'system', NOW());

-- 流程要素状态管理权限
INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archProcessElementStatus', 'archProcessElementStatus:view', '查看页面', '1', '流程要素状态管理查看权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archProcessElementStatus', 'archProcessElementStatus:add', '新增', '2', '流程要素状态管理新增权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archProcessElementStatus', 'archProcessElementStatus:edit', '编辑', '3', '流程要素状态管理编辑权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archProcessElementStatus', 'archProcessElementStatus:delete', '删除', '4', '流程要素状态管理删除权限', 'system', NOW());

INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
VALUES ('arch', 'archProcessElementStatus', 'archProcessElementStatus:export', '导出', '5', '流程要素状态管理导出权限', 'system', NOW());

-- 4. 添加权限依赖关系到 auth_permission_property 表
-- 自动化规则管理权限依赖
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleCompose:add', 'DEPEND', 'archRuleCompose:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleCompose:edit', 'DEPEND', 'archRuleCompose:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleCompose:delete', 'DEPEND', 'archRuleCompose:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleCompose:export', 'DEPEND', 'archRuleCompose:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleCompose:import', 'DEPEND', 'archRuleCompose:view', 'system', NOW());

-- 操作记录管理权限依赖
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleOperateLog:add', 'DEPEND', 'archRuleOperateLog:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleOperateLog:edit', 'DEPEND', 'archRuleOperateLog:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleOperateLog:delete', 'DEPEND', 'archRuleOperateLog:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleOperateLog:export', 'DEPEND', 'archRuleOperateLog:view', 'system', NOW());

-- 执行记录管理权限依赖
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleExecLog:delete', 'DEPEND', 'archRuleExecLog:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archRuleExecLog:export', 'DEPEND', 'archRuleExecLog:view', 'system', NOW());

-- OA记录管理权限依赖
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archOaRecord:add', 'DEPEND', 'archOaRecord:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archOaRecord:edit', 'DEPEND', 'archOaRecord:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archOaRecord:delete', 'DEPEND', 'archOaRecord:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archOaRecord:export', 'DEPEND', 'archOaRecord:view', 'system', NOW());

-- 流程要素状态管理权限依赖
INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archProcessElementStatus:add', 'DEPEND', 'archProcessElementStatus:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archProcessElementStatus:edit', 'DEPEND', 'archProcessElementStatus:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archProcessElementStatus:delete', 'DEPEND', 'archProcessElementStatus:view', 'system', NOW());

INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
VALUES('archProcessElementStatus:export', 'DEPEND', 'archProcessElementStatus:view', 'system', NOW());

-- 5. 添加权限到admin权限模板 auth_permission_template 表
-- 自动化规则管理权限模板
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleCompose:view', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleCompose:add', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleCompose:edit', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleCompose:delete', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleCompose:export', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleCompose:import', 1, 'system', NOW());

-- 操作记录管理权限模板
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleOperateLog:view', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleOperateLog:add', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleOperateLog:edit', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleOperateLog:delete', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleOperateLog:export', 1, 'system', NOW());

-- 执行记录管理权限模板
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleExecLog:view', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleExecLog:delete', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archRuleExecLog:export', 1, 'system', NOW());

-- OA记录管理权限模板
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archOaRecord:view', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archOaRecord:add', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archOaRecord:edit', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archOaRecord:delete', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archOaRecord:export', 1, 'system', NOW());

-- 流程要素状态管理权限模板
INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archProcessElementStatus:view', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archProcessElementStatus:add', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archProcessElementStatus:edit', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archProcessElementStatus:delete', 1, 'system', NOW());

INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
VALUES('archAdmin', 'archProcessElementStatus:export', 1, 'system', NOW());

-- 6. 为admin角色分配权限到 auth_role_permission 表
-- 自动化规则管理角色权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleCompose:view', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleCompose:add', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleCompose:edit', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleCompose:delete', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleCompose:export', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleCompose:import', 'system', '', NOW());

-- 操作记录管理角色权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleOperateLog:view', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleOperateLog:add', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleOperateLog:edit', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleOperateLog:delete', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleOperateLog:export', 'system', '', NOW());

-- 执行记录管理角色权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleExecLog:view', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleExecLog:delete', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archRuleExecLog:export', 'system', '', NOW());

-- OA记录管理角色权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archOaRecord:view', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archOaRecord:add', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archOaRecord:edit', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archOaRecord:delete', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archOaRecord:export', 'system', '', NOW());

-- 流程要素状态管理角色权限
INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archProcessElementStatus:view', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archProcessElementStatus:add', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archProcessElementStatus:edit', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archProcessElementStatus:delete', 'system', '', NOW());

INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
VALUES ('arch', 'admin', 'archProcessElementStatus:export', 'system', '', NOW());

-- 7. 添加UI组件权限配置到 auth_resource_ui_component 表
-- 自动化规则管理UI组件权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleCompose', 'menu:archRuleCompose:view', '平台设置菜单-自动化规则管理', 'btn', 'archRuleCompose:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleCompose', 'archRuleCompose:view', '自动化规则管理-查看', 'tab', 'archRuleCompose:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleCompose', 'archRuleCompose:add', '自动化规则管理-新增', 'btn', 'archRuleCompose:add', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleCompose', 'archRuleCompose:edit', '自动化规则管理-编辑', 'btn', 'archRuleCompose:edit', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleCompose', 'archRuleCompose:delete', '自动化规则管理-删除', 'btn', 'archRuleCompose:delete', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleCompose', 'archRuleCompose:export', '自动化规则管理-导出', 'btn', 'archRuleCompose:export', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleCompose', 'archRuleCompose:import', '自动化规则管理-导入', 'btn', 'archRuleCompose:import', 1, 1, 'system', NOW());

-- 操作记录管理UI组件权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleOperateLog', 'menu:archRuleOperateLog:view', '平台设置菜单-操作记录管理', 'btn', 'archRuleOperateLog:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleOperateLog', 'archRuleOperateLog:view', '操作记录管理-查看', 'tab', 'archRuleOperateLog:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleOperateLog', 'archRuleOperateLog:delete', '操作记录管理-删除', 'btn', 'archRuleOperateLog:delete', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleOperateLog', 'archRuleOperateLog:export', '操作记录管理-导出', 'btn', 'archRuleOperateLog:export', 1, 1, 'system', NOW());

-- 执行记录管理UI组件权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleExecLog', 'menu:archRuleExecLog:view', '平台设置菜单-执行记录管理', 'btn', 'archRuleExecLog:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleExecLog', 'archRuleExecLog:view', '执行记录管理-查看', 'tab', 'archRuleExecLog:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleExecLog', 'archRuleExecLog:delete', '执行记录管理-删除', 'btn', 'archRuleExecLog:delete', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archRuleExecLog', 'archRuleExecLog:export', '执行记录管理-导出', 'btn', 'archRuleExecLog:export', 1, 1, 'system', NOW());

-- OA记录管理UI组件权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archOaRecord', 'menu:archOaRecord:view', '平台设置菜单-OA记录管理', 'btn', 'archOaRecord:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archOaRecord', 'archOaRecord:view', 'OA记录管理-查看', 'tab', 'archOaRecord:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archOaRecord', 'archOaRecord:add', 'OA记录管理-新增', 'btn', 'archOaRecord:add', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archOaRecord', 'archOaRecord:edit', 'OA记录管理-编辑', 'btn', 'archOaRecord:edit', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archOaRecord', 'archOaRecord:delete', 'OA记录管理-删除', 'btn', 'archOaRecord:delete', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archOaRecord', 'archOaRecord:export', 'OA记录管理-导出', 'btn', 'archOaRecord:export', 1, 1, 'system', NOW());

-- 流程要素状态管理UI组件权限
INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archProcessElementStatus', 'menu:archProcessElementStatus:view', '平台设置菜单-流程要素状态管理', 'btn', 'archProcessElementStatus:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archProcessElementStatus', 'archProcessElementStatus:view', '流程要素状态管理-查看', 'tab', 'archProcessElementStatus:view', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archProcessElementStatus', 'archProcessElementStatus:add', '流程要素状态管理-新增', 'btn', 'archProcessElementStatus:add', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archProcessElementStatus', 'archProcessElementStatus:edit', '流程要素状态管理-编辑', 'btn', 'archProcessElementStatus:edit', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archProcessElementStatus', 'archProcessElementStatus:delete', '流程要素状态管理-删除', 'btn', 'archProcessElementStatus:delete', 1, 1, 'system', NOW());

INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
VALUES ('archProcessElementStatus', 'archProcessElementStatus:export', '流程要素状态管理-导出', 'btn', 'archProcessElementStatus:export', 1, 1, 'system', NOW());