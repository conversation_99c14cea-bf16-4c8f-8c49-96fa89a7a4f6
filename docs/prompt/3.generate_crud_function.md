# CRUD代码生成指令

## 任务概述
读取`docs/sql/init.sql`中的DDL建表信息，和`docs/design/自动化规则设计文档（crud）.md`文档中的`${1.3}`章节的字段信息，生成基于数据表定义的完整CRUD功能，包括后端Java代码、前端Vue界面和菜单SQL。

## 数据源文件
- DDL文件：`docs/sql/init.sql` - 包含表结构定义，用于生成CRUD功能
- 数据字典文件：`docs/sql/dict_data.sql` - 包含数据字典定义，用于生成下拉选项和展示值

## 代码生成规范

### 1. 通用要求`
- **必须实现的功能**：
  - 列表查询、分页、条件筛选
  - 新增记录、编辑记录、删除记录、批量删除
  - 数据导入、数据导出、模板下载（模板下载链接必须放在导入弹窗内）
  - Excel模板必须使用中文表头，包含表中所有业务字段，不得遗漏
- **接口规范**：
  - 前后端接口地址和数据格式必须完全一致
  - 导入和导出功能必须使用POST请求方式
- **数据校验**：
  - 必须根据SQL定义的字段类型/长度在DTO和Query参数中添加对应校验注解（如@NotNull、@Size等）
  - 必须覆盖查询/新增/编辑所有场景的输入校验

### 2. 后端实现规范
- **代码位置**：所有后端代码必须放在app模块下
- **代码结构**：必须生成以下Java类：
  - Controller：处理HTTP请求,生成的代码放在 jaas-interface模块(jacp-security下)
  - DTO：数据传输对象，生成的代码放在 jaas-interface模块(jacp-security下)
  - Entity：数据库实体，生成的代码放在 jaas-domain-app模块(jacp-security下)
  - Mapper：数据库操作接口，生成的代码放在 jaas-domain-app模块(jacp-security下)
  - Query：查询参数对象，生成的代码放在 jaas-interface模块(jacp-security下)
  - Service：业务逻辑接口，生成的代码放在 jaas-domain-app模块(jacp-security下)
  - ServiceImpl：业务逻辑实现，生成的代码放在 jaas-domain-app模块(jacp-security下)
  - Mapper XML：MyBatis映射文件，生成的代码放在 jaas-domain-app模块(jacp-security下)

- **数据库操作**：
  - Mapper.xml的insert语句不得包含create_time/update_time/is_del字段，这些字段由框架自动处理

- **命名规范**：
  - 类名必须基于表名生成，示例：
    - 表名：dur_liability_cash_flow（dur为模块名）
    - 类名：DurLiabilityCashFlow[Controller|DTO|Entity|Mapper|Query|Service|ServiceImpl]
  - 生成的DTO、Query、Entity属性名和类型必须严格对应SQL定义的字段名和类型
  - 不得添加SQL定义之外的额外属性
  - Controller的@RequestMapping路径使用`模块名+小写类名前缀`，多个单词间用斜杠(/)分隔

### 3. 前端实现规范（web模块）
- **代码位置**：所有前端代码必须放在web工程下
- **文件结构**：
  - 必须按照`web/src/modules/option/*.vue`格式创建目录和文件,*要用驼峰命名方式。
  - 示例：DurLiabilityCashFlowController → `web/src/modules/option/durLiabilityCashFlow.vue`（dur为模块名）
- **代码参考**：
  - 必须参照web/src/modules/option/adminOption.list.vue文件
  - 包括js文件、路由配置、模型定义和整体架构（包括页面权限引入方式,权限的引入要完全和adminOption.list.vue保持一致）
  - 页面的权限控制：要用到后面生成的`docs/sql/menu.sql`里auth_resource_ui_component的 code字段控制
- **数据校验**：必须对所有输入项进行校验，包括：
  - 列表页查询条件
  - 新增、编辑页的所有输入项
  - 校验规则必须参考DDL语句中的数据类型、格式及长度
  ```
- **下拉框实现**：所有下拉框代码必须按以下格式生成

### 4. 菜单SQL生成规范
- **写入SQL文件**：生成的菜单SQL必须写入`docs/sql/menu.sql`这个文件，禁止写入其他文件
- **格式示例**：
  ```sql
  -- 1. 添加模块记录到 auth_module 表
  INSERT INTO `auth_module` (app_code, code, name, category, sequence, create_user)
  VALUES ('arch', 'archKnowledgeConfig', '应知应会管理', 'plat', '14', 'system');

  -- 2. 添加菜单记录到 auth_menu 表
  -- 注意：应知应会现在是一个独立的设置页面
  INSERT INTO `auth_menu` (parent_id, code, name, sequence, url, status, app_code, icon, permission_code)
  VALUES (-1, 'archKnowledgeConfig', '应知应会管理', 14, 'archKnowledgeConfig', 1, 'arch', 'el-icon-document', 'archKnowledgeConfig:view');
  
  -- 3. 添加权限记录到 auth_permission 表
  -- 基础查看权限
  INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
  VALUES ('arch', 'archKnowledgeConfig', 'archKnowledgeConfig:view', '查看页面', '1', '应知应会管理查看权限', 'system', NOW());
  
  -- 管理权限（可选，如果需要编辑功能）
  INSERT INTO `auth_permission` (app_code, module_code, code, name, sequence, description, create_user, create_time)
  VALUES ('arch', 'archKnowledgeConfig', 'archKnowledgeConfig:admin', '管理', '2', '应知应会管理权限', 'system', NOW());
  
  -- 4. 添加权限依赖关系到 auth_permission_property 表
  -- 管理权限依赖查看权限
  INSERT INTO auth_permission_property(permission_code, prop_key, prop_value, create_user, create_time)
  VALUES('archKnowledgeConfig:admin', 'DEPEND', 'archKnowledgeConfig:view', 'system', NOW());
  
  -- 5. 添加权限到admin权限模板 auth_permission_template 表
  -- 为archAdmin模板添加查看权限
  INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
  VALUES('archAdmin', 'archKnowledgeConfig:view', 1, 'system', NOW());
  
  -- 为archAdmin模板添加管理权限
  INSERT INTO `auth_permission_template` (template_code, permission_code, enabled, create_user, create_time)
  VALUES('archAdmin', 'archKnowledgeConfig:admin', 1, 'system', NOW());
  
  -- 6. 为admin角色分配权限到 auth_role_permission 表
  -- 分配查看权限
  INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
  VALUES ('arch', 'admin', 'archKnowledgeConfig:view', 'system', '', NOW());
  
  -- 分配管理权限
  INSERT INTO `auth_role_permission` (app_code, role_code, permission_code, create_user, scope, create_time)
  VALUES ('arch', 'admin', 'archKnowledgeConfig:admin', 'system', '', NOW());
  
  -- 7. 添加UI组件权限配置到 auth_resource_ui_component 表（可选）
  -- 菜单查看权限
  INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
  VALUES ('archKnowledgeConfig', 'menu:archKnowledgeConfig:view', '平台设置菜单-应知应会管理', 'btn', 'archKnowledgeConfig:view', 1, 1, 'system', NOW());
  
  -- 页面查看权限（整体设置页面中的tab）
  INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
  VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:view', '应知应会管理-查看', 'tab', 'archKnowledgeConfig:view', 1, 1, 'system', NOW());
  
  -- 编辑按钮权限
  INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
  VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:edit', '应知应会管理-编辑', 'btn', 'archKnowledgeConfig:admin', 1, 1, 'system', NOW());
  
  -- 新增按钮权限
  INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
  VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:add', '应知应会管理-新增', 'btn', 'archKnowledgeConfig:admin', 1, 1, 'system', NOW());
  
  -- 删除按钮权限
  INSERT INTO `auth_resource_ui_component`(module_code, code, name, type, permission_code, visible, auth_present_type, create_user, create_time)
  VALUES ('archKnowledgeConfig', 'archKnowledgeConfig:delete', '应知应会管理-删除', 'btn', 'archKnowledgeConfig:admin', 1, 1, 'system', NOW());
  ```

## 执行要求
- **代码生成位置**：必须严格按照上述规范在指定模块目录下生成代码
- **执行顺序**：先生成后端代码，再生成前端代码，最后生成菜单SQL
- **任务拆分**：当表字段信息超过30个时，拆分为多个分片任务完成字段补全，保证生成全量字段信息
- **代码质量**：生成的代码必须符合Java和Vue代码规范，包括缩进、命名、注释等
- **代码检查**：生成完所以的代码后，请先自己仔细检查所以有的代码是否有问题
- 生成代码前请先与我确认，你是否对需求有其他疑问