# 字典数据生成工具

## 功能描述
根据数据库表结构自动生成字典数据的INSERT语句，用于系统初始化。

## 详细需求
1. **输入文件**：
  - 读取`docs/sql/init.sql`文件中的DDL建表语句

2. **输出文件**：
  - 生成的INSERT语句将写入`docs/sql/dict.sql`文件

3. **处理规则**：
  - 分析DDL中的字段定义，识别COMMENT中包含枚举值或多选值的字段
  - 对于每个识别出的多值字段，生成对应的字典类型和字典数据INSERT语句
  - 字典类型命名规则：`{模块名前缀}_{表名}_{字段名}`

4. **字段处理**：
  - 自增字段不需要包含在INSERT语句中：
    - `biz_dict`表的`id`字段
    - `biz_dict_data`表的`id`字段
  - 所有INSERT语句必须包含：
    - 字典表：dict_name, dict_code, status, create_erp, create_time
    - 字典数据表：dict_sort, dict_label, dict_value, dict_code

## 表结构参考
```sql
CREATE TABLE biz_dict (
   id INT NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
   dict_name VARCHAR(50) DEFAULT NULL COMMENT '字典名称',
   dict_code VARCHAR(50) DEFAULT NULL COMMENT '字典编码',
   dict_desc VARCHAR(255) DEFAULT NULL COMMENT '字典描述',
   dict_sort INT DEFAULT NULL COMMENT '字典排序值',
   dict_url VARCHAR(255) DEFAULT NULL COMMENT '字典数据源URL',
   status INT DEFAULT NULL COMMENT '状态  0禁用 1正常',
   create_erp VARCHAR(32) DEFAULT NULL COMMENT '创建人',
   create_time DATETIME(6) DEFAULT NULL COMMENT '创建时间',
   update_erp VARCHAR(32) DEFAULT NULL COMMENT '修改人',
   update_time DATETIME(6) DEFAULT NULL COMMENT '修改时间',
   PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典表';

CREATE TABLE biz_dict_data (
   id BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
   dict_code VARCHAR(50) DEFAULT NULL COMMENT '字典编码',
   dict_label VARCHAR(50) DEFAULT NULL COMMENT '字典label',
   dict_value VARCHAR(128) DEFAULT NULL COMMENT '字典值，系统自动生成',
   dict_sort INT DEFAULT NULL COMMENT '字典排序值',
   level INT DEFAULT NULL COMMENT '层级',
   parent_id INT NOT NULL DEFAULT 0 COMMENT '字典数据上级ID，顶级=0',
   color VARCHAR(32) DEFAULT NULL COMMENT '文本颜色',
   background_color VARCHAR(32) DEFAULT NULL COMMENT '背景颜色',
   icon VARCHAR(255) DEFAULT NULL COMMENT '图标或图片路径',
   PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

```

## 输出示例
```sql
-- 字典类型
INSERT INTO arch.biz_dict_data (
    dict_code,
    dict_label,
    dict_value,
    dict_sort,
    level,
    parent_id,
    color,
    background_color,
    icon
) VALUES (
             'gender',
             '男',
             'M',
             1,
             1,
             0,
             '#000000',
             '#FFFFFF',
             NULL
         );


-- 字典数据
INSERT INTO arch.biz_dict_data (
    dict_code,
    dict_label,
    dict_value,
    dict_sort,
    level,
    parent_id,
    color,
    background_color,
    icon
) VALUES (
             'gender',
             '男',
             'M',
             1,
             1,
             0,
             '#000000',
             '#FFFFFF',
             NULL
         );

```
