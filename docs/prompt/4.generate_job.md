# 批处理任务代码生成指南

## 基本要求
- 基于`docs/design/${设计文档名称}`文档中的`${章节号}`章节需求生成完整功能代码
- 所有代码必须生成到`job`模块下，严格遵循项目现有的包结构和命名规范
- 代码生成过程必须认真、细致，确保无语法错误、逻辑错误和编译错误

## 代码规范
- 使用JDK 1.8版本，不使用高于Java 8的语法特性和API
- 严格遵循PowerJob框架规范，使用`@PowerJobHandler`注解标记处理器类
- 实体类必须继承`com.xl.xxx.job.common.entity.BaseEntity`，不得使用其他包下的BaseEntity类
- 模块隔离：`app`和`job`模块相互独立，不允许跨模块引用类，每个模块必须独立生成自己的代码

## 开发流程
1. 首先检查是否已存在相关代码，若存在则评估其是否符合需求，只有不符合时才重新生成
2. 使用其他类或实体类前，先检查项目中是否存在相关方法或字段：
    - 优先使用已有的方法和字段
    - 只有确认不存在时才创建新的方法和字段
3. 生成完整的单元测试代码，不使用mock数据，必须真实调用接口确保代码正确性