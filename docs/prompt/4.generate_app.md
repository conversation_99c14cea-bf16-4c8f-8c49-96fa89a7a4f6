# app代码生成指南

## 基本要求
- 基于`docs/design/自动化规则设计文档（crud）.md`文档中的`${3}`章节和`${附录}`生成完整触发器功能代码
- 所有代码必须严格遵循项目现有的包结构和命名规范
- 代码生成过程必须认真、细致，确保无语法错误、逻辑错误和编译错误
- 生成代码前请选与我确认，你是否对需求有其他疑问
- 根据设计文档实现具体的触发器功能，如何触发，触发后执行的具体逻辑，比如变更状态、发送消息、发起 oa等

## 代码规范
- 使用JDK 1.8版本，不使用高于Java 8的语法特性和API
-
## 开发流程
1. 首先检查是否已存在相关代码，若存在则评估其是否符合需求，只有不符合时才重新生成
2. 使用其他类或实体类前，先检查项目中是否存在相关方法或字段：
   - 优先使用已有的方法和字段
   - 只有确认不存在时才创建新的方法和字段
3. 生成完整的单元测试代码，不使用mock数据，必须真实调用接口确保代码正确性
4. 生成代码前请先与我确认，你是否对需求有其他疑问