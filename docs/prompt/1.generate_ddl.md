# 基于设计文档创建表DDL

## 数据库环境：
- MySQL 8.0
- 字符集：utf8mb4
- 排序规则：utf8mb4_general_ci

## 需求描述：
1. 请基于`docs/design/自动化规则设计文档（crud）.md`文档中的`${1.3}`章节生成数据表的DDL语句
2. 将生成的所有DDL语句追加到`docs/sql/init.sql`文件末尾，不要覆盖或修改文件中已有的DDL语句
3. 确保每个表的DDL语句包含设计文档中定义的全部字段，不得遗漏任何业务字段
4. 每个表的DDL语句之间需要用注释分隔，注明表名和表说明

## 公共字段要求：
为每个表添加以下公共字段：

| 字段名         | 数据类型     | 长度 | 允许空 | 是否主键 | 默认值             | 说明           |
|-------------|----------|----| ------ | -------- |-----------------|--------------|
| id          | bigint   | 20 | 否     | 是       | 无               | 主键           |
| create_time | datetime |    | 否     | 否       | CURRENT_TIMESTAMP | 创建时间         |
| create_erp  | varchar  | 64 | 是     | 否       | NULL            | 创建者          |
| update_time | datetime |    | 否     | 否       | CURRENT_TIMESTAMP | 更新时间         |
| update_erp  | varchar  | 64 | 是     | 否       | NULL            | 更新者          |
| is_del      | tinyint  | 1  | 否     | 否       | 0               | 是否删除，0:否，1:是 |


## 输出格式示例：
```sql
-- 表名：t_example_table（示例表）
CREATE TABLE IF NOT EXISTS `t_example_table` (
  -- id放到第一行
  `id` bigint(20) NOT NULL COMMENT '主键',
  -- 以下是业务字段
  `field_name` varchar(50) NOT NULL COMMENT '字段说明',
  -- 以下是公共字段
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  INDEX `idx_field_name` (`field_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例表';
```