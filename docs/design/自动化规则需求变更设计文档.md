
# 1. 变更内容

- **字段变更**
    - `biz_rule_compose_new.trigger_type`
      - 原字段 | trigger_type          | tinyint       | NO       | 触发器类型：1事项状态改变、2事项属性改变、3定时触发|
      - 变更为 | trigger_type          | tinyint       | NO       | 触发器类型：1事项状态改变、2事项属性改变、3事项状态变更或属性变更、4定时触发|
      - 增加了一种新的触发器类型
  - `biz_rule_compose_new.source_status_code/target_status_code`
      - 这两个字段变更为下面四个字段
      - | source_type   | tinyint       | YES      | 原编码类型：1状态、2阶段（触发器类型为事项状态改变时，使用此字段）             |
        | source_code   | varchar(32)   | YES      | 原编码（触发器类型为事项状态改变时，使用此字段）               |
        | target_type   | tinyint       | YES      | 目标编码类型：1状态、2阶段（触发器类型为事项状态改变时，使用此字段）            |
        | target_code   | varchar(32)   | YES      | 目标编码（触发器类型为事项状态改变时，使用此字段）              |
  - `biz_rule_compose_new.schedule_type/schedule_detail/schedule_time`
      - 原字段 | schedule_type/schedule_detail/schedule_time  | 定时触发的触发方式、触发时间、触发时间详情|
      - 删除这三个定时触发相关的字段变更，改为一个字段：| cron_expression | varchar(30) | YES      | cron表达式, 用于定时触发,定时触发时必填|
  - `biz_rule_compose_new.update_status/update_status_data`
      - 这两个字段变更为下面两个字段
      - | update_status_or_stage | tinyint       | NO       | 变更关联流程要素状态或阶段：0否、1是                     |
        | update_status_or_stage_data     | varchar(1024) | YES      | 变更流程要素状态或阶段涉及到的数据                          |
- **新增内结构变更**
    - oa_data
    ```json
    {
        "oaTemplateCode": "aaaaa", // OA模板编码
        "oaApproved": { // OA审批通过
          "matchAll": true,  // 是否匹配所有
          "updateStatusData": [ // 流程要素状态数据
            {
              "processElementCode": "xq",  // 流程要素编码
              "type": 1,  // 状态类型：1状态、2阶段
              "code": 1  // 需要变更的编码
            },
            {
              "processElementCode": "rw",
              "type": 2,  // 状态类型：1状态、2阶段
              "code": 2  // 需要变更的编码
            }
          ],
          "propertyChangeData": "xxx"
        },
      "oaNotApproved": { // OA审批未通过
        "updateStatusData": [
          {
            "processElementCode": "xq",
            "type": 1,  // 状态类型：1状态、2阶段
            "code": 1  // 需要变更的编码
          },
          {
            "processElementCode": "rw",
            "type": 2,  // 状态类型：1状态、2阶段
            "code": 2  // 需要变更的编码
          }
        ],
        "propertyChangeData": "xxx" // 属性变更数据
      }
    }
    ```
    - update_status_or_stage_data
    ```json
    {
      "matchAll": true, // 是否匹配所有
      "updateStatusData": [ //流程要素状态数据
        {
          "processElementCode": "xq", //流程要素编码
          "type": 1,  // 状态类型：1状态、2阶段
          "code": 1  // 需要变更的编码
        },
        {
          "processElementCode": "rw",
          "type": 2,  // 状态类型：1状态、2阶段
          "code": 2  // 需要变更的编码
        }
      ]
    }
    ```
    - 以上的 json 结构变更都是把原来的 statusCode 变更为 type 和 code

- 1、按照变更内容修改自动化规则相关代码逻辑，包括增删改查和触发器，确保代码逻辑正确，以及修改`自动化规则引擎TRD设计.md`
- 2、生成代码前请选与我确认，你是否对变更有其他疑问
---