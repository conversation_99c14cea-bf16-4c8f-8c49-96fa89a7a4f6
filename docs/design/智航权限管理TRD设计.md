
## 模块设计
### 流程设计

### 数据状态设计

### 界面交互设计


### 接口设计
**（1）服务间接口关系图**


**（2）关键接口时序图**


**（3）接口清单**

| 序号 | 接口名称 | 备注说明 |
| --- | --- | --- |
| 1 | 获取预定义角色清单 | |
| 2 | 修改角色信息 | |
| 3 | 获取授权成员清单 | |
| 4 | 获取应用清单 | |
| 5 | 获取SEPG团队空间 | |
| 6 | 用户授权 | |
| 7 | 删除角色用户 | |


**（4）接口明细**

1. 获取预定义角色清单

| 接口名称 | 获取预定义角色清单 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | <font style="color:rgb(31, 31, 31);">devops-api/uaos/api/v1/auth/role/query</font> | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | keyword: '关键字' | string | 参数示例 | {<br/>keyword：'admin'<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": null,     "rawMessage": null,     "data": [] } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": null,     "rawMessage": null,     "data": [         {             "appCode": "arch",             "code": "admin",             "name": "平台管理员",             "roleType": "plat",             "builtIn": true,             "control": false,             "createBy": "admin",             "createAt": "2025-07-20T18:00:00"             "description": "拥有平台设置所有权限"         }     ] } ```  | | |


2. 修改角色信息

| 接口名称 | 修改角色信息 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | <font style="color:rgb(31, 31, 31);">devops-api/uaos/api/v1/auth/role/update</font> | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | code: '角色编码' | string<br/>description: '角色描述' | string<br/>control: '应用内指定 ' | boolean  | 参数示例 | {<br/>   code: 'admin',<br/>   description: 'this is decs',<br/>   control: true<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": null, } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "true", } ```  | | |


3. 获取授权用户清单

| 接口名称 | 获取授权用户清单 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | <font style="color:rgb(31, 31, 31);">devops-api/uaos/api/v1/auth/role/members</font> | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | erp: '用户关键字' | string<br/>dept: '部门编码' | string<br/>start: '开始时间 ' | dateTime<br/>end: '结束时间' | dateTime <br/>pageNum: '当前页' | int<br/>pageSize: '页容量' | int | 参数示例 | {<br/>   erp: 'admin',<br/>   dept: '/12/343',<br/>   start: '2025-07-01',<br/>   end: '2025-07-30',<br/>   pageNum: 1,<br/>   pageSize: 10<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": null,     "rawMessage": null,     "data": {         "records": []         "total": 13         }  } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": null,     "rawMessage": null,     "data": {         "records": [             {                 "id": 1000011,                 "userErp": "shex",                 "userName": "佘旭",                 "userOrgCode": "DE0057",                 "userOrgName": "天弘基金管理有限公司-中后台-技术研发部-业务架构中心",                 "opUserErp": "yuanyang",                 "opUserName": "远扬",                 "opTime": "2025-02-27 08:42:50",                 "appList": [{                     id: 1001,                     code: "spaceDemand",                     name: "团队空间"                 }],                 "spaceList": [                   {                     id: 1001,                     code: 'infra',                     name: '基础技术组'                   }                 ]                              }         ],         "total": 1     } } ```  | | |


4. 获取应用清单

| 接口名称 | 获取应用清单 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | <font style="color:rgb(31, 31, 31);">devops-api/arch/api/v1/appstore</font> | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | 无 | 参数示例 | 无 |
| 返回数据格式 | ```plain {     "code": 200,     "data": [],     "message": ""  } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "data": [         {             "accessMode": "1",             "appCode": "jcsUi",             "appType": 1,             "appUserRelList": [],             "authApp": 1,             "block": {},             "code": "jcsUi",             "createTime": 1718785933000,             "creator": {                 "erp": "admin",                 "name": "admin"             },             "desc": "代码扫描",             "helpUrl": "",             "icon": "https://w.thfund.work/devops-api/storage/devopsbucket/codeScaner.png",             "id": 242,             "licenseStatus": 0,             "name": "代码扫描",             "owner": {                 "erp": "admin",                 "name": "admin"             },             "permission": false,             "status": 2,             "url": "/jcsUi/storageList"         }     ],     "message": "SUCCESS" } ```  | | |


5. 获取SEPG团队空间

| 接口名称 | 获取SEPG团队空间 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | <font style="color:rgb(31, 31, 31);">/devops-api/space/api/v1/spaces?orderType=DESC&dataType=SEPG</font> | | |
| 请求方式 | get | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | <font style="color:rgb(31, 31, 31);">orderType: 排序方式 | DESC ASC</font><br/><font style="color:rgb(31, 31, 31);">dataType: 数据类型 固定值 SEPG</font> | 参数示例 | <font style="color:rgb(31, 31, 31);">orderType=DESC&dataType=SEPG</font> |
| 返回数据格式 | ```plain {     "code": 200,     "data": [],  } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "data": [         {             "activeCardCount": 0,             "archived": 0,             "attentionType": 0,             "cTime": 1754363755000,             "desc": "",             "id": 170,             "key": "SmartNavHub",             "memberCount": 12,             "mode": 2,             "name": "智航项目专用空间",             "privilage": 1         }     ] } ```  | | |


6. 用户授权

| 接口名称 | 用户授权操作 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | <font style="color:rgb(31, 31, 31);">devops-api/uaos/api/v1/auth/role/doAuth</font> | | |
| 请求方式 | post | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | <font style="color:rgb(31, 31, 31);">id: 数据主键 | int</font><br/><font style="color:rgb(31, 31, 31);">userErp: 授权用户账号 | string</font><br/><font style="color:rgb(31, 31, 31);">useName: 授权用户名称 | string</font><br/><font style="color:rgb(31, 31, 31);">orgCode: 所属部门编码 | string</font><br/><font style="color:rgb(31, 31, 31);">orgName: 所属部门名称 | string</font><br/><font style="color:rgb(31, 31, 31);">roleCodes: 授权角色code数组 | array</font><br/><font style="color:rgb(31, 31, 31);">appList: [] 授权应用code数组 | array</font><br/><font style="color:rgb(31, 31, 31);">spaceList[] 授权团队空间ID数组 | array</font> | 参数示例 | <font style="color:rgb(31, 31, 31);">{</font><br/><font style="color:rgb(31, 31, 31);">id: -1,</font><br/><font style="color:rgb(31, 31, 31);">userErp: 'yuanyang'</font><br/><font style="color:rgb(31, 31, 31);">useName: "远扬"</font><br/><font style="color:rgb(31, 31, 31);">orgCode: "/a/acd"</font><br/><font style="color:rgb(31, 31, 31);">orgName: "基础技术组"</font><br/><font style="color:rgb(31, 31, 31);">roleCodes: [admin,appAdmin,spaceAdmin]</font><br/><font style="color:rgb(31, 31, 31);">appList: ['spaceDemand','test'] </font><br/><font style="color:rgb(31, 31, 31);">spaceList:[100013,10056] </font><br/><font style="color:rgb(31, 31, 31);">}</font> |
| 返回数据格式 | ```plain {     "code": 200,     "message": ""  } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS" } ```  | | |


7. 删除用户授权

| 接口名称 | 用户授权操作 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | <font style="color:rgb(31, 31, 31);">devops-api/uaos/api/v1/auth/role/delAuth/{id}</font> | | |
| 请求方式 | Delete | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | <font style="color:rgb(31, 31, 31);">id: 数据主键 | int</font><br/> | 参数示例 | id=1002 |
| 返回数据格式 | ```plain {     "code": 200,     "message": ""  } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS" } ```  | | |


### 数据模型设计
**（1）数据关系图**

**   **

**（2）数据模型清单**

| 序号 | 逻辑名称 | 物理名称 |
| --- | --- | --- |
| 1 | 系统角色表 | auth_role |
| 2 | 角色成员表 | auth_role_member |
| 3 | 应用信息表 | app_info |
| 4 | 应用成员关系表 | app_user_rel |
| 5 | 团队空间信息表 | biz_space |
| 6 | 团队空间成员关系表 | biz_space_member |


**（3）数据结构说明**

1. **系统角色表 - auth_role**

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int(11)` | 否 | 自动递增 | 主键 | - |
| `app_code` | `varchar(64)` | 否 | - | 唯一键（与 `code` 组合） | 应用编码 |
| `code` | `varchar(64)` | 否 | - | 唯一键（与 `app_code` 组合）、普通索引 | 角色编码 |
| `template_code` | `varchar(64)` | 是 | `NULL` | - | 模板标识 |
| `role_type` | `varchar(20)` | 是 | `NULL` | - | 角色类型 |
| `name` | `varchar(255)` | 否 | - | - | 角色名称 |
| `built_in` | `tinyint(1)` | 否 | - | - | 是否内置角色 |
| `description` | `varchar(255)` | 是 | `NULL` | - | 角色类描述 |
| `create_user` | `varchar(64)` | 是 | `NULL` | - | 创建人 |
| `create_time` | `datetime` | 是 | 当前时间戳 | - | 创建时间 |
| `update_user` | `varchar(64)` | 是 | `NULL` | - | 更新人 |
| `update_time` | `datetime` | 是 | 当前时间戳 | - | 最后修改时间 |
| `domain` | `tinyint(1)` | 是 | `1` | - | 角色适用领域：1 平台，2 应用，3 应用内范围 |
| `control` | `tinyint(1)` | 是 | `0` | - | 是否应用内指定：0 否，1 是 |




2. **系统角色用户表 - auth_role_member**

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int(11)` | 否 | 自动递增 | 主键 | - |
| `app_code` | `varchar(64)` | 否 | - | 唯一键（组合） | 应用编码 |
| `scope` | `varchar(64)` | 否 | 空字符串 | 唯一键（组合） | 范围 |
| `role_code` | `varchar(64)` | 否 | - | 唯一键（组合） | 角色编码 |
| `user_erp` | `varchar(64)` | 否 | - | 唯一键（组合） | 用户标识 |
| `built_in` | `varchar(255)` | 否 | - | - | 是否内置角色成员 |
| `create_user` | `varchar(64)` | 是 | `NULL` | - | 创建人 |
| `create_time` | `datetime` | 是 | 当前时间戳 | - | 创建时间 |
| `update_user` | `varchar(64)` | 是 | `NULL` | - | 更新人 |
| `update_time` | `datetime` | 是 | `NULL` | - | 最后修改时间 |
| `deleted` | `tinyint(1)` | 是 | `0` | - | 删除标志位，0:未删除，1:已删除 |
| `tenant_id` | `int(11)` | 是 | `NULL` | - | 租户ID |


3. **应用信息表-app_info**

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int(11) unsigned` | 否 | 自动递增 | 主键 | 自增主键 |
| `code` | `varchar(64)` | 是 | `NULL` | 普通索引 | 编码 |
| `name` | `varchar(64)` | 是 | `NULL` | - | 名称 |
| `mode` | `tinyint(2)` | 是 | `NULL` | - | 权限模型，1 RBAC模型、2 ACL模型 |
| `desc` | `varchar(1024)` | 是 | `NULL` | - | 应用描述 |
| `icon` | `varchar(256)` | 是 | `NULL` | - | 图标 |
| `store` | `tinyint(2)` | 是 | `NULL` | - | 应用商店，1应用商店，`null`其他 |
| `help_url` | `varchar(256)` | 是 | `NULL` | - | 帮助链接 |
| `tech_support_number` | `varchar(64)` | 是 | `NULL` | - | 技术支持（群）号 |
| `access_type` | `tinyint(2)` | 是 | `NULL` | - | 访问方式，1应用商店，`null`其他 |
| `access_secret` | `varchar(64)` | 是 | `NULL` | - | 访问密钥 |
| `token` | `varchar(64)` | 是 | `NULL` | - | Token |
| `owner` | `varchar(64)` | 是 | `NULL` | - | 创建者 |
| `status` | `tinyint(2)` | 是 | `NULL` | - | 应用状态，1准备中、2已上架、3审核中、4已下架 |
| `deleted` | `tinyint(2)` | 是 | `0` | - | 删除标识，0:未删除，1:已删除 |
| `create_user` | `varchar(64)` | 是 | `NULL` | - | 创建者账号 |
| `create_time` | `datetime` | 是 | `NULL` | - | 创建日期 |
| `update_time` | `datetime` | 是 | `NULL` | - | 更新时间 |
| `tenant_id` | `int(11)` | 是 | `NULL` | 普通索引 | 租户ID |
| `group` | `varchar(64)` | 是 | `NULL` | - | 分组 |
| `app_type` | `tinyint(2)` | 是 | `NULL` | - | 应用类型，1第三方应用、2自研应用 |
| `app_org_id` | `varchar(64)` | 是 | `NULL` | - | 企业/部门ID |
| `app_org_name` | `varchar(64)` | 是 | `NULL` | - | 企业/部门名称 |
| `auth_app` | `tinyint(2)` | 是 | `0` | - | 是否接入权限，0未接入，1已接入 |


4. **应用成员关系表-app_user_rel**

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int(11) unsigned` | 否 | 自动递增 | 主键 | 自增主键 |
| `app_id` | `int(11)` | 是 | `NULL` | 普通索引（组合） | 关联后端应用ID，`sec_app`表ID |
| `role` | `varchar(2)` | 是 | `NULL` | 普通索引（组合） | 角色，`1`-负责人 |
| `user_account` | `varchar(64)` | 是 | `NULL` | 普通索引（组合） | 账号 |
| `user_name` | `varchar(64)` | 是 | `NULL` | - | 姓名 |
| `create_user` | `varchar(64)` | 是 | `NULL` | - | 创建人账号 |
| `create_time` | `datetime` | 是 | `NULL` | - | 创建时间 |
| `tenant_id` | `int(11)` | 是 | `NULL` | 普通索引（组合） | 租户ID |


5. **团队空间信息表-biz_space**

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int(11)` | 否 | 自动递增 | 主键 | 自增ID |
| `key` | `varchar(64)` | 是 | `NULL` | 唯一键（组合） | 空间标志 |
| `name` | `varchar(64)` | 是 | `NULL` | - | 团队空间名 |
| `desc` | `varchar(4000)` | 是 | `NULL` | - | 空间描述 |
| `mode` | `tinyint(4)` | 是 | `2` | - | 空间类型，1：通用模式空间，2：敏捷模式空间 |
| `deleted` | `tinyint(1)` | 是 | `0` | - | 删除标志位，0:未删除，1:已删除 |
| `archived` | `int(11)` | 是 | `0` | - | 归档标示，0:未归档，1:已归档 |
| `creator_erp` | `varchar(32)` | 是 | `NULL` | - | 创建人ERP |
| `creator_name` | `varchar(32)` | 是 | `NULL` | - | 创建人名称 |
| `opt_erp` | `varchar(32)` | 是 | `NULL` | - | 最后操作人ERP |
| `opt_name` | `varchar(32)` | 是 | `NULL` | - | 最后操作人名称 |
| `c_time` | `datetime` | 是 | `NULL` | - | 创建时间 |
| `u_time` | `datetime` | 是 | `NULL` | - | 更新时间 |
| `tenant_id` | `int(11)` | 是 | `NULL` | 唯一键（组合） | 租户ID |
| `slot_reg_status` | `tinyint(4)` | 否 | `1` | - | 应用扩展状态 |
| `main_data_id` | `int(20)` | 否 | `NULL` | - | 汇报关系主键 |


6. 团队空间成员关系表-biz_space_member

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int(11)` | 否 | 自动递增 | 主键 | 自增主键 |
| `space_id` | `int(11)` | 是 | `NULL` | 普通索引 | 空间ID |
| `member_erp` | `varchar(100)` | 是 | `NULL` | 普通索引 | 成员ERP |
| `member_name` | `varchar(100)` | 是 | `NULL` | - | 成员名称 |
| `member_org_id` | `varchar(255)` | 是 | `NULL` | 普通索引 | 成员所属部门ID |
| `member_org_name` | `varchar(512)` | 是 | `NULL` | - | 成员所属部门名称 |
| `privilage` | `tinyint(2)` | 是 | `NULL` | - | 空间组员权限，枚举：1:管理员，2:空间成员，3:游客 |
| `c_time` | `datetime` | 是 | `NULL` | - | 创建时间 |
| `u_time` | `datetime` | 是 | `NULL` | - | 更新时间 |
| `tenant_id` | `int(11)` | 是 | `NULL` | - | 租户ID |




