# 系统功能设计文档

## 1. 业务模型

### 1.1 业务模块关系图

[这里使用mermaid流程图描述各模块间的层级关系]


### 1.2 模块列表

[描述各模块的基础信息（编号、名称、英文名、英文缩写）
编号：文档内唯一标识，不同文档可以重复，编号以MD开头+4位数字（从0001开始）
名称：模块的中文名称
英文名：模块的英文名称，也是缩写的全称
英文缩写：用于生成代码时，命名包路径，如，com.xinlong.xxx.dur]

| 模块编号   | 模块名称  | 模块英文名         | 英文缩写 |
| ------ |-------|---------------|------|
| MD0001 | 主框架模块 | jacp-security | arch |

### 1.3 数据模型

#### 1.3.1 XX模块

[描述XX模块下的表间关系及表属性信息]

##### 1.3.1.1 表间关系

[表间关系用mermaid图描述，主要用于梳理关系，使用英文描述，中文字符会报错]


##### 1.3.1.2 表名字典

[列出XX模块所有表信息]

| 表编号    | 表中文名        | 表英文名                       | 备注  |
|--------|-------------|----------------------------| --- |
| TB0001 | 自动化规则表      | biz_rule_compose_new       |     |
| TB0002 | 操作记录表       | biz_rule_operate_log_new    |     |
| TB0003 | 自动化规则执行记录表  | biz_rule_exec_log          |     |
| TB0004 | OA触发记录表     | biz_oa_record          |     |
| TB0005 | 流程要素状态表    | biz_process_element_status |     |

- TB0001~TB0004为自动化规则功能的相关表，TB0005以及之后的表为自动化规则功能以外的其他功能，流程要素状态变更、事项属性变更、消息通知、发起人与通知人相Same时跳过通知等

##### 1.3.1.3 表集

[描述详细的表属性信息，id（默认为主键）、create_time、update_time、create_erp、update_erp、is_del等字段不需要描述，后续生成DDL时会自动补充
唯一索引：这里唯一索引是指业务字段单字段或多字段组合的唯一性，如多字段组合的情况，在所有字段唯一索引列设置为“是”
说明：描述字段的作用，枚举类型字段格式为描述;value1:label1[,...,valueN:labelN]，这里一定要注意格式，在后续开发步骤中会通过这里的字段枚举描述生成字典数据]

**（1）TB0001**

###### 表结构说明

| 字段名                         | 字段类型          | 允许为空 | 描述                                                         |
|-----------------------------|---------------|--------|------------------------------------------------------------|
| rule_code                   | varchar(50)   | NO     | 规则编码                                                       |
| rule_name                   | varchar(50)   | NO     | 规则名称                                                       |
| description                 | varchar(100)  | NO     | 规则描述                                                       |
| trigger_type                | tinyint       | NO     | 触发器类型：1事项触发、2定时触发                                          |
| trigger_type_action         | tinyint       | YES    | 触发器动作：1事项状态改变或属性改变、2事项状态改变、3事项属性改变 （触发器类型为事项触发时使用）         |
| process_element_code        | varchar(32)   | NO     | 流程要素编码，与字典配置对应                                             |
| source_type                 | tinyint       | YES    | 原编码类型：1阶段、2主状态、3子状态 （触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）      |
| source_code                 | varchar(32)   | YES    | 原编码（触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）                       |
| target_type                 | tinyint       | YES    | 目标编码类型：1阶段、2主状态、3子状态（触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）      |
| target_code                 | varchar(32)   | YES    | 目标编码（触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）                      |
| property_change_code        | varchar(32)   | YES    | 发生变更的属性（触发器类型为事项触发并且触发器动作为事项属性改变时或者是触发器类型为定时触发时，使用此字段）     |
| trigger_timing              | varchar(32)   | YES    | 触发时机：before事前、after事后  （触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）   |
| unfinished_repeated         | tinyint       | YES    | 有未完结的执行时，不能重复发起：0否、1是   （触发器类型为事项触发时，使用此字段）                |
| plus_n                      | int           | YES    | 发布成功后的第几日：用于定时触发并且发生变更的属性为：发布成功+N日字段发起验收 时使用， 发生变更的属性存的是编码 |
| cron_expression             | varchar(30)   | YES    | cron表达式, 用于定时触发,定时触发时必填                                    |
| actions                     | varchar(4000) | NO     | 执行动作：是具体的代码函数                                              |
| priority                    | int           | YES    | 权重   （保留字段，当前未使用到）                                         |
| is_reversible               | tinyint       | YES    | 规则是否可逆：0不可逆、1可逆  （保留字段，当前未使用到）                             |
| oa                          | tinyint       | NO     | 触发OA：0否、1是                                                 |
| oa_data                     | varchar(1024) | YES    | 触发OA涉及到的数据                                                 |
| update_status_or_stage      | tinyint       | NO     | 变更关联流程要素状态或阶段：0否、1是                                        |
| update_status_or_stage_data | varchar(1024) | YES    | 变更流程要素状态或阶段涉及到的数据                                          |
| property_change             | tinyint       | NO     | 触发属性变更：0否、1是                                               |
| property_change_data        | varchar(100)  | YES    | 变更属性涉及到的数据  (需要变更的字段编码)                                    |
| msg                         | tinyint       | NO     | 触发消息：0否、1是                                                 |
| msg_data                    | varchar(1024) | YES    | 消息涉及到的数据                                                   |
| interceptor                 | tinyint       | NO     | 触发拦截器：0否、1是                                                |
| interceptor_data            | varchar(1024) | YES    | 拦截器涉及到的数据                                                   |
| space_id                    | int           | YES    | 团队空间ID                                                     |
| state                       | tinyint       | NO     | 状态：0禁用、1启用                                                 |
| extra_data                  | varchar(1024) | YES    | 扩展字段                                                       |
| tenant_id                   | int           | NO     | 租户ID                                                       |

###### 1.3.1.3.1 字段结构说明
- oa_data
```json
{
    "oaTemplateCode": "aaaaa", // OA模板编码
    "oaApproved": { // OA审批通过
      "matchAll": true,  // 是否匹配所有
      "updateStatusData": [ // 流程要素状态数据
        {
          "processElementCode": "xq",  // 流程要素编码
          "type": 1,  // 状态类型：1阶段、2主状态、3子状态
          "code": 1  // 需要变更的编码
        },
        {
          "processElementCode": "rw",
          "type": 2,  // 状态类型：1阶段、2主状态、3子状态
          "code": 2  // 需要变更的编码
        }
      ],
      "propertyChangeData": "xxx"
    },
  "oaNotApproved": { // OA审批未通过
    "updateStatusData": [
      {
        "processElementCode": "xq",
        "type": 1,  // 状态类型：1阶段、2主状态、3子状态
        "code": 1  // 需要变更的编码
      },
      {
        "processElementCode": "rw",
        "type": 2,  // 状态类型：1阶段、2主状态、3子状态
        "code": 2  // 需要变更的编码
      }
    ],
    "propertyChangeData": "xxx" // 属性变更数据
  }
}
```
- msg_data
```json
{
    "notifySelf": true, //发起人和通知人相同时跳过通知
    "msgEventKey": [ //消息事件键
      "xxx",
      "xxx"
    ]
}
```
- update_status_data
```json
{
  "matchAll": true, // 是否匹配所有
  "updateStatusData": [ //流程要素状态数据
    {
      "processElementCode": "xq", //流程要素编码
      "type": 1,  // 状态类型：1阶段、2主状态、3子状态
      "code": 1  // 需要变更的编码
    },
    {
      "processElementCode": "rw",
      "type": 2,  // 状态类型：1阶段、2主状态、3子状态
      "code": 2  // 需要变更的编码
    }
  ]
}
```
- interceptor_data
```json
{
  "interceptorCode": "aa", // 拦截器编码
  "block": 1 //满足拦截去是否继续执行其他动作：1是、0否
}
```

**（2）TB0002**

###### 表结构说明

| 字段名       | 字段类型       | 允许为空 | 描述                                       |
|--------------|----------------|----------|--------------------------------------------|
| rule_id      | bigint(11)     | NO       | 自动化规则ID                               |
| create_erp   | varchar(100)   | YES      | 创建者 ERP 账号                            |
| create_time  | datetime       | YES      | 创建时间                                   |
| action_id    | tinyint(3)     | NO       | 操作类型（如新增、修改、删除等）          |
| before       | varchar(10000) | YES      | 操作前快照（JSON 字符串）                 |
| after        | varchar(10000) | YES      | 操作后快照（JSON 字符串）                 |
| tenant_id    | int(11)        | YES      | 租户 ID                                    |

**（3）TB0003**

###### 表结构说明

| 字段名               | 字段类型        | 允许为空 | 描述                                                               |
|----------------------|-----------------|----------|--------------------------------------------------------------------|
| rule_code            | VARCHAR(100)    | NO       | 规则编码                                                           |
| rule_name            | VARCHAR(100)    | YES      | 规则名称                                                           |
| trigger_type         | tinyint       | NO     | 触发器类型：1事项触发、2定时触发                                          |
| trigger_type_action  | tinyint       | YES    | 触发器动作：1事项状态改变或属性改变、2事项状态改变、3事项属性改变 （触发器类型为事项触发时使用）         |
| process_element_code | varchar(32)   | NO     | 流程要素编码，与字典配置对应                                             |
| source_type          | tinyint       | YES    | 原编码类型：1阶段、2主状态、3子状态 （触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）      |
| source_code          | varchar(32)   | YES    | 原编码（触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）                       |
| target_type          | tinyint       | YES    | 目标编码类型：1阶段、2主状态、3子状态（触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）      |
| target_code          | varchar(32)   | YES    | 目标编码（触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）                      |
| property_change_code | varchar(32)   | YES    | 发生变更的属性（触发器类型为事项触发并且触发器动作为事项属性改变时或者是触发器类型为定时触发时，使用此字段）     |
| trigger_timing       | varchar(32)   | YES    | 触发时机：before事前、after事后  （触发器类型为事项触发并且触发器动作为事项状态改变时，使用此字段）   |
| unfinished_repeated  | tinyint       | YES    | 有未完结的执行时，不能重复发起：0否、1是   （触发器类型为事项触发时，使用此字段）                |
| plus_n               | int           | YES    | 发布成功后的第几日：用于定时触发并且发生变更的属性为：发布成功+N日字段发起验收 时使用， 发生变更的属性存的是编码 |
| cron_expression      | varchar(30)   | YES    | cron表达式, 用于定时触发,定时触发时必填                                    |
| exec_result          | TINYINT(2)      | NO       | 执行结果：0未执行，1成功，2失败，3部分成功                         |
| message              | VARCHAR(1000)   | YES      | 执行结果描述（成功提示或错误信息）                                 |
| oa_record_id         | VARCHAR(512)    | YES      | 关联的 OA 记录 ID                                      |
| msg_record_ids       | VARCHAR(512)    | YES      | 关联的消息记录 ID（逗号分隔）                                      |

**（4）TB0004**

###### 表结构说明

| 字段名            | 字段类型       | 允许为空 | 描述                                                        |
|-------------------|----------------|----------|-------------------------------------------------------------|
| oa_instance_id    | varchar(100)   | YES      | OA 审批实例 ID（来自外部 OA 系统）                          |
| oa_template_code  | varchar(100)   | YES      | 使用的 OA 模板编码                                           |
| oa_title          | varchar(255)   | YES      | 提交 OA 标题（如“卡片状态更新审批”）                        |
| oa_url            | varchar(255)   | YES      | OA 链接                                                      |
| status            | tinyint(2)     | NO       | 审批状态：0 审批中，1 审批通过，2 审批不通过                 |
| result_msg        | varchar(1000)  | YES      | 返回信息（失败原因、审批链接等）                            |
| oa_payload        | text           | YES      | 发起 OA 时提交的原始 JSON 数据                              |
| tenant_id         | int(11)        | YES      | 租户 ID（多租户支持）                                       |

**（5）TB0005**

###### 表结构说明

| 字段名       | 类型           | 允许为空 | 描述                                                |
| --------- | ------------ | ---- | ------------------------------------------------- |
| id        | int          | NO   | 自增主键ID                                            |
| config_id | int          | NO   | 流程要素配置ID                                          |
| req_type  | varchar(32)  | NO   | 需求类型：MASTER（主需求）、SUB（子需求）                         |
| stage_code | varchar(255) | NO   | 阶段编码，与字典配置对应                                      |
| status_code | int          | NO   | 状态编码，系统生成 = 自增 +10                                |
| status_name | varchar(32)  | NO   | 状态名称                                              |
| sort      | int          | NO   | 排序值                                               |
| attr_flag | int          | NO   | 属性：0=无，1=活跃，2=等待                                  |
| status_attr | varchar(32)  | NO   | 状态属性：global=全局，globalExtra=全局扩展，spaceExtra=团队空间扩展 |
| wip_status | tinyint      | NO   | WIP状态：0=不限制数量，1=允许超出，仅提示                          |
| wip_count | int          | NO   | WIP数量                                             |
| rele_change | tinyint      | NO   | 是否关联到变更：0=否，1=是（主需求、快修流程使用）                       |
| visible   | tinyint      | NO   | 显示/隐藏：0=隐藏，1=显示                                   |
| deleted   | tinyint      | NO   | 删除标志：0=未删，1=已删                                    |
| form_json | text         | YES  | 状态对应的屏显配置，存储为 JSON                                |

# 自动化规则模块设计文档

## 1.4 用例列表

| 用例编号 | 用例名称         | 用例描述                                                   | 模块编号 |
| ------ |--------------|--------------------------------------------------------| ------ |
| UC0001 | 新增自动化规则      | 用户在页面上填写规则信息（触发条件、动作），保存后写入 TB0001                     | MD0001 |
| UC0002 | 修改自动化规则      | 用户编辑已存在的规则信息，更新 TB0001、TB0002                          | MD0001 |
| UC0003 | 删除自动化规则      | 用户在页面删除指定规则，逻辑删除 TB0001                                | MD0001 |
| UC0004 | 查询自动化规则列表    | 用户在页面查询所有规则，按条件分页展示 TB0001 的数据                         | MD0001 |
| UC0005 | 查看自动化规则详情    | 用户点击某条规则查看详情，联表查询 TB0001                               | MD0001 |
| UC0006 | 执行自动化规则触发    | 系统检测到触发事件时，匹配 TB0001的条件，执行 TB0001 的动作，写入 TB0003、TB0004 | MD0001 |
| UC0007 | 查询规则执行日志     | 用户查询 TB0003，查看历史执行结果                                   | MD0001 |
| UC0008 | OA 审批关联规则触发  | 当执行动作配置中包含 OA 审批，系统接收 OA 审批结果后，匹配 TB0001，执行规则动作        | MD0001 |

---

## 2. 业务概念与术语

| 术语       | 释义                                                                                |
| -------- |-----------------------------------------------------------------------------------|
| 自动化规则     | 由触发条件、约束条件、执行动作组成的配置集合，系统在满足条件时自动执行对应动作                                           |
| 触发器（Trigger） | 规则的触发来源，如状态变更、属性变更、定时任务，存储在 TB0001.trigger_type和TB0001.trigger_type_action 字段     |
| 条件（Condition） | 附加的判断逻辑，例如字段取值是否满足要求，存储在 TB0001 中 ，如 TB0001.source_type和TB0001.source_code 字段     |
| 动作（Action）   | 当规则被触发并满足条件时，系统需要执行的操作，如发送消息、变更状态、发起 OA 审批，存储在 TB0001 中，如 TB0001.oa和TB0001.msg 字段 |
| 执行日志（Log）  | 系统每次触发规则后的执行情况记录，包括执行状态、结果信息，存储在 TB0003 中                                         |
| OA 审批      | 执行动作的一种类型，系统可通过 OA 审批配置（oaTemplateCode、通过/拒绝动作）与外部 OA 系统交互                        |

---

## 3. 功能需求

### 3.1 自动化规则模块

#### 3.1.1 页面描述

##### 3.1.1.1 页面列表

| 页面编号 | 页面名称       | 类型  | 依赖的主页面编号 |
| ------ | ---------- | --- | -------- |
| PG0001 | 自动化规则列表页   | 主页面 |          |
| PG0002 | 自动化规则详情页   | 弹层  | PG0001   |
| PG0003 | 新增/修改规则页   | 主页面 | PG0001   |
| PG0004 | 执行日志查询页    | 主页面 | PG0001   |
| PG0005 | OA 审批记录页    | 主页面 | PG0001   |

##### 3.1.1.2 页面说明（示例：PG0001）

###### 3.1.1.2.1 原型图
[这里放原型的图片，相对路径引用]

###### 3.1.1.2.2 输入区

| 字段名       | 字段类型 | 格式   | 备注                        |
| --------- | ---- | ---- |---------------------------|
| 规则名称      | 输入框  | 长度50 | 模糊搜索                      |
| 触发器类型     | 下拉框  | 长度20 | 事项触发/定时触发                 |
| 触发器动作     | 下拉框  | 长度20 | 事项状态改变或属性改变/事项状态改变/事项属性改变 |
| 状态        | 下拉框  | 长度10 | 启用/禁用                     |

###### 3.1.1.2.3 展示区

| 字段名   | 字段类型 | 格式  | 备注        |
|-------|------| --- | --------- |
| 规则名称  | 文本   |     |             |
| 规则描述  | 文本   |     |           |
| 流程要素  | 文本   |     |           |
| 触发器类型 | 文本   |     | 状态变更/属性变更/定时 |
| 状态    | 按钮   |     | 启用/禁用     |
| 最后修改人 | 文本   |     |             |
| 操作    | 按钮组  |     | 编辑、删除、详情 |

---

#### 3.1.2 接口功能

##### 3.1.2.1 接口示例：查询规则列表（IF0001）

###### 3.1.2.1.1 接口功能概述
查询自动化规则，支持分页、多条件筛选。

###### 3.1.2.1.2 接口基本信息

| 类型           | 描述                     |
| ------------ | ---------------------- |
| Method       | POST                   |
| Content-Type | application/json       |
| Uri          | /api/rule/queryList    |

###### 3.1.2.1.3 接口入参

| 参数名        | 类型     | 是否必填 | 描述     |
| ---------- | ------ | ---- | ------ |
| ruleName   | string | 否    | 规则名称   |
| triggerType| int    | 否    | 触发器类型 |
| status     | int    | 否    | 状态     |
| pageNum    | int    | 是    | 页码     |
| pageSize   | int    | 是    | 每页条数   |

###### 3.1.2.1.4 接口出参

| 参数名     | 类型     | 描述     |
| ------- | ------ | ------ |
| code    | int    | 返回码   |
| msg     | string | 消息     |
| data    | array  | 规则数据集 |

###### 3.1.2.1.5 接口功能详述
从 TB0001 表分页查询规则基本信息，并返回给前端。

---

#### 3.1.3 用例描述（示例 UC0006：执行自动化规则触发）

| 用例编号 | UC0006       |
|:----:| ------------- |
| 类型   | 服务           |
| 用例名称 | 执行自动化规则触发     |
| 参与者  | 系统            |
| 页面   | 无             |
| 关联表  | TB0001,TB0002,TB0003,TB0004 |
| 前置用例 |               |

**步骤 1. 监听触发器事件**  
(1) 系统接收触发器事件（状态变更、属性变更、定时任务等），读取 TB0001.trigger_type和TB0001.trigger_type_action 进行匹配。

**步骤 2. 校验规则条件**  
(1) 从 TB0001 中读取对应规则的条件配置。  
(2) 判断业务数据是否满足条件，若不满足则终止。

**步骤 3. 执行动作**  
(1) 从 TB0001 中读取 actions 配置。  
(2) 按顺序执行动作：
- 状态/阶段变更（更新业务数据状态字段）
- 属性变更（更新指定属性字段）
- 消息通知（写入消息队列或调用消息发送服务）
- OA 审批（调用外部 OA 系统接口）
- 拦截器（中止或放行操作流转）

**步骤 4. 记录日志**  
(1) 将执行结果写入 TB0004，包含 exec_result、message。

---

#### 3.1.4 功能逻辑图

##### （1）自动化规则执行链路
**触发器事件（Trigger）**
- 系统监听事项状态/属性变更事件 或 定时任务
    - 匹配 TB0001 自动化规则（trigger_type、trigger_type_action、条件）
        - 校验触发条件（source_code / target_code / property_change_code 等）
            - 执行动作集合（actions）
                - 状态/阶段变更（update_status_or_stage_data）
                - 属性变更（property_change_data）
                - 消息通知（msg_data）
                - OA 审批动作（oa_data → TB0004）
                - 拦截器（interceptor_data）
            - 写入执行日志 TB0003
                - 返回执行结果（exec_result、message）

##### （2）页面与接口交互关系
- PG0001 自动化规则列表页 —— IF0001 查询规则列表
    - IF0006 启用/禁用规则
- PG0002 自动化规则详情页 —— IF0002 查询规则详情
    - IF0004 修改规则
    - IF0005 删除规则
- PG0003 自动化规则新增页 —— IF0003 新增规则
- PG0004 执行日志查询页 —— IF0007 查询执行日志
- PG0005 OA 审批记录页 —— IF0008 查询 OA 审批记录
- 系统内部 —— IF0009 规则引擎触发接口


---

## 附录

### A. 参数类型说明

| 参数类型   | 说明                                                                 |
| -------- | ------------------------------------------------------------------ |
| int      | 整数，常用于 ID、状态标识、类型标识                                                   |
| tinyint  | 小整数，常用于布尔/枚举型字段（0/1、状态码）                                             |
| varchar  | 字符串，存储规则名称、编码、描述等文本信息                                               |
| text     | 大文本，存储 JSON 配置、屏显配置等                                                    |
| datetime | 日期时间，存储操作时间、执行时间                                                       |

---

### B. 功能逻辑表达方式

- **触发器（Trigger）**
    - trigger_type：1=事项触发，2=定时触发
    - trigger_type_action：1=状态+属性变更，2=状态变更，3=属性变更
    - cron_expression/plus_n：定时触发规则

- **执行动作（Action）**
    - **状态/阶段变更**（update_status_or_stage=1）
        - update_status_or_stage_data：存储变更的流程要素 JSON
    - **属性变更**（property_change=1）
        - property_change_data：存储需要变更的属性字段编码
    - **消息通知**（msg=1）
        - msg_data：存储消息配置 JSON（notifySelf、msgEventKey 等）
    - **OA 审批**（oa=1）
        - oa_data：存储 OA 配置 JSON，包含 oaTemplateCode、审批通过/不通过的动作
    - **拦截器**（interceptor=1）
        - interceptor_data：存储拦截逻辑 JSON（interceptorCode、block 等）

- **执行日志（Log）**
    - exec_result：0=未执行，1=成功，2=失败，3=部分成功
    - message：执行结果描述（错误信息或提示）  


