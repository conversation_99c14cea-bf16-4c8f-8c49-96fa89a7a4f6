# 自动化规则模块需求整理

-  请基于`docs/design/自动化规则设计文档（crud）.md`文档中的`${1.3}`章节的字段信息，生成具体的自动化规则触发逻辑

## 1. 功能概述

自动化规则用于在 DevOps 平台中，实现以下场景的自动处理：

- 事项状态变更触发
- 事项属性变更触发
- 定时触发（每日 / 每周 / 每月 / 一次性）（powerjob、quartz、mq）
- 自动执行预定义动作（如修改状态、发消息、触发 OA 审批）
- 记录执行日志，便于审计与回溯

目标：减少人工干预，提高流程自动化和可观测性。

---

## 2. 功能点明细

### 2.1 规则基础信息

- 规则编码、名称、描述
- 所属空间与租户隔离
- 状态（启用/禁用）
- 权重（priority，用于多规则命中时排序,当前字段暂时用不着）
- 是否可逆（is_reversible 当前字段暂时用不着）
- 是否允许重复触发（unfinished_repeated）

### 2.2 触发条件

1. **触发类型（trigger_type）**
    - 1：事项状态改变
    - 2：事项属性改变
    - 3：定时触发

2. **状态变更触发字段**
    - 原状态编码（source_status_code）
    - 目标状态编码（target_status_code）

3. **属性变更触发字段**
    - 发生变更的属性（property_change_code）

4. **定时触发字段**
    - 周期类型（schedule_type：`daily`、`weekly`、`monthly`、`once`）
    - 周期详情（schedule_detail）
    - 周期时间（schedule_time）

5. **触发时机**
    - 事前（before）或事后（after）

---

### 2.3 执行动作

- **状态更新**
    - 修改当前事项状态（update_status）
    - 修改关联流程要素状态（update_status_data）
- **发起 OA 审批**
    - oa=1 时根据 oa_data（具体结构参考`docs/design/自动化规则设计文档.md`） 调用 OA 审批流程
- **发送消息通知**
    - msg=1 时根据 msg_data（具体结构参考`docs/design/自动化规则设计文档.md`） 发送消息
    - 跳过通知控制 notifySelf=true时，如果触发人是通知人可跳过发送

动作执行结果需写入执行日志。

---

### 2.4 执行日志与审计

- 主表：`biz_rule_exec_log`
    - 记录规则编码、触发时间、触发类型、触发用户
    - 记录执行结果（成功 / 失败 / 部分成功）
    - 可关联触发的 OA 审批记录或消息记录
    - 可记录触发源业务ID（trigger_source_id）以回溯触发对象

- 子表：
    - `biz_oa_record`：记录每次自动化触发的 OA 审批详情
    - `biz_msg_send_detail`（可选）：记录消息发送详情
    - `biz_rule_operate_log`：记录规则配置的操作快照（before / after JSON）

---

### 2.5 规则管理能力

- 规则新增、编辑、启用、禁用、删除
- 配置触发条件、执行动作、优先级
- 支持团队空间与租户隔离
- 可查看规则执行历史、执行结果与失败原因
- 支持操作审计，记录规则变更前后快照

---

## 3. 流程说明（简化版）

1. **事件监听 / 定时触发**
    - 状态变更
    - 属性变更
    - 定时调度器扫描规则

2. **筛选规则**
    - 根据 `trigger_type`、状态码 / 属性 / 时间等匹配规则

3. **规则校验**
    - 检查启用状态
    - 检查是否允许重复触发
    - 检查是否需要跳过通知

4. **执行动作**
    - 修改状态、发消息、触发 OA
    - 执行动作可并行或顺序执行

5. **写入执行日志**
    - 主表记录规则触发与执行结果
    - 子表记录 OA 审批或消息发送详情

---

## 4. 关键字段映射表

| 功能点       | 关键字段                          |
|--------------|-----------------------------------|
| 触发类型     | trigger_type                      |
| 状态变更条件 | source_status_code / target_status_code |
| 属性变更条件 | property_change_code              |
| 定时触发条件 | schedule_type / schedule_detail / schedule_time |
| 状态变更动作 | update_status     |
| OA 审批动作  | oa / oa_data                      |
| 消息动作     | msg / msg_data                    |
| 跳过通知     | notifySelf                              |
| 执行日志     | biz_rule_exec_log                 |

## 5、详细设计

- 1、触发源通过业务代码自动调用，因为涉及事前和事后，直接监听数据库，是否不好控制，trigger_source_id记录的就是需求id、任务 id 等
- 2、定时触发需要用于发送报告，powerjob、quartz、mq这三种目前我也不知道哪种比较合适，自行判断
- 3、oa 目前好像还没有 api 接口，你可以先扫描系统是否有相关代码，没有的话先模拟
- 4、process_element_code对应的就是需求、子需求、任务、快修、提测工单
- 5、状态变更的实体信息是通过trigger_source_id和process_element_code去对应的业务数据库查询的，这里需注意像 id、状态这些是固定字段，但是具体的业务数据是存的一个json
- 6、多规则同时命中，你可以自己考虑如何执行，但是priority这个字段不要用
- 7、执行失败重试机制，自行考虑
- 8、unfinished_repeated判断逻辑是根据，trigger_source_id和process_element_code去biz_rule_exec_log查询是否有正在执行的