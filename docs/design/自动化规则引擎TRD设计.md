## 模块设计
### 流程设计

自动化规则引擎采用事件驱动的架构设计，支持状态变更、属性变更和定时触发三种规则类型。

**规则执行流程：**
1. 业务事件触发 → 规则匹配 → 规则执行 → 动作执行 → 结果记录
2. 定时调度触发 → 规则匹配 → 规则执行 → 动作执行 → 结果记录

**规则配置流程：**
1. 规则创建 → 规则配置 → 规则测试 → 规则发布 → 规则监控

### 数据状态设计

**规则状态：**
- 0：禁用
- 1：启用

**执行结果状态：**
- 0：未执行
- 1：执行成功
- 2：执行失败
- 3：部分成功

**触发类型：**
- 1：状态变更触发
- 2：属性变更触发
- 3：定时触发

### 界面交互设计

**规则配置界面：**
- 规则基本信息配置
- 触发条件配置
- 执行动作配置
- 规则测试功能

**规则监控界面：**
- 规则执行日志查询
- 规则执行统计
- 规则性能监控

### 接口设计
**（1）服务间接口关系图**

```
业务系统 → 规则引擎 → PowerJob服务
业务系统 → 规则引擎 → OA系统
业务系统 → 规则引擎 → 消息系统
```

**（2）关键接口时序图**

```
业务系统 → 规则触发接口 → 规则引擎 → 规则匹配 → 动作执行 → 结果返回
```

**（3）接口清单**

| 序号 | 接口名称 | 备注说明 |
| --- | --- | --- |
| 1 | 分页查询规则列表 | 支持多条件查询和分页 |
| 2 | 查询规则详情 | 根据ID查询规则完整信息 |
| 3 | 新增规则 | 创建新的自动化规则 |
| 4 | 修改规则 | 更新规则配置信息 |
| 5 | 删除规则 | 删除指定规则 |
| 6 | 批量删除规则 | 批量删除多个规则 |
| 7 | 启用/禁用规则 | 控制规则的启用状态 |
| 8 | 导出规则 | 导出规则配置到Excel |
| 9 | 导入规则 | 从Excel导入规则配置 |
| 10 | 下载导入模板 | 下载规则导入模板 |
| 11 | 统一规则触发接口 | 最灵活的规则触发接口 |
| 12 | 状态变更触发 | 状态变更专用触发接口 |
| 13 | 属性变更触发 | 属性变更专用触发接口 |
| 14 | 事前状态变更触发（含OA） | 事前状态变更含OA审批检查 |
| 15 | 检查事前OA审批规则 | 检查是否需要OA审批 |
| 16 | 事前状态变更触发 | 状态变更前触发规则 |
| 17 | 事后状态变更触发 | 状态变更后触发规则 |
| 18 | 异步状态变更触发 | 异步状态变更触发 |
| 19 | 分页查询执行日志 | 查询规则执行历史记录 |

**（4）接口明细**

1. 分页查询规则列表

| 接口名称 | 分页查询规则列表 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/page | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | pageNum: '当前页码' | int<br/>pageSize: '每页大小' | int<br/>ruleCode: '规则编码' | string<br/>ruleName: '规则名称' | string<br/>triggerType: '触发类型' | int<br/>processElementCode: '流程要素编码' | string<br/>state: '状态' | int<br/>spaceId: '团队空间ID' | long | 参数示例 | {<br/>  "pageNum": 1,<br/>  "pageSize": 10,<br/>  "ruleCode": "AUTO_ASSIGN",<br/>  "ruleName": "自动分配",<br/>  "triggerType": 1,<br/>  "processElementCode": "xq",<br/>  "state": 1,<br/>  "spaceId": 170<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {         "records": [],         "total": 0,         "size": 10,         "current": 1,         "pages": 0     } } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {         "records": [             {                 "id": 1,                 "ruleCode": "AUTO_ASSIGN_DEVELOPER",                 "ruleName": "自动分配开发人员",                 "description": "当需求状态变更为开发中时，自动分配开发人员",                 "triggerType": 1,                 "processElementCode": "xq",                 "sourceStatusCode": "pending",                 "targetStatusCode": "developing",                 "triggerTiming": "after",                 "operateId": 2,                 "priority": 1,                 "state": 1,                 "spaceId": 170,                 "createTime": "2025-01-15 10:30:00",                 "createErp": "zhangsan"             }         ],         "total": 1,         "size": 10,         "current": 1,         "pages": 1     } } ```  | | |

2. 新增规则

| 接口名称 | 新增规则 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | ruleCode: '规则编码' | string<br/>ruleName: '规则名称' | string<br/>description: '规则描述' | string<br/>triggerType: '触发类型' | int<br/>processElementCode: '流程要素编码' | string<br/>sourceStatusCode: '原状态编码' | string<br/>targetStatusCode: '目标状态编码' | string<br/>triggerTiming: '触发时机' | string<br/>operateId: '操作类型ID' | int<br/>updateStatus: '是否更新状态' | int<br/>updateStatusData: '状态更新数据' | string<br/>msg: '是否消息通知' | int<br/>msgData: '消息数据' | string<br/>spaceId: '团队空间ID' | long | 参数示例 | {<br/>  "ruleCode": "AUTO_ASSIGN_DEVELOPER",<br/>  "ruleName": "自动分配开发人员",<br/>  "description": "当需求状态变更为开发中时，自动分配开发人员",<br/>  "triggerType": 1,<br/>  "processElementCode": "xq",<br/>  "sourceStatusCode": "pending",<br/>  "targetStatusCode": "developing",<br/>  "triggerTiming": "after",<br/>  "operateId": 2,<br/>  "updateStatus": 1,<br/>  "updateStatusData": "{\"matchAll\":true,\"updateStatusData\":[{\"processElementCode\":\"rw\",\"statusCode\":2}]}",<br/>  "msg": 1,<br/>  "msgData": "{\"notifySelf\":true,\"msgEventKey\":[\"REQUIREMENT_ASSIGNED\"]}",<br/>  "spaceId": 170<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {         "id": 1,         "ruleCode": "AUTO_ASSIGN_DEVELOPER"     } } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {         "id": 1,         "ruleCode": "AUTO_ASSIGN_DEVELOPER"     } } ```  | | |

3. 事后状态变更触发

| 接口名称 | 事后状态变更触发 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/trigger/after/status | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/x-www-form-urlencoded | | |
| 请求参描说明 | triggerSourceId: '触发源ID' | long<br/>processElementCode: '流程要素编码' | string<br/>sourceStatusCode: '原状态编码' | string<br/>targetStatusCode: '目标状态编码' | string<br/>triggerUser: '触发人ERP' | string | 参数示例 | triggerSourceId=123&processElementCode=xq&sourceStatusCode=pending&targetStatusCode=developing&triggerUser=zhangsan |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |

4. 事后属性变更触发

| 接口名称 | 事后属性变更触发 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/trigger/after/property | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/x-www-form-urlencoded | | |
| 请求参描说明 | triggerSourceId: '触发源ID' | long<br/>processElementCode: '流程要素编码' | string<br/>propertyChangeCode: '属性变更编码' | string<br/>triggerUser: '触发人ERP' | string | 参数示例 | triggerSourceId=123&processElementCode=xq&propertyChangeCode=priority_high&triggerUser=zhangsan |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |

5. 分页查询执行日志

| 接口名称 | 分页查询执行日志 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/logs/page | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | pageNum: '当前页码' | int<br/>pageSize: '每页大小' | int<br/>ruleCode: '规则编码' | string<br/>triggerType: '触发类型' | int<br/>triggerUser: '触发人' | string<br/>execResult: '执行结果' | int | 参数示例 | {<br/>  "pageNum": 1,<br/>  "pageSize": 10,<br/>  "ruleCode": "AUTO_ASSIGN",<br/>  "triggerType": 1,<br/>  "triggerUser": "zhangsan",<br/>  "execResult": 1<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {         "records": [],         "total": 0     } } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {         "records": [             {                 "id": 1,                 "ruleCode": "AUTO_ASSIGN_DEVELOPER",                 "ruleName": "自动分配开发人员",                 "triggerType": 1,                 "triggerTime": "2025-01-15 14:30:00",                 "triggerUser": "zhangsan",                 "triggerSourceId": 123,                 "execResult": 1,                 "message": "执行成功"             }         ],         "total": 1     } } ```  | | |

6. 查询规则详情

| 接口名称 | 查询规则详情 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/{id} | | |
| 请求方式 | GET | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | id: '规则ID' | long | 参数示例 | id=1 |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {} } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": {         "id": 1,         "ruleCode": "AUTO_ASSIGN_DEVELOPER",         "ruleName": "自动分配开发人员",         "description": "当需求状态变更为开发中时，自动分配开发人员",         "triggerType": 1,         "processElementCode": "xq",         "sourceStatusCode": "pending",         "targetStatusCode": "developing",         "triggerTiming": "after",         "operateId": 2,         "priority": 1,         "state": 1,         "spaceId": 170,         "createTime": "2025-01-15 10:30:00",         "createErp": "zhangsan"     } } ```  | | |

7. 修改规则

| 接口名称 | 修改规则 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/{id} | | |
| 请求方式 | PUT | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | id: '规则ID' | long<br/>请求体参数与新增规则相同 | 参数示例 | {<br/>  "id": 1,<br/>  "ruleCode": "AUTO_ASSIGN_DEVELOPER",<br/>  "ruleName": "自动分配开发人员",<br/>  "description": "修改后的描述",<br/>  "triggerType": 1,<br/>  "processElementCode": "xq",<br/>  "sourceStatusCode": "pending",<br/>  "targetStatusCode": "developing",<br/>  "triggerTiming": "after",<br/>  "operateId": 2,<br/>  "spaceId": 170<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |

8. 删除规则

| 接口名称 | 删除规则 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/{id} | | |
| 请求方式 | DELETE | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | id: '规则ID' | long | 参数示例 | id=1 |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |

9. 批量删除规则

| 接口名称 | 批量删除规则 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/batch | | |
| 请求方式 | DELETE | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | ids: '规则ID列表' | array | 参数示例 | {<br/>  "ids": [1, 2, 3]<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |

10. 启用/禁用规则

| 接口名称 | 启用/禁用规则 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/{id}/state | | |
| 请求方式 | PUT | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | id: '规则ID' | long<br/>state: '状态' | int | 参数示例 | {<br/>  "state": 1<br/>} |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": true } ```  | | |

11. 导出规则

| 接口名称 | 导出规则 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/export | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/json | | |
| 请求参描说明 | 查询参数与分页查询接口相同，不包含分页参数 | 参数示例 | {<br/>  "ruleCode": "AUTO_ASSIGN",<br/>  "ruleName": "自动分配",<br/>  "triggerType": 1,<br/>  "processElementCode": "xq",<br/>  "state": 1,<br/>  "spaceId": 170<br/>} |
| 返回数据格式 | Excel文件流 | | |
| 返回数据示例 | Excel文件下载 | | |

12. 导入规则

| 接口名称 | 导入规则 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/import | | |
| 请求方式 | POST | | |
| 请求参数类型 | multipart/form-data | | |
| 请求参描说明 | file: 'Excel文件' | file | 参数示例 | file=rules.xlsx |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "导入结果" } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "导入完成，成功：5条，失败：0条" } ```  | | |

13. 下载导入模板

| 接口名称 | 下载导入模板 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/rules/template | | |
| 请求方式 | GET | | |
| 请求参数类型 | 无 | | |
| 请求参描说明 | 无参数 | 参数示例 | 无 |
| 返回数据格式 | Excel模板文件流 | | |
| 返回数据示例 | Excel模板文件下载 | | |

14. 事前状态变更触发

| 接口名称 | 事前状态变更触发 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/trigger/before/status | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/x-www-form-urlencoded | | |
| 请求参描说明 | triggerSourceId: '触发源ID' | long<br/>processElementCode: '流程要素编码' | string<br/>sourceStatusCode: '原状态编码' | string<br/>targetStatusCode: '目标状态编码' | string<br/>triggerUser: '触发人ERP' | string | 参数示例 | triggerSourceId=123&processElementCode=xq&sourceStatusCode=pending&targetStatusCode=developing&triggerUser=zhangsan |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |

15. 事前属性变更触发

| 接口名称 | 事前属性变更触发 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/trigger/before/property | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/x-www-form-urlencoded | | |
| 请求参描说明 | triggerSourceId: '触发源ID' | long<br/>processElementCode: '流程要素编码' | string<br/>propertyChangeCode: '属性变更编码' | string<br/>triggerUser: '触发人ERP' | string | 参数示例 | triggerSourceId=123&processElementCode=xq&propertyChangeCode=priority_high&triggerUser=zhangsan |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "触发成功" } ```  | | |

16. 异步状态变更触发

| 接口名称 | 异步状态变更触发 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/trigger/async/after/status | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/x-www-form-urlencoded | | |
| 请求参描说明 | triggerSourceId: '触发源ID' | long<br/>processElementCode: '流程要素编码' | string<br/>sourceStatusCode: '原状态编码' | string<br/>targetStatusCode: '目标状态编码' | string<br/>triggerUser: '触发人ERP' | string | 参数示例 | triggerSourceId=123&processElementCode=xq&sourceStatusCode=pending&targetStatusCode=developing&triggerUser=zhangsan |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "异步触发成功" } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "异步触发成功" } ```  | | |

17. 异步属性变更触发

| 接口名称 | 异步属性变更触发 | | |
| :---: | --- | --- | --- |
| 测试环境地址 | [http://cloudflow-test.thfund.work](http://cloudflow-test.thfund.work/) | | |
| 生产环境地址 | http://w.thfund.work | | |
| 接口地址 | devops-api/rule/api/v1/trigger/async/after/property | | |
| 请求方式 | POST | | |
| 请求参数类型 | application/x-www-form-urlencoded | | |
| 请求参描说明 | triggerSourceId: '触发源ID' | long<br/>processElementCode: '流程要素编码' | string<br/>propertyChangeCode: '属性变更编码' | string<br/>triggerUser: '触发人ERP' | string | 参数示例 | triggerSourceId=123&processElementCode=xq&propertyChangeCode=priority_high&triggerUser=zhangsan |
| 返回数据格式 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "异步触发成功" } ```  | | |
| 返回数据示例 | ```plain {     "code": 200,     "message": "SUCCESS",     "data": "异步触发成功" } ```  | | |

### 数据模型设计
**（1）数据关系图**

```
biz_rule_compose_new (规则配置表)
    ↓ 1:N
biz_rule_exec_log (规则执行日志表)
```

**（2）数据模型清单**

| 序号 | 逻辑名称 | 物理名称 |
| --- | --- | --- |
| 1 | 自动化规则表 | biz_rule_compose_new |
| 2 | 规则执行日志表 | biz_rule_exec_log |

**（3）数据结构说明**

1. **自动化规则表 - biz_rule_compose_new**

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `bigint(20)` | 否 | 自动递增 | 主键 | 主键ID |
| `rule_code` | `varchar(100)` | 否 | - | 唯一键 | 规则编码 |
| `rule_name` | `varchar(200)` | 否 | - | - | 规则名称 |
| `description` | `varchar(500)` | 是 | `NULL` | - | 规则描述 |
| `trigger_type` | `int(11)` | 否 | - | 普通索引 | 触发类型：1状态变更，2属性变更，3状态变更或属性变更，4定时触发 |
| `process_element_code` | `varchar(50)` | 否 | - | 普通索引 | 流程要素编码 |
| `source_type` | `tinyint` | 是 | `NULL` | - | 原编码类型：1状态、2阶段 |
| `source_code` | `varchar(32)` | 是 | `NULL` | - | 原编码 |
| `target_type` | `tinyint` | 是 | `NULL` | - | 目标编码类型：1状态、2阶段 |
| `target_code` | `varchar(32)` | 是 | `NULL` | - | 目标编码 |
| `property_change_code` | `varchar(50)` | 是 | `NULL` | - | 属性变更编码 |
| `trigger_timing` | `varchar(20)` | 否 | - | - | 触发时机：before事前，after事后 |
| `operate_id` | `int(11)` | 是 | `NULL` | - | 操作类型ID |
| `actions` | `varchar(200)` | 是 | `NULL` | - | 执行动作描述 |
| `priority` | `int(11)` | 是 | `999` | - | 优先级，数值越小优先级越高 |
| `is_reversible` | `int(11)` | 是 | `0` | - | 是否可逆：0否，1是 |
| `unfinished_repeated` | `int(11)` | 是 | `0` | - | 未完成重复：0允许，1不允许 |
| `oa` | `int(11)` | 是 | `0` | - | 是否触发OA：0否，1是 |
| `oa_data` | `text` | 是 | `NULL` | - | OA数据JSON |
| `update_status_or_stage` | `int(11)` | 是 | `0` | - | 是否更新状态或阶段：0否，1是 |
| `update_status_or_stage_data` | `text` | 是 | `NULL` | - | 状态或阶段更新数据JSON |
| `property_change` | `int(11)` | 是 | `0` | - | 是否属性变更：0否，1是 |
| `property_change_data` | `varchar(100)` | 是 | `NULL` | - | 属性变更数据 |
| `msg` | `int(11)` | 是 | `0` | - | 是否消息通知：0否，1是 |
| `msg_data` | `text` | 是 | `NULL` | - | 消息数据JSON |
| `pass` | `int(11)` | 是 | `0` | - | 是否跳过通知：0否，1是 |
| `rule_attr` | `text` | 是 | `NULL` | - | 规则属性JSON |
| `space_id` | `bigint(20)` | 是 | `NULL` | 普通索引 | 团队空间ID |
| `cron_expression` | `varchar(30)` | 是 | `NULL` | - | cron表达式，用于定时触发 |
| `state` | `int(11)` | 否 | `1` | 普通索引 | 状态：0禁用，1启用 |
| `extra_data` | `text` | 是 | `NULL` | - | 扩展字段 |
| `tenant_id` | `bigint(20)` | 是 | `NULL` | 普通索引 | 租户ID |
| `is_del` | `int(11)` | 否 | `0` | - | 删除标识：0未删除，1已删除 |
| `create_time` | `datetime` | 否 | 当前时间戳 | - | 创建时间 |
| `create_erp` | `varchar(50)` | 是 | `NULL` | - | 创建人ERP |
| `update_time` | `datetime` | 否 | 当前时间戳 | - | 更新时间 |
| `update_erp` | `varchar(50)` | 是 | `NULL` | - | 更新人ERP |

2. **规则执行日志表 - biz_rule_exec_log**

| 字段名称 | 数据类型 | 是否为空 | 默认值 | 键 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `bigint(20)` | 否 | 自动递增 | 主键 | 主键ID |
| `rule_code` | `varchar(100)` | 否 | - | 普通索引 | 规则编码 |
| `rule_name` | `varchar(200)` | 是 | `NULL` | - | 规则名称 |
| `trigger_type` | `int(11)` | 否 | - | 普通索引 | 触发类型：1状态变更，2属性变更，3状态变更或属性变更，4定时触发 |
| `trigger_time` | `datetime` | 否 | - | 普通索引 | 触发时间 |
| `trigger_user` | `varchar(50)` | 是 | `NULL` | 普通索引 | 触发人ERP |
| `trigger_source_id` | `bigint(20)` | 是 | `NULL` | 普通索引 | 触发源ID |
| `process_elemen_id` | `int(11)` | 是 | `NULL` | - | 流程要素ID |
| `source_type` | `tinyint` | 是 | `NULL` | - | 原编码类型：1状态、2阶段 |
| `source_code` | `varchar(32)` | 是 | `NULL` | - | 原编码 |
| `target_type` | `tinyint` | 是 | `NULL` | - | 目标编码类型：1状态、2阶段 |
| `target_code` | `varchar(32)` | 是 | `NULL` | - | 目标编码 |
| `property_change_code` | `varchar(50)` | 是 | `NULL` | - | 属性变更编码 |
| `exec_result` | `int(11)` | 否 | `0` | 普通索引 | 执行结果：0未执行，1成功，2失败，3部分成功 |
| `message` | `varchar(500)` | 是 | `NULL` | - | 执行结果描述 |
| `oa_record_id` | `varchar(100)` | 是 | `NULL` | - | OA记录ID |
| `msg_record_ids` | `varchar(500)` | 是 | `NULL` | - | 消息记录ID列表，逗号分隔 |
| `tenant_id` | `bigint(20)` | 是 | `NULL` | 普通索引 | 租户ID |
| `is_del` | `int(11)` | 否 | `0` | - | 删除标识：0未删除，1已删除 |
| `create_time` | `datetime` | 否 | 当前时间戳 | - | 创建时间 |
| `update_time` | `datetime` | 否 | 当前时间戳 | - | 更新时间 |
